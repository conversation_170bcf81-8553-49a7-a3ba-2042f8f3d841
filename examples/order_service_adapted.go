package examples

import (
	"context"
	"errors"
	"tradicao/internal/models"
	"tradicao/internal/repository"
)

// OrderServiceAdapted representa o serviço de ordens adaptado para usar a interface ID
type OrderServiceAdapted struct {
	repo repository.GenericRepository[models.MaintenanceOrderAdapted]
}

// NewOrderServiceAdapted cria uma nova instância do serviço de ordens adaptado
func NewOrderServiceAdapted(repo repository.GenericRepository[models.MaintenanceOrderAdapted]) *OrderServiceAdapted {
	return &OrderServiceAdapted{
		repo: repo,
	}
}

// GetOrderDetails retorna os detalhes de uma ordem
func (s *OrderServiceAdapted) GetOrderDetails(id models.ID) (*models.MaintenanceOrderAdapted, *models.Branch, *models.Equipment, *models.User, *models.User, *models.User, error) {
	// Buscar a ordem pelo ID
	order, err := s.repo.GetByID(context.Background(), id)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			return nil, nil, nil, nil, nil, nil, errors.New("ordem não encontrada")
		}
		return nil, nil, nil, nil, nil, nil, err
	}

	// Buscar informações relacionadas (implementação simplificada)
	branch := &models.Branch{
		Name: "Filial Exemplo",
	}
	equipment := &models.Equipment{
		Name: "Equipamento Exemplo",
	}
	createdBy := &models.User{
		Name: "Usuário Criador",
	}
	assignedTo := &models.User{
		Name: "Usuário Atribuído",
	}
	provider := &models.User{
		Name: "Fornecedor",
	}

	return order, branch, equipment, createdBy, assignedTo, provider, nil
}

// CreateOrderAdapted cria uma nova ordem
func (s *OrderServiceAdapted) CreateOrderAdapted(ctx context.Context, order *models.MaintenanceOrderAdapted) error {
	// Validar a ordem
	if order.Title == "" {
		return errors.New("título é obrigatório")
	}
	if order.Description == "" {
		return errors.New("descrição é obrigatória")
	}

	// Criar a ordem
	return s.repo.Create(ctx, order)
}

// UpdateOrderAdapted atualiza uma ordem existente
func (s *OrderServiceAdapted) UpdateOrderAdapted(ctx context.Context, order *models.MaintenanceOrderAdapted) error {
	// Validar a ordem
	if order.Title == "" {
		return errors.New("título é obrigatório")
	}
	if order.Description == "" {
		return errors.New("descrição é obrigatória")
	}
	if order.ID == nil || order.ID.IsZero() {
		return errors.New("ID é obrigatório")
	}

	// Atualizar a ordem
	return s.repo.Update(ctx, order.ID, order)
}

// DeleteOrder remove uma ordem
func (s *OrderServiceAdapted) DeleteOrder(ctx context.Context, id models.ID) error {
	// Verificar se a ordem existe
	_, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			return errors.New("ordem não encontrada")
		}
		return err
	}

	// Remover a ordem
	return s.repo.Delete(ctx, id)
}

// ListOrders lista todas as ordens com paginação
func (s *OrderServiceAdapted) ListOrders(ctx context.Context, page, pageSize int) ([]models.MaintenanceOrderAdapted, int, error) {
	// Validar parâmetros
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// Calcular offset
	offset := (page - 1) * pageSize

	// Buscar ordens
	return s.repo.List(ctx, offset, pageSize)
}

// GetCostsByOrderID retorna os custos de uma ordem
func (s *OrderServiceAdapted) GetCostsByOrderID(id models.ID) ([]models.CostItem, error) {
	// Verificar se a ordem existe
	_, err := s.repo.GetByID(context.Background(), id)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			return nil, errors.New("ordem não encontrada")
		}
		return nil, err
	}

	// Implementação simplificada
	return []models.CostItem{}, nil
}

// AddCost adiciona um custo a uma ordem
func (s *OrderServiceAdapted) AddCost(orderID models.ID, userID models.ID, cost models.CostItem) (*models.CostItem, error) {
	// Verificar se a ordem existe
	_, err := s.repo.GetByID(context.Background(), orderID)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			return nil, errors.New("ordem não encontrada")
		}
		return nil, err
	}

	// Implementação simplificada
	return &models.CostItem{}, nil
}

// UpdateStatus atualiza o status de uma ordem
func (s *OrderServiceAdapted) UpdateStatus(orderID models.ID, userID models.ID, newStatus models.OrderStatus, reason string) (*models.MaintenanceOrderAdapted, error) {
	// Buscar a ordem
	order, err := s.repo.GetByID(context.Background(), orderID)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			return nil, errors.New("ordem não encontrada")
		}
		return nil, err
	}

	// Atualizar o status
	order.Status = newStatus

	// Salvar a ordem
	err = s.repo.Update(context.Background(), orderID, order)
	if err != nil {
		return nil, err
	}

	return order, nil
}

// AssignProvider atribui um fornecedor a uma ordem
func (s *OrderServiceAdapted) AssignProvider(orderID models.ID, userID models.ID, providerID models.ID) (*models.MaintenanceOrderAdapted, error) {
	// Buscar a ordem
	order, err := s.repo.GetByID(context.Background(), orderID)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			return nil, errors.New("ordem não encontrada")
		}
		return nil, err
	}

	// Atualizar o fornecedor
	order.ServiceProviderID = providerID

	// Salvar a ordem
	err = s.repo.Update(context.Background(), orderID, order)
	if err != nil {
		return nil, err
	}

	return order, nil
}

// AddInteraction adiciona uma interação a uma ordem
func (s *OrderServiceAdapted) AddInteraction(orderID models.ID, userID models.ID, message string) (*models.Interaction, error) {
	// Verificar se a ordem existe
	_, err := s.repo.GetByID(context.Background(), orderID)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			return nil, errors.New("ordem não encontrada")
		}
		return nil, err
	}

	// Implementação simplificada
	return &models.Interaction{}, nil
}
