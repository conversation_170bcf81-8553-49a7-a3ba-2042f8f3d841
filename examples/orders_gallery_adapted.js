/**
 * Ordens de Serviço - Estilo Galeria - Rede Tradição
 * Script adaptado para usar a interface ID abstrata
 */

// Variáveis globais
let orders = [];
let filteredOrders = [];

// Inicialização quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar tooltips do Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Carregar ordens (inicialmente todas)
    loadOrders();

    // Event listeners
    setupEventListeners();
});

/**
 * Configura os event listeners da página
 */
function setupEventListeners() {
    // Alternar entre visualização em grade e lista
    document.getElementById('gridViewBtn').addEventListener('click', function() {
        document.getElementById('gridView').classList.remove('d-none');
        document.getElementById('listView').classList.add('d-none');
        this.classList.add('active');
        document.getElementById('listViewBtn').classList.remove('active');
    });

    document.getElementById('listViewBtn').addEventListener('click', function() {
        document.getElementById('listView').classList.remove('d-none');
        document.getElementById('gridView').classList.add('d-none');
        this.classList.add('active');
        document.getElementById('gridViewBtn').classList.remove('active');
    });

    // Filtro de status
    document.getElementById('statusFilter').addEventListener('change', function() {
        applyFilters();
    });

    // Filtro de prioridade
    document.getElementById('priorityFilter').addEventListener('change', function() {
        applyFilters();
    });

    // Filtro de data
    document.getElementById('dateFilter').addEventListener('change', function() {
        applyFilters();
    });

    // Busca
    document.getElementById('searchBtn').addEventListener('click', function() {
        applyFilters();
    });

    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            applyFilters();
        }
    });

    // Botão de limpar filtros
    document.getElementById('clearFiltersBtn').addEventListener('click', function() {
        document.getElementById('statusFilter').value = 'all';
        document.getElementById('priorityFilter').value = 'all';
        document.getElementById('searchInput').value = '';
        document.getElementById('dateFilter').value = '';
        applyFilters();
    });

    // Botão de atualizar
    document.getElementById('refreshBtn').addEventListener('click', function() {
        loadOrders();
    });

    // Botões de excluir ordem
    document.querySelectorAll('.delete-order').forEach(button => {
        button.addEventListener('click', function() {
            const orderId = this.getAttribute('data-id');
            confirmDeleteOrder(orderId);
        });
    });
}

/**
 * Carrega a lista de ordens
 */
function loadOrders() {
    // Exibir indicador de carregamento (se existir)
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.classList.remove('d-none');
    }

    // Ocultar mensagem de "nenhuma ordem"
    const noOrdersMessage = document.getElementById('noOrdersMessage');
    if (noOrdersMessage) {
        noOrdersMessage.classList.add('d-none');
    }

    // Fazer requisição para a API
    fetch('/api/orders')
        .then(response => {
            if (!response.ok) {
                throw new Error('Falha ao carregar ordens');
            }
            return response.json();
        })
        .then(data => {
            // Armazenar as ordens
            orders = data;
            filteredOrders = [...data];

            // Ocultar indicador de carregamento
            if (loadingIndicator) {
                loadingIndicator.classList.add('d-none');
            }

            if (data.length === 0) {
                // Exibir mensagem de "nenhuma ordem"
                if (noOrdersMessage) {
                    noOrdersMessage.classList.remove('d-none');
                }
            } else {
                // Renderizar as ordens
                renderOrders(data);
                // Atualizar contadores
                updateCounters(data);
            }
        })
        .catch(error => {
            console.error('Erro ao carregar ordens:', error);
            if (loadingIndicator) {
                loadingIndicator.classList.add('d-none');
            }
            if (noOrdersMessage) {
                noOrdersMessage.classList.remove('d-none');
                noOrdersMessage.querySelector('p').textContent = 'Erro ao carregar ordens: ' + error.message;
            }
            showToast('Erro ao carregar ordens: ' + error.message, 'error');
        });
}

/**
 * Renderiza as ordens na interface
 * @param {Array} ordersList - Lista de ordens
 */
function renderOrders(ordersList) {
    const gridView = document.getElementById('gridView');
    const tableBody = document.getElementById('orderTableBody');

    // Se não houver elementos para renderizar, não fazer nada
    if (!gridView || !tableBody) {
        return;
    }

    // Limpar a grade de ordens (mantendo elementos especiais como mensagens)
    const specialElements = Array.from(gridView.querySelectorAll('#loadingIndicator, #noOrdersMessage'));
    gridView.innerHTML = '';
    specialElements.forEach(el => gridView.appendChild(el));

    // Limpar a tabela
    tableBody.innerHTML = '';

    if (ordersList.length === 0) {
        // Exibir mensagem de "nenhuma ordem"
        const noOrdersMessage = document.getElementById('noOrdersMessage');
        if (noOrdersMessage) {
            noOrdersMessage.classList.remove('d-none');
        } else {
            // Criar mensagem se não existir
            const emptyMessage = document.createElement('div');
            emptyMessage.id = 'noOrdersMessage';
            emptyMessage.className = 'text-center py-5';
            emptyMessage.style.gridColumn = '1 / -1';
            emptyMessage.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-clipboard-list empty-state-icon"></i>
                    <p>Nenhuma ordem de serviço encontrada com os filtros selecionados.</p>
                    <a href="/orders/create" class="btn btn-shell-yellow mt-3">
                        <i class="fas fa-plus"></i> Nova Ordem
                    </a>
                </div>
            `;
            gridView.appendChild(emptyMessage);
        }

        // Adicionar linha vazia na tabela
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-clipboard-list empty-state-icon"></i>
                        <p>Nenhuma ordem de serviço encontrada com os filtros selecionados.</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // Ocultar mensagem de "nenhuma ordem"
    const noOrdersMessage = document.getElementById('noOrdersMessage');
    if (noOrdersMessage) {
        noOrdersMessage.classList.add('d-none');
    }

    // Renderizar cada ordem
    ordersList.forEach(order => {
        // Determinar classes de status e prioridade
        const statusClass = `status-${order.status}`;
        const priorityClass = `priority-${order.priority}`;

        // Obter labels de status e prioridade
        const statusLabel = getStatusLabel(order.status);
        const priorityLabel = getPriorityLabel(order.priority);

        // Formatar data
        const formattedDate = formatDate(order.created_at);

        // Obter o ID como string
        const idStr = order.id.toString ? order.id.toString() : order.id;

        // Adicionar à visualização em grade
        const orderItem = document.createElement('div');
        orderItem.className = 'order-item';
        orderItem.innerHTML = `
            <div class="order-card">
                <div class="card-header">
                    <div class="order-id">#${idStr}</div>
                    <div class="order-date">${formattedDate}</div>
                </div>
                <div class="card-body">
                    <h5 class="card-title" title="${order.title}">${order.title}</h5>
                    <p class="card-text description">${order.description || 'Sem descrição'}</p>
                    <div class="order-meta">
                        <span class="status-badge ${statusClass}">
                            <i class="fas fa-circle"></i> ${statusLabel}
                        </span>
                        <span class="priority-badge ${priorityClass}">
                            <i class="fas fa-flag"></i> ${priorityLabel}
                        </span>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="card-actions">
                        <a href="/orders/${idStr}" class="btn-icon" data-bs-toggle="tooltip" title="Ver Detalhes">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="/orders/${idStr}/edit" class="btn-icon" data-bs-toggle="tooltip" title="Editar">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button class="btn-icon delete-order" data-id="${idStr}" data-bs-toggle="tooltip" title="Excluir">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        gridView.appendChild(orderItem);

        // Adicionar à visualização em lista
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>#${idStr}</td>
            <td>${order.title}</td>
            <td>${order.description || 'Sem descrição'}</td>
            <td>
                <span class="status-badge ${statusClass}">
                    <i class="fas fa-circle"></i> ${statusLabel}
                </span>
            </td>
            <td>
                <span class="priority-badge ${priorityClass}">
                    <i class="fas fa-flag"></i> ${priorityLabel}
                </span>
            </td>
            <td>${formattedDate}</td>
            <td>
                <div class="action-buttons">
                    <a href="/orders/${idStr}" class="btn-icon" data-bs-toggle="tooltip" title="Ver Detalhes">
                        <i class="fas fa-eye"></i>
                    </a>
                    <a href="/orders/${idStr}/edit" class="btn-icon" data-bs-toggle="tooltip" title="Editar">
                        <i class="fas fa-edit"></i>
                    </a>
                    <button class="btn-icon delete-order" data-id="${idStr}" data-bs-toggle="tooltip" title="Excluir">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tableBody.appendChild(row);
    });

    // Adicionar event listeners para os botões de excluir
    document.querySelectorAll('.delete-order').forEach(button => {
        button.addEventListener('click', function() {
            const orderId = this.getAttribute('data-id');
            confirmDeleteOrder(orderId);
        });
    });

    // Reinicializar tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Atualiza os contadores de ordens
 * @param {Array} ordersList - Lista de ordens
 */
function updateCounters(ordersList) {
    // Contar ordens por status
    const pendingCount = ordersList.filter(order => order.status === 'pending').length;
    const inProgressCount = ordersList.filter(order => order.status === 'in_progress').length;
    const completedCount = ordersList.filter(order => order.status === 'completed').length;
    const totalCount = ordersList.length;

    // Atualizar os contadores na interface
    document.getElementById('pendingCount').textContent = pendingCount;
    document.getElementById('inProgressCount').textContent = inProgressCount;
    document.getElementById('completedCount').textContent = completedCount;
    document.getElementById('totalCount').textContent = totalCount;
}

/**
 * Aplica os filtros selecionados às ordens
 */
function applyFilters() {
    // Obter valores dos filtros
    const statusFilter = document.getElementById('statusFilter').value;
    const priorityFilter = document.getElementById('priorityFilter').value;
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const dateFilter = document.getElementById('dateFilter').value;

    // Filtrar ordens
    let filtered = [...orders];

    // Filtrar por status
    if (statusFilter !== 'all') {
        filtered = filtered.filter(order => order.status === statusFilter);
    }

    // Filtrar por prioridade
    if (priorityFilter !== 'all') {
        filtered = filtered.filter(order => order.priority === priorityFilter);
    }

    // Filtrar por data
    if (dateFilter) {
        const filterDate = new Date(dateFilter);
        filtered = filtered.filter(order => {
            const orderDate = new Date(order.created_at);
            return orderDate.toDateString() === filterDate.toDateString();
        });
    }

    // Filtrar por termo de busca
    if (searchTerm) {
        filtered = filtered.filter(order =>
            (order.title && order.title.toLowerCase().includes(searchTerm)) ||
            (order.description && order.description.toLowerCase().includes(searchTerm)) ||
            (order.id && order.id.toString().includes(searchTerm))
        );
    }

    // Atualizar a lista filtrada
    filteredOrders = filtered;

    // Renderizar as ordens filtradas
    renderOrders(filtered);
    // Atualizar contadores
    updateCounters(filtered);
}

/**
 * Confirma a exclusão de uma ordem
 * @param {string} orderId - ID da ordem
 */
function confirmDeleteOrder(orderId) {
    if (confirm(`Tem certeza que deseja excluir a ordem #${orderId}?`)) {
        deleteOrder(orderId);
    }
}

/**
 * Exclui uma ordem
 * @param {string} orderId - ID da ordem
 */
function deleteOrder(orderId) {
    fetch(`/api/orders/${orderId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Falha ao excluir ordem');
        }
        return response.json();
    })
    .then(data => {
        showToast('Ordem excluída com sucesso!', 'success');
        // Remover a ordem da lista
        orders = orders.filter(order => order.id.toString() !== orderId);
        filteredOrders = filteredOrders.filter(order => order.id.toString() !== orderId);
        // Renderizar as ordens atualizadas
        renderOrders(filteredOrders);
        // Atualizar contadores
        updateCounters(filteredOrders);
    })
    .catch(error => {
        console.error('Erro ao excluir ordem:', error);
        showToast('Erro ao excluir ordem: ' + error.message, 'error');
    });
}

/**
 * Exibe uma notificação toast
 * @param {string} message - Mensagem a ser exibida
 * @param {string} type - Tipo de notificação (success, error)
 */
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toastPlacement');
    
    // Criar o elemento toast
    const toastElement = document.createElement('div');
    toastElement.className = `toast toast-shell ${type} show`;
    toastElement.setAttribute('role', 'alert');
    toastElement.setAttribute('aria-live', 'assertive');
    toastElement.setAttribute('aria-atomic', 'true');
    
    // Ícone baseado no tipo
    let icon = 'info-circle';
    if (type === 'success') icon = 'check-circle';
    if (type === 'error') icon = 'exclamation-circle';
    
    // Conteúdo do toast
    toastElement.innerHTML = `
        <div class="toast-header">
            <i class="fas fa-${icon} me-2"></i>
            <strong class="me-auto">${type === 'success' ? 'Sucesso' : type === 'error' ? 'Erro' : 'Informação'}</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Fechar"></button>
        </div>
        <div class="toast-body">
            ${message}
        </div>
    `;
    
    // Adicionar ao container
    toastContainer.appendChild(toastElement);
    
    // Configurar para fechar automaticamente após 5 segundos
    setTimeout(() => {
        toastElement.classList.remove('show');
        setTimeout(() => {
            toastContainer.removeChild(toastElement);
        }, 300);
    }, 5000);
    
    // Adicionar evento para fechar ao clicar no botão
    toastElement.querySelector('.btn-close').addEventListener('click', () => {
        toastElement.classList.remove('show');
        setTimeout(() => {
            toastContainer.removeChild(toastElement);
        }, 300);
    });
}

/**
 * Formata uma data para exibição
 * @param {string} dateString - String de data
 * @returns {string} Data formatada
 */
function formatDate(dateString) {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Data inválida';
    
    return date.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
}

/**
 * Obtém o label de um status
 * @param {string} status - Código do status
 * @returns {string} Label do status
 */
function getStatusLabel(status) {
    const statusMap = {
        'pending': 'Pendente',
        'in_progress': 'Em Andamento',
        'completed': 'Concluída',
        'cancelled': 'Cancelada'
    };
    
    return statusMap[status] || status;
}

/**
 * Obtém o label de uma prioridade
 * @param {string} priority - Código da prioridade
 * @returns {string} Label da prioridade
 */
function getPriorityLabel(priority) {
    const priorityMap = {
        'low': 'Baixa',
        'medium': 'Média',
        'high': 'Alta',
        'critical': 'Crítica'
    };
    
    return priorityMap[priority] || priority;
}
