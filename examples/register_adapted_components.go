package examples

import (
	"database/sql"

	"tradicao/internal/models"
	"tradicao/internal/repository"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"
	"gorm.io/gorm"
)

// RegisterAdaptedComponents registra os componentes adaptados para usar a interface ID
// no container de dependências
func RegisterAdaptedComponents(
	router *gin.Engine,
	gormDB *gorm.DB,
	mongoDB *mongo.Database,
	sqlDB *sql.DB,
	authMiddleware gin.HandlerFunc,
) {
	// Registrar repositório genérico para MaintenanceOrderAdapted
	orderRepo := repository.NewMaintenanceOrderGenericRepository(
		gormDB,
		mongoDB,
		sqlDB,
		"maintenance_orders",
	)

	// Registrar serviço adaptado para MaintenanceOrderAdapted
	orderService := NewOrderServiceAdapted(orderRepo)

	// Registrar handler adaptado para MaintenanceOrderAdapted
	orderHandler := NewOrderHandlerAdapted(orderService)

	// Registrar rotas para o handler adaptado
	apiGroup := router.Group("/api/orders-adapted")
	apiGroup.Use(authMiddleware)
	{
		apiGroup.GET("", orderHandler.ListOrders)
		apiGroup.POST("", orderHandler.CreateOrder)
		apiGroup.GET("/:id", orderHandler.GetOrder)
		apiGroup.PUT("/:id", orderHandler.UpdateOrder)
		apiGroup.DELETE("/:id", orderHandler.DeleteOrder)
	}

	// Registrar rotas para páginas HTML
	webGroup := router.Group("/orders-adapted")
	webGroup.Use(authMiddleware)
	{
		webGroup.GET("", orderHandler.ListOrdersPage)
		webGroup.GET("/:id", orderHandler.ShowOrderDetail)
	}
}

// ExampleUsage mostra como usar a interface ID em código
func ExampleUsage() {
	// Criar um NumericID
	id1 := models.NewNumericID(123)

	// Criar um ObjectID a partir de uma string
	id2, _ := models.ParseID("507f1f77bcf86cd799439011")

	// Comparar IDs
	if id1.Equals(id2) {
		// IDs são iguais
	}

	// Verificar se ID está vazio
	if id1.IsZero() {
		// ID está vazio
	}

	// Converter para string
	idStr := id1.String()

	// Converter de volta para ID
	id3, _ := models.ParseID(idStr)

	// Usar em um modelo
	order := &models.MaintenanceOrderAdapted{
		ID:          id3,
		Title:       "Exemplo",
		Description: "Descrição de exemplo",
	}

	// Acessar o ID
	_ = order.ID.String()
}
