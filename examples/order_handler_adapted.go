package examples

import (
	"net/http"
	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// OrderHandlerAdapted contém as dependências para os handlers de ordem
// usando a nova interface ID
type OrderHandlerAdapted struct {
	Service services.OrderServiceInterface
}

// NewOrderHandlerAdapted cria uma nova instância de OrderHandlerAdapted
func NewOrderHandlerAdapted(service services.OrderServiceInterface) *OrderHandlerAdapted {
	return &OrderHandlerAdapted{Service: service}
}

// GetOrder retorna os detalhes de uma ordem de serviço
// GET /api/orders/:id
func (h *OrderHandlerAdapted) GetOrder(c *gin.Context) {
	idStr := c.Param("id")

	// Converter string para ID abstrato
	id, err := models.ParseID(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido: " + err.Error()})
		return
	}

	// Converter ID para uint para compatibilidade com a interface
	numID, err := models.ConvertToNumericID(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Erro ao converter ID: " + err.Error()})
		return
	}

	// Usar o serviço adaptado para obter a ordem
	order, branch, equipment, createdBy, assignedTo, provider, err := h.Service.GetOrderDetails(numID.Value)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"order":       order,
		"branch":      branch,
		"equipment":   equipment,
		"created_by":  createdBy,
		"assigned_to": assignedTo,
		"provider":    provider,
	})
}

// CreateOrder cria uma nova ordem de serviço
// POST /api/orders
func (h *OrderHandlerAdapted) CreateOrder(c *gin.Context) {
	var order models.MaintenanceOrderAdapted
	if err := c.ShouldBindJSON(&order); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Obter ID do usuário do contexto
	userIDStr, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}

	// Converter para ID abstrato
	userID, err := models.ParseID(userIDStr.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "ID de usuário inválido"})
		return
	}

	// Definir campos da ordem
	order.CreatedByUserID = userID
	order.Status = models.StatusPending

	// Criar a ordem
	err = h.Service.CreateOrderAdapted(c.Request.Context(), &order)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, order)
}

// UpdateOrder atualiza uma ordem de serviço existente
// PUT /api/orders/:id
func (h *OrderHandlerAdapted) UpdateOrder(c *gin.Context) {
	idStr := c.Param("id")

	// Converter string para ID abstrato
	id, err := models.ParseID(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido: " + err.Error()})
		return
	}

	var order models.MaintenanceOrderAdapted
	if err := c.ShouldBindJSON(&order); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Definir o ID da ordem
	order.ID = id

	// Atualizar a ordem
	err = h.Service.UpdateOrderAdapted(c.Request.Context(), &order)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, order)
}

// DeleteOrder remove uma ordem de serviço
// DELETE /api/orders/:id
func (h *OrderHandlerAdapted) DeleteOrder(c *gin.Context) {
	idStr := c.Param("id")

	// Converter string para ID abstrato
	id, err := models.ParseID(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido: " + err.Error()})
		return
	}

	// Remover a ordem
	err = h.Service.DeleteOrder(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Ordem removida com sucesso"})
}

// ShowOrderDetail renderiza a página de detalhes da ordem
// GET /orders/:id
func (h *OrderHandlerAdapted) ShowOrderDetail(c *gin.Context) {
	// Obter ID da ordem
	idStr := c.Param("id")

	// Converter string para ID abstrato
	id, err := models.ParseID(idStr)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "ID inválido: " + err.Error(),
		})
		return
	}

	// Converter ID para uint para compatibilidade com a interface
	numID, err := models.ConvertToNumericID(id)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Erro ao converter ID: " + err.Error(),
		})
		return
	}

	// Obter detalhes da ordem
	order, branch, equipment, createdBy, assignedTo, provider, err := h.Service.GetOrderDetails(numID.Value)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Erro ao carregar detalhes da ordem: " + err.Error(),
		})
		return
	}

	// Obter usuário atual
	userIDStr, _ := c.Get("userID")
	userID, _ := models.ParseID(userIDStr.(string))
	userRole, _ := c.Get("userRole")
	userName, _ := c.Get("userName")

	// Renderizar a página de detalhes
	c.HTML(http.StatusOK, "ordens/order_detail_service.html", gin.H{
		"title":       "Detalhes da Ordem #" + id.String(),
		"page":        "order_detail",
		"ActivePage":  "orders",
		"Order":       order,
		"Branch":      branch,
		"Equipment":   equipment,
		"CreatedUser": createdBy,
		"AssignedTo":  assignedTo,
		"Provider":    provider,
		"CurrentUser": gin.H{
			"ID":   userID,
			"Role": userRole,
			"Name": userName,
		},
	})
}

// ListOrders lista todas as ordens de serviço
// GET /api/orders
func (h *OrderHandlerAdapted) ListOrders(c *gin.Context) {
	// Obter parâmetros de paginação
	page := 1
	pageSize := 10

	// Obter ordens
	orders, total, err := h.Service.ListOrders(c.Request.Context(), page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"orders": orders,
		"pagination": gin.H{
			"page":      page,
			"page_size": pageSize,
			"total":     total,
			"pages":     (total + pageSize - 1) / pageSize,
		},
	})
}

// ListOrdersPage renderiza a página de listagem de ordens
// GET /orders
func (h *OrderHandlerAdapted) ListOrdersPage(c *gin.Context) {
	// Obter parâmetros de paginação
	page := 1
	pageSize := 10

	// Obter ordens
	orders, totalCount, err := h.Service.ListOrders(c.Request.Context(), page, pageSize)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Erro ao carregar ordens: " + err.Error(),
		})
		return
	}

	// Calcular número total de páginas
	totalPages := (totalCount + pageSize - 1) / pageSize

	// Obter opções de status e prioridade
	statusOptions := map[string]string{
		"pending":     "Pendente",
		"in_progress": "Em Andamento",
		"completed":   "Concluída",
		"cancelled":   "Cancelada",
	}

	priorityOptions := map[string]string{
		"low":      "Baixa",
		"medium":   "Média",
		"high":     "Alta",
		"critical": "Crítica",
	}

	// Contar ordens por status
	pendingCount := 0
	inProgressCount := 0
	completedCount := 0
	for _, order := range orders {
		switch order.Status {
		case "pending":
			pendingCount++
		case "in_progress":
			inProgressCount++
		case "completed":
			completedCount++
		}
	}

	// Obter usuário atual
	userIDStr, _ := c.Get("userID")
	userID, _ := models.ParseID(userIDStr.(string))
	userRole, _ := c.Get("userRole")
	userName, _ := c.Get("userName")
	userEmail, _ := c.Get("userEmail")

	// Renderizar a página de listagem
	c.HTML(http.StatusOK, "ordens/orders_gallery_style.html", gin.H{
		"title":           "Ordens de Serviço",
		"page":            "orders",
		"ActivePage":      "orders",
		"Orders":          orders,
		"StatusOptions":   statusOptions,
		"PriorityOptions": priorityOptions,
		"PendingCount":    pendingCount,
		"InProgressCount": inProgressCount,
		"CompletedCount":  completedCount,
		"TotalCount":      len(orders),
		"TotalPages":      totalPages,
		"CurrentPage":     page,
		"PageSize":        pageSize,
		"TotalRecords":    totalCount,
		"User": gin.H{
			"ID":    userID,
			"Role":  userRole,
			"Name":  userName,
			"Email": userEmail,
		},
	})
}
