package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"text/template"
)

// Schema representa um esquema de entidade
type Schema struct {
	Name   string   `json:"name"`
	Fields []Field  `json:"fields"`
	Edges  []Edge   `json:"edges"`
	Mixin  []string `json:"mixin"`
}

// Field representa um campo de entidade
type Field struct {
	Name     string `json:"name"`
	Type     string `json:"type"`
	Optional bool   `json:"optional"`
	Unique   bool   `json:"unique"`
	Default  string `json:"default"`
}

// Edge representa um relacionamento entre entidades
type Edge struct {
	Name     string `json:"name"`
	Type     string `json:"type"`
	Ref      string `json:"ref"`
	Unique   bool   `json:"unique"`
	Required bool   `json:"required"`
}

// SchemaGenerator gera esquemas Ent a partir de definições JSON
type SchemaGenerator struct {
	SchemaDir   string
	TemplateDir string
	OutputDir   string
}

// NewSchemaGenerator cria um novo SchemaGenerator
func NewSchemaGenerator(schemaDir, templateDir, outputDir string) *SchemaGenerator {
	return &SchemaGenerator{
		SchemaDir:   schemaDir,
		TemplateDir: templateDir,
		OutputDir:   outputDir,
	}
}

// Generate gera esquemas Ent a partir de definições JSON
func (g *SchemaGenerator) Generate() error {
	// Ler arquivos de esquema
	files, err := ioutil.ReadDir(g.SchemaDir)
	if err != nil {
		return fmt.Errorf("erro ao ler diretório de esquemas: %v", err)
	}

	// Processar cada arquivo de esquema
	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".json") {
			err := g.processSchemaFile(file.Name())
			if err != nil {
				return fmt.Errorf("erro ao processar arquivo de esquema %s: %v", file.Name(), err)
			}
		}
	}

	return nil
}

// processSchemaFile processa um arquivo de esquema
func (g *SchemaGenerator) processSchemaFile(fileName string) error {
	// Ler arquivo de esquema
	schemaPath := filepath.Join(g.SchemaDir, fileName)
	schemaContent, err := ioutil.ReadFile(schemaPath)
	if err != nil {
		return fmt.Errorf("erro ao ler arquivo de esquema: %v", err)
	}

	// Decodificar esquema
	var schema Schema
	err = json.Unmarshal(schemaContent, &schema)
	if err != nil {
		return fmt.Errorf("erro ao decodificar esquema: %v", err)
	}

	// Gerar esquema Ent
	err = g.generateEntSchema(schema)
	if err != nil {
		return fmt.Errorf("erro ao gerar esquema Ent: %v", err)
	}

	return nil
}

// generateEntSchema gera um esquema Ent a partir de um esquema
func (g *SchemaGenerator) generateEntSchema(schema Schema) error {
	// Ler o arquivo de template
	templatePath := filepath.Join(g.TemplateDir, "ent_schema.go.tmpl")
	templateContent, err := ioutil.ReadFile(templatePath)
	if err != nil {
		return fmt.Errorf("erro ao ler arquivo de template: %v", err)
	}

	// Criar o template
	tmpl, err := template.New("ent_schema").Parse(string(templateContent))
	if err != nil {
		return fmt.Errorf("erro ao analisar template: %v", err)
	}

	// Criar diretório de saída se não existir
	outputDir := filepath.Join(g.OutputDir, "ent/schema")
	err = os.MkdirAll(outputDir, 0755)
	if err != nil {
		return fmt.Errorf("erro ao criar diretório de saída: %v", err)
	}

	// Criar arquivo de saída
	outputFile := filepath.Join(outputDir, strings.ToLower(schema.Name)+".go")
	file, err := os.Create(outputFile)
	if err != nil {
		return fmt.Errorf("erro ao criar arquivo de saída: %v", err)
	}
	defer file.Close()

	// Renderizar o template para o arquivo
	err = tmpl.Execute(file, schema)
	if err != nil {
		return fmt.Errorf("erro ao renderizar template: %v", err)
	}

	fmt.Printf("Esquema Ent gerado com sucesso em %s\n", outputFile)
	return nil
}

func main() {
	if len(os.Args) < 4 {
		fmt.Println("Uso: go run schema_generator.go <diretorio_esquemas> <diretorio_templates> <diretorio_saida>")
		os.Exit(1)
	}

	schemaDir := os.Args[1]
	templateDir := os.Args[2]
	outputDir := os.Args[3]

	generator := NewSchemaGenerator(schemaDir, templateDir, outputDir)
	err := generator.Generate()
	if err != nil {
		fmt.Printf("Erro ao gerar esquemas: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("Esquemas gerados com sucesso")
}
