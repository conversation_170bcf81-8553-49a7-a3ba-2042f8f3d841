{"name": "User", "fields": [{"name": "name", "type": "String", "optional": false, "unique": false}, {"name": "email", "type": "String", "optional": false, "unique": true}, {"name": "password", "type": "String", "optional": false, "unique": false}, {"name": "role", "type": "String", "optional": false, "unique": false, "default": "\"branch_user\""}, {"name": "failed_attempts", "type": "Int", "optional": true, "unique": false, "default": "0"}, {"name": "blocked", "type": "Bool", "optional": true, "unique": false, "default": "false"}, {"name": "totp_secret", "type": "String", "optional": true, "unique": false, "default": "\"\""}, {"name": "totp_enabled", "type": "Bool", "optional": true, "unique": false, "default": "false"}, {"name": "last_password_change", "type": "Time", "optional": true, "unique": false}, {"name": "force_password_change", "type": "Bool", "optional": true, "unique": false, "default": "false"}], "edges": [{"name": "branch", "type": "Branch", "unique": true, "required": false}, {"name": "managed_branches", "type": "Branch", "ref": "manager", "unique": false, "required": false}, {"name": "maintenance_orders", "type": "MaintenanceOrder", "ref": "requested_by", "unique": false, "required": false}, {"name": "created_maintenance_orders", "type": "MaintenanceOrder", "ref": "created_by", "unique": false, "required": false}, {"name": "approved_maintenance_orders", "type": "MaintenanceOrder", "ref": "approved_by", "unique": false, "required": false}], "mixin": ["Time", "SoftDelete"]}