{"name": "MaintenanceOrder", "fields": [{"name": "title", "type": "String", "optional": false, "unique": false}, {"name": "description", "type": "String", "optional": true, "unique": false}, {"name": "status", "type": "String", "optional": false, "unique": false, "default": "\"pending\""}, {"name": "priority", "type": "String", "optional": false, "unique": false, "default": "\"medium\""}, {"name": "scheduled_date", "type": "Time", "optional": true, "unique": false}, {"name": "completion_date", "type": "Time", "optional": true, "unique": false}, {"name": "estimated_cost", "type": "Float", "optional": true, "unique": false}, {"name": "actual_cost", "type": "Float", "optional": true, "unique": false}], "edges": [{"name": "branch", "type": "Branch", "unique": true, "required": true}, {"name": "equipment", "type": "Equipment", "unique": true, "required": true}, {"name": "requested_by", "type": "User", "unique": true, "required": true}, {"name": "created_by", "type": "User", "unique": true, "required": true}, {"name": "approved_by", "type": "User", "unique": true, "required": false}, {"name": "assigned_provider", "type": "ServiceProvider", "unique": true, "required": false}, {"name": "materials", "type": "Material", "ref": "maintenance_order", "unique": false, "required": false}, {"name": "photos", "type": "Photo", "ref": "order", "unique": false, "required": false}, {"name": "calendar_events", "type": "CalendarEvent", "ref": "maintenance_order", "unique": false, "required": false}], "mixin": ["Time", "SoftDelete"]}