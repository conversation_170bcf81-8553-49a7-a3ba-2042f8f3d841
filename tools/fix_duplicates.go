package main

import (
	"fmt"
	"os"
	"regexp"
	"strings"
)

// Função principal para corrigir definições duplicadas
func runFixTemplateDuplicatesExtended() {
	fmt.Println("Corrigindo templates com definições duplicadas...")

	// Lista de arquivos com problemas conhecidos
	problemFiles := []string{
		"web/templates/novapagina/novapagina.html",
		"web/templates/ordens/orders.html",
		"web/templates/ordens/order_detail.html",
	}

	// Corrige cada arquivo com problemas conhecidos
	for _, file := range problemFiles {
		fmt.Printf("Processando arquivo: %s\n", file)
		if err := fixTemplateDuplicates(file); err != nil {
			fmt.Printf("Erro ao corrigir arquivo %s: %v\n", file, err)
		} else {
			fmt.Printf("Arquivo corrigido com sucesso: %s\n", file)
		}
	}

	fmt.Println("\nProcesso de correção concluído!")
}

// Corrige definições duplicadas em um arquivo
func fixTemplateDuplicates(path string) error {
	// Verifica se o arquivo existe
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return fmt.Errorf("arquivo não encontrado: %s", path)
	}

	// Lê o conteúdo do arquivo
	content, err := os.ReadFile(path)
	if err != nil {
		return fmt.Errorf("erro ao ler arquivo: %v", err)
	}

	contentStr := string(content)

	// Expressão regular para encontrar definições de templates
	defineRegex := regexp.MustCompile(`{{\s*define\s+"([^"]+)"\s*}}`)
	matches := defineRegex.FindAllStringSubmatch(contentStr, -1)

	if len(matches) <= 1 {
		fmt.Printf("Arquivo %s não tem definições duplicadas\n", path)
		return nil // Não há definições duplicadas
	}

	// Obtém o nome do template
	templateName := matches[0][1]

	// Verifica se há definições duplicadas
	hasDuplicates := false
	for _, match := range matches[1:] {
		if match[1] == templateName {
			hasDuplicates = true
			break
		}
	}

	if !hasDuplicates {
		fmt.Printf("Arquivo %s não tem definições duplicadas do mesmo template\n", path)
		return nil // Não há definições duplicadas do mesmo template
	}

	fmt.Printf("Encontradas definições duplicadas para o template '%s' no arquivo %s\n", templateName, path)

	// Extrai o conteúdo entre as definições
	sections := make([]string, 0)

	// Divide o conteúdo em seções
	parts := strings.Split(contentStr, "{{ define")

	// A primeira parte é o que vem antes da primeira definição
	header := parts[0]

	// As outras partes são as definições
	for i, part := range parts[1:] {
		if i == 0 {
			// Primeira definição
			sections = append(sections, "{{ define"+part)
		} else {
			// Encontra o final da definição anterior
			endIndex := strings.LastIndex(sections[len(sections)-1], "{{ end }}")
			if endIndex != -1 {
				// Remove o final da definição anterior
				sections[len(sections)-1] = sections[len(sections)-1][:endIndex]
			}

			// Adiciona a nova definição
			sections = append(sections, "{{ define"+part)
		}
	}

	// Cria um novo conteúdo com uma única definição
	newContent := header + "{{ define \"" + templateName + "\" }}\n"

	// Adiciona o conteúdo de cada seção
	for _, section := range sections {
		// Remove a definição e o final
		section = regexp.MustCompile(`{{\s*define\s+"[^"]+"\s*}}`).ReplaceAllString(section, "")
		section = regexp.MustCompile(`{{\s*end\s*}}`).ReplaceAllString(section, "")

		// Adiciona o conteúdo
		newContent += section
	}

	// Adiciona o final
	newContent += "{{ end }}"

	// Escreve o novo conteúdo no arquivo
	err = os.WriteFile(path, []byte(newContent), 0644)
	if err != nil {
		return fmt.Errorf("erro ao escrever arquivo: %v", err)
	}

	fmt.Printf("Arquivo %s corrigido com sucesso\n", path)
	return nil
}
