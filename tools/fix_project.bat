@echo off
rem Script para executar as ferramentas de validação e correção de código
rem Este script compila e executa as ferramentas de validação de código
rem para corrigir problemas comuns no projeto

echo === 🔍 FERRAMENTA DE CORREÇÃO DO PROJETO TENHOAPP ===
echo Esta ferramenta verifica e corrige problemas comuns no código do projeto.

rem Diretório raiz (padrão é o diretório atual)
set ROOT_DIR=%1
if "%ROOT_DIR%"=="" set ROOT_DIR=.

echo.
echo Compilando ferramentas de validação...
go build -o .\tools\template_validator.exe .\tools\template_validator.go
go build -o .\tools\code_validator.exe .\tools\code_validator.go

if %ERRORLEVEL% neq 0 (
    echo Erro ao compilar as ferramentas de validação!
    exit /b 1
)

echo.
echo 🔷 FASE 1: Verificando templates e carregamentos...
.\tools\template_validator.exe "%ROOT_DIR%"

echo.
echo 🔷 FASE 2: Verificando problemas gerais de código...
.\tools\code_validator.exe "%ROOT_DIR%"

echo.
echo ✅ VERIFICAÇÃO CONCLUÍDA!
echo A aplicação deve ser testada para garantir que todas as correções foram aplicadas corretamente.
echo Recomendações finais:
echo 1. Execute 'go build ./...' para verificar se há erros de compilação
echo 2. Inicie o servidor com 'go run cmd/main.go' e teste manualmente as principais funcionalidades
echo 3. Verifique os logs em busca de erros durante a execução

rem Limpa arquivos temporários
echo.
echo Limpando arquivos temporários...
del /Q .\tools\template_validator.exe
del /Q .\tools\code_validator.exe

echo Processo finalizado com sucesso! 