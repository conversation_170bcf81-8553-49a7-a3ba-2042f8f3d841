package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"strings"
)

// ValidationResult armazena resultados da validação
type ValidationResult struct {
	FilePath        string
	OriginalContent string
	UpdatedContent  string
	HasChanges      bool
	Errors          []string
	ProblemsFound   []string
}

// CodeValidator valida e corrige problemas no código
type CodeValidator struct {
	RootDir string
	Results map[string]*ValidationResult
}

// NewCodeValidator cria um novo validador de código
func NewCodeValidator(rootDir string) *CodeValidator {
	return &CodeValidator{
		RootDir: rootDir,
		Results: make(map[string]*ValidationResult),
	}
}

// Run executa a validação e correção
func (v *CodeValidator) Run() error {
	// Percorre todos os arquivos
	err := filepath.Walk(v.RootDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Pula diretórios
		if info.IsDir() {
			return nil
		}

		// Pula diretórios especiais
		if strings.Contains(path, "/vendor/") || strings.Contains(path, "\\vendor\\") ||
			strings.Contains(path, "/node_modules/") || strings.Contains(path, "\\node_modules\\") ||
			strings.Contains(path, "/.git/") || strings.Contains(path, "\\.git\\") {
			return nil
		}

		// Processa o arquivo de acordo com sua extensão
		ext := filepath.Ext(path)
		switch ext {
		case ".go":
			return v.processGoFile(path, info.Mode())
		case ".html":
			return v.processHTMLFile(path, info.Mode())
		case ".js":
			return v.processJSFile(path, info.Mode())
		case ".json":
			return v.processJSONFile(path, info.Mode())
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("erro ao percorrer arquivos: %v", err)
	}

	return nil
}

// Inicia um resultado de validação para um arquivo
func (v *CodeValidator) initResult(path string, content string) *ValidationResult {
	result := &ValidationResult{
		FilePath:        path,
		OriginalContent: content,
		UpdatedContent:  content,
		HasChanges:      false,
		Errors:          []string{},
		ProblemsFound:   []string{},
	}
	v.Results[path] = result
	return result
}

// Salva as alterações no arquivo se houver mudanças
func (v *CodeValidator) saveChanges(result *ValidationResult, fileMode os.FileMode) {
	if result.HasChanges {
		err := ioutil.WriteFile(result.FilePath, []byte(result.UpdatedContent), fileMode)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("erro ao salvar arquivo: %v", err))
		}
	}
}

// Processa arquivos Go
func (v *CodeValidator) processGoFile(path string, fileMode os.FileMode) error {
	content, err := ioutil.ReadFile(path)
	if err != nil {
		return fmt.Errorf("erro ao ler arquivo %s: %v", path, err)
	}

	result := v.initResult(path, string(content))

	// Verifica e corrige problemas comuns em arquivos Go
	updatedContent, hasChanges, problems := v.fixGoCommonIssues(result.UpdatedContent)
	result.UpdatedContent = updatedContent
	result.HasChanges = result.HasChanges || hasChanges
	result.ProblemsFound = append(result.ProblemsFound, problems...)

	// Verifica e corrige carregamentos redundantes de templates
	updatedContent, hasChanges, problems = v.fixTemplateLoading(result.UpdatedContent)
	result.UpdatedContent = updatedContent
	result.HasChanges = result.HasChanges || hasChanges
	result.ProblemsFound = append(result.ProblemsFound, problems...)

	// Verifica referências incorretas a templates
	updatedContent, hasChanges, problems = v.fixTemplateReferences(result.UpdatedContent)
	result.UpdatedContent = updatedContent
	result.HasChanges = result.HasChanges || hasChanges
	result.ProblemsFound = append(result.ProblemsFound, problems...)

	// Salva as alterações se necessário
	v.saveChanges(result, fileMode)

	return nil
}

// Processa arquivos HTML
func (v *CodeValidator) processHTMLFile(path string, fileMode os.FileMode) error {
	content, err := ioutil.ReadFile(path)
	if err != nil {
		return fmt.Errorf("erro ao ler arquivo %s: %v", path, err)
	}

	result := v.initResult(path, string(content))

	// Verifica e corrige problemas de HTML
	updatedContent, hasChanges, problems := v.fixHTMLIssues(result.UpdatedContent, path)
	result.UpdatedContent = updatedContent
	result.HasChanges = result.HasChanges || hasChanges
	result.ProblemsFound = append(result.ProblemsFound, problems...)

	// Verifica e corrige URLs incorretas de API em formulários e AJAX
	updatedContent, hasChanges, problems = v.fixAPIURLs(result.UpdatedContent)
	result.UpdatedContent = updatedContent
	result.HasChanges = result.HasChanges || hasChanges
	result.ProblemsFound = append(result.ProblemsFound, problems...)

	// Salva as alterações se necessário
	v.saveChanges(result, fileMode)

	return nil
}

// Processa arquivos JavaScript
func (v *CodeValidator) processJSFile(path string, fileMode os.FileMode) error {
	content, err := ioutil.ReadFile(path)
	if err != nil {
		return fmt.Errorf("erro ao ler arquivo %s: %v", path, err)
	}

	result := v.initResult(path, string(content))

	// Verifica e corrige problemas de JavaScript
	updatedContent, hasChanges, problems := v.fixJSIssues(result.UpdatedContent)
	result.UpdatedContent = updatedContent
	result.HasChanges = result.HasChanges || hasChanges
	result.ProblemsFound = append(result.ProblemsFound, problems...)

	// Salva as alterações se necessário
	v.saveChanges(result, fileMode)

	return nil
}

// Processa arquivos JSON
func (v *CodeValidator) processJSONFile(path string, fileMode os.FileMode) error {
	content, err := ioutil.ReadFile(path)
	if err != nil {
		return fmt.Errorf("erro ao ler arquivo %s: %v", path, err)
	}

	result := v.initResult(path, string(content))

	// Verifica e corrige problemas de JSON
	updatedContent, hasChanges, problems := v.fixJSONIssues(result.UpdatedContent)
	result.UpdatedContent = updatedContent
	result.HasChanges = result.HasChanges || hasChanges
	result.ProblemsFound = append(result.ProblemsFound, problems...)

	// Salva as alterações se necessário
	v.saveChanges(result, fileMode)

	return nil
}

// Corrige problemas comuns em Go
func (v *CodeValidator) fixGoCommonIssues(content string) (string, bool, []string) {
	hasChanges := false
	problems := []string{}
	updatedContent := content

	// Procura por comentários de código que podem ser removidos
	commentPattern := regexp.MustCompile(`(?m)^\s*//\s*TODO.*$`)
	if commentPattern.MatchString(updatedContent) {
		problems = append(problems, "Encontrados comentários TODO")
	}

	// Procura por funções muito longas (indício de complexidade)
	longFuncPattern := regexp.MustCompile(`func [^{]+{(?:[^{}]|{[^{}]*})*{(?:[^{}]|{[^{}]*})*{(?:[^{}]|{[^{}]*})*}[^{}]*}[^{}]*}`)
	if longFuncPattern.MatchString(updatedContent) {
		problems = append(problems, "Encontradas funções potencialmente muito complexas")
	}

	return updatedContent, hasChanges, problems
}

// Corrige carregamentos redundantes de templates
func (v *CodeValidator) fixTemplateLoading(content string) (string, bool, []string) {
	hasChanges := false
	problems := []string{}
	updatedContent := content

	// Procura pelo padrão LoadHTMLGlob("web/templates/*") após LoadHTMLGlob("web/templates/**/*")
	doubleLoadPattern := regexp.MustCompile(`(router|r|engine)\.LoadHTMLGlob\("web/templates/\*\*/\*"\)[\s\n]+.*LoadHTMLGlob\("web/templates/\*"\)`)

	if doubleLoadPattern.MatchString(updatedContent) {
		problems = append(problems, "Carregamento redundante de templates (LoadHTMLGlob)")
		updatedContent = doubleLoadPattern.ReplaceAllStringFunc(updatedContent, func(matchedString string) string {
			lines := strings.Split(matchedString, "\n")

			// Comentar a segunda linha de carregamento
			for i := 1; i < len(lines); i++ {
				if strings.Contains(lines[i], "LoadHTMLGlob") && strings.Contains(lines[i], "web/templates/*") {
					// Comentar a linha
					lines[i] = strings.TrimSpace(lines[i])
					if !strings.HasPrefix(lines[i], "//") {
						lines[i] = "// " + lines[i] + " // Comentado automaticamente - carregamento redundante"
					}
					break
				}
			}

			return strings.Join(lines, "\n")
		})

		hasChanges = true
	}

	return updatedContent, hasChanges, problems
}

// Corrige referências incorretas a templates
func (v *CodeValidator) fixTemplateReferences(content string) (string, bool, []string) {
	hasChanges := false
	problems := []string{}
	updatedContent := content

	// Padrões de referências incorretas a templates
	templates := map[string]string{
		"login.html":       "login/login.html",
		"dashboard.html":   "dashboard/dashboard.html",
		"calendar.html":    "calendarios/calendar.html",
		"minha_conta.html": "minhaconta/minha-conta.html",
		"settings.html":    "settings/settings.html",
	}

	for oldRef, newRef := range templates {
		pattern := regexp.MustCompile(`c\.HTML\([^,]+, *"` + oldRef + `"`)
		if pattern.MatchString(updatedContent) {
			problems = append(problems, fmt.Sprintf("Referência incorreta ao template %s", oldRef))
			updatedContent = pattern.ReplaceAllStringFunc(updatedContent, func(matchedString string) string {
				return strings.Replace(matchedString, `"`+oldRef+`"`, `"`+newRef+`"`, 1)
			})
			hasChanges = true
		}
	}

	return updatedContent, hasChanges, problems
}

// Corrige problemas em HTML
func (v *CodeValidator) fixHTMLIssues(content string, path string) (string, bool, []string) {
	hasChanges := false
	problems := []string{}
	updatedContent := content

	// Verifica tags HTML não fechadas
	openTags := []string{"div", "span", "p", "section", "form", "button"}
	for _, tag := range openTags {
		openPattern := regexp.MustCompile(`<` + tag + `[^>]*>`)
		closePattern := regexp.MustCompile(`</` + tag + `>`)

		openCount := len(openPattern.FindAllString(updatedContent, -1))
		closeCount := len(closePattern.FindAllString(updatedContent, -1))

		if openCount > closeCount {
			problems = append(problems, fmt.Sprintf("Possível tag <%s> não fechada (%d aberturas vs %d fechamentos)", tag, openCount, closeCount))
		}
	}

	// Verifica DOCTYPE faltando em documentos HTML completos
	if strings.Contains(path, ".html") &&
		!strings.Contains(path, "partial") &&
		!strings.Contains(path, "component") &&
		!regexp.MustCompile(`<!DOCTYPE\s+html`).MatchString(updatedContent) {
		problems = append(problems, "DOCTYPE HTML faltando")
	}

	return updatedContent, hasChanges, problems
}

// Corrige URLs incorretas de API em formulários e AJAX
func (v *CodeValidator) fixAPIURLs(content string) (string, bool, []string) {
	hasChanges := false
	problems := []string{}
	updatedContent := content

	// Padrões de URLs incorretas e suas substituições
	urlPatterns := map[string]string{
		`action="/auth"`:         `action="/api/auth/login"`,
		`action="/api/login"`:    `action="/api/auth/login"`,
		`fetch\("/login"`:        `fetch("/api/auth/login"`,
		`fetch\("/auth"`:         `fetch("/api/auth/login"`,
		`url:\s*["']auth["']`:    `url: "/api/auth/login"`,
		`url:\s*["']/login["']`:  `url: "/api/auth/login"`,
		`url:\s*["']/logout["']`: `url: "/api/auth/logout"`,
	}

	for oldURL, newURL := range urlPatterns {
		pattern := regexp.MustCompile(oldURL)
		if pattern.MatchString(updatedContent) {
			problems = append(problems, fmt.Sprintf("URL incorreta encontrada: %s", oldURL))
			updatedContent = pattern.ReplaceAllString(updatedContent, newURL)
			hasChanges = true
		}
	}

	return updatedContent, hasChanges, problems
}

// Corrige problemas em JavaScript
func (v *CodeValidator) fixJSIssues(content string) (string, bool, []string) {
	hasChanges := false
	problems := []string{}
	updatedContent := content

	// Verifica tratamento inadequado de erros em AJAX/fetch
	if strings.Contains(updatedContent, "fetch(") &&
		!strings.Contains(updatedContent, ".catch(") {
		problems = append(problems, "Possível falta de tratamento de erro em requisição fetch")
	}

	// Procura por potenciais erros de sintaxe JSON.parse sem try/catch
	if strings.Contains(updatedContent, "JSON.parse") &&
		!strings.Contains(updatedContent, "try") {
		problems = append(problems, "Uso de JSON.parse sem try/catch")
	}

	return updatedContent, hasChanges, problems
}

// Corrige problemas em JSON
func (v *CodeValidator) fixJSONIssues(content string) (string, bool, []string) {
	hasChanges := false
	problems := []string{}
	updatedContent := content

	// Verifica vírgulas no final de objetos JSON
	trailingCommaPattern := regexp.MustCompile(`,[\\s\\n]*[\\}\\]]`)
	if trailingCommaPattern.MatchString(updatedContent) {
		problems = append(problems, "Possíveis vírgulas finais em objetos JSON (não permitido pela especificação)")
	}

	return updatedContent, hasChanges, problems
}

// PrintResults imprime os resultados da validação
func (v *CodeValidator) PrintResults() {
	fmt.Println("\n==== RESULTADOS DA VALIDAÇÃO DE CÓDIGO ====\n")

	changedFiles := 0
	totalProblems := 0

	// Agrupa problemas por tipo
	problemTypes := make(map[string]int)

	for path, result := range v.Results {
		if len(result.ProblemsFound) > 0 {
			totalProblems += len(result.ProblemsFound)
			fmt.Printf("Arquivo: %s\n", path)
			fmt.Printf("  Problemas encontrados (%d):\n", len(result.ProblemsFound))

			for _, problem := range result.ProblemsFound {
				fmt.Printf("    - %s\n", problem)
				problemTypes[problem]++
			}

			if result.HasChanges {
				changedFiles++
				fmt.Printf("  ✅ Correções aplicadas\n")
			} else {
				fmt.Printf("  ⚠️ Problemas identificados mas não corrigidos automaticamente\n")
			}
			fmt.Println()
		}
	}

	fmt.Println("==== SUMÁRIO ====")
	fmt.Printf("Total de arquivos analisados: %d\n", len(v.Results))
	fmt.Printf("Arquivos com problemas: %d\n", totalProblems)
	fmt.Printf("Arquivos modificados: %d\n", changedFiles)

	if len(problemTypes) > 0 {
		fmt.Println("\nTipos de problemas encontrados:")
		for problem, count := range problemTypes {
			fmt.Printf("  - %s: %d ocorrências\n", problem, count)
		}
	}
}

// RunCodeValidation executa a validação e correção completa
func RunCodeValidation(rootDir string) {
	validator := NewCodeValidator(rootDir)
	err := validator.Run()

	if err != nil {
		log.Fatalf("Erro durante a validação: %v", err)
	}

	validator.PrintResults()
}

// A função main está comentada para evitar conflito com main de template_validator.go
// Para usar este validador diretamente, descomente a função main
/*
func main() {
	rootDir := "."
	if len(os.Args) > 1 {
		rootDir = os.Args[1]
	}

	fmt.Printf("Iniciando validação e correção de código no diretório: %s\n", rootDir)
	RunCodeValidation(rootDir)
	fmt.Println("\nProcesso de validação concluído. Verifique os resultados e teste a aplicação.")
}
*/
