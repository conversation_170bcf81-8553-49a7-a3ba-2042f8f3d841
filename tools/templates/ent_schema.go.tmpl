package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/mixin"
)

// {{.Name}} holds the schema definition for the {{.Name}} entity.
type {{.Name}} struct {
	ent.Schema
}

// Fields of the {{.Name}}.
func ({{.Name}}) Fields() []ent.Field {
	return []ent.Field{
		{{range .Fields}}field.{{.Type}}("{{.Name}}").
			{{if .Optional}}Optional().{{end}}
			{{if .Unique}}Unique().{{end}}
			{{if .Default}}Default({{.Default}}).{{end}}
			Comment("{{.Name}} field"),
		{{end}}
	}
}

// Edges of the {{.Name}}.
func ({{.Name}}) Edges() []ent.Edge {
	return []ent.Edge{
		{{range .Edges}}edge.{{if .Unique}}To{{else}}From{{end}}("{{.Name}}", {{.Type}}.Type).
			{{if .Ref}}Ref("{{.Ref}}").{{end}}
			{{if .Unique}}Unique().{{end}}
			{{if .Required}}Required().{{end}}
			Comment("{{.Name}} edge"),
		{{end}}
	}
}

// Mixin of the {{.Name}}.
func ({{.Name}}) Mixin() []ent.Mixin {
	return []ent.Mixin{
		{{range .Mixin}}mixin.{{.}}(),
		{{end}}
	}
}
