package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/mixin"
)

// SoftDeleteMixin implements the soft delete pattern for schemas.
type SoftDeleteMixin struct {
	// We embed the `mixin.Schema` to avoid
	// implementing the rest of the methods.
	mixin.Schema
}

// Fields of the SoftDeleteMixin.
func (SoftDeleteMixin) Fields() []ent.Field {
	return []ent.Field{
		field.Time("deleted_at").
			Optional().
			Nillable().
			Comment("Deletion time of the record"),
	}
}

// Hooks of the SoftDeleteMixin.
func (SoftDeleteMixin) Hooks() []ent.Hook {
	return []ent.Hook{
		// Hook that soft-deletes entities by setting
		// the "deleted_at" field instead of deleting
		// the entity from the database.
	}
}
