package {{.Package}}

import (
	"time"
)

// {{.Name}} representa a entidade {{.Name}}
type {{.Name}} struct {
	ID        int       `json:"id"`
	{{range .Fields}}{{.Name}} {{.Type}} `json:"{{.JSONName}}"`
	{{end}}
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at,omitempty"`
}

// {{.Name}}Repository define a interface para o repositório de {{.Name}}
type {{.Name}}Repository interface {
	Find(id int) (*{{.Name}}, error)
	FindAll() ([]*{{.Name}}, error)
	Create(entity *{{.Name}}) error
	Update(entity *{{.Name}}) error
	Delete(id int) error
}

// {{.Name}}Service define a interface para o serviço de {{.Name}}
type {{.Name}}Service interface {
	Find(id int) (*{{.Name}}, error)
	FindAll() ([]*{{.Name}}, error)
	Create(entity *{{.Name}}) error
	Update(entity *{{.Name}}) error
	Delete(id int) error
}

// {{.Name}}Handler define a interface para o handler de {{.Name}}
type {{.Name}}Handler interface {
	Find(id int) (*{{.Name}}, error)
	FindAll() ([]*{{.Name}}, error)
	Create(entity *{{.Name}}) error
	Update(entity *{{.Name}}) error
	Delete(id int) error
}
