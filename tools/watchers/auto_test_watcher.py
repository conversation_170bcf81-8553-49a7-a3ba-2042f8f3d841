#!/usr/bin/env python3
"""
Script de monitoramento automático de testes
Este script monitora alterações nos arquivos do projeto e executa testes automaticamente.
"""

import os
import sys
import time
import subprocess
import hashlib
from datetime import datetime
import argparse

# Configurações padrão
DEFAULT_WATCH_DIRS = ["internal", "web", "cmd"]
DEFAULT_TEST_COMMAND = "go test ./..."
DEFAULT_IGNORE_PATTERNS = [".git", "__pycache__", "node_modules", ".vscode", "*.pyc", "*.exe", "*.log"]
DEFAULT_INTERVAL = 2  # segundos

class AutoTestWatcher:
    def __init__(self, watch_dirs, test_command, ignore_patterns, interval):
        self.watch_dirs = watch_dirs
        self.test_command = test_command
        self.ignore_patterns = ignore_patterns
        self.interval = interval
        self.file_hashes = {}
        self.last_run_time = 0
        
    def should_ignore(self, path):
        """Verifica se um caminho deve ser ignorado com base nos padrões de ignorar."""
        for pattern in self.ignore_patterns:
            if pattern.startswith("*"):
                if path.endswith(pattern[1:]):
                    return True
            elif pattern in path:
                return True
        return False
    
    def calculate_file_hashes(self):
        """Calcula hashes de todos os arquivos nos diretórios monitorados."""
        new_hashes = {}
        for watch_dir in self.watch_dirs:
            if not os.path.exists(watch_dir):
                print(f"Aviso: Diretório {watch_dir} não existe. Ignorando.")
                continue
                
            for root, dirs, files in os.walk(watch_dir):
                # Filtrar diretórios ignorados
                dirs[:] = [d for d in dirs if not self.should_ignore(os.path.join(root, d))]
                
                for file in files:
                    file_path = os.path.join(root, file)
                    if self.should_ignore(file_path):
                        continue
                        
                    try:
                        with open(file_path, 'rb') as f:
                            file_hash = hashlib.md5(f.read()).hexdigest()
                            new_hashes[file_path] = file_hash
                    except Exception as e:
                        print(f"Erro ao ler arquivo {file_path}: {e}")
        
        return new_hashes
    
    def files_changed(self):
        """Verifica se algum arquivo foi alterado desde a última verificação."""
        new_hashes = self.calculate_file_hashes()
        
        if not self.file_hashes:
            self.file_hashes = new_hashes
            return False
        
        # Verificar arquivos alterados ou adicionados
        changed_files = []
        for file_path, file_hash in new_hashes.items():
            if file_path not in self.file_hashes or self.file_hashes[file_path] != file_hash:
                changed_files.append(file_path)
        
        # Verificar arquivos removidos
        for file_path in self.file_hashes:
            if file_path not in new_hashes:
                changed_files.append(f"{file_path} (removido)")
        
        self.file_hashes = new_hashes
        return changed_files
    
    def run_tests(self):
        """Executa os testes e retorna o resultado."""
        print(f"\n{'='*80}")
        print(f"Executando testes: {self.test_command}")
        print(f"Data/Hora: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'-'*80}")
        
        try:
            start_time = time.time()
            process = subprocess.Popen(
                self.test_command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            stdout, stderr = process.communicate()
            
            duration = time.time() - start_time
            
            print(stdout)
            if stderr:
                print("ERROS:")
                print(stderr)
            
            if process.returncode == 0:
                print(f"\n✅ TESTES PASSARAM! ({duration:.2f}s)")
            else:
                print(f"\n❌ TESTES FALHARAM! ({duration:.2f}s)")
            
            print(f"{'='*80}\n")
            return process.returncode == 0
            
        except Exception as e:
            print(f"Erro ao executar testes: {e}")
            return False
    
    def watch(self):
        """Monitora alterações nos arquivos e executa testes quando necessário."""
        print(f"Iniciando monitoramento de testes automáticos...")
        print(f"Monitorando diretórios: {', '.join(self.watch_dirs)}")
        print(f"Comando de teste: {self.test_command}")
        print(f"Intervalo de verificação: {self.interval} segundos")
        print(f"Pressione Ctrl+C para sair")
        
        # Calcular hashes iniciais
        self.file_hashes = self.calculate_file_hashes()
        print(f"Monitorando {len(self.file_hashes)} arquivos")
        
        try:
            while True:
                changed_files = self.files_changed()
                
                if changed_files:
                    print(f"\nAlterações detectadas em {len(changed_files)} arquivos:")
                    for file in changed_files[:5]:  # Mostrar apenas os primeiros 5 arquivos
                        print(f"  - {file}")
                    if len(changed_files) > 5:
                        print(f"  - ... e {len(changed_files) - 5} mais")
                    
                    # Aguardar um pouco para garantir que todas as alterações foram salvas
                    time.sleep(0.5)
                    
                    # Executar testes
                    self.run_tests()
                    self.last_run_time = time.time()
                
                time.sleep(self.interval)
                
        except KeyboardInterrupt:
            print("\nMonitoramento interrompido pelo usuário.")
            return

def main():
    parser = argparse.ArgumentParser(description="Monitora alterações nos arquivos e executa testes automaticamente.")
    parser.add_argument("--dirs", nargs="+", default=DEFAULT_WATCH_DIRS, 
                        help=f"Diretórios para monitorar (padrão: {' '.join(DEFAULT_WATCH_DIRS)})")
    parser.add_argument("--command", default=DEFAULT_TEST_COMMAND, 
                        help=f"Comando de teste para executar (padrão: '{DEFAULT_TEST_COMMAND}')")
    parser.add_argument("--ignore", nargs="+", default=DEFAULT_IGNORE_PATTERNS, 
                        help=f"Padrões para ignorar (padrão: {' '.join(DEFAULT_IGNORE_PATTERNS)})")
    parser.add_argument("--interval", type=float, default=DEFAULT_INTERVAL, 
                        help=f"Intervalo de verificação em segundos (padrão: {DEFAULT_INTERVAL})")
    
    args = parser.parse_args()
    
    watcher = AutoTestWatcher(
        watch_dirs=args.dirs,
        test_command=args.command,
        ignore_patterns=args.ignore,
        interval=args.interval
    )
    
    watcher.watch()

if __name__ == "__main__":
    main()
