//go:build test_watcher
// +build test_watcher

package main

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

// Configurações
var (
	watchDirs          = []string{"internal", "web", "cmd"}
	defaultTestCommand = "go test ./..."
	ignorePatterns     = []string{".git", "__pycache__", "node_modules", ".vscode", ".exe", ".log"}
	checkInterval      = 2 * time.Second
	fileHashes         = make(map[string]string)
	lastTestRunTime    time.Time
	isWindows          = runtime.GOOS == "windows"
	logFile            = "test_errors.log"
	ignoreErrorsFile   = "ignore_errors.txt"
	ignoreErrors       = make(map[string]bool)

	// Mapeamento de tipos de arquivo para comandos de teste específicos
	testCommands map[string]string

	// Mapeamento de erros comuns para sugestões de correção
	errorSuggestions = map[string]string{
		"undefined":           "Verifique se todas as variáveis estão definidas antes de usá-las.",
		"syntax error":        "Há um erro de sintaxe no código. Verifique parênteses, chaves e ponto-e-vírgula.",
		"cannot find package": "Pacote não encontrado. Execute 'go get' para instalar a dependência.",
		"failed to compile":   "Erro de compilação. Verifique a sintaxe e tipos de dados.",
		"permission denied":   "Problema de permissão. Verifique as permissões do arquivo ou diretório.",
		"404":                 "Recurso não encontrado. Verifique URLs e caminhos.",
		"connection refused":  "Conexão recusada. Verifique se o serviço está em execução.",
		"timeout":             "Tempo limite excedido. Verifique a conexão de rede ou aumente o timeout.",
		"out of memory":       "Memória insuficiente. Otimize o uso de memória ou aumente os recursos.",
		"null pointer":        "Referência nula. Verifique se o objeto foi inicializado antes de acessá-lo.",
		"invalid argument":    "Argumento inválido. Verifique os parâmetros passados para a função.",
		"file not found":      "Arquivo não encontrado. Verifique o caminho e nome do arquivo.",
		"database error":      "Erro de banco de dados. Verifique a conexão e consultas SQL.",
	}
)

func init() {
	// Inicializar comandos de teste com base no sistema operacional
	testCommands = make(map[string]string)

	// Comandos comuns para ambos os sistemas
	testCommands[".go"] = "go test ./..."
	testCommands["internal/"] = "go test ./internal/..."
	testCommands["cmd/"] = "go test ./cmd/..."
	testCommands["api/"] = "go test ./api/..."
	testCommands["services/"] = "go test ./services/..."

	// Comandos específicos para cada sistema operacional
	if isWindows {
		// Comandos para Windows
		testCommands[".js"] = "cmd /c echo Executando testes de JavaScript... && go run cmd/main.go test-js"
		testCommands["web/static/js/"] = "cmd /c echo Verificando JavaScript... && go run cmd/main.go lint-js"
		testCommands[".html"] = "cmd /c echo Verificando templates HTML... && go run cmd/main.go validate-templates"
		testCommands["web/templates/"] = "cmd /c echo Verificando templates... && go run cmd/main.go validate-templates"
		testCommands[".css"] = "cmd /c echo Verificando CSS... && go run cmd/main.go lint-css"
		testCommands["web/static/css/"] = "cmd /c echo Verificando CSS... && go run cmd/main.go lint-css"
		testCommands[".json"] = "cmd /c echo Validando JSON... && go run cmd/main.go validate-json"
		testCommands[".yaml"] = "cmd /c echo Validando YAML... && go run cmd/main.go validate-yaml"
		testCommands[".yml"] = "cmd /c echo Validando YAML... && go run cmd/main.go validate-yaml"
	} else {
		// Comandos para Linux/Mac
		testCommands[".js"] = "echo 'Executando testes de JavaScript...' && go run cmd/main.go test-js"
		testCommands["web/static/js/"] = "echo 'Verificando JavaScript...' && go run cmd/main.go lint-js"
		testCommands[".html"] = "echo 'Verificando templates HTML...' && go run cmd/main.go validate-templates"
		testCommands["web/templates/"] = "echo 'Verificando templates...' && go run cmd/main.go validate-templates"
		testCommands[".css"] = "echo 'Verificando CSS...' && go run cmd/main.go lint-css"
		testCommands["web/static/css/"] = "echo 'Verificando CSS...' && go run cmd/main.go lint-css"
		testCommands[".json"] = "echo 'Validando JSON...' && go run cmd/main.go validate-json"
		testCommands[".yaml"] = "echo 'Validando YAML...' && go run cmd/main.go validate-yaml"
		testCommands[".yml"] = "echo 'Validando YAML...' && go run cmd/main.go validate-yaml"
	}
}

func main() {
	fmt.Println("Iniciando monitoramento de testes automáticos...")
	fmt.Printf("Sistema operacional: %s\n", runtime.GOOS)
	fmt.Printf("Monitorando diretórios: %s\n", strings.Join(watchDirs, ", "))
	fmt.Printf("Comando de teste padrão: %s\n", defaultTestCommand)
	fmt.Printf("Intervalo de verificação: %v\n", checkInterval)
	fmt.Println("Pressione Ctrl+C para sair")

	// Calcular hashes iniciais
	calculateFileHashes()
	fmt.Printf("Monitorando %d arquivos\n", len(fileHashes))

	// Loop principal
	for {
		time.Sleep(checkInterval)
		changedFiles := checkForChanges()
		if len(changedFiles) > 0 {
			fmt.Printf("\nAlterações detectadas em %d arquivos:\n", len(changedFiles))
			for i, file := range changedFiles {
				if i < 5 {
					fmt.Printf("  - %s\n", file)
				} else if i == 5 {
					fmt.Printf("  - ... e %d mais\n", len(changedFiles)-5)
					break
				}
			}

			// Aguardar um pouco para garantir que todas as alterações foram salvas
			time.Sleep(500 * time.Millisecond)

			// Determinar quais testes executar com base nos arquivos alterados
			testsToRun := determineTestsToRun(changedFiles)

			// Executar testes
			for _, testCmd := range testsToRun {
				runTests(testCmd)
			}

			lastTestRunTime = time.Now()
		}
	}
}

// calculateFileHashes calcula os hashes MD5 de todos os arquivos nos diretórios monitorados
func calculateFileHashes() {
	newHashes := make(map[string]string)

	for _, dir := range watchDirs {
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			fmt.Printf("Aviso: Diretório %s não existe. Ignorando.\n", dir)
			continue
		}

		err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}

			// Normalizar caminho para comparações consistentes entre sistemas
			path = normalizePath(path)

			// Ignorar diretórios
			if info.IsDir() {
				for _, pattern := range ignorePatterns {
					if strings.Contains(path, pattern) {
						return filepath.SkipDir
					}
				}
				return nil
			}

			// Verificar se o arquivo deve ser ignorado
			for _, pattern := range ignorePatterns {
				if strings.Contains(path, pattern) {
					return nil
				}
			}

			// Calcular hash do arquivo
			hash, err := calculateMD5(path)
			if err != nil {
				fmt.Printf("Erro ao calcular hash para %s: %v\n", path, err)
				return nil
			}

			newHashes[path] = hash
			return nil
		})

		if err != nil {
			fmt.Printf("Erro ao percorrer diretório %s: %v\n", dir, err)
		}
	}

	fileHashes = newHashes
}

// normalizePath normaliza um caminho para usar barras normais (/) em todos os sistemas
func normalizePath(path string) string {
	if isWindows {
		return strings.ReplaceAll(path, "\\", "/")
	}
	return path
}

// calculateMD5 calcula o hash MD5 de um arquivo
func calculateMD5(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	return hex.EncodeToString(hash.Sum(nil)), nil
}

// checkForChanges verifica se algum arquivo foi alterado desde a última verificação
func checkForChanges() []string {
	newHashes := make(map[string]string)
	changedFiles := []string{}

	for _, dir := range watchDirs {
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			continue
		}

		err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}

			// Normalizar caminho para comparações consistentes entre sistemas
			path = normalizePath(path)

			// Ignorar diretórios
			if info.IsDir() {
				for _, pattern := range ignorePatterns {
					if strings.Contains(path, pattern) {
						return filepath.SkipDir
					}
				}
				return nil
			}

			// Verificar se o arquivo deve ser ignorado
			for _, pattern := range ignorePatterns {
				if strings.Contains(path, pattern) {
					return nil
				}
			}

			// Calcular hash do arquivo
			hash, err := calculateMD5(path)
			if err != nil {
				return nil
			}

			newHashes[path] = hash

			// Verificar se o arquivo foi alterado
			oldHash, exists := fileHashes[path]
			if !exists || oldHash != hash {
				changedFiles = append(changedFiles, path)
			}

			return nil
		})

		if err != nil {
			fmt.Printf("Erro ao percorrer diretório %s: %v\n", dir, err)
		}
	}

	// Verificar arquivos removidos
	for path := range fileHashes {
		if _, exists := newHashes[path]; !exists {
			changedFiles = append(changedFiles, path+" (removido)")
		}
	}

	fileHashes = newHashes
	return changedFiles
}

// determineTestsToRun determina quais testes executar com base nos arquivos alterados
func determineTestsToRun(changedFiles []string) []string {
	// Usar um map para evitar comandos duplicados
	testCmdsMap := make(map[string]bool)

	// Verificar cada arquivo alterado
	for _, file := range changedFiles {
		// Ignorar arquivos removidos
		if strings.Contains(file, "(removido)") {
			continue
		}

		// Normalizar caminho para comparações consistentes entre sistemas
		file = normalizePath(file)

		// Verificar se o arquivo corresponde a algum padrão específico
		commandFound := false

		// Primeiro, verificar diretórios específicos
		for pattern, cmd := range testCommands {
			if strings.HasPrefix(pattern, "/") || strings.HasSuffix(pattern, "/") {
				// É um padrão de diretório
				if strings.Contains(file, pattern) {
					testCmdsMap[cmd] = true
					commandFound = true
				}
			}
		}

		// Depois, verificar extensões de arquivo
		if !commandFound {
			ext := filepath.Ext(file)
			if cmd, ok := testCommands[ext]; ok {
				testCmdsMap[cmd] = true
				commandFound = true
			}
		}

		// Se nenhum comando específico foi encontrado, usar o comando padrão
		if !commandFound {
			testCmdsMap[defaultTestCommand] = true
		}
	}

	// Converter o map para slice
	testCmds := make([]string, 0, len(testCmdsMap))
	for cmd := range testCmdsMap {
		testCmds = append(testCmds, cmd)
	}

	// Se nenhum comando foi determinado, usar o comando padrão
	if len(testCmds) == 0 {
		testCmds = append(testCmds, defaultTestCommand)
	}

	return testCmds
}

// runTests executa os testes e exibe os resultados
func runTests(testCommand string) {
	fmt.Println("\n================================================================================")
	fmt.Printf("Executando testes: %s\n", testCommand)
	fmt.Printf("Data/Hora: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Println("--------------------------------------------------------------------------------")

	startTime := time.Now()

	var cmd *exec.Cmd
	if isWindows {
		// No Windows, usamos cmd /c para executar comandos complexos
		if strings.Contains(testCommand, "&&") {
			cmd = exec.Command("cmd", "/c", testCommand)
		} else {
			// Dividir o comando em partes
			cmdParts := strings.Fields(testCommand)
			cmd = exec.Command(cmdParts[0], cmdParts[1:]...)
		}
	} else {
		// No Linux/Mac, podemos usar /bin/sh -c
		if strings.Contains(testCommand, "&&") {
			cmd = exec.Command("/bin/sh", "-c", testCommand)
		} else {
			// Dividir o comando em partes
			cmdParts := strings.Fields(testCommand)
			cmd = exec.Command(cmdParts[0], cmdParts[1:]...)
		}
	}

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	err := cmd.Run()

	duration := time.Since(startTime)

	if err != nil {
		fmt.Printf("\n❌ TESTES FALHARAM! (%.2f segundos)\n", duration.Seconds())
	} else {
		fmt.Printf("\n✅ TESTES PASSARAM! (%.2f segundos)\n", duration.Seconds())
	}

	fmt.Println("================================================================================\n")
}
