package main

import (
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
)

// Regras para validação de templates
type TemplateRule struct {
	Name        string
	Description string
	Validator   func(content string, path string) (bool, string)
}

type TemplateStructureValidationResult struct {
	FilePath        string
	OriginalContent string
	UpdatedContent  string
	HasChanges      bool
	Errors          []string
}

func runTemplateStructureValidation() {
	// Diretório de templates
	templatesDir := "web/templates"
	if len(os.Args) > 2 {
		templatesDir = os.Args[2]
	}

	// Verifica se o diretório existe
	if _, err := os.Stat(templatesDir); os.IsNotExist(err) {
		fmt.Printf("Diretório de templates não encontrado: %s\n", templatesDir)
		os.Exit(1)
	}

	// Define as regras de validação
	rules := []TemplateRule{
		{
			Name:        "Definição única de template",
			Description: "Cada template deve ter apenas uma definição com {{ define \"nome\" }}",
			Validator:   validateSingleDefinition,
		},
		{
			Name:        "Nome de template correto",
			Description: "O nome do template deve corresponder ao caminho relativo do arquivo",
			Validator:   validateTemplateName,
		},
		{
			Name:        "DOCTYPE e viewport",
			Description: "Templates completos devem ter DOCTYPE e meta viewport",
			Validator:   validateDocTypeAndViewport,
		},
		{
			Name:        "Estrutura de layout",
			Description: "Templates devem seguir a estrutura de layout com sidebar e content-with-sidebar",
			Validator:   validateLayoutStructure,
		},
		{
			Name:        "Inclusão de CSS e JS",
			Description: "CSS e JS devem ser incluídos em arquivos separados",
			Validator:   validateExternalResources,
		},
		{
			Name:        "Cores do Design System Shell",
			Description: "Deve usar as cores do Design System Shell",
			Validator:   validateShellColors,
		},
		{
			Name:        "Fontes do Design System Shell",
			Description: "Deve usar as fontes Rajdhani e Share Tech Mono",
			Validator:   validateShellFonts,
		},
	}

	// Executa a validação
	results := validateTemplates(templatesDir, rules)

	// Exibe os resultados
	displayResults(convertResults(results))
}

func convertResults(results []ValidationResult) []TemplateStructureValidationResult {
	converted := make([]TemplateStructureValidationResult, len(results))
	for i, r := range results {
		converted[i] = TemplateStructureValidationResult{
			FilePath:        r.FilePath,
			OriginalContent: r.OriginalContent,
			UpdatedContent:  r.UpdatedContent,
			HasChanges:      r.HasChanges,
			Errors:          r.Errors,
		}
	}
	return converted
}

// Valida todos os templates no diretório
func validateTemplates(dir string, rules []TemplateRule) []ValidationResult {
	var results []ValidationResult

	// Percorre todos os arquivos HTML no diretório
	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Ignora diretórios e arquivos que não são HTML
		if info.IsDir() || filepath.Ext(path) != ".html" {
			return nil
		}

		// Lê o conteúdo do arquivo
		content, err := os.ReadFile(path)
		if err != nil {
			fmt.Printf("Erro ao ler arquivo %s: %v\n", path, err)
			return nil
		}

		// Cria um resultado de validação para o arquivo
		result := ValidationResult{
			FilePath:        path,
			OriginalContent: string(content),
			UpdatedContent:  string(content),
			HasChanges:      false,
			Errors:          []string{},
		}

		// Aplica cada regra ao arquivo
		for _, rule := range rules {
			valid, message := rule.Validator(string(content), path)
			if !valid {
				result.Errors = append(result.Errors, fmt.Sprintf("%s: %s", rule.Name, message))
			}
		}

		// Adiciona o resultado à lista
		results = append(results, result)
		return nil
	})

	if err != nil {
		fmt.Printf("Erro ao percorrer diretório: %v\n", err)
	}

	return results
}

func displayResults(results []TemplateStructureValidationResult) {
	totalFiles := len(results)
	filesWithErrors := 0
	totalErrors := 0

	fmt.Println("\n=== RELATÓRIO DE VALIDAÇÃO DE TEMPLATES ===")
	fmt.Printf("Total de arquivos analisados: %d\n\n", totalFiles)

	// Exibe os resultados para cada arquivo
	for _, result := range results {
		if len(result.Errors) > 0 {
			filesWithErrors++
			totalErrors += len(result.Errors)

			relPath, _ := filepath.Rel(".", result.FilePath)
			fmt.Printf("📄 %s\n", relPath)

			for _, err := range result.Errors {
				fmt.Printf("  ❌ %s\n", err)
			}

			// Removed warnings loop because Warnings field does not exist
			// for _, warn := range result.Warnings {
			// 	fmt.Printf("  ⚠️ %s\n", warn)
			// }

			fmt.Println()
		}
	}

	// Exibe o resumo
	fmt.Println("=== RESUMO ===")
	fmt.Printf("Total de arquivos com problemas: %d/%d (%.1f%%)\n",
		filesWithErrors, totalFiles, float64(filesWithErrors)/float64(totalFiles)*100)
	fmt.Printf("Total de erros encontrados: %d\n", totalErrors)

	// Exibe arquivos sem erros
	if filesWithErrors < totalFiles {
		fmt.Println("\nArquivos sem problemas:")
		for _, result := range results {
			if len(result.Errors) == 0 {
				relPath, _ := filepath.Rel(".", result.FilePath)
				fmt.Printf("  ✅ %s\n", relPath)
			}
		}
	}
}

// === VALIDADORES ===

// Verifica se o template tem apenas uma definição
func validateSingleDefinition(content string, path string) (bool, string) {
	defineRegex := regexp.MustCompile(`{{\s*define\s+"([^"]+)"\s*}}`)
	matches := defineRegex.FindAllStringSubmatch(content, -1)

	if len(matches) == 0 {
		return false, "Template não tem definição"
	}

	if len(matches) > 1 {
		// Verifica se são definições do mesmo template
		firstTemplate := matches[0][1]
		for _, match := range matches[1:] {
			if match[1] == firstTemplate {
				return false, fmt.Sprintf("Template tem múltiplas definições para '%s'", firstTemplate)
			}
		}
	}

	return true, ""
}

// Verifica se o nome do template corresponde ao caminho relativo
func validateTemplateName(content string, path string) (bool, string) {
	defineRegex := regexp.MustCompile(`{{\s*define\s+"([^"]+)"\s*}}`)
	matches := defineRegex.FindAllStringSubmatch(content, -1)

	if len(matches) == 0 {
		return false, "Template não tem definição"
	}

	// Obtém o caminho relativo
	relPath, _ := filepath.Rel("web/templates", path)
	relPath = strings.ReplaceAll(relPath, "\\", "/") // Normaliza para usar / em todos os sistemas

	// Verifica se o nome do template corresponde ao caminho relativo
	templateName := matches[0][1]
	if templateName != relPath && !strings.HasSuffix(relPath, templateName) {
		return false, fmt.Sprintf("Nome do template '%s' não corresponde ao caminho '%s'", templateName, relPath)
	}

	return true, ""
}

// Verifica se o template tem DOCTYPE e meta viewport
func validateDocTypeAndViewport(content string, path string) (bool, string) {
	// Ignora templates parciais (como sidebar.html)
	if strings.Contains(path, "layouts") && !strings.Contains(path, "base_layout.html") {
		return true, ""
	}

	// Verifica se tem DOCTYPE
	doctypeRegex := regexp.MustCompile(`<!DOCTYPE\s+html>`)
	if !doctypeRegex.MatchString(content) {
		return false, "Template não tem DOCTYPE HTML"
	}

	// Verifica se tem meta viewport
	viewportRegex := regexp.MustCompile(`<meta\s+name=["']viewport["']\s+content=["'][^"']*["']>`)
	if !viewportRegex.MatchString(content) {
		return false, "Template não tem meta viewport"
	}

	return true, ""
}

// Verifica se o template segue a estrutura de layout
func validateLayoutStructure(content string, path string) (bool, string) {
	// Ignora templates parciais e layouts
	if strings.Contains(path, "layouts") || strings.Contains(path, "login") {
		return true, ""
	}

	// Verifica se inclui o sidebar
	sidebarRegex := regexp.MustCompile(`{{\s*template\s+"(sidebar|layouts/sidebar.html)"\s+\.\s*}}`)
	if !sidebarRegex.MatchString(content) {
		return false, "Template não inclui o sidebar com {{ template \"sidebar\" . }}"
	}

	// Verifica se tem a estrutura de conteúdo
	contentStructureRegex := regexp.MustCompile(`<div\s+class=["']content-with-sidebar["']>`)
	if !contentStructureRegex.MatchString(content) {
		return false, "Template não tem a div com classe 'content-with-sidebar'"
	}

	mainContentRegex := regexp.MustCompile(`<div\s+class=["']main-content["']>`)
	if !mainContentRegex.MatchString(content) {
		return false, "Template não tem a div com classe 'main-content'"
	}

	return true, ""
}

// Verifica se CSS e JS estão em arquivos separados
func validateExternalResources(content string, path string) (bool, string) {
	// Verifica se tem estilos inline
	styleRegex := regexp.MustCompile(`<style>[\s\S]*?</style>`)
	styleMatches := styleRegex.FindAllString(content, -1)

	// Permite pequenos blocos de estilo (menos de 10 linhas)
	for _, match := range styleMatches {
		lines := strings.Count(match, "\n")
		if lines > 10 {
			return false, "Template tem blocos de estilo inline muito grandes"
		}
	}

	// Verifica se tem scripts inline
	scriptRegex := regexp.MustCompile(`<script>[\s\S]*?</script>`)
	scriptMatches := scriptRegex.FindAllString(content, -1)

	// Permite pequenos blocos de script (menos de 10 linhas)
	for _, match := range scriptMatches {
		lines := strings.Count(match, "\n")
		if lines > 10 {
			return false, "Template tem blocos de script inline muito grandes"
		}
	}

	return true, ""
}

// Verifica se o template usa as cores do Design System Shell
func validateShellColors(content string, path string) (bool, string) {
	// Ignora templates parciais e layouts
	if strings.Contains(path, "layouts") || strings.Contains(path, "login") {
		return true, ""
	}

	// Verifica se usa as cores do Shell
	shellRedRegex := regexp.MustCompile(`(--shell-red|#ED1C24)`)
	shellYellowRegex := regexp.MustCompile(`(--shell-yellow|#FDB813)`)
	shellDarkRegex := regexp.MustCompile(`(--shell-dark|#333333)`)

	if !shellRedRegex.MatchString(content) && !shellYellowRegex.MatchString(content) && !shellDarkRegex.MatchString(content) {
		return false, "Template não usa as cores do Design System Shell"
	}

	return true, ""
}

// Verifica se o template usa as fontes do Design System Shell
func validateShellFonts(content string, path string) (bool, string) {
	// Ignora templates parciais e layouts
	if strings.Contains(path, "layouts") || strings.Contains(path, "login") {
		return true, ""
	}

	// Verifica se usa as fontes do Shell
	rajdhaniRegex := regexp.MustCompile(`Rajdhani`)
	shareTechMonoRegex := regexp.MustCompile(`Share\s*Tech\s*Mono`)

	if !rajdhaniRegex.MatchString(content) && !shareTechMonoRegex.MatchString(content) {
		return false, "Template não usa as fontes do Design System Shell (Rajdhani e Share Tech Mono)"
	}

	return true, ""
}
