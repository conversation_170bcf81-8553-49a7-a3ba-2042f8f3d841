package main

import (
	"fmt"
	"os"
	"path/filepath"
)

func runFixTemplates() {
	// Diretório de templates
	templatesDir := "web/templates"

	// Verificar se dashboard.html existe na raiz
	dashboardPath := filepath.Join(templatesDir, "dashboard.html")
	if _, err := os.Stat(dashboardPath); os.IsNotExist(err) {
		// Se não existe, copiar de dashboard/dashboard.html
		dashboardDirPath := filepath.Join(templatesDir, "dashboard", "dashboard.html")
		if _, err := os.Stat(dashboardDirPath); err == nil {
			// Ler o conteúdo do arquivo original
			content, err := os.ReadFile(dashboardDirPath)
			if err != nil {
				fmt.Printf("Erro ao ler o arquivo %s: %v\n", dashboardDirPath, err)
				return
			}

			// Escrever o conteúdo no novo arquivo
			err = os.WriteFile(dashboardPath, content, 0644)
			if err != nil {
				fmt.Printf("Erro ao criar o arquivo %s: %v\n", dashboardPath, err)
				return
			}

			fmt.Printf("Template copiado com sucesso: %s -> %s\n", dashboardDirPath, dashboardPath)
		} else {
			fmt.Printf("Arquivo não encontrado: %s\n", dashboardDirPath)
		}
	} else {
		fmt.Printf("O arquivo já existe: %s\n", dashboardPath)
	}

	// Atualizar referências no código
	updateTemplateReferences()
}

func updateTemplateReferences() {
	// Lista de diretórios para buscar arquivos Go
	dirsToSearch := []string{
		"cmd",
		"internal",
		"handlers",
	}

	for _, dir := range dirsToSearch {
		err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}

			// Ignorar diretórios
			if info.IsDir() {
				return nil
			}

			// Processar apenas arquivos Go
			if filepath.Ext(path) != ".go" {
				return nil
			}

			// Ler o conteúdo do arquivo
			content, err := os.ReadFile(path)
			if err != nil {
				return err
			}

			// Converter para string
			contentStr := string(content)

			// Verificar se contém "dashboard/dashboard.html"
			hasReference := false
			// Substituir "dashboard/dashboard.html" por "dashboard.html"
			updatedContent := contentStr
			if contentStr != updatedContent {
				hasReference = true
			}

			// Se houve alteração, salvar o arquivo
			if hasReference {
				err = os.WriteFile(path, []byte(updatedContent), info.Mode())
				if err != nil {
					return err
				}
				fmt.Printf("Atualizado arquivo: %s\n", path)
			}

			return nil
		})

		if err != nil {
			fmt.Printf("Erro ao processar diretório %s: %v\n", dir, err)
		}
	}
}
