const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// Diretórios
const inputDir = path.join(__dirname, '../raw-images');
const outputDir = path.join(__dirname, '../static/images');

// Garantir que os diretórios existam
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Configurações de otimização
const configs = [
  { suffix: '-lg', width: 1920, quality: 85 },
  { suffix: '-md', width: 1280, quality: 80 },
  { suffix: '-sm', width: 768, quality: 75 },
];

// Processar imagens
async function processImages() {
  const files = fs.readdirSync(inputDir);
  
  for (const file of files) {
    if (file.match(/\.(jpg|jpeg|png)$/i)) {
      const inputPath = path.join(inputDir, file);
      const filename = path.parse(file).name;
      
      console.log(`Processando: ${file}`);
      
      // Criar versões em diferentes tamanhos
      for (const config of configs) {
        const outputPath = path.join(outputDir, `${filename}${config.suffix}.webp`);
        
        await sharp(inputPath)
          .resize({ width: config.width, withoutEnlargement: true })
          .webp({ quality: config.quality })
          .toFile(outputPath);
          
        console.log(`Criado: ${outputPath}`);
      }
      
      // Criar thumbnail
      const thumbPath = path.join(outputDir, `${filename}-thumb.webp`);
      await sharp(inputPath)
        .resize({ width: 300, height: 300, fit: 'cover' })
        .webp({ quality: 70 })
        .toFile(thumbPath);
        
      console.log(`Thumbnail criado: ${thumbPath}`);
    }
  }
}

// Executar o processamento
processImages()
  .then(() => console.log('Processamento de imagens concluído!'))
  .catch(err => console.error('Erro:', err));