# Ferramentas de Validação e Correção do Projeto TenhoApp

Este diretório contém ferramentas para verificar e corrigir problemas comuns no código do projeto.

## Ferramentas Disponíveis

1. **template_validator.go**: Verifica e corrige problemas com templates, incluindo:
   - Carregamentos redundantes de templates
   - Referências incorretas a templates
   - Rotas de templates inconsistentes

2. **code_validator.go**: Verifica e corrige problemas gerais de código, incluindo:
   - Problemas em arquivos Go
   - Problemas em arquivos HTML
   - Problemas em arquivos JavaScript
   - Problemas em arquivos JSON

3. **Scripts de execução**:
   - `fix_project.sh`: Para sistemas Unix/Linux/macOS
   - `fix_project.bat`: Para sistemas Windows

## Como Usar

### Método 1: Usando os scripts de execução

Este é o método mais simples, que compila e executa automaticamente as ferramentas.

#### No Windows:
```
.\tools\fix_project.bat
```

#### No Linux/macOS:
```
chmod +x ./tools/fix_project.sh
./tools/fix_project.sh
```

### Método 2: Compilar e executar manualmente

Se preferir, você pode compilar e executar as ferramentas individualmente:

```
# Compilar as ferramentas
go build -o template_validator ./tools/template_validator.go
go build -o code_validator ./tools/code_validator.go

# Executar as ferramentas
./template_validator
./code_validator
```

## Após a Correção

Depois de executar as ferramentas, é importante:

1. **Compilar o projeto** para verificar se não há erros:
   ```
   go build ./...
   ```

2. **Iniciar o servidor** e testar manualmente:
   ```
   go run cmd/main.go
   ```

3. **Verificar os logs** em busca de erros ou mensagens de aviso.

## Como as Ferramentas Funcionam

Estas ferramentas percorrem os arquivos do projeto e aplicam correções para problemas conhecidos:

- **Carregamentos redundantes de templates**: Quando há chamadas múltiplas para `LoadHTMLGlob` com padrões que se sobrepõem.
- **Referências incorretas a templates**: Quando um template é referenciado por um caminho incorreto.
- **Problemas de HTML**: Tags não fechadas, DOCTYPE faltando, etc.
- **Problemas de JSON**: Problemas de sintaxe, vírgulas indevidas, etc.
- **Problemas de JavaScript**: Falta de tratamento de erros, problemas com JSON.parse, etc.

## Importante

- Estas ferramentas fazem modificações diretas nos arquivos. É recomendável fazer um backup do projeto antes de executá-las.
- Nem todos os problemas podem ser corrigidos automaticamente. Alguns problemas são apenas identificados.
- Após as correções, é essencial testar o aplicativo completamente para garantir que tudo funcione conforme esperado. 