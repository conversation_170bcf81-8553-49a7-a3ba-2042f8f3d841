package main

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"text/template"
)

// TemplateRenderer renderiza templates para gerar código
type TemplateRenderer struct {
	TemplateDir string
}

// NewTemplateRenderer cria um novo TemplateRenderer
func NewTemplateRenderer(templateDir string) *TemplateRenderer {
	return &TemplateRenderer{
		TemplateDir: templateDir,
	}
}

// RenderTemplate renderiza um template com os dados fornecidos
func (r *TemplateRenderer) RenderTemplate(templateName string, data interface{}) (string, error) {
	// Ler o arquivo de template
	templatePath := filepath.Join(r.TemplateDir, templateName)
	templateContent, err := ioutil.ReadFile(templatePath)
	if err != nil {
		return "", fmt.Errorf("erro ao ler arquivo de template: %v", err)
	}

	// Criar o template
	tmpl, err := template.New(templateName).Parse(string(templateContent))
	if err != nil {
		return "", fmt.Errorf("erro ao analisar template: %v", err)
	}

	// Renderizar o template
	var result string
	err = tmpl.Execute(os.Stdout, data)
	if err != nil {
		return "", fmt.Errorf("erro ao renderizar template: %v", err)
	}

	return result, nil
}

// RenderTemplateToFile renderiza um template com os dados fornecidos e salva em um arquivo
func (r *TemplateRenderer) RenderTemplateToFile(templateName string, data interface{}, outputFile string) error {
	// Ler o arquivo de template
	templatePath := filepath.Join(r.TemplateDir, templateName)
	templateContent, err := ioutil.ReadFile(templatePath)
	if err != nil {
		return fmt.Errorf("erro ao ler arquivo de template: %v", err)
	}

	// Criar o template
	tmpl, err := template.New(templateName).Parse(string(templateContent))
	if err != nil {
		return fmt.Errorf("erro ao analisar template: %v", err)
	}

	// Criar diretório de saída se não existir
	outputDir := filepath.Dir(outputFile)
	err = os.MkdirAll(outputDir, 0755)
	if err != nil {
		return fmt.Errorf("erro ao criar diretório de saída: %v", err)
	}

	// Criar arquivo de saída
	file, err := os.Create(outputFile)
	if err != nil {
		return fmt.Errorf("erro ao criar arquivo de saída: %v", err)
	}
	defer file.Close()

	// Renderizar o template para o arquivo
	err = tmpl.Execute(file, data)
	if err != nil {
		return fmt.Errorf("erro ao renderizar template: %v", err)
	}

	return nil
}

// Função principal para o template renderer
func runTemplateRendererMain() {
	if len(os.Args) < 4 {
		fmt.Println("Uso: go run template_renderer.go <diretorio_templates> <nome_template> <arquivo_saida>")
		os.Exit(1)
	}

	templateDir := os.Args[1]
	templateName := os.Args[2]
	outputFile := os.Args[3]

	renderer := NewTemplateRenderer(templateDir)

	// Exemplo de dados
	data := struct {
		Package string
		Name    string
		Fields  []struct {
			Name     string
			Type     string
			JSONName string
		}
	}{
		Package: "models",
		Name:    "User",
		Fields: []struct {
			Name     string
			Type     string
			JSONName string
		}{
			{Name: "Name", Type: "string", JSONName: "name"},
			{Name: "Email", Type: "string", JSONName: "email"},
			{Name: "Password", Type: "string", JSONName: "password"},
			{Name: "Role", Type: "string", JSONName: "role"},
		},
	}

	err := renderer.RenderTemplateToFile(templateName, data, outputFile)
	if err != nil {
		fmt.Printf("Erro ao renderizar template: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("Template renderizado com sucesso em %s\n", outputFile)
}
