package main

import (
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
)

func runFixTemplateDuplicatesImpl() {
	fmt.Println("Corrigindo templates com definições duplicadas...")

	// Diretório de templates
	templatesDir := "web/templates"

	// Lista de arquivos com problemas conhecidos
	problemFiles := []string{
		"web/templates/novapagina/novapagina.html",
		"web/templates/ordens/orders.html",
		"web/templates/ordens/order_detail.html",
	}

	// Corrige cada arquivo
	for _, file := range problemFiles {
		fmt.Printf("Processando arquivo: %s\n", file)
		if err := fixTemplateDuplicatesExtended(file); err != nil {
			fmt.Printf("Erro ao corrigir arquivo %s: %v\n", file, err)
		} else {
			fmt.Printf("Arquivo corrigido com sucesso: %s\n", file)
		}
	}

	// Procura por outros arquivos com problemas
	fmt.Println("\nProcurando outros arquivos com definições duplicadas...")

	err := filepath.Walk(templatesDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Ignora diretórios e arquivos que não são HTML
		if info.IsDir() || filepath.Ext(path) != ".html" {
			return nil
		}

		// Ignora arquivos já corrigidos
		for _, file := range problemFiles {
			if path == file {
				return nil
			}
		}

		// Verifica se o arquivo tem definições duplicadas
		hasDuplicates, err := checkForDuplicateDefinitions(path)
		if err != nil {
			fmt.Printf("Erro ao verificar arquivo %s: %v\n", path, err)
			return nil
		}

		if hasDuplicates {
			fmt.Printf("Encontrado arquivo com definições duplicadas: %s\n", path)
			if err := fixTemplateDuplicatesExtended(path); err != nil {
				fmt.Printf("Erro ao corrigir arquivo %s: %v\n", path, err)
			} else {
				fmt.Printf("Arquivo corrigido com sucesso: %s\n", path)
			}
		}

		return nil
	})

	if err != nil {
		fmt.Printf("Erro ao percorrer diretório: %v\n", err)
	}

	fmt.Println("\nProcesso de correção concluído!")
}

// Verifica se um arquivo tem definições duplicadas
func checkForDuplicateDefinitions(path string) (bool, error) {
	// Lê o conteúdo do arquivo
	content, err := os.ReadFile(path)
	if err != nil {
		return false, fmt.Errorf("erro ao ler arquivo: %v", err)
	}

	// Expressão regular para encontrar definições de templates
	defineRegex := regexp.MustCompile(`{{\s*define\s+"([^"]+)"\s*}}`)
	matches := defineRegex.FindAllStringSubmatch(string(content), -1)

	// Verifica se há definições duplicadas
	if len(matches) > 1 {
		templateNames := make(map[string]int)
		for _, match := range matches {
			templateName := match[1]
			templateNames[templateName]++
		}

		// Verifica se algum nome de template aparece mais de uma vez
		for name, count := range templateNames {
			if count > 1 {
				return true, nil
			}
		}
	}

	return false, nil
}

// Corrige definições duplicadas em um arquivo
func fixTemplateDuplicatesExtended(path string) error {
	// Lê o conteúdo do arquivo
	content, err := os.ReadFile(path)
	if err != nil {
		return fmt.Errorf("erro ao ler arquivo: %v", err)
	}

	contentStr := string(content)

	// Expressão regular para encontrar definições de templates
	defineRegex := regexp.MustCompile(`{{\s*define\s+"([^"]+)"\s*}}`)
	matches := defineRegex.FindAllStringSubmatch(contentStr, -1)

	if len(matches) <= 1 {
		return nil // Não há definições duplicadas
	}

	// Obtém o nome do template
	templateName := matches[0][1]

	// Verifica se há definições duplicadas
	hasDuplicates := false
	for _, match := range matches[1:] {
		if match[1] == templateName {
			hasDuplicates = true
			break
		}
	}

	if !hasDuplicates {
		return nil // Não há definições duplicadas do mesmo template
	}

	// Extrai o conteúdo entre as definições
	sections := make([]string, 0)

	// Divide o conteúdo em seções
	parts := strings.Split(contentStr, "{{ define")

	// A primeira parte é o que vem antes da primeira definição
	header := parts[0]

	// As outras partes são as definições
	for i, part := range parts[1:] {
		if i == 0 {
			// Primeira definição
			sections = append(sections, "{{ define"+part)
		} else {
			// Encontra o final da definição anterior
			endIndex := strings.LastIndex(sections[len(sections)-1], "{{ end }}")
			if endIndex != -1 {
				// Remove o final da definição anterior
				sections[len(sections)-1] = sections[len(sections)-1][:endIndex]
			}

			// Adiciona a nova definição
			sections = append(sections, "{{ define"+part)
		}
	}

	// Cria um novo conteúdo com uma única definição
	newContent := header + "{{ define \"" + templateName + "\" }}\n"

	// Adiciona o conteúdo de cada seção
	for _, section := range sections {
		// Remove a definição e o final
		section = regexp.MustCompile(`{{\s*define\s+"[^"]+"\s*}}`).ReplaceAllString(section, "")
		section = regexp.MustCompile(`{{\s*end\s*}}`).ReplaceAllString(section, "")

		// Adiciona o conteúdo
		newContent += section
	}

	// Adiciona o final
	newContent += "{{ end }}"

	// Escreve o novo conteúdo no arquivo
	err = os.WriteFile(path, []byte(newContent), 0644)
	if err != nil {
		return fmt.Errorf("erro ao escrever arquivo: %v", err)
	}

	return nil
}
