package main

import (
	"fmt"
	"os"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Uso: go run tools/main.go <comando>")
		fmt.Println("Comandos disponíveis:")
		fmt.Println("  fix-duplicates        - Corrige definições duplicadas em templates")
		fmt.Println("  incremental-render    - Executa o incremental renderer")
		fmt.Println("  code-split           - Executa o code splitter")
		fmt.Println("  template-render      - Executa o template renderer")
		fmt.Println("  template-validate    - Executa o template validator")
		os.Exit(1)
	}

	switch os.Args[1] {
	case "fix-duplicates":
		runFixTemplateDuplicates()
	case "incremental-render":
		runIncrementalRenderer()
	case "code-split":
		runCodeSplitter()
	case "template-render":
		runTemplateRenderer()
	case "template-validate":
		runTemplateValidator()
	default:
		fmt.Printf("Comando desconhecido: %s\n", os.Args[1])
		os.Exit(1)
	}
}

// Funções stub para evitar erro de compilação, serão implementadas no exports.go
func runFixTemplateDuplicates() {
	runFixTemplateDuplicatesImpl()
}

func runIncrementalRenderer() {
	runIncrementalRendererMain()
}

func runCodeSplitter() {
	runCodeSplitterMain()
}

func runTemplateRenderer() {
	runTemplateRendererMain()
}

func runTemplateValidator() {
	fmt.Println("Função runTemplateValidator não implementada")
}
