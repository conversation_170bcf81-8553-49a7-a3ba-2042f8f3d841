#!/bin/bash

# Script para executar a migração do banco de dados
# Uso: ./run_migration.sh [dry_run]
# Se dry_run for especificado, a migração será executada em modo de simulação

# Configurações
BACKUP_DIR="backups"
LOG_DIR="logs"
MIGRATION_DIR="migrations"
DRY_RUN=false

# Funções auxiliares
check_requirements() {
    echo "Verificando requisitos do sistema..."
    
    # Verificar espaço em disco
    DISK_SPACE=$(df -h . | awk 'NR==2 {print $4}')
    if [[ ${DISK_SPACE%G} -lt 10 ]]; then
        echo "ERRO: Espaço em disco insuficiente. Necessário pelo menos 10GB livre."
        exit 1
    fi
    
    # Verificar memória
    MEMORY=$(free -g | awk 'NR==2 {print $4}')
    if [[ $MEMORY -lt 4 ]]; then
        echo "ERRO: Memória RAM insuficiente. Necessário pelo menos 4GB livre."
        exit 1
    fi
    
    # Verificar dependências
    command -v psql >/dev/null 2>&1 || {
        echo "ERRO: PostgreSQL não está instalado."
        exit 1
    }
    
    command -v go >/dev/null 2>&1 || {
        echo "ERRO: Go não está instalado."
        exit 1
    }
}

create_backup() {
    echo "Criando backup do banco de dados..."
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_FILE="$BACKUP_DIR/backup_$TIMESTAMP.sql"
    
    mkdir -p "$BACKUP_DIR"
    PGPASSWORD=$DB_PASS pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" > "$BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        echo "Backup criado com sucesso: $BACKUP_FILE"
    else
        echo "ERRO: Falha ao criar backup."
        exit 1
    fi
}

setup_environment() {
    echo "Configurando ambiente..."
    
    # Criar diretórios necessários
    mkdir -p "$LOG_DIR"
    mkdir -p "$MIGRATION_DIR"
    
    # Carregar variáveis de ambiente
    if [ -f .env ]; then
        source .env
    else
        echo "ERRO: Arquivo .env não encontrado."
        exit 1
    fi
    
    # Verificar se todas as variáveis necessárias estão definidas
    required_vars=("DB_HOST" "DB_PORT" "DB_USER" "DB_PASS" "DB_NAME")
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            echo "ERRO: Variável $var não está definida no arquivo .env"
            exit 1
        fi
    done
}

run_migration() {
    echo "Iniciando migração..."
    
    # Configurar modo dry run se especificado
    if [ "$1" == "dry_run" ]; then
        DRY_RUN=true
        echo "Executando em modo DRY RUN - nenhuma alteração será feita no banco de dados."
    fi
    
    # Exportar variáveis de ambiente para o Go
    export DB_HOST DB_PORT DB_USER DB_PASS DB_NAME
    
    # Executar migração
    if [ "$DRY_RUN" = true ]; then
        go run migrate_data.go --dry-run
    else
        go run migrate_data.go
    fi
    
    if [ $? -eq 0 ]; then
        echo "Migração concluída com sucesso."
    else
        echo "ERRO: Falha na migração."
        exit 1
    fi
}

# Main
echo "Iniciando processo de migração..."
echo "Data/Hora: $(date)"

# Verificar argumentos
if [ "$1" == "dry_run" ]; then
    echo "Modo DRY RUN ativado."
fi

# Executar verificações e preparação
check_requirements
setup_environment
create_backup

# Executar migração
run_migration "$1"

echo "Processo de migração concluído."
echo "Data/Hora: $(date)"
