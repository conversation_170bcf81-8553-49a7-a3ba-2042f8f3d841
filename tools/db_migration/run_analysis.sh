#!/bin/bash

# Script para executar a análise do banco de dados
# Uso: ./run_analysis.sh

# Configurações do banco de dados
export DB_HOST="localhost"
export DB_PORT="5432"
export DB_USER="postgres"
export DB_PASS="postgres"
export DB_NAME="tradicao"

# Compilar o script de análise
echo "Compilando script de análise..."
cd "$(dirname "$0")"
go build -o analyze_db analyze_db.go
if [ $? -ne 0 ]; then
    echo "Erro ao compilar script de análise"
    exit 1
fi

# Executar a análise
echo "Executando análise do banco de dados..."
./analyze_db
if [ $? -ne 0 ]; then
    echo "Erro durante a análise"
    exit 1
fi

echo "Análise concluída com sucesso"
echo "Relatório gerado em database_analysis_report.md"
exit 0
