#!/bin/bash

# Script para gerar o esquema Atlas
# Uso: ./run_generate_atlas.sh

# Compilar o script de geração de esquema Atlas
echo "Compilando script de geração de esquema Atlas..."
cd "$(dirname "$0")"
go build -o generate_atlas_schema generate_atlas_schema.go
if [ $? -ne 0 ]; then
    echo "Erro ao compilar script de geração de esquema Atlas"
    exit 1
fi

# Executar a geração de esquema Atlas
echo "Executando geração de esquema Atlas..."
./generate_atlas_schema
if [ $? -ne 0 ]; then
    echo "Erro durante a geração de esquema Atlas"
    exit 1
fi

echo "Geração de esquema Atlas concluída com sucesso"
echo "Arquivos gerados:"
echo "- atlas.hcl"
echo "- migrations/atlas/schema.hcl"
echo "- migrations/atlas/001_initial_schema.sql"
exit 0
