package main

import (
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
)

// Definição das tabelas do novo esquema
var tables = []struct {
	Name    string
	SQL     string
	Comment string
}{
	{
		Name: "users",
		SQL: `CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    name VA<PERSON>HAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    branch_id INTEGER REFERENCES branches(id),
    failed_attempts INTEGER DEFAULT 0,
    blocked BOOLEAN DEFAULT FALSE,
    totp_secret VARCHAR(255) DEFAULT '',
    totp_enabled BOOLEAN DEFAULT FALSE,
    last_password_change TIMESTAMP,
    force_password_change BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);`,
		Comment: "Tabela de usuários do sistema",
	},
	{
		Name: "branches",
		SQL: `CREATE TABLE branches (
    id SERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    code VARCHAR(20) NOT NULL UNIQUE,
    address VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(2),
    zip_code VARCHAR(20),
    phone VARCHAR(20),
    email VARCHAR(100),
    type VARCHAR(20) DEFAULT 'urban',
    is_active BOOLEAN DEFAULT TRUE,
    manager_id INTEGER REFERENCES users(id),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    opening_hours VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);`,
		Comment: "Tabela de filiais/postos",
	},
	{
		Name: "equipment",
		SQL: `CREATE TABLE equipment (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    serial_number VARCHAR(100),
    model VARCHAR(100),
    brand VARCHAR(100),
    type VARCHAR(50) NOT NULL,
    installation_date TIMESTAMP,
    last_maintenance TIMESTAMP,
    last_preventive TIMESTAMP,
    next_preventive TIMESTAMP,
    status VARCHAR(50) DEFAULT 'active',
    location VARCHAR(100),
    branch_id INTEGER NOT NULL REFERENCES branches(id),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);`,
		Comment: "Tabela de equipamentos",
	},
	{
		Name: "service_providers",
		SQL: `CREATE TABLE service_providers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    cnpj VARCHAR(20) NOT NULL,
    specialties VARCHAR(255),
    area_of_expertise VARCHAR(100),
    contact VARCHAR(100),
    average_rating DECIMAL(3, 2),
    status VARCHAR(20) DEFAULT 'active',
    user_id INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);`,
		Comment: "Tabela de prestadores de serviço",
	},
	{
		Name: "maintenance_orders",
		SQL: `CREATE TABLE maintenance_orders (
    id SERIAL PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    priority VARCHAR(20) DEFAULT 'medium',
    branch_id INTEGER NOT NULL REFERENCES branches(id),
    equipment_id INTEGER NOT NULL REFERENCES equipment(id),
    requested_by INTEGER NOT NULL REFERENCES users(id),
    created_by_user_id INTEGER NOT NULL REFERENCES users(id),
    assigned_provider_id INTEGER REFERENCES service_providers(id),
    approved_by_user_id INTEGER REFERENCES users(id),
    scheduled_date TIMESTAMP,
    completion_date TIMESTAMP,
    estimated_cost DECIMAL(10, 2),
    actual_cost DECIMAL(10, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);`,
		Comment: "Tabela de ordens de manutenção",
	},
	{
		Name: "tag_categories",
		SQL: `CREATE TABLE tag_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);`,
		Comment: "Tabela de categorias de tags",
	},
	{
		Name: "tags",
		SQL: `CREATE TABLE tags (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    category_id INTEGER REFERENCES tag_categories(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);`,
		Comment: "Tabela de tags",
	},
	{
		Name: "equipment_tags",
		SQL: `CREATE TABLE equipment_tags (
    equipment_id INTEGER NOT NULL REFERENCES equipment(id),
    tag_id INTEGER NOT NULL REFERENCES tags(id),
    PRIMARY KEY (equipment_id, tag_id)
);`,
		Comment: "Tabela de relacionamento entre equipamentos e tags",
	},
	{
		Name: "materials",
		SQL: `CREATE TABLE materials (
    id SERIAL PRIMARY KEY,
    maintenance_order_id INTEGER NOT NULL REFERENCES maintenance_orders(id),
    name VARCHAR(100) NOT NULL,
    quantity INTEGER NOT NULL,
    unit_cost DECIMAL(10, 2) NOT NULL,
    cost DECIMAL(10, 2) NOT NULL,
    unit VARCHAR(20),
    added_by_user_id INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);`,
		Comment: "Tabela de materiais usados em ordens de manutenção",
	},
	{
		Name: "maintenance_order_photos",
		SQL: `CREATE TABLE maintenance_order_photos (
    id SERIAL PRIMARY KEY,
    order_id INTEGER NOT NULL REFERENCES maintenance_orders(id),
    file_path VARCHAR(255) NOT NULL,
    file_name VARCHAR(100) NOT NULL,
    file_size INTEGER NOT NULL,
    content_type VARCHAR(50) NOT NULL,
    caption VARCHAR(255),
    uploaded_by INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);`,
		Comment: "Tabela de fotos de ordens de manutenção",
	},
	{
		Name: "security_policies",
		SQL: `CREATE TABLE security_policies (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    password_min_length INTEGER DEFAULT 8,
    password_require_uppercase BOOLEAN DEFAULT TRUE,
    password_require_number BOOLEAN DEFAULT TRUE,
    password_require_special_char BOOLEAN DEFAULT TRUE,
    password_expiry_days INTEGER DEFAULT 90,
    max_login_attempts INTEGER DEFAULT 5,
    lockout_duration_minutes INTEGER DEFAULT 30,
    enable_2fa BOOLEAN DEFAULT FALSE,
    session_timeout_minutes INTEGER DEFAULT 60,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);`,
		Comment: "Tabela de políticas de segurança",
	},
	{
		Name: "audit_logs",
		SQL: `CREATE TABLE audit_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100),
    details TEXT,
    ip VARCHAR(50),
    user_agent VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);`,
		Comment: "Tabela de logs de auditoria",
	},
	{
		Name: "calendar_events",
		SQL: `CREATE TABLE calendar_events (
    id SERIAL PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP,
    user_id INTEGER REFERENCES users(id),
    maintenance_order_id INTEGER REFERENCES maintenance_orders(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);`,
		Comment: "Tabela de eventos do calendário",
	},
}

// Definição dos índices
var indexes = []struct {
	TableName string
	Name      string
	Columns   string
	Unique    bool
}{
	{"users", "idx_users_email", "email", true},
	{"users", "idx_users_role", "role", false},
	{"users", "idx_users_branch_id", "branch_id", false},

	{"branches", "idx_branches_manager_id", "manager_id", false},
	{"branches", "idx_branches_is_active", "is_active", false},
	{"branches", "idx_branches_city_state", "city, state", false},

	{"equipment", "idx_equipment_branch_id", "branch_id", false},
	{"equipment", "idx_equipment_type", "type", false},
	{"equipment", "idx_equipment_status", "status", false},
	{"equipment", "idx_equipment_next_preventive", "next_preventive", false},

	{"maintenance_orders", "idx_maintenance_orders_branch_id", "branch_id", false},
	{"maintenance_orders", "idx_maintenance_orders_equipment_id", "equipment_id", false},
	{"maintenance_orders", "idx_maintenance_orders_status", "status", false},
	{"maintenance_orders", "idx_maintenance_orders_priority", "priority", false},
	{"maintenance_orders", "idx_maintenance_orders_scheduled_date", "scheduled_date", false},

	{"service_providers", "idx_service_providers_status", "status", false},
	{"tags", "idx_tags_category_id", "category_id", false},
	{"materials", "idx_materials_maintenance_order_id", "maintenance_order_id", false},
	{"maintenance_order_photos", "idx_maintenance_order_photos_order_id", "order_id", false},
	{"audit_logs", "idx_audit_logs_user_id", "user_id", false},
	{"calendar_events", "idx_calendar_events_user_id", "user_id", false},
	{"calendar_events", "idx_calendar_events_maintenance_order_id", "maintenance_order_id", false},
	{"calendar_events", "idx_calendar_events_start_date", "start_date", false},
}

// Definição das constraints
var constraints = []struct {
	TableName string
	Name      string
	SQL       string
}{
	{"users", "check_user_role", "ALTER TABLE users ADD CONSTRAINT check_user_role CHECK (role IN ('admin', 'manager', 'financial', 'branch_user', 'technician', 'provider'));"},
	{"equipment", "check_equipment_status", "ALTER TABLE equipment ADD CONSTRAINT check_equipment_status CHECK (status IN ('active', 'inactive', 'maintenance', 'discontinued'));"},
	{"maintenance_orders", "check_order_status", "ALTER TABLE maintenance_orders ADD CONSTRAINT check_order_status CHECK (status IN ('pending', 'approved', 'in_progress', 'completed', 'cancelled'));"},
	{"maintenance_orders", "check_order_priority", "ALTER TABLE maintenance_orders ADD CONSTRAINT check_order_priority CHECK (priority IN ('low', 'medium', 'high', 'critical'));"},
}

func GenerateAtlasSchema() {
	// Esta função foi renomeada para evitar conflitos com outras funções main
	// Criar diretório para os arquivos Atlas
	atlasDir := "migrations/atlas"
	err := os.MkdirAll(atlasDir, 0755)
	if err != nil {
		fmt.Printf("Erro ao criar diretório %s: %v\n", atlasDir, err)
		return
	}

	// Criar arquivo de esquema Atlas
	schemaFile, err := os.Create(filepath.Join(atlasDir, "schema.hcl"))
	if err != nil {
		fmt.Printf("Erro ao criar arquivo de esquema Atlas: %v\n", err)
		return
	}
	defer schemaFile.Close()

	// Escrever cabeçalho do arquivo
	schemaFile.WriteString(`// Atlas Schema para o projeto Tradição
// Gerado automaticamente por generate_atlas_schema.go

schema "public" {
  comment = "Esquema principal do banco de dados Tradição"
}

`)

	// Escrever definições de tabelas
	for _, table := range tables {
		schemaFile.WriteString(fmt.Sprintf(`table "%s" {
  schema = schema.public
  comment = "%s"

  %s

}

`, table.Name, table.Comment, formatSQLForAtlas(table.SQL)))
	}

	// Escrever definições de índices
	for _, idx := range indexes {
		uniqueStr := ""
		if idx.Unique {
			uniqueStr = "unique = true"
		}

		schemaFile.WriteString(fmt.Sprintf(`index "%s" {
  table = table.%s
  columns = [%s]
  %s
}

`, idx.Name, idx.TableName, formatColumnsForAtlas(idx.Columns), uniqueStr))
	}

	fmt.Println("Arquivo de esquema Atlas gerado com sucesso em", filepath.Join(atlasDir, "schema.hcl"))

	// Criar arquivo de migração inicial
	migrationFile, err := os.Create(filepath.Join(atlasDir, "001_initial_schema.sql"))
	if err != nil {
		fmt.Printf("Erro ao criar arquivo de migração inicial: %v\n", err)
		return
	}
	defer migrationFile.Close()

	// Escrever cabeçalho do arquivo de migração
	migrationFile.WriteString(`-- Migração inicial para o esquema do projeto Tradição
-- Gerado automaticamente por generate_atlas_schema.go

-- Criar tabelas
`)

	// Escrever comandos SQL para criar tabelas
	for _, table := range tables {
		migrationFile.WriteString(fmt.Sprintf("-- Tabela: %s\n%s\n\n", table.Name, table.SQL))
	}

	// Escrever comandos SQL para criar índices
	migrationFile.WriteString("-- Criar índices\n")
	for _, idx := range indexes {
		uniqueStr := ""
		if idx.Unique {
			uniqueStr = "UNIQUE "
		}

		migrationFile.WriteString(fmt.Sprintf("CREATE %sINDEX %s ON %s (%s);\n",
			uniqueStr, idx.Name, idx.TableName, idx.Columns))
	}
	migrationFile.WriteString("\n")

	// Escrever comandos SQL para adicionar constraints
	migrationFile.WriteString("-- Adicionar constraints\n")
	for _, constraint := range constraints {
		migrationFile.WriteString(fmt.Sprintf("%s\n", constraint.SQL))
	}

	fmt.Println("Arquivo de migração inicial gerado com sucesso em", filepath.Join(atlasDir, "001_initial_schema.sql"))

	// Criar arquivo de configuração Atlas
	atlasConfig, err := os.Create("atlas.hcl")
	if err != nil {
		fmt.Printf("Erro ao criar arquivo de configuração Atlas: %v\n", err)
		return
	}
	defer atlasConfig.Close()

	// Escrever configuração Atlas
	atlasConfig.WriteString(`// Atlas configuration file
// See: https://atlasgo.io/atlas-schema/projects

// Define o ambiente de desenvolvimento
env "dev" {
  // Usar o driver PostgreSQL
  src = "postgres://postgres:postgres@localhost:5432/tradicao_dev?sslmode=disable"

  // Diretório para migrações
  migration {
    dir = "file://migrations/atlas"
  }

  // Formato das migrações
  format {
    migrate {
      apply = "sql"
    }
  }
}

// Define o ambiente de produção
env "prod" {
  // Usar o driver PostgreSQL
  // Substitua os valores conforme necessário
  src = "postgres://postgres:postgres@localhost:5432/tradicao?sslmode=disable"

  // Diretório para migrações
  migration {
    dir = "file://migrations/atlas"
  }

  // Formato das migrações
  format {
    migrate {
      apply = "sql"
    }
  }
}
`)

	fmt.Println("Arquivo de configuração Atlas gerado com sucesso em atlas.hcl")
}

// formatSQLForAtlas formata o SQL para o formato HCL do Atlas
func formatSQLForAtlas(sql string) string {
	// Remover a primeira e a última linha (CREATE TABLE e );)
	lines := strings.Split(sql, "\n")
	if len(lines) <= 2 {
		return ""
	}

	// Remover a primeira linha (CREATE TABLE ...)
	lines = lines[1:]

	// Remover a última linha (que contém apenas ");")
	lines = lines[:len(lines)-1]

	// Processar cada linha para o formato Atlas
	for i, line := range lines {
		// Remover espaços em branco no início
		line = strings.TrimSpace(line)

		// Remover vírgula no final, se houver
		if strings.HasSuffix(line, ",") {
			line = line[:len(line)-1]
		}

		// Dividir a linha em nome da coluna e definição
		parts := strings.SplitN(line, " ", 2)
		if len(parts) < 2 {
			continue
		}

		columnName := parts[0]
		columnDef := parts[1]

		// Formatar para o formato Atlas
		lines[i] = fmt.Sprintf("  column \"%s\" {\n    %s\n  }",
			columnName, formatColumnDefForAtlas(columnDef))
	}

	return strings.Join(lines, "\n\n")
}

// formatColumnDefForAtlas formata a definição de coluna para o formato HCL do Atlas
func formatColumnDefForAtlas(def string) string {
	// Substituir tipos SQL por tipos Atlas
	def = strings.Replace(def, "SERIAL PRIMARY KEY", "type = int, auto_increment = true, primary_key = true", -1)
	def = strings.Replace(def, "INTEGER", "type = int", -1)
	def = strings.Replace(def, "BOOLEAN", "type = bool", -1)
	def = strings.Replace(def, "TIMESTAMP", "type = timestamp", -1)
	def = strings.Replace(def, "TEXT", "type = text", -1)

	// Tratar VARCHAR
	varcharRegex := strings.NewReplacer("VARCHAR(", "type = varchar(", ")", ")")
	def = varcharRegex.Replace(def)

	// Tratar DECIMAL
	decimalRegex := strings.NewReplacer("DECIMAL(", "type = decimal(", ")", ")")
	def = decimalRegex.Replace(def)

	// Tratar NOT NULL
	def = strings.Replace(def, "NOT NULL", "null = false", -1)

	// Tratar UNIQUE
	def = strings.Replace(def, "UNIQUE", "unique = true", -1)

	// Tratar DEFAULT
	if strings.Contains(def, "DEFAULT") {
		parts := strings.SplitN(def, "DEFAULT", 2)
		if len(parts) == 2 {
			defaultValue := strings.TrimSpace(parts[1])

			// Tratar valores específicos
			if defaultValue == "CURRENT_TIMESTAMP" {
				defaultValue = "now()"
			} else if defaultValue == "TRUE" || defaultValue == "true" {
				defaultValue = "true"
			} else if defaultValue == "FALSE" || defaultValue == "false" {
				defaultValue = "false"
			} else if strings.HasPrefix(defaultValue, "'") && strings.HasSuffix(defaultValue, "'") {
				// Manter strings como estão
			} else if _, err := strconv.Atoi(defaultValue); err == nil {
				// É um número, manter como está
			} else {
				// Outros valores, colocar entre aspas
				defaultValue = fmt.Sprintf("\"%s\"", defaultValue)
			}

			def = fmt.Sprintf("%s default = %s", parts[0], defaultValue)
		}
	}

	// Tratar REFERENCES
	if strings.Contains(def, "REFERENCES") {
		parts := strings.SplitN(def, "REFERENCES", 2)
		if len(parts) == 2 {
			refParts := strings.Split(strings.TrimSpace(parts[1]), "(")
			if len(refParts) == 2 {
				tableName := strings.TrimSpace(refParts[0])
				columnName := strings.TrimSpace(strings.TrimSuffix(refParts[1], ")"))
				def = fmt.Sprintf("%s foreign_key { references = table.%s.column.%s }",
					parts[0], tableName, columnName)
			}
		}
	}

	return def
}

// formatColumnsForAtlas formata a lista de colunas para o formato HCL do Atlas
func formatColumnsForAtlas(columns string) string {
	cols := strings.Split(columns, ",")
	for i, col := range cols {
		cols[i] = fmt.Sprintf("column.%s", strings.TrimSpace(col))
	}
	return strings.Join(cols, ", ")
}
