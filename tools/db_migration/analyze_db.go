package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"sort"
	"strings"
	"time"

	"github.com/joho/godotenv"
	_ "github.com/lib/pq"
)

// TableInfo armazena informações sobre uma tabela
type TableInfo struct {
	Name        string
	ColumnCount int
	RowCount    int
	Columns     []ColumnInfo
	Indexes     []IndexInfo
	ForeignKeys []ForeignKeyInfo
}

// ColumnInfo armazena informações sobre uma coluna
type ColumnInfo struct {
	Name         string
	Type         string
	IsNullable   string
	DefaultValue sql.NullString
}

// IndexInfo armazena informações sobre um índice
type IndexInfo struct {
	Name      string
	IsUnique  bool
	IsPrimary bool
	Columns   string
}

// ForeignKeyInfo armazena informações sobre uma chave estrangeira
type ForeignKeyInfo struct {
	Name          string
	ColumnName    string
	RefTableName  string
	RefColumnName string
	UpdateRule    string
	DeleteRule    string
}

func main() {
	// Carregar variáveis de ambiente
	err := godotenv.Load()
	if err != nil {
		log.Println("Aviso: Arquivo .env não encontrado, usando valores padrão")
	}

	// Obter configurações do banco de dados
	dbHost := getEnvOrDefault("DB_HOST", "localhost")
	dbPort := getEnvOrDefault("DB_PORT", "5432")
	dbUser := getEnvOrDefault("DB_USER", "postgres")
	dbPassword := getEnvOrDefault("DB_PASS", "postgres")
	dbName := getEnvOrDefault("DB_NAME", "tradicao")

	// Conectar ao banco de dados
	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)

	db, err := sql.Open("postgres", connStr)
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}
	defer db.Close()

	// Verificar conexão
	err = db.Ping()
	if err != nil {
		log.Fatalf("Erro ao pingar o banco de dados: %v", err)
	}

	fmt.Println("Conexão com o banco de dados estabelecida com sucesso!")
	fmt.Println("Analisando estrutura do banco de dados...")

	// Obter lista de tabelas
	tables, err := getTables(db)
	if err != nil {
		log.Fatalf("Erro ao obter lista de tabelas: %v", err)
	}

	// Obter informações detalhadas de cada tabela
	tableInfos := make([]TableInfo, 0, len(tables))
	for _, tableName := range tables {
		info, err := getTableInfo(db, tableName)
		if err != nil {
			log.Printf("Erro ao obter informações da tabela %s: %v", tableName, err)
			continue
		}
		tableInfos = append(tableInfos, info)
	}

	// Ordenar tabelas por nome
	sort.Slice(tableInfos, func(i, j int) bool {
		return tableInfos[i].Name < tableInfos[j].Name
	})

	// Criar arquivo de relatório
	reportFile, err := os.Create("database_analysis_report.md")
	if err != nil {
		log.Fatalf("Erro ao criar arquivo de relatório: %v", err)
	}
	defer reportFile.Close()

	// Escrever cabeçalho do relatório
	reportFile.WriteString("# Relatório de Análise do Banco de Dados\n\n")
	reportFile.WriteString(fmt.Sprintf("**Data da Análise:** %s\n\n", time.Now().Format("02/01/2006 15:04:05")))
	reportFile.WriteString(fmt.Sprintf("**Banco de Dados:** %s\n\n", dbName))
	reportFile.WriteString(fmt.Sprintf("**Total de Tabelas:** %d\n\n", len(tableInfos)))

	// Escrever resumo das tabelas
	reportFile.WriteString("## Resumo das Tabelas\n\n")
	reportFile.WriteString("| Nome da Tabela | Colunas | Registros | Índices | Chaves Estrangeiras |\n")
	reportFile.WriteString("|---------------|---------|-----------|---------|---------------------|\n")

	for _, info := range tableInfos {
		reportFile.WriteString(fmt.Sprintf("| %s | %d | %d | %d | %d |\n",
			info.Name, info.ColumnCount, info.RowCount, len(info.Indexes), len(info.ForeignKeys)))
	}

	// Escrever detalhes de cada tabela
	reportFile.WriteString("\n## Detalhes das Tabelas\n\n")

	for _, info := range tableInfos {
		reportFile.WriteString(fmt.Sprintf("### Tabela: %s\n\n", info.Name))

		// Colunas
		reportFile.WriteString("#### Colunas\n\n")
		reportFile.WriteString("| Nome | Tipo | Nullable | Valor Padrão |\n")
		reportFile.WriteString("|------|------|----------|-------------|\n")

		for _, col := range info.Columns {
			defaultValue := "NULL"
			if col.DefaultValue.Valid {
				defaultValue = col.DefaultValue.String
			}
			reportFile.WriteString(fmt.Sprintf("| %s | %s | %s | %s |\n",
				col.Name, col.Type, col.IsNullable, defaultValue))
		}

		// Índices
		if len(info.Indexes) > 0 {
			reportFile.WriteString("\n#### Índices\n\n")
			reportFile.WriteString("| Nome | Único | Primário | Colunas |\n")
			reportFile.WriteString("|------|-------|----------|--------|\n")

			for _, idx := range info.Indexes {
				reportFile.WriteString(fmt.Sprintf("| %s | %t | %t | %s |\n",
					idx.Name, idx.IsUnique, idx.IsPrimary, idx.Columns))
			}
		}

		// Chaves Estrangeiras
		if len(info.ForeignKeys) > 0 {
			reportFile.WriteString("\n#### Chaves Estrangeiras\n\n")
			reportFile.WriteString("| Nome | Coluna | Tabela Referenciada | Coluna Referenciada | Update Rule | Delete Rule |\n")
			reportFile.WriteString("|------|--------|---------------------|---------------------|-------------|-------------|\n")

			for _, fk := range info.ForeignKeys {
				reportFile.WriteString(fmt.Sprintf("| %s | %s | %s | %s | %s | %s |\n",
					fk.Name, fk.ColumnName, fk.RefTableName, fk.RefColumnName, fk.UpdateRule, fk.DeleteRule))
			}
		}

		reportFile.WriteString("\n")
	}

	// Análise de possíveis problemas
	reportFile.WriteString("## Análise de Problemas\n\n")

	// Verificar tabelas duplicadas
	duplicateTables := findDuplicateTables(tableInfos)
	if len(duplicateTables) > 0 {
		reportFile.WriteString("### Possíveis Tabelas Duplicadas\n\n")
		for _, group := range duplicateTables {
			reportFile.WriteString(fmt.Sprintf("- Grupo: %s\n", strings.Join(group, ", ")))
		}
		reportFile.WriteString("\n")
	}

	// Verificar inconsistências de nomenclatura
	inconsistentNames := findInconsistentNames(tableInfos)
	if len(inconsistentNames) > 0 {
		reportFile.WriteString("### Inconsistências de Nomenclatura\n\n")
		for _, issue := range inconsistentNames {
			reportFile.WriteString(fmt.Sprintf("- %s\n", issue))
		}
		reportFile.WriteString("\n")
	}

	// Verificar índices ausentes em chaves estrangeiras
	missingIndexes := findMissingIndexes(tableInfos)
	if len(missingIndexes) > 0 {
		reportFile.WriteString("### Índices Ausentes em Chaves Estrangeiras\n\n")
		for _, issue := range missingIndexes {
			reportFile.WriteString(fmt.Sprintf("- %s\n", issue))
		}
		reportFile.WriteString("\n")
	}

	fmt.Println("Análise concluída! Relatório gerado em database_analysis_report.md")
}

// getTables retorna a lista de tabelas no banco de dados
func getTables(db *sql.DB) ([]string, error) {
	rows, err := db.Query(`
		SELECT table_name 
		FROM information_schema.tables 
		WHERE table_schema = 'public' 
		AND table_type = 'BASE TABLE'
		ORDER BY table_name
	`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tables []string
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			return nil, err
		}
		tables = append(tables, tableName)
	}

	return tables, nil
}

// getTableInfo retorna informações detalhadas sobre uma tabela
func getTableInfo(db *sql.DB, tableName string) (TableInfo, error) {
	info := TableInfo{
		Name: tableName,
	}

	// Obter colunas
	columns, err := getColumns(db, tableName)
	if err != nil {
		return info, err
	}
	info.Columns = columns
	info.ColumnCount = len(columns)

	// Obter contagem de registros
	var count int
	err = db.QueryRow(fmt.Sprintf("SELECT COUNT(*) FROM %s", tableName)).Scan(&count)
	if err != nil {
		log.Printf("Erro ao contar registros da tabela %s: %v", tableName, err)
	}
	info.RowCount = count

	// Obter índices
	indexes, err := getIndexes(db, tableName)
	if err != nil {
		log.Printf("Erro ao obter índices da tabela %s: %v", tableName, err)
	}
	info.Indexes = indexes

	// Obter chaves estrangeiras
	foreignKeys, err := getForeignKeys(db, tableName)
	if err != nil {
		log.Printf("Erro ao obter chaves estrangeiras da tabela %s: %v", tableName, err)
	}
	info.ForeignKeys = foreignKeys

	return info, nil
}

// getColumns retorna informações sobre as colunas de uma tabela
func getColumns(db *sql.DB, tableName string) ([]ColumnInfo, error) {
	rows, err := db.Query(`
		SELECT column_name, data_type, is_nullable, column_default
		FROM information_schema.columns
		WHERE table_schema = 'public' AND table_name = $1
		ORDER BY ordinal_position
	`, tableName)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var columns []ColumnInfo
	for rows.Next() {
		var col ColumnInfo
		if err := rows.Scan(&col.Name, &col.Type, &col.IsNullable, &col.DefaultValue); err != nil {
			return nil, err
		}
		columns = append(columns, col)
	}

	return columns, nil
}

// getIndexes retorna informações sobre os índices de uma tabela
func getIndexes(db *sql.DB, tableName string) ([]IndexInfo, error) {
	rows, err := db.Query(`
		SELECT
			i.relname AS index_name,
			ix.indisunique AS is_unique,
			ix.indisprimary AS is_primary,
			array_to_string(ARRAY(
				SELECT pg_get_indexdef(ix.indexrelid, k + 1, true)
				FROM generate_subscripts(ix.indkey, 1) AS k
				ORDER BY k
			), ', ') AS column_names
		FROM
			pg_index ix
		JOIN
			pg_class i ON i.oid = ix.indexrelid
		JOIN
			pg_class t ON t.oid = ix.indrelid
		JOIN
			pg_namespace n ON n.oid = t.relnamespace
		WHERE
			t.relname = $1
		AND
			n.nspname = 'public'
		ORDER BY
			i.relname
	`, tableName)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var indexes []IndexInfo
	for rows.Next() {
		var idx IndexInfo
		if err := rows.Scan(&idx.Name, &idx.IsUnique, &idx.IsPrimary, &idx.Columns); err != nil {
			return nil, err
		}
		indexes = append(indexes, idx)
	}

	return indexes, nil
}

// getForeignKeys retorna informações sobre as chaves estrangeiras de uma tabela
func getForeignKeys(db *sql.DB, tableName string) ([]ForeignKeyInfo, error) {
	rows, err := db.Query(`
		SELECT
			tc.constraint_name,
			kcu.column_name,
			ccu.table_name AS foreign_table_name,
			ccu.column_name AS foreign_column_name,
			rc.update_rule,
			rc.delete_rule
		FROM
			information_schema.table_constraints AS tc
		JOIN
			information_schema.key_column_usage AS kcu
			ON tc.constraint_name = kcu.constraint_name
			AND tc.table_schema = kcu.table_schema
		JOIN
			information_schema.constraint_column_usage AS ccu
			ON ccu.constraint_name = tc.constraint_name
			AND ccu.table_schema = tc.table_schema
		JOIN
			information_schema.referential_constraints AS rc
			ON tc.constraint_name = rc.constraint_name
		WHERE
			tc.constraint_type = 'FOREIGN KEY'
			AND tc.table_name = $1
			AND tc.table_schema = 'public'
	`, tableName)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var foreignKeys []ForeignKeyInfo
	for rows.Next() {
		var fk ForeignKeyInfo
		if err := rows.Scan(&fk.Name, &fk.ColumnName, &fk.RefTableName, &fk.RefColumnName, &fk.UpdateRule, &fk.DeleteRule); err != nil {
			return nil, err
		}
		foreignKeys = append(foreignKeys, fk)
	}

	return foreignKeys, nil
}

// findDuplicateTables identifica possíveis tabelas duplicadas com base na estrutura
func findDuplicateTables(tables []TableInfo) [][]string {
	// Mapa para agrupar tabelas por "assinatura" (conjunto de colunas)
	tablesBySignature := make(map[string][]string)

	for _, table := range tables {
		// Criar uma assinatura baseada nos nomes e tipos das colunas
		var colNames []string
		for _, col := range table.Columns {
			colNames = append(colNames, fmt.Sprintf("%s:%s", col.Name, col.Type))
		}
		sort.Strings(colNames)
		signature := strings.Join(colNames, "|")

		// Adicionar a tabela ao grupo com a mesma assinatura
		tablesBySignature[signature] = append(tablesBySignature[signature], table.Name)
	}

	// Filtrar apenas grupos com mais de uma tabela (possíveis duplicatas)
	var duplicateGroups [][]string
	for _, group := range tablesBySignature {
		if len(group) > 1 {
			duplicateGroups = append(duplicateGroups, group)
		}
	}

	// Verificar também por nomes similares
	similarNameGroups := findSimilarNameTables(tables)
	for _, group := range similarNameGroups {
		// Verificar se o grupo já está incluído
		alreadyIncluded := false
		for _, existingGroup := range duplicateGroups {
			if containsAllStrings(existingGroup, group) {
				alreadyIncluded = true
				break
			}
		}

		if !alreadyIncluded {
			duplicateGroups = append(duplicateGroups, group)
		}
	}

	return duplicateGroups
}

// findSimilarNameTables identifica tabelas com nomes similares
func findSimilarNameTables(tables []TableInfo) [][]string {
	// Pares de nomes em inglês/português que podem indicar duplicação
	namePairs := map[string]string{
		"user":        "usuario",
		"users":       "usuarios",
		"branch":      "filial",
		"branches":    "filiais",
		"equipment":   "equipamento",
		"equipments":  "equipamentos",
		"order":       "ordem",
		"orders":      "ordens",
		"maintenance": "manutencao",
		"station":     "posto",
		"stations":    "postos",
	}

	// Mapa para agrupar tabelas por raiz de nome
	tablesByRoot := make(map[string][]string)

	for _, table := range tables {
		tableName := strings.ToLower(table.Name)

		// Verificar se o nome da tabela corresponde a algum par conhecido
		for eng, pt := range namePairs {
			if strings.Contains(tableName, eng) {
				tablesByRoot[eng] = append(tablesByRoot[eng], table.Name)
			}
			if strings.Contains(tableName, pt) {
				tablesByRoot[eng] = append(tablesByRoot[eng], table.Name)
			}
		}
	}

	// Filtrar apenas grupos com mais de uma tabela
	var similarGroups [][]string
	for _, group := range tablesByRoot {
		if len(group) > 1 {
			similarGroups = append(similarGroups, group)
		}
	}

	return similarGroups
}

// findInconsistentNames identifica inconsistências de nomenclatura
func findInconsistentNames(tables []TableInfo) []string {
	var issues []string

	// Verificar mistura de inglês e português nos nomes de tabelas
	englishTables := 0
	portugueseTables := 0

	// Palavras comuns em inglês e português para detecção
	englishWords := []string{"user", "order", "equipment", "maintenance", "branch", "station"}
	portugueseWords := []string{"usuario", "ordem", "equipamento", "manutencao", "filial", "posto"}

	for _, table := range tables {
		tableName := strings.ToLower(table.Name)

		isEnglish := false
		isPortuguese := false

		for _, word := range englishWords {
			if strings.Contains(tableName, word) {
				isEnglish = true
				break
			}
		}

		for _, word := range portugueseWords {
			if strings.Contains(tableName, word) {
				isPortuguese = true
				break
			}
		}

		if isEnglish {
			englishTables++
		}
		if isPortuguese {
			portugueseTables++
		}
	}

	if englishTables > 0 && portugueseTables > 0 {
		issues = append(issues, fmt.Sprintf("Mistura de nomenclatura: %d tabelas com nomes em inglês e %d tabelas com nomes em português",
			englishTables, portugueseTables))
	}

	// Verificar inconsistências em nomes de colunas
	for _, table := range tables {
		englishColumns := 0
		portugueseColumns := 0

		for _, col := range table.Columns {
			colName := strings.ToLower(col.Name)

			isEnglish := false
			isPortuguese := false

			// Palavras comuns em colunas
			engColWords := []string{"name", "type", "status", "date", "description", "address", "city"}
			ptColWords := []string{"nome", "tipo", "status", "data", "descricao", "endereco", "cidade"}

			for _, word := range engColWords {
				if strings.Contains(colName, word) {
					isEnglish = true
					break
				}
			}

			for _, word := range ptColWords {
				if strings.Contains(colName, word) {
					isPortuguese = true
					break
				}
			}

			if isEnglish {
				englishColumns++
			}
			if isPortuguese {
				portugueseColumns++
			}
		}

		if englishColumns > 0 && portugueseColumns > 0 {
			issues = append(issues, fmt.Sprintf("Tabela %s: Mistura de nomenclatura em colunas (%d em inglês, %d em português)",
				table.Name, englishColumns, portugueseColumns))
		}
	}

	return issues
}

// findMissingIndexes identifica chaves estrangeiras sem índices
func findMissingIndexes(tables []TableInfo) []string {
	var issues []string

	for _, table := range tables {
		for _, fk := range table.ForeignKeys {
			// Verificar se existe um índice para esta coluna
			hasIndex := false
			for _, idx := range table.Indexes {
				if strings.Contains(idx.Columns, fk.ColumnName) {
					hasIndex = true
					break
				}
			}

			if !hasIndex {
				issues = append(issues, fmt.Sprintf("Tabela %s: Chave estrangeira %s (%s) não possui índice",
					table.Name, fk.Name, fk.ColumnName))
			}
		}
	}

	return issues
}

// containsAllStrings verifica se todos os elementos de subset estão em set
func containsAllStrings(set, subset []string) bool {
	if len(subset) > len(set) {
		return false
	}

	setMap := make(map[string]bool)
	for _, s := range set {
		setMap[s] = true
	}

	for _, s := range subset {
		if !setMap[s] {
			return false
		}
	}

	return true
}

// getEnvOrDefault retorna o valor da variável de ambiente ou o valor padrão
func getEnvOrDefault(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}
