# Guia de Troubleshooting para Migração de Banco de Dados

## Problemas Comuns e Soluções

### 1. <PERSON><PERSON>s de Conexão

#### Sintomas:
- Falha ao conectar ao banco de dados
- Timeout na conexão
- Erro de autenticação

#### Soluções:
1. Verificar credenciais no arquivo .env
2. Confirmar se o PostgreSQL está rodando:
   ```bash
   systemctl status postgresql
   ```
3. Verificar firewall:
   ```bash
   sudo ufw status
   ```
4. Testar conexão manualmente:
   ```bash
   psql -h localhost -U postgres -d tradicao
   ```

### 2. Erros de Espaço em Disco

#### Sintomas:
- Falha ao criar backup
- Erro de espaço insuficiente
- Migração interrompida

#### Soluções:
1. Verificar espaço livre:
   ```bash
   df -h
   ```
2. Limpar backups antigos:
   ```bash
   find backups/ -type f -mtime +7 -delete
   ```
3. Limpar logs antigos:
   ```bash
   find logs/ -type f -mtime +7 -delete
   ```

### 3. Erros de Memória

#### Sintomas:
- Processo de migração lento
- OOM (Out of Memory) errors
- Sistema travando

#### Soluções:
1. Verificar uso de memória:
   ```bash
   free -h
   ```
2. Ajustar configurações do PostgreSQL:
   ```bash
   sudo nano /etc/postgresql/[version]/main/postgresql.conf
   ```
   - Ajustar `shared_buffers`
   - Ajustar `work_mem`
   - Ajustar `maintenance_work_mem`

### 4. Erros de Validação de Dados

#### Sintomas:
- Contagem de registros diferente
- Referências quebradas
- Dados inconsistentes

#### Soluções:
1. Verificar logs de migração:
   ```bash
   cat logs/migration_*.log
   ```
2. Executar validação manual:
   ```bash
   ./run_migration.sh validate
   ```
3. Verificar relatório de métricas:
   ```bash
   cat logs/metrics_*.json
   ```

### 5. Erros de Permissão

#### Sintomas:
- Falha ao criar backup
- Falha ao acessar arquivos
- Erro de permissão negada

#### Soluções:
1. Verificar permissões de diretórios:
   ```bash
   ls -la backups/ logs/ migrations/
   ```
2. Ajustar permissões:
   ```bash
   chmod 755 backups/ logs/ migrations/
   chown -R postgres:postgres backups/ logs/ migrations/
   ```

## Procedimentos de Recuperação

### 1. Rollback Manual

```bash
# Listar backups disponíveis
ls -l backups/

# Restaurar backup específico
psql -h localhost -U postgres -d tradicao < backups/backup_YYYYMMDD_HHMMSS.sql
```

### 2. Recuperação de Dados

```bash
# Verificar tabelas com problemas
cat logs/metrics_*.json | jq '.TableMetrics | to_entries[] | select(.value.FailedRecords > 0)'

# Recuperar dados específicos
./run_migration.sh --table=users --date=2024-04-20
```

### 3. Limpeza de Ambiente

```bash
# Limpar arquivos temporários
rm -rf tmp/*

# Limpar caches
go clean -cache

# Reiniciar serviços
sudo systemctl restart postgresql
```

## Contato e Suporte

Em caso de problemas persistentes, entre em contato com a equipe de suporte:

- Email: <EMAIL>
- Telefone: (XX) XXXX-XXXX
- Horário de atendimento: Segunda a Sexta, 9h às 18h 