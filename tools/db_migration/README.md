# Sistema de Migração de Banco de Dados

## Visão Geral

Este sistema foi desenvolvido para realizar a migração de dados entre bancos PostgreSQL, com foco em segurança, confiabilidade e monitoramento. O sistema inclui ferramentas para análise, geração de schema e migração de dados.

## Arquitetura

O sistema é composto por três componentes principais:

1. **Análise do Banco (`analyze_db.go`)**
   - Analisa a estrutura do banco de origem
   - Identifica tabelas, colunas e relacionamentos
   - Verifica inconsistências e problemas potenciais

2. **Geração de Schema (`generate_atlas_schema.go`)**
   - Gera schema compatível com Atlas
   - Mapeia tipos de dados
   - Cria índices e constraints

3. **Migração de Dados (`migrate_data.go`)**
   - Realiza a migração dos dados
   - Inclui sistema de retry e validação
   - Gera logs e métricas detalhadas

## Fluxo de Migração

1. **Preparação**
   ```bash
   # Verificar requisitos
   ./run_migration.sh check
   
   # Criar backup
   ./run_migration.sh backup
   ```

2. **Análise**
   ```bash
   # Analisar banco de origem
   ./run_analysis.sh
   ```

3. **Geração de Schema**
   ```bash
   # Gerar schema Atlas
   ./run_generate_atlas.sh
   ```

4. **Migração**
   ```bash
   # Executar em modo dry run
   ./run_migration.sh dry_run
   
   # Executar migração real
   ./run_migration.sh
   ```

## Estrutura de Diretórios

```
db_migration/
├── backups/          # Backups do banco de dados
├── logs/            # Logs de migração e métricas
├── migrations/      # Scripts de migração
├── analyze_db.go    # Ferramenta de análise
├── generate_atlas_schema.go  # Gerador de schema
├── migrate_data.go  # Ferramenta de migração
├── run_analysis.sh  # Script de análise
├── run_generate_atlas.sh  # Script de geração
├── run_migration.sh # Script de migração
└── TROUBLESHOOTING.md  # Guia de solução de problemas
```

## Dependências

- Go 1.16+
- PostgreSQL 12+
- Atlas CLI
- Bash 4.0+

## Requisitos do Sistema

- Mínimo 10GB de espaço em disco
- Mínimo 4GB de RAM livre
- Acesso root/sudo para configuração do PostgreSQL
- Conexão estável com o banco de dados

## Estruturas de Dados

### Métricas de Migração

```go
type MigrationMetrics struct {
    StartTime      time.Time
    EndTime        time.Time
    TotalRecords   int64
    FailedRecords  int64
    SuccessRecords int64
    TableMetrics   map[string]TableMetric
}

type TableMetric struct {
    StartTime      time.Time
    EndTime        time.Time
    TotalRecords   int64
    FailedRecords  int64
    SuccessRecords int64
    Errors         []string
}
```

### Logs

Os logs são gerados em dois formatos:

1. **Logs de Texto** (`migration_log_YYYYMMDD_HHMMSS.txt`)
   - Registro cronológico das operações
   - Mensagens de erro e avisos
   - Informações de progresso

2. **Métricas JSON** (`migration_metrics_YYYYMMDD_HHMMSS.json`)
   - Estatísticas detalhadas por tabela
   - Tempos de execução
   - Contagens de registros

## Boas Práticas

### 1. Dry Run
- Sempre execute em modo dry run primeiro
- Verifique os logs e métricas
- Confirme a contagem de registros

### 2. Backup
- Faça backup antes de cada migração
- Mantenha backups em local seguro
- Verifique a integridade do backup

### 3. Monitoramento
- Monitore o uso de recursos
- Verifique os logs regularmente
- Acompanhe o progresso da migração

### 4. Tratamento de Erros
- Use o sistema de retry para falhas temporárias
- Valide os dados após a migração
- Mantenha logs detalhados

## Segurança

- Use variáveis de ambiente para credenciais
- Limite permissões de usuário do banco
- Criptografe backups sensíveis
- Mantenha logs seguros

## Performance

- Use batch processing para grandes volumes
- Otimize queries de migração
- Monitore uso de recursos
- Ajuste configurações do PostgreSQL

## Manutenção

- Limpe logs antigos regularmente
- Mantenha backups organizados
- Atualize documentação
- Revise configurações periodicamente

## Suporte

Para problemas ou dúvidas, consulte:
1. [TROUBLESHOOTING.md](TROUBLESHOOTING.md)
2. Equipe de suporte: <EMAIL>
3. Documentação do PostgreSQL
4. Documentação do Atlas
