#!/bin/bash

# Script para executar todos os passos da migração
# Uso: ./run_all.sh [dry_run]
# Se dry_run for especificado, a migração será executada em modo de simulação

# Verificar se o modo dry run foi especificado
if [ "$1" == "dry_run" ]; then
    DRY_RUN=true
    echo "Executando em modo DRY RUN - nenhuma alteração será feita no banco de destino"
else
    DRY_RUN=false
    echo "Executando migração real - as alterações serão aplicadas ao banco de destino"
fi

# Diretório do script
SCRIPT_DIR="$(dirname "$0")"
cd "$SCRIPT_DIR"

# Criar diretório para logs
mkdir -p logs

# 1. Analisar o banco de dados atual
echo "1. Analisando o banco de dados atual..."
./run_analysis.sh > logs/analysis.log 2>&1
if [ $? -ne 0 ]; then
    echo "Erro ao analisar o banco de dados atual"
    exit 1
fi
echo "Análise concluída com sucesso"
echo "Relatório gerado em database_analysis_report.md"

# 2. Gerar o esquema Atlas
echo "2. Gerando o esquema Atlas..."
./run_generate_atlas.sh > logs/generate_atlas.log 2>&1
if [ $? -ne 0 ]; then
    echo "Erro ao gerar o esquema Atlas"
    exit 1
fi
echo "Geração do esquema Atlas concluída com sucesso"
echo "Arquivos gerados:"
echo "- atlas.hcl"
echo "- migrations/atlas/schema.hcl"
echo "- migrations/atlas/001_initial_schema.sql"

# 3. Migrar os dados
echo "3. Migrando os dados..."
if [ "$DRY_RUN" == "true" ]; then
    ./run_migration.sh dry_run > logs/migration.log 2>&1
else
    ./run_migration.sh > logs/migration.log 2>&1
fi
if [ $? -ne 0 ]; then
    echo "Erro ao migrar os dados"
    exit 1
fi
echo "Migração de dados concluída com sucesso"
echo "Log gerado em logs/migration.log"

echo "Todos os passos da migração foram concluídos com sucesso"
exit 0
