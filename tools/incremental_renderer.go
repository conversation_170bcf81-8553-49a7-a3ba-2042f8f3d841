package main

import (
	"bytes"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
)

// IncrementalRenderer renderiza código em partes para evitar travamentos
type IncrementalRenderer struct {
	buffer     bytes.Buffer
	maxSize    int
	outputFile string
	partCount  int
}

// NewIncrementalRenderer cria um novo IncrementalRenderer
func NewIncrementalRenderer(outputFile string, maxSize int) *IncrementalRenderer {
	return &IncrementalRenderer{
		buffer:     bytes.Buffer{},
		maxSize:    maxSize,
		outputFile: outputFile,
		partCount:  0,
	}
}

// Render renderiza uma parte do código
func (r *IncrementalRenderer) Render(part string) error {
	r.buffer.WriteString(part)

	// Se o buffer atingiu o tamanho máximo, salvar em um arquivo temporário
	if r.buffer.Len() >= r.maxSize {
		err := r.flush(true)
		if err != nil {
			return err
		}
	}

	return nil
}

// Flush salva o conteúdo do buffer em um arquivo
func (r *IncrementalRenderer) Flush() error {
	return r.flush(false)
}

// flush salva o conteúdo do buffer em um arquivo
func (r *IncrementalRenderer) flush(isTemp bool) error {
	if r.buffer.Len() == 0 {
		return nil
	}

	var outputFile string
	if isTemp {
		// Salvar em um arquivo temporário
		r.partCount++
		outputFile = fmt.Sprintf("%s.part%d", r.outputFile, r.partCount)
	} else {
		// Salvar no arquivo final
		outputFile = r.outputFile
	}

	// Criar diretório de saída se não existir
	outputDir := filepath.Dir(outputFile)
	err := os.MkdirAll(outputDir, 0755)
	if err != nil {
		return fmt.Errorf("erro ao criar diretório de saída: %v", err)
	}

	// Salvar o conteúdo do buffer
	err = ioutil.WriteFile(outputFile, r.buffer.Bytes(), 0644)
	if err != nil {
		return fmt.Errorf("erro ao salvar arquivo %s: %v", outputFile, err)
	}

	// Limpar o buffer
	r.buffer.Reset()

	return nil
}

// Merge mescla todos os arquivos temporários em um único arquivo
func (r *IncrementalRenderer) Merge() error {
	// Se não há arquivos temporários, não há nada para mesclar
	if r.partCount == 0 {
		return nil
	}

	// Criar arquivo final
	outputFile, err := os.Create(r.outputFile)
	if err != nil {
		return fmt.Errorf("erro ao criar arquivo final: %v", err)
	}
	defer outputFile.Close()

	// Mesclar arquivos temporários
	for i := 1; i <= r.partCount; i++ {
		tempFile := fmt.Sprintf("%s.part%d", r.outputFile, i)
		content, err := ioutil.ReadFile(tempFile)
		if err != nil {
			return fmt.Errorf("erro ao ler arquivo temporário %s: %v", tempFile, err)
		}

		_, err = outputFile.Write(content)
		if err != nil {
			return fmt.Errorf("erro ao escrever no arquivo final: %v", err)
		}

		// Remover arquivo temporário
		err = os.Remove(tempFile)
		if err != nil {
			return fmt.Errorf("erro ao remover arquivo temporário %s: %v", tempFile, err)
		}
	}

	// Adicionar conteúdo do buffer, se houver
	if r.buffer.Len() > 0 {
		_, err = outputFile.Write(r.buffer.Bytes())
		if err != nil {
			return fmt.Errorf("erro ao escrever no arquivo final: %v", err)
		}
		r.buffer.Reset()
	}

	return nil
}

// Função principal para o incremental renderer
func runIncrementalRendererMain() {
	if len(os.Args) < 2 {
		fmt.Println("Uso: go run incremental_renderer.go <arquivo_saida>")
		os.Exit(1)
	}

	outputFile := os.Args[1]
	maxSize := 1024 * 1024 // 1MB

	renderer := NewIncrementalRenderer(outputFile, maxSize)

	// Exemplo de uso
	for i := 0; i < 10; i++ {
		content := fmt.Sprintf("Parte %d do código\n", i+1)
		err := renderer.Render(content)
		if err != nil {
			fmt.Printf("Erro ao renderizar parte %d: %v\n", i+1, err)
			os.Exit(1)
		}
	}

	// Salvar o conteúdo restante
	err := renderer.Flush()
	if err != nil {
		fmt.Printf("Erro ao salvar conteúdo restante: %v\n", err)
		os.Exit(1)
	}

	// Mesclar arquivos temporários
	err = renderer.Merge()
	if err != nil {
		fmt.Printf("Erro ao mesclar arquivos temporários: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("Código renderizado com sucesso em %s\n", outputFile)
}
