package main

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"regexp"
	"strings"
)

// CodeSplitter divide um arquivo grande em vários arquivos menores
type CodeSplitter struct {
	InputFile string
	Content   string
}

// NewCodeSplitter cria um novo CodeSplitter
func NewCodeSplitter(inputFile string) (*CodeSplitter, error) {
	content, err := ioutil.ReadFile(inputFile)
	if err != nil {
		return nil, fmt.Errorf("erro ao ler arquivo: %v", err)
	}

	return &CodeSplitter{
		InputFile: inputFile,
		Content:   string(content),
	}, nil
}

// Split divide o arquivo em vários arquivos menores
func (s *CodeSplitter) Split() (map[string]string, error) {
	// Extrair o pacote
	packageRegex := regexp.MustCompile(`package\s+(\w+)`)
	packageMatch := packageRegex.FindStringSubmatch(s.Content)
	if len(packageMatch) < 2 {
		return nil, fmt.Errorf("não foi possível encontrar o pacote no arquivo")
	}
	packageName := packageMatch[1]

	// Extrair as importações
	importRegex := regexp.MustCompile(`import\s+\(([\s\S]*?)\)`)
	importMatch := importRegex.FindStringSubmatch(s.Content)
	var imports string
	if len(importMatch) >= 2 {
		imports = importMatch[1]
	}

	// Extrair as funções
	funcRegex := regexp.MustCompile(`func\s+(\w+)[\s\S]*?{([\s\S]*?)(?:\n}\n|\n}$)`)
	funcMatches := funcRegex.FindAllStringSubmatch(s.Content, -1)

	// Extrair as estruturas
	typeRegex := regexp.MustCompile(`type\s+(\w+)\s+struct\s+{([\s\S]*?)(?:\n}\n|\n}$)`)
	typeMatches := typeRegex.FindAllStringSubmatch(s.Content, -1)

	// Criar os arquivos
	files := make(map[string]string)

	// Adicionar funções
	for _, match := range funcMatches {
		funcName := match[1]
		funcBody := match[0]
		fileName := fmt.Sprintf("%s_%s.go", strings.ToLower(funcName), packageName)

		content := fmt.Sprintf("package %s\n\n", packageName)
		if imports != "" {
			content += fmt.Sprintf("import (\n%s\n)\n\n", imports)
		}
		content += funcBody

		files[fileName] = content
	}

	// Adicionar estruturas
	for _, match := range typeMatches {
		typeName := match[1]
		typeBody := match[0]
		fileName := fmt.Sprintf("%s_%s.go", strings.ToLower(typeName), packageName)

		content := fmt.Sprintf("package %s\n\n", packageName)
		if imports != "" {
			content += fmt.Sprintf("import (\n%s\n)\n\n", imports)
		}
		content += typeBody

		files[fileName] = content
	}

	return files, nil
}

// SaveFiles salva os arquivos divididos
func (s *CodeSplitter) SaveFiles(files map[string]string, outputDir string) error {
	// Criar diretório de saída se não existir
	err := os.MkdirAll(outputDir, 0755)
	if err != nil {
		return fmt.Errorf("erro ao criar diretório de saída: %v", err)
	}

	// Salvar os arquivos
	for fileName, content := range files {
		outputPath := filepath.Join(outputDir, fileName)
		err := ioutil.WriteFile(outputPath, []byte(content), 0644)
		if err != nil {
			return fmt.Errorf("erro ao salvar arquivo %s: %v", fileName, err)
		}
	}

	return nil
}

// Função principal para o code splitter
func runCodeSplitterMain() {
	if len(os.Args) < 3 {
		fmt.Println("Uso: go run code_splitter.go <arquivo_entrada> <diretorio_saida>")
		os.Exit(1)
	}

	inputFile := os.Args[1]
	outputDir := os.Args[2]

	splitter, err := NewCodeSplitter(inputFile)
	if err != nil {
		fmt.Printf("Erro ao criar splitter: %v\n", err)
		os.Exit(1)
	}

	files, err := splitter.Split()
	if err != nil {
		fmt.Printf("Erro ao dividir arquivo: %v\n", err)
		os.Exit(1)
	}

	err = splitter.SaveFiles(files, outputDir)
	if err != nil {
		fmt.Printf("Erro ao salvar arquivos: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("Arquivo %s dividido em %d arquivos no diretório %s\n", inputFile, len(files), outputDir)
}
