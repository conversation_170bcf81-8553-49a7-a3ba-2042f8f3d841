#!/bin/bash

# Script para executar as ferramentas de validação e correção de código
# 
# Este script compila e executa as ferramentas de validação de código
# para corrigir problemas comuns no projeto

echo "=== 🔍 FERRAMENTA DE CORREÇÃO DO PROJETO TENHOAPP ==="
echo "Esta ferramenta verifica e corrige problemas comuns no código do projeto."

# Diret<PERSON>rio raiz (padrão é o diretório atual)
ROOT_DIR=${1:-.}

echo -e "\nCompilando ferramentas de validação..."
go build -o ./tools/template_validator ./tools/template_validator.go
go build -o ./tools/code_validator ./tools/code_validator.go

if [ $? -ne 0 ]; then
    echo "Erro ao compilar as ferramentas de validação!"
    exit 1
fi

echo -e "\n🔷 FASE 1: Verificando templates e carregamentos..."
./tools/template_validator "$ROOT_DIR"

echo -e "\n🔷 FASE 2: Verificando problemas gerais de código..."
./tools/code_validator "$ROOT_DIR"

echo -e "\n✅ VERIFICAÇÃO CONCLUÍDA!"
echo "A aplicação deve ser testada para garantir que todas as correções foram aplicadas corretamente."
echo "Recomendações finais:"
echo "1. Execute 'go build ./...' para verificar se há erros de compilação"
echo "2. Inicie o servidor com 'go run cmd/main.go' e teste manualmente as principais funcionalidades"
echo "3. Verifique os logs em busca de erros durante a execução"

# Limpa arquivos temporários
echo -e "\nLimpando arquivos temporários..."
rm -f ./tools/template_validator ./tools/code_validator

echo "Processo finalizado com sucesso!" 