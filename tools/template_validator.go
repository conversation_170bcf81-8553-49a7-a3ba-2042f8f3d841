package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"strings"
)

// TemplateValidationResult armazena resultados da validação
type TemplateValidationResult struct {
	FilePath        string
	OriginalContent string
	UpdatedContent  string
	HasChanges      bool
	Errors          []string
}

// TemplateValidator valida e corrige problemas com templates
type TemplateValidator struct {
	RootDir         string
	Results         map[string]*TemplateValidationResult
	TemplatePattern string
}

// NewTemplateValidator cria um novo validador de templates
func NewTemplateValidator(rootDir string) *TemplateValidator {
	return &TemplateValidator{
		RootDir:         rootDir,
		Results:         make(map[string]*TemplateValidationResult),
		TemplatePattern: `web/templates/\*\*/\*`,
	}
}

// Run executa a validação e correção
func (v *TemplateValidator) Run() error {
	// Percorre todos os arquivos .go
	err := filepath.Walk(v.RootDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Pula diretórios
		if info.IsDir() {
			return nil
		}

		// Só trata arquivos .go
		if !strings.HasSuffix(path, ".go") {
			return nil
		}

		// Pula o diretório vendor
		if strings.Contains(path, "/vendor/") || strings.Contains(path, "\\vendor\\") {
			return nil
		}

		// Processa o arquivo
		return v.processFile(path, info.Mode())
	})

	if err != nil {
		return fmt.Errorf("erro ao percorrer arquivos: %v", err)
	}

	return nil
}

// Processa um arquivo Go em busca de problemas com templates
func (v *TemplateValidator) processFile(path string, fileMode os.FileMode) error {
	content, err := os.ReadFile(path)
	if err != nil {
		return fmt.Errorf("erro ao ler arquivo %s: %v", path, err)
	}

	originalContent := string(content)
	result := &TemplateValidationResult{
		FilePath:        path,
		OriginalContent: originalContent,
		UpdatedContent:  originalContent,
		HasChanges:      false,
		Errors:          []string{},
	}

	v.Results[path] = result

	// Verifica carregamentos redundantes de templates
	updatedContent, hasChanges := v.fixRedundantTemplateLoading(result.UpdatedContent)
	result.UpdatedContent = updatedContent
	result.HasChanges = result.HasChanges || hasChanges

	// Verifica referências incorretas ao template login.html
	updatedContent, hasLoginChanges := v.fixLoginTemplateReference(result.UpdatedContent)
	result.UpdatedContent = updatedContent
	result.HasChanges = result.HasChanges || hasLoginChanges

	// Verifica e corrige referências incorretas a dashboard.html
	updatedContent, hasDashboardChanges := v.fixDashboardTemplateReference(result.UpdatedContent)
	result.UpdatedContent = updatedContent
	result.HasChanges = result.HasChanges || hasDashboardChanges

	// Se houver mudanças, salva o arquivo
	if result.HasChanges {
		err = os.WriteFile(path, []byte(result.UpdatedContent), fileMode)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("erro ao salvar arquivo: %v", err))
		}
	}

	return nil
}

// Corrige carregamentos redundantes de templates
func (v *TemplateValidator) fixRedundantTemplateLoading(content string) (string, bool) {
	hasChanges := false
	updatedContent := content

	// Procura pelo padrão LoadHTMLGlob("web/templates/*") após LoadHTMLGlob("web/templates/**/*")
	doubleLoadPattern := regexp.MustCompile(`(router|r|engine)\.LoadHTMLGlob\("web/templates/\*\*/\*"\)[\s\n]+.*LoadHTMLGlob\("web/templates/\*"\)`)

	if doubleLoadPattern.MatchString(updatedContent) {
		updatedContent = doubleLoadPattern.ReplaceAllStringFunc(updatedContent, func(matchedString string) string {
			lines := strings.Split(matchedString, "\n")

			// Comentar a segunda linha de carregamento
			for i := 1; i < len(lines); i++ {
				if strings.Contains(lines[i], "LoadHTMLGlob") && strings.Contains(lines[i], "web/templates/*") {
					// Comentar a linha
					lines[i] = strings.TrimSpace(lines[i])
					if !strings.HasPrefix(lines[i], "//") {
						lines[i] = "// " + lines[i] + " // Comentado automaticamente - carregamento redundante"
					}
					break
				}
			}

			return strings.Join(lines, "\n")
		})

		hasChanges = true
	}

	return updatedContent, hasChanges
}

// Corrige referências incorretas ao template login.html
func (v *TemplateValidator) fixLoginTemplateReference(content string) (string, bool) {
	hasChanges := false
	updatedContent := content

	// Procura e corrige referências a "login.html" para "login/login.html"
	loginPattern := regexp.MustCompile(`c\.HTML\([^,]+, *"login\.html"`)
	if loginPattern.MatchString(updatedContent) {
		updatedContent = loginPattern.ReplaceAllStringFunc(updatedContent, func(matchedString string) string {
			return strings.Replace(matchedString, `"login.html"`, `"login/login.html"`, 1)
		})
		hasChanges = true
	}

	return updatedContent, hasChanges
}

// Corrige referências incorretas ao template dashboard.html
func (v *TemplateValidator) fixDashboardTemplateReference(content string) (string, bool) {
	hasChanges := false
	updatedContent := content

	// Procura e corrige referências a "dashboard.html" para "dashboard/dashboard.html"
	dashboardPattern := regexp.MustCompile(`c\.HTML\([^,]+, *"dashboard\.html"`)
	if dashboardPattern.MatchString(updatedContent) {
		updatedContent = dashboardPattern.ReplaceAllStringFunc(updatedContent, func(matchedString string) string {
			return strings.Replace(matchedString, `"dashboard.html"`, `"dashboard/dashboard.html"`, 1)
		})
		hasChanges = true
	}

	return updatedContent, hasChanges
}

// PrintResults imprime os resultados da validação
func (v *TemplateValidator) PrintResults() {
	changedFiles := 0
	for path, result := range v.Results {
		if result.HasChanges {
			changedFiles++
			fmt.Printf("Arquivo %s foi modificado\n", path)
		}
	}

	fmt.Printf("\nSumário:\n")
	fmt.Printf("- Total de arquivos analisados: %d\n", len(v.Results))
	fmt.Printf("- Arquivos modificados: %d\n", changedFiles)
}

// RunTemplateValidation executa a validação e correção completa
func RunTemplateValidation(rootDir string) {
	validator := NewTemplateValidator(rootDir)
	err := validator.Run()

	if err != nil {
		log.Fatalf("Erro durante a validação: %v", err)
	}

	validator.PrintResults()
}

// Esta função é chamada pelo main.go
// Implementação em exports.go
