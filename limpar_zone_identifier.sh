#!/bin/bash

# Script para remover todos os arquivos :Zone.Identifier do projeto
# Uso: ./limpar_zone_identifier.sh

# Cores para saída
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} Iniciando limpeza de arquivos :Zone.Identifier..."

# Contar quantos arquivos serão removidos
TOTAL=$(find . -name "*:Zone.Identifier" | wc -l)
echo -e "${YELLOW}Encontrados ${TOTAL} arquivos :Zone.Identifier para remover.${NC}"

# Remover os arquivos
if [ $TOTAL -gt 0 ]; then
    echo -e "${YELLOW}Removendo arquivos...${NC}"
    find . -name "*:Zone.Identifier" -delete
    
    # Verificar se todos foram removidos
    REMAINING=$(find . -name "*:Zone.Identifier" | wc -l)
    if [ $REMAINING -eq 0 ]; then
        echo -e "${GREEN}Todos os arquivos :Zone.Identifier foram removidos com sucesso!${NC}"
    else
        echo -e "${YELLOW}Atenção: ${REMAINING} arquivos não puderam ser removidos.${NC}"
    fi
else
    echo -e "${GREEN}Nenhum arquivo :Zone.Identifier encontrado.${NC}"
fi

echo -e "${CYAN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} Limpeza concluída."
