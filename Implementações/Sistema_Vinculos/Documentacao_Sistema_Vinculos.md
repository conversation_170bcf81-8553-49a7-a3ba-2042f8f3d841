# Documentação do Sistema de Gerenciamento de Vínculos

## Visão Geral

O Sistema de Gerenciamento de Vínculos é responsável por gerenciar os relacionamentos entre filiais, prestadoras de serviço e técnicos no Sistema de Gestão Tradição. Ele permite que administradores e gerentes configurem quais prestadoras podem atender a quais filiais, e quais técnicos podem ser designados para quais filiais.

## Funcionalidades Implementadas

1. **Gerenciamento de Vínculos entre Prestadoras e Filiais**
   - Criação de vínculos entre prestadoras e filiais
   - Remoção de vínculos entre prestadoras e filiais
   - Listagem de prestadoras vinculadas a uma filial

2. **Gerenciamento de Vínculos entre Técnicos e Filiais**
   - Criação de vínculos entre técnicos e filiais
   - Remoção de vínculos entre técnicos e filiais
   - Listagem de técnicos vinculados a uma filial

3. **Gerenciamento de Vínculos entre Técnicos e Ordens**
   - Atribuição de técnicos a ordens de manutenção
   - Remoção de atribuições de técnicos a ordens
   - Listagem de técnicos atribuídos a uma ordem
   - Listagem de ordens atribuídas a um técnico

4. **Validações na Criação de Ordens**
   - Exibição apenas de prestadoras vinculadas à filial
   - Exibição apenas de técnicos vinculados à prestadora selecionada

## Estrutura do Sistema

### Modelos de Dados

1. **BranchProviderLink**
   - Representa o vínculo entre uma filial e uma prestadora
   - Campos: ID, BranchID, ProviderID, CreatedAt, UpdatedAt

2. **TechnicianBranch**
   - Representa o vínculo entre um técnico e uma filial
   - Campos: TechnicianID, BranchID, SpecialtyID, CreatedAt, UpdatedAt

3. **TechnicianOrder**
   - Representa o vínculo entre um técnico e uma ordem
   - Campos: ID, TechnicianID, OrderID, CreatedAt, CreatedBy, Notes

### Serviços

1. **LinkManagementService**
   - Gerencia vínculos entre filiais, prestadoras e técnicos
   - Métodos:
     - LinkProviderToBranch
     - UnlinkProviderFromBranch
     - GetBranchProviders
     - LinkTechnicianToBranch
     - UnlinkTechnicianFromBranch
     - GetBranchTechnicians
     - GetProviderTechnicians
     - GetTechnicianBranches
     - GetProviderBranches

2. **TechnicianOrderService**
   - Gerencia vínculos entre técnicos e ordens
   - Métodos:
     - AssignOrderToTechnician
     - UnassignOrderFromTechnician
     - GetOrdersByTechnician
     - GetTechniciansByOrder
     - HasAccessToOrder
     - GetAllAssignments
     - BatchAssignOrders
     - BatchAssignTechnicians

### Controladores

1. **LinkManagementController**
   - Controlador para gerenciar vínculos entre filiais, prestadoras e técnicos
   - Endpoints:
     - POST /api/links/provider-branch
     - DELETE /api/links/provider-branch/:provider_id/:branch_id
     - GET /api/links/branch/:branch_id/providers
     - POST /api/links/technician-branch
     - DELETE /api/links/technician-branch/:technician_id/:branch_id
     - GET /api/links/branch/:branch_id/technicians
     - GET /api/links/provider/:provider_id/technicians

2. **TechnicianAssignmentController**
   - Controlador para gerenciar vínculos entre técnicos e ordens
   - Endpoints:
     - POST /api/links/technician-order
     - DELETE /api/links/technician-order/:technician_id/:order_id
     - GET /api/links/order/:order_id/technicians
     - GET /api/links/technician/:technician_id/orders

### Interface de Usuário

1. **Página de Gerenciamento de Vínculos**
   - Rota: /admin/links
   - Funcionalidades:
     - Gerenciamento de vínculos entre prestadoras e filiais
     - Gerenciamento de vínculos entre técnicos e filiais
     - Visualização de técnicos por prestadora

2. **Formulário de Criação de Ordens**
   - Rota: /orders/create
   - Funcionalidades:
     - Seleção de prestadoras vinculadas à filial
     - Seleção de técnicos vinculados à prestadora selecionada

## Fluxos de Trabalho

### Criação de Vínculo entre Prestadora e Filial

1. Administrador acessa a página de gerenciamento de vínculos
2. Seleciona a aba "Prestadoras e Filiais"
3. Clica no botão "Novo Vínculo"
4. Seleciona a prestadora e a filial
5. Clica em "Salvar"
6. O sistema cria o vínculo entre a prestadora e a filial

### Criação de Vínculo entre Técnico e Filial

1. Administrador acessa a página de gerenciamento de vínculos
2. Seleciona a aba "Técnicos e Filiais"
3. Clica no botão "Novo Vínculo"
4. Seleciona o técnico, a filial e a especialidade
5. Clica em "Salvar"
6. O sistema cria o vínculo entre o técnico e a filial

### Criação de Ordem com Seleção de Prestadora e Técnico

1. Usuário acessa o formulário de criação de ordens
2. Preenche os campos obrigatórios
3. Seleciona uma prestadora vinculada à filial
4. O sistema carrega os técnicos vinculados à prestadora selecionada
5. Usuário seleciona um técnico (opcional)
6. Usuário envia o formulário
7. O sistema cria a ordem e, se um técnico foi selecionado, cria o vínculo entre o técnico e a ordem

## Permissões

- Apenas usuários com perfil de administrador, gerente ou financeiro podem gerenciar vínculos
- Usuários com perfil de filial não têm acesso à página de gerenciamento de vínculos nem às suas APIs
- Qualquer usuário pode criar ordens, mas apenas prestadoras vinculadas à filial são exibidas como opções
- Apenas técnicos vinculados à prestadora selecionada são exibidos como opções

## Considerações Técnicas

- Os vínculos são armazenados em tabelas de junção no banco de dados
- As validações são realizadas tanto no frontend quanto no backend
- O sistema utiliza o padrão de arquitetura MVC (Model-View-Controller)
- As APIs seguem o padrão RESTful
