# Implementação da Interface de Gerenciamento de Relações

## Visão Geral

Esta documentação detalha a implementação da interface de gerenciamento de relações entre filiais, prestadoras de serviço e técnicos no Sistema de Gestão Tradição. A interface permite visualizar, criar, editar e excluir relações entre estas entidades, facilitando a gestão de quais prestadoras podem atender a quais filiais e quais técnicos estão associados a quais filiais.

## Arquivos Implementados

### Templates HTML
- **Página Principal**: `web/templates/link_management/link_management.html`
- **Documentação**: `web/templates/link_management/README.md`

### Arquivos CSS
- **Estilos da Página**: `web/static/css/link_management.css`

### Arquivos JavaScript
- **Funcionalidades da Página**: `web/static/js/link_management.js`

### Documentação
- **Documentação da Implementação**: `Implementações/Sistema_Vinculos/Interface_Gerenciamento_Relacoes.md`

## Estrutura da Página

A página de Gerenciamento de Relações é composta por três abas principais:

1. **Prestadoras e Filiais**: Gerencia quais prestadoras de serviço estão autorizadas a atender quais filiais.
2. **Técnicos e Filiais**: Gerencia quais técnicos estão associados a quais filiais, incluindo suas especialidades.
3. **Prestadoras e Técnicos**: Visualiza quais técnicos estão associados a quais prestadoras de serviço.

Cada aba contém:
- Filtros para facilitar a busca
- Tabela para visualização das relações existentes
- Botões para adicionar, editar e excluir relações
- Modais para criação e edição de relações

## Funcionalidades Implementadas

### Gerenciamento de Prestadoras e Filiais
- Visualizar todas as relações entre prestadoras e filiais
- Adicionar uma nova prestadora a uma filial
- Remover uma prestadora de uma filial
- Filtrar por prestadora ou filial

### Gerenciamento de Técnicos e Filiais
- Visualizar todas as relações entre técnicos e filiais
- Adicionar um técnico a uma filial com uma especialidade específica
- Remover um técnico de uma filial
- Filtrar por técnico, filial ou especialidade

### Visualização de Prestadoras e Técnicos
- Visualizar todos os técnicos associados a uma prestadora
- Filtrar por prestadora

## Integração com API

A página utiliza os seguintes endpoints da API:

### Vínculos entre Prestadoras e Filiais
- `GET /api/links/provider-branch` - Listar todos os vínculos
- `POST /api/links/provider-branch` - Criar um novo vínculo
- `DELETE /api/links/provider-branch/:provider_id/:branch_id` - Remover um vínculo
- `GET /api/links/branch/:branch_id/providers` - Listar prestadoras de uma filial

### Vínculos entre Técnicos e Filiais
- `GET /api/links/technician-branch` - Listar todos os vínculos
- `POST /api/links/technician-branch` - Criar um novo vínculo
- `DELETE /api/links/technician-branch/:technician_id/:branch_id` - Remover um vínculo
- `GET /api/links/branch/:branch_id/technicians` - Listar técnicos de uma filial

### Vínculos entre Prestadoras e Técnicos
- `GET /api/links/provider/:provider_id/technicians` - Listar técnicos de uma prestadora

## Detalhes de Implementação

### HTML

O template HTML principal (`link_management.html`) segue a estrutura padrão do projeto, com:
- Inclusão da sidebar via `{{ template "layouts/sidebar.html" . }}`
- Estrutura de abas usando Bootstrap 5
- Tabelas para exibição de dados
- Modais para criação e edição de relações
- Filtros para facilitar a busca

### CSS

O arquivo CSS (`link_management.css`) implementa:
- Estilos consistentes com o design system Shell
- Tema escuro com detalhes em amarelo e vermelho
- Estilos responsivos para diferentes tamanhos de tela
- Animações sutis para melhorar a experiência do usuário

### JavaScript

O arquivo JavaScript (`link_management.js`) implementa:
- Carregamento de dados via API
- Renderização dinâmica das tabelas
- Filtros para facilitar a busca
- Criação e exclusão de relações via API
- Validação de formulários
- Feedback visual para o usuário

## Fluxo de Trabalho

### Vinculação de Prestadora a Filial

1. Usuário acessa a página de gerenciamento de relações
2. Seleciona a aba "Prestadoras e Filiais"
3. Clica no botão "Novo Vínculo"
4. Seleciona a prestadora e a filial no modal
5. Clica em "Salvar"
6. O sistema envia uma requisição POST para `/api/links/provider-branch`
7. A tabela é atualizada com o novo vínculo

### Vinculação de Técnico a Filial

1. Usuário acessa a página de gerenciamento de relações
2. Seleciona a aba "Técnicos e Filiais"
3. Clica no botão "Novo Vínculo"
4. Seleciona o técnico, a filial e a especialidade no modal
5. Clica em "Salvar"
6. O sistema envia uma requisição POST para `/api/links/technician-branch`
7. A tabela é atualizada com o novo vínculo

### Visualização de Técnicos por Prestadora

1. Usuário acessa a página de gerenciamento de relações
2. Seleciona a aba "Prestadoras e Técnicos"
3. Seleciona a prestadora no dropdown
4. O sistema envia uma requisição GET para `/api/links/provider/:provider_id/technicians`
5. A tabela é atualizada com os técnicos da prestadora selecionada

## Considerações de Segurança

- Apenas usuários com perfil de administrador, gerente ou financeiro têm acesso a esta página
- Todas as operações são validadas no backend antes de serem executadas
- Dados sensíveis não são expostos na interface
- Confirmação é solicitada antes de operações destrutivas (exclusão de vínculos)

## Próximos Passos

- Implementar paginação para tabelas com muitos registros
- Adicionar funcionalidade de exportação de dados
- Implementar histórico de alterações para auditoria
- Adicionar visualização em formato de gráfico para facilitar a compreensão das relações

## Conclusão

A implementação da interface de gerenciamento de relações fornece uma ferramenta completa e intuitiva para administrar os vínculos entre filiais, prestadoras de serviço e técnicos no Sistema de Gestão Tradição. A interface segue os padrões visuais do projeto e implementa todas as funcionalidades necessárias para uma gestão eficiente das relações.
