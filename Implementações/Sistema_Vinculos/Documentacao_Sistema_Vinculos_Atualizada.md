# Sistema de Gerenciamento de Vínculos

## Visão Geral

O Sistema de Gerenciamento de Vínculos é responsável por gerenciar as relações entre filiais, prestadoras de serviço e técnicos no Sistema de Gestão Tradição. Ele permite:

1. Vincular prestadoras de serviço a filiais
2. Vincular técnicos a filiais com especialidades específicas
3. Visualizar técnicos por prestadora
4. Gerenciar a atribuição de técnicos a ordens de serviço

## Arquitetura

O sistema segue a arquitetura limpa e é composto por:

### Camada de Apresentação
- **Interface Web**: Página de gerenciamento de vínculos (`/admin/links`)
- **API REST**: Endpoints para gerenciamento de vínculos

### Camada de Controladores
- **LinkManagementController**: Gerencia vínculos entre filiais, prestadoras e técnicos
- **TechnicianAssignmentController**: Gerencia atribuição de técnicos a ordens

### Camada de Serviços
- **LinkManagementService**: Implementa a lógica de negócio para gerenciamento de vínculos

### Camada de Repositórios
- **BranchRepository**: Acesso a dados de filiais
- **TechnicianRepository**: Acesso a dados de técnicos
- **ServiceProviderRepository**: Acesso a dados de prestadoras

## Endpoints da API

### Vínculos entre Prestadoras e Filiais

- **GET /api/links/provider-branch**
  - Retorna todos os vínculos entre prestadoras e filiais
  - Resposta: `{ "success": true, "links": [...] }`

- **POST /api/links/provider-branch**
  - Cria um novo vínculo entre prestadora e filial
  - Corpo: `{ "provider_id": 1, "branch_id": 2 }`
  - Resposta: `{ "success": true, "message": "Prestador vinculado com sucesso" }`

- **DELETE /api/links/provider-branch/:provider_id/:branch_id**
  - Remove um vínculo entre prestadora e filial
  - Resposta: `{ "success": true, "message": "Vínculo excluído com sucesso" }`

- **GET /api/links/branch/:branch_id/providers**
  - Retorna as prestadoras vinculadas a uma filial específica
  - Resposta: `[{ "id": 1, "name": "Prestadora A", ... }]`

### Vínculos entre Técnicos e Filiais

- **GET /api/links/technician-branch**
  - Retorna todos os vínculos entre técnicos e filiais
  - Resposta: `{ "success": true, "links": [...] }`

- **POST /api/links/technician-branch**
  - Cria um novo vínculo entre técnico e filial
  - Corpo: `{ "technician_id": 1, "branch_id": 2, "specialty_id": 3 }`
  - Resposta: `{ "success": true, "message": "Técnico vinculado com sucesso" }`

- **DELETE /api/links/technician-branch/:technician_id/:branch_id**
  - Remove um vínculo entre técnico e filial
  - Resposta: `{ "success": true, "message": "Vínculo excluído com sucesso" }`

- **GET /api/links/branch/:branch_id/technicians**
  - Retorna os técnicos vinculados a uma filial específica
  - Resposta: `[{ "id": 1, "name": "Técnico A", ... }]`

- **GET /api/links/provider/:provider_id/technicians**
  - Retorna os técnicos vinculados a uma prestadora específica
  - Resposta: `[{ "id": 1, "name": "Técnico A", ... }]`

### Vínculos entre Técnicos e Ordens

- **POST /api/links/technician-order**
  - Atribui um técnico a uma ordem
  - Corpo: `{ "technician_id": 1, "order_id": 2, "notes": "Observações" }`
  - Resposta: `{ "success": true, "message": "Técnico atribuído com sucesso" }`

- **DELETE /api/links/technician-order/:technician_id/:order_id**
  - Remove a atribuição de um técnico a uma ordem
  - Resposta: `{ "success": true, "message": "Atribuição removida com sucesso" }`

- **GET /api/links/order/:order_id/technicians**
  - Retorna os técnicos atribuídos a uma ordem específica
  - Resposta: `[{ "id": 1, "name": "Técnico A", ... }]`

- **GET /api/links/technician/:technician_id/orders**
  - Retorna as ordens atribuídas a um técnico específico
  - Resposta: `[{ "id": 1, "title": "Ordem A", ... }]`

## Interface de Usuário

A interface de usuário para gerenciamento de vínculos está disponível em `/admin/links` e é composta por três abas:

1. **Prestadoras e Filiais**: Gerenciamento de vínculos entre prestadoras e filiais
2. **Técnicos e Filiais**: Gerenciamento de vínculos entre técnicos e filiais
3. **Prestadoras e Técnicos**: Visualização de técnicos por prestadora

### Funcionalidades da Interface

- Listar vínculos existentes
- Criar novos vínculos
- Excluir vínculos existentes
- Visualizar técnicos por prestadora

## Fluxo de Trabalho

### Vinculação de Prestadora a Filial

1. Administrador acessa a página de gerenciamento de vínculos
2. Seleciona a aba "Prestadoras e Filiais"
3. Clica em "Novo Vínculo"
4. Seleciona a prestadora e a filial
5. Confirma a criação do vínculo

### Vinculação de Técnico a Filial

1. Administrador acessa a página de gerenciamento de vínculos
2. Seleciona a aba "Técnicos e Filiais"
3. Clica em "Novo Vínculo"
4. Seleciona o técnico, a filial e a especialidade
5. Confirma a criação do vínculo

### Visualização de Técnicos por Prestadora

1. Administrador acessa a página de gerenciamento de vínculos
2. Seleciona a aba "Prestadoras e Técnicos"
3. Seleciona a prestadora no dropdown
4. Visualiza a lista de técnicos vinculados à prestadora

## Considerações de Segurança

- Apenas usuários com perfil de Administrador, Gerente ou Financeiro podem acessar as funcionalidades de gerenciamento de vínculos
- Usuários com perfil de Filial não têm acesso à página de gerenciamento de vínculos nem às suas APIs
- Todas as rotas da API são protegidas por autenticação JWT
- As rotas administrativas são protegidas por middleware de verificação de perfil que restringe o acesso apenas aos perfis autorizados

## Integração com Outros Sistemas

O Sistema de Gerenciamento de Vínculos se integra com:

1. **Sistema de Ordens de Serviço**: Para atribuição de técnicos a ordens
2. **Sistema de Usuários**: Para autenticação e autorização
3. **Sistema de Filiais**: Para gerenciamento de filiais
4. **Sistema de Prestadoras**: Para gerenciamento de prestadoras
5. **Sistema de Técnicos**: Para gerenciamento de técnicos

## Tabelas do Banco de Dados

### service_provider_branches
- `id`: ID do vínculo
- `service_provider_id`: ID da prestadora
- `branch_id`: ID da filial
- `created_at`: Data de criação
- `updated_at`: Data de atualização

### technician_branches
- `id`: ID do vínculo
- `technician_id`: ID do técnico
- `branch_id`: ID da filial
- `specialty_id`: ID da especialidade
- `created_at`: Data de criação
- `updated_at`: Data de atualização

### technician_orders
- `id`: ID da atribuição
- `technician_id`: ID do técnico
- `order_id`: ID da ordem
- `created_by`: ID do usuário que criou a atribuição
- `notes`: Observações
- `created_at`: Data de criação
- `updated_at`: Data de atualização
