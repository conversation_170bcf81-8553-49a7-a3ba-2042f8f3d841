# Atualização do Sistema de Permissões para Gerenciamento de Vínculos

## Visão Geral

Este documento descreve as atualizações realizadas no sistema de permissões para o gerenciamento de vínculos entre filiais, prestadoras de serviço e técnicos no Sistema de Gestão Tradição. As alterações visam restringir o acesso à página de gerenciamento de vínculos e suas APIs apenas para usuários com perfis de administrador, gerente e financeiro, removendo o acesso para usuários com perfil de filial.

## Alterações Realizadas

### 1. Atualização do Arquivo de Permissões

O arquivo `data/permissions.yaml` foi atualizado para remover as permissões de acesso à página de gerenciamento de vínculos e suas APIs para usuários com perfil de filial:

#### Antes:

```yaml
filial:
    description: Gestor de Filial/Posto
    pages:
        - dashboard
        - calendario-flip
        - manutencao
        - nova-manutencao
        - minha-conta
        - orders
        - maintenance/view
        - ordemtecnico
        - manutencaoordem
        - admin/link-management
    apis:
        - api/user/me
        - api/auth/logout
        - api/equipments
        - api/equipments/:id
        - api/equipments/types
        - api/equipments/filial/:id
        - api/maintenance
        - api/maintenance/:id
        - api/stations
        - api/notifications/settings
        - api/notifications/subscribe
        - api/ordens/:id/manutencao
        - api/ordens/:id/custos
        - api/ordens/:id/cronograma
        - api/ordens/:id/chat
        - api/ordens/:id
        - api/links/provider-branch
        - api/links/branch/:branch_id/providers
        - api/links/technician-branch
        - api/links/branch/:branch_id/technicians
        - api/links/provider/:provider_id/technicians
        - api/links/provider-technician
        - api/links/provider-technician/inherit-branches
        - api/links/technician-order
        - api/links/order/:order_id/technicians
        - api/links/technician/:technician_id/orders
        - api/permissions/reload
```

#### Depois:

```yaml
filial:
    description: Gestor de Filial/Posto
    pages:
        - dashboard
        - calendario-flip
        - manutencao
        - nova-manutencao
        - minha-conta
        - orders
        - maintenance/view
        - ordemtecnico
        - manutencaoordem
    apis:
        - api/user/me
        - api/auth/logout
        - api/equipments
        - api/equipments/:id
        - api/equipments/types
        - api/equipments/filial/:id
        - api/maintenance
        - api/maintenance/:id
        - api/stations
        - api/notifications/settings
        - api/notifications/subscribe
        - api/ordens/:id/manutencao
        - api/ordens/:id/custos
        - api/ordens/:id/cronograma
        - api/ordens/:id/chat
        - api/ordens/:id
```

### 2. Atualização das Rotas de Gerenciamento de Vínculos

O arquivo `internal/routes/link_management_routes.go` foi atualizado para remover o perfil de filial do middleware de papel:

#### Antes:

```go
// Rotas administrativas (apenas admin, gerente, financeiro e filial)
adminLinks := apiLinks.Group("")
adminLinks.Use(middleware.RoleMiddleware(models.RoleAdmin, models.RoleGerente, models.RoleFinanceiro, models.RoleFilial))
```

#### Depois:

```go
// Rotas administrativas (apenas admin, gerente e financeiro)
adminLinks := apiLinks.Group("")
adminLinks.Use(middleware.RoleMiddleware(models.RoleAdmin, models.RoleGerente, models.RoleFinanceiro))
```

## Impacto das Alterações

As alterações realizadas têm os seguintes impactos:

1. **Restrição de Acesso à Página**: Usuários com perfil de filial não podem mais acessar a página de gerenciamento de vínculos (`/admin/link-management`). Ao tentar acessar essa página, eles serão redirecionados para a página de acesso negado.

2. **Restrição de Acesso às APIs**: Usuários com perfil de filial não podem mais acessar as APIs de gerenciamento de vínculos (`/api/links/*`). Ao tentar acessar essas APIs, eles receberão uma resposta de erro com status 403 (Forbidden).

3. **Manutenção da Segurança**: As alterações garantem que apenas usuários com perfis administrativos (admin, gerente e financeiro) possam gerenciar os vínculos entre filiais, prestadoras e técnicos, mantendo a integridade e segurança do sistema.

## Verificação das Alterações

As alterações foram verificadas com os seguintes testes:

1. **Acesso à Página**: Tentativa de acesso à página de gerenciamento de vínculos com um usuário de perfil filial, resultando em redirecionamento para a página de acesso negado.

2. **Acesso às APIs**: Tentativa de acesso às APIs de gerenciamento de vínculos com um usuário de perfil filial, resultando em resposta de erro com status 403.

3. **Acesso com Perfil Administrativo**: Verificação de que usuários com perfis de admin, gerente e financeiro ainda podem acessar a página e as APIs de gerenciamento de vínculos.

## Conclusão

As alterações realizadas no sistema de permissões para o gerenciamento de vínculos garantem que apenas usuários com perfis administrativos (admin, gerente e financeiro) possam gerenciar os vínculos entre filiais, prestadoras e técnicos. Isso mantém a integridade e segurança do sistema, evitando que usuários com perfil de filial possam modificar esses vínculos.

A documentação existente do Sistema de Gerenciamento de Vínculos foi atualizada para refletir essas alterações, garantindo que todos os desenvolvedores e usuários do sistema estejam cientes das novas restrições de acesso.
