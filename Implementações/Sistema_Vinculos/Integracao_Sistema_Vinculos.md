# Integração do Sistema de Gerenciamento de Vínculos

Para integrar o Sistema de Gerenciamento de Vínculos ao Sistema de Gestão Tradição, é necessário adicionar o código abaixo ao arquivo `cmd/main.go` após a inicialização dos outros serviços e antes da configuração das rotas.

## Passos para Integração

1. Adicione as seguintes importações ao arquivo `cmd/main.go` (se ainda não existirem):

```go
import (
    // ... outras importações ...
    "tradicao/internal/controllers"
    "tradicao/internal/repository"
    "tradicao/internal/routes"
    "tradicao/internal/services"
)
```

2. Adicione o seguinte código após a inicialização dos outros serviços e antes da configuração das rotas:

```go
// Inicializar repositório de branch
branchRepo := repository.NewBranchRepository(db)

// Inicializar repositório de service provider
serviceProviderRepo := repository.NewServiceProviderRepository(db)

// Inicializar serviço de gerenciamento de vínculos
linkManagementService := services.NewLinkManagementService(
    db,
    branchRepo,
    technicianRepo,
    serviceProviderRepo,
)

// Inicializar controlador de gerenciamento de vínculos
linkManagementController := controllers.NewLinkManagementController(linkManagementService)

// Configurar rotas de gerenciamento de vínculos
routes.SetupLinkManagementRoutes(router, linkManagementController, technicianOrderService)
```

3. Certifique-se de que o `technicianOrderService` já está inicializado antes deste código.

## Verificação da Integração

Após adicionar o código acima, você pode verificar se a integração foi bem-sucedida acessando as seguintes URLs:

- `/admin/links` - Página de gerenciamento de vínculos
- `/api/links/branch/:branch_id/providers` - API para listar prestadores vinculados a uma filial
- `/api/links/provider/:provider_id/technicians` - API para listar técnicos vinculados a uma prestadora

## Considerações Adicionais

- Certifique-se de que os repositórios `branchRepo`, `technicianRepo` e `serviceProviderRepo` estão corretamente inicializados.
- Verifique se o banco de dados possui as tabelas necessárias para o funcionamento do sistema de gerenciamento de vínculos.
- Certifique-se de que o usuário que está acessando as rotas tem as permissões necessárias.

## Exemplo de Uso

Após a integração, você pode usar o sistema de gerenciamento de vínculos da seguinte forma:

1. Acesse a página de gerenciamento de vínculos em `/admin/links`.
2. Crie vínculos entre prestadoras e filiais.
3. Crie vínculos entre técnicos e filiais.
4. Ao criar uma ordem de manutenção, apenas as prestadoras vinculadas à filial serão exibidas como opções.
5. Ao selecionar uma prestadora, apenas os técnicos vinculados a essa prestadora serão exibidos como opções.
