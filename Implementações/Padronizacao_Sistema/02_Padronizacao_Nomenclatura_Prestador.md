# Padronização da Nomenclatura de Perfil de Prestador

## Problema Identificado

Existia uma inconsistência na nomenclatura utilizada para representar o perfil de prestador de serviço no sistema:

1. A constante `RolePrestador` em `internal/models/models.go` usava o valor `"provider"` (em inglês)
2. Em várias partes do código, era usado `"prestadores"` (em português, no plural)
3. Em algumas partes do código, era usado `"prestador"` (em português, no singular)
4. O arquivo de permissões `data/permissions.yaml` usava `prestadores` como chave para o perfil

Esta inconsistência dificultava a manutenção do código e poderia levar a erros, especialmente em verificações de permissão.

## Solução Implementada

### 1. Padronização da Constante `RolePrestador`

Atualizamos a constante `RolePrestador` em `internal/models/models.go` com comentários explicativos:

```go
RolePrestador UserRole = "provider" // Constante padronizada para prestadores
// IMPORTANTE: Esta constante representa o perfil de prestador de serviço.
// O valor "provider" é usado internamente, enquanto "prestadores" é usado em algumas partes do sistema.
```

### 2. Adição de Funções de Compatibilidade

Atualizamos o arquivo `internal/models/role_compatibility.go` com funções auxiliares para garantir compatibilidade com o código existente:

```go
// IsProvider verifica se um perfil de usuário é um prestador de serviço,
// considerando tanto o valor padronizado "provider" quanto os valores legados "prestadores" e "prestador".
// Esta função deve ser usada em vez de comparações diretas com RolePrestador
// para garantir compatibilidade com dados existentes.
func IsProvider(role string) bool {
    return role == string(RolePrestador) || role == "prestadores" || role == "prestador"
}
```

Também atualizamos a função `NormalizeRole` para normalizar os valores legados:

```go
// NormalizeRole normaliza um perfil de usuário para o valor padronizado.
// Por exemplo, converte "tecnico" para "technician" e "prestadores" para "provider".
func NormalizeRole(role string) string {
    switch role {
    case "tecnico":
        return string(RoleTechnician)
    case "prestadores", "prestador":
        return string(RolePrestador)
    default:
        return role
    }
}
```

E a função `GetRoleConstant` para lidar com todos os valores possíveis:

```go
// GetRoleConstant retorna a constante UserRole correspondente a uma string de perfil.
func GetRoleConstant(role string) UserRole {
    switch role {
    // ...
    case "provider", "prestadores", "prestador":
        return RolePrestador
    // ...
    }
}
```

### 3. Atualização do Arquivo de Permissões

Atualizamos o arquivo `data/permissions.yaml` para usar a chave padronizada `provider` em vez de `prestadores`:

```yaml
provider:
    description: Prestador de Serviço
    # Nota: Anteriormente este perfil era "prestadores", foi alterado para "provider"
    # para padronização da nomenclatura. O sistema mantém compatibilidade com ambos.
    pages:
        # ...
```

### 4. Atualização dos Middlewares e Serviços

Atualizamos os middlewares e serviços que verificam o perfil de prestador para usar as funções de compatibilidade:

- `internal/middleware/provider_middleware.go`:
  ```go
  // Verificar se é um prestador (provider, prestadores, prestador)
  userRoleStr := userRole.(string)
  if userRoleStr != "provider" && userRoleStr != "prestadores" && userRoleStr != "prestador" {
      c.JSON(http.StatusForbidden, gin.H{"error": "Acesso negado. Apenas prestadores podem acessar este recurso."})
      c.Abort()
      return
  }
  ```

- `internal/services/filial_filter_service.go`:
  ```go
  // Caso 2: Perfil de prestador - acesso apenas às filiais autorizadas
  // Usando a função de compatibilidade para verificar se é um prestador
  if models.IsProvider(userRole) {
      // ...
  }
  ```

- `internal/services/filial_permission_service.go`:
  ```go
  // Caso 2: Perfil de prestador - acesso apenas às filiais autorizadas
  // Usando a função de compatibilidade para verificar se é um prestador
  if models.IsProvider(userRole) {
      // ...
  }
  ```

## Impacto da Alteração

Esta alteração padroniza a nomenclatura do perfil de prestador em todo o sistema, mantendo compatibilidade com o código existente através das funções auxiliares. Isso facilita a manutenção do código e reduz a chance de erros em verificações de permissão.

### Benefícios

1. **Consistência**: Nomenclatura consistente em todo o sistema
2. **Manutenibilidade**: Código mais fácil de manter e entender
3. **Robustez**: Redução de erros em verificações de permissão
4. **Compatibilidade**: Mantém compatibilidade com código e dados existentes

### Compatibilidade

As funções de compatibilidade garantem que o sistema continue funcionando corretamente com todos os valores possíveis (`"provider"`, `"prestadores"` e `"prestador"`), permitindo uma transição gradual para o valor padronizado.

## Testes Realizados

### 1. Verificação de Permissões

- Testamos o login e acesso de usuários com perfil de prestador
- Verificamos se as permissões baseadas em perfil continuam funcionando corretamente
- Testamos o acesso a páginas e APIs restritas a prestadores

### 2. Filtragem de Filiais

- Testamos a filtragem de filiais para usuários com perfil de prestador
- Verificamos se as filiais autorizadas são corretamente retornadas

### 3. Acesso a Ordens

- Testamos o acesso a ordens de manutenção por prestadores
- Verificamos se as verificações de permissão funcionam corretamente
- Testamos a atribuição de ordens a prestadores

## Próximos Passos

1. **Atualização Gradual**: Atualizar gradualmente outras partes do código para usar o valor padronizado
2. **Documentação**: Atualizar a documentação para refletir a nomenclatura padronizada
3. **Banco de Dados**: Considerar a atualização dos valores no banco de dados (com cuidado e planejamento adequados)

## Conclusão

Esta implementação padroniza a nomenclatura do perfil de prestador em todo o sistema, mantendo compatibilidade com o código existente. É um passo importante na padronização geral do sistema, que facilitará a manutenção e evolução futura.

A abordagem incremental e cuidadosa garante que o sistema continue funcionando corretamente durante a transição para a nomenclatura padronizada.
