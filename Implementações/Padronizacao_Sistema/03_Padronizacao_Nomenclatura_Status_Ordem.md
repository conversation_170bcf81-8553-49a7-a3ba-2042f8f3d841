# Padronização da Nomenclatura de Status de Ordem

## Problema Identificado

Existia uma inconsistência na nomenclatura e verificação dos status de ordem de manutenção no sistema:

1. O sistema utilizava constantes padronizadas em inglês (`StatusPending`, `StatusInProgress`, etc.) e constantes legadas em português (`StatusPendente`, `StatusEmAndamento`, etc.)
2. Diferentes partes do código usavam diferentes abordagens para verificar status:
   - Comparações diretas com strings literais
   - Comparações com constantes padronizadas
   - Comparações com constantes legadas
3. Algumas partes do código usavam `StatusCanceled` enquanto outras usavam `StatusCancelled` (com dois "l")
4. Não havia uma forma padronizada de verificar se um status pertencia a um conjunto de status válidos

Estas inconsistências dificultavam a manutenção do código e poderiam levar a erros, especialmente em verificações de transição de status.

## Solução Implementada

### 1. Padronização das Constantes de Status

Atualizamos o arquivo `internal/models/maintenance_enums.go` com comentários explicativos sobre as constantes padronizadas e legadas:

```go
// Constantes para status de manutenção
// IMPORTANTE: Estas são as constantes padronizadas para status de ordem.
// Todas as novas implementações devem usar estas constantes em inglês.
const (
    StatusPending    OrderStatus = "pending"     // Ordem pendente, aguardando processamento
    StatusScheduled  OrderStatus = "scheduled"   // Ordem agendada para uma data específica
    StatusInProgress OrderStatus = "in_progress" // Ordem em andamento, sendo executada
    StatusCompleted  OrderStatus = "completed"   // Ordem concluída com sucesso
    StatusCancelled  OrderStatus = "cancelled"   // Ordem cancelada (não será executada)
    StatusVerified   OrderStatus = "verified"    // Ordem verificada após conclusão
    StatusRejected   OrderStatus = "rejected"    // Ordem rejeitada (não aprovada)
    StatusApproved   OrderStatus = "approved"    // Ordem aprovada para execução
)

// Valores legados para compatibilidade
// IMPORTANTE: Estas constantes são mantidas apenas para compatibilidade com código existente.
// Novas implementações NÃO devem usar estas constantes, mas sim as constantes padronizadas em inglês.
const (
    StatusPendente    OrderStatus = "pendente"     // Legado: use StatusPending
    StatusAgendada    OrderStatus = "agendada"     // Legado: use StatusScheduled
    StatusEmAndamento OrderStatus = "em_andamento" // Legado: use StatusInProgress
    StatusConcluida   OrderStatus = "concluida"    // Legado: use StatusCompleted
    StatusCancelada   OrderStatus = "cancelada"    // Legado: use StatusCancelled
    StatusVerificada  OrderStatus = "verificada"   // Legado: use StatusVerified
    StatusRejeitada   OrderStatus = "rejeitada"    // Legado: use StatusRejected
    StatusAprovada    OrderStatus = "aprovada"     // Legado: use StatusApproved
)
```

### 2. Implementação de Funções de Compatibilidade

Adicionamos novas funções de compatibilidade ao arquivo `internal/models/maintenance_status.go`:

```go
// IsOrderStatus verifica se uma string é um status de ordem válido,
// considerando tanto os valores padronizados quanto os valores legados.
// Esta função deve ser usada em vez de comparações diretas com constantes de status
// para garantir compatibilidade com dados existentes.
func IsOrderStatus(status string, validStatus OrderStatus) bool {
    normalizedStatus := NormalizeOrderStatus(status)
    return normalizedStatus == validStatus
}

// IsOrderStatusInSet verifica se uma string é um dos status de ordem válidos no conjunto fornecido,
// considerando tanto os valores padronizados quanto os valores legados.
// Esta função deve ser usada em vez de múltiplas comparações diretas com constantes de status
// para garantir compatibilidade com dados existentes.
func IsOrderStatusInSet(status string, validStatuses ...OrderStatus) bool {
    normalizedStatus := NormalizeOrderStatus(status)
    for _, validStatus := range validStatuses {
        if normalizedStatus == validStatus {
            return true
        }
    }
    return false
}
```

### 3. Atualização das Verificações de Status

Atualizamos as funções que verificam status de ordem para usar as funções de compatibilidade:

#### 3.1. Função `isValidStatus` em `internal/services/maintenance_service.go`:

```go
// isValidStatus verifica se o status é válido
// Usa a função IsOrderStatusInSet para verificar se o status está no conjunto de status válidos
// Esta implementação suporta tanto os valores padronizados quanto os valores legados
func isValidStatus(status string) bool {
    return models.IsOrderStatusInSet(status,
        models.StatusPending,
        models.StatusScheduled,
        models.StatusInProgress,
        models.StatusCompleted,
        models.StatusCancelled, // Nota: Usamos StatusCancelled em vez de StatusCanceled
        models.StatusVerified,
        models.StatusRejected,
        models.StatusApproved,
        models.StatusConfirmed,
    )
}
```

#### 3.2. Função `isValidOrderStatusTransition` em `internal/services/order_service_adapter.go`:

```go
// isValidOrderStatusTransition verifica se a transição de status é válida para ordens de manutenção
// Esta função usa as constantes padronizadas e funções de compatibilidade para verificar transições
func isValidOrderStatusTransition(atual models.OrderStatus, novo models.OrderStatus, _ uint) bool {
    // Normalizar os status para garantir compatibilidade com valores legados
    atualNormalizado := models.NormalizeOrderStatus(string(atual))
    novoNormalizado := models.NormalizeOrderStatus(string(novo))

    // Transições permitidas usando constantes padronizadas
    switch atualNormalizado {
    case models.StatusPending:
        return novoNormalizado == models.StatusInProgress || novoNormalizado == models.StatusCancelled
    case models.StatusInProgress:
        return novoNormalizado == models.StatusCompleted || novoNormalizado == models.StatusCancelled
    case models.StatusCompleted:
        return novoNormalizado == models.StatusInProgress // Reabrir uma ordem concluída
    case models.StatusCancelled:
        return novoNormalizado == models.StatusPending // Reabrir uma ordem cancelada
    default:
        return false
    }
}
```

#### 3.3. Função `isValidStatusTransition` em `internal/services/ordens_service.go`:

```go
// Verifica se a transição de status é válida com base em regras de negócio
// Esta função usa as constantes padronizadas e funções de compatibilidade para verificar transições
func isValidStatusTransition(atual, novo models.StatusOrdem, _ uint) bool {
    // Normalizar os status para garantir compatibilidade com valores legados
    atualNormalizado := models.NormalizeOrderStatus(string(atual))
    novoNormalizado := models.NormalizeOrderStatus(string(novo))

    // Por enquanto, implementa uma versão simplificada do fluxo usando constantes padronizadas
    switch atualNormalizado {
    case models.StatusInProgress:
        // De em atendimento pode ir para pendente ou concluída
        return novoNormalizado == models.StatusPending ||
            novoNormalizado == models.StatusCompleted
    case models.StatusPending:
        // De pendente pode ir para aprovada, rejeitada ou cancelada
        return novoNormalizado == models.StatusApproved ||
            novoNormalizado == models.StatusRejected ||
            novoNormalizado == models.StatusCancelled // Nota: Usamos StatusCancelled em vez de StatusCanceled
    // ... outras transições ...
    }
}
```

## Impacto da Alteração

Esta alteração padroniza a nomenclatura e verificação de status de ordem em todo o sistema, mantendo compatibilidade com o código existente através das funções auxiliares. Isso facilita a manutenção do código e reduz a chance de erros em verificações de status e transições.

### Benefícios

1. **Consistência**: Nomenclatura consistente em todo o sistema
2. **Manutenibilidade**: Código mais fácil de manter e entender
3. **Robustez**: Redução de erros em verificações de status
4. **Compatibilidade**: Mantém compatibilidade com código e dados existentes
5. **Documentação**: Melhor documentação das constantes e seus significados

### Compatibilidade

As funções de compatibilidade garantem que o sistema continue funcionando corretamente com todos os valores possíveis (padronizados e legados), permitindo uma transição gradual para os valores padronizados.

## Testes Realizados

### 1. Verificação de Status

- Testamos a validação de status usando a função `isValidStatus`
- Verificamos se tanto os valores padronizados quanto os valores legados são reconhecidos corretamente

### 2. Verificação de Transições

- Testamos as transições de status usando as funções `isValidOrderStatusTransition` e `isValidStatusTransition`
- Verificamos se as transições válidas são permitidas e as inválidas são bloqueadas

### 3. Normalização de Status

- Testamos a normalização de status usando a função `NormalizeOrderStatus`
- Verificamos se os valores legados são corretamente convertidos para os valores padronizados

## Próximos Passos

1. **Atualização Gradual**: Atualizar gradualmente outras partes do código para usar as constantes padronizadas
2. **Documentação**: Atualizar a documentação para refletir a nomenclatura padronizada
3. **Banco de Dados**: Considerar a atualização dos valores no banco de dados (com cuidado e planejamento adequados)
4. **Testes Automatizados**: Implementar testes automatizados para as funções de compatibilidade

## Conclusão

Esta implementação padroniza a nomenclatura e verificação de status de ordem em todo o sistema, mantendo compatibilidade com o código existente. É um passo importante na padronização geral do sistema, que facilitará a manutenção e evolução futura.

A abordagem incremental e cuidadosa garante que o sistema continue funcionando corretamente durante a transição para a nomenclatura padronizada.
