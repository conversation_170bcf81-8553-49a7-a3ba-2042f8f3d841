# Padronização das Relações entre Entidades

## Problema Identificado

O sistema possuía várias inconsistências nas relações entre entidades:

1. **Nomenclatura Inconsistente de Chaves Estrangeiras**:
   - `branch_id` vs `filial_id` vs `station_id`
   - `technician_id` vs `tecnico_id`
   - `service_provider_id` vs `prestador_id`

2. **Tabelas de Junção com Nomes Inconsistentes**:
   - `technician_branches` vs `provider_branches`
   - `branch_station_links` vs `equipment_tags`

3. **Relações Duplicadas**:
   - Relações entre `Branch` e `Equipment` definidas em múltiplos lugares
   - Relações entre `User` e `MaintenanceOrder` definidas de formas diferentes

4. **Falta de Padronização nas Definições GORM**:
   - Algumas relações usam `foreignKey`
   - Outras usam `references`
   - Algumas não especificam a chave estrangeira

Essas inconsistências dificultavam a manutenção do código, aumentavam a complexidade e podiam levar a erros.

## Solução Implementada

### 1. Criação de um Arquivo de Definição de Relações

Criamos um arquivo `internal/models/entity_relations.go` que contém todas as definições padronizadas de relações entre entidades:

```go
// Constantes para nomes de chaves estrangeiras
const (
    // Chaves estrangeiras relacionadas a Branch
    FKBranchID       = "branch_id"       // Chave estrangeira para Branch
    FKManagerID      = "manager_id"      // Chave estrangeira para gerente (User)
    
    // Chaves estrangeiras relacionadas a User
    FKUserID         = "user_id"         // Chave estrangeira para User
    FKRequesterID    = "requester_id"    // Chave estrangeira para solicitante (User)
    // ... outras constantes ...
)

// Constantes para nomes de tabelas de junção
const (
    // Tabelas de junção relacionadas a Branch
    JoinTableTechnicianBranches = "technician_branches" // Tabela de junção entre técnicos e filiais
    JoinTableProviderBranches   = "provider_branches"   // Tabela de junção entre prestadores e filiais
    // ... outras constantes ...
)

// Definições de relações GORM para Branch
var BranchRelations = map[string]string{
    RelFieldManager:         "foreignKey:" + FKManagerID,
    RelFieldEquipment:       "foreignKey:" + FKBranchID,
    // ... outras definições ...
}

// ... definições para outros modelos ...
```

### 2. Atualização do Modelo Branch

Atualizamos o modelo `Branch` para usar relações padronizadas com outras entidades:

```go
// Relações com outras entidades
// Estas relações seguem o padrão definido em entity_relations.go
Manager           *User               `json:"manager,omitempty" gorm:"foreignKey:ManagerID"`                  // Gerente responsável pela filial
Equipment         []Equipment         `json:"equipment,omitempty" gorm:"foreignKey:BranchID"`                 // Equipamentos da filial
MaintenanceOrders []MaintenanceOrder  `json:"maintenance_orders,omitempty" gorm:"foreignKey:BranchID"`        // Ordens de manutenção da filial
Technicians       []User              `json:"technicians,omitempty" gorm:"many2many:technician_branches"`     // Técnicos associados à filial
ServiceProviders  []ServiceProvider   `json:"service_providers,omitempty" gorm:"many2many:provider_branches"` // Prestadores associados à filial
```

### 3. Atualização do Modelo Equipment

Atualizamos o modelo `Equipment` para usar relações padronizadas com outras entidades:

```go
// Relações com outras entidades
// Estas relações seguem o padrão definido em entity_relations.go
Branch            *Branch            `json:"branch,omitempty" gorm:"foreignKey:BranchID"`                  // Filial à qual o equipamento pertence
MaintenanceOrders []MaintenanceOrder `json:"maintenance_orders,omitempty" gorm:"foreignKey:EquipmentID"`   // Ordens de manutenção do equipamento
Tags              []Tag              `json:"tags,omitempty" gorm:"many2many:equipment_tags"`               // Tags associadas ao equipamento
EquipmentType     *EquipmentType     `json:"equipment_type,omitempty" gorm:"foreignKey:EquipmentTypeID"`   // Tipo padronizado do equipamento
```

### 4. Atualização do Modelo MaintenanceOrder

Atualizamos o modelo `MaintenanceOrder` para usar relações padronizadas com outras entidades:

```go
// Relações com outras entidades
// Estas relações seguem o padrão definido em entity_relations.go
Branch           *Branch           `json:"branch,omitempty" gorm:"foreignKey:BranchID"`                 // Filial à qual a ordem pertence
Equipment        *Equipment        `json:"equipment,omitempty" gorm:"foreignKey:EquipmentID"`           // Equipamento associado à ordem
Requester        *User             `json:"requester,omitempty" gorm:"foreignKey:CreatedByUserID"`       // Usuário que criou a ordem
Technician       *User             `json:"technician,omitempty" gorm:"foreignKey:TechnicianID"`         // Técnico responsável pela ordem
ServiceProvider  *ServiceProvider  `json:"service_provider,omitempty" gorm:"foreignKey:ServiceProviderID"` // Prestador responsável pela ordem
AssignedTo       *User             `json:"assigned_to,omitempty" gorm:"foreignKey:AssignedToUserID"`    // Usuário responsável pela ordem
Materials        []MaintenanceMaterial `json:"materials,omitempty" gorm:"foreignKey:OrderID"`           // Materiais usados na ordem
Notes            []MaintenanceNote `json:"notes,omitempty" gorm:"foreignKey:OrderID"`                   // Notas da ordem
```

### 5. Atualização do Modelo User

Criamos um novo arquivo `internal/models/user.go` com a definição completa do modelo `User` e suas relações padronizadas:

```go
// Relações com outras entidades
// Estas relações seguem o padrão definido em entity_relations.go
Branch              *Branch              `json:"branch,omitempty" gorm:"foreignKey:BranchID"`                  // Filial associada ao usuário
ServiceProvider     *ServiceProvider     `json:"service_provider,omitempty" gorm:"foreignKey:ServiceProviderID"` // Prestador associado ao usuário
ManagedBranches     []*Branch            `json:"managed_branches,omitempty" gorm:"foreignKey:ManagerID"`       // Filiais gerenciadas pelo usuário
RequestedOrders     []*MaintenanceOrder  `json:"requested_orders,omitempty" gorm:"foreignKey:CreatedByUserID"` // Ordens solicitadas pelo usuário
AssignedOrders      []*MaintenanceOrder  `json:"assigned_orders,omitempty" gorm:"foreignKey:AssignedToUserID"` // Ordens atribuídas ao usuário
TechnicianOrders    []*MaintenanceOrder  `json:"technician_orders,omitempty" gorm:"foreignKey:TechnicianID"`   // Ordens atribuídas ao técnico
TechnicianBranches  []*Branch            `json:"technician_branches,omitempty" gorm:"many2many:technician_branches"` // Filiais associadas ao técnico
```

## Impacto da Alteração

Esta alteração padroniza as relações entre entidades em todo o sistema, facilitando a manutenção e evolução do código.

### Benefícios

1. **Consistência**: Nomenclatura consistente para chaves estrangeiras e tabelas de junção
2. **Manutenibilidade**: Código mais fácil de manter e entender
3. **Documentação**: Melhor documentação das relações entre entidades
4. **Centralização**: Definições centralizadas em um único arquivo
5. **Clareza**: Relações claramente definidas e documentadas

### Compatibilidade

As relações padronizadas são compatíveis com o código existente, pois mantêm os mesmos nomes de colunas e tabelas no banco de dados. A mudança afeta apenas a definição dos modelos no código-fonte.

## Próximos Passos

1. **Atualização Gradual**: Atualizar gradualmente outras partes do código para usar as relações padronizadas
2. **Padronização dos Nomes de Tabelas**: Padronizar os nomes das tabelas no banco de dados
3. **Centralização da Lógica de Atribuição**: Centralizar a lógica de atribuição de ordens de manutenção

## Conclusão

Esta implementação padroniza as relações entre entidades em todo o sistema, mantendo compatibilidade com o código existente. É um passo importante na padronização geral do sistema, que facilitará a manutenção e evolução futura.

A abordagem incremental e cuidadosa garante que o sistema continue funcionando corretamente durante a transição para as relações padronizadas.
