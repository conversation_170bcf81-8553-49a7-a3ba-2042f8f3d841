# Padronização da Nomenclatura de Perfil de Técnico

## Problema Identificado

Existia uma inconsistência na nomenclatura utilizada para representar o perfil de técnico no sistema:

1. A constante `RoleTechnician` em `internal/models/models.go` usava o valor `"tecnico"` (em português, sem acento)
2. Em algumas partes do código, era usado `"technician"` (em inglês)
3. O arquivo de permissões `data/permissions.yaml` usava `tecnico` como chave para o perfil
4. Diferentes verificações de perfil usavam comparações diretas com ambos os valores

Esta inconsistência dificultava a manutenção do código e poderia levar a erros, especialmente em verificações de permissão.

## Solução Implementada

### 1. Padronização da Constante `RoleTechnician`

Atualizamos a constante `RoleTechnician` em `internal/models/models.go` para usar o valor padronizado `"technician"` (em inglês):

```go
RoleTechnician UserRole = "technician" // Constante padronizada para técnicos
// IMPORTANTE: Anteriormente esta constante usava "tecnico". Foi alterada para "technician"
// para padronização da nomenclatura. O código foi adaptado para manter compatibilidade.
```

### 2. Criação de Funções de Compatibilidade

Criamos um novo arquivo `internal/models/role_compatibility.go` com funções auxiliares para garantir compatibilidade com o código existente:

```go
// IsTechnician verifica se um perfil de usuário é um técnico,
// considerando tanto o valor padronizado "technician" quanto o valor legado "tecnico".
func IsTechnician(role string) bool {
    return role == string(RoleTechnician) || role == "tecnico"
}

// NormalizeRole normaliza um perfil de usuário para o valor padronizado.
func NormalizeRole(role string) string {
    switch role {
    case "tecnico":
        return string(RoleTechnician)
    default:
        return role
    }
}

// GetRoleConstant retorna a constante UserRole correspondente a uma string de perfil.
func GetRoleConstant(role string) UserRole {
    // Implementação...
}
```

### 3. Atualização do Arquivo de Permissões

Atualizamos o arquivo `data/permissions.yaml` para usar a chave padronizada `technician` em vez de `tecnico`:

```yaml
technician:
    description: "Técnico de Manutenção"
    # Nota: Anteriormente este perfil era "tecnico", foi alterado para "technician"
    # para padronização da nomenclatura. O sistema mantém compatibilidade com ambos.
    pages:
        # ...
```

### 4. Atualização dos Middlewares e Serviços

Atualizamos os middlewares e serviços que verificam o perfil de técnico para usar as funções de compatibilidade:

- `internal/middleware/ordem_tecnico_middleware.go`:
  ```go
  // Usando a função de compatibilidade para verificar se é um técnico
  if models.IsTechnician(userRoleStr) {
      // ...
  }
  ```

- `internal/services/filial_filter_service.go`:
  ```go
  // Usando a função de compatibilidade para verificar se é um técnico
  if models.IsTechnician(userRole) {
      // ...
  }
  ```

- `internal/services/filial_permission_service.go`:
  ```go
  // Usando a função de compatibilidade para verificar se é um técnico
  if models.IsTechnician(userRole) {
      // ...
  }
  ```

## Impacto da Alteração

Esta alteração padroniza a nomenclatura do perfil de técnico em todo o sistema, mantendo compatibilidade com o código existente através das funções auxiliares. Isso facilita a manutenção do código e reduz a chance de erros em verificações de permissão.

### Benefícios

1. **Consistência**: Nomenclatura consistente em todo o sistema
2. **Manutenibilidade**: Código mais fácil de manter e entender
3. **Robustez**: Redução de erros em verificações de permissão
4. **Compatibilidade**: Mantém compatibilidade com código e dados existentes

### Compatibilidade

As funções de compatibilidade garantem que o sistema continue funcionando corretamente com ambos os valores (`"technician"` e `"tecnico"`), permitindo uma transição gradual para o valor padronizado.

## Testes Realizados

### 1. Verificação de Permissões

- Testamos o login e acesso de usuários com perfil de técnico
- Verificamos se as permissões baseadas em perfil continuam funcionando corretamente
- Testamos o acesso a páginas e APIs restritas a técnicos

### 2. Filtragem de Filiais

- Testamos a filtragem de filiais para usuários com perfil de técnico
- Verificamos se as filiais atribuídas são corretamente retornadas
- Testamos o caso especial do usuário `<EMAIL>`

### 3. Acesso a Ordens

- Testamos o acesso a ordens de manutenção por técnicos
- Verificamos se as verificações de permissão funcionam corretamente
- Testamos a atribuição de ordens a técnicos

## Próximos Passos

1. **Atualização Gradual**: Atualizar gradualmente outras partes do código para usar o valor padronizado
2. **Documentação**: Atualizar a documentação para refletir a nomenclatura padronizada
3. **Banco de Dados**: Considerar a atualização dos valores no banco de dados (com cuidado e planejamento adequados)

## Conclusão

Esta implementação padroniza a nomenclatura do perfil de técnico em todo o sistema, mantendo compatibilidade com o código existente. É um primeiro passo importante na padronização geral do sistema, que facilitará a manutenção e evolução futura.

A abordagem incremental e cuidadosa garante que o sistema continue funcionando corretamente durante a transição para a nomenclatura padronizada.
