# Padronização do Sistema de Gestão Tradição

Este diretório contém a documentação e implementação das padronizações realizadas no Sistema de Gestão Tradição, com o objetivo de melhorar a consistência, manutenibilidade e robustez do código.

## Visão Geral

O Sistema de Gestão Tradição evoluiu organicamente ao longo do tempo, resultando em algumas inconsistências na nomenclatura, duplicação de modelos e múltiplas implementações para a mesma funcionalidade. Este projeto de padronização visa resolver esses problemas de forma incremental, mantendo a compatibilidade com o código existente.

## Implementações Realizadas

### 1. [Padronização da Nomenclatura de Perfil de Técnico](01_Padronizacao_Nomenclatura_Tecnico.md)

- Padronização da constante `RoleTechnician` para usar o valor `"technician"` (em inglês)
- Criação de funções de compatibilidade para garantir compatibilidade com o código existente
- Atualização do arquivo de permissões para usar a chave padronizada
- Atualização dos middlewares e serviços que verificam o perfil de técnico

### 2. [Padronização da Nomenclatura de Perfil de Prestador](02_Padronizacao_Nomenclatura_Prestador.md)

- Padronização da constante `RolePrestador` para usar o valor `"provider"` (em inglês)
- Expansão das funções de compatibilidade para lidar com múltiplos valores legados (`"prestadores"` e `"prestador"`)
- Atualização do arquivo de permissões para usar a chave padronizada
- Atualização dos middlewares e serviços que verificam o perfil de prestador

### 3. [Padronização da Nomenclatura de Status de Ordem](03_Padronizacao_Nomenclatura_Status_Ordem.md)

- Padronização das constantes de status de ordem com comentários explicativos
- Implementação de funções de compatibilidade para verificar status e conjuntos de status
- Atualização das funções que verificam status e transições de status
- Padronização para usar `StatusCancelled` (com dois "l") em todo o sistema

### 4. [Consolidação dos Modelos de Filial](04_Consolidacao_Modelos_Filial.md)

- Criação de um arquivo de compatibilidade para modelos de filial
- Atualização do modelo `Branch` como modelo principal padronizado
- Adição de métodos para inicializar campos virtuais
- Atualização dos métodos de conversão para usar funções de compatibilidade
- Atualização dos métodos de resumo para usar o modelo `Branch` como modelo principal

### 5. [Padronização das Relações entre Entidades](05_Padronizacao_Relacoes_Entidades.md)

- Criação de um arquivo de definição de relações entre entidades
- Padronização das constantes para nomes de chaves estrangeiras e tabelas de junção
- Atualização dos modelos `Branch`, `Equipment`, `MaintenanceOrder` e `User` para usar relações padronizadas
- Documentação clara das relações entre entidades
- Centralização das definições de relações em um único arquivo

### 6. [Padronização dos Nomes de Tabelas](06_Padronizacao_Nomes_Tabelas.md)

- Criação de um arquivo de definição de nomes de tabelas
- Padronização das constantes para nomes de tabelas
- Atualização dos métodos `TableName()` em todos os modelos para usar as constantes definidas
- Funções auxiliares para obter nomes de tabelas e normalizar nomes de tabelas legadas
- Centralização das definições de nomes de tabelas em um único arquivo

### 7. [Padronização do Sistema de Atribuição de Ordens](07_Padronizacao_Sistema_Atribuicao_Ordens.md)

- Criação de um serviço centralizado para gerenciar a atribuição de ordens
- Padronização do modelo `TechnicianOrder` para usar as constantes de nomes de tabelas
- Criação de um novo middleware de permissões que usa o serviço centralizado
- Criação de novos handlers de atribuição que usam o serviço centralizado
- Configuração de novas rotas para usar os novos handlers

### 8. [Consolidação do Sistema de Permissões](08_Consolidacao_Sistema_Permissoes.md)

- Criação de um serviço unificado para gerenciar todas as permissões
- Criação de um middleware unificado para verificar permissões
- Atualização do sistema global de permissões para incluir o serviço e middleware unificados
- Atualização dos middlewares existentes para usar o serviço unificado
- Implementação de fallbacks para garantir compatibilidade com o código existente

### 9. [Implementação da Auditoria de Permissões](09_Implementacao_Auditoria_Permissoes.md)

- Criação de um serviço de auditoria de permissões
- Implementação de configurações para controlar o comportamento da auditoria
- Integração com o serviço unificado de permissões
- Atualização dos middlewares para passar informações adicionais para o serviço de auditoria
- Criação de endpoints para consulta de logs de auditoria

## Princípios de Implementação

Todas as implementações seguem os seguintes princípios:

1. **Compatibilidade**: Manter compatibilidade com o código existente
2. **Incrementalidade**: Implementar mudanças de forma incremental
3. **Testabilidade**: Garantir que cada mudança seja testável
4. **Documentação**: Documentar detalhadamente cada mudança

## Plano de Padronização

O plano de padronização inclui as seguintes fases:

### Fase 1: Padronização de Nomenclatura e Constantes

- ✅ Padronização da nomenclatura de perfil de técnico
- ✅ Padronização da nomenclatura de perfil de prestador
- ✅ Padronização da nomenclatura de status de ordem
- ⬜ Padronização de outras constantes de perfil

### Fase 2: Consolidação de Modelos

- ✅ Consolidação dos modelos de filial
- ✅ Padronização das relações entre entidades
- ✅ Padronização dos nomes de tabelas

### Fase 3: Padronização do Sistema de Atribuição de Ordens

- ✅ Centralização da lógica de atribuição
- ✅ Padronização do fluxo de atribuição
- ✅ Remoção de verificações hardcoded

### Fase 4: Consolidação do Sistema de Permissões

- ✅ Padronização dos middlewares de permissão
- ✅ Expansão do serviço de permissões centralizado
- ✅ Implementação de auditoria de permissões

## Como Contribuir

Para contribuir com o projeto de padronização:

1. Identifique inconsistências no código
2. Proponha uma solução que mantenha compatibilidade com o código existente
3. Implemente a solução de forma incremental
4. Teste exaustivamente a solução
5. Documente a implementação seguindo o padrão existente

## Testes

Cada implementação deve ser testada exaustivamente para garantir que não quebra a funcionalidade existente. Os testes devem incluir:

1. **Testes Unitários**: Testar componentes individuais
2. **Testes de Integração**: Testar a interação entre componentes
3. **Testes de Aceitação**: Testar o comportamento do sistema como um todo

## Documentação

Cada implementação deve ser documentada detalhadamente, incluindo:

1. **Problema**: Descrição do problema a ser resolvido
2. **Solução**: Descrição da solução implementada
3. **Impacto**: Avaliação do impacto da solução no sistema
4. **Testes**: Descrição dos testes realizados
5. **Próximos Passos**: Sugestões para trabalhos futuros
