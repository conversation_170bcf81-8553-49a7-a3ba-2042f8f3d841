# Implementação da Auditoria de Permissões

## Problema Identificado

O sistema já possuía um modelo `AuditLog` para registrar ações dos usuários, mas não havia uma auditoria específica para verificações de permissões. Isso é importante para:

1. **Rastreabilidade**: Rastrear tentativas de acesso não autorizado
2. **Segurança**: Identificar possíveis vulnerabilidades de segurança
3. **Conformidade**: Cumprir requisitos de conformidade
4. **Investigação**: Facilitar a investigação de incidentes de segurança

A falta de auditoria de permissões dificultava a identificação de tentativas de acesso não autorizado e a investigação de incidentes de segurança.

## Solução Implementada

### 1. Criação de um Serviço de Auditoria de Permissões

Criamos um serviço dedicado para auditoria de permissões:

```go
// PermissionAuditService é o serviço de auditoria de permissões
type PermissionAuditService struct {
    db     *gorm.DB
    config AuditConfig
}
```

O serviço inclui métodos para:

- `LogPermissionCheck`: Registrar uma verificação de permissão
- `LogPageAccess`: Registrar um acesso a uma página
- `LogAPIAccess`: Registrar um acesso a uma API
- `GetAuditLogs`: Obter logs de auditoria
- `SetConfig`: Definir a configuração da auditoria
- `GetConfig`: Obter a configuração atual da auditoria

### 2. Implementação de Configurações de Auditoria

Implementamos configurações para controlar o comportamento da auditoria:

```go
// AuditLevel define o nível de detalhe da auditoria
type AuditLevel string

const (
    // AuditLevelNone desativa a auditoria
    AuditLevelNone AuditLevel = "none"
    // AuditLevelMinimal registra apenas tentativas de acesso negadas
    AuditLevelMinimal AuditLevel = "minimal"
    // AuditLevelBasic registra tentativas de acesso negadas e acessos a recursos sensíveis
    AuditLevelBasic AuditLevel = "basic"
    // AuditLevelFull registra todas as verificações de permissão
    AuditLevelFull AuditLevel = "full"
)

// AuditConfig define as configurações da auditoria de permissões
type AuditConfig struct {
    // Enabled indica se a auditoria está ativada
    Enabled bool `json:"enabled"`
    // Level define o nível de detalhe da auditoria
    Level AuditLevel `json:"level"`
    // LogSuccessfulChecks indica se verificações bem-sucedidas devem ser registradas
    LogSuccessfulChecks bool `json:"log_successful_checks"`
    // LogFailedChecks indica se verificações falhas devem ser registradas
    LogFailedChecks bool `json:"log_failed_checks"`
    // SensitiveResources define recursos considerados sensíveis
    SensitiveResources []string `json:"sensitive_resources"`
}
```

### 3. Integração com o Serviço Unificado de Permissões

Atualizamos o serviço unificado de permissões para usar o serviço de auditoria:

```go
// UnifiedPermissionService é o serviço centralizado para gerenciar todas as permissões
type UnifiedPermissionService struct {
    db                *gorm.DB
    service           *Service
    mutex             sync.RWMutex
    orderAssignmentSvc OrderAssignmentServiceInterface
    auditService      *PermissionAuditService
}
```

Atualizamos os métodos do serviço unificado para registrar verificações de permissão:

```go
// HasPagePermission verifica se um usuário tem permissão para acessar uma página
func (s *UnifiedPermissionService) HasPagePermission(userID uint, userRole string, pagePath string, ip string, userAgent string) bool {
    // Usar o serviço de permissões existente
    allowed := s.service.HasPermission(userRole, pagePath, PagePermission)
    
    // Registrar na auditoria
    if s.auditService != nil {
        s.auditService.LogPageAccess(userID, userRole, pagePath, allowed, ip, userAgent)
    }
    
    return allowed
}
```

### 4. Atualização dos Middlewares

Atualizamos os middlewares para passar informações adicionais para o serviço de auditoria:

```go
// Obter IP e User-Agent
ip := c.ClientIP()
userAgent := c.Request.UserAgent()

// Verifica se o usuário tem permissão para acessar a página
if !m.service.HasPagePermission(userID, userRole.(string), path, ip, userAgent) {
    // ...
}
```

### 5. Criação de Endpoints para Consulta de Logs de Auditoria

Criamos endpoints para consultar os logs de auditoria:

```go
// AuditLogHandler é o handler para consulta de logs de auditoria
type AuditLogHandler struct {
    auditService *permissions.PermissionAuditService
}

// GetAuditLogs retorna os logs de auditoria
func (h *AuditLogHandler) GetAuditLogs(c *gin.Context) {
    // ...
}

// GetAuditConfig retorna a configuração atual da auditoria
func (h *AuditLogHandler) GetAuditConfig(c *gin.Context) {
    // ...
}

// UpdateAuditConfig atualiza a configuração da auditoria
func (h *AuditLogHandler) UpdateAuditConfig(c *gin.Context) {
    // ...
}
```

Configuramos rotas para os endpoints:

```go
// SetupAuditLogRoutes configura as rotas para consulta de logs de auditoria
func SetupAuditLogRoutes(router *gin.Engine, authMiddleware gin.HandlerFunc) {
    // ...
    
    // Grupo de rotas para a API de logs de auditoria
    apiAuditLogs := router.Group("/api/audit-logs")
    apiAuditLogs.Use(authMiddleware)
    
    // Apenas administradores podem acessar logs de auditoria
    apiAuditLogs.Use(middleware.RoleMiddleware(models.RoleAdmin))
    {
        // Obter logs de auditoria
        apiAuditLogs.GET("", auditLogHandler.GetAuditLogs)
        
        // Obter configuração da auditoria
        apiAuditLogs.GET("/config", auditLogHandler.GetAuditConfig)
        
        // Atualizar configuração da auditoria
        apiAuditLogs.PUT("/config", auditLogHandler.UpdateAuditConfig)
    }
}
```

## Impacto da Alteração

Esta alteração implementa um sistema de auditoria de permissões que:

1. **Registra Verificações de Permissão**: Registra todas as verificações de permissão, incluindo tentativas de acesso negadas
2. **Configurável**: Permite configurar o nível de detalhe da auditoria
3. **Consulta de Logs**: Fornece endpoints para consultar os logs de auditoria
4. **Segurança**: Melhora a segurança do sistema, permitindo identificar tentativas de acesso não autorizado

### Benefícios

1. **Rastreabilidade**: Permite rastrear tentativas de acesso não autorizado
2. **Segurança**: Ajuda a identificar possíveis vulnerabilidades de segurança
3. **Conformidade**: Ajuda a cumprir requisitos de conformidade
4. **Investigação**: Facilita a investigação de incidentes de segurança
5. **Configurabilidade**: Permite configurar o nível de detalhe da auditoria

### Compatibilidade

O sistema de auditoria de permissões é compatível com o código existente, pois:

1. Utiliza o modelo `AuditLog` existente
2. Não altera o comportamento das verificações de permissão
3. Implementa fallbacks para garantir que o sistema continue funcionando durante a transição

## Próximos Passos

1. **Implementação de Dashboards**: Criar dashboards para visualização dos logs de auditoria
2. **Alertas**: Implementar alertas para tentativas de acesso não autorizado
3. **Relatórios**: Implementar relatórios de auditoria
4. **Retenção de Logs**: Implementar políticas de retenção de logs

## Conclusão

Esta implementação completa a Fase 4 do plano de padronização do sistema, que incluía a padronização dos middlewares de permissão, a expansão do serviço de permissões centralizado e a implementação da auditoria de permissões.

A abordagem incremental e cuidadosa garante que o sistema continue funcionando corretamente durante a transição para o novo sistema de auditoria de permissões.
