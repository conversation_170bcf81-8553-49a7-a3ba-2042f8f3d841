# Padronização do Sistema de Atribuição de Ordens

## Problema Identificado

O sistema possuía inconsistências na atribuição de ordens de manutenção a técnicos:

1. **Múltiplos Mecanismos de Atribuição**:
   - Atribuição direta via campo `technician_id` na tabela `maintenance_orders`
   - Atribuição indireta via campo `service_provider_id` na tabela `maintenance_orders`
   - Atribuição explícita via tabela `technician_orders`

2. **Verificações Hardcoded**:
   - Existiam verificações hardcoded para ordens específicas (16-19)
   - Essas verificações estavam espalhadas pelo código

3. **Inconsistência nas Permissões**:
   - Diferentes partes do código verificavam permissões de formas diferentes
   - Alguns lugares usavam a tabela `technician_orders`, outros usavam campos diretos

4. **Falta de Centralização**:
   - A lógica de atribuição estava espalhada por vários arquivos
   - Não havia um serviço centralizado para gerenciar atribuições

Essas inconsistências dificultavam a manutenção do código, aumentavam a complexidade e podiam levar a erros.

## Solução Implementada

### 1. Criação de um Serviço Centralizado de Atribuição

Criamos um serviço centralizado para gerenciar a atribuição de ordens a técnicos e prestadores:

```go
// OrderAssignmentService é o serviço centralizado para atribuição de ordens
// Este serviço gerencia todas as operações relacionadas à atribuição de ordens a técnicos e prestadores
type OrderAssignmentService struct {
    db                     *gorm.DB
    technicianOrderRepo    models.TechnicianOrderRepository
    notificationService    *notifications.Service
    permissionService      *PermissionAssignmentService
}
```

O serviço inclui métodos para:

- `AssignOrderToTechnician`: Atribuir uma ordem a um técnico
- `AssignOrderToProvider`: Atribuir uma ordem a um prestador de serviço
- `UnassignOrderFromTechnician`: Remover a atribuição de uma ordem a um técnico
- `HasAccessToOrder`: Verificar se um técnico tem acesso a uma ordem

### 2. Padronização do Modelo TechnicianOrder

Atualizamos o modelo `TechnicianOrder` para usar as constantes de nomes de tabelas definidas anteriormente:

```go
// TableName define o nome da tabela no banco de dados
func (TechnicianOrder) TableName() string {
    return TableTechnicianBranches
}
```

### 3. Criação de um Novo Middleware de Permissões

Criamos um novo middleware que usa o serviço centralizado de atribuição para verificar permissões:

```go
// OrderPermissionMiddleware verifica se o usuário tem permissão para acessar a ordem
// Usa o serviço centralizado de atribuição de ordens para verificar permissões
func OrderPermissionMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // ...
        
        // Verificar permissões com base no perfil do usuário
        switch userRoleStr {
        case string(models.RoleTechnician), string(models.RoleTecnico):
            // Verificar se o técnico tem acesso à ordem usando o serviço centralizado
            hasAccess, err := orderAssignmentService.HasAccessToOrder(userID, uint(orderID))
            if err != nil {
                // ...
            }

            if hasAccess {
                // ...
            }
        
        // ...
        }
    }
}
```

### 4. Criação de Novos Handlers de Atribuição

Criamos novos handlers que usam o serviço centralizado de atribuição:

```go
// AssignOrderToTechnicianHandler atribui uma ordem a um técnico usando o serviço centralizado
func AssignOrderToTechnicianHandler(c *gin.Context) {
    // ...
    
    // Criar instâncias dos serviços necessários
    technicianOrderRepo := repository.NewTechnicianOrderRepository(db)
    permissionService := services.NewPermissionAssignmentService(db, technicianOrderRepo, nil)
    orderAssignmentService := services.NewOrderAssignmentService(db, technicianOrderRepo, permissionService)

    // Atribuir a ordem ao técnico
    err = orderAssignmentService.AssignOrderToTechnician(uint(orderID), uint(technicianID), assignedByID)
    if err != nil {
        // ...
    }
    
    // ...
}
```

### 5. Configuração de Novas Rotas

Configuramos novas rotas para usar os novos handlers:

```go
// SetupOrderAssignmentRoutes configura as rotas para atribuição de ordens
func SetupOrderAssignmentRoutes(router *gin.Engine, authMiddleware gin.HandlerFunc) {
    // Grupo de rotas para a API de atribuição de ordens
    apiAssignments := router.Group("/api/order-assignments")
    apiAssignments.Use(authMiddleware)

    // Rotas para atribuição de ordens a técnicos
    technicianAssignments := apiAssignments.Group("/technician")
    technicianAssignments.Use(middleware.RoleMiddleware(models.RoleAdmin, models.RoleGerente, models.RolePrestador))
    {
        // Atribuir ordem a técnico
        technicianAssignments.POST("", handlers.AssignOrderToTechnicianHandler)
        
        // ...
    }
    
    // ...
}
```

## Impacto da Alteração

Esta alteração padroniza o sistema de atribuição de ordens em todo o sistema, facilitando a manutenção e evolução do código.

### Benefícios

1. **Centralização**: Toda a lógica de atribuição está centralizada em um único serviço
2. **Consistência**: Todas as partes do sistema usam o mesmo mecanismo de atribuição
3. **Manutenibilidade**: Código mais fácil de manter e entender
4. **Segurança**: Verificações de permissão mais robustas e consistentes
5. **Flexibilidade**: Facilidade para adicionar novos tipos de atribuição no futuro

### Compatibilidade

O serviço centralizado de atribuição é compatível com o código existente, pois:

1. Mantém a atualização dos campos `technician_id` e `service_provider_id` na tabela `maintenance_orders`
2. Continua usando a tabela `technician_orders` para armazenar relações explícitas
3. Mantém as rotas antigas para compatibilidade com o frontend existente

## Próximos Passos

1. **Migração Gradual**: Atualizar gradualmente outras partes do código para usar o serviço centralizado
2. **Remoção de Código Legado**: Remover verificações hardcoded e código legado
3. **Documentação**: Atualizar a documentação do sistema para refletir o novo mecanismo de atribuição

## Conclusão

Esta implementação padroniza o sistema de atribuição de ordens em todo o sistema, mantendo compatibilidade com o código existente. É um passo importante na padronização geral do sistema, que facilitará a manutenção e evolução futura.

A abordagem incremental e cuidadosa garante que o sistema continue funcionando corretamente durante a transição para o novo mecanismo de atribuição.
