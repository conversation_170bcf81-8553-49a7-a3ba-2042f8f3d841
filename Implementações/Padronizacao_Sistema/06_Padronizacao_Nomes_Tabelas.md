# Padronização dos Nomes de Tabelas

## Problema Identificado

O sistema possuía inconsistências nos nomes das tabelas no banco de dados:

1. **Duplicidade de Tabelas**:
   - `branches` e `stations` para representar filiais
   - `usuarios` e `users` para representar usuários

2. **Mistura de Idiomas**:
   - Algumas tabelas com nomes em inglês (`branches`, `users`, `equipment`)
   - Outras tabelas com nomes em português (`filiais`, `usuarios`, `equipamentos`)

3. **Inconsistência na Pluralização**:
   - Algumas tabelas no plural (`users`, `branches`)
   - Outras tabelas no singular (`equipment` em vez de `equipments`)

4. **Tabelas de Junção com Nomes Inconsistentes**:
   - `technician_branches` vs `provider_branches`
   - `branch_station_links` vs `equipment_tags`

Essas inconsistências dificultavam a manutenção do código, aumentavam a complexidade e podiam levar a erros.

## Solução Implementada

### 1. Criação de um Arquivo de Definição de Tabelas

Criamos um arquivo `internal/models/table_names.go` que contém todas as definições padronizadas de nomes de tabelas:

```go
// Constantes para nomes de tabelas principais
const (
    // Tabelas principais
    TableUsers             = "users"              // Tabela de usuários
    TableBranches          = "branches"           // Tabela de filiais
    TableEquipment         = "equipment"          // Tabela de equipamentos
    TableMaintenanceOrders = "maintenance_orders" // Tabela de ordens de manutenção
    TableServiceProviders  = "service_providers"  // Tabela de prestadores de serviço
    // ... outras constantes ...
)

// Constantes para nomes de tabelas legadas (para compatibilidade)
const (
    // Tabelas legadas em português
    TableUsuarios         = "usuarios"          // Tabela legada de usuários
    TableFiliais          = "filiais"           // Tabela legada de filiais
    TableEquipamentos     = "equipamentos"      // Tabela legada de equipamentos
    TableOrdensServico    = "ordens_servico"    // Tabela legada de ordens de serviço
    TablePrestadores      = "prestadores"       // Tabela legada de prestadores de serviço
    // ... outras constantes ...
)
```

O arquivo também inclui funções auxiliares para obter nomes de tabelas e normalizar nomes de tabelas legadas:

```go
// GetTableName retorna o nome padronizado da tabela para um modelo específico
func GetTableName(modelName string) string {
    switch modelName {
    case "User":
        return TableUsers
    case "Branch":
        return TableBranches
    // ... outros casos ...
    default:
        return ""
    }
}

// NormalizeTableName normaliza o nome da tabela para o formato padronizado
func NormalizeTableName(tableName string) string {
    switch tableName {
    case TableUsuarios:
        return TableUsers
    case TableFiliais, TableStations:
        return TableBranches
    // ... outros casos ...
    default:
        return tableName
    }
}
```

### 2. Atualização dos Métodos TableName nos Modelos

Atualizamos os métodos `TableName()` em todos os modelos para usar as constantes definidas no arquivo de definição de tabelas:

#### Modelo Branch

```go
// TableName especifica o nome da tabela para o modelo Branch
func (Branch) TableName() string {
    return TableBranches
}
```

#### Modelo User

```go
// TableName especifica o nome da tabela para o modelo User
func (User) TableName() string {
    return TableUsers
}
```

#### Modelo Equipment

```go
// TableName especifica o nome da tabela para o modelo Equipment
func (Equipment) TableName() string {
    return TableEquipment
}
```

#### Modelo MaintenanceOrder

```go
// TableName retorna o nome da tabela no banco de dados
func (MaintenanceOrder) TableName() string {
    return TableMaintenanceOrders
}
```

#### Modelo EquipmentType

```go
// TableName retorna o nome da tabela no banco de dados
func (EquipmentType) TableName() string {
    return TableEquipmentTypes
}
```

#### Modelo ServiceProvider

```go
// TableName retorna o nome da tabela no banco de dados
func (ServiceProvider) TableName() string {
    return TableServiceProviders
}
```

#### Modelo ProviderBranch

```go
// TableName retorna o nome da tabela no banco de dados
func (ProviderBranch) TableName() string {
    return TableProviderBranches
}
```

## Impacto da Alteração

Esta alteração padroniza os nomes das tabelas em todo o sistema, facilitando a manutenção e evolução do código.

### Benefícios

1. **Consistência**: Nomenclatura consistente para tabelas em todo o sistema
2. **Manutenibilidade**: Código mais fácil de manter e entender
3. **Centralização**: Definições centralizadas em um único arquivo
4. **Flexibilidade**: Facilidade para alterar nomes de tabelas no futuro
5. **Documentação**: Melhor documentação dos nomes de tabelas

### Compatibilidade

As constantes de nomes de tabelas são compatíveis com o código existente, pois mantêm os mesmos nomes de tabelas no banco de dados. A mudança afeta apenas a definição dos modelos no código-fonte.

## Próximos Passos

1. **Atualização Gradual**: Atualizar gradualmente outras partes do código para usar as constantes de nomes de tabelas
2. **Migrações de Banco de Dados**: Criar migrações para renomear tabelas legadas para os nomes padronizados
3. **Documentação**: Atualizar a documentação do banco de dados para refletir os nomes padronizados

## Conclusão

Esta implementação padroniza os nomes das tabelas em todo o sistema, mantendo compatibilidade com o código existente. É um passo importante na padronização geral do sistema, que facilitará a manutenção e evolução futura.

A abordagem incremental e cuidadosa garante que o sistema continue funcionando corretamente durante a transição para os nomes de tabelas padronizados.
