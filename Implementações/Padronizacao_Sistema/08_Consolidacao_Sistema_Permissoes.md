# Consolidação do Sistema de Permissões

## Problema Identificado

O sistema possuía inconsistências no sistema de permissões:

1. **Múltiplos Middlewares de Permissão**:
   - `VerificarPermissaoOrdem` para verificar permissões de ordens
   - `PageAccessMiddleware` para verificar permissões de páginas
   - `APIAccessMiddleware` para verificar permissões de APIs
   - `ResourcePermissionMiddleware` para verificar permissões de recursos

2. **Verificações Hardcoded**:
   - Algumas verificações de permissão estavam hardcoded em handlers
   - Outras estavam espalhadas pelo código

3. **Inconsistência nas Permissões de Recursos**:
   - Diferentes partes do código verificavam permissões de recursos de formas diferentes
   - Alguns lugares usavam o serviço centralizado, outros usavam verificações diretas

4. **Falta de Centralização**:
   - A lógica de verificação de permissões estava espalhada por vários arquivos
   - Não havia um serviço centralizado para gerenciar todas as permissões

Essas inconsistências dificultavam a manutenção do código, aumentavam a complexidade e podiam levar a erros.

## Solução Implementada

### 1. Criação de um Serviço Unificado de Permissões

Criamos um serviço unificado para gerenciar todas as permissões:

```go
// UnifiedPermissionService é o serviço centralizado para gerenciar todas as permissões
// Este serviço unifica as verificações de permissão para páginas, APIs e recursos
type UnifiedPermissionService struct {
    db                *gorm.DB
    service           *Service
    mutex             sync.RWMutex
    orderAssignmentSvc OrderAssignmentServiceInterface
}
```

O serviço inclui métodos para:

- `HasPagePermission`: Verificar permissões de páginas
- `HasAPIPermission`: Verificar permissões de APIs
- `HasResourcePermission`: Verificar permissões de recursos específicos
- `GetPermittedRoles`: Obter papéis permitidos para um recurso

### 2. Criação de um Middleware Unificado

Criamos um middleware unificado para verificar permissões:

```go
// UnifiedMiddleware é o middleware unificado para verificação de permissões
type UnifiedMiddleware struct {
    service *UnifiedPermissionService
}
```

O middleware inclui métodos para:

- `PageAccessMiddleware`: Verificar permissões de páginas
- `APIAccessMiddleware`: Verificar permissões de APIs
- `ResourcePermissionMiddleware`: Verificar permissões de recursos específicos
- `RoleMiddleware`: Verificar se o usuário tem um dos papéis especificados

### 3. Atualização do Sistema Global de Permissões

Atualizamos o sistema global de permissões para incluir o serviço e middleware unificados:

```go
// Variáveis globais para armazenar os serviços e middlewares de permissões
var (
    globalPermissionsMiddleware     *Middleware
    globalUnifiedPermissionService  *UnifiedPermissionService
    globalUnifiedPermissionMiddleware *UnifiedMiddleware
    globalMutex                     sync.RWMutex
)
```

Implementamos funções para:

- `SetGlobalUnifiedService`: Definir o serviço unificado global
- `GetGlobalUnifiedService`: Obter o serviço unificado global
- `SetGlobalUnifiedMiddleware`: Definir o middleware unificado global
- `GetGlobalUnifiedMiddleware`: Obter o middleware unificado global
- `InitializeGlobalPermissions`: Inicializar o sistema global de permissões
- `UpdateGlobalPermissions`: Atualizar o sistema global de permissões

### 4. Atualização dos Middlewares Existentes

Atualizamos os middlewares existentes para usar o serviço unificado:

#### PageAccessMiddleware

```go
func PageAccessMiddleware_Internal() gin.HandlerFunc {
    // Usar o novo sistema de permissões unificado
    unifiedMiddleware := permissions.GetGlobalUnifiedMiddleware()
    if unifiedMiddleware != nil {
        // Usar o novo middleware unificado de permissões
        return unifiedMiddleware.PageAccessMiddleware()
    }
    
    // Fallback para o sistema antigo
    middleware := permissions.GetGlobalMiddleware()
    if middleware != nil {
        // Usar o middleware de permissões existente
        return middleware.PageAccessMiddleware()
    }

    // Fallback para o sistema legado se nenhum dos dois estiver disponível
    log.Println("AVISO: Middleware global de permissões não configurado, usando implementação legada")
}
```

#### APIAccessMiddleware

```go
func APIAccessMiddleware() gin.HandlerFunc {
    // Usar o novo sistema de permissões unificado
    unifiedMiddleware := permissions.GetGlobalUnifiedMiddleware()
    if unifiedMiddleware != nil {
        // Usar o novo middleware unificado de permissões
        return unifiedMiddleware.APIAccessMiddleware()
    }
    
    // Fallback para o sistema antigo
    middleware := permissions.GetGlobalMiddleware()
    if middleware == nil {
        log.Println("ERRO: Middleware de permissões não configurado")
        return func(c *gin.Context) {
            c.JSON(http.StatusInternalServerError, gin.H{
                "error": "Sistema de permissões não configurado corretamente",
            })
            c.Abort()
        }
    }

    return middleware.APIAccessMiddleware()
}
```

#### ResourcePermissionMiddleware

```go
// Usar o serviço unificado de permissões
unifiedService := permissions.GetGlobalUnifiedService()
if unifiedService != nil {
    // Verificar permissão usando o serviço unificado
    if !unifiedService.HasResourcePermission(userID, userRole, resourceType, uint(resourceID), action) {
        log.Printf("[RESOURCE-MIDDLEWARE] Acesso negado: Usuário %d (role: %s) tentou %s recurso %s:%d",
            userID, userRole, action, resourceType, resourceID)
        c.JSON(http.StatusForbidden, gin.H{
            "success": false,
            "message": "Você não tem permissão para acessar este recurso",
        })
        c.Abort()
        return
    }
} else {
    // Fallback para o serviço antigo
    service := permissions.GetGlobalService()
    if service == nil {
        log.Printf("[RESOURCE-MIDDLEWARE] Serviço de permissões não configurado")
        c.JSON(http.StatusInternalServerError, gin.H{
            "success": false,
            "message": "Serviço de permissões não configurado",
        })
        c.Abort()
        return
    }

    // Verificar permissão usando o serviço antigo
    if !service.HasResourcePermission(userID, resourceType, uint(resourceID), action) {
        log.Printf("[RESOURCE-MIDDLEWARE] Acesso negado: Usuário %d tentou %s recurso %s:%d",
            userID, action, resourceType, resourceID)
        c.JSON(http.StatusForbidden, gin.H{
            "success": false,
            "message": "Você não tem permissão para acessar este recurso",
        })
        c.Abort()
        return
    }
}
```

## Impacto da Alteração

Esta alteração consolida o sistema de permissões em todo o sistema, facilitando a manutenção e evolução do código.

### Benefícios

1. **Centralização**: Toda a lógica de verificação de permissões está centralizada em um único serviço
2. **Consistência**: Todas as partes do sistema usam o mesmo mecanismo de verificação de permissões
3. **Manutenibilidade**: Código mais fácil de manter e entender
4. **Segurança**: Verificações de permissão mais robustas e consistentes
5. **Flexibilidade**: Facilidade para adicionar novos tipos de permissão no futuro

### Compatibilidade

O serviço unificado de permissões é compatível com o código existente, pois:

1. Mantém compatibilidade com o serviço de permissões existente
2. Implementa fallbacks para garantir que o sistema continue funcionando durante a transição
3. Mantém a mesma interface de verificação de permissões

## Próximos Passos

1. **Migração Gradual**: Atualizar gradualmente outras partes do código para usar o serviço unificado
2. **Remoção de Código Legado**: Remover verificações hardcoded e código legado
3. **Documentação**: Atualizar a documentação do sistema para refletir o novo mecanismo de permissões

## Conclusão

Esta implementação consolida o sistema de permissões em todo o sistema, mantendo compatibilidade com o código existente. É um passo importante na padronização geral do sistema, que facilitará a manutenção e evolução futura.

A abordagem incremental e cuidadosa garante que o sistema continue funcionando corretamente durante a transição para o novo mecanismo de permissões.
