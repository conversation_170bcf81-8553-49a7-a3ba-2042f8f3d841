# Consolidação dos Modelos de Filial

## Problema Identificado

O sistema possuía três modelos principais para representar filiais, o que causava confusão, duplicação de código e dificuldade de manutenção:

1. **Branch** (`internal/models/branch.go`):
   - Tabela: `branches`
   - Modelo principal para representar filiais/postos
   - Usa nomenclatura em inglês no código-fonte

2. **Filial** (`internal/models/filial.go`):
   - Tabela: `stations`
   - Modelo intermediário que serve como adaptador
   - Usa nomenclatura em português no código-fonte

3. **Station** (`internal/models/station.go`):
   - Tabela: `stations`
   - Modelo legado marcado como "Deprecated"
   - Usa nomenclatura em inglês no código-fonte

Além disso, existia um quarto modelo mais simples:

4. **BranchModel** (`internal/models/user.go`):
   - Tabela: `branches`
   - Modelo simplificado usado em contextos específicos

Essa multiplicidade de modelos causava:
- Duplicação de código
- Inconsistência na nomenclatura
- Dificuldade de manutenção
- Confusão para novos desenvolvedores

## Solução Implementada

### 1. Criação de um Arquivo de Compatibilidade

Criamos um arquivo `internal/models/branch_compatibility.go` que contém todas as funções de compatibilidade para os modelos de filial:

```go
// BranchToFilial converte Branch para Filial
func BranchToFilial(b *Branch) *Filial {
    // Implementação
}

// FilialToBranch converte Filial para Branch
func FilialToBranch(f *Filial) *Branch {
    // Implementação
}

// BranchToStation converte Branch para Station
func BranchToStation(b *Branch) *Station {
    // Implementação
}

// StationToBranch converte Station para Branch
func StationToBranch(s *Station) *Branch {
    // Implementação
}

// BranchModelToBranch converte BranchModel para Branch
func BranchModelToBranch(bm *BranchModel) *Branch {
    // Implementação
}

// BranchToBranchModel converte Branch para BranchModel
func BranchToBranchModel(b *Branch) *BranchModel {
    // Implementação
}

// NormalizeBranchType normaliza o tipo de filial
func NormalizeBranchType(branchType string) string {
    // Implementação
}

// GetBranchTypeDisplay retorna o texto em português para exibição do tipo de filial
func GetBranchTypeDisplay(branchType string) string {
    // Implementação
}
```

### 2. Atualização do Modelo Branch

Atualizamos o modelo `Branch` para incluir todos os campos necessários e adicionar comentários explicativos sobre a padronização:

```go
// Branch representa uma filial no sistema
// IMPORTANTE: Este é o modelo principal e padronizado para representar filiais/postos no sistema.
// Todas as novas implementações devem usar este modelo em vez de Filial ou Station.
//
// Para compatibilidade com código existente, use as funções em branch_compatibility.go:
// - BranchToFilial: converte Branch para Filial
// - BranchToStation: converte Branch para Station
// - FilialToBranch: converte Filial para Branch
// - StationToBranch: converte Station para Branch
type Branch struct {
    // Campos principais armazenados no banco de dados
    ID              uint           `gorm:"primaryKey" json:"id"`                     // ID único da filial
    Name            string         `gorm:"size:100;not null" json:"name"`            // Nome da filial
    Code            string         `gorm:"size:20;unique;not null" json:"code"`      // Código único da filial
    // ... outros campos ...

    // Campos virtuais para compatibilidade com outros modelos
    // Estes campos não são armazenados no banco de dados
    Type         string   `json:"type" gorm:"-"`                // Alias para BranchType
    PostalCode   string   `json:"postal_code" gorm:"-"`         // Alias para ZipCode
    // ... outros campos virtuais ...
}
```

### 3. Adição de Métodos para Inicializar Campos Virtuais

Adicionamos métodos para inicializar os campos virtuais no modelo `Branch`:

```go
// InitVirtualFields inicializa os campos virtuais do modelo Branch
func (b *Branch) InitVirtualFields() {
    // Implementação
}

// AfterFind é um hook do GORM que é executado após carregar um registro do banco de dados
func (b *Branch) AfterFind(tx *gorm.DB) error {
    b.InitVirtualFields()
    return nil
}

// BeforeSave é um hook do GORM que é executado antes de salvar um registro no banco de dados
func (b *Branch) BeforeSave(tx *gorm.DB) error {
    // Sincronizar campos virtuais com campos reais
    // Implementação
    return nil
}
```

### 4. Atualização dos Métodos de Conversão

Atualizamos os métodos de conversão nos modelos `Branch`, `Filial` e `Station` para usar as funções de compatibilidade:

```go
// ToFilial converte Branch para Filial
// Deprecated: Use Branch diretamente em vez de converter para Filial
func (b *Branch) ToFilial() *Filial {
    return BranchToFilial(b)
}

// FromFilial converte Filial para Branch
// Deprecated: Use Branch diretamente em vez de converter de Filial
func FromFilial(f *Filial) *Branch {
    return FilialToBranch(f)
}
```

### 5. Atualização dos Métodos de Resumo

Atualizamos os métodos de resumo para usar o modelo `Branch` como modelo principal:

```go
// ToSummary converte Branch para BranchSummary
// Esta é a função principal para obter um resumo da filial.
func (b *Branch) ToSummary() BranchSummary {
    // Implementação
}

// ToFilialSummary converte Branch para FilialSummary
// Deprecated: Use ToSummary em vez desta função.
func (b *Branch) ToFilialSummary() FilialSummary {
    // Implementação
}

// ToStationSummary converte Branch para StationSummary
// Deprecated: Use ToSummary em vez desta função.
func (b *Branch) ToStationSummary() StationSummary {
    // Implementação
}
```

## Impacto da Alteração

Esta alteração consolida os modelos de filial em um único modelo principal (`Branch`) e fornece adaptadores para manter a compatibilidade com o código existente. Isso simplifica o código, reduz a duplicação e torna o sistema mais fácil de manter.

### Benefícios

1. **Consistência**: Nomenclatura consistente em todo o sistema
2. **Manutenibilidade**: Código mais fácil de manter e entender
3. **Redução de Duplicação**: Eliminação de código duplicado
4. **Clareza**: Modelo principal claramente identificado
5. **Compatibilidade**: Mantém compatibilidade com código existente

### Compatibilidade

As funções de compatibilidade garantem que o sistema continue funcionando corretamente com todos os modelos existentes, permitindo uma transição gradual para o modelo padronizado.

## Próximos Passos

1. **Atualização Gradual**: Atualizar gradualmente outras partes do código para usar o modelo `Branch` diretamente
2. **Documentação**: Atualizar a documentação para refletir a padronização
3. **Repositórios**: Consolidar os repositórios de filial em um único repositório
4. **Testes**: Implementar testes para garantir que a consolidação não cause regressões

## Conclusão

Esta implementação consolida os modelos de filial em um único modelo principal, mantendo compatibilidade com o código existente. É um passo importante na padronização geral do sistema, que facilitará a manutenção e evolução futura.

A abordagem incremental e cuidadosa garante que o sistema continue funcionando corretamente durante a transição para o modelo padronizado.
