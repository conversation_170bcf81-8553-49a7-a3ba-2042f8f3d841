# Sistema de Autenticação e Segurança

## Visão Geral

O Sistema de Autenticação e Segurança é responsável por gerenciar o acesso dos usuários ao sistema, garantindo que apenas usuários autorizados possam acessar recursos específicos. O sistema implementa autenticação baseada em JWT (JSON Web Tokens) com suporte para refresh tokens e blacklist para logout seguro.

## Componentes Principais

### 1. Estrutura de Dados

O sistema utiliza as seguintes entidades principais:

- **Usuários**: Armazena informações dos usuários, incluindo credenciais e perfis
- **Tokens**: Gerencia tokens JWT para autenticação
- **Blacklist de Tokens**: Armazena tokens revogados para logout seguro

### 2. Modelos de Dados

- **User**: Representa um usuário no sistema, com campos como ID, nome, email, senha (hash), papel, etc.
- **Token**: Representa um token JWT, com campos como token, usuário, expiração, etc.
- **BlacklistedToken**: Representa um token revogado, com campos como token, expiração, etc.

### 3. Serviço de Autenticação

O serviço de autenticação contém os seguintes métodos principais:

- **Login**: Autentica um usuário com base em email e senha
- **Logout**: Revoga o token atual do usuário
- **RefreshToken**: Gera um novo token de acesso com base em um refresh token válido
- **VerifyToken**: Verifica se um token é válido e não está na blacklist
- **GenerateToken**: Gera um novo token JWT para um usuário
- **HashPassword**: Gera um hash seguro para uma senha
- **VerifyPassword**: Verifica se uma senha corresponde ao hash armazenado

### 4. Middleware de Autenticação

O middleware de autenticação é responsável por verificar se o usuário está autenticado antes de permitir o acesso a recursos protegidos:

- **AuthMiddleware**: Verifica se o token JWT é válido e adiciona informações do usuário ao contexto
- **RoleMiddleware**: Verifica se o usuário tem um papel específico
- **CSRFMiddleware**: Protege contra ataques CSRF (Cross-Site Request Forgery)

## Fluxo de Autenticação

### Login

1. O usuário envia suas credenciais (email e senha) para o endpoint de login.
2. O sistema verifica se o email existe e se a senha está correta.
3. Se as credenciais forem válidas, o sistema gera um token JWT e um refresh token.
4. O token JWT é retornado ao cliente e o refresh token é armazenado em um cookie HTTP-only.

### Acesso a Recursos Protegidos

1. O cliente envia uma requisição com o token JWT no cabeçalho de autorização.
2. O middleware de autenticação verifica se o token é válido e não está na blacklist.
3. Se o token for válido, o middleware adiciona informações do usuário ao contexto da requisição.
4. O handler da requisição pode então acessar as informações do usuário e verificar permissões específicas.

### Refresh Token

1. Quando o token JWT expira, o cliente pode solicitar um novo token usando o refresh token.
2. O cliente envia o refresh token para o endpoint de refresh.
3. O sistema verifica se o refresh token é válido.
4. Se o refresh token for válido, o sistema gera um novo token JWT e o retorna ao cliente.

### Logout

1. O cliente envia uma requisição para o endpoint de logout.
2. O sistema adiciona o token JWT atual à blacklist.
3. O sistema remove o refresh token do cookie HTTP-only.
4. O cliente não pode mais acessar recursos protegidos com o token revogado.

## Medidas de Segurança

### Proteção de Senhas

- As senhas são armazenadas como hashes usando algoritmos seguros (bcrypt).
- O sistema implementa políticas de senha forte (comprimento mínimo, caracteres especiais, etc.).
- O sistema limita o número de tentativas de login para prevenir ataques de força bruta.

### Proteção contra Ataques

- **CSRF**: O sistema implementa proteção contra ataques CSRF usando tokens CSRF.
- **XSS**: O sistema implementa proteção contra ataques XSS (Cross-Site Scripting) usando sanitização de entrada.
- **SQL Injection**: O sistema utiliza consultas parametrizadas para prevenir injeção SQL.
- **Rate Limiting**: O sistema implementa limitação de taxa para prevenir ataques de força bruta.

### Segurança de Tokens

- Os tokens JWT são assinados com um segredo seguro.
- Os tokens têm um tempo de expiração curto para minimizar o risco de uso não autorizado.
- Os refresh tokens são armazenados em cookies HTTP-only para prevenir acesso via JavaScript.
- A blacklist de tokens permite revogar tokens antes da expiração.

## Arquivos Principais

- **Serviço de Autenticação**: `internal/auth/service.go`
- **Middleware de Autenticação**: `internal/middleware/auth.go`
- **Handlers de Autenticação**: `handlers/auth_handler.go`
- **Modelos**: `internal/models/user.go`, `internal/models/token.go`

## Como Testar

Para testar o sistema de autenticação:

1. Tente fazer login com credenciais válidas e verifique se um token JWT é retornado.
2. Tente acessar um recurso protegido com o token JWT e verifique se o acesso é permitido.
3. Tente acessar um recurso protegido sem token ou com um token inválido e verifique se o acesso é negado.
4. Tente fazer logout e verifique se o token é revogado.
5. Tente usar um token revogado e verifique se o acesso é negado.
6. Tente usar o refresh token para obter um novo token JWT.

## Possíveis Melhorias

- Implementar autenticação de dois fatores (2FA).
- Adicionar suporte para login social (Google, Facebook, etc.).
- Melhorar o sistema de blacklist de tokens usando um armazenamento mais eficiente.
- Implementar rotação automática de chaves de assinatura JWT.
- Adicionar suporte para diferentes níveis de segurança baseados no contexto (ex: operações sensíveis requerem reautenticação).
