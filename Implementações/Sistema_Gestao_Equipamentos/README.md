# Sistema de Gestão de Equipamentos

## Visão Geral

O Sistema de Gestão de Equipamentos é responsável pelo cadastro, monitoramento e manutenção de equipamentos nos postos de combustível. Ele permite o registro detalhado de cada equipamento, seu histórico de manutenção e sua localização, facilitando o gerenciamento eficiente do parque de equipamentos da rede.

## Componentes Principais

### 1. Estrutura de Dados

O sistema utiliza as seguintes entidades principais:

- **Equipamentos**: Representa os equipamentos físicos nos postos
- **Tipos de Equipamento**: Categorias padronizadas para classificar equipamentos
- **Histórico de Equipamentos**: Registra eventos e alterações nos equipamentos
- **Componentes de Equipamentos**: Partes que compõem um equipamento complexo

### 2. Modelos de Dados

- **Equipment**: Representa um equipamento físico, com campos como ID, nome, descrição, tipo, status, etc.
- **EquipmentType**: Representa um tipo padronizado de equipamento
- **EquipmentHistory**: Representa um evento no histórico de um equipamento
- **EquipmentComponent**: Representa um componente de um equipamento complexo

### 3. Serviço de Gestão de Equipamentos

O serviço de gestão de equipamentos contém os seguintes métodos principais:

- **CreateEquipment**: Cria um novo equipamento
- **UpdateEquipment**: Atualiza informações de um equipamento existente
- **DeleteEquipment**: Remove um equipamento do sistema
- **GetEquipment**: Obtém informações detalhadas de um equipamento
- **ListEquipments**: Lista equipamentos com filtros e paginação
- **AddEquipmentHistory**: Adiciona um evento ao histórico de um equipamento
- **GetEquipmentHistory**: Obtém o histórico de um equipamento
- **AssignEquipmentToBranch**: Atribui um equipamento a uma filial
- **TransferEquipment**: Transfere um equipamento de uma filial para outra

### 4. Handlers de Equipamentos

Os handlers de equipamentos são responsáveis por processar as requisições HTTP relacionadas a equipamentos:

- **CreateEquipmentHandler**: Processa requisições para criar equipamentos
- **UpdateEquipmentHandler**: Processa requisições para atualizar equipamentos
- **DeleteEquipmentHandler**: Processa requisições para remover equipamentos
- **GetEquipmentHandler**: Processa requisições para obter informações de equipamentos
- **ListEquipmentsHandler**: Processa requisições para listar equipamentos
- **TransferEquipmentHandler**: Processa requisições para transferir equipamentos

## Fluxos Principais

### Cadastro de Equipamento

1. O usuário acessa a página de cadastro de equipamentos.
2. O usuário preenche as informações do equipamento (nome, descrição, tipo, etc.).
3. O sistema valida as informações e cria o equipamento.
4. O sistema registra a criação no histórico do equipamento.
5. O sistema notifica os usuários relevantes sobre o novo equipamento.

### Atualização de Equipamento

1. O usuário acessa a página de detalhes do equipamento.
2. O usuário edita as informações do equipamento.
3. O sistema valida as informações e atualiza o equipamento.
4. O sistema registra a atualização no histórico do equipamento.
5. O sistema notifica os usuários relevantes sobre a atualização.

### Transferência de Equipamento

1. O usuário acessa a página de transferência de equipamentos.
2. O usuário seleciona o equipamento a ser transferido e a filial de destino.
3. O sistema valida a transferência e atualiza a filial do equipamento.
4. O sistema registra a transferência no histórico do equipamento.
5. O sistema notifica os usuários relevantes sobre a transferência.

### Consulta de Histórico

1. O usuário acessa a página de detalhes do equipamento.
2. O usuário seleciona a opção de visualizar histórico.
3. O sistema exibe o histórico completo do equipamento, incluindo criação, atualizações, manutenções, transferências, etc.

## Integração com Outros Sistemas

### Sistema de Ordens de Manutenção

- Quando uma ordem de manutenção é criada para um equipamento, o sistema registra a ordem no histórico do equipamento.
- Quando uma ordem de manutenção é concluída, o sistema atualiza o status do equipamento e registra a conclusão no histórico.

### Sistema de Atribuição Automática

- O sistema de atribuição automática utiliza informações do tipo de equipamento para encontrar prestadores elegíveis.
- Quando um equipamento é transferido para uma nova filial, o sistema de atribuição automática considera a nova localização para futuras ordens.

### Sistema de Notificações

- O sistema envia notificações sobre eventos importantes relacionados a equipamentos, como criação, atualização, transferência, etc.
- Os usuários podem configurar quais tipos de notificações desejam receber sobre equipamentos.

## Arquivos Principais

- **Serviço de Equipamentos**: `internal/services/equipment_service.go`
- **Handlers de Equipamentos**: `handlers/equipment_handler.go`
- **Modelos**: `internal/models/equipment.go`, `internal/models/equipment_type.go`, `internal/models/equipment_history.go`

## Como Testar

Para testar o sistema de gestão de equipamentos:

1. Crie um novo equipamento e verifique se ele é criado corretamente.
2. Atualize as informações de um equipamento e verifique se elas são atualizadas corretamente.
3. Transfira um equipamento de uma filial para outra e verifique se a transferência é registrada corretamente.
4. Consulte o histórico de um equipamento e verifique se todos os eventos estão registrados.
5. Crie uma ordem de manutenção para um equipamento e verifique se ela é registrada no histórico.

## Possíveis Melhorias

- Implementar um sistema de QR codes para identificação rápida de equipamentos em campo.
- Adicionar suporte para upload de fotos e documentos relacionados a equipamentos.
- Implementar um sistema de alertas para manutenção preventiva baseado no histórico do equipamento.
- Adicionar suporte para rastreamento de peças e componentes substituídos durante manutenções.
- Implementar um dashboard com métricas de desempenho e saúde dos equipamentos.
