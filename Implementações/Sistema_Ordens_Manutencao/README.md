# Sistema de Ordens de Manutenção

## Visão Geral

O Sistema de Ordens de Manutenção é o núcleo da plataforma, responsável pelo gerenciamento completo do ciclo de vida das ordens de manutenção para equipamentos nos postos de combustível. Ele permite a criação, atribuição, acompanhamento e fechamento de ordens de manutenção, garantindo que todos os serviços sejam executados de forma eficiente e documentada.

## Componentes Principais

### 1. Estrutura de Dados

O sistema utiliza as seguintes entidades principais:

- **Ordens de Manutenção**: Representa as solicitações de manutenção para equipamentos
- **Histórico de Ordens**: Registra todas as alterações e eventos relacionados a uma ordem
- **Itens de Custo**: Registra custos associados a uma ordem de manutenção
- **Cronograma**: Registra datas e horários planejados para execução da manutenção
- **Interações**: Registra comunicações e comentários relacionados a uma ordem

### 2. Modelos de Dados

- **MaintenanceOrder**: Representa uma ordem de manutenção, com campos como ID, equipamento, descrição, prioridade, status, etc.
- **MaintenanceOrderHistory**: Representa um evento no histórico de uma ordem
- **CostItem**: Representa um item de custo associado a uma ordem
- **Schedule**: Representa um agendamento para execução da manutenção
- **Interaction**: Representa uma interação ou comentário relacionado a uma ordem

### 3. Serviço de Ordens de Manutenção

O serviço de ordens de manutenção contém os seguintes métodos principais:

- **CreateOrder**: Cria uma nova ordem de manutenção
- **UpdateOrder**: Atualiza informações de uma ordem existente
- **DeleteOrder**: Remove uma ordem do sistema
- **GetOrder**: Obtém informações detalhadas de uma ordem
- **ListOrders**: Lista ordens com filtros e paginação
- **AssignOrder**: Atribui uma ordem a um técnico ou prestador
- **ChangeOrderStatus**: Altera o status de uma ordem
- **AddCostItem**: Adiciona um item de custo a uma ordem
- **ScheduleMaintenance**: Agenda a execução da manutenção
- **AddInteraction**: Adiciona uma interação ou comentário a uma ordem
- **GetOrderHistory**: Obtém o histórico completo de uma ordem

### 4. Handlers de Ordens de Manutenção

Os handlers de ordens de manutenção são responsáveis por processar as requisições HTTP relacionadas a ordens:

- **CreateOrderHandler**: Processa requisições para criar ordens
- **UpdateOrderHandler**: Processa requisições para atualizar ordens
- **DeleteOrderHandler**: Processa requisições para remover ordens
- **GetOrderHandler**: Processa requisições para obter informações de ordens
- **ListOrdersHandler**: Processa requisições para listar ordens
- **AssignOrderHandler**: Processa requisições para atribuir ordens
- **ChangeOrderStatusHandler**: Processa requisições para alterar o status de ordens

## Fluxos Principais

### Criação de Ordem

1. O usuário acessa a página de criação de ordens.
2. O usuário seleciona o equipamento e preenche as informações da ordem (descrição, prioridade, etc.).
3. O sistema valida as informações e cria a ordem.
4. O sistema registra a criação no histórico da ordem.
5. O sistema notifica os usuários relevantes sobre a nova ordem.
6. Se configurado, o sistema atribui automaticamente a ordem a um prestador elegível.

### Atribuição de Ordem

1. O usuário acessa a página de detalhes da ordem.
2. O usuário seleciona a opção de atribuir a ordem.
3. O usuário seleciona o técnico ou prestador para atribuição.
4. O sistema atualiza a ordem com a atribuição.
5. O sistema registra a atribuição no histórico da ordem.
6. O sistema notifica o técnico ou prestador sobre a atribuição.

### Execução de Manutenção

1. O técnico acessa a página de ordens atribuídas a ele.
2. O técnico seleciona a ordem a ser executada.
3. O técnico registra o início da execução, alterando o status da ordem.
4. O técnico executa a manutenção e registra informações relevantes.
5. O técnico registra a conclusão da manutenção, alterando o status da ordem.
6. O sistema registra todas as alterações no histórico da ordem.
7. O sistema notifica os usuários relevantes sobre a conclusão da manutenção.

### Registro de Custos

1. O usuário acessa a página de detalhes da ordem.
2. O usuário seleciona a opção de adicionar item de custo.
3. O usuário preenche as informações do item (descrição, valor, etc.).
4. O sistema adiciona o item de custo à ordem.
5. O sistema atualiza o custo total da ordem.
6. O sistema registra a adição do item no histórico da ordem.

## Status de Ordens

O sistema suporta vários status para ordens de manutenção, incluindo:

- **Pendente**: Ordem criada, mas ainda não atribuída
- **Atribuída**: Ordem atribuída a um técnico ou prestador
- **Em Progresso**: Manutenção em execução
- **Concluída**: Manutenção concluída com sucesso
- **Cancelada**: Ordem cancelada
- **Em Espera**: Ordem em espera por algum motivo (peças, autorização, etc.)
- **Reprogramada**: Ordem reprogramada para outra data

## Prioridades de Ordens

O sistema suporta várias prioridades para ordens de manutenção, incluindo:

- **Baixa**: Manutenção não urgente
- **Média**: Manutenção com prioridade normal
- **Alta**: Manutenção urgente
- **Crítica**: Manutenção extremamente urgente

## Integração com Outros Sistemas

### Sistema de Gestão de Equipamentos

- Quando uma ordem é criada, o sistema obtém informações detalhadas do equipamento.
- Quando uma ordem é concluída, o sistema atualiza o histórico do equipamento.

### Sistema de Atribuição Automática

- O sistema de atribuição automática é acionado quando uma nova ordem é criada.
- A ordem é automaticamente atribuída a um prestador elegível com base no tipo de equipamento e filial.

### Sistema de Notificações

- O sistema envia notificações sobre eventos importantes relacionados a ordens, como criação, atribuição, alteração de status, etc.
- Os usuários podem configurar quais tipos de notificações desejam receber sobre ordens.

### Sistema Financeiro

- Os itens de custo registrados nas ordens são utilizados pelo sistema financeiro para controle de despesas e faturamento.

## Arquivos Principais

- **Serviço de Ordens**: `internal/services/ordens_service.go`
- **Handlers de Ordens**: `handlers/ordens_handler.go`
- **Modelos**: `internal/models/maintenance_order.go`, `internal/models/maintenance_order_history.go`, `internal/models/cost_item.go`

## Como Testar

Para testar o sistema de ordens de manutenção:

1. Crie uma nova ordem e verifique se ela é criada corretamente.
2. Atribua a ordem a um técnico e verifique se a atribuição é registrada corretamente.
3. Altere o status da ordem e verifique se a alteração é registrada corretamente.
4. Adicione itens de custo e verifique se eles são registrados corretamente.
5. Verifique se as notificações são enviadas aos usuários relevantes.
6. Verifique se o histórico da ordem registra todas as alterações corretamente.

## Possíveis Melhorias

- Implementar um sistema de aprovação para ordens com custos acima de um limite.
- Adicionar suporte para upload de fotos e documentos relacionados a ordens.
- Implementar um sistema de avaliação para manutenções concluídas.
- Adicionar suporte para ordens recorrentes (manutenções preventivas periódicas).
- Implementar um sistema de relatórios detalhados sobre ordens de manutenção.
