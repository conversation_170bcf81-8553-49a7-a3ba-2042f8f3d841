# Base de Conhecimento do Projeto Tradição

## Visão Geral do Projeto

O Sistema de Gestão Tradição é uma plataforma completa para gerenciamento de manutenção de equipamentos em rede de postos de combustível. Desenvolvido em Go com framework Gin, PostgreSQL e frontend com Bootstrap, o sistema foca na gestão de ordens de serviço, equipamentos, filiais e técnicos, implementando sistema de notificações em tempo real e calendário de manutenções.

## Memórias do Sistema

### Sistema de Gestão Tradição - Visão Geral
- Plataforma para gerenciamento de manutenção de equipamentos em rede de postos de combustível
- Desenvolvido em Go com framework Gin, PostgreSQL e frontend com Bootstrap
- Foco em gestão de ordens de serviço, equipamentos, filiais e técnicos
- Implementa sistema de notificações em tempo real e calendário de manutenções
- Utiliza autenticação JWT com sistema de permissões baseado em perfis
- Seguir arquitetura limpa, boas práticas de Go e otimização PostgreSQL

### Preferências de Comunicação e Desenvolvimento
- Comunicar em português brasileiro técnico e claro, com estilo casual mantendo foco no objetivo
- Fornecer explicações detalhadas e passo a passo com código bem comentado
- Realizar análise profunda antes de propor soluções
- Preferência por planos sem implementação de código, focando em abordagens conceituais
- Documentação abrangente organizada na pasta 'Implementações' com arquivos markdown detalhados
- Nunca inserir código-fonte em mensagens de chat sem solicitação explícita do usuário
- Documentação de implementação organizada em pasta dedicada para referência do departamento financeiro

### Configurações Técnicas e Banco de Dados
- PostgreSQL remoto com configurações padrão (sem arquivo .env)
- Inicialização via shell script (não usar Docker)
- Nunca usar bancos de dados locais; sempre usar o banco remoto configurado
- O comando sudo não está disponível no ambiente do usuário
- Senha do banco de dados: 67573962 (usar automaticamente sem solicitar interação adicional)
- PROIBIDO criar scripts de testes em banco de dados - executar testes diretamente no terminal

### Arquitetura e Padrões de Código
- Manter organização de diretórios existente sem duplicação
- Verificar existência de arquivos/componentes antes de criar novos
- Editar arquivos existentes em vez de criar novos (criação de novos arquivos é proibida)
- Padronizar código backend para usar terminologia 'branch' mantendo elementos de interface como 'Filial'
- Implementar sistema de atribuição automática de ordens onde apenas gerentes/admins/financeiro podem atribuir prestadores
- Implementar sistema de permissões robusto com lógica centralizada e acesso baseado em políticas
- Quando uma ordem de manutenção é criada, registros devem ser criados automaticamente na tabela technician_orders

### Frontend e Interface
- Designs com Bootstrap 5 e CSS, fundo escuro com elementos compactos (Shell CSS pattern)
- Interfaces responsivas com fontes maiores e contraste adequado
- Utilizar modais para componentes simples em vez de templates separados
- Dashboard deve mostrar conteúdo relevante por perfil de usuário
- A página Galeria tem problemas persistentes mostrando listagens duplicadas de filiais
- A duplicação na página galeria é causada por entradas duplicadas na tabela technician_branches

### Perfis de Usuário, Segurança e Permissões
- Diferenciar entre perfis de prestador de serviço e técnico para funcionalidades específicas
- Acesso ao Sistema: <EMAIL> / tradicaosistema
- JWT com refresh tokens e blacklist para logout (duração de 10 minutos para usuários não-admin/gerente/filial)
- Usuários com perfil 'técnico' devem ver apenas Dashboard, Ordem Técnica, Postos, Minha Conta e Sair no menu
- Diferentes perfis têm acesso restrito a postos/filiais
- Técnicos estão enfrentando erro 403 ao tentar acessar detalhes de ordens específicas
- Usuários técnicos estão tendo problemas com ordens de serviço falsas/simuladas aparecendo em suas contas
- O sistema de permissões tem verificações de acesso de emergência codificadas para ordens 16-19

## Diretrizes Avançadas para o Sistema de Gestão Tradição

### Análise Profunda e Meticulosa
- **Mapeamento Completo**: Realizar mapeamento completo das estruturas, dependências e fluxos de dados relevantes
- **Detecção de Padrões**: Identificar padrões arquiteturais existentes e manter consistência
- **Análise de Impacto**: Avaliar minuciosamente o impacto de cada alteração proposta em todo o sistema
- **Verificação de Duplicação**: Detectar e eliminar qualquer duplicação de código ou funcionalidade
- **Auditoria de Segurança**: Identificar proativamente vulnerabilidades de segurança em cada análise

### Compreensão do Domínio
- **Modelo Mental Completo**: Desenvolver um modelo mental completo do domínio de negócio
- **Terminologia Precisa**: Usar a terminologia exata do domínio em todo o código e documentação
- **Regras de Negócio**: Identificar e documentar todas as regras de negócio implícitas no código
- **Fluxos de Usuário**: Compreender profundamente os fluxos de usuário para cada funcionalidade

### Arquitetura Impecável
- **Clean Architecture**: Aplicar princípios de Clean Architecture com separação rigorosa de camadas
- **Desacoplamento**: Manter alto nível de desacoplamento entre componentes
- **Injeção de Dependência**: Utilizar injeção de dependência para facilitar testes e flexibilidade
- **Interfaces Bem Definidas**: Criar interfaces claras e coesas entre componentes
- **Tratamento de Erros Robusto**: Implementar estratégia consistente e abrangente para tratamento de erros

### Código Go de Classe Mundial
- **Idiomático**: Escrever código Go idiomático seguindo as convenções da comunidade
- **Concorrência Segura**: Utilizar padrões de concorrência seguros e eficientes
- **Gerenciamento de Recursos**: Garantir liberação adequada de recursos (defer, close)
- **Otimização de Performance**: Otimizar código crítico com benchmarks e profiling
- **Documentação Exemplar**: Documentar código com exemplos claros e explicações de design

### Frontend Excepcional
- **Componentes Reutilizáveis**: Criar componentes altamente reutilizáveis e bem documentados
- **Experiência Perfeita**: Desenvolver interfaces com experiência de usuário fluida e intuitiva
- **Acessibilidade Total**: Implementar acessibilidade WCAG AAA em todas as interfaces
- **Responsividade Perfeita**: Garantir funcionamento perfeito em qualquer dispositivo ou tamanho de tela
- **Performance Otimizada**: Otimizar carregamento e renderização para experiência instantânea

### Banco de Dados Otimizado
- **Modelagem Precisa**: Criar modelos de dados que reflitam perfeitamente o domínio
- **Queries Eficientes**: Escrever queries otimizadas com planos de execução verificados
- **Índices Estratégicos**: Implementar índices estratégicos baseados em padrões de acesso reais
- **Integridade Garantida**: Manter integridade referencial e de domínio em todas as operações
- **Escalabilidade**: Projetar para escalabilidade horizontal e vertical desde o início

## Metodologia de Desenvolvimento

### Planejamento Estratégico
1. **Análise Exaustiva**: Realizar análise exaustiva do requisito e código existente
2. **Mapeamento de Dependências**: Identificar todas as dependências e interações
3. **Plano Detalhado**: Criar plano detalhado com passos específicos e verificáveis
4. **Previsão de Obstáculos**: Antecipar possíveis problemas e preparar soluções alternativas
5. **Validação de Abordagem**: Validar a abordagem escolhida contra requisitos e arquitetura existente

### Implementação Meticulosa
1. **Desenvolvimento Incremental**: Implementar em incrementos pequenos e verificáveis
2. **Verificação Contínua**: Verificar cada incremento contra o plano e requisitos
3. **Refatoração Proativa**: Refatorar proativamente para manter qualidade de código
4. **Testes Abrangentes**: Criar testes unitários, de integração e end-to-end para cada funcionalidade
5. **Documentação Integrada**: Documentar o código e decisões de design durante o desenvolvimento

### Validação Rigorosa
1. **Testes Automatizados**: Executar suíte completa de testes automatizados
2. **Revisão de Código**: Realizar auto-revisão crítica do código implementado
3. **Verificação de Segurança**: Verificar vulnerabilidades de segurança
4. **Validação de Performance**: Validar performance sob condições reais de uso
5. **Verificação de Compatibilidade**: Testar em diferentes ambientes e configurações

## Padrões Específicos do Projeto Tradição

### Sistema de Banco de Dados PostgreSQL
- **Conexão Otimizada**: Implementar e manter pool de conexões eficiente com o PostgreSQL
- **Migrações Seguras**: Utilizar sistema de migrações Atlas para alterações de schema com rollback garantido
- **Queries Parametrizadas**: Usar exclusivamente queries parametrizadas para prevenir injeção SQL
- **Transações ACID**: Garantir propriedades ACID em todas as operações críticas de banco de dados
- **Backup Automatizado**: Implementar e verificar regularmente sistema de backup automatizado
- **Índices Estratégicos**: Criar e manter índices baseados em análise real de padrões de consulta

### Framework Gin e APIs RESTful
- **Middleware Especializado**: Desenvolver middlewares específicos para cada necessidade de segurança e funcionalidade
- **Rotas Semânticas**: Estruturar rotas de forma semântica e consistente seguindo princípios RESTful
- **Validação Robusta**: Implementar validação completa de todas as entradas em nível de API
- **Respostas Padronizadas**: Padronizar formato de resposta para sucesso e erro em todas as APIs
- **Documentação Swagger**: Manter documentação Swagger/OpenAPI atualizada para todas as APIs
- **Rate Limiting**: Implementar rate limiting inteligente para prevenir abusos

### Autenticação e Segurança
- **JWT Avançado**: Implementar sistema JWT com refresh tokens e revogação eficiente
- **Blacklist de Tokens**: Manter e otimizar blacklist de tokens para logout seguro
- **Permissões Granulares**: Implementar sistema de permissões granular baseado em perfis e recursos
- **Auditoria Completa**: Registrar e monitorar todas as ações sensíveis no sistema
- **Proteção CSRF**: Implementar proteção CSRF em todas as operações de modificação
- **Sanitização HTML**: Sanitizar rigorosamente qualquer conteúdo HTML gerado por usuários

### Gestão de Filiais e Equipamentos
- **Hierarquia Clara**: Manter hierarquia clara entre filiais, equipamentos e ordens de serviço
- **Isolamento de Dados**: Garantir que cada filial tenha acesso apenas aos seus próprios equipamentos
- **Transferências Seguras**: Implementar sistema robusto para transferência de equipamentos entre filiais
- **Histórico Completo**: Manter histórico completo de todas as operações em equipamentos
- **Rastreabilidade**: Garantir rastreabilidade completa de cada equipamento no sistema

### Ordens de Manutenção e Fluxo de Trabalho
- **Fluxo Configurável**: Implementar fluxo de trabalho configurável para ordens de manutenção
- **Atribuição Inteligente**: Criar sistema inteligente de atribuição de ordens a técnicos
- **Notificações Automáticas**: Enviar notificações automáticas em cada mudança de status
- **Documentação Integrada**: Permitir anexar documentos e imagens a ordens de serviço
- **Relatórios Detalhados**: Gerar relatórios detalhados de manutenção com métricas relevantes
- **Calendário Interativo**: Manter visualização de calendário interativa e otimizada

## Regras Específicas para Implementação

- **Análise Prévia**: Analisar a existência de arquivos antes de criar ou modificar, evitando duplicidade
- **Conexão PostgreSQL**: Conectar ao banco de dados PostgreSQL remoto usando configurações padrão
- **IDs Numéricos**: Padronizar para IDs numéricos e documentar estratégias de IDs claramente
- **Abstração**: Fortalecer camadas de abstração no código
- **Tecnologias Permitidas**: Nunca usar Python ou Docker no projeto
- **Autorização**: Nunca mudar sistemas sem autorização explícita
- **Inicialização**: Preferir iniciar servidor, banco de dados e projeto através de shell script
- **Implementação Incremental**: Implementar funcionalidades incrementalmente com planos precisos
- **Backups**: Criar backups do projeto com nomes descritivos e documentação detalhada
- **Implementações Responsivas**: Desenvolver interfaces responsivas com análise prévia
- **Execução de Servidores**: Não executar servidores em segundo plano
- **Terminais Separados**: Executar comandos em terminais separados em vez de modificar scripts
- **Análise Profunda**: Realizar análises profundas do código antes de implementar soluções
- **Correção Completa**: Analisar e corrigir todos os erros antes de testar implementações
- **Edição Sequencial**: Escrever código em sequência direta no arquivo, salvando incrementalmente
- **Edições Pequenas**: Editar arquivos em partes pequenas e verificáveis
- **Preferência por Edição**: Preferir editar arquivos existentes em vez de criar novos

## Arquitetura e Estilo Visual
- **Regras de Arquivos**: Respeitar as regras dos arquivos Frontend e Backend nas pastas .cursor/rules
- **Componentes Separados**: Criar arquivos separados para componentes do painel financeiro
- **Bootstrap**: Utilizar Bootstrap e extensões compatíveis com Go para interfaces visuais
- **Estilo Visual**: Manter estilo visual com fundo escuro e elementos de interface compactos
- **Sidebar**: Melhorar o visual do perfil no sidebar (profile-avatar e profile-info)
- **Documentação de Componentes**: Documentar cada componente HTML na mesma subpasta do template
- **Modais**: Utilizar o método modal para componentes simples em vez de templates HTML separados
- **Layout Consistente**: Manter exatamente o mesmo layout entre páginas relacionadas

## Implementações Recentes

### Padronização do Sistema de Filiais
- Criação da tabela `cities` para armazenar informações das cidades
- Padronização da nomenclatura das filiais para seguir o formato "Filial [número] - [cidade]"
- Correção de usuários com perfil 'filial' sem associação com filiais
- Adição de índices para otimizar consultas
- Correção de relacionamentos entre prestadores de serviço e filiais
- Verificação e correção da integridade referencial entre tabelas
- Atualização da interface de seleção de filiais

### Correção de Erros no Sistema de Técnicos
- Correção do erro 500 na página de ordem técnico
- Remoção da dependência da tabela `technician_orders` que não existe
- Modificação do sistema de permissões para verificar corretamente o acesso de técnicos às ordens
- Correção da consulta SQL para buscar ordens atribuídas ao técnico ou ao prestador de serviço
- Remoção de entradas duplicadas na tabela `technician_branches`

## Documentação do Projeto

A documentação do projeto está organizada na pasta `Implementações` na raiz do projeto, seguindo uma estrutura clara:

- **Resumo_Implementacoes.md**: Índice geral de todas as implementações
- **Estrutura_Documentacao.md**: Diretrizes para organização da documentação
- **Sistema_[Nome]/**: Pastas para cada subsistema com documentação específica
  - **README.md**: Visão geral do subsistema
  - **[Funcionalidade].md**: Documentos detalhados sobre funcionalidades específicas

A documentação é mantida atualizada a cada nova implementação, seguindo as diretrizes estabelecidas para garantir consistência e facilidade de acesso às informações.

---

Este documento serve como base de conhecimento abrangente sobre o Projeto Tradição, capturando as memórias, diretrizes, regras e implementações recentes. Ele pode ser usado como referência para transferir conhecimento para outro software ou para novos membros da equipe.
