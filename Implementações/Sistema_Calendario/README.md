# Sistema de Calendário e Agendamento

## Visão Geral

O Sistema de Calendário e Agendamento fornece uma interface visual para gerenciar e visualizar ordens de manutenção ao longo do tempo. Ele permite que os usuários visualizem, criem e gerenciem ordens de manutenção em diferentes visualizações de calendário, facilitando o planejamento e acompanhamento das atividades de manutenção.

## Componentes Principais

### 1. Estrutura de Dados

O sistema utiliza as seguintes entidades principais:

- **Eventos de Calendário**: Representa ordens de manutenção e outros eventos no calendário
- **Agendamentos**: Representa datas e horários planejados para execução de manutenções
- **Visualizações de Calendário**: Diferentes formas de visualizar o calendário (mês, semana, dia, lista)

### 2. Modelos de Dados

- **CalendarEvent**: Representa um evento no calendário, com campos como ID, título, descrição, data de início, data de fim, etc.
- **Schedule**: Representa um agendamento para execução de manutenção
- **CalendarView**: Representa uma configuração de visualização do calendário

### 3. Serviço de Calendário

O serviço de calendário contém os seguintes métodos principais:

- **GetEvents**: Obtém eventos do calendário para um período específico
- **CreateEvent**: Cria um novo evento no calendário
- **UpdateEvent**: Atualiza informações de um evento existente
- **DeleteEvent**: Remove um evento do calendário
- **GetSchedules**: Obtém agendamentos para um período específico
- **CreateSchedule**: Cria um novo agendamento
- **UpdateSchedule**: Atualiza informações de um agendamento existente
- **DeleteSchedule**: Remove um agendamento

### 4. Handlers de Calendário

Os handlers de calendário são responsáveis por processar as requisições HTTP relacionadas ao calendário:

- **GetEventsHandler**: Processa requisições para obter eventos do calendário
- **CreateEventHandler**: Processa requisições para criar eventos no calendário
- **UpdateEventHandler**: Processa requisições para atualizar eventos no calendário
- **DeleteEventHandler**: Processa requisições para remover eventos do calendário
- **GetCalendarViewHandler**: Processa requisições para obter visualizações do calendário

## Visualizações de Calendário

O sistema suporta várias visualizações de calendário, incluindo:

### Visualização Mensal

- Exibe todos os eventos do mês em um formato de grade
- Permite visualizar rapidamente a distribuição de eventos ao longo do mês
- Ideal para planejamento de longo prazo

### Visualização Semanal

- Exibe eventos da semana em um formato de grade por hora
- Permite visualizar detalhes de eventos ao longo da semana
- Ideal para planejamento semanal

### Visualização Diária

- Exibe eventos do dia em um formato detalhado por hora
- Permite visualizar todos os detalhes de eventos do dia
- Ideal para planejamento diário

### Visualização de Lista

- Exibe eventos em formato de lista, ordenados por data
- Permite filtrar e pesquisar eventos facilmente
- Ideal para busca e análise de eventos

## Funcionalidades Principais

### Visualização de Ordens de Manutenção

1. O usuário acessa a página do calendário.
2. O usuário seleciona a visualização desejada (mês, semana, dia, lista).
3. O sistema exibe as ordens de manutenção na visualização selecionada.
4. O usuário pode filtrar as ordens por status, prioridade, técnico, filial, etc.
5. O usuário pode clicar em uma ordem para ver detalhes.

### Criação de Ordens via Calendário

1. O usuário acessa a página do calendário.
2. O usuário clica em uma data ou horário específico.
3. O sistema exibe um formulário para criação de ordem.
4. O usuário preenche as informações da ordem.
5. O sistema cria a ordem e a exibe no calendário.

### Agendamento de Manutenções

1. O usuário acessa a página de detalhes da ordem.
2. O usuário seleciona a opção de agendar manutenção.
3. O usuário seleciona a data e horário para a manutenção.
4. O sistema cria o agendamento e o associa à ordem.
5. O sistema exibe o agendamento no calendário.

### Arrastar e Soltar

1. O usuário acessa a página do calendário.
2. O usuário arrasta um evento para uma nova data ou horário.
3. O sistema atualiza a data e horário do evento.
4. O sistema registra a alteração no histórico da ordem associada.
5. O sistema notifica os usuários relevantes sobre a alteração.

## Filtros e Pesquisa

O sistema oferece diversos filtros para facilitar a visualização de eventos no calendário:

- **Filtro por Status**: Exibe apenas eventos com status específicos
- **Filtro por Prioridade**: Exibe apenas eventos com prioridades específicas
- **Filtro por Técnico**: Exibe apenas eventos atribuídos a técnicos específicos
- **Filtro por Filial**: Exibe apenas eventos de filiais específicas
- **Filtro por Tipo de Equipamento**: Exibe apenas eventos relacionados a tipos específicos de equipamento
- **Pesquisa por Texto**: Permite buscar eventos por texto em título, descrição, etc.

## Integração com Outros Sistemas

### Sistema de Ordens de Manutenção

- Quando uma ordem é criada, o sistema cria automaticamente um evento no calendário.
- Quando uma ordem é atualizada, o sistema atualiza o evento correspondente no calendário.
- Quando uma ordem é concluída ou cancelada, o sistema atualiza o status do evento no calendário.

### Sistema de Notificações

- O sistema envia notificações sobre eventos importantes relacionados ao calendário, como criação, atualização, reagendamento, etc.
- Os usuários podem configurar lembretes para eventos futuros.

### Sistema de Permissões

- O sistema de permissões controla quais eventos cada usuário pode ver no calendário.
- Usuários com perfil "filial" só veem eventos da sua própria filial.
- Usuários com perfil "técnico" só veem eventos atribuídos a eles.
- Usuários com perfil "prestador" só veem eventos atribuídos à sua empresa.

## Tecnologias Utilizadas

O sistema de calendário utiliza a biblioteca FullCalendar.js para a interface de usuário, integrada com o backend Go através de APIs RESTful:

- **FullCalendar.js**: Biblioteca JavaScript para renderização do calendário
- **APIs RESTful**: Endpoints para obter, criar, atualizar e excluir eventos
- **WebSockets**: Para atualizações em tempo real do calendário

## Arquivos Principais

- **Serviço de Calendário**: `internal/calendar/service.go`
- **Handlers de Calendário**: `handlers/calendar_handler.go`
- **Modelos**: `internal/models/calendar_event.go`, `internal/models/schedule.go`
- **Frontend**: `web/templates/calendarios/calendario.html`, `web/static/js/calendario.js`

## Como Testar

Para testar o sistema de calendário:

1. Acesse a página do calendário e verifique se os eventos são exibidos corretamente.
2. Crie uma nova ordem através do calendário e verifique se ela é criada corretamente.
3. Arraste um evento para uma nova data e verifique se a data da ordem é atualizada.
4. Aplique filtros e verifique se apenas os eventos correspondentes são exibidos.
5. Alterne entre diferentes visualizações e verifique se os eventos são exibidos corretamente em cada uma.

## Possíveis Melhorias

- Implementar visualização de calendário por equipe ou departamento.
- Adicionar suporte para exportação de eventos para formatos como iCal ou CSV.
- Implementar um sistema de cores mais sofisticado para diferentes tipos de eventos.
- Adicionar suporte para visualização de conflitos de agendamento.
- Implementar um sistema de sugestão automática de horários para agendamento.
