# Resumo das Implementações do Sistema de Gestão Tradição

## Resumo Executivo

O Sistema de Gestão Tradição é uma plataforma completa para gerenciamento de manutenção de equipamentos em rede de postos de combustível. O sistema foi desenvolvido utilizando Go com o framework Gin para o backend e Bootstrap para o frontend, seguindo princípios de arquitetura limpa e boas práticas de desenvolvimento.

A plataforma oferece uma solução integrada para todo o ciclo de vida da manutenção de equipamentos, desde o cadastro de filiais e equipamentos até a gestão de ordens de manutenção, atribuição a prestadores de serviço, acompanhamento de execução e controle financeiro.

## Arquitetura do Sistema

O sistema segue uma arquitetura modular, com componentes bem definidos e baixo acoplamento, permitindo fácil manutenção e extensão. A arquitetura é composta por:

- **Camada de Apresentação**: Interface web responsiva desenvolvida com Bootstrap
- **Camada de API**: Endpoints RESTful implementados com o framework Gin
- **Camada de Serviços**: Lógica de negócio encapsulada em serviços especializados
- **Camada de Repositórios**: Acesso a dados através de interfaces abstratas
- **Camada de Modelos**: Representação das entidades do domínio

## Principais Implementações

1. **Sistema de Atribuição Automática de Ordens**: Permite a atribuição automática de ordens de manutenção a prestadores de serviço com base no tipo de equipamento e na filial onde a ordem foi criada.

2. **Sistema de Verificação de Permissões Robusto**: Implementa um sistema centralizado de permissões baseado em políticas, com suporte para permissões granulares baseadas em recursos específicos.

3. **Sistema de Notificações**: Fornece notificações em tempo real para usuários sobre eventos relevantes, como atribuição de ordens, mudanças de status, etc.

4. **Sistema de Autenticação e Segurança**: Implementa autenticação segura com JWT, refresh tokens e proteção contra diversos tipos de ataques.

5. **Sistema de Gestão de Equipamentos**: Permite o cadastro, monitoramento e manutenção de equipamentos nos postos de combustível.

6. **Sistema de Gestão de Filiais**: Gerencia o cadastro e administração das filiais (postos de combustível) da rede.

7. **Sistema de Ordens de Manutenção**: Gerencia o ciclo de vida completo das ordens de manutenção.

8. **Sistema de Calendário e Agendamento**: Fornece interface visual para gerenciar e visualizar ordens de manutenção ao longo do tempo.

9. **Sistema Financeiro**: Gerencia aspectos financeiros relacionados às ordens de manutenção, prestadores de serviço e filiais.

## Componentes Implementados

### 1. Sistema de Atribuição Automática de Ordens

#### Estrutura de Dados
- **Tipos de Equipamento**: Categorias padronizadas para classificar equipamentos
- **Relacionamento Prestador-Tipo de Equipamento**: Associa prestadores a tipos de equipamento que podem atender
- **Relacionamento Prestador-Filial**: Associa prestadores a filiais que podem atender
- **Equipamentos**: Inclui referência ao tipo padronizado de equipamento

#### Modelos de Dados
- `EquipmentType`: Representa um tipo padronizado de equipamento
- `ProviderEquipmentType`: Relaciona prestadores com tipos de equipamento
- `ProviderBranch`: Relaciona prestadores com filiais

#### Serviços
- `AutoAssignmentService`: Gerencia a atribuição automática de ordens
  - `AssignOrderToProvider`: Atribui automaticamente uma ordem a um prestador
  - `GetEligibleProviders`: Encontra prestadores elegíveis para uma ordem
  - `AssignProviderToBranch`: Atribui um prestador a uma filial
  - `AssignProviderToEquipmentType`: Atribui um prestador a um tipo de equipamento

#### Fluxo de Funcionamento
1. Quando uma nova ordem é criada, o sistema verifica o tipo de equipamento e a filial
2. O sistema busca prestadores que atendem aquele tipo de equipamento e filial
3. A ordem é atribuída automaticamente ao prestador elegível
4. Notificações são enviadas ao prestador e técnicos relevantes

### 2. Sistema de Verificação de Permissões Robusto

#### Estrutura
- Arquivo de configuração centralizado: `data/permissions.yaml`
- Pacote de permissões: `internal/permissions`
- Middleware de permissões: `internal/middleware`

#### Componentes
- `Service`: Serviço central de permissões
  - `HasPermission`: Verifica se um papel tem permissão para acessar um recurso
  - `GetPermittedRoles`: Obtém papéis com permissão para um recurso
  - `HasResourcePermission`: Verifica permissões para recursos específicos
- `Middleware`: Middleware para verificação de permissões
  - `PageAccessMiddleware`: Verifica permissões de páginas
  - `APIAccessMiddleware`: Verifica permissões de APIs
  - `ResourcePermissionMiddleware`: Verifica permissões de recursos

#### Tipos de Permissões
- Permissões de Página: Controla acesso a páginas da interface
- Permissões de API: Controla acesso a endpoints da API
- Permissões de Recurso: Controla operações em recursos específicos (ordens, equipamentos, etc.)

#### Sistema de Cache
- Cache para resultados de verificações de permissões
- Limpeza automática de entradas expiradas
- Métodos para controlar o cache (habilitar, desabilitar, limpar)

### 3. Sistema de Notificações

#### Estrutura de Dados
- **Notificações**: Mensagens enviadas aos usuários
- **Assinaturas de Notificação**: Preferências de notificação dos usuários
- **Clientes WebSocket**: Conexões ativas para notificações em tempo real

#### Modelos de Dados
- `Notification`: Representa uma notificação para um usuário
- `NotificationSubscription`: Representa uma assinatura de notificação
- `WebSocketClient`: Representa um cliente WebSocket conectado

#### Serviços
- `NotificationService`: Gerencia o envio e armazenamento de notificações
  - `NotifyOrderAssigned`: Notifica sobre atribuição de ordens
  - `NotifyProviderOrderAssigned`: Notifica prestadores sobre ordens atribuídas
  - `EnviarNotificacao`: Envia notificação para um usuário específico
  - `EnviarNotificacaoPorPapel`: Envia notificação para usuários com determinado papel

#### Métodos de Entrega
- Notificações no aplicativo (exibidas na interface do usuário)
- Notificações push (usando WebPush)
- Notificações em tempo real (usando WebSockets)

## Integração entre Componentes

Os sistemas implementados trabalham de forma integrada, criando um ecossistema coeso onde cada componente interage com os demais para fornecer uma experiência completa:

### Fluxo de Trabalho Integrado

1. O **Sistema de Autenticação** valida o usuário e determina seu perfil.
2. O **Sistema de Permissões** verifica as permissões do usuário para acessar recursos.
3. O usuário acessa o **Sistema de Gestão de Filiais** para gerenciar postos.
4. O **Sistema de Gestão de Equipamentos** permite cadastrar e monitorar equipamentos nas filiais.
5. Quando um equipamento precisa de manutenção, o **Sistema de Ordens de Manutenção** permite criar uma ordem.
6. O **Sistema de Atribuição Automática** encontra o prestador adequado para a ordem.
7. O **Sistema de Notificações** envia alertas aos usuários relevantes sobre a nova ordem.
8. O **Sistema de Calendário** exibe a ordem no calendário para planejamento.
9. Após a execução, o **Sistema Financeiro** gerencia os custos e faturamento da ordem.

### Matriz de Integração

| Sistema | Integrações Principais |
|---------|------------------------|
| Atribuição Automática | Ordens de Manutenção, Gestão de Equipamentos, Gestão de Filiais, Notificações |
| Permissões | Autenticação, todos os demais sistemas para controle de acesso |
| Notificações | Todos os sistemas que geram eventos relevantes para usuários |
| Autenticação | Permissões, todos os sistemas que requerem acesso seguro |
| Gestão de Equipamentos | Gestão de Filiais, Ordens de Manutenção, Atribuição Automática |
| Gestão de Filiais | Gestão de Equipamentos, Ordens de Manutenção, Financeiro |
| Ordens de Manutenção | Atribuição Automática, Calendário, Notificações, Financeiro |
| Calendário | Ordens de Manutenção, Notificações |
| Financeiro | Ordens de Manutenção, Gestão de Filiais, Notificações |

## Perfis de Usuário

O sistema suporta diversos perfis de usuário, cada um com permissões e funcionalidades específicas:

- **Administrador**: Acesso completo a todas as funcionalidades do sistema
- **Gerente**: Gestão de manutenções, aprovações, relatórios e configurações
- **Financeiro**: Acesso ao módulo financeiro, relatórios e aprovações de custos
- **Filial**: Gestão de equipamentos e ordens de manutenção da própria filial
- **Prestador**: Gestão de ordens atribuídas à sua empresa
- **Técnico**: Visualização e execução de ordens atribuídas

## Tecnologias Utilizadas

### Backend
- **Go**: Linguagem de programação principal
- **Gin**: Framework web para APIs RESTful
- **GORM**: ORM para acesso a dados
- **JWT**: Autenticação baseada em tokens
- **WebSockets**: Comunicação em tempo real

### Frontend
- **HTML5/CSS3**: Estrutura e estilo das páginas
- **Bootstrap 5**: Framework CSS para interface responsiva
- **JavaScript**: Interatividade no lado do cliente
- **FullCalendar.js**: Biblioteca para visualização de calendário
- **Chart.js**: Biblioteca para gráficos e visualizações

## Diagramas

### Fluxo de Atribuição Automática de Ordens

```
┌─────────────┐     ┌───────────────────┐     ┌─────────────────┐
│ Criação de  │     │  Verificação de   │     │   Busca de      │
│   Ordem     │────▶│ Tipo Equipamento  │────▶│  Prestadores    │
└─────────────┘     └───────────────────┘     └─────────────────┘
                                                       │
┌─────────────┐     ┌───────────────────┐             ▼
│  Envio de   │     │   Atribuição ao   │     ┌─────────────────┐
│ Notificação │◀────│     Prestador     │◀────│  Seleção do     │
└─────────────┘     └───────────────────┘     │   Prestador     │
                                              └─────────────────┘
```

### Fluxo de Verificação de Permissões

```
┌─────────────┐     ┌───────────────────┐     ┌─────────────────┐
│  Requisição │     │  Verificação de   │     │  Verificação    │
│  do Usuário │────▶│     Papel         │────▶│  de Permissão   │
└─────────────┘     └───────────────────┘     └─────────────────┘
                                                       │
┌─────────────┐     ┌───────────────────┐             ▼
│  Acesso     │     │   Verificação     │     ┌─────────────────┐
│  Concedido  │◀────│    de Cache       │◀────│  Consulta de    │
└─────────────┘     └───────────────────┘     │  Configuração   │
                                              └─────────────────┘
```

## Índice de Documentação Detalhada

### Sistemas Principais
- [Sistema de Atribuição Automática de Ordens](./Sistema_Atribuicao_Automatica/README.md)
- [Sistema de Verificação de Permissões](./Sistema_Permissoes/README.md)
- [Sistema de Notificações](./Sistema_Notificacoes/README.md)

### Sistemas Adicionais
- [Sistema de Autenticação e Segurança](./Sistema_Autenticacao/README.md)
- [Sistema de Gestão de Equipamentos](./Sistema_Gestao_Equipamentos/README.md)
- [Sistema de Gestão de Filiais](./Sistema_Gestao_Filiais/README.md)
  - [Padronização do Sistema de Filiais](./Sistema_Gestao_Filiais/Padronizacao_Sistema_Filiais.md) - *Nova implementação*
- [Sistema de Ordens de Manutenção](./Sistema_Ordens_Manutencao/README.md)
- [Sistema de Calendário e Agendamento](./Sistema_Calendario/README.md)
- [Sistema Financeiro](./Sistema_Financeiro/README.md)

### Documentação e Padronização
- [Estrutura de Documentação](./Estrutura_Documentacao.md) - *Nova implementação*
- [Padronização de Nomenclatura](./Padronização_Nomenclatura/README.md)

## Benefícios do Sistema

O Sistema de Gestão Tradição oferece diversos benefícios para a rede de postos de combustível:

### Eficiência Operacional
- **Automação de Processos**: Redução de trabalho manual com atribuição automática de ordens
- **Centralização de Informações**: Todas as informações em um único sistema integrado
- **Redução de Tempo**: Processos mais rápidos e eficientes para manutenção de equipamentos

### Controle e Visibilidade
- **Monitoramento em Tempo Real**: Acompanhamento do status de equipamentos e ordens
- **Rastreabilidade Completa**: Histórico detalhado de todas as ações e eventos
- **Dashboards e Relatórios**: Visualização clara de métricas e indicadores de desempenho

### Redução de Custos
- **Manutenção Preventiva**: Redução de falhas graves com manutenção programada
- **Otimização de Recursos**: Melhor alocação de técnicos e prestadores
- **Controle Financeiro**: Gestão eficiente de custos e despesas de manutenção

### Melhoria na Qualidade de Serviço
- **Tempo de Resposta**: Atendimento mais rápido a problemas de equipamentos
- **Comunicação Eficiente**: Notificações em tempo real para todos os envolvidos
- **Padronização**: Processos padronizados para garantir qualidade consistente

## Conclusão

O Sistema de Gestão Tradição representa uma solução completa e integrada para o gerenciamento de manutenção de equipamentos em rede de postos de combustível. Com sua arquitetura modular e sistemas especializados, a plataforma oferece todas as ferramentas necessárias para otimizar processos, reduzir custos e melhorar a qualidade dos serviços de manutenção.

A implementação de funcionalidades avançadas como atribuição automática de ordens, sistema robusto de permissões e notificações em tempo real coloca o Sistema de Gestão Tradição na vanguarda das soluções de gestão de manutenção, proporcionando um diferencial competitivo significativo para a rede de postos.

A documentação detalhada de cada sistema permite uma compreensão profunda da plataforma, facilitando sua manutenção, evolução e expansão para atender às necessidades futuras da rede Tradição.
