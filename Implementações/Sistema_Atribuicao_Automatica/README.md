# Sistema de Atribuição Automática de Ordens

## Visão Geral

O Sistema de Atribuição Automática de Ordens é uma funcionalidade que permite a atribuição automática de ordens de manutenção a prestadores de serviço com base no tipo de equipamento e na filial onde a ordem foi criada. Isso torna o processo de atribuição de ordens mais eficiente e reduz a necessidade de intervenção manual.

## Componentes Principais

### 1. Estrutura de Dados

O sistema utiliza as seguintes entidades principais:

- **Tipos de Equipamento**: Categorias padronizadas para classificar equipamentos
- **Relacionamento Prestador-Tipo de Equipamento**: Associa prestadores a tipos de equipamento que podem atender
- **Relacionamento Prestador-Filial**: Associa prestadores a filiais que podem atender
- **Equipamentos**: Inclui referência ao tipo padronizado de equipamento

### 2. Modelos de Dados

- **EquipmentType**: Representa um tipo padronizado de equipamento
- **ProviderEquipmentType**: Relaciona prestadores com tipos de equipamento
- **ProviderBranch**: Relaciona prestadores com filiais

Os modelos estão implementados em `internal/models/` e representam as entidades do sistema.

### 3. Serviço de Atribuição Automática

O serviço de atribuição automática está implementado em `internal/services/auto_assignment_service.go` e contém os seguintes métodos principais:

- **AssignOrderToProvider**: Atribui automaticamente uma ordem a um prestador de serviço
- **GetEligibleProviders**: Encontra prestadores elegíveis para uma ordem com base no tipo de equipamento e filial
- **AssignProviderToBranch**: Atribui um prestador a uma filial
- **AssignProviderToEquipmentType**: Atribui um prestador a um tipo de equipamento

### 4. Integração com o Sistema de Notificações

Quando uma ordem é atribuída automaticamente, o sistema envia notificações para:

- O prestador de serviço atribuído
- Os técnicos associados ao prestador de serviço
- O gestor da filial onde a ordem foi criada

## Fluxo de Funcionamento

1. **Criação da Ordem**:
   - Quando uma nova ordem de manutenção é criada, o sistema verifica o equipamento associado.
   - Se o equipamento tiver um tipo padronizado, o sistema usa esse tipo para encontrar prestadores elegíveis.
   - Se o equipamento não tiver um tipo padronizado, o sistema tenta encontrar um tipo com base no campo `Type`.

2. **Busca de Prestadores Elegíveis**:
   - O sistema busca prestadores que atendem ao tipo de equipamento da ordem.
   - O sistema filtra os prestadores que atendem à filial onde a ordem foi criada.
   - O sistema ordena os prestadores com base em critérios como proximidade, disponibilidade, etc.

3. **Atribuição da Ordem**:
   - O sistema seleciona o prestador mais adequado da lista de elegíveis.
   - A ordem é atribuída ao prestador selecionado.
   - O sistema registra a atribuição no histórico da ordem.

4. **Notificação**:
   - O sistema envia notificações ao prestador e técnicos relevantes.
   - As notificações são enviadas por meio do sistema de notificações.

## Configuração e Gerenciamento

### Interface de Administração

Administradores, gerentes e financeiros podem gerenciar as atribuições de prestadores a filiais e tipos de equipamento através da interface de usuário:

- **Atribuição de Prestadores a Filiais**: Permite definir quais prestadores atendem a quais filiais.
- **Atribuição de Prestadores a Tipos de Equipamento**: Permite definir quais prestadores atendem a quais tipos de equipamento.

### Arquivos Principais

- **Serviço**: `internal/services/auto_assignment_service.go`
- **Modelos**: `internal/models/equipment_type.go`, `internal/models/provider_equipment_type.go`, `internal/models/provider_branch.go`

## Como Testar

Para testar o sistema de atribuição automática:

1. Crie um tipo de equipamento padronizado.
2. Atribua um prestador a esse tipo de equipamento.
3. Atribua o prestador a uma filial.
4. Crie um equipamento com o tipo padronizado.
5. Crie uma ordem de manutenção para esse equipamento.
6. Verifique se a ordem foi atribuída automaticamente ao prestador correto.

## Possíveis Melhorias

- Implementar um algoritmo mais sofisticado para seleção de prestadores, considerando carga de trabalho, avaliações, etc.
- Adicionar suporte para atribuição baseada em prioridade da ordem.
- Implementar um sistema de rotação de prestadores para distribuir as ordens de forma mais equilibrada.
- Adicionar suporte para restrições temporais (horário de funcionamento dos prestadores).
