# Sistema de Gestão de Filiais

## Visão Geral

O Sistema de Gestão de Filiais é responsável pelo cadastro, monitoramento e administração das filiais (postos de combustível) da rede. Ele permite o registro detalhado de cada filial, seus equipamentos, usuários associados e histórico de manutenções, facilitando o gerenciamento eficiente da rede de postos.

## Componentes Principais

### 1. Estrutura de Dados

O sistema utiliza as seguintes entidades principais:

- **Filiais (Branches)**: Representa os postos de combustível da rede
- **Usuários de Filial**: Usuários associados a uma filial específica
- **Equipamentos de Filial**: Equipamentos instalados em uma filial
- **Histórico de Filial**: Registra eventos e alterações nas filiais

### 2. Modelos de Dados

- **Branch**: Representa uma filial (posto), com campos como ID, nome, endereço, cidade, estado, etc.
- **BranchUser**: Representa a associação entre um usuário e uma filial
- **BranchEquipment**: Representa a associação entre um equipamento e uma filial
- **BranchHistory**: Representa um evento no histórico de uma filial

### 3. Serviço de Gestão de Filiais

O serviço de gestão de filiais contém os seguintes métodos principais:

- **CreateBranch**: Cria uma nova filial
- **UpdateBranch**: Atualiza informações de uma filial existente
- **DeleteBranch**: Remove uma filial do sistema
- **GetBranch**: Obtém informações detalhadas de uma filial
- **ListBranches**: Lista filiais com filtros e paginação
- **AddUserToBranch**: Adiciona um usuário a uma filial
- **RemoveUserFromBranch**: Remove um usuário de uma filial
- **GetBranchUsers**: Obtém os usuários associados a uma filial
- **GetBranchEquipments**: Obtém os equipamentos instalados em uma filial
- **GetBranchHistory**: Obtém o histórico de uma filial

### 4. Handlers de Filiais

Os handlers de filiais são responsáveis por processar as requisições HTTP relacionadas a filiais:

- **CreateBranchHandler**: Processa requisições para criar filiais
- **UpdateBranchHandler**: Processa requisições para atualizar filiais
- **DeleteBranchHandler**: Processa requisições para remover filiais
- **GetBranchHandler**: Processa requisições para obter informações de filiais
- **ListBranchesHandler**: Processa requisições para listar filiais
- **AddUserToBranchHandler**: Processa requisições para adicionar usuários a filiais
- **RemoveUserFromBranchHandler**: Processa requisições para remover usuários de filiais

## Fluxos Principais

### Cadastro de Filial

1. O usuário acessa a página de cadastro de filiais.
2. O usuário preenche as informações da filial (nome, endereço, cidade, estado, etc.).
3. O sistema valida as informações e cria a filial.
4. O sistema registra a criação no histórico da filial.
5. O sistema notifica os usuários relevantes sobre a nova filial.

### Atualização de Filial

1. O usuário acessa a página de detalhes da filial.
2. O usuário edita as informações da filial.
3. O sistema valida as informações e atualiza a filial.
4. O sistema registra a atualização no histórico da filial.
5. O sistema notifica os usuários relevantes sobre a atualização.

### Gestão de Usuários de Filial

1. O usuário acessa a página de gestão de usuários da filial.
2. O usuário pode adicionar novos usuários à filial ou remover usuários existentes.
3. O sistema atualiza as associações entre usuários e filial.
4. O sistema registra as alterações no histórico da filial.
5. O sistema notifica os usuários afetados sobre as alterações.

### Visualização de Equipamentos

1. O usuário acessa a página de equipamentos da filial.
2. O sistema exibe todos os equipamentos instalados na filial.
3. O usuário pode filtrar os equipamentos por tipo, status, etc.
4. O usuário pode acessar detalhes de cada equipamento.

## Isolamento de Dados

Um aspecto importante do Sistema de Gestão de Filiais é o isolamento de dados, garantindo que cada filial tenha acesso apenas aos seus próprios dados:

- Usuários com perfil "filial" só podem ver e gerenciar dados da sua própria filial.
- Usuários com perfil "técnico" só podem ver filiais designadas a eles.
- Usuários com perfil "prestador" só podem ver filiais autorizadas para eles.
- Usuários com perfil "gerente", "financeiro" ou "admin" podem ver todas as filiais.

## Integração com Outros Sistemas

### Sistema de Gestão de Equipamentos

- Quando um equipamento é instalado em uma filial, o sistema registra a instalação no histórico da filial.
- Quando um equipamento é transferido de uma filial para outra, o sistema atualiza as associações e registra a transferência no histórico de ambas as filiais.

### Sistema de Atribuição Automática

- O sistema de atribuição automática utiliza informações da filial para encontrar prestadores elegíveis.
- A relação entre prestadores e filiais é gerenciada pelo sistema de gestão de filiais.

### Sistema de Notificações

- O sistema envia notificações sobre eventos importantes relacionados a filiais, como criação, atualização, adição/remoção de usuários, etc.
- Os usuários podem configurar quais tipos de notificações desejam receber sobre filiais.

## Arquivos Principais

- **Serviço de Filiais**: `internal/services/branch_service.go`
- **Handlers de Filiais**: `handlers/branch_handler.go`
- **Modelos**: `internal/models/branch.go`, `internal/models/branch_user.go`, `internal/models/branch_history.go`

## Como Testar

Para testar o sistema de gestão de filiais:

1. Crie uma nova filial e verifique se ela é criada corretamente.
2. Atualize as informações de uma filial e verifique se elas são atualizadas corretamente.
3. Adicione um usuário a uma filial e verifique se a associação é criada corretamente.
4. Remova um usuário de uma filial e verifique se a associação é removida corretamente.
5. Verifique se o isolamento de dados está funcionando corretamente, garantindo que cada usuário só veja as filiais apropriadas.

## Possíveis Melhorias

- Implementar um mapa interativo mostrando a localização de todas as filiais.
- Adicionar suporte para upload de fotos e documentos relacionados a filiais.
- Implementar um sistema de métricas para acompanhar o desempenho de cada filial.
- Adicionar suporte para agrupamento de filiais por região ou franquia.
- Implementar um sistema de comunicação interna entre filiais e sede.
