# Sistema de Notificações

## Visão Geral

O Sistema de Notificações é uma implementação abrangente para enviar e gerenciar notificações para os usuários do sistema. Ele suporta múltiplos canais de entrega, incluindo notificações no aplicativo, notificações push e notificações em tempo real via WebSockets. O sistema é altamente flexível e pode ser facilmente estendido para suportar novos tipos de notificações e canais de entrega.

## Componentes Principais

### 1. Estrutura de Dados

O sistema utiliza as seguintes entidades principais:

- **Notificações**: Mensagens enviadas aos usuários
- **Assinaturas de Notificação**: Preferências de notificação dos usuários
- **Clientes WebSocket**: Conexões ativas para notificações em tempo real

### 2. Modelos de Dados

- **Notification**: Representa uma notificação enviada a um usuário
- **NotificationSubscription**: Representa uma assinatura de notificação
- **WebSocketClient**: Representa um cliente WebSocket conectado

Os modelos estão implementados em `internal/models/` e incluem campos como título, mensagem, tipo, status de leitura, etc.

### 3. Serviço de Notificações

O serviço de notificações está implementado em `internal/notifications/service.go` e `internal/services/notification_service.go`, contendo os seguintes métodos principais:

- **NotifyOrderAssigned**: Notifica um técnico ou prestador quando uma ordem é atribuída
- **NotifyProviderOrderAssigned**: Notifica um prestador quando uma ordem é atribuída
- **EnviarNotificacao**: Envia uma notificação para um usuário específico
- **EnviarNotificacaoPorPapel**: Envia uma notificação para usuários com determinado papel
- **BroadcastToUser**: Envia uma mensagem para todos os clientes WebSocket de um usuário
- **BroadcastToRole**: Envia uma mensagem para todos os clientes WebSocket com determinado papel
- **BroadcastAll**: Envia uma mensagem para todos os clientes WebSocket conectados

### 4. Repositório de Notificações

O repositório de notificações está implementado em `internal/repository/notification_repository.go` e contém os seguintes métodos principais:

- **Create**: Cria uma nova notificação
- **GetByUserID**: Obtém as notificações de um usuário
- **MarkAsRead**: Marca uma notificação como lida
- **GetUnreadCount**: Obtém o número de notificações não lidas de um usuário

### 5. Handler de WebSocket

O handler de WebSocket está implementado em `internal/handlers/websocket_handler.go` e gerencia as conexões WebSocket para notificações em tempo real:

- **HandleWebSocket**: Estabelece uma conexão WebSocket com um cliente
- **writePump**: Envia mensagens para o cliente WebSocket
- **readPump**: Recebe mensagens do cliente WebSocket

## Tipos de Notificações

O sistema suporta vários tipos de notificações, incluindo:

- **NotificationOrderAssigned**: Notificação de atribuição de ordem
- **NotificationOrderStatusChanged**: Notificação de mudança de status de ordem
- **NotificationPaymentStatusChanged**: Notificação de mudança de status de pagamento
- **NotificationSystemAlert**: Alerta do sistema
- **NotificationTypeInfo**: Notificação informativa

## Canais de Entrega

O sistema suporta os seguintes canais de entrega:

### Notificações no Aplicativo

As notificações são armazenadas no sistema e exibidas na interface do usuário. Os usuários podem visualizar suas notificações e marcá-las como lidas.

### Notificações Push

O sistema utiliza a API WebPush para enviar notificações push para navegadores compatíveis. Isso permite que os usuários recebam notificações mesmo quando não estão usando o sistema.

### Notificações em Tempo Real

O sistema utiliza WebSockets para enviar notificações em tempo real para usuários conectados. Isso permite uma experiência mais interativa e responsiva.

## Fluxo de Funcionamento

1. **Criação de Notificação**:
   - Um evento ocorre no sistema (ex: uma ordem é atribuída).
   - O serviço de notificações cria uma notificação para os usuários relevantes.
   - A notificação é armazenada no sistema.

2. **Entrega de Notificação**:
   - A notificação é enviada através dos canais de entrega apropriados.
   - Para notificações no aplicativo, a notificação é simplesmente armazenada.
   - Para notificações push, a notificação é enviada usando a API WebPush.
   - Para notificações em tempo real, a notificação é enviada via WebSocket.

3. **Recebimento de Notificação**:
   - O usuário recebe a notificação através dos canais de entrega configurados.
   - Para notificações no aplicativo, o usuário vê a notificação na interface.
   - Para notificações push, o usuário recebe uma notificação do navegador.
   - Para notificações em tempo real, a interface do usuário é atualizada instantaneamente.

4. **Gerenciamento de Notificação**:
   - O usuário pode visualizar suas notificações.
   - O usuário pode marcar notificações como lidas.
   - O usuário pode configurar suas preferências de notificação.

## Arquivos Principais

- **Serviço**: `internal/notifications/service.go`, `internal/services/notification_service.go`
- **Modelos**: `internal/models/notification.go`
- **Repositório**: `internal/repository/notification_repository.go`
- **Handler WebSocket**: `internal/handlers/websocket_handler.go`
- **Handler de Notificações**: `handlers/notification_handler.go`
- **Template de Lista de Notificações**: `web/templates/notifications/list.html`

## Como Testar

Para testar o sistema de notificações:

1. Crie uma notificação para um usuário específico.
2. Verifique se a notificação aparece na interface do usuário.
3. Teste o envio de notificações push inscrevendo um navegador.
4. Teste as notificações em tempo real estabelecendo uma conexão WebSocket.
5. Verifique se as notificações são marcadas como lidas corretamente.

## Possíveis Melhorias

- Implementar suporte para notificações por e-mail.
- Adicionar suporte para notificações por SMS.
- Implementar um sistema de prioridade para notificações.
- Adicionar suporte para notificações agendadas.
- Melhorar a interface de usuário para gerenciamento de notificações.
- Implementar um sistema de filtros para notificações.
