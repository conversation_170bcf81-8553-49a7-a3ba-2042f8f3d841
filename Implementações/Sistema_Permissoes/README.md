# Sistema Unificado de Verificação de Permissões

## Visão Geral

O Sistema Unificado de Verificação de Permissões é uma implementação robusta e centralizada para controlar o acesso a diferentes partes do sistema. Ele oferece uma solução flexível e fácil de manter, com suporte para auditoria de acessos. As permissões são definidas em um único arquivo YAML, permitindo uma visão clara e centralizada de todas as permissões do sistema.

## Componentes Principais

### 1. Estrutura de Configuração

O sistema utiliza um arquivo de configuração YAML para definir as permissões:

- **Pap<PERSON>is (Roles)**: Define os diferentes papéis de usuário no sistema (admin, filial, financeiro, etc.)
- **Permissões de Página**: Define quais páginas cada papel pode acessar
- **Permissões de API**: Define quais endpoints de API cada papel pode acessar
- **Páginas Públicas**: Lista de páginas que não requerem autenticação
- **APIs Públicas**: Lista de endpoints de API que não requerem autenticação

### 2. Serviço Unificado de Permissões

O serviço unificado de permissões está implementado em `internal/permissions/unified_service.go` e contém os seguintes métodos principais:

- **HasPagePermission**: Verifica se um usuário tem permissão para acessar uma página
- **HasAPIPermission**: Verifica se um usuário tem permissão para acessar uma API
- **HasResourcePermission**: Verifica se um usuário tem permissão para realizar uma ação em um recurso específico
- **GetPermittedRoles**: Obtém a lista de papéis que têm permissão para acessar um recurso

### 3. Middleware Unificado de Permissões

O middleware unificado de permissões está implementado em `internal/permissions/unified_middleware.go` e contém os seguintes componentes:

- **PageAccessMiddleware**: Verifica se o usuário tem permissão para acessar uma página
- **APIAccessMiddleware**: Verifica se o usuário tem permissão para acessar um endpoint de API
- **ResourcePermissionMiddleware**: Verifica se o usuário tem permissão para realizar uma ação em um recurso específico
- **RoleMiddleware**: Verifica se o usuário tem um dos papéis especificados

### 4. Sistema de Auditoria

O sistema inclui um mecanismo de auditoria para registrar todas as verificações de permissão:

- **LogPageAccess**: Registra tentativas de acesso a páginas
- **LogAPIAccess**: Registra tentativas de acesso a APIs
- **LogPermissionCheck**: Registra verificações de permissão para recursos específicos
- **GetAuditLogs**: Obtém logs de auditoria para análise

## Tipos de Permissões

### Permissões de Página

Controlam o acesso a páginas da interface do usuário. Exemplos:

- dashboard
- calendario-flip
- manutencao
- financeiro
- relatorios

### Permissões de API

Controlam o acesso a endpoints da API. Exemplos:

- api/user/me
- api/auth/logout
- api/equipments
- api/maintenance
- api/dashboard/metrics

### Permissões de Recurso

Controlam operações em recursos específicos:

- **ResourceOrder**: Permissões para ordens de manutenção
- **ResourceEquipment**: Permissões para equipamentos
- **ResourceBranch**: Permissões para filiais
- **ResourceTechnician**: Permissões para técnicos
- **ResourceServiceProvider**: Permissões para prestadores de serviço

### Ações em Recursos

Definem as operações que podem ser realizadas em recursos:

- **ActionView**: Visualizar um recurso
- **ActionCreate**: Criar um recurso
- **ActionUpdate**: Atualizar um recurso
- **ActionDelete**: Excluir um recurso
- **ActionAssign**: Atribuir um recurso a um usuário

## Fluxo de Funcionamento

1. **Carregamento de Configuração**:
   - O sistema carrega a configuração de permissões do arquivo YAML.
   - A configuração é validada para garantir consistência.

2. **Verificação de Permissão de Página**:
   - Quando um usuário tenta acessar uma página, o middleware verifica se o papel do usuário tem permissão.
   - A verificação é registrada no sistema de auditoria.
   - Se a página for pública, o acesso é permitido sem verificação.
   - Se o usuário for administrador, o acesso é sempre permitido.
   - Caso contrário, o sistema verifica se o papel do usuário tem permissão para a página específica.

3. **Verificação de Permissão de API**:
   - Quando um usuário faz uma requisição a um endpoint de API, o middleware verifica se o papel do usuário tem permissão.
   - A verificação é registrada no sistema de auditoria.
   - O processo é similar à verificação de permissão de página.

4. **Verificação de Permissão de Recurso**:
   - Quando um usuário tenta realizar uma ação em um recurso específico, o sistema verifica se o usuário tem permissão.
   - A verificação é registrada no sistema de auditoria.
   - A verificação considera o papel do usuário, o tipo de recurso, o ID do recurso e a ação a ser realizada.

## Arquivos Principais

- **Serviço Unificado**: `internal/permissions/unified_service.go`
- **Middleware Unificado**: `internal/permissions/unified_middleware.go`
- **Serviço de Auditoria**: `internal/permissions/audit_service.go`
- **Tipos**: `internal/permissions/types.go`
- **Configuração**: `data/permissions.yaml`

## Como Testar

Para testar o sistema de permissões:

1. Verifique se o arquivo de configuração `data/permissions.yaml` está corretamente configurado.
2. Tente acessar diferentes páginas e APIs com diferentes papéis de usuário.
3. Verifique se as permissões estão sendo aplicadas corretamente.
4. Teste o middleware de permissões de recurso com diferentes tipos de recursos e ações.
5. Verifique os logs de auditoria para confirmar que as verificações estão sendo registradas corretamente.

## Possíveis Melhorias Futuras

- Adicionar suporte para permissões dinâmicas baseadas em atributos do usuário.
- Implementar um sistema de herança de permissões entre papéis.
- Adicionar uma interface de administração para gerenciar permissões de forma visual.
- Expandir o sistema de auditoria com mais detalhes e opções de filtragem.
- Implementar alertas para tentativas de acesso não autorizado.
