# Padronização de Nomenclatura no Sistema Tradição

## Visão Geral

Este documento descreve a padronização de nomenclatura implementada no Sistema Tradição, especialmente para os modelos de ordens de manutenção, status, prioridades e tipos de manutenção. A padronização visa garantir consistência em todo o código, facilitar a manutenção e melhorar a interoperabilidade entre diferentes partes do sistema.

## Motivação

Anteriormente, o sistema utilizava uma mistura de termos em português e inglês, com diferentes formatos e convenções. Isso causava problemas como:

1. Inconsistência na representação de dados
2. Dificuldade em manter o código
3. Problemas de interoperabilidade entre diferentes partes do sistema
4. Confusão na documentação e nos logs

A padronização resolve esses problemas estabelecendo convenções claras e consistentes para a nomenclatura em todo o sistema.

## Convenções Adotadas

### Princípios Gerais

1. **Valores Internos em Inglês**: Todos os valores armazenados internamente no sistema (banco de dados, código, APIs) devem estar em inglês.
2. **Exibição em Português**: A interface do usuário deve exibir os valores em português, usando mapas de tradução.
3. **Formato Consistente**: Usar snake_case para valores de enumeração e camelCase para nomes de campos.
4. **Compatibilidade com Código Existente**: Manter compatibilidade com código existente através de constantes e funções de conversão.

### Status de Ordens de Manutenção

| Valor Padronizado (Interno) | Valor Legado | Exibição (UI) |
|----------------------------|--------------|---------------|
| `pending`                  | `pendente`   | Pendente      |
| `scheduled`                | `agendada`   | Agendada      |
| `in_progress`              | `em_andamento` | Em Andamento |
| `completed`                | `concluida`  | Concluída     |
| `cancelled`                | `cancelada`  | Cancelada     |
| `verified`                 | `verificada` | Verificada    |
| `rejected`                 | `rejeitada`  | Rejeitada     |
| `approved`                 | `aprovada`   | Aprovada      |
| `confirmed`                | `confirmada` | Confirmada    |

### Níveis de Prioridade

| Valor Padronizado (Interno) | Valor Legado | Exibição (UI) |
|----------------------------|--------------|---------------|
| `low`                      | `baixa`      | Baixa         |
| `medium`                   | `media`      | Média         |
| `high`                     | `alta`       | Alta          |
| `critical`                 | `critica`    | Crítica       |

### Tipos de Manutenção

| Valor Padronizado (Interno) | Valor Legado | Exibição (UI) |
|----------------------------|--------------|---------------|
| `preventive`               | `preventiva` | Preventiva    |
| `corrective`               | `corretiva`  | Corretiva     |
| `inspection`               | `inspecao`   | Inspeção      |
| `calibration`              | `calibragem` | Calibragem    |
| `installation`             | `instalacao` | Instalação    |
| `emergency`                | `emergencial` | Emergencial  |

## Implementação

### Enumerações

As enumerações foram definidas em `internal/models/maintenance_enums.go` com os valores padronizados em inglês:

```go
// OrderStatus é um tipo para status de ordens de manutenção
type OrderStatus string

// Constantes para status de manutenção
const (
    StatusPending    OrderStatus = "pending"
    StatusScheduled  OrderStatus = "scheduled"
    StatusInProgress OrderStatus = "in_progress"
    StatusCompleted  OrderStatus = "completed"
    StatusCancelled  OrderStatus = "cancelled"
    StatusVerified   OrderStatus = "verified"
    StatusRejected   OrderStatus = "rejected"
    StatusApproved   OrderStatus = "approved"
)
```

### Mapas de Tradução

Para cada enumeração, foi criado um mapa de tradução para exibição em português:

```go
// Mapeamento de status para exibição em português
var OrderStatusDisplay = map[OrderStatus]string{
    StatusPending:    "Pendente",
    StatusScheduled:  "Agendada",
    StatusInProgress: "Em Andamento",
    StatusCompleted:  "Concluída",
    StatusCancelled:  "Cancelada",
    StatusVerified:   "Verificada",
    StatusRejected:   "Rejeitada",
    StatusApproved:   "Aprovada",
}
```

### Compatibilidade com Código Existente

Para manter compatibilidade com código existente, foram definidas constantes com os valores legados:

```go
// Valores legados para compatibilidade
const (
    StatusPendente    OrderStatus = "pendente"
    StatusAgendada    OrderStatus = "agendada"
    StatusEmAndamento OrderStatus = "em_andamento"
    StatusConcluida   OrderStatus = "concluida"
    StatusCancelada   OrderStatus = "cancelada"
    StatusVerificada  OrderStatus = "verificada"
    StatusRejeitada   OrderStatus = "rejeitada"
    StatusAprovada    OrderStatus = "aprovada"
)
```

### Funções de Normalização

Foram implementadas funções para normalizar valores legados para os novos valores padronizados:

```go
// NormalizeOrderStatus converte valores legados para os novos valores padronizados
func NormalizeOrderStatus(status string) OrderStatus {
    switch status {
    case string(StatusPendente):
        return StatusPending
    case string(StatusAgendada):
        return StatusScheduled
    // ...
    default:
        // Se já for um valor padronizado, retorna como está
        if IsStandardOrderStatus(status) {
            return OrderStatus(status)
        }
        // Valor desconhecido, retorna pendente como padrão
        return StatusPending
    }
}
```

### Funções de Verificação

Foram implementadas funções para verificar se um valor já está no formato padronizado:

```go
// IsStandardOrderStatus verifica se o status já está no formato padronizado
func IsStandardOrderStatus(status string) bool {
    switch status {
    case string(StatusPending), string(StatusScheduled), string(StatusInProgress),
        string(StatusCompleted), string(StatusCancelled), string(StatusVerified),
        string(StatusRejected), string(StatusApproved), string(StatusConfirmed):
        return true
    default:
        return false
    }
}
```

### Funções de Exibição

Foram implementadas funções para obter o texto de exibição em português:

```go
// GetOrderStatusDisplay retorna o texto em português para exibição do status
func GetOrderStatusDisplay(status OrderStatus) string {
    if display, ok := OrderStatusDisplay[status]; ok {
        return display
    }
    return string(status)
}
```

## Uso no Código

### Normalização Automática

O modelo `MaintenanceOrder` implementa um método `BeforeSave` que normaliza automaticamente os valores antes de salvar no banco de dados:

```go
// BeforeSave é chamado antes de salvar o registro no banco
func (m *MaintenanceOrder) BeforeSave(tx *gorm.DB) error {
    // Normalizar status e prioridade para garantir que estejam no formato padronizado
    m.Status = NormalizeOrderStatus(string(m.Status))
    m.Priority = NormalizePriorityLevel(string(m.Priority))
    
    return nil
}
```

### Conversão para API

O modelo `MaintenanceOrder` implementa um método `ToAPIResponse` que converte o modelo para o formato de resposta da API, incluindo os textos de exibição em português:

```go
// ToAPIResponse converte o modelo para o formato de resposta da API
func (m *MaintenanceOrder) ToAPIResponse() map[string]any {
    return map[string]any{
        // ...
        "status":          m.Status,
        "priority":        m.Priority,
        // ...
        "status_display":  GetOrderStatusDisplay(m.Status),
        "priority_display": GetPriorityLevelDisplay(m.Priority),
    }
}
```

## Testes

Foram implementados testes automatizados para verificar a conformidade com as convenções de nomenclatura:

- `TestNormalizeOrderStatus`: Testa a normalização de status
- `TestNormalizePriorityLevel`: Testa a normalização de prioridades
- `TestGetOrderStatusDisplay`: Testa a obtenção de textos de exibição para status
- `TestGetPriorityLevelDisplay`: Testa a obtenção de textos de exibição para prioridades
- `TestIsStandardOrderStatus`: Testa a verificação de status padronizados
- `TestIsStandardPriorityLevel`: Testa a verificação de prioridades padronizadas
- `TestMaintenanceOrderToAPIResponse`: Testa a conversão do modelo para resposta de API
- `TestMaintenanceOrderFromLegacyOrdem`: Testa a conversão de modelos legados
- `TestMaintenanceOrderBeforeSave`: Testa a normalização automática antes de salvar

## Conclusão

A padronização de nomenclatura implementada no Sistema Tradição garante consistência em todo o código, facilita a manutenção e melhora a interoperabilidade entre diferentes partes do sistema. A abordagem adotada mantém compatibilidade com código existente, permitindo uma migração gradual para os novos padrões.
