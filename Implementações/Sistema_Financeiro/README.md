# Sistema Financeiro

## Visão Geral

O Sistema Financeiro é responsável pelo gerenciamento de aspectos financeiros relacionados às ordens de manutenção, prestadores de serviço e filiais. Ele permite o controle de custos, faturamento, pagamentos e relatórios financeiros, fornecendo uma visão clara e detalhada das finanças relacionadas às operações de manutenção.

## Componentes Principais

### 1. Estrutura de Dados

O sistema utiliza as seguintes entidades principais:

- **Itens de Custo**: Registra custos associados a ordens de manutenção
- **Faturas**: Representa faturas emitidas para clientes ou recebidas de prestadores
- **Pagamentos**: Registra pagamentos realizados ou recebidos
- **Configurações Financeiras**: Armazena configurações relacionadas a finanças
- **Relatórios Financeiros**: Representa relatórios financeiros gerados pelo sistema

### 2. Modelos de Dados

- **CostItem**: Representa um item de custo associado a uma ordem de manutenção
- **Invoice**: Representa uma fatura emitida ou recebida
- **Payment**: Representa um pagamento realizado ou recebido
- **FinanceSettings**: Representa configurações financeiras do sistema
- **FinancialReport**: Representa um relatório financeiro

### 3. Serviço Financeiro

O serviço financeiro contém os seguintes métodos principais:

- **AddCostItem**: Adiciona um item de custo a uma ordem
- **UpdateCostItem**: Atualiza informações de um item de custo
- **DeleteCostItem**: Remove um item de custo
- **GetOrderCosts**: Obtém todos os custos associados a uma ordem
- **CreateInvoice**: Cria uma nova fatura
- **UpdateInvoice**: Atualiza informações de uma fatura
- **DeleteInvoice**: Remove uma fatura
- **GetInvoice**: Obtém informações detalhadas de uma fatura
- **ListInvoices**: Lista faturas com filtros e paginação
- **RegisterPayment**: Registra um pagamento
- **GetPayment**: Obtém informações detalhadas de um pagamento
- **ListPayments**: Lista pagamentos com filtros e paginação
- **GenerateFinancialReport**: Gera um relatório financeiro

### 4. Handlers Financeiros

Os handlers financeiros são responsáveis por processar as requisições HTTP relacionadas a finanças:

- **AddCostItemHandler**: Processa requisições para adicionar itens de custo
- **UpdateCostItemHandler**: Processa requisições para atualizar itens de custo
- **DeleteCostItemHandler**: Processa requisições para remover itens de custo
- **GetOrderCostsHandler**: Processa requisições para obter custos de uma ordem
- **CreateInvoiceHandler**: Processa requisições para criar faturas
- **UpdateInvoiceHandler**: Processa requisições para atualizar faturas
- **DeleteInvoiceHandler**: Processa requisições para remover faturas
- **GetInvoiceHandler**: Processa requisições para obter informações de faturas
- **ListInvoicesHandler**: Processa requisições para listar faturas
- **RegisterPaymentHandler**: Processa requisições para registrar pagamentos
- **GenerateFinancialReportHandler**: Processa requisições para gerar relatórios financeiros

## Fluxos Principais

### Registro de Custos

1. O usuário acessa a página de detalhes da ordem.
2. O usuário seleciona a opção de adicionar item de custo.
3. O usuário preenche as informações do item (descrição, valor, categoria, etc.).
4. O sistema adiciona o item de custo à ordem.
5. O sistema atualiza o custo total da ordem.
6. O sistema registra a adição do item no histórico da ordem.

### Criação de Fatura

1. O usuário acessa a página de faturas.
2. O usuário seleciona a opção de criar nova fatura.
3. O usuário seleciona as ordens a serem incluídas na fatura.
4. O sistema calcula o valor total da fatura com base nos custos das ordens.
5. O usuário revisa e confirma a fatura.
6. O sistema cria a fatura e a associa às ordens selecionadas.
7. O sistema notifica os usuários relevantes sobre a nova fatura.

### Registro de Pagamento

1. O usuário acessa a página de detalhes da fatura.
2. O usuário seleciona a opção de registrar pagamento.
3. O usuário preenche as informações do pagamento (valor, data, método, etc.).
4. O sistema registra o pagamento e o associa à fatura.
5. O sistema atualiza o status da fatura com base no pagamento.
6. O sistema notifica os usuários relevantes sobre o pagamento.

### Geração de Relatório Financeiro

1. O usuário acessa a página de relatórios financeiros.
2. O usuário seleciona o tipo de relatório e o período desejado.
3. O usuário seleciona filtros adicionais (filial, prestador, etc.).
4. O sistema gera o relatório com base nos parâmetros selecionados.
5. O sistema exibe o relatório na interface ou permite o download em diferentes formatos.

## Categorias de Custo

O sistema suporta várias categorias de custo para melhor organização e análise:

- **Mão de Obra**: Custos relacionados ao trabalho dos técnicos
- **Peças**: Custos de peças e componentes substituídos
- **Deslocamento**: Custos de deslocamento dos técnicos
- **Equipamentos**: Custos de equipamentos utilizados na manutenção
- **Serviços Terceirizados**: Custos de serviços prestados por terceiros
- **Outros**: Custos que não se enquadram nas categorias anteriores

## Métodos de Pagamento

O sistema suporta vários métodos de pagamento:

- **Transferência Bancária**: Pagamentos realizados via transferência bancária
- **Boleto**: Pagamentos realizados via boleto bancário
- **Cartão de Crédito**: Pagamentos realizados via cartão de crédito
- **Cartão de Débito**: Pagamentos realizados via cartão de débito
- **Dinheiro**: Pagamentos realizados em dinheiro
- **Outros**: Outros métodos de pagamento

## Dashboard Financeiro

O sistema inclui um dashboard financeiro que fornece uma visão geral das finanças:

- **Resumo de Custos**: Exibe o total de custos por categoria, período, filial, etc.
- **Resumo de Faturas**: Exibe o total de faturas emitidas, pagas, vencidas, etc.
- **Resumo de Pagamentos**: Exibe o total de pagamentos realizados por método, período, etc.
- **Gráficos e Indicadores**: Exibe gráficos e indicadores financeiros para análise visual
- **Alertas**: Exibe alertas sobre faturas vencidas, pagamentos pendentes, etc.

## Integração com Outros Sistemas

### Sistema de Ordens de Manutenção

- Quando uma ordem é criada, o sistema financeiro cria um registro para controle de custos.
- Quando uma ordem é concluída, o sistema financeiro calcula o custo total e pode gerar uma fatura automaticamente.

### Sistema de Gestão de Prestadores

- O sistema financeiro utiliza informações dos prestadores para faturamento e pagamentos.
- O sistema financeiro pode calcular comissões e taxas para prestadores com base em acordos predefinidos.

### Sistema de Notificações

- O sistema envia notificações sobre eventos importantes relacionados a finanças, como criação de faturas, pagamentos, etc.
- O sistema envia alertas sobre faturas vencidas, pagamentos pendentes, etc.

## Arquivos Principais

- **Serviço Financeiro**: `internal/services/finance_service.go`
- **Handlers Financeiros**: `handlers/finance_handler.go`
- **Modelos**: `internal/models/cost_item.go`, `internal/models/invoice.go`, `internal/models/payment.go`, `internal/models/finance_settings.go`
- **Frontend**: `web/templates/financeiro/dashboard.html`, `web/static/js/finance/dashboard.js`

## Como Testar

Para testar o sistema financeiro:

1. Adicione um item de custo a uma ordem e verifique se ele é registrado corretamente.
2. Crie uma fatura incluindo várias ordens e verifique se o valor total é calculado corretamente.
3. Registre um pagamento para uma fatura e verifique se o status da fatura é atualizado.
4. Gere um relatório financeiro e verifique se os dados estão corretos.
5. Verifique se o dashboard financeiro exibe informações atualizadas e precisas.

## Possíveis Melhorias

- Implementar integração com sistemas contábeis externos.
- Adicionar suporte para múltiplas moedas e conversão automática.
- Implementar um sistema de aprovação para custos acima de um limite.
- Adicionar suporte para notas fiscais eletrônicas.
- Implementar um sistema de previsão financeira baseado em histórico e tendências.
