// Atlas configuration file
// See: https://atlasgo.io/atlas-schema/projects

variable "db_url" {
  type = string
}

variable "db_user" {
  type = string
}

variable "db_pass" {
  type = string
}

env "local" {
  url = "postgres://fcobdj_tradicao:<EMAIL>:54243/fcobdj_tradicao?sslmode=disable"

  migration {
    dir = "file://migrations/atlas"
    format = "sql"
    version_table = "schema_versions"
  }

  src = "schema:*"
}

env "dev" {
  url = "postgres://${var.db_user}:${var.db_pass}@postgres-ag-br1-03.conteige.cloud:54243/fcobdj_tradicao?sslmode=disable"

  migration {
    dir = "file://migrations/atlas"
    format = "sql"
    version_table = "schema_versions"
  }

  backup {
    dir = "file://backups/dev"
    retention = "7d"
  }

  src = "schema:*"
}

env "staging" {
  url = "postgres://${var.db_user}:${var.db_pass}@localhost:5432/tradicao_staging?sslmode=disable"

  migration {
    dir = "file://migrations/atlas"
    format = "sql"
    version_table = "schema_versions"
  }

  backup {
    dir = "file://backups/staging"
    retention = "14d"
  }

  src = "schema:*"
}

env "prod" {
  url = "postgres://${var.db_user}:${var.db_pass}@localhost:5432/tradicao?sslmode=disable"

  migration {
    dir = "file://migrations/atlas"
    format = "sql"
    version_table = "schema_versions"
  }

  backup {
    dir = "file://backups/prod"
    retention = "30d"
  }

  src = "schema:*"
}

// Definição do schema
schema "public" {
  charset = "utf8mb4"
  collate = "utf8mb4_unicode_ci"
}
