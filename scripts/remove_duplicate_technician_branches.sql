-- Script para remover entradas duplicadas na tabela technician_branches
-- Especificamente para o técnico com ID 94 (<EMAIL>)

-- <PERSON><PERSON>, vamos identificar as duplicações
WITH duplicates AS (
    SELECT 
        technician_id, 
        branch_id, 
        specialty_id,
        COUNT(*) as count,
        MIN(id) as min_id
    FROM 
        technician_branches
    GROUP BY 
        technician_id, branch_id, specialty_id
    HAVING 
        COUNT(*) > 1
)
SELECT * FROM duplicates;

-- <PERSON><PERSON><PERSON>, vamos remover as duplicações mantendo apenas o registro com o menor ID
DELETE FROM technician_branches
WHERE id IN (
    SELECT tb.id
    FROM technician_branches tb
    JOIN (
        SELECT 
            technician_id, 
            branch_id, 
            specialty_id,
            MIN(id) as min_id
        FROM 
            technician_branches
        GROUP BY 
            technician_id, branch_id, specialty_id
        HAVING 
            COUNT(*) > 1
    ) d ON tb.technician_id = d.technician_id 
        AND tb.branch_id = d.branch_id 
        AND tb.specialty_id = d.specialty_id
    WHERE tb.id > d.min_id
);

-- Verificar se ainda existem duplicações
WITH duplicates AS (
    SELECT 
        technician_id, 
        branch_id, 
        specialty_id,
        COUNT(*) as count
    FROM 
        technician_branches
    GROUP BY 
        technician_id, branch_id, specialty_id
    HAVING 
        COUNT(*) > 1
)
SELECT * FROM duplicates;

-- Verificar as associações do técnico com ID 94
SELECT * FROM technician_branches WHERE technician_id = 94;
