#!/bin/bash

echo "=== Lint Robot ==="
echo "Verificando e corrigindo problemas de lint no código..."

# Verificar se Go está instalado
if ! command -v go &> /dev/null; then
    echo "Erro: Go não está instalado!"
    exit 1
fi

# Verificar se staticcheck está instalado
if ! command -v staticcheck &> /dev/null; then
    echo "Staticcheck não está instalado. Instalando..."
    go install honnef.co/go/tools/cmd/staticcheck@latest
    if [ $? -ne 0 ]; then
        echo "Erro ao instalar staticcheck!"
        exit 1
    fi
    echo "Staticcheck instalado com sucesso!"
fi

# Executar o script de lint
go run scripts/lint_robot.go

echo ""
echo "Processo de lint concluído!"
