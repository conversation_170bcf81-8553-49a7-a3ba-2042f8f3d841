package main

import (
	"fmt"
	"log"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func main() {
	// Configurar conexão com o banco de dados
	host := "postgres-ag-br1-03.conteige.cloud"
	port := "54243"
	user := "fcobdj_tradicao"
	password := "67573962"
	dbname := "fcobdj_tradicao"

	// Construir string de conexão
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		host, port, user, password, dbname)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Falha ao conectar ao banco de dados: %v", err)
	}

	log.Println("Conexão com o banco de dados estabelecida com sucesso")

	// Consultar a estrutura da tabela maintenance_orders
	var columns []struct {
		ColumnName string `gorm:"column:column_name"`
		DataType   string `gorm:"column:data_type"`
	}

	if err := db.Raw(`
		SELECT column_name, data_type 
		FROM information_schema.columns 
		WHERE table_name = 'maintenance_orders'
		ORDER BY ordinal_position
	`).Scan(&columns).Error; err != nil {
		log.Fatalf("Erro ao consultar estrutura da tabela: %v", err)
	}

	fmt.Println("\nEstrutura da tabela maintenance_orders:")
	fmt.Println("---------------------------------------")
	for _, col := range columns {
		fmt.Printf("%-25s %s\n", col.ColumnName, col.DataType)
	}
}
