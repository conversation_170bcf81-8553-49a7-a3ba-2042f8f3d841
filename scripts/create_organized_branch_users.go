package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/joho/godotenv"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"tradicao/internal/models"
	"tradicao/scripts/common"
)

// Configurações
const (
	BaseID = 100 // ID base para filiais e usuários
)

// Estrutura para armazenar os dados da filial
type FilialData struct {
	Nome     string
	Endereco string
	Bairro   string
	Cidade   string
	Estado   string
	Contato  string
	Codigo   string // Código da filial (ex: Filial01, Filial02, etc.)
	Numero   int    // Número da filial (ex: 1, 2, etc.)
}

func main() {
	// Carregar variáveis de ambiente do arquivo .env
	if err := godotenv.Load(); err != nil {
		log.Printf("Aviso: Não foi possível carregar o arquivo .env: %v", err)
	}

	// Inicializar conexão com o banco de dados usando a string de conexão completa
	databaseURL := os.Getenv("DATABASE_URL")
	if databaseURL == "" {
		// Tentar construir a string de conexão a partir de variáveis individuais
		dbHost := os.Getenv("DB_HOST")
		dbPort := os.Getenv("DB_PORT")
		dbUser := os.Getenv("DB_USER")
		dbPass := os.Getenv("DB_PASS")
		dbName := os.Getenv("DB_NAME")

		if dbHost != "" && dbPort != "" && dbUser != "" && dbPass != "" && dbName != "" {
			databaseURL = fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
				dbHost, dbPort, dbUser, dbPass, dbName)
		} else {
			log.Fatalf("Variáveis de ambiente de banco de dados não definidas")
		}
	}

	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Ler o arquivo com a lista de filiais
	filiais, err := lerArquivoFiliais(common.FilialListPath)
	if err != nil {
		log.Fatalf("Erro ao ler arquivo de filiais: %v", err)
	}

	fmt.Printf("Encontradas %d filiais no arquivo\n\n", len(filiais))

	// Desativar temporariamente as restrições de chave estrangeira
	db.Exec("SET session_replication_role = 'replica';")
	defer db.Exec("SET session_replication_role = 'origin';")

	// Para cada filial, criar um registro na tabela branches e um usuário correspondente
	for _, filial := range filiais {
		// Calcular o ID da filial (BaseID + número da filial)
		filialID := uint(BaseID + filial.Numero)

		// Verificar se já existe uma filial com este ID
		var existingBranch models.Branch
		err := db.Where("id = ?", filialID).First(&existingBranch).Error
		if err == nil {
			fmt.Printf("Já existe uma filial com ID %d (%s). Pulando...\n", filialID, existingBranch.Name)
			continue
		}

		// Criar nova filial com ID específico
		branch := models.Branch{
			ID:        filialID,
			Name:      filial.Nome,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}

		// Salvar a filial no banco de dados
		if err := db.Create(&branch).Error; err != nil {
			fmt.Printf("Erro ao criar filial %s (ID: %d): %v\n", filial.Nome, filialID, err)
			continue
		}

		fmt.Printf("Filial %s criada com sucesso (ID: %d)\n", filial.Nome, filialID)

		// Verificar se já existe um usuário com este ID
		var existingUser models.User
		err = db.Where("id = ?", filialID).First(&existingUser).Error
		if err == nil {
			fmt.Printf("Já existe um usuário com ID %d (%s). Pulando...\n", filialID, existingUser.Name)
			continue
		}

		// Criar email para o usuário
		email := fmt.Sprintf("%<EMAIL>", strings.ToLower(filial.Codigo))

		// Gerar hash da senha
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(DefaultPassword), bcrypt.DefaultCost)
		if err != nil {
			fmt.Printf("Erro ao gerar hash da senha para %s: %v\n", filial.Codigo, err)
			continue
		}

		// Criar o usuário com o mesmo ID da filial
		user := models.User{
			ID:        filialID,
			Name:      filial.Codigo,
			Email:     email,
			Password:  string(hashedPassword),
			Role:      models.RoleFilial,
			BranchID:  &filialID, // Mesmo ID da filial
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}

		// Salvar o usuário no banco de dados
		if err := db.Create(&user).Error; err != nil {
			fmt.Printf("Erro ao criar usuário %s (ID: %d): %v\n", filial.Codigo, filialID, err)
			continue
		}

		fmt.Printf("Usuário %s criado com sucesso (ID: %d, Email: %s, Branch ID: %d)\n",
			filial.Codigo, filialID, email, filialID)
	}

	fmt.Println("\nProcesso concluído!")
}

// lerArquivoFiliais lê o arquivo com a lista de filiais e retorna uma slice de FilialData
func lerArquivoFiliais(filePath string) ([]FilialData, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var filiais []FilialData
	scanner := bufio.NewScanner(file)

	// Expressão regular para extrair informações da linha
	// Formato esperado: "Filial XX - Endereço, Bairro, Cidade/UF - (XX)XXXX-XXXX"
	re := regexp.MustCompile(`(Filial|Matriz) (\d+|) - ([^,]+), ([^,]+), ([^/]+)/([^-]+) - (.+)`)

	for scanner.Scan() {
		linha := scanner.Text()
		if linha == "" {
			continue
		}

		matches := re.FindStringSubmatch(linha)
		if len(matches) >= 8 {
			// Determinar o código e número da filial
			tipoFilial := matches[1]   // "Filial" ou "Matriz"
			numeroFilial := matches[2] // Número da filial ou vazio para Matriz

			var codigo string
			var nome string
			var numero int

			if tipoFilial == "Matriz" {
				codigo = "Matriz"
				nome = "Matriz"
				numero = 0 // Matriz terá ID = BaseID (100)
			} else {
				// Remover espaços e garantir que tenha 2 dígitos
				numeroFilial = strings.TrimSpace(numeroFilial)
				if len(numeroFilial) == 1 {
					numeroFilial = "0" + numeroFilial
				}

				// Converter para número
				num, err := strconv.Atoi(numeroFilial)
				if err != nil {
					fmt.Printf("Erro ao converter número da filial %s: %v\n", numeroFilial, err)
					continue
				}

				codigo = fmt.Sprintf("Filial%s", numeroFilial)
				nome = fmt.Sprintf("Filial %s", numeroFilial)
				numero = num
			}

			filial := FilialData{
				Nome:     nome,
				Endereco: strings.TrimSpace(matches[3]),
				Bairro:   strings.TrimSpace(matches[4]),
				Cidade:   strings.TrimSpace(matches[5]),
				Estado:   strings.TrimSpace(matches[6]),
				Contato:  strings.TrimSpace(matches[7]),
				Codigo:   codigo,
				Numero:   numero,
			}
			filiais = append(filiais, filial)
		} else {
			fmt.Printf("Aviso: Linha não corresponde ao formato esperado: %s\n", linha)

			// Tentar extrair o máximo de informações possível
			parts := strings.Split(linha, " - ")
			if len(parts) >= 2 {
				nome := parts[0]

				// Extrair o código e número da filial do nome
				codigo := ""
				numero := 0

				if strings.HasPrefix(nome, "Filial ") {
					numStr := strings.TrimPrefix(nome, "Filial ")
					// Remover espaços
					numStr = strings.TrimSpace(numStr)

					// Converter para número
					num, err := strconv.Atoi(numStr)
					if err == nil {
						numero = num

						// Garantir que tenha 2 dígitos
						if len(numStr) == 1 {
							numStr = "0" + numStr
						}

						codigo = fmt.Sprintf("Filial%s", numStr)
					} else {
						fmt.Printf("Erro ao converter número da filial %s: %v\n", numStr, err)
						continue
					}
				} else if nome == "Matriz" {
					codigo = "Matriz"
					numero = 0
				} else {
					// Se não conseguir extrair, usar um código genérico
					codigo = strings.ReplaceAll(nome, " ", "")
					numero = 99 // Número genérico para filiais sem número
				}

				addressParts := strings.Split(parts[1], ", ")
				endereco := ""
				bairro := ""
				cidadeUF := ""
				contato := ""

				if len(addressParts) >= 1 {
					endereco = addressParts[0]
				}

				if len(addressParts) >= 2 {
					bairro = addressParts[1]
				}

				if len(addressParts) >= 3 {
					cidadeUFParts := strings.Split(addressParts[2], " - ")
					cidadeUF = cidadeUFParts[0]

					if len(cidadeUFParts) >= 2 {
						contato = cidadeUFParts[1]
					}
				}

				cidadeEstado := strings.Split(cidadeUF, "/")
				cidade := ""
				estado := ""

				if len(cidadeEstado) >= 1 {
					cidade = cidadeEstado[0]
				}

				if len(cidadeEstado) >= 2 {
					estado = cidadeEstado[1]
				}

				filial := FilialData{
					Nome:     nome,
					Endereco: endereco,
					Bairro:   bairro,
					Cidade:   cidade,
					Estado:   estado,
					Contato:  contato,
					Codigo:   codigo,
					Numero:   numero,
				}

				filiais = append(filiais, filial)
			}
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	return filiais, nil
}
