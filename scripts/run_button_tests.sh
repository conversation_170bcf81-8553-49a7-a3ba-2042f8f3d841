#!/bin/bash

# Cores para saída
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Credenciais padrão
EMAIL="<EMAIL>"
SENHA="i1t2@3l4O5"
PAGE="/financeiro"

# Verificar se foram fornecidas credenciais como parâmetros
if [ $# -ge 1 ]; then
    PAGE="$1"
    echo -e "${YELLOW}Testando página: ${PAGE}${NC}"
fi

if [ $# -ge 2 ]; then
    EMAIL="$2"
    echo -e "${YELLOW}Usando email/usuário: ${EMAIL}${NC}"
fi

if [ $# -ge 3 ]; then
    SENHA="$3"
    echo -e "${YELLOW}Usando senha personalizada${NC}"
fi

echo -e "${YELLOW}Iniciando testes de funcionalidade dos botões...${NC}"

# Diretório do projeto
PROJECT_DIR=$(pwd)
echo -e "Diretório do projeto: ${PROJECT_DIR}"

# Verificar se o Chrome/Chromium está instalado
if ! command -v chromium &> /dev/null && ! command -v chromium-browser &> /dev/null && ! command -v google-chrome &> /dev/null; then
    echo -e "${RED}Chrome/Chromium não encontrado. Por favor, instale para executar os testes.${NC}"
    echo -e "Você pode instalar com: apt-get install -y chromium"
    exit 1
fi

# Exportar variável de ambiente para o Chromium
export CHROME_PATH=$(which chromium || which chromium-browser || which google-chrome)

# Verificar se as dependências já estão instaladas
echo -e "${YELLOW}Verificando dependências do teste...${NC}"
if ! grep -q "github.com/chromedp/chromedp" tests/visual/go.mod &> /dev/null; then
    echo -e "${YELLOW}Instalando dependências do teste...${NC}"
    cd tests/visual && go mod tidy
    cd ../../
else
    echo -e "${GREEN}Dependências já instaladas.${NC}"
fi

# Verificar se o servidor já está rodando
echo -e "${YELLOW}Verificando se o servidor já está rodando...${NC}"
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/login > /dev/null 2>&1; then
    echo -e "${GREEN}Servidor já está rodando.${NC}"
    SERVER_ALREADY_RUNNING=true
else
    echo -e "${RED}ATENÇÃO: O servidor não está rodando. Por favor, inicie o servidor em outro terminal com:${NC}"
    echo -e "${YELLOW}go run cmd/main.go${NC}"
    echo -e "${RED}Abortando teste...${NC}"
    exit 1
fi

# Criar diretório para screenshots
mkdir -p tests/visual/screenshots

# Executar os testes de botões
echo -e "${YELLOW}Executando testes de botões...${NC}"
cd tests/visual && VISUAL_TEST_EMAIL="$EMAIL" VISUAL_TEST_PASSWORD="$SENHA" VISUAL_TEST_PAGE="$PAGE" go test -v -run TestButtonsFunctionality

# Capturar resultado dos testes
TEST_RESULT=$?

# Não encerrar o servidor, pois ele foi iniciado manualmente
echo -e "${GREEN}Mantendo o servidor rodando...${NC}"

# Verificar resultado dos testes
if [ $TEST_RESULT -eq 0 ]; then
    echo -e "${GREEN}Testes de botões concluídos com sucesso!${NC}"
    echo -e "${YELLOW}Screenshots salvos em: tests/visual/screenshots/${NC}"
else
    echo -e "${RED}Testes de botões falharam.${NC}"
fi

exit $TEST_RESULT
