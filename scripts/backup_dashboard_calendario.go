package main

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"
)

func runBackupDashboardCalendario() {
	// Arquivo original e destino do backup
	sourceFile := "web/templates/dashboard_calendario_ajustado.html"
	destFile := "dashboard_calendario_ajustado.html"

	// Verifica se o arquivo de origem existe
	if _, err := os.Stat(sourceFile); os.IsNotExist(err) {
		// Se o arquivo original não existe, mas o backup sim, restaura o backup
		if _, err := os.Stat(destFile); err == nil {
			fmt.Println("Arquivo original não encontrado. Restaurando do backup...")
			copyFile(destFile, sourceFile)
			fmt.Println("Arquivo restaurado com sucesso!")
		} else {
			fmt.Println("Arquivo original e backup não encontrados!")
		}
		return
	}

	// Realiza o backup
	err := copyFile(sourceFile, destFile)
	if err != nil {
		fmt.Printf("Erro ao fazer backup: %v\n", err)
		return
	}

	fmt.Printf("Backup realizado com sucesso em %s\n", time.Now().Format("2006-01-02 15:04:05"))
}

func copyFile(src, dst string) error {
	// Cria diretórios intermediários se necessário
	err := os.MkdirAll(filepath.Dir(dst), 0755)
	if err != nil {
		return err
	}

	// Abre o arquivo de origem
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	// Cria o arquivo de destino
	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	// Copia o conteúdo
	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return err
	}

	return nil
}
