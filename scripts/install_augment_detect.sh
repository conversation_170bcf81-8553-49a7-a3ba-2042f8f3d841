#!/bin/bash

# Script para detectar o interpretador Python ativo e instalar o augment

PYTHON_BIN=$(which python3 || which python)

if [ -z "$PYTHON_BIN" ]; then
    echo "Python não encontrado no PATH."
    exit 1
fi

echo "Usando interpretador Python em: $PYTHON_BIN"

# Instalar augment usando o pip do interpretador detectado
"$PYTHON_BIN" -m pip install --upgrade pip
"$PYTHON_BIN" -m pip install augment

echo "Instalação do augment concluída usando o interpretador detectado."
