package main

import (
	"fmt"
	"log"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func main() {
	// Configurar conexão com o banco de dados
	host := "postgres-ag-br1-03.conteige.cloud"
	port := "54243"
	user := "fcobdj_tradicao"
	password := "67573962"
	dbname := "fcobdj_tradicao"

	// Construir string de conexão
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		host, port, user, password, dbname)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Falha ao conectar ao banco de dados: %v", err)
	}

	log.Println("Conexão com o banco de dados estabelecida com sucesso")

	// Verificar se a tabela service_providers existe
	var tableExists bool
	db.Raw(`
		SELECT EXISTS (
			SELECT FROM information_schema.tables 
			WHERE table_name = 'service_providers'
		)
	`).Scan(&tableExists)

	if !tableExists {
		log.Println("A tabela service_providers não existe")
		return
	}

	// Consultar os prestadores de serviço
	type ServiceProvider struct {
		ID   uint
		Name string
	}

	var providers []ServiceProvider
	if err := db.Table("service_providers").Find(&providers).Error; err != nil {
		log.Fatalf("Erro ao consultar prestadores de serviço: %v", err)
	}

	fmt.Println("\nPrestadores de Serviço:")
	fmt.Println("------------------------")
	if len(providers) == 0 {
		fmt.Println("Nenhum prestador de serviço encontrado")
	} else {
		for _, provider := range providers {
			fmt.Printf("ID: %d, Nome: %s\n", provider.ID, provider.Name)
		}
	}

	// Verificar se o técnico com ID 13 existe na tabela users
	var tecnico struct {
		ID   uint
		Name string
		Role string
	}
	if err := db.Table("users").Where("id = ?", 13).First(&tecnico).Error; err != nil {
		log.Printf("Técnico com ID 13 não encontrado: %v", err)
	} else {
		fmt.Printf("\nTécnico encontrado: ID=%d, Nome=%s, Papel=%s\n", tecnico.ID, tecnico.Name, tecnico.Role)
	}
}
