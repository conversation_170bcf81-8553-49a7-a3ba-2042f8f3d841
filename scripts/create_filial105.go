package main

import (
	"fmt"
	"log"
	"os"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"tradicao/internal/models"
)

func main() {
	// Conectar ao banco de dados diretamente
	dsn := os.Getenv("DATABASE_URL")
	if dsn == "" {
		dsn = "postgresql://fcobdj_tradicao:<EMAIL>:54243/fcobdj_tradicao?sslmode=disable"
	}

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Verificar se a filial 105 já existe
	var branch models.BranchModel
	result := db.Where("id = ?", 105).First(&branch)
	if result.Error != nil {
		if result.Error.Error() == "record not found" {
			// Criar a filial 105
			branch = models.BranchModel{
				ID:          105,
				Name:        "Filial05",
				Address:     "Endereço da Filial 05",
				City:        "Cidade da Filial 05",
				State:       "SP",
				ZipCode:     "12345-678",
				Phone:       "(11) 1234-5678",
				Email:       "<EMAIL>",
				ContactInfo: "Contato da Filial 05",
				IsActive:    true,
			}
			result = db.Create(&branch)
			if result.Error != nil {
				log.Fatalf("Erro ao criar filial: %v", result.Error)
			}
			fmt.Println("Filial 105 criada com sucesso!")
		} else {
			log.Fatalf("Erro ao verificar filial: %v", result.Error)
		}
	} else {
		fmt.Println("Filial 105 já existe no banco de dados.")
	}

	// Verificar se o usuário <EMAIL> já existe
	var user models.UserModel
	result = db.Where("email = ?", "<EMAIL>").First(&user)
	if result.Error != nil {
		if result.Error.Error() == "record not found" {
			// Criar o usuário <EMAIL>
			hashedPassword, err := bcrypt.GenerateFromPassword([]byte("tradicaosistema"), bcrypt.DefaultCost)
			if err != nil {
				log.Fatalf("Erro ao gerar hash da senha: %v", err)
			}

			branchID := uint(105)
			user = models.UserModel{
				ID:             105,
				Name:           "Filial05",
				Email:          "<EMAIL>",
				Password:       string(hashedPassword),
				Role:           "filial",
				BranchID:       &branchID,
				FailedAttempts: 0,
				Blocked:        false,
				TOTPEnabled:    false,
			}
			result = db.Create(&user)
			if result.Error != nil {
				log.Fatalf("Erro ao criar usuário: %v", result.Error)
			}
			fmt.Println("Usuário <EMAIL> criado com sucesso!")
		} else {
			log.Fatalf("Erro ao verificar usuário: %v", result.Error)
		}
	} else {
		fmt.Println("Usuário <EMAIL> já existe no banco de dados.")
	}
}
