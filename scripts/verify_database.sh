#!/bin/bash

# Script para verificar a integridade do banco de dados após a limpeza
# Uso: sudo ./verify_database.sh

# Configurações
LOG_DIR="logs"
mkdir -p $LOG_DIR
LOG_FILE="$LOG_DIR/verify_database_$(date +%Y%m%d_%H%M%S).log"

# Função para registrar mensagens
log() {
    echo "$(date +"%Y-%m-%d %H:%M:%S") - $1" | tee -a $LOG_FILE
}

log "Iniciando verificação do banco de dados..."

# Verificar se o banco de dados existe
sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw tradicao_ent
if [ $? -ne 0 ]; then
    log "ERRO: Banco de dados tradicao_ent não encontrado."
    exit 1
fi

# Verificar tabelas
log "Verificando tabelas..."
tables=$(sudo -u postgres psql -d tradicao_ent -t -c "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;")
expected_tables=("branches" "equipment" "maintenance_orders" "schema_versions" "users")

for table in "${expected_tables[@]}"; do
    if echo "$tables" | grep -q "$table"; then
        log "Tabela $table encontrada."
    else
        log "ERRO: Tabela $table não encontrada."
        exit 1
    fi
done

# Verificar contagem de registros
log "Verificando contagem de registros..."
for table in "${expected_tables[@]}"; do
    if [ "$table" != "schema_versions" ]; then
        count=$(sudo -u postgres psql -d tradicao_ent -t -c "SELECT COUNT(*) FROM $table;")
        log "Tabela $table: $count registros"
    fi
done

# Verificar chaves estrangeiras
log "Verificando chaves estrangeiras..."
foreign_keys=$(sudo -u postgres psql -d tradicao_ent -t -c "
    SELECT
        tc.table_name, 
        kcu.column_name, 
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name 
    FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
    WHERE 
        tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_schema = 'public';")

log "Chaves estrangeiras encontradas:"
echo "$foreign_keys" | while read line; do
    log "$line"
done

# Verificar integridade referencial
log "Verificando integridade referencial..."

# Verificar users -> branches
orphaned=$(sudo -u postgres psql -d tradicao_ent -t -c "
    SELECT COUNT(*) FROM users u 
    WHERE u.branch_id IS NOT NULL 
    AND NOT EXISTS (SELECT 1 FROM branches b WHERE b.id = u.branch_id);")
log "Users com branch_id inválido: $orphaned"

# Verificar branches -> users (manager_id)
orphaned=$(sudo -u postgres psql -d tradicao_ent -t -c "
    SELECT COUNT(*) FROM branches b 
    WHERE b.manager_id IS NOT NULL 
    AND NOT EXISTS (SELECT 1 FROM users u WHERE u.id = b.manager_id);")
log "Branches com manager_id inválido: $orphaned"

# Verificar equipment -> branches
orphaned=$(sudo -u postgres psql -d tradicao_ent -t -c "
    SELECT COUNT(*) FROM equipment e 
    WHERE e.branch_id IS NOT NULL 
    AND NOT EXISTS (SELECT 1 FROM branches b WHERE b.id = e.branch_id);")
log "Equipment com branch_id inválido: $orphaned"

# Verificar maintenance_orders -> equipment
orphaned=$(sudo -u postgres psql -d tradicao_ent -t -c "
    SELECT COUNT(*) FROM maintenance_orders mo 
    WHERE mo.equipment_id IS NOT NULL 
    AND NOT EXISTS (SELECT 1 FROM equipment e WHERE e.id = mo.equipment_id);")
log "Maintenance orders com equipment_id inválido: $orphaned"

# Verificar maintenance_orders -> users (requester_id)
orphaned=$(sudo -u postgres psql -d tradicao_ent -t -c "
    SELECT COUNT(*) FROM maintenance_orders mo 
    WHERE mo.requester_id IS NOT NULL 
    AND NOT EXISTS (SELECT 1 FROM users u WHERE u.id = mo.requester_id);")
log "Maintenance orders com requester_id inválido: $orphaned"

log "Verificação concluída."
log "Data/Hora de conclusão: $(date)"
