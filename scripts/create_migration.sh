#!/bin/bash

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Verifica se o nome da migração foi fornecido
if [ -z "$1" ]; then
    echo -e "${RED}Erro: Forneça um nome para a migração${NC}"
    echo -e "Uso: $0 nome_da_migracao"
    exit 1
fi

# Gera timestamp para o nome do arquivo
TIMESTAMP=$(date +%Y%m%d%H%M%S)
MIGRATION_NAME=$(echo "$1" | tr ' ' '_' | tr '[:upper:]' '[:lower:]')
MIGRATION_FILE="migrations/atlas/${TIMESTAMP}_${MIGRATION_NAME}.sql"

# Cria o arquivo de migração
cat > $MIGRATION_FILE << EOF
-- migrate:up

-- Suas alterações aqui

-- migrate:down

-- Comandos para reverter as alterações
EOF

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Migração criada com sucesso: $MIGRATION_FILE${NC}"
    echo -e "${YELLOW}Edite o arquivo para adicionar as alterações necessárias${NC}"
else
    echo -e "${RED}Erro ao criar arquivo de migração${NC}"
    exit 1
fi 