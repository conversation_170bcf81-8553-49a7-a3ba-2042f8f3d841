@echo off
echo Sincronizando completamente o projeto para o Kali Linux...
echo Esta operação pode demorar alguns minutos.

REM Cria a pasta de destino no Kali Linux
ssh -p 2222 root@************ "mkdir -p /root/projeto"

REM Sincroniza todos os arquivos para o Kali Linux
REM Primeiro, cria um arquivo tar com todo o conteúdo
tar -cf projeto.tar .

REM Transfere o arquivo tar para o Kali Linux
scp -P 2222 projeto.tar root@************:/root/

REM Extrai o arquivo tar no Kali Linux
ssh -p 2222 root@************ "cd /root && tar -xf projeto.tar -C /root/projeto && rm projeto.tar"

REM Remove o arquivo tar local
del projeto.tar

REM Cria o script de compilação otimizada
ssh -p 2222 root@************ "cat > /root/projeto/build.sh << 'EOF'
#!/bin/bash

# Configurações de otimização
export GOGC=off
export GOAMD64=v3
export GOMAXPROCS=8
export GOCACHE=/tmp/go-cache

# Limpar binários antigos
mkdir -p bin
rm -f bin/app

# Compilar com otimizações
echo \"Compilando com otimizações...\"
time go build -o bin/app -ldflags=\"-s -w\" ./cmd/main.go

# Verificar o tamanho do binário
echo \"Tamanho do binário:\"
ls -lh bin/app

echo \"Compilação concluída!\"
EOF"

REM Torna o script executável
ssh -p 2222 root@************ "chmod +x /root/projeto/build.sh"

echo Sincronização completa concluída!
echo Agora você pode conectar o VS Code via SSH à pasta /root/projeto/ no Kali Linux.
