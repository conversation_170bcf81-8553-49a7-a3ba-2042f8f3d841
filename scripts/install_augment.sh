#!/bin/bash

# Script para instalar automaticamente o pacote augment

if command -v augment >/dev/null 2>&1; then
    echo "augment já está instalado."
else
    echo "augment não encontrado. Instalando..."
    # Tente instalar via pip
    if command -v pip >/dev/null 2>&1; then
        pip install augment
    else
        echo "pip não encontrado. Por favor, instale o pip primeiro."
        exit 1
    fi
fi
