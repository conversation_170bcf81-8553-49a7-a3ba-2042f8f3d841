@echo off
echo === Lint Robot ===
echo Verificando e corrigindo problemas de lint no codigo...

REM Verificar se Go esta instalado
where go >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Erro: Go nao esta instalado!
    exit /b 1
)

REM Verificar se staticcheck esta instalado
where staticcheck >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Staticcheck nao esta instalado. Instalando...
    go install honnef.co/go/tools/cmd/staticcheck@latest
    if %ERRORLEVEL% neq 0 (
        echo Erro ao instalar staticcheck!
        exit /b 1
    )
    echo Staticcheck instalado com sucesso!
)

REM Executar o script de lint
go run scripts/lint_robot.go

echo.
echo Processo de lint concluido!
