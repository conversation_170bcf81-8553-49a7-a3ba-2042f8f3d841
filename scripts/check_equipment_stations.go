//go:build check_equipment_stations

package main

import (
	"fmt"
	"log"
	"os"

	"tradicao/internal/models"

	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func main() {
	// Carregar variáveis de ambiente do arquivo .env
	err := godotenv.Load()
	if err != nil {
		log.Printf("Erro ao carregar arquivo .env: %v", err)
	}

	// Usar a string de conexão completa do arquivo .env
	dsn := os.Getenv("DATABASE_URL")
	if dsn == "" {
		log.Fatalf("Variável de ambiente DATABASE_URL não encontrada")
	}

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	log.Printf("Conexão com banco de dados estabelecida com sucesso")

	// Buscar todos os equipamentos
	var equipments []models.Equipment
	if err := db.Find(&equipments).Error; err != nil {
		log.Fatalf("Erro ao buscar equipamentos: %v", err)
	}

	// Exibir informações dos equipamentos
	fmt.Println("=== Equipamentos do Sistema ===")
	fmt.Println("ID\tNome\tTipo\tFilial")
	fmt.Println("--------------------------------------------------")
	for _, equipment := range equipments {
		fmt.Printf("%d\t%s\t%s\t%d\n", equipment.ID, equipment.Name, equipment.Type, equipment.FilialID)
	}

	// Buscar todas as filiais
	var stations []models.Station
	if err := db.Find(&stations).Error; err != nil {
		log.Fatalf("Erro ao buscar filiais: %v", err)
	}

	// Exibir informações das filiais
	fmt.Println("\n=== Filiais do Sistema ===")
	fmt.Println("ID\tNome\tEndereço")
	fmt.Println("--------------------------------------------------")
	for _, station := range stations {
		fmt.Printf("%d\t%s\t%s\n", station.ID, station.Name, station.Address)
	}

	// Buscar todos os usuários com suas filiais
	var users []models.User
	if err := db.Find(&users).Error; err != nil {
		log.Fatalf("Erro ao buscar usuários: %v", err)
	}

	// Exibir informações dos usuários e suas filiais
	fmt.Println("\n=== Usuários e suas Filiais ===")
	fmt.Println("ID\tNome\tEmail\tPerfil\tFilial")
	fmt.Println("--------------------------------------------------")
	for _, user := range users {
		branchID := "N/A"
		if user.BranchID != nil {
			branchID = fmt.Sprintf("%d", *user.BranchID)
		}
		fmt.Printf("%d\t%s\t%s\t%s\t%s\n", user.ID, user.Name, user.Email, user.Role, branchID)
	}
}
