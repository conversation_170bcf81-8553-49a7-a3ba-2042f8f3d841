package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"regexp"
	"strings"
	"time"

	"github.com/joho/godotenv"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"tradicao/internal/models"
)

// Configurações
const (
	DefaultPassword = "tradicaosistema" // Senha padrão para os novos usuários
	FilialListPath  = "data/filiais_lista.txt"
)

// Usar o tipo FilialData do pacote common

func main() {
	// Carregar variáveis de ambiente do arquivo .env
	if err := godotenv.Load(); err != nil {
		log.Printf("Aviso: Não foi possível carregar o arquivo .env: %v", err)
	}

	// Inicializar conexão com o banco de dados usando a string de conexão completa
	databaseURL := os.Getenv("DATABASE_URL")
	if databaseURL == "" {
		// Tentar construir a string de conexão a partir de variáveis individuais
		dbHost := os.Getenv("DB_HOST")
		dbPort := os.Getenv("DB_PORT")
		dbUser := os.Getenv("DB_USER")
		dbPass := os.Getenv("DB_PASS")
		dbName := os.Getenv("DB_NAME")

		if dbHost != "" && dbPort != "" && dbUser != "" && dbPass != "" && dbName != "" {
			databaseURL = fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
				dbHost, dbPort, dbUser, dbPass, dbName)
		} else {
			log.Fatalf("Variáveis de ambiente de banco de dados não definidas")
		}
	}

	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Ler o arquivo com a lista de filiais
	filiais, err := lerArquivoFiliais(FilialListPath)
	if err != nil {
		log.Fatalf("Erro ao ler arquivo de filiais: %v", err)
	}

	fmt.Printf("Encontradas %d filiais no arquivo\n", len(filiais))

	// Para cada filial, criar um registro na tabela branches e um usuário correspondente
	for _, filial := range filiais {
		// Usar o código da filial já definido na estrutura
		filialCode := filial.Codigo

		// Verificar se a filial já existe
		var existingBranch models.Branch
		err := db.Where("name = ?", filial.Nome).First(&existingBranch).Error

		var branchID uint

		if err == nil {
			// Filial já existe
			fmt.Printf("Filial %s já existe no banco de dados (ID: %d)\n", filial.Nome, existingBranch.ID)
			branchID = existingBranch.ID
		} else if err != nil && strings.Contains(err.Error(), "record not found") {
			// Criar nova filial
			branch := models.Branch{
				Name:      filial.Nome,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			}

			// Verificar se os campos existem na tabela branches
			// Se não existirem, remover os campos da struct
			if !campoExisteNaTabela(db, "branches", "city") {
				fmt.Println("Campo 'city' não existe na tabela branches")
				// Não podemos remover campos da struct, mas podemos criar uma nova struct com apenas os campos necessários
				// Ou usar db.Omit() para ignorar campos específicos
			}

			if err := db.Create(&branch).Error; err != nil {
				fmt.Printf("Erro ao criar filial %s: %v\n", filial.Nome, err)
				continue
			}

			fmt.Printf("Filial %s criada com sucesso (ID: %d)\n", filial.Nome, branch.ID)
			branchID = branch.ID
		} else {
			fmt.Printf("Erro ao verificar filial %s: %v\n", filial.Nome, err)
			continue
		}

		// Verificar se já existe um usuário para esta filial
		var existingUser models.User
		err = db.Where("name = ? OR email = ?", filialCode, fmt.Sprintf("%<EMAIL>", strings.ToLower(filialCode))).First(&existingUser).Error

		if err == nil {
			fmt.Printf("Usuário %s já existe (ID: %d)\n", filialCode, existingUser.ID)
			continue
		}

		if err != nil && !strings.Contains(err.Error(), "record not found") {
			fmt.Printf("Erro ao verificar usuário %s: %v\n", filialCode, err)
			continue
		}

		// Criar email para o usuário
		email := fmt.Sprintf("%<EMAIL>", strings.ToLower(filialCode))

		// Gerar hash da senha
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(DefaultPassword), bcrypt.DefaultCost)
		if err != nil {
			fmt.Printf("Erro ao gerar hash da senha para %s: %v\n", filialCode, err)
			continue
		}

		// Criar o usuário
		user := models.User{
			Name:      filialCode,
			Email:     email,
			Password:  string(hashedPassword),
			Role:      models.RoleFilial,
			BranchID:  &branchID,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}

		// Salvar o usuário no banco de dados
		if err := db.Create(&user).Error; err != nil {
			fmt.Printf("Erro ao criar usuário %s: %v\n", filialCode, err)
			continue
		}

		fmt.Printf("Usuário %s criado com sucesso (ID: %d, Email: %s)\n", filialCode, user.ID, email)
	}

	fmt.Println("Processo concluído!")
}

// lerArquivoFiliais lê o arquivo com a lista de filiais e retorna uma slice de FilialData
func lerArquivoFiliais(filePath string) ([]FilialData, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var filiais []FilialData
	scanner := bufio.NewScanner(file)

	// Expressão regular para extrair informações da linha
	// Formato esperado: "Filial XX - Endereço, Bairro, Cidade/UF - (XX)XXXX-XXXX"
	re := regexp.MustCompile(`(Filial|Matriz) (\d+|) - ([^,]+), ([^,]+), ([^/]+)/([^-]+) - (.+)`)

	for scanner.Scan() {
		linha := scanner.Text()
		if linha == "" {
			continue
		}

		matches := re.FindStringSubmatch(linha)
		if len(matches) >= 8 {
			// Determinar o código da filial
			tipoFilial := matches[1]   // "Filial" ou "Matriz"
			numeroFilial := matches[2] // Número da filial ou vazio para Matriz

			var codigo string
			var nome string

			if tipoFilial == "Matriz" {
				codigo = "Matriz"
				nome = "Matriz"
			} else {
				// Remover espaços e garantir que tenha 2 dígitos
				numeroFilial = strings.TrimSpace(numeroFilial)
				if len(numeroFilial) == 1 {
					numeroFilial = "0" + numeroFilial
				}
				codigo = fmt.Sprintf("Filial%s", numeroFilial)
				nome = fmt.Sprintf("Filial %s", numeroFilial)
			}

			filial := FilialData{
				Nome:     nome,
				Endereco: strings.TrimSpace(matches[3]),
				Bairro:   strings.TrimSpace(matches[4]),
				Cidade:   strings.TrimSpace(matches[5]),
				Estado:   strings.TrimSpace(matches[6]),
				Contato:  strings.TrimSpace(matches[7]),
				Codigo:   codigo,
			}
			filiais = append(filiais, filial)
		} else {
			fmt.Printf("Aviso: Linha não corresponde ao formato esperado: %s\n", linha)

			// Tentar extrair o máximo de informações possível
			parts := strings.Split(linha, " - ")
			if len(parts) >= 2 {
				nome := parts[0]

				// Extrair o código da filial do nome
				codigo := ""
				if strings.HasPrefix(nome, "Filial ") {
					numero := strings.TrimPrefix(nome, "Filial ")
					// Remover espaços e garantir que tenha 2 dígitos
					numero = strings.TrimSpace(numero)
					if len(numero) == 1 {
						numero = "0" + numero
					}
					codigo = fmt.Sprintf("Filial%s", numero)
				} else if nome == "Matriz" {
					codigo = "Matriz"
				} else {
					// Se não conseguir extrair, usar um código genérico
					codigo = strings.ReplaceAll(nome, " ", "")
				}

				addressParts := strings.Split(parts[1], ", ")
				endereco := ""
				bairro := ""
				cidadeUF := ""
				contato := ""

				if len(addressParts) >= 1 {
					endereco = addressParts[0]
				}

				if len(addressParts) >= 2 {
					bairro = addressParts[1]
				}

				if len(addressParts) >= 3 {
					cidadeUFParts := strings.Split(addressParts[2], " - ")
					cidadeUF = cidadeUFParts[0]

					if len(cidadeUFParts) >= 2 {
						contato = cidadeUFParts[1]
					}
				}

				cidadeEstado := strings.Split(cidadeUF, "/")
				cidade := ""
				estado := ""

				if len(cidadeEstado) >= 1 {
					cidade = cidadeEstado[0]
				}

				if len(cidadeEstado) >= 2 {
					estado = cidadeEstado[1]
				}

				filial := FilialData{
					Nome:     nome,
					Endereco: endereco,
					Bairro:   bairro,
					Cidade:   cidade,
					Estado:   estado,
					Contato:  contato,
					Codigo:   codigo,
				}

				filiais = append(filiais, filial)
			}
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	return filiais, nil
}

// campoExisteNaTabela verifica se um campo existe em uma tabela
func campoExisteNaTabela(db *gorm.DB, tabela, campo string) bool {
	// Consulta para verificar se o campo existe na tabela
	var result struct {
		Exists bool
	}

	// A consulta SQL depende do banco de dados (PostgreSQL neste caso)
	query := `
		SELECT EXISTS (
			SELECT 1
			FROM information_schema.columns
			WHERE table_name = ?
			AND column_name = ?
		) as exists
	`

	db.Raw(query, tabela, campo).Scan(&result)

	return result.Exists
}
