#!/bin/bash

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Diretório das migrações
MIGRATIONS_DIR="migrations/atlas"

# Função para verificar sintaxe SQL
check_sql_syntax() {
    local file=$1
    echo -e "${YELLOW}Verificando sintaxe de $file...${NC}"
    
    # Verifica se o arquivo contém as seções up/down
    if ! grep -q "^-- migrate:up" "$file" || ! grep -q "^-- migrate:down" "$file"; then
        echo -e "${RED}Erro: $file não contém as seções migrate:up e migrate:down${NC}"
        return 1
    }
    
    # Extrai e verifica a seção UP
    sed -n '/^-- migrate:up/,/^-- migrate:down/p' "$file" | \
    PGPASSWORD=$DB_PASS psql -U $DB_USER -h localhost -d postgres -f - >/dev/null 2>&1
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}Erro de sintaxe na seção UP de $file${NC}"
        return 1
    fi
    
    # Extrai e verifica a seção DOWN
    sed -n '/^-- migrate:down/,/^-- migrate:up\|$/p' "$file" | \
    PGPASSWORD=$DB_PASS psql -U $DB_USER -h localhost -d postgres -f - >/dev/null 2>&1
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}Erro de sintaxe na seção DOWN de $file${NC}"
        return 1
    fi
    
    echo -e "${GREEN}Sintaxe OK em $file${NC}"
    return 0
}

# Função para verificar ordem das migrações
check_migration_order() {
    echo -e "${YELLOW}Verificando ordem das migrações...${NC}"
    
    local prev_version=0
    for file in $(ls -1 $MIGRATIONS_DIR/*.sql | sort); do
        local version=$(basename "$file" | cut -d'_' -f1)
        if ! [[ "$version" =~ ^[0-9]+$ ]]; then
            echo -e "${RED}Erro: $file não segue o padrão de versionamento${NC}"
            return 1
        fi
        
        if [ "$version" -le "$prev_version" ] && [ "$prev_version" -ne 0 ]; then
            echo -e "${RED}Erro: $file tem versão menor ou igual à migração anterior${NC}"
            return 1
        fi
        
        prev_version=$version
    done
    
    echo -e "${GREEN}Ordem das migrações OK${NC}"
    return 0
}

# Função para verificar dependências entre tabelas
check_table_dependencies() {
    echo -e "${YELLOW}Verificando dependências entre tabelas...${NC}"
    
    for file in $MIGRATIONS_DIR/*.sql; do
        # Procura por referências a tabelas (REFERENCES keyword)
        local refs=$(grep -i "references" "$file" | grep -oE "[a-zA-Z_]+\([a-zA-Z_]+\)")
        
        for ref in $refs; do
            local table=$(echo $ref | cut -d'(' -f1)
            # Verifica se a tabela referenciada é criada antes
            if ! grep -q "CREATE.*TABLE.*$table" $(ls $MIGRATIONS_DIR/*.sql | sort | sed -n "1,/$file/p"); then
                echo -e "${RED}Erro: $file referencia tabela $table que não foi criada anteriormente${NC}"
                return 1
            fi
        done
    done
    
    echo -e "${GREEN}Dependências entre tabelas OK${NC}"
    return 0
}

# Verifica se as variáveis de ambiente estão definidas
if [ -z "$DB_USER" ] || [ -z "$DB_PASS" ]; then
    echo -e "${RED}Erro: Variáveis DB_USER e DB_PASS devem estar definidas${NC}"
    exit 1
fi

# Executa todas as verificações
echo -e "${YELLOW}Iniciando verificação das migrações...${NC}"

check_migration_order
if [ $? -ne 0 ]; then exit 1; fi

check_table_dependencies
if [ $? -ne 0 ]; then exit 1; fi

for file in $MIGRATIONS_DIR/*.sql; do
    check_sql_syntax "$file"
    if [ $? -ne 0 ]; then exit 1; fi
done

echo -e "${GREEN}Todas as verificações passaram com sucesso!${NC}" 