#!/bin/bash

# Script para extrair informações detalhadas do banco de dados tradicao
# Uso: sudo ./scripts/extrair_info_tradicao.sh

# Configurações
OUTPUT_DIR="docs/database"
mkdir -p $OUTPUT_DIR
OUTPUT_FILE="$OUTPUT_DIR/tradicao_database_info.md"

echo "# Informações do Banco de Dados 'tradicao'" > $OUTPUT_FILE
echo "Data de extração: $(date)" >> $OUTPUT_FILE
echo "" >> $OUTPUT_FILE

echo "## Lista de Tabelas" >> $OUTPUT_FILE
echo "" >> $OUTPUT_FILE
echo "| Nome da Tabela | Número de Registros |" >> $OUTPUT_FILE
echo "|---------------|---------------------|" >> $OUTPUT_FILE

# Obter lista de tabelas e contar registros
tables=$(sudo -u postgres psql -d tradicao -t -c "SELECT tablename FROM pg_tables WHERE schemaname = 'public' ORDER BY tablename;")

for table in $tables; do
    table=$(echo $table | tr -d ' ')
    count=$(sudo -u postgres psql -d tradicao -t -c "SELECT COUNT(*) FROM $table;")
    count=$(echo $count | tr -d ' ')
    echo "| $table | $count |" >> $OUTPUT_FILE
done

echo "" >> $OUTPUT_FILE
echo "## Estrutura das Tabelas" >> $OUTPUT_FILE
echo "" >> $OUTPUT_FILE

for table in $tables; do
    table=$(echo $table | tr -d ' ')
    echo "### Tabela: $table" >> $OUTPUT_FILE
    echo "" >> $OUTPUT_FILE
    
    # Obter estrutura da tabela
    echo "#### Colunas" >> $OUTPUT_FILE
    echo "" >> $OUTPUT_FILE
    echo "| Coluna | Tipo | Nullable | Default |" >> $OUTPUT_FILE
    echo "|--------|------|----------|---------|" >> $OUTPUT_FILE
    
    columns=$(sudo -u postgres psql -d tradicao -t -c "
        SELECT 
            column_name, 
            data_type, 
            is_nullable, 
            column_default
        FROM 
            information_schema.columns 
        WHERE 
            table_schema = 'public' 
            AND table_name = '$table'
        ORDER BY 
            ordinal_position;")
    
    echo "$columns" | while read line; do
        if [ -n "$line" ]; then
            col_name=$(echo $line | awk '{print $1}')
            data_type=$(echo $line | awk '{print $2}')
            is_nullable=$(echo $line | awk '{print $3}')
            col_default=$(echo $line | awk '{$1=$2=$3=""; print $0}' | sed 's/^[ \t]*//')
            
            echo "| $col_name | $data_type | $is_nullable | $col_default |" >> $OUTPUT_FILE
        fi
    done
    
    echo "" >> $OUTPUT_FILE
    
    # Obter índices da tabela
    echo "#### Índices" >> $OUTPUT_FILE
    echo "" >> $OUTPUT_FILE
    
    indices=$(sudo -u postgres psql -d tradicao -t -c "
        SELECT
            indexname,
            indexdef
        FROM
            pg_indexes
        WHERE
            tablename = '$table'
        ORDER BY
            indexname;")
    
    if [ -n "$indices" ]; then
        echo "| Nome do Índice | Definição |" >> $OUTPUT_FILE
        echo "|---------------|-----------|" >> $OUTPUT_FILE
        
        echo "$indices" | while read line; do
            if [ -n "$line" ]; then
                idx_name=$(echo $line | awk '{print $1}')
                idx_def=$(echo $line | awk '{$1=""; print $0}' | sed 's/^[ \t]*//')
                
                echo "| $idx_name | $idx_def |" >> $OUTPUT_FILE
            fi
        done
    else
        echo "Nenhum índice encontrado para esta tabela." >> $OUTPUT_FILE
    fi
    
    echo "" >> $OUTPUT_FILE
    
    # Obter chaves estrangeiras
    echo "#### Chaves Estrangeiras" >> $OUTPUT_FILE
    echo "" >> $OUTPUT_FILE
    
    fkeys=$(sudo -u postgres psql -d tradicao -t -c "
        SELECT
            tc.constraint_name,
            kcu.column_name,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
        FROM
            information_schema.table_constraints AS tc
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
              AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
              AND ccu.table_schema = tc.table_schema
        WHERE
            tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_name = '$table';")
    
    if [ -n "$fkeys" ]; then
        echo "| Nome da Constraint | Coluna | Tabela Referenciada | Coluna Referenciada |" >> $OUTPUT_FILE
        echo "|-------------------|--------|---------------------|---------------------|" >> $OUTPUT_FILE
        
        echo "$fkeys" | while read line; do
            if [ -n "$line" ]; then
                constraint_name=$(echo $line | awk '{print $1}')
                column_name=$(echo $line | awk '{print $2}')
                foreign_table=$(echo $line | awk '{print $3}')
                foreign_column=$(echo $line | awk '{print $4}')
                
                echo "| $constraint_name | $column_name | $foreign_table | $foreign_column |" >> $OUTPUT_FILE
            fi
        done
    else
        echo "Nenhuma chave estrangeira encontrada para esta tabela." >> $OUTPUT_FILE
    fi
    
    echo "" >> $OUTPUT_FILE
    
    # Amostra de dados (primeiros 5 registros)
    echo "#### Amostra de Dados (até 5 registros)" >> $OUTPUT_FILE
    echo "" >> $OUTPUT_FILE
    
    sample=$(sudo -u postgres psql -d tradicao -t -c "SELECT * FROM $table LIMIT 5;")
    
    if [ -n "$sample" ]; then
        echo '```' >> $OUTPUT_FILE
        echo "$sample" >> $OUTPUT_FILE
        echo '```' >> $OUTPUT_FILE
    else
        echo "Nenhum dado encontrado nesta tabela." >> $OUTPUT_FILE
    fi
    
    echo "" >> $OUTPUT_FILE
    echo "---" >> $OUTPUT_FILE
    echo "" >> $OUTPUT_FILE
done

echo "## Visões" >> $OUTPUT_FILE
echo "" >> $OUTPUT_FILE

views=$(sudo -u postgres psql -d tradicao -t -c "SELECT viewname FROM pg_views WHERE schemaname = 'public' ORDER BY viewname;")

if [ -n "$views" ]; then
    echo "| Nome da Visão | Definição |" >> $OUTPUT_FILE
    echo "|--------------|-----------|" >> $OUTPUT_FILE
    
    for view in $views; do
        view=$(echo $view | tr -d ' ')
        definition=$(sudo -u postgres psql -d tradicao -t -c "SELECT definition FROM pg_views WHERE viewname = '$view';")
        
        echo "| $view | $definition |" >> $OUTPUT_FILE
    done
else
    echo "Nenhuma visão encontrada neste banco de dados." >> $OUTPUT_FILE
fi

echo "" >> $OUTPUT_FILE
echo "## Funções" >> $OUTPUT_FILE
echo "" >> $OUTPUT_FILE

functions=$(sudo -u postgres psql -d tradicao -t -c "SELECT proname FROM pg_proc WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public') ORDER BY proname;")

if [ -n "$functions" ]; then
    echo "| Nome da Função | Linguagem | Definição |" >> $OUTPUT_FILE
    echo "|---------------|-----------|-----------|" >> $OUTPUT_FILE
    
    for func in $functions; do
        func=$(echo $func | tr -d ' ')
        lang=$(sudo -u postgres psql -d tradicao -t -c "SELECT l.lanname FROM pg_language l JOIN pg_proc p ON p.prolang = l.oid WHERE p.proname = '$func' AND p.pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public') LIMIT 1;")
        definition=$(sudo -u postgres psql -d tradicao -t -c "SELECT prosrc FROM pg_proc WHERE proname = '$func' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public') LIMIT 1;")
        
        echo "| $func | $lang | $definition |" >> $OUTPUT_FILE
    done
else
    echo "Nenhuma função encontrada neste banco de dados." >> $OUTPUT_FILE
fi

echo "" >> $OUTPUT_FILE
echo "## Triggers" >> $OUTPUT_FILE
echo "" >> $OUTPUT_FILE

triggers=$(sudo -u postgres psql -d tradicao -t -c "SELECT tgname FROM pg_trigger WHERE tgisinternal = false ORDER BY tgname;")

if [ -n "$triggers" ]; then
    echo "| Nome do Trigger | Tabela | Evento | Função |" >> $OUTPUT_FILE
    echo "|----------------|--------|--------|--------|" >> $OUTPUT_FILE
    
    for trigger in $triggers; do
        trigger=$(echo $trigger | tr -d ' ')
        table=$(sudo -u postgres psql -d tradicao -t -c "SELECT relname FROM pg_class WHERE oid = (SELECT tgrelid FROM pg_trigger WHERE tgname = '$trigger' AND tgisinternal = false);")
        event=$(sudo -u postgres psql -d tradicao -t -c "SELECT CASE WHEN tgtype & 2 > 0 THEN 'INSERT' WHEN tgtype & 4 > 0 THEN 'DELETE' WHEN tgtype & 8 > 0 THEN 'UPDATE' ELSE 'UNKNOWN' END FROM pg_trigger WHERE tgname = '$trigger' AND tgisinternal = false;")
        function=$(sudo -u postgres psql -d tradicao -t -c "SELECT proname FROM pg_proc WHERE oid = (SELECT tgfoid FROM pg_trigger WHERE tgname = '$trigger' AND tgisinternal = false);")
        
        echo "| $trigger | $table | $event | $function |" >> $OUTPUT_FILE
    done
else
    echo "Nenhum trigger encontrado neste banco de dados." >> $OUTPUT_FILE
fi

echo "" >> $OUTPUT_FILE
echo "## Sequências" >> $OUTPUT_FILE
echo "" >> $OUTPUT_FILE

sequences=$(sudo -u postgres psql -d tradicao -t -c "SELECT sequence_name FROM information_schema.sequences WHERE sequence_schema = 'public' ORDER BY sequence_name;")

if [ -n "$sequences" ]; then
    echo "| Nome da Sequência | Valor Atual | Valor Mínimo | Valor Máximo | Incremento |" >> $OUTPUT_FILE
    echo "|-------------------|-------------|--------------|--------------|------------|" >> $OUTPUT_FILE
    
    for seq in $sequences; do
        seq=$(echo $seq | tr -d ' ')
        current=$(sudo -u postgres psql -d tradicao -t -c "SELECT last_value FROM $seq;")
        min=$(sudo -u postgres psql -d tradicao -t -c "SELECT min_value FROM $seq;")
        max=$(sudo -u postgres psql -d tradicao -t -c "SELECT max_value FROM $seq;")
        increment=$(sudo -u postgres psql -d tradicao -t -c "SELECT increment_by FROM $seq;")
        
        echo "| $seq | $current | $min | $max | $increment |" >> $OUTPUT_FILE
    done
else
    echo "Nenhuma sequência encontrada neste banco de dados." >> $OUTPUT_FILE
fi

echo "Informações do banco de dados 'tradicao' extraídas com sucesso para $OUTPUT_FILE"
