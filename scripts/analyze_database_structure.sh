#!/bin/bash

# Script para analisar a estrutura do banco de dados e comparar com os esquemas ENT
# Uso: sudo ./analyze_database_structure.sh

# Configurações
REPORT_DIR="reports"
mkdir -p $REPORT_DIR

echo "Iniciando análise da estrutura do banco de dados..."
echo "Data/Hora: $(date)"

# Função para analisar um banco de dados
analyze_database() {
    local db_name=$1
    local report_file="$REPORT_DIR/${db_name}_analysis_report.md"
    
    echo "# Relatório de Análise do Banco de Dados $db_name" > $report_file
    echo "Data: $(date)" >> $report_file
    echo "" >> $report_file
    
    echo "## Tabelas Existentes" >> $report_file
    echo "" >> $report_file
    echo "| Tabela | Colunas | Registros |" >> $report_file
    echo "|--------|---------|-----------|" >> $report_file
    
    # Obter lista de tabelas
    tables=$(sudo -u postgres psql -d $db_name -t -c "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;")
    
    # Para cada tabela, obter informações detalhadas
    for table in $tables; do
        table=$(echo $table | tr -d ' ')
        
        # Contar colunas
        column_count=$(sudo -u postgres psql -d $db_name -t -c "SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'public' AND table_name = '$table';")
        
        # Contar registros
        record_count=$(sudo -u postgres psql -d $db_name -t -c "SELECT COUNT(*) FROM $table;")
        
        echo "| $table | $column_count | $record_count |" >> $report_file
        
        # Obter detalhes das colunas
        echo "" >> $report_file
        echo "### Detalhes da Tabela: $table" >> $report_file
        echo "" >> $report_file
        echo "| Coluna | Tipo | Nullable | Default |" >> $report_file
        echo "|--------|------|----------|---------|" >> $report_file
        
        column_details=$(sudo -u postgres psql -d $db_name -t -c "
            SELECT 
                column_name, 
                data_type, 
                is_nullable, 
                column_default
            FROM 
                information_schema.columns 
            WHERE 
                table_schema = 'public' 
                AND table_name = '$table'
            ORDER BY 
                ordinal_position;")
        
        while IFS='|' read -r col_name data_type is_nullable col_default; do
            col_name=$(echo $col_name | tr -d ' ')
            data_type=$(echo $data_type | tr -d ' ')
            is_nullable=$(echo $is_nullable | tr -d ' ')
            col_default=$(echo $col_default | tr -d ' ')
            
            echo "| $col_name | $data_type | $is_nullable | $col_default |" >> $report_file
        done <<< "$column_details"
        
        echo "" >> $report_file
    done
    
    echo "## Relacionamentos entre Tabelas" >> $report_file
    echo "" >> $report_file
    echo "| Tabela | Coluna | Referencia Tabela | Referencia Coluna |" >> $report_file
    echo "|--------|--------|-------------------|-------------------|" >> $report_file
    
    # Obter relacionamentos (chaves estrangeiras)
    relationships=$(sudo -u postgres psql -d $db_name -t -c "
        SELECT
            tc.table_name, 
            kcu.column_name, 
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name 
        FROM 
            information_schema.table_constraints AS tc 
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
              AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
              AND ccu.table_schema = tc.table_schema
        WHERE 
            tc.constraint_type = 'FOREIGN KEY' 
            AND tc.table_schema = 'public';")
    
    while IFS='|' read -r table_name column_name foreign_table foreign_column; do
        table_name=$(echo $table_name | tr -d ' ')
        column_name=$(echo $column_name | tr -d ' ')
        foreign_table=$(echo $foreign_table | tr -d ' ')
        foreign_column=$(echo $foreign_column | tr -d ' ')
        
        echo "| $table_name | $column_name | $foreign_table | $foreign_column |" >> $report_file
    done <<< "$relationships"
    
    echo "" >> $report_file
    echo "Análise concluída para o banco de dados $db_name."
}

# Analisar banco fcobdj_tradicao
echo "Analisando banco de dados fcobdj_tradicao..."
analyze_database "fcobdj_tradicao"

# Analisar banco tradicao
echo "Analisando banco de dados tradicao..."
analyze_database "tradicao"

echo "Análise concluída. Relatórios gerados em $REPORT_DIR"
echo "Data/Hora de conclusão: $(date)"
