#!/bin/bash

# Script para executar a migração da tabela technician_orders
# Este script deve ser executado a partir da raiz do projeto

# Cores para saída
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Função para exibir mensagens de log
log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

# Função para exibir mensagens de erro
error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Função para exibir mensagens de aviso
warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Verificar se o arquivo de migração existe
if [ ! -f "migrations/create_technician_orders.sql" ]; then
    error "Arquivo de migração não encontrado: migrations/create_technician_orders.sql"
    exit 1
fi

# Obter as credenciais do banco de dados
DB_HOST=${DB_HOST:-"postgres-ag-br1-03.conteige.cloud"}
DB_PORT=${DB_PORT:-"54243"}
DB_NAME=${DB_NAME:-"fcobdj_tradicao"}
DB_USER=${DB_USER:-"fcobdj_tradicao"}
DB_PASSWORD=${DB_PASSWORD:-"67573962"}

# Perguntar se o usuário quer usar as credenciais padrão
read -p "Usar credenciais padrão do banco de dados? (s/n): " use_default

if [ "$use_default" != "s" ]; then
    # Solicitar as credenciais do banco de dados
    read -p "Host do banco de dados [$DB_HOST]: " input_host
    read -p "Porta do banco de dados [$DB_PORT]: " input_port
    read -p "Nome do banco de dados [$DB_NAME]: " input_name
    read -p "Usuário do banco de dados [$DB_USER]: " input_user
    read -p "Senha do banco de dados: " input_password

    # Atualizar as credenciais se fornecidas
    DB_HOST=${input_host:-$DB_HOST}
    DB_PORT=${input_port:-$DB_PORT}
    DB_NAME=${input_name:-$DB_NAME}
    DB_USER=${input_user:-$DB_USER}
    DB_PASSWORD=${input_password:-$DB_PASSWORD}
fi

# Exibir as credenciais que serão usadas
log "Usando as seguintes credenciais:"
log "Host: $DB_HOST"
log "Porta: $DB_PORT"
log "Banco de dados: $DB_NAME"
log "Usuário: $DB_USER"

# Confirmar antes de executar
read -p "Deseja continuar com a migração? (s/n): " confirm
if [ "$confirm" != "s" ]; then
    warning "Migração cancelada pelo usuário"
    exit 0
fi

# Executar a migração
log "Executando migração..."
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f migrations/create_technician_orders.sql

# Verificar o resultado
if [ $? -eq 0 ]; then
    log "Migração executada com sucesso!"
else
    error "Erro ao executar a migração"
    exit 1
fi

# Perguntar se o usuário quer verificar os registros criados
read -p "Deseja verificar os registros criados na tabela technician_orders? (s/n): " check_records
if [ "$check_records" = "s" ]; then
    log "Verificando registros..."
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT * FROM technician_orders;"
fi

log "Processo de migração concluído"
exit 0
