#!/bin/bash

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Função para executar migrações
run_migration() {
    local env=$1
    echo -e "${YELLOW}Executando migrações para ambiente: $env${NC}"

    # Verifica se as variáveis de ambiente estão definidas
    if [ -z "$DB_USER" ] || [ -z "$DB_PASS" ]; then
        echo -e "${RED}Erro: Variáveis DB_USER e DB_PASS devem estar definidas${NC}"
        exit 1
    fi

    # Executa o Atlas com as variáveis
    atlas migrate apply \
        --env $env \
        --var "db_user=${DB_USER}" \
        --var "db_pass=${DB_PASS}"

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Migrações executadas com sucesso para $env${NC}"
    else
        echo -e "${RED}Erro ao executar migrações para $env${NC}"
        exit 1
    fi
}

# Verifica se o ambiente foi especificado
if [ -z "$1" ]; then
    echo -e "${RED}Erro: Especifique o ambiente (dev, staging, prod)${NC}"
    exit 1
fi

# Executa as migrações para o ambiente especificado
case $1 in
    "dev"|"staging"|"prod")
        run_migration $1
        ;;
    *)
        echo -e "${RED}Erro: Ambiente inválido. Use dev, staging ou prod${NC}"
        exit 1
        ;;
esac