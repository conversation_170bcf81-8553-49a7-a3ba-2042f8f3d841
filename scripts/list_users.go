package main

import (
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"tradicao/internal/models"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func mainListUsers() {
	// Obter configurações do banco de dados
	host := os.Getenv("DB_HOST")
	port := os.Getenv("DB_PORT")
	user := os.Getenv("DB_USER")
	password := os.Getenv("DB_PASS")
	dbname := os.Getenv("DB_NAME")

	// Usar valores padrão se as variáveis de ambiente não estiverem definidas
	if host == "" || port == "" || user == "" || password == "" || dbname == "" {
		log.Println("Variáveis de ambiente não encontradas, usando configuração padrão")
		host = "postgres-ag-br1-03.conteige.cloud"
		port = "54243"
		user = "fcobdj_tradicao"
		password = "67573962"
		dbname = "fcobdj_tradicao"
	}

	// Construir string de conexão
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable",
		host, user, password, dbname, port)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Buscar todos os usuários
	var users []models.User
	if err := db.Find(&users).Error; err != nil {
		log.Fatalf("Erro ao buscar usuários: %v", err)
	}

	// Exibir informações dos usuários no console
	fmt.Println("=== Usuários do Sistema ===")
	fmt.Println("ID\tNome\tEmail\tPerfil\tFilial")
	fmt.Println("--------------------------------------------------")
	for _, user := range users {
		branchID := "N/A"
		if user.BranchID != nil {
			branchID = fmt.Sprintf("%d", *user.BranchID)
		}
		fmt.Printf("%d\t%s\t%s\t%s\t%s\n", user.ID, user.Name, user.Email, user.Role, branchID)
	}

	// Exibir mensagem se não houver usuários
	if len(users) == 0 {
		fmt.Println("\nNenhum usuário encontrado no sistema.")
		return
	}

	// Criar diretório docs se não existir
	if _, err := os.Stat("docs"); os.IsNotExist(err) {
		os.Mkdir("docs", 0755)
	}

	// Criar arquivo de documentação
	file, err := os.Create("docs/usuarios_cadastrados.md")
	if err != nil {
		log.Fatalf("Erro ao criar arquivo de documentação: %v", err)
	}
	defer file.Close()

	// Escrever cabeçalho
	now := time.Now()
	fmt.Fprintf(file, "# Usuários Cadastrados no Sistema\n\n")
	fmt.Fprintf(file, "Data de geração: %s\n\n", now.Format("02/01/2006 15:04:05"))
	fmt.Fprintf(file, "Total de usuários: %d\n\n", len(users))

	// Escrever tabela de usuários
	fmt.Fprintf(file, "| ID | Nome | Email | Perfil | Filial | Status | Último Acesso |\n")
	fmt.Fprintf(file, "|---|---|---|---|---|---|---|\n")

	// Mapear perfis para descrições mais amigáveis
	roleDescriptions := map[string]string{
		"admin":       "Administrador",
		"gerente":     "Gerente",
		"financeiro":  "Financeiro",
		"tecnico":     "Técnico",
		"technician":  "Técnico",
		"filial":      "Filial",
		"prestador":   "Prestador",
		"provider":    "Prestador",
		"branch_user": "Usuário de Filial",
	}

	// Consultar todas as filiais
	var branches []models.Branch
	db.Find(&branches)

	// Mapear filiais por ID
	branchMap := make(map[uint]string)
	for _, branch := range branches {
		branchMap[branch.ID] = branch.Name
	}

	// Escrever dados dos usuários
	for _, user := range users {
		// Obter descrição do perfil
		roleStr := string(user.Role)
		roleDesc := roleDescriptions[roleStr]
		if roleDesc == "" {
			roleDesc = roleStr
		}

		// Formatar status
		status := "Ativo"
		if user.Blocked {
			status = "Bloqueado"
		}

		// Formatar último acesso (usando UpdatedAt como substituto para LastLogin)
		lastAccess := "Nunca"
		if !user.UpdatedAt.IsZero() {
			lastAccess = user.UpdatedAt.Format("02/01/2006 15:04")
		}

		// Formatar filial
		filial := "N/A"
		if user.BranchID != nil && *user.BranchID > 0 {
			filial = branchMap[*user.BranchID]
			if filial == "" {
				filial = fmt.Sprintf("Filial ID %d", *user.BranchID)
			}
		}

		fmt.Fprintf(file, "| %d | %s | %s | %s | %s | %s | %s |\n",
			user.ID, user.Name, user.Email, roleDesc, filial, status, lastAccess)
	}

	// Adicionar seção de estatísticas
	fmt.Fprintf(file, "\n## Estatísticas por Perfil\n\n")

	// Contar usuários por perfil
	roleCounts := make(map[string]int)
	for _, user := range users {
		roleStr := string(user.Role)
		roleCounts[roleStr]++
	}

	// Escrever estatísticas
	fmt.Fprintf(file, "| Perfil | Quantidade | Porcentagem |\n")
	fmt.Fprintf(file, "|---|---|---|\n")

	for role, count := range roleCounts {
		// Obter descrição do perfil
		roleDesc := roleDescriptions[role]
		if roleDesc == "" {
			roleDesc = role
		}

		percentage := float64(count) / float64(len(users)) * 100
		fmt.Fprintf(file, "| %s | %d | %.1f%% |\n", roleDesc, count, percentage)
	}

	// Adicionar seção de usuários por status
	fmt.Fprintf(file, "\n## Estatísticas por Status\n\n")

	// Contar usuários por status
	activeCount := 0
	blockedCount := 0
	for _, user := range users {
		if user.Blocked {
			blockedCount++
		} else {
			activeCount++
		}
	}

	// Escrever estatísticas de status
	fmt.Fprintf(file, "| Status | Quantidade | Porcentagem |\n")
	fmt.Fprintf(file, "|---|---|---|\n")
	fmt.Fprintf(file, "| Ativo | %d | %.1f%% |\n", activeCount, float64(activeCount)/float64(len(users))*100)
	fmt.Fprintf(file, "| Bloqueado | %d | %.1f%% |\n", blockedCount, float64(blockedCount)/float64(len(users))*100)

	// Adicionar seção de usuários por filial
	fmt.Fprintf(file, "\n## Usuários por Filial\n\n")

	// Contar usuários por filial
	branchCounts := make(map[uint]int)
	for _, user := range users {
		if user.BranchID != nil {
			branchCounts[*user.BranchID]++
		} else {
			branchCounts[0]++
		}
	}

	// Escrever estatísticas de filiais
	fmt.Fprintf(file, "| Filial | Quantidade | Porcentagem |\n")
	fmt.Fprintf(file, "|---|---|---|\n")

	for branchID, count := range branchCounts {
		branchName := branchMap[branchID]
		if branchName == "" {
			if branchID == 0 {
				branchName = "Sem filial"
			} else {
				branchName = fmt.Sprintf("Filial ID %d", branchID)
			}
		}

		percentage := float64(count) / float64(len(users)) * 100
		fmt.Fprintf(file, "| %s | %d | %.1f%% |\n", branchName, count, percentage)
	}

	// Adicionar seção de técnicos
	fmt.Fprintf(file, "\n## Lista de Técnicos\n\n")
	fmt.Fprintf(file, "| ID | Nome | Email | Filial | Status | Último Acesso |\n")
	fmt.Fprintf(file, "|---|---|---|---|---|---|\n")

	// Filtrar apenas técnicos
	for _, user := range users {
		roleStr := string(user.Role)
		if strings.ToLower(roleStr) == "tecnico" || strings.ToLower(roleStr) == "technician" {
			// Formatar status
			status := "Ativo"
			if user.Blocked {
				status = "Bloqueado"
			}

			// Formatar último acesso
			lastAccess := "Nunca"
			if !user.UpdatedAt.IsZero() {
				lastAccess = user.UpdatedAt.Format("02/01/2006 15:04")
			}

			// Formatar filial
			filial := "N/A"
			if user.BranchID != nil && *user.BranchID > 0 {
				filial = branchMap[*user.BranchID]
				if filial == "" {
					filial = fmt.Sprintf("Filial ID %d", *user.BranchID)
				}
			}

			fmt.Fprintf(file, "| %d | %s | %s | %s | %s | %s |\n",
				user.ID, user.Name, user.Email, filial, status, lastAccess)
		}
	}

	fmt.Println("\nArquivo de documentação gerado com sucesso: docs/usuarios_cadastrados.md")
}
