package main

import (
	"fmt"
	"log"
	"os"
	"tradicao/internal/permissions"
)

func main() {
	// Configurar logs
	log.SetFlags(log.Ldate | log.Ltime | log.Lmicroseconds)

	// Verificar argumentos
	if len(os.Args) < 3 {
		fmt.Println("Uso: go run scripts/test_permissions.go <tipo> <caminho> <perfil>")
		fmt.Println("  tipo: 'page' ou 'api'")
		fmt.Println("  caminho: caminho da página ou API (ex: 'dashboard', 'api/user/me')")
		fmt.Println("  perfil: perfil do usuário (ex: 'admin', 'gerente', 'tecnico')")
		os.Exit(1)
	}

	// Obter argumentos
	permType := os.Args[1]
	path := os.Args[2]
	role := ""
	if len(os.Args) > 3 {
		role = os.Args[3]
	}

	// Carregar configuração de permissões
	config, err := permissions.LoadConfig("data/permissions.yaml")
	if err != nil {
		log.Fatalf("Erro ao carregar configuração de permissões: %v", err)
	}

	// Exibir configuração
	fmt.Println("Configuração de permissões carregada com sucesso")
	fmt.Printf("Papéis definidos: %d\n", len(config.Roles))
	fmt.Printf("Páginas públicas: %d\n", len(config.PublicPages))
	fmt.Printf("APIs públicas: %d\n", len(config.PublicAPIs))

	// Criar serviço de permissões
	service, err := permissions.NewService("data/permissions.yaml")
	if err != nil {
		log.Fatalf("Erro ao criar serviço de permissões: %v", err)
	}

	// Verificar permissão
	if role != "" {
		var permTypeEnum permissions.PermissionType
		if permType == "page" {
			permTypeEnum = permissions.PagePermission
		} else {
			permTypeEnum = permissions.APIPermission
		}

		hasPermission := service.HasPermission(role, path, permTypeEnum)
		fmt.Printf("Perfil '%s' tem permissão para acessar '%s'? %v\n", role, path, hasPermission)
	}

	// Listar perfis com permissão
	var permTypeEnum permissions.PermissionType
	if permType == "page" {
		permTypeEnum = permissions.PagePermission
	} else {
		permTypeEnum = permissions.APIPermission
	}

	permittedRoles := service.GetPermittedRoles(path, permTypeEnum)
	fmt.Printf("Perfis com permissão para acessar '%s': %v\n", path, permittedRoles)
}
