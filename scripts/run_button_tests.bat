@echo off
setlocal enabledelayedexpansion

REM Cores para saída (não funcionam em todos os terminais Windows)
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "NC=[0m"

REM Credenciais padrão
set "EMAIL=<EMAIL>"
set "SENHA=i1t2@3l4O5"
set "PAGE=/financeiro"

REM Verificar se foram fornecidas credenciais como parâmetros
if not "%~1"=="" (
    set "PAGE=%~1"
    echo %YELLOW%Testando página: %PAGE%%NC%
)

if not "%~2"=="" (
    set "EMAIL=%~2"
    echo %YELLOW%Usando email/usuário: %EMAIL%%NC%
)

if not "%~3"=="" (
    set "SENHA=%~3"
    echo %YELLOW%Usando senha personalizada%NC%
)

echo %YELLOW%Iniciando testes de funcionalidade dos botões...%NC%

REM Diretório do projeto
set "PROJECT_DIR=%cd%"
echo Diretório do projeto: %PROJECT_DIR%

REM Verificar se o Chrome/Chromium está instalado
where chrome >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    where chromium >nul 2>&1
    if %ERRORLEVEL% NEQ 0 (
        echo %RED%Chrome/Chromium não encontrado. Por favor, instale para executar os testes.%NC%
        echo Você pode baixar em: https://www.google.com/chrome/
        exit /b 1
    )
)

REM Verificar se as dependências já estão instaladas
echo %YELLOW%Verificando dependências do teste...%NC%
findstr /C:"github.com/chromedp/chromedp" tests\visual\go.mod >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo %YELLOW%Instalando dependências do teste...%NC%
    cd tests\visual && go mod tidy
    cd ..\..\
) else (
    echo %GREEN%Dependências já instaladas.%NC%
)

REM Verificar se o servidor já está rodando
echo %YELLOW%Verificando se o servidor já está rodando...%NC%
curl -s -o nul -w "%%{http_code}" http://localhost:8080/login >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo %GREEN%Servidor já está rodando.%NC%
    set "SERVER_ALREADY_RUNNING=true"
) else (
    echo %RED%ATENÇÃO: O servidor não está rodando. Por favor, inicie o servidor em outro terminal com:%NC%
    echo %YELLOW%go run cmd\main.go%NC%
    echo %RED%Abortando teste...%NC%
    exit /b 1
)

REM Criar diretório para screenshots
if not exist tests\visual\screenshots mkdir tests\visual\screenshots

REM Executar os testes de botões
echo %YELLOW%Executando testes de botões...%NC%
cd tests\visual && set "VISUAL_TEST_EMAIL=%EMAIL%" && set "VISUAL_TEST_PASSWORD=%SENHA%" && set "VISUAL_TEST_PAGE=%PAGE%" && go test -v -run TestButtonsFunctionality

REM Capturar resultado dos testes
set "TEST_RESULT=%ERRORLEVEL%"

REM Não encerrar o servidor, pois ele foi iniciado manualmente
echo %GREEN%Mantendo o servidor rodando...%NC%

REM Voltar para o diretório do projeto
cd %PROJECT_DIR%

REM Verificar resultado dos testes
if %TEST_RESULT% EQU 0 (
    echo %GREEN%Testes de botões concluídos com sucesso!%NC%
    echo %YELLOW%Screenshots salvos em: tests\visual\screenshots\%NC%
) else (
    echo %RED%Testes de botões falharam.%NC%
)

exit /b %TEST_RESULT%
