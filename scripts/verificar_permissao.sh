#!/bin/bash

# Script para verificar permissões de uma página específica
# Uso: ./verificar_permissao.sh <pagina> <perfil>

# Verificar argumentos
if [ $# -lt 2 ]; then
    echo "Uso: $0 <pagina> <perfil>"
    echo "Exemplo: $0 ordemtecnica tecnico"
    exit 1
fi

PAGINA=$1
PERFIL=$2

# Remover a barra inicial, se houver
PAGINA=${PAGINA#/}

# Executar o script Go para verificar a permissão
cd /root/projeto_linux
go run scripts/check_page_permission.go "$PAGINA" "$PERFIL"

# Verificar se o usuário existe no banco de dados
echo -e "\nVerificando usuário com perfil '$PERFIL'..."
USUARIO=$(go run scripts/check_user_role.go "${PERFIL}@tradicao.com" 2>/dev/null)

if [ $? -eq 0 ]; then
    echo "$USUARIO"
else
    echo "Usuário com email ${PERFIL}@tradicao.com não encontrado no banco de dados."
fi

# Verificar se a rota está configurada no servidor
echo -e "\nVerificando configuração da rota no servidor..."
ROTA=$(grep -n "/$PAGINA" cmd/main.go | head -n 1)

if [ -n "$ROTA" ]; then
    echo "Rota encontrada no arquivo main.go:"
    echo "$ROTA"
    
    # Extrair o número da linha
    LINHA=$(echo "$ROTA" | cut -d':' -f1)
    
    # Mostrar o contexto da rota (5 linhas antes e depois)
    echo -e "\nContexto da rota:"
    sed -n "$((LINHA-5)),$((LINHA+5))p" cmd/main.go
else
    echo "Rota '/$PAGINA' não encontrada no arquivo main.go."
fi

echo -e "\nVerificando template associado à rota..."
TEMPLATE=$(grep -n "ManutencaoOrdem.html\|Ordemtecnico.html" cmd/main.go | grep -i "$PAGINA")

if [ -n "$TEMPLATE" ]; then
    echo "Template encontrado:"
    echo "$TEMPLATE"
else
    echo "Nenhum template específico encontrado para a rota '/$PAGINA'."
fi

echo -e "\nRecomendações:"
echo "1. Verifique se a página '$PAGINA' está corretamente definida no arquivo data/permissoes_paginas.md"
echo "2. Verifique se o perfil '$PERFIL' está na lista de perfis com acesso à página"
echo "3. Verifique se a rota está corretamente configurada no arquivo cmd/main.go"
echo "4. Verifique se o template associado à rota existe e está correto"
echo "5. Reinicie o servidor após fazer alterações no arquivo de permissões"
