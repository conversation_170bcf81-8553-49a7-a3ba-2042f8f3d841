//go:build screenshot

package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/chromedp/chromedp"
)

func main() {
	// Criar diretório para screenshots se não existir
	screenshotDir := "screenshots"
	if _, err := os.Stat(screenshotDir); os.IsNotExist(err) {
		os.Mkdir(screenshotDir, 0755)
	}

	// Configurar opções do Chrome
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", true),
		chromedp.Flag("disable-gpu", true),
		chromedp.Flag("no-sandbox", true),
		chromedp.Flag("disable-dev-shm-usage", true),
		chromedp.WindowSize(1280, 800),
	)

	// Criar contexto do navegador
	allocCtx, cancel := chromedp.NewExecAllocator(context.Background(), opts...)
	defer cancel()

	ctx, cancel := chromedp.NewContext(allocCtx)
	defer cancel()

	// Definir timeout
	ctx, cancel = context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// Fazer login no sistema
	fmt.Println("Fazendo login no sistema...")
	email := "<EMAIL>"
	senha := "i1t2@3l4O5"

	var buf []byte
	var title string

	// Fazer login
	err := chromedp.Run(ctx,
		// Navegar para a página de login
		chromedp.Navigate("http://localhost:8080/login"),
		chromedp.Sleep(2*time.Second),

		// Preencher o formulário de login
		chromedp.Clear(`input[name="email"]`, chromedp.ByQuery),
		chromedp.SendKeys(`input[name="email"]`, email, chromedp.ByQuery),
		chromedp.Clear(`input[name="password"]`, chromedp.ByQuery),
		chromedp.SendKeys(`input[name="password"]`, senha, chromedp.ByQuery),

		// Clicar no botão de login
		chromedp.Click(`button[type="submit"]`, chromedp.ByQuery),
		chromedp.Sleep(5*time.Second),
	)

	if err != nil {
		log.Fatalf("Erro ao fazer login: %v", err)
	}

	// Navegar para a página de galeria
	fmt.Println("Navegando para a página de galeria...")
	err = chromedp.Run(ctx,
		chromedp.Navigate("http://localhost:8080/galeria"),
		chromedp.Sleep(5*time.Second), // Aguardar carregamento completo
		chromedp.Title(&title),
		chromedp.CaptureScreenshot(&buf),
	)

	if err != nil {
		log.Fatalf("Erro ao navegar para a página da galeria: %v", err)
	}

	// Salvar screenshot
	filename := fmt.Sprintf("%s/galeria_screenshot.png", screenshotDir)
	if err := os.WriteFile(filename, buf, 0644); err != nil {
		log.Fatalf("Erro ao salvar screenshot: %v", err)
	}

	fmt.Printf("Título da página: %s\n", title)
	fmt.Printf("Screenshot salvo em: %s\n", filename)
}
