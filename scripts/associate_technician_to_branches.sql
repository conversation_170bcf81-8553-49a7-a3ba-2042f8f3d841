-- Criar uma especialidade padrão se não existir nenhuma
INSERT INTO technician_specialties (name, description, equipment_types, created_at, updated_at)
VALUES ('Manutenção Geral', 'Especialista em manutenção geral de equipamentos', ARRAY['bomba', 'tanque', 'compressor'], NOW(), NOW())
ON CONFLICT DO NOTHING;

-- Obter o ID do técnico
SELECT id FROM users WHERE email = '<EMAIL>';

-- Obter o ID da especialidade
SELECT id FROM technician_specialties LIMIT 1;
