package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
	_ "github.com/lib/pq"
)

func main() {
	// Carregar variáveis de ambiente
	err := godotenv.Load()
	if err != nil {
		log.Println("Erro ao carregar arquivo .env:", err)
	}

	// Obter configurações do banco de dados
	dbUser := os.Getenv("DB_USER")
	dbPass := os.Getenv("DB_PASS")
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbName := os.Getenv("DB_NAME")

	// Construir string de conexão
	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPass, dbName)

	// Conectar ao banco de dados
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		log.Fatal("Erro ao conectar ao banco de dados:", err)
	}
	defer db.Close()

	// Verificar conexão
	err = db.Ping()
	if err != nil {
		log.Fatal("Erro ao verificar conexão com o banco de dados:", err)
	}

	fmt.Println("Conexão com o banco de dados estabelecida com sucesso!")

	// Consultar estrutura da tabela equipment
	rows, err := db.Query(`
		SELECT column_name, data_type, is_nullable
		FROM information_schema.columns
		WHERE table_name = 'equipment'
		ORDER BY ordinal_position
	`)
	if err != nil {
		log.Fatal("Erro ao consultar estrutura da tabela:", err)
	}
	defer rows.Close()

	// Exibir estrutura da tabela
	fmt.Println("\nEstrutura da tabela equipment:")
	fmt.Println("-----------------------------")
	fmt.Printf("%-20s %-20s %-10s\n", "Coluna", "Tipo", "Nullable")
	fmt.Println("-----------------------------")

	for rows.Next() {
		var columnName, dataType, isNullable string
		if err := rows.Scan(&columnName, &dataType, &isNullable); err != nil {
			log.Fatal("Erro ao ler dados da consulta:", err)
		}
		fmt.Printf("%-20s %-20s %-10s\n", columnName, dataType, isNullable)
	}

	if err := rows.Err(); err != nil {
		log.Fatal("Erro ao iterar sobre resultados:", err)
	}
}
