# Script para configurar o acesso ao website do WSL2 a partir de outros computadores na rede
# Este script deve ser executado no PowerShell como administrador no Windows host

# Obter o IP do WSL2
Write-Host "Obtendo o IP do WSL2..." -ForegroundColor Green
$wslIP = (wsl hostname -I).Trim()
Write-Host "IP do WSL2: $wslIP" -ForegroundColor Cyan

# Verificar se já existe um redirecionamento de porta
$existingConfig = netsh interface portproxy show all | Select-String "8080"
if ($existingConfig) {
    Write-Host "Removendo configuração de redirecionamento de porta existente..." -ForegroundColor Yellow
    netsh interface portproxy delete v4tov4 listenport=8080 listenaddress=0.0.0.0
}

# Configurar o redirecionamento de porta (porta 8080 do Windows -> porta 8080 do WSL2)
Write-Host "Configurando redirecionamento de porta..." -ForegroundColor Green
netsh interface portproxy add v4tov4 listenport=8080 listenaddress=0.0.0.0 connectport=8080 connectaddress=$wslIP

# Verificar se a regra de firewall já existe
$firewallRule = Get-NetFirewallRule -DisplayName "WSL2 Web Server" -ErrorAction SilentlyContinue
if (-not $firewallRule) {
    # Permitir a porta no firewall do Windows
    Write-Host "Configurando regra de firewall..." -ForegroundColor Green
    New-NetFirewallRule -DisplayName "WSL2 Web Server" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 8080
} else {
    Write-Host "Regra de firewall já existe." -ForegroundColor Cyan
}

# Obter o IP do Windows host
$windowsIP = (Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias Ethernet).IPAddress
if (-not $windowsIP) {
    $windowsIP = (Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias Wi-Fi).IPAddress
}

Write-Host "`nConfiguração concluída!" -ForegroundColor Green
Write-Host "O website agora deve estar acessível em: http://$windowsIP`:8080" -ForegroundColor Cyan
Write-Host "`nPara verificar a configuração de redirecionamento de porta:" -ForegroundColor Yellow
Write-Host "netsh interface portproxy show all" -ForegroundColor Gray

Write-Host "`nPara remover esta configuração, execute:" -ForegroundColor Yellow
Write-Host "netsh interface portproxy delete v4tov4 listenport=8080 listenaddress=0.0.0.0" -ForegroundColor Gray
Write-Host "Remove-NetFirewallRule -DisplayName `"WSL2 Web Server`"" -ForegroundColor Gray
