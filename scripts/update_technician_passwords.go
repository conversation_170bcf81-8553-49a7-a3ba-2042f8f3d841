package main

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// User representa um usuário no sistema (versão simplificada)
type User struct {
	ID       uint   `json:"id" gorm:"primaryKey"`
	Name     string `json:"name"`
	Email    string `json:"email" gorm:"unique"`
	Password string `json:"password"`
	Type     string `json:"type" gorm:"column:type"` // Role do usuário
}

func main() {
	// Carregar variáveis de ambiente
	if err := godotenv.Load(); err != nil {
		log.Println("Aviso: Arquivo .env não encontrado, usando valores padrão")
	}

	// Obter configurações do banco de dados
	host := os.Getenv("DB_HOST")
	port := os.Getenv("DB_PORT")
	user := os.Getenv("DB_USER")
	password := os.Getenv("DB_PASS")
	dbname := os.Getenv("DB_NAME")

	// Verificar se estamos tentando usar um banco de dados local
	if host == "localhost" || host == "127.0.0.1" {
		log.Fatal("ERRO: Uso de banco de dados local é EXPRESSAMENTE PROIBIDO! Use apenas o banco de dados remoto configurado no .env")
	}

	// Usar valores padrão se as variáveis de ambiente não estiverem definidas
	if host == "" || port == "" || user == "" || password == "" || dbname == "" {
		log.Println("Variáveis de ambiente não encontradas, usando configuração padrão")
		host = "postgres-ag-br1-03.conteige.cloud"
		port = "54243"
		user = "fcobdj_tradicao"
		password = "67573962"
		dbname = "fcobdj_tradicao"
	}

	// Construir string de conexão
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable",
		host, user, password, dbname, port)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Buscar todos os usuários técnicos
	var technicians []User
	if err := db.Where("type = ?", "tecnico").Find(&technicians).Error; err != nil {
		log.Fatalf("Erro ao buscar técnicos: %v", err)
	}

	fmt.Printf("Encontrados %d técnicos\n", len(technicians))

	// Senha padrão para todos os técnicos
	defaultPassword := "tradicaosistema"

	// Atualizar a senha de cada técnico
	for _, tech := range technicians {
		// Verificar se a senha já é um hash bcrypt válido
		err := bcrypt.CompareHashAndPassword([]byte(tech.Password), []byte(defaultPassword))
		if err == nil {
			fmt.Printf("Técnico %s (ID: %d) já tem um hash bcrypt válido\n", tech.Email, tech.ID)
			continue
		}

		// Gerar novo hash da senha
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(defaultPassword), bcrypt.DefaultCost)
		if err != nil {
			fmt.Printf("Erro ao gerar hash da senha para técnico %s: %v\n", tech.Email, err)
			continue
		}

		// Atualizar a senha no banco de dados
		if err := db.Model(&User{}).Where("id = ?", tech.ID).Update("password", string(hashedPassword)).Error; err != nil {
			fmt.Printf("Erro ao atualizar senha do técnico %s: %v\n", tech.Email, err)
			continue
		}

		fmt.Printf("Senha atualizada com sucesso para técnico %s (ID: %d)\n", tech.Email, tech.ID)
	}

	fmt.Println("\nProcesso concluído!")
}
