package main

import (
	"os"
	"path/filepath"
	"strings"
)

// findTemplateFiles encontra todos os arquivos de template HTML
func findTemplateFiles(templatesDir string) ([]string, error) {
	var templateFiles []string

	err := filepath.Walk(templatesDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() && strings.HasSuffix(info.Name(), ".html") {
			// Ignorar templates na pasta de backup
			if !strings.Contains(path, "/backup/") {
				templateFiles = append(templateFiles, path)
			}
		}

		return nil
	})

	return templateFiles, err
}

// isPartialTemplate verifica se um template é parcial (usado com 'define')
func isPartialTemplate(path string) bool {
	content, err := os.ReadFile(path)
	if err != nil {
		return false
	}

	// Verifica se o arquivo contém uma definição de template
	return strings.Contains(string(content), "{{ define ") || strings.Contains(string(content), "{{define ")
}
