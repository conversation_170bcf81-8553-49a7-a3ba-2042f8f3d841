//go:build extrair_filiais

package main

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"

	"github.com/PuerkitoBio/goquery"
)

const htmlContent = `
<!-- Cole aqui o HTML fornecido pelo usuário -->
<div class="unidade" id="unidade-1">
    <h3 class="nome-filial">Posto Shell - Centro</h3>
    <p class="endereco">Rua das Flores, 123 - Centro</p>
</div>
<div class="unidade" id="unidade-2">
    <h3 class="nome-filial">Posto Shell - Jardim</h3>
    <p class="endereco">Av. Brasil, 456 - Jardim América</p>
</div>
<!-- Adicione aqui o restante do HTML fornecido -->
`

type Unidade struct {
	ID       string `json:"id"`
	Nome     string `json:"nome"`
	Endereco string `json:"endereco"`
}

func main() {
	// Carregar o HTML no goquery
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlContent))
	if err != nil {
		log.Fatalf("Erro ao carregar HTML: %v", err)
	}

	var unidades []Unidade

	// Selecionar cada div com class "unidade"
	doc.Find("div.unidade").Each(func(i int, s *goquery.Selection) {
		id, exists := s.Attr("id")
		if !exists {
			id = ""
		}
		nome := s.Find("h3.nome-filial").Text()
		endereco := s.Find("p.endereco").Text()

		unidade := Unidade{
			ID:       id,
			Nome:     strings.TrimSpace(nome),
			Endereco: strings.TrimSpace(endereco),
		}
		unidades = append(unidades, unidade)
	})

	// Converter para JSON e imprimir
	jsonData, err := json.MarshalIndent(unidades, "", "  ")
	if err != nil {
		log.Fatalf("Erro ao converter para JSON: %v", err)
	}

	fmt.Println(string(jsonData))
}
