#!/bin/bash

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Função para criar backup
create_backup() {
    local env=$1
    local db_name="tradicao"
    if [ "$env" != "prod" ]; then
        db_name="${db_name}_${env}"
    fi
    
    echo -e "${YELLOW}Criando backup do ambiente $env...${NC}"
    
    # Cria diretório de backup se não existir
    mkdir -p "backups/$env"
    
    # Nome do arquivo de backup
    local backup_file="backups/$env/backup_$(date +%Y%m%d_%H%M%S).sql"
    
    # Executa o backup
    PGPASSWORD=$DB_PASS pg_dump -U $DB_USER -h localhost -d $db_name > $backup_file
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Backup criado com sucesso: $backup_file${NC}"
        
        # Limpa backups antigos
        find "backups/$env" -type f -name "backup_*.sql" -mtime +7 -delete
    else
        echo -e "${RED}Erro ao criar backup${NC}"
        exit 1
    fi
}

# Função para restaurar backup
restore_backup() {
    local env=$1
    local backup_file=$2
    local db_name="tradicao"
    if [ "$env" != "prod" ]; then
        db_name="${db_name}_${env}"
    fi
    
    echo -e "${YELLOW}Restaurando backup para ambiente $env...${NC}"
    
    # Verifica se o arquivo existe
    if [ ! -f "$backup_file" ]; then
        echo -e "${RED}Arquivo de backup não encontrado: $backup_file${NC}"
        exit 1
    fi
    
    # Cria banco de dados se não existir
    PGPASSWORD=$DB_PASS psql -U $DB_USER -h localhost -c "CREATE DATABASE $db_name;" 2>/dev/null
    
    # Restaura o backup
    PGPASSWORD=$DB_PASS psql -U $DB_USER -h localhost -d $db_name < $backup_file
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Backup restaurado com sucesso${NC}"
    else
        echo -e "${RED}Erro ao restaurar backup${NC}"
        exit 1
    fi
}

# Função para listar backups
list_backups() {
    local env=$1
    echo -e "${YELLOW}Listando backups do ambiente $env...${NC}"
    
    if [ ! -d "backups/$env" ]; then
        echo -e "${RED}Nenhum backup encontrado para o ambiente $env${NC}"
        return
    fi
    
    ls -lh "backups/$env/backup_*.sql" 2>/dev/null || echo -e "${RED}Nenhum backup encontrado${NC}"
}

# Verifica se as variáveis de ambiente estão definidas
if [ -z "$DB_USER" ] || [ -z "$DB_PASS" ]; then
    echo -e "${RED}Erro: Variáveis DB_USER e DB_PASS devem estar definidas${NC}"
    exit 1
fi

# Verifica o comando
case $1 in
    "backup")
        if [ -z "$2" ]; then
            echo -e "${RED}Erro: Especifique o ambiente (dev, staging, prod)${NC}"
            exit 1
        fi
        create_backup $2
        ;;
    "restore")
        if [ -z "$2" ] || [ -z "$3" ]; then
            echo -e "${RED}Erro: Especifique o ambiente e o arquivo de backup${NC}"
            exit 1
        fi
        restore_backup $2 $3
        ;;
    "list")
        if [ -z "$2" ]; then
            echo -e "${RED}Erro: Especifique o ambiente (dev, staging, prod)${NC}"
            exit 1
        fi
        list_backups $2
        ;;
    *)
        echo -e "${RED}Uso: $0 {backup|restore|list} [ambiente] [arquivo]${NC}"
        echo -e "Exemplos:"
        echo -e "  $0 backup dev"
        echo -e "  $0 restore dev backups/dev/backup_20240324.sql"
        echo -e "  $0 list dev"
        exit 1
        ;;
esac 