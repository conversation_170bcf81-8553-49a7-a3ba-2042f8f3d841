package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"
)

// Estrutura para armazenar o token JWT
type AuthResponse struct {
	Token string `json:"token"`
}

func runTestApi() {
	// Configuração do teste
	baseURL := "http://0.0.0.0:5000"

	// Verifica se o servidor está rodando
	fmt.Println("Testando API e componentes do aplicativo...")
	fmt.Println("Verificando se o servidor está rodando...")

	// Aguardar até 5 segundos para o servidor estar pronto
	var statusCode int
	for range 5 {
		resp, err := http.Get(baseURL)
		if err == nil {
			statusCode = resp.StatusCode
			resp.Body.Close()
			break
		}
		fmt.Println("Servidor não está respondendo, tentando novamente em 1 segundo...")
		time.Sleep(1 * time.Second)
	}

	if statusCode != 200 {
		fmt.Println("❌ Servidor não está respondendo. Certifique-se de que está rodando com 'go run cmd/main.go'")
		os.Exit(1)
	}

	fmt.Println("✅ Servidor está respondendo!")

	// Teste 1: Tentar fazer login
	fmt.Println("\nTestando login na API...")

	loginPayload := map[string]string{
		"username": "<EMAIL>",
		"password": "i1t2@3l4O5",
	}

	payloadBytes, _ := json.Marshal(loginPayload)

	loginResp, err := http.Post(
		baseURL+"/api/auth/login",
		"application/json",
		bytes.NewBuffer(payloadBytes),
	)

	if err != nil {
		fmt.Printf("❌ Erro ao tentar login: %v\n", err)
	} else {
		defer loginResp.Body.Close()

		if loginResp.StatusCode == 200 {
			fmt.Println("✅ Login realizado com sucesso!")

			body, _ := io.ReadAll(loginResp.Body)
			var authResp AuthResponse
			json.Unmarshal(body, &authResp)

			// Se o login foi bem-sucedido e temos um token, testar um endpoint protegido
			if authResp.Token != "" {
				fmt.Println("\nTestando endpoint protegido com token JWT...")

				req, _ := http.NewRequest("GET", baseURL+"/api/user/me", nil)
				req.Header.Add("Authorization", "Bearer "+authResp.Token)

				client := &http.Client{}
				resp, err := client.Do(req)

				if err != nil {
					fmt.Printf("❌ Erro ao acessar endpoint protegido: %v\n", err)
				} else {
					defer resp.Body.Close()

					if resp.StatusCode == 200 {
						fmt.Println("✅ Endpoint protegido acessado com sucesso!")
						userInfo, _ := io.ReadAll(resp.Body)
						fmt.Println("   Informações do usuário:", string(userInfo))
					} else {
						fmt.Printf("❌ Falha ao acessar endpoint protegido. Status: %d\n", resp.StatusCode)
					}
				}
			}
		} else {
			fmt.Printf("❌ Falha no login. Status: %d\n", loginResp.StatusCode)
			body, _ := io.ReadAll(loginResp.Body)
			fmt.Println("   Resposta:", string(body))
		}
	}

	// Teste 2: Testar rota pública
	fmt.Println("\nTestando rota pública...")
	resp, err := http.Get(baseURL + "/login")

	if err != nil {
		fmt.Printf("❌ Erro ao acessar rota pública: %v\n", err)
	} else {
		defer resp.Body.Close()

		if resp.StatusCode == 200 {
			fmt.Println("✅ Rota pública acessada com sucesso!")
		} else {
			fmt.Printf("❌ Falha ao acessar rota pública. Status: %d\n", resp.StatusCode)
		}
	}

	fmt.Println("\n----- Resumo dos testes da API -----")
	fmt.Println("✅ Verificação do servidor concluída")
	fmt.Println("Testes de API concluídos!")
}
