package main

import (
	"fmt"
	"log"
	"os"
	"tradicao/internal/models"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Uso: go run scripts/check_user.go <email>")
		os.Exit(1)
	}

	email := os.Args[1]

	// Conectar ao banco de dados diretamente
	dsn := "postgresql://fcobdj_tradicao:<EMAIL>:54243/fcobdj_tradicao?sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Buscar usuário pelo email
	var user models.User
	result := db.Where("email = ?", email).First(&user)
	if result.Error != nil {
		fmt.Printf("Erro ao buscar usuário com email %s: %v\n", email, result.Error)
		os.Exit(1)
	}

	fmt.Printf("Usuário encontrado:\n")
	fmt.Printf("ID: %d\n", user.ID)
	fmt.Printf("Nome: %s\n", user.Name)
	fmt.Printf("Email: %s\n", user.Email)
	fmt.Printf("Perfil: %s\n", user.Role)
	if user.BranchID != nil {
		fmt.Printf("Filial ID: %d\n", *user.BranchID)
	} else {
		fmt.Printf("Filial ID: Não associado a uma filial\n")
	}
	fmt.Printf("Bloqueado: %v\n", user.Blocked)
	fmt.Printf("Tentativas falhas: %d\n", user.FailedAttempts)
	fmt.Printf("2FA habilitado: %v\n", user.TOTPEnabled)
}
