package main

import (
	"flag"
	"fmt"
	"os"
)

// runMainScript é uma função de exemplo que não é mais usada
// Mantida aqui para referência futura
func runMainScript() {
	// Definir flags para cada script
	setupSecurityCmd := flag.NewFlagSet("setup-security", flag.ExitOnError)
	lintRobotCmd := flag.NewFlagSet("lint-robot", flag.ExitOnError)

	// Verificar se foi fornecido um comando
	if len(os.Args) < 2 {
		fmt.Println("Uso: go run main.go <comando>")
		fmt.Println("Comandos disponíveis:")
		fmt.Println("  setup-security - Configura a segurança inicial do sistema")
		fmt.Println("  lint-robot     - Executa o robô de lint")
		os.Exit(1)
	}

	// Executar o comando apropriado
	switch os.Args[1] {
	case "setup-security":
		setupSecurityCmd.Parse(os.Args[2:])
		setupSecurity()
	case "lint-robot":
		lintRobotCmd.Parse(os.Args[2:])
		runLintRobot()
	default:
		fmt.Printf("Comando desconhecido: %s\n", os.Args[1])
		os.Exit(1)
	}
}
