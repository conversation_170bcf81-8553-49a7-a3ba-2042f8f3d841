package main

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"tradicao/internal/models"
)

func main() {
	// Carregar variáveis de ambiente do arquivo .env
	if err := godotenv.Load(); err != nil {
		log.Printf("Aviso: Não foi possível carregar o arquivo .env: %v", err)
	}

	// Inicializar conexão com o banco de dados usando a string de conexão completa
	databaseURL := os.Getenv("DATABASE_URL")
	if databaseURL == "" {
		// Tentar construir a string de conexão a partir de variáveis individuais
		dbHost := os.Getenv("DB_HOST")
		dbPort := os.Getenv("DB_PORT")
		dbUser := os.Getenv("DB_USER")
		dbPass := os.Getenv("DB_PASS")
		dbName := os.Getenv("DB_NAME")

		if dbHost != "" && dbPort != "" && dbUser != "" && dbPass != "" && dbName != "" {
			databaseURL = fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
				dbHost, dbPort, dbUser, dbPass, dbName)
		} else {
			log.Fatalf("Variáveis de ambiente de banco de dados não definidas")
		}
	}

	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Buscar todos os usuários com role "branch_user"
	var users []models.User
	if err := db.Where("type = ?", models.RoleFilial).Find(&users).Error; err != nil {
		log.Fatalf("Erro ao buscar usuários: %v", err)
	}

	fmt.Printf("Encontrados %d usuários com role 'branch_user'\n\n", len(users))
	fmt.Println("Lista de usuários de filiais:")
	fmt.Println("-----------------------------")
	fmt.Printf("%-5s | %-15s | %-30s | %-10s\n", "ID", "Nome", "Email", "Filial ID")
	fmt.Println("-----------------------------")

	for _, user := range users {
		branchID := "N/A"
		if user.BranchID != nil {
			branchID = fmt.Sprintf("%d", *user.BranchID)
		}
		fmt.Printf("%-5d | %-15s | %-30s | %-10s\n", user.ID, user.Name, user.Email, branchID)
	}

	// Buscar todas as filiais
	var branches []models.Branch
	if err := db.Find(&branches).Error; err != nil {
		log.Fatalf("Erro ao buscar filiais: %v", err)
	}

	fmt.Printf("\nEncontradas %d filiais\n\n", len(branches))
	fmt.Println("Lista de filiais:")
	fmt.Println("-----------------------------")
	fmt.Printf("%-5s | %-30s\n", "ID", "Nome")
	fmt.Println("-----------------------------")

	for _, branch := range branches {
		fmt.Printf("%-5d | %-30s\n", branch.ID, branch.Name)
	}

	// Verificar se os IDs dos usuários correspondem aos IDs das filiais
	fmt.Printf("\nVerificação de correspondência de IDs:\n")
	fmt.Println("-----------------------------")

	for _, user := range users {
		if user.BranchID == nil {
			fmt.Printf("Usuário %s (ID: %d) não está associado a nenhuma filial\n", user.Name, user.ID)
			continue
		}

		if user.ID != *user.BranchID {
			fmt.Printf("Usuário %s (ID: %d) tem ID diferente da filial (ID: %d)\n", user.Name, user.ID, *user.BranchID)
		} else {
			fmt.Printf("Usuário %s (ID: %d) tem ID igual ao da filial (ID: %d) ✓\n", user.Name, user.ID, *user.BranchID)
		}
	}
}
