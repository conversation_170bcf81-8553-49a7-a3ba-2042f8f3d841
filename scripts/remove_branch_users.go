package main

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"tradicao/internal/models"
)

func main() {
	// Carregar variáveis de ambiente do arquivo .env
	if err := godotenv.Load(); err != nil {
		log.Printf("Aviso: Não foi possível carregar o arquivo .env: %v", err)
	}

	// Inicializar conexão com o banco de dados usando a string de conexão completa
	databaseURL := os.Getenv("DATABASE_URL")
	if databaseURL == "" {
		// Tentar construir a string de conexão a partir de variáveis individuais
		dbHost := os.Getenv("DB_HOST")
		dbPort := os.Getenv("DB_PORT")
		dbUser := os.Getenv("DB_USER")
		dbPass := os.Getenv("DB_PASS")
		dbName := os.Getenv("DB_NAME")

		if dbHost != "" && dbPort != "" && dbUser != "" && dbPass != "" && dbName != "" {
			databaseURL = fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
				dbHost, dbPort, dbUser, dbPass, dbName)
		} else {
			log.Fatalf("Variáveis de ambiente de banco de dados não definidas")
		}
	}

	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Desativar temporariamente as restrições de chave estrangeira
	db.Exec("SET session_replication_role = 'replica';")
	defer db.Exec("SET session_replication_role = 'origin';")

	// Buscar todos os usuários com role "branch_user"
	var users []models.User
	if err := db.Where("type = ?", models.RoleFilial).Find(&users).Error; err != nil {
		log.Fatalf("Erro ao buscar usuários: %v", err)
	}

	fmt.Printf("Encontrados %d usuários com role 'branch_user'\n", len(users))

	// Remover usuários de filiais
	for _, user := range users {
		if err := db.Delete(&user).Error; err != nil {
			fmt.Printf("Erro ao remover usuário %s (ID: %d): %v\n", user.Name, user.ID, err)
		} else {
			fmt.Printf("Usuário %s (ID: %d) removido com sucesso\n", user.Name, user.ID)
		}
	}

	// Buscar todas as filiais
	var branches []models.Branch
	if err := db.Find(&branches).Error; err != nil {
		log.Fatalf("Erro ao buscar filiais: %v", err)
	}

	fmt.Printf("Encontradas %d filiais\n", len(branches))

	// Remover filiais
	for _, branch := range branches {
		if err := db.Delete(&branch).Error; err != nil {
			fmt.Printf("Erro ao remover filial %s (ID: %d): %v\n", branch.Name, branch.ID, err)
		} else {
			fmt.Printf("Filial %s (ID: %d) removida com sucesso\n", branch.Name, branch.ID)
		}
	}

	fmt.Println("Processo de remoção concluído!")
}
