package main

import (
	"fmt"
	"log"
	"os"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// UserModel representa um usuário no sistema
type UserModel struct {
	ID             uint   `json:"id" gorm:"primaryKey"`
	Name           string `json:"name"`
	Email          string `json:"email" gorm:"unique"`
	Password       string `json:"-"`
	Role           string `json:"role" gorm:"column:type"`
	BranchID       *uint  `json:"branch_id,omitempty"`
	FailedAttempts int    `json:"failed_attempts" gorm:"default:0"`
	Blocked        bool   `json:"blocked" gorm:"default:false"`
	TOTPEnabled    bool   `json:"totp_enabled" gorm:"default:false"`
}

// TableName especifica o nome da tabela para o modelo UserModel
func (UserModel) TableName() string {
	return "users"
}

func main() {
	// Conectar ao banco de dados
	dsn := os.Getenv("DATABASE_URL")
	if dsn == "" {
		dsn = "postgresql://fcobdj_tradicao:<EMAIL>:54243/fcobdj_tradicao?sslmode=disable"
	}

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Buscar todos os usuários com role "branch_user"
	var users []UserModel
	result := db.Where("type = ?", "branch_user").Find(&users)
	if result.Error != nil {
		log.Fatalf("Erro ao buscar usuários: %v", result.Error)
	}

	fmt.Printf("Encontrados %d usuários com perfil 'branch_user'\n", len(users))

	// Atualizar cada usuário para role "filial"
	for _, user := range users {
		fmt.Printf("Atualizando usuário ID %d (%s) de 'branch_user' para 'filial'\n", user.ID, user.Email)

		result = db.Model(&UserModel{}).Where("id = ?", user.ID).Update("type", "filial")
		if result.Error != nil {
			log.Printf("ERRO ao atualizar usuário ID %d: %v\n", user.ID, result.Error)
		} else {
			fmt.Printf("Usuário ID %d atualizado com sucesso!\n", user.ID)
		}
	}

	fmt.Println("Migração concluída!")
}
