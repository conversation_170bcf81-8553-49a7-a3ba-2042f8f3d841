package main

import (
	"fmt"
	"log"
	"os"
	"regexp"
	"strconv"

	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"tradicao/internal/models"
)

func main() {
	// Carregar variáveis de ambiente do arquivo .env
	if err := godotenv.Load(); err != nil {
		log.Printf("Aviso: Não foi possível carregar o arquivo .env: %v", err)
	}

	// Inicializar conexão com o banco de dados usando a string de conexão completa
	databaseURL := os.Getenv("DATABASE_URL")
	if databaseURL == "" {
		// Tentar construir a string de conexão a partir de variáveis individuais
		dbHost := os.Getenv("DB_HOST")
		dbPort := os.Getenv("DB_PORT")
		dbUser := os.Getenv("DB_USER")
		dbPass := os.Getenv("DB_PASS")
		dbName := os.Getenv("DB_NAME")

		if dbHost != "" && dbPort != "" && dbUser != "" && dbPass != "" && dbName != "" {
			databaseURL = fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
				dbHost, dbPort, dbUser, dbPass, dbName)
		} else {
			log.Fatalf("Variáveis de ambiente de banco de dados não definidas")
		}
	}

	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Buscar todos os usuários com role "branch_user"
	var users []models.User
	if err := db.Where("type = ?", models.RoleFilial).Find(&users).Error; err != nil {
		log.Fatalf("Erro ao buscar usuários: %v", err)
	}

	fmt.Printf("Encontrados %d usuários com role 'branch_user'\n\n", len(users))

	// Desativar temporariamente as restrições de chave estrangeira
	db.Exec("SET session_replication_role = 'replica';")
	defer db.Exec("SET session_replication_role = 'origin';")

	// Para cada usuário, extrair o número da filial do nome e atualizar o branch_id
	for _, user := range users {
		// Extrair o número da filial do nome do usuário
		re := regexp.MustCompile(`Filial(\d+)`)
		matches := re.FindStringSubmatch(user.Name)

		var filialNum int
		if user.Name == "Matriz" {
			filialNum = 1 // ID 1 para a Matriz
		} else if len(matches) >= 2 {
			var err error
			filialNum, err = strconv.Atoi(matches[1])
			if err != nil {
				fmt.Printf("Erro ao converter número da filial para %s: %v\n", user.Name, err)
				continue
			}
		} else {
			fmt.Printf("Não foi possível extrair o número da filial do nome %s\n", user.Name)
			continue
		}

		// Verificar se a filial com este ID existe
		var branch models.Branch
		err := db.Where("id = ?", filialNum).First(&branch).Error
		if err != nil {
			fmt.Printf("Filial com ID %d não encontrada para o usuário %s\n", filialNum, user.Name)
			continue
		}

		// Atualizar o branch_id do usuário
		result := db.Model(&user).Update("branch_id", filialNum)
		if result.Error != nil {
			fmt.Printf("Erro ao atualizar branch_id do usuário %s: %v\n", user.Name, result.Error)
			continue
		}

		fmt.Printf("Usuário %s (ID: %d): branch_id atualizado para %d\n", user.Name, user.ID, filialNum)
	}

	fmt.Println("\nProcesso concluído!")
}
