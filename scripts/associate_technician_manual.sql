-- Associar o técnico às filiais 102 (Filial 02) e 105 (Filial 05)
-- <PERSON><PERSON>, remover qualquer associação existente para evitar duplicações
DELETE FROM technician_branches
WHERE technician_id = 94
  AND branch_id IN (102, 105);

-- <PERSON><PERSON><PERSON>, inserir as novas associações
INSERT INTO technician_branches (technician_id, branch_id, specialty_id, created_at, updated_at)
VALUES
    (94, 102, 3, NOW(), NOW()),
    (94, 105, 3, NOW(), NOW())
ON CONFLICT (technician_id, branch_id, specialty_id) DO NOTHING;
