package main

import (
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
)

func runAnalyzeTemplates() {
	// Esta função foi renomeada para evitar conflitos com outras funções main no mesmo pacote
	// Caminho para o diretório principal do projeto
	projectDir := "."

	// Caminho para o diretório de templates
	templatesDir := filepath.Join(projectDir, "web", "templates")

	// Caminho para o arquivo main.go
	mainGoPath := filepath.Join(projectDir, "cmd", "main.go")

	// Obter todos os arquivos de template
	templateFiles, err := getTemplateFiles(templatesDir)
	if err != nil {
		fmt.Printf("Erro ao ler diretório de templates: %v\n", err)
		return
	}

	// Ler o conteúdo do arquivo main.go
	mainGoContent, err := os.ReadFile(mainGoPath)
	if err != nil {
		fmt.Printf("Erro ao ler arquivo main.go: %v\n", err)
		return
	}

	// Encontrar referências a templates no arquivo main.go
	referencedTemplates := findTemplateReferences(string(mainGoContent))

	// Verificar quais templates não são referenciados
	unusedTemplates := findUnusedTemplates(templateFiles, referencedTemplates)

	// Exibir resultados
	fmt.Println("========= ANÁLISE DE TEMPLATES =========")
	fmt.Printf("Total de templates: %d\n", len(templateFiles))
	fmt.Printf("Templates referenciados: %d\n", len(referencedTemplates))
	fmt.Printf("Templates não utilizados: %d\n", len(unusedTemplates))

	fmt.Println("\nTemplates não utilizados:")
	for _, template := range unusedTemplates {
		fmt.Printf("- %s\n", template)
	}
}

// getTemplateFiles retorna uma lista de todos os arquivos de template
func getTemplateFiles(templatesDir string) ([]string, error) {
	var templates []string

	// Caminhar pelo diretório de templates e coletar arquivos .html
	err := filepath.Walk(templatesDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() && strings.HasSuffix(info.Name(), ".html") {
			// Armazenar apenas o nome do arquivo, sem o caminho completo
			templates = append(templates, info.Name())
		}

		return nil
	})

	return templates, err
}

// findTemplateReferences encontra referências a templates no código
func findTemplateReferences(content string) map[string]bool {
	referenced := make(map[string]bool)

	// Padrão para encontrar c.HTML(xxx, "template.html", xxx)
	pattern := `c\.HTML\([^,]+,\s*"([^"]+)"`
	re := regexp.MustCompile(pattern)
	matches := re.FindAllStringSubmatch(content, -1)

	for _, match := range matches {
		if len(match) > 1 {
			templateName := match[1]
			referenced[templateName] = true
		}
	}

	// Também verificar por ParseGlob que carrega templates
	parsePattern := `template\.Must\(templateSet\.ParseGlob\("([^"]+)"\)\)`
	parseRe := regexp.MustCompile(parsePattern)
	parseMatches := parseRe.FindAllStringSubmatch(content, -1)

	for _, match := range parseMatches {
		if len(match) > 1 {
			globPattern := match[1]
			fmt.Printf("Padrão de glob encontrado: %s\n", globPattern)
			// Aqui precisaríamos expandir o padrão glob, mas para simplificar,
			// vamos apenas marcar que há templates sendo carregados em massa
		}
	}

	// Verificar referências a content na base_sidemenu.html
	contentPattern := `"content":\s*"([^"]+)"`
	contentRe := regexp.MustCompile(contentPattern)
	contentMatches := contentRe.FindAllStringSubmatch(content, -1)

	for _, match := range contentMatches {
		if len(match) > 1 {
			contentTemplate := match[1]
			referenced[contentTemplate] = true
		}
	}

	return referenced
}

// findUnusedTemplates encontra templates que não são referenciados
func findUnusedTemplates(allTemplates []string, referencedTemplates map[string]bool) []string {
	var unused []string

	for _, template := range allTemplates {
		if !referencedTemplates[template] {
			unused = append(unused, template)
		}
	}

	return unused
}
