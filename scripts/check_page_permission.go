package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"strings"
)

func main() {
	if len(os.Args) < 3 {
		fmt.Println("Uso: go run scripts/check_page_permission.go <pagina> <perfil>")
		os.Exit(1)
	}

	pagina := os.Args[1]
	perfil := os.Args[2]

	// Remover a barra inicial, se houver
	pagina = strings.TrimPrefix(pagina, "/")

	// Carregar permissões do arquivo
	permissoes, err := carregarPermissoes()
	if err != nil {
		log.Fatalf("Erro ao carregar permissões: %v", err)
	}

	// Verificar se a página existe nas permissões
	roles, existe := permissoes[pagina]
	if !existe {
		fmt.Printf("Página '%s' não encontrada nas permissões\n", pagina)
		return
	}

	// Verificar se o perfil tem permissão
	temPermissao := false
	for _, role := range roles {
		if role == perfil {
			temPermissao = true
			break
		}
	}

	fmt.Printf("Página: %s\n", pagina)
	fmt.Printf("Perfil: %s\n", perfil)
	fmt.Printf("Perfis com permissão: %v\n", roles)
	fmt.Printf("Tem permissão: %t\n", temPermissao)
}

func carregarPermissoes() (map[string][]string, error) {
	permissoes := make(map[string][]string)

	// Carregar permissões do arquivo
	file, err := os.Open("data/permissoes_usuarios.md")
	if err != nil {
		return nil, fmt.Errorf("erro ao abrir arquivo de permissões: %v", err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	var currentPage string
	var inRoles bool

	for scanner.Scan() {
		line := scanner.Text()

		// Verificar se é uma linha de página
		if strings.HasPrefix(line, "### /") {
			currentPage = strings.TrimPrefix(line, "### /")
			inRoles = false
		}

		// Verificar se é a seção de papéis
		if strings.Contains(line, "Papéis com acesso:") {
			inRoles = true
			continue
		}

		// Verificar se é o início da lista de papéis
		if inRoles && strings.Contains(line, "```") {
			if !strings.HasPrefix(line, "```") {
				// Se a linha contém "```" mas não começa com "```", é o fim da lista
				inRoles = false
			}
			continue
		}

		// Se estamos na lista de papéis, adicionar o papel
		if inRoles && line != "" {
			permissoes[currentPage] = append(permissoes[currentPage], strings.TrimSpace(line))
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("erro ao ler arquivo de permissões: %v", err)
	}

	return permissoes, nil
}
