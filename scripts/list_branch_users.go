package main

import (
	"fmt"
	"log"
	"os"
	"strings"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// User representa um usuário no sistema (versão simplificada)
type User struct {
	ID             uint   `json:"id" gorm:"primaryKey"`
	Name           string `json:"name"`
	Email          string `json:"email" gorm:"unique"`
	Password       string `json:"-"`
	Role           string `json:"role" gorm:"column:type"`
	BranchID       *uint  `json:"branch_id,omitempty"`
	FailedAttempts int    `json:"failed_attempts" gorm:"default:0"`
	Blocked        bool   `json:"blocked" gorm:"default:false"`
	TOTPEnabled    bool   `json:"totp_enabled" gorm:"default:false"`
}

func main() {
	// Conectar ao banco de dados diretamente
	dsn := os.Getenv("DATABASE_URL")
	if dsn == "" {
		dsn = "postgresql://fcobdj_tradicao:<EMAIL>:54243/fcobdj_tradicao?sslmode=disable"
	}

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Buscar usuários de filiais
	var users []User
	result := db.Where("type = ? OR email LIKE ?", "filial", "filial%").Find(&users)
	if result.Error != nil {
		log.Fatalf("Erro ao buscar usuários de filiais: %v", result.Error)
	}

	fmt.Printf("Encontrados %d usuários de filiais:\n\n", len(users))
	for _, user := range users {
		fmt.Printf("ID: %d\n", user.ID)
		fmt.Printf("Nome: %s\n", user.Name)
		fmt.Printf("Email: %s\n", user.Email)
		fmt.Printf("Perfil: %s\n", user.Role)
		if user.BranchID != nil {
			fmt.Printf("Filial ID: %d\n", *user.BranchID)
		} else {
			fmt.Printf("Filial ID: Não associado a uma filial\n")
		}
		fmt.Printf("Bloqueado: %v\n", user.Blocked)
		fmt.Printf("Tentativas falhas: %d\n", user.FailedAttempts)
		fmt.Printf("2FA habilitado: %v\n\n", user.TOTPEnabled)
	}

	// Buscar todos os usuários cujo email começa com "filial"
	var filialUsers []User
	result = db.Where("email LIKE ?", "filial%").Find(&filialUsers)
	if result.Error != nil {
		log.Fatalf("Erro ao buscar usuários com email filial*: %v", result.Error)
	}

	fmt.Printf("Encontrados %d usuários com email começando com 'filial':\n\n", len(filialUsers))
	for _, user := range filialUsers {
		fmt.Printf("Email: %s\n", user.Email)
	}

	// Verificar se existe um usuário <NAME_EMAIL>
	var user105 User
	result = db.Where("email = ?", "<EMAIL>").First(&user105)
	if result.Error != nil {
		if strings.Contains(result.Error.Error(), "record not found") {
			fmt.Println("\nUsuário <EMAIL> NÃO EXISTE no banco de dados!")
		} else {
			fmt.Printf("\nErro ao buscar usuário <EMAIL>: %v\n", result.Error)
		}
	} else {
		fmt.Println("\nUsuário <EMAIL> existe no banco de dados.")
	}
}
