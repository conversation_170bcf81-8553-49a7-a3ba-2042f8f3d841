package main

import (
	"fmt"
	"log"
	"os"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"tradicao/internal/models"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Uso: go run scripts/cmd/verify_user/main.go <email>")
		os.Exit(1)
	}

	email := os.Args[1]

	// Obter configurações do banco de dados
	host := os.Getenv("DB_HOST")
	port := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	password := os.Getenv("DB_PASS")
	dbname := os.Getenv("DB_NAME")

	// Usar valores padrão se as variáveis de ambiente não estiverem definidas
	if host == "" || port == "" || dbUser == "" || password == "" || dbname == "" {
		log.Println("Variáveis de ambiente não encontradas, usando configuração padrão")
		host = "postgres-ag-br1-03.conteige.cloud"
		port = "54243"
		dbUser = "fcobdj_tradicao"
		password = "67573962"
		dbname = "fcobdj_tradicao"
	}

	// Construir string de conexão
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable",
		host, dbUser, password, dbname, port)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Buscar usuário pelo email
	var user models.User
	result := db.Where("email = ?", email).First(&user)
	if result.Error != nil {
		log.Fatalf("Erro ao buscar usuário: %v", result.Error)
	}

	fmt.Printf("Usuário encontrado:\n")
	fmt.Printf("ID: %d\n", user.ID)
	fmt.Printf("Nome: %s\n", user.Name)
	fmt.Printf("Email: %s\n", user.Email)
	fmt.Printf("Perfil: %s\n", user.Role)
	if user.BranchID != nil {
		fmt.Printf("Filial ID: %d\n", *user.BranchID)
	} else {
		fmt.Printf("Filial ID: Não associado a uma filial\n")
	}
	fmt.Printf("Bloqueado: %v\n", user.Blocked)
	fmt.Printf("Tentativas falhas: %d\n", user.FailedAttempts)
	fmt.Printf("2FA habilitado: %v\n", user.TOTPEnabled)
}
