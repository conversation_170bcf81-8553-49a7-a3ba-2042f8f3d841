package main

import (
	"fmt"
	"log"
	"strings"
	"time"

	"golang.org/x/crypto/bcrypt"

	"tradicao/internal/database"
	"tradicao/internal/models"
)

func main() {
	// Inicializar conexão com o banco de dados
	db, err := database.InitGorm()
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Buscar todas as filiais ativas
	var branches []models.Branch
	if err := db.Where("is_active = ?", true).Find(&branches).Error; err != nil {
		log.Fatalf("Erro ao buscar filiais: %v", err)
	}

	fmt.Printf("Encontradas %d filiais ativas\n", len(branches))

	// Para cada filial, criar um usuário
	for _, branch := range branches {
		// Verificar se já existe um usuário para esta filial
		var existingUser models.User
		err := db.Where("branch_id = ? AND role = ?", branch.ID, string(models.RoleFilial)).First(&existingUser).Error

		if err == nil {
			fmt.Printf("Filial %s (ID: %d) já possui um usuário: %s\n", branch.Name, branch.ID, existingUser.Email)
			continue
		}

		if err != nil && !strings.Contains(err.Error(), "record not found") {
			fmt.Printf("Erro ao verificar usuário existente para filial %s: %v\n", branch.Name, err)
			continue
		}

		// Criar email para a filial
		email := createBranchEmail(branch.Name)

		// Verificar se o email já está em uso
		var userWithEmail models.User
		err = db.Where("email = ?", email).First(&userWithEmail).Error
		if err == nil {
			fmt.Printf("Email %s já está em uso. Gerando alternativa...\n", email)
			email = createAlternativeEmail(branch.Name, branch.ID)
		}

		// Gerar hash da senha
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte("Filial@123"), bcrypt.DefaultCost)
		if err != nil {
			fmt.Printf("Erro ao gerar hash da senha para filial %s: %v\n", branch.Name, err)
			continue
		}

		// Criar o usuário
		user := models.User{
			Name:      fmt.Sprintf("Filial %s", branch.Name),
			Email:     email,
			Password:  string(hashedPassword),
			Role:      string(models.RoleFilial), // Convertendo para string
			BranchID:  &branch.ID,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}

		// Salvar o usuário no banco de dados
		if err := db.Create(&user).Error; err != nil {
			fmt.Printf("Erro ao criar usuário para filial %s: %v\n", branch.Name, err)
			continue
		}

		fmt.Printf("Usuário criado com sucesso para filial %s (ID: %d): %s\n", branch.Name, branch.ID, email)
	}

	fmt.Println("Processo concluído!")
}

// createBranchEmail cria um email para a filial baseado no nome
func createBranchEmail(branchName string) string {
	// Remover espaços e caracteres especiais
	name := strings.ToLower(branchName)
	name = strings.ReplaceAll(name, " ", ".")
	name = strings.ReplaceAll(name, "-", "")
	name = strings.ReplaceAll(name, "_", "")
	name = strings.ReplaceAll(name, "/", "")
	name = strings.ReplaceAll(name, "\\", "")
	name = strings.ReplaceAll(name, "(", "")
	name = strings.ReplaceAll(name, ")", "")

	// Remover acentos
	name = removeAccents(name)

	return fmt.Sprintf("<EMAIL>", name)
}

// createAlternativeEmail cria um email alternativo quando o principal já está em uso
func createAlternativeEmail(branchName string, branchID uint) string {
	name := strings.ToLower(branchName)
	name = strings.ReplaceAll(name, " ", ".")
	name = removeAccents(name)
	return fmt.Sprintf("<EMAIL>", name, branchID)
}

// removeAccents remove acentos de uma string
func removeAccents(s string) string {
	replacements := map[string]string{
		"á": "a", "à": "a", "â": "a", "ã": "a", "ä": "a",
		"é": "e", "è": "e", "ê": "e", "ë": "e",
		"í": "i", "ì": "i", "î": "i", "ï": "i",
		"ó": "o", "ò": "o", "ô": "o", "õ": "o", "ö": "o",
		"ú": "u", "ù": "u", "û": "u", "ü": "u",
		"ç": "c", "ñ": "n",
	}

	for old, new := range replacements {
		s = strings.ReplaceAll(s, old, new)
	}

	return s
}
