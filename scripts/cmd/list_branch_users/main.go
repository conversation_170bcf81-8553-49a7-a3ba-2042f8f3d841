package main

import (
	"fmt"
	"log"
	"os"
	"strings"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	
	"tradicao/scripts/common"
)

func main() {
	// Obter configurações do banco de dados
	host := os.Getenv("DB_HOST")
	port := os.Getenv("DB_PORT")
	user := os.Getenv("DB_USER")
	password := os.Getenv("DB_PASS")
	dbname := os.Getenv("DB_NAME")

	// Usar valores padrão se as variáveis de ambiente não estiverem definidas
	if host == "" || port == "" || user == "" || password == "" || dbname == "" {
		log.Println("Variáveis de ambiente não encontradas, usando configuração padrão")
		host = "postgres-ag-br1-03.conteige.cloud"
		port = "54243"
		user = "fcobdj_tradicao"
		password = "67573962"
		dbname = "fcobdj_tradicao"
	}

	// Construir string de conexão
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable",
		host, user, password, dbname, port)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Buscar todos os usuários com role "filial"
	var users []common.User
	if err := db.Where("type = ?", "filial").Find(&users).Error; err != nil {
		log.Fatalf("Erro ao buscar usuários: %v", err)
	}

	fmt.Printf("Encontrados %d usuários de filiais:\n\n", len(users))
	for _, user := range users {
		fmt.Printf("ID: %d\n", user.ID)
		fmt.Printf("Nome: %s\n", user.Name)
		fmt.Printf("Email: %s\n", user.Email)
		fmt.Printf("Perfil: %s\n", user.Role)
		if user.BranchID != nil {
			fmt.Printf("Filial ID: %d\n", *user.BranchID)
		} else {
			fmt.Printf("Filial ID: Não associado a uma filial\n")
		}
		fmt.Printf("Bloqueado: %v\n", user.Blocked)
		fmt.Printf("Tentativas falhas: %d\n", user.FailedAttempts)
		fmt.Printf("2FA habilitado: %v\n\n", user.TOTPEnabled)
	}

	// Buscar todos os usuários cujo email começa com "filial"
	var filialUsers []common.User
	if err := db.Where("email LIKE ?", "filial%").Find(&filialUsers).Error; err != nil {
		log.Fatalf("Erro ao buscar usuários de filial: %v", err)
	}

	fmt.Printf("Encontrados %d usuários com email começando com 'filial':\n\n", len(filialUsers))
	for _, user := range filialUsers {
		fmt.Printf("ID: %d\n", user.ID)
		fmt.Printf("Nome: %s\n", user.Name)
		fmt.Printf("Email: %s\n", user.Email)
		fmt.Printf("Perfil: %s\n", user.Role)
		if user.BranchID != nil {
			fmt.Printf("Filial ID: %d\n", *user.BranchID)
		} else {
			fmt.Printf("Filial ID: Não associado a uma filial\n")
		}
		fmt.Printf("Bloqueado: %v\n", user.Blocked)
		fmt.Printf("Tentativas falhas: %d\n", user.FailedAttempts)
		fmt.Printf("2FA habilitado: %v\n\n", user.TOTPEnabled)
	}
}
