package main

import (
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"github.com/golang-jwt/jwt/v5"
	
	"tradicao/scripts/common"
)

// Claims representa as claims do token JWT
type Claims struct {
	UserID uint   `json:"user_id"`
	Role   string `json:"role"`
	Email  string `json:"email"`
	jwt.RegisteredClaims
}

func main() {
	if len(os.Args) < 3 {
		fmt.Println("Uso: go run scripts/cmd/test_page_access/main.go <email> <senha> [pagina]")
		os.Exit(1)
	}

	email := os.Args[1]
	password := os.Args[2]
	pagina := "ordemtecnica"
	if len(os.Args) > 3 {
		pagina = os.Args[3]
	}

	// Obter configurações do banco de dados
	host := os.Getenv("DB_HOST")
	port := os.Getenv("DB_PORT")
	user := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASS")
	dbname := os.Getenv("DB_NAME")

	// Usar valores padrão se as variáveis de ambiente não estiverem definidas
	if host == "" || port == "" || user == "" || dbPassword == "" || dbname == "" {
		log.Println("Variáveis de ambiente não encontradas, usando configuração padrão")
		host = "postgres-ag-br1-03.conteige.cloud"
		port = "54243"
		user = "fcobdj_tradicao"
		dbPassword = "67573962"
		dbname = "fcobdj_tradicao"
	}

	// Construir string de conexão
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable",
		host, user, dbPassword, dbname, port)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Buscar usuário pelo email
	var userInfo common.User
	result := db.Where("email = ?", email).First(&userInfo)
	if result.Error != nil {
		log.Fatalf("Erro ao buscar usuário: %v", result.Error)
	}

	// Verificar senha
	err = bcrypt.CompareHashAndPassword([]byte(userInfo.Password), []byte(password))
	if err != nil {
		fmt.Printf("Senha incorreta para usuário: %s\n", email)
		os.Exit(1)
	}

	// Gerar token JWT
	token, err := generateToken(userInfo.ID, userInfo.Email, userInfo.Role)
	if err != nil {
		log.Fatalf("Erro ao gerar token: %v", err)
	}

	// Carregar permissões do arquivo
	permissoes, err := carregarPermissoes()
	if err != nil {
		log.Fatalf("Erro ao carregar permissões: %v", err)
	}

	// Remover a barra inicial, se houver
	pagina = strings.TrimPrefix(pagina, "/")

	// Verificar se a página existe nas permissões
	roles, existe := permissoes[pagina]
	if !existe {
		fmt.Printf("Página '%s' não encontrada nas permissões\n", pagina)
		return
	}

	// Verificar se o perfil tem permissão
	temPermissao := false
	for _, role := range roles {
		if role == userInfo.Role {
			temPermissao = true
			break
		}
	}

	fmt.Printf("=== Simulação de Acesso ===\n")
	fmt.Printf("Usuário: %s (ID: %d)\n", userInfo.Name, userInfo.ID)
	fmt.Printf("Email: %s\n", userInfo.Email)
	fmt.Printf("Perfil: %s\n", userInfo.Role)
	fmt.Printf("Página: %s\n", pagina)
	fmt.Printf("Perfis com permissão: %v\n", roles)
	fmt.Printf("Tem permissão: %t\n", temPermissao)
	fmt.Printf("Token JWT: %s\n", token)
}

func generateToken(userID uint, email, role string) (string, error) {
	// Define tempo de expiração
	expirationTime := time.Now().Add(24 * time.Hour)

	// Cria as claims
	claims := &Claims{
		UserID: userID,
		Role:   role,
		Email:  email,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	// Cria o token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Assina o token
	return token.SignedString([]byte("chave-secreta-shell-tradicao"))
}

func carregarPermissoes() (map[string][]string, error) {
	permissoes := make(map[string][]string)

	// Carregar permissões do arquivo
	file, err := os.Open("data/permissoes_paginas.md")
	if err != nil {
		return nil, fmt.Errorf("erro ao abrir arquivo de permissões: %v", err)
	}
	defer file.Close()

	// Ler o arquivo linha por linha
	content, err := os.ReadFile("data/permissoes_paginas.md")
	if err != nil {
		return nil, fmt.Errorf("erro ao ler arquivo de permissões: %v", err)
	}

	lines := strings.Split(string(content), "\n")
	inTable := false

	for _, line := range lines {
		// Verificar se estamos na seção da tabela
		if strings.Contains(line, "| Página | Perfis com acesso |") {
			inTable = true
			continue
		}

		// Pular a linha de separação da tabela
		if strings.Contains(line, "|---") {
			continue
		}

		if inTable && strings.HasPrefix(line, "|") {
			parts := strings.Split(line, "|")
			if len(parts) >= 3 {
				pagePath := strings.TrimSpace(parts[1])
				rolesStr := strings.TrimSpace(parts[2])

				// Remover as aspas e formatação markdown
				pagePath = strings.Trim(pagePath, "`")
				// Remover a barra inicial
				pagePath = strings.TrimPrefix(pagePath, "/")

				// Separar os perfis
				roles := strings.Split(rolesStr, ",")

				// Armazenar a lista de perfis limpos
				var cleanRoles []string
				for _, role := range roles {
					role = strings.TrimSpace(role)
					if role != "" {
						cleanRoles = append(cleanRoles, role)
					}
				}

				// Adicionar ao mapa
				permissoes[pagePath] = cleanRoles
			}
		}

		// Sair do processamento da tabela quando encontrar uma linha em branco
		if inTable && strings.TrimSpace(line) == "" {
			inTable = false
		}
	}

	return permissoes, nil
}
