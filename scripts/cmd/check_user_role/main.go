package main

import (
	"fmt"
	"log"
	"os"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	
	"tradicao/scripts/common"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Uso: go run scripts/cmd/check_user_role/main.go <email>")
		os.Exit(1)
	}

	email := os.Args[1]

	// Obter configurações do banco de dados
	host := os.Getenv("DB_HOST")
	port := os.Getenv("DB_PORT")
	user := os.Getenv("DB_USER")
	password := os.Getenv("DB_PASS")
	dbname := os.Getenv("DB_NAME")

	// Usar valores padrão se as variáveis de ambiente não estiverem definidas
	if host == "" || port == "" || user == "" || password == "" || dbname == "" {
		log.Println("Variáveis de ambiente não encontradas, usando configuração padrão")
		host = "postgres-ag-br1-03.conteige.cloud"
		port = "54243"
		user = "fcobdj_tradicao"
		password = "67573962"
		dbname = "fcobdj_tradicao"
	}

	// Construir string de conexão
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable",
		host, user, password, dbname, port)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Buscar usuário pelo email
	var userInfo common.User
	result := db.Where("email = ?", email).First(&userInfo)
	if result.Error != nil {
		log.Fatalf("Erro ao buscar usuário: %v", result.Error)
	}

	fmt.Printf("=== Informações do Usuário ===\n")
	fmt.Printf("ID: %d\n", userInfo.ID)
	fmt.Printf("Nome: %s\n", userInfo.Name)
	fmt.Printf("Email: %s\n", userInfo.Email)
	fmt.Printf("Perfil: %s\n", userInfo.Role)
	
	branchID := "N/A"
	if userInfo.BranchID != nil {
		branchID = fmt.Sprintf("%d", *userInfo.BranchID)
	}
	fmt.Printf("Filial ID: %s\n", branchID)
	
	status := "Ativo"
	if userInfo.Blocked {
		status = "Bloqueado"
	}
	fmt.Printf("Status: %s\n", status)
	
	fmt.Printf("2FA Habilitado: %t\n", userInfo.TOTPEnabled)
}
