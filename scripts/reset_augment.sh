#!/bin/bash

# Script para resetar o pacote augment: desinstala, limpa estado e reinstala

if command -v pip >/dev/null 2>&1; then
    echo "Desinstalando augment (se estiver instalado)..."
    pip uninstall -y augment

    echo "Limpando arquivos de estado e cache do augment..."
    # Apagar diretórios e arquivos comuns de cache/estado do augment
    rm -rf ~/.augment
    rm -rf ~/.cache/augment
    rm -rf ~/.config/augment

    echo "Reinstalando augment..."
    pip install augment

    echo "Reset do augment concluído."
else
    echo "pip não encontrado. Por favor, instale o pip primeiro."
    exit 1
fi
