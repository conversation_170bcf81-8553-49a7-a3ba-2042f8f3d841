-- Script para exportar filiais do banco de dados fcobdj_tradicao
\copy (SELECT id, name, COALESCE(code, 'POSTO' || LPAD(id::text, 3, '0')) as code, address, city, state, COALESCE(zip_code, '') as zip_code, COALESCE(phone, '') as phone, COALESCE(email, '') as email, COALESCE(type, 'urban') as type, COALESCE(is_active, TRUE) as is_active, COALESCE(created_at, CURRENT_TIMESTAMP) as created_at, COALESCE(updated_at, CURRENT_TIMESTAMP) as updated_at FROM branches) TO '/tmp/filiais_export.csv' WITH CSV HEADER;
