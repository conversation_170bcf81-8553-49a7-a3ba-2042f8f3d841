#!/bin/bash

# Script para executar a migração que cria a tabela de transferências de equipamentos

# Carregar variáveis de ambiente
if [ -f .env ]; then
    export $(cat .env | grep -v '#' | awk '/=/ {print $1}')
fi

# Verificar se as variáveis de ambiente estão definidas
if [ -z "$DB_HOST" ] || [ -z "$DB_PORT" ] || [ -z "$DB_USER" ] || [ -z "$DB_PASSWORD" ] || [ -z "$DB_NAME" ]; then
    echo "Erro: Variáveis de ambiente de banco de dados não definidas."
    echo "Certifique-se de que o arquivo .env contém as variáveis DB_HOST, DB_PORT, DB_USER, DB_PASSWORD e DB_NAME."
    exit 1
fi

# Construir a string de conexão
DB_CONNECTION="postgresql://$DB_USER:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME?sslmode=disable"

echo "Executando migração para criar a tabela de transferências de equipamentos..."

# Executar o script SQL
psql "$DB_CONNECTION" -f migrations/020_create_equipment_transfers_table.sql

# Verificar o resultado
if [ $? -eq 0 ]; then
    echo "Migração executada com sucesso!"
else
    echo "Erro ao executar a migração."
    exit 1
fi

echo "Tabela equipment_transfers criada com sucesso."
