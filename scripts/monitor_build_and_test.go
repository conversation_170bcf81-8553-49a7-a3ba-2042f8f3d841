// Package monitor implementa a funcionalidade de monitoramento de comandos para compilação e execução de testes automatizados.
package main

import (
	"bufio"
	"fmt"
	"os"
	"os/exec"
	"strings"
)

// Monitor monitora comandos de compilação ou inicialização do servidor
// e executa testes automatizados somente após detectar esses comandos.
func main() {
	fmt.Println("Monitorando comandos para compilação/inicialização...")

	scanner := bufio.NewScanner(os.Stdin)
	for scanner.Scan() {
		line := scanner.Text()
		fmt.Printf("Comando detectado: %s\n", line)

		// Verifica se o comando é de compilação ou inicialização
		if isBuildOrRunCommand(line) {
			fmt.Println("Comando de build/run detectado. Executando testes automatizados...")
			runTests()
		}
	}

	if err := scanner.Err(); err != nil {
		fmt.Fprintf(os.<PERSON>, "Erro ao ler entrada: %v\n", err)
	}
}

func isBuildOrRunCommand(cmd string) bool {
	// Ajuste os comandos conforme o padrão do seu projeto
	buildCommands := []string{
		"go build",
		"go run",
		"./bin/app",
	}

	for _, bcmd := range buildCommands {
		if strings.Contains(cmd, bcmd) {
			return true
		}
	}
	return false
}

func runTests() {
	// Comando para rodar os testes automatizados
	cmd := exec.Command("go", "test", "./...")
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	err := cmd.Run()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Erro ao executar testes: %v\n", err)
	} else {
		fmt.Println("Testes concluídos com sucesso.")
	}
}
