package main

import (
	"fmt"
	"log"
	"os"
	"regexp"
	"strconv"

	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"tradicao/internal/models"
)

func main() {
	// Carregar variáveis de ambiente do arquivo .env
	if err := godotenv.Load(); err != nil {
		log.Printf("Aviso: Não foi possível carregar o arquivo .env: %v", err)
	}

	// Inicializar conexão com o banco de dados usando a string de conexão completa
	databaseURL := os.Getenv("DATABASE_URL")
	if databaseURL == "" {
		// Tentar construir a string de conexão a partir de variáveis individuais
		dbHost := os.Getenv("DB_HOST")
		dbPort := os.Getenv("DB_PORT")
		dbUser := os.Getenv("DB_USER")
		dbPass := os.Getenv("DB_PASS")
		dbName := os.Getenv("DB_NAME")

		if dbHost != "" && dbPort != "" && dbUser != "" && dbPass != "" && dbName != "" {
			databaseURL = fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
				dbHost, dbPort, dbUser, dbPass, dbName)
		} else {
			log.Fatalf("Variáveis de ambiente de banco de dados não definidas")
		}
	}

	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Buscar todos os usuários com role "branch_user"
	var users []models.User
	if err := db.Where("type = ?", models.RoleFilial).Find(&users).Error; err != nil {
		log.Fatalf("Erro ao buscar usuários: %v", err)
	}

	fmt.Printf("Encontrados %d usuários com role 'branch_user'\n\n", len(users))

	// Desativar temporariamente as restrições de chave estrangeira
	db.Exec("SET session_replication_role = 'replica';")
	defer db.Exec("SET session_replication_role = 'origin';")

	// Primeiro, atualizar os IDs das filiais
	var branches []models.Branch
	if err := db.Find(&branches).Error; err != nil {
		log.Fatalf("Erro ao buscar filiais: %v", err)
	}

	fmt.Printf("Encontradas %d filiais\n\n", len(branches))

	// Mapa para armazenar a relação entre o nome da filial e seu novo ID
	branchNameToNewID := make(map[string]uint)

	// Para cada filial, extrair o número da filial do nome e atualizar o ID
	for _, branch := range branches {
		// Extrair o número da filial do nome
		re := regexp.MustCompile(`Filial (\d+)`)
		matches := re.FindStringSubmatch(branch.Name)

		var newID uint
		if branch.Name == "Matriz" {
			newID = 1 // ID 1 para a Matriz
		} else if len(matches) >= 2 {
			filialNum, err := strconv.Atoi(matches[1])
			if err != nil {
				fmt.Printf("Erro ao converter número da filial para %s: %v\n", branch.Name, err)
				continue
			}
			newID = uint(filialNum)
		} else {
			// Se não conseguir extrair o número, manter o ID atual
			fmt.Printf("Não foi possível extrair o número da filial do nome %s. Mantendo ID atual.\n", branch.Name)
			continue
		}

		// Verificar se o ID já está em uso por outra filial
		var existingBranch models.Branch
		err := db.Where("id = ? AND id != ?", newID, branch.ID).First(&existingBranch).Error
		if err == nil {
			fmt.Printf("ID %d já está em uso por outra filial (%s). Não é possível atualizar o ID de %s.\n",
				newID, existingBranch.Name, branch.Name)
			continue
		}

		// Armazenar a relação entre o nome da filial e seu novo ID
		branchNameToNewID[branch.Name] = newID

		// Atualizar o ID da filial usando SQL direto
		result := db.Exec("UPDATE branches SET id = ? WHERE id = ?", newID, branch.ID)
		if result.Error != nil {
			fmt.Printf("Erro ao atualizar ID da filial %s: %v\n", branch.Name, result.Error)
			continue
		}

		fmt.Printf("Filial %s: ID atualizado de %d para %d\n", branch.Name, branch.ID, newID)
	}

	// Agora, atualizar os IDs dos usuários e seus branch_id
	// Buscar novamente os usuários após as alterações nas filiais
	if err := db.Where("type = ?", models.RoleFilial).Find(&users).Error; err != nil {
		log.Fatalf("Erro ao buscar usuários: %v", err)
	}

	// Para cada usuário, extrair o número da filial do nome e atualizar o ID
	for _, user := range users {
		// Extrair o número da filial do nome do usuário
		re := regexp.MustCompile(`Filial(\d+)`)
		matches := re.FindStringSubmatch(user.Name)

		var newID uint
		var branchName string

		if user.Name == "Matriz" {
			newID = 1 // ID 1 para a Matriz
			branchName = "Matriz"
		} else if len(matches) >= 2 {
			filialNum, err := strconv.Atoi(matches[1])
			if err != nil {
				fmt.Printf("Erro ao converter número da filial para %s: %v\n", user.Name, err)
				continue
			}
			newID = uint(filialNum)
			branchName = fmt.Sprintf("Filial %s", matches[1])
		} else {
			fmt.Printf("Não foi possível extrair o número da filial do nome %s\n", user.Name)
			continue
		}

		// Verificar se o ID já está em uso por outro usuário que não seja de filial
		var existingUser models.User
		err := db.Where("id = ? AND type != ?", newID, models.RoleFilial).First(&existingUser).Error
		if err == nil {
			fmt.Printf("ID %d já está em uso por outro usuário (%s). Não é possível atualizar o ID de %s.\n",
				newID, existingUser.Name, user.Name)
			continue
		}

		// Obter o novo ID da filial correspondente
		newBranchID, exists := branchNameToNewID[branchName]
		if !exists {
			fmt.Printf("Não foi encontrada filial com nome %s para o usuário %s\n", branchName, user.Name)
			continue
		}

		// Atualizar o ID do usuário e o branch_id usando SQL direto
		result := db.Exec("UPDATE users SET id = ?, branch_id = ? WHERE id = ?", newID, newBranchID, user.ID)
		if result.Error != nil {
			fmt.Printf("Erro ao atualizar ID e branch_id do usuário %s: %v\n", user.Name, result.Error)
			continue
		}

		fmt.Printf("Usuário %s: ID atualizado de %d para %d, branch_id atualizado para %d\n",
			user.Name, user.ID, newID, newBranchID)
	}

	fmt.Println("\nProcesso concluído!")
}
