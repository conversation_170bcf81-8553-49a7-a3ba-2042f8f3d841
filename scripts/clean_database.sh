#!/bin/bash

# Script para limpar o banco de dados e prepará-lo para migração ENT+Atlas
# Uso: sudo ./clean_database.sh [--dry-run]

# Verificar se o modo dry run foi especificado
DRY_RUN=false
if [ "$1" == "--dry-run" ]; then
    DRY_RUN=true
    echo "Executando em modo DRY RUN - nenhuma alteração será feita no banco de dados."
fi

# Configurações
LOG_DIR="logs"
mkdir -p $LOG_DIR
LOG_FILE="$LOG_DIR/clean_database_$(date +%Y%m%d_%H%M%S).log"

# Função para registrar mensagens
log() {
    echo "$(date +"%Y-%m-%d %H:%M:%S") - $1" | tee -a $LOG_FILE
}

log "Iniciando limpeza do banco de dados..."

# Verificar se o backup foi feito
if [ ! -d "backups/$(date +%Y%m%d)" ]; then
    log "AVISO: Não foi encontrado backup para hoje. Recomenda-se executar o script backup_databases.sh primeiro."
    read -p "Deseja continuar mesmo assim? (s/n): " confirm
    if [ "$confirm" != "s" ]; then
        log "Operação cancelada pelo usuário."
        exit 1
    fi
fi

# Função para executar comandos SQL com segurança
execute_sql() {
    local db=$1
    local sql=$2
    local description=$3
    
    log "Executando: $description"
    
    if [ "$DRY_RUN" = true ]; then
        log "DRY RUN: $sql"
    else
        echo "$sql" | sudo -u postgres psql -d $db 2>> $LOG_FILE
        if [ $? -eq 0 ]; then
            log "Sucesso: $description"
        else
            log "ERRO: Falha ao executar: $description"
            log "Consulte o log para mais detalhes: $LOG_FILE"
            exit 1
        fi
    fi
}

# 1. Criar um novo banco de dados limpo para o ENT
log "Criando novo banco de dados para ENT..."
if [ "$DRY_RUN" = false ]; then
    sudo -u postgres psql -c "CREATE DATABASE tradicao_ent WITH TEMPLATE template0 ENCODING 'UTF8' LC_COLLATE 'C.UTF-8' LC_CTYPE 'C.UTF-8';" 2>> $LOG_FILE
    if [ $? -ne 0 ]; then
        log "AVISO: Banco de dados tradicao_ent pode já existir. Tentando remover e recriar..."
        sudo -u postgres psql -c "DROP DATABASE IF EXISTS tradicao_ent;" 2>> $LOG_FILE
        sudo -u postgres psql -c "CREATE DATABASE tradicao_ent WITH TEMPLATE template0 ENCODING 'UTF8' LC_COLLATE 'C.UTF-8' LC_CTYPE 'C.UTF-8';" 2>> $LOG_FILE
        if [ $? -ne 0 ]; then
            log "ERRO: Falha ao criar banco de dados tradicao_ent."
            exit 1
        fi
    fi
else
    log "DRY RUN: Criar banco de dados tradicao_ent"
fi

# 2. Criar tabela de versões para o Atlas
execute_sql "tradicao_ent" "
CREATE TABLE IF NOT EXISTS schema_versions (
    version VARCHAR(255) NOT NULL,
    description VARCHAR(255) NOT NULL,
    type VARCHAR(20) NOT NULL,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (version)
);" "Criar tabela schema_versions para Atlas"

# 3. Migrar dados relevantes do banco fcobdj_tradicao para o novo banco
# Primeiro, vamos criar as tabelas básicas necessárias para o ENT

# Tabela branches
execute_sql "tradicao_ent" "
CREATE TABLE branches (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) NOT NULL UNIQUE,
    address VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(2),
    zip_code VARCHAR(20),
    phone VARCHAR(20),
    email VARCHAR(100),
    type VARCHAR(20) DEFAULT 'urban',
    is_active BOOLEAN DEFAULT TRUE,
    manager_id INTEGER,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    opening_hours VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);" "Criar tabela branches"

# Tabela users
execute_sql "tradicao_ent" "
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    branch_id INTEGER,
    failed_attempts INTEGER DEFAULT 0,
    blocked BOOLEAN DEFAULT FALSE,
    totp_secret VARCHAR(255) DEFAULT '',
    totp_enabled BOOLEAN DEFAULT FALSE,
    last_password_change TIMESTAMP,
    force_password_change BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);" "Criar tabela users"

# Tabela equipment
execute_sql "tradicao_ent" "
CREATE TABLE equipment (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    serial_number VARCHAR(100),
    model VARCHAR(100),
    brand VARCHAR(100),
    type VARCHAR(50) NOT NULL,
    installation_date TIMESTAMP,
    last_maintenance TIMESTAMP,
    last_preventive TIMESTAMP,
    next_preventive TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active',
    location VARCHAR(255),
    notes TEXT,
    branch_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);" "Criar tabela equipment"

# Tabela maintenance_orders
execute_sql "tradicao_ent" "
CREATE TABLE maintenance_orders (
    id SERIAL PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'pending',
    priority VARCHAR(20) DEFAULT 'medium',
    branch_id INTEGER,
    equipment_id INTEGER,
    requester_id INTEGER,
    approver_id INTEGER,
    technician_id INTEGER,
    cancellation_reason TEXT,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    estimated_time INTEGER,
    actual_time INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);" "Criar tabela maintenance_orders"

# Adicionar chaves estrangeiras após criar todas as tabelas
execute_sql "tradicao_ent" "
ALTER TABLE users ADD CONSTRAINT fk_users_branch FOREIGN KEY (branch_id) REFERENCES branches(id);
ALTER TABLE branches ADD CONSTRAINT fk_branches_manager FOREIGN KEY (manager_id) REFERENCES users(id);
ALTER TABLE equipment ADD CONSTRAINT fk_equipment_branch FOREIGN KEY (branch_id) REFERENCES branches(id);
ALTER TABLE maintenance_orders ADD CONSTRAINT fk_maintenance_orders_branch FOREIGN KEY (branch_id) REFERENCES branches(id);
ALTER TABLE maintenance_orders ADD CONSTRAINT fk_maintenance_orders_equipment FOREIGN KEY (equipment_id) REFERENCES equipment(id);
ALTER TABLE maintenance_orders ADD CONSTRAINT fk_maintenance_orders_requester FOREIGN KEY (requester_id) REFERENCES users(id);
ALTER TABLE maintenance_orders ADD CONSTRAINT fk_maintenance_orders_approver FOREIGN KEY (approver_id) REFERENCES users(id);
ALTER TABLE maintenance_orders ADD CONSTRAINT fk_maintenance_orders_technician FOREIGN KEY (technician_id) REFERENCES users(id);
" "Adicionar chaves estrangeiras"

# Criar índices para otimização
execute_sql "tradicao_ent" "
CREATE INDEX idx_users_email ON users (email);
CREATE INDEX idx_users_role ON users (role);
CREATE INDEX idx_users_branch_id ON users (branch_id);
CREATE INDEX idx_branches_manager_id ON branches (manager_id);
CREATE INDEX idx_branches_is_active ON branches (is_active);
CREATE INDEX idx_branches_city_state ON branches (city, state);
CREATE INDEX idx_equipment_branch_id ON equipment (branch_id);
CREATE INDEX idx_equipment_type ON equipment (type);
CREATE INDEX idx_equipment_status ON equipment (status);
CREATE INDEX idx_maintenance_orders_branch_id ON maintenance_orders (branch_id);
CREATE INDEX idx_maintenance_orders_equipment_id ON maintenance_orders (equipment_id);
CREATE INDEX idx_maintenance_orders_status ON maintenance_orders (status);
CREATE INDEX idx_maintenance_orders_priority ON maintenance_orders (priority);
" "Criar índices"

# 4. Migrar dados do banco fcobdj_tradicao para o novo banco
log "Migrando dados do banco fcobdj_tradicao para o novo banco tradicao_ent..."

# Migrar branches
execute_sql "tradicao_ent" "
INSERT INTO branches (id, name, address, city, state, is_active, created_at, updated_at)
SELECT id, name, address, city, state, is_active, created_at, updated_at
FROM fcobdj_tradicao.branches;" "Migrar dados de branches"

# Migrar users
execute_sql "tradicao_ent" "
INSERT INTO users (id, name, email, password, role, branch_id, created_at, updated_at)
SELECT id, name, email, password, role, branch_id, created_at, updated_at
FROM fcobdj_tradicao.users;" "Migrar dados de users"

# Migrar equipment
execute_sql "tradicao_ent" "
INSERT INTO equipment (id, name, type, model, serial_number, status, branch_id, created_at, updated_at)
SELECT id, name, type, model, serial_number, status, branch_id, created_at, updated_at
FROM fcobdj_tradicao.equipment;" "Migrar dados de equipment"

# Migrar maintenance_orders
execute_sql "tradicao_ent" "
INSERT INTO maintenance_orders (id, title, description, status, priority, branch_id, equipment_id, requester_id, created_at, updated_at)
SELECT id, title, description, status, priority, branch_id, equipment_id, requester_id, created_at, updated_at
FROM fcobdj_tradicao.maintenance_orders;" "Migrar dados de maintenance_orders"

log "Migração de dados concluída com sucesso."

# 5. Atualizar as sequências para evitar conflitos de ID
execute_sql "tradicao_ent" "
SELECT setval('branches_id_seq', (SELECT MAX(id) FROM branches));
SELECT setval('users_id_seq', (SELECT MAX(id) FROM users));
SELECT setval('equipment_id_seq', (SELECT MAX(id) FROM equipment));
SELECT setval('maintenance_orders_id_seq', (SELECT MAX(id) FROM maintenance_orders));
" "Atualizar sequências"

log "Limpeza e preparação do banco de dados concluída com sucesso."
log "O novo banco de dados 'tradicao_ent' está pronto para ser usado com ENT e Atlas."
log "Data/Hora de conclusão: $(date)"
