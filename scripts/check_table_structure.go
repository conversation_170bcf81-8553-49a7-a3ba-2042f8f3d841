package main

import (
	"fmt"
	"log"
	"os"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// Configurar conexão com o banco de dados
	dsn := "postgresql://fcobdj_tradicao:<EMAIL>:54243/fcobdj_tradicao?sslmode=disable"

	// Configurar logger do GORM
	gormLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold: 0,
			LogLevel:      logger.Info,
			Colorful:      true,
		},
	)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Verificar a estrutura da tabela maintenance_orders
	var columns []struct {
		ColumnName string `gorm:"column:column_name"`
		DataType   string `gorm:"column:data_type"`
	}

	db.Raw(`
		SELECT column_name, data_type
		FROM information_schema.columns
		WHERE table_name = 'maintenance_orders'
		ORDER BY ordinal_position
	`).Scan(&columns)

	fmt.Println("=== Estrutura da tabela maintenance_orders ===")
	fmt.Println("Coluna\t\t\tTipo")
	fmt.Println("--------------------------------------------------")
	for _, column := range columns {
		fmt.Printf("%s\t\t\t%s\n", column.ColumnName, column.DataType)
	}

	// Verificar as restrições de chave estrangeira
	var constraints []struct {
		ConstraintName   string `gorm:"column:constraint_name"`
		ColumnName       string `gorm:"column:column_name"`
		ReferencedTable  string `gorm:"column:referenced_table_name"`
		ReferencedColumn string `gorm:"column:referenced_column_name"`
	}

	db.Raw(`
		SELECT
			tc.constraint_name,
			kcu.column_name,
			ccu.table_name AS referenced_table_name,
			ccu.column_name AS referenced_column_name
		FROM
			information_schema.table_constraints AS tc
			JOIN information_schema.key_column_usage AS kcu
				ON tc.constraint_name = kcu.constraint_name
			JOIN information_schema.constraint_column_usage AS ccu
				ON ccu.constraint_name = tc.constraint_name
		WHERE
			tc.constraint_type = 'FOREIGN KEY'
			AND tc.table_name = 'maintenance_orders'
	`).Scan(&constraints)

	fmt.Println("\n=== Restrições de Chave Estrangeira ===")
	fmt.Println("Nome da Restrição\t\tColuna\t\tTabela Referenciada\t\tColuna Referenciada")
	fmt.Println("--------------------------------------------------")
	for _, constraint := range constraints {
		fmt.Printf("%s\t\t%s\t\t%s\t\t%s\n", constraint.ConstraintName, constraint.ColumnName, constraint.ReferencedTable, constraint.ReferencedColumn)
	}
}
