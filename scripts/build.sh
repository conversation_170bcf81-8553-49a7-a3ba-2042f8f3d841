#!/bin/bash

# Configurações de otimização
export GOGC=off
export GOAMD64=v3
export GOMAXPROCS=8
export GOCACHE=/tmp/go-cache

# Limpar binários antigos
mkdir -p bin
rm -f bin/app

# Compilar com otimizações
echo \ Compilando com otimizações...\
time go build -o bin/app -ldflags=\-s -w\ ./cmd/main.go

# Verificar o tamanho do binário
echo \Tamanho do binário:\
ls -lh bin/app

echo \Compilação concluída!\
