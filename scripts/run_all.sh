#!/bin/bash

# Script principal para executar todo o processo de limpeza e configuração
# Uso: sudo ./scripts/run_all.sh [--dry-run]

# Verificar se o modo dry run foi especificado
DRY_RUN=""
if [ "$1" == "--dry-run" ]; then
    DRY_RUN="--dry-run"
    echo "Executando em modo DRY RUN - nenhuma alteração será feita no banco de dados."
fi

# Configurações
LOG_DIR="logs"
mkdir -p $LOG_DIR
LOG_FILE="$LOG_DIR/run_all_$(date +%Y%m%d_%H%M%S).log"

# Função para registrar mensagens
log() {
    echo "$(date +"%Y-%m-%d %H:%M:%S") - $1" | tee -a $LOG_FILE
}

log "Iniciando processo completo de limpeza e configuração..."

# 1. Fazer backup dos bancos de dados
log "Etapa 1: Fazendo backup dos bancos de dados..."
sudo ./scripts/backup_databases.sh
if [ $? -ne 0 ]; then
    log "ERRO: Falha ao fazer backup dos bancos de dados."
    exit 1
fi
log "Backup dos bancos de dados concluído com sucesso."

# 2. Analisar a estrutura do banco de dados
log "Etapa 2: Analisando a estrutura do banco de dados..."
sudo ./scripts/analyze_database_structure.sh
if [ $? -ne 0 ]; then
    log "ERRO: Falha ao analisar a estrutura do banco de dados."
    exit 1
fi
log "Análise da estrutura do banco de dados concluída com sucesso."

# 3. Limpar o banco de dados
log "Etapa 3: Limpando o banco de dados..."
sudo ./scripts/clean_database.sh $DRY_RUN
if [ $? -ne 0 ]; then
    log "ERRO: Falha ao limpar o banco de dados."
    exit 1
fi
log "Limpeza do banco de dados concluída com sucesso."

# 4. Verificar a integridade do banco de dados
log "Etapa 4: Verificando a integridade do banco de dados..."
sudo ./scripts/verify_database.sh
if [ $? -ne 0 ]; then
    log "ERRO: Falha ao verificar a integridade do banco de dados."
    exit 1
fi
log "Verificação da integridade do banco de dados concluída com sucesso."

# 5. Configurar o ENT e Atlas
log "Etapa 5: Configurando o ENT e Atlas..."
./scripts/setup_ent_atlas.sh
if [ $? -ne 0 ]; then
    log "ERRO: Falha ao configurar o ENT e Atlas."
    exit 1
fi
log "Configuração do ENT e Atlas concluída com sucesso."

# 6. Executar migrações Atlas (apenas se não estiver em modo dry run)
if [ "$DRY_RUN" == "" ]; then
    log "Etapa 6: Executando migrações Atlas..."
    ./scripts/run_atlas_migrations.sh
    if [ $? -ne 0 ]; then
        log "ERRO: Falha ao executar migrações Atlas."
        exit 1
    fi
    log "Execução de migrações Atlas concluída com sucesso."
else
    log "Etapa 6: Pulando execução de migrações Atlas (modo dry run)."
fi

log "Processo completo de limpeza e configuração concluído com sucesso."
log "Data/Hora de conclusão: $(date)"

echo ""
echo "==================================================================="
echo "                  PROCESSO CONCLUÍDO COM SUCESSO                   "
echo "==================================================================="
echo ""
echo "O banco de dados foi limpo e configurado para uso com ENT e Atlas."
echo "Próximos passos:"
echo "1. Verifique os relatórios gerados em $LOG_DIR"
echo "2. Atualize o arquivo .env com as credenciais corretas do banco de dados"
echo "3. Execute a aplicação para testar a conexão com o novo banco de dados"
echo ""
echo "Para mais informações, consulte a documentação em docs/migration/README.md"
echo "==================================================================="
