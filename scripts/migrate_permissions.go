package main

import (
	"bufio"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"

	"gopkg.in/yaml.v3"
)

// Config representa a configuração de permissões
type Config struct {
	Roles       map[string]RoleConfig `yaml:"roles"`
	PublicPages []string              `yaml:"public_pages"`
	PublicAPIs  []string              `yaml:"public_apis"`
}

// RoleConfig representa a configuração de um papel
type RoleConfig struct {
	Description string   `yaml:"description"`
	Pages       []string `yaml:"pages"`
	APIs        []string `yaml:"apis"`
}

func main() {
	// Configurar logs
	log.SetFlags(log.Ldate | log.Ltime | log.Lmicroseconds)

	// Criar configuração vazia
	config := Config{
		Roles:       make(map[string]RoleConfig),
		PublicPages: []string{"", "login", "logout", "acesso_negado", "register", "password-reset", "change-password"},
		PublicAPIs:  []string{"api/login", "api/register", "api/password/reset/request", "api/password/reset"},
	}

	// Carregar permissões de páginas
	log.Println("Carregando permissões de páginas...")
	if err := loadPagePermissions(&config); err != nil {
		log.Fatalf("Erro ao carregar permissões de páginas: %v", err)
	}

	// Carregar permissões de APIs
	log.Println("Carregando permissões de APIs...")
	if err := loadAPIPermissions(&config); err != nil {
		log.Fatalf("Erro ao carregar permissões de APIs: %v", err)
	}

	// Adicionar descrições para os papéis
	descriptions := map[string]string{
		"admin":       "Administrador do Sistema",
		"gerente":     "Gerente / Gestor de Manutenção",
		"financeiro":  "Analista Financeiro",
		"tecnico":     "Técnico de Manutenção",
		"filial":      "Gestor de Filial/Posto",
		"branch_user": "Usuário de Filial",
		"prestadores": "Prestador de Serviço",
	}

	for role, roleConfig := range config.Roles {
		if desc, ok := descriptions[role]; ok {
			roleConfig.Description = desc
			config.Roles[role] = roleConfig
		}
	}

	// Salvar configuração em YAML
	log.Println("Salvando configuração em YAML...")
	if err := saveConfig(&config, "data/permissions.yaml"); err != nil {
		log.Fatalf("Erro ao salvar configuração: %v", err)
	}

	log.Println("Migração concluída com sucesso!")
	fmt.Printf("Papéis definidos: %d\n", len(config.Roles))
	fmt.Printf("Páginas públicas: %d\n", len(config.PublicPages))
	fmt.Printf("APIs públicas: %d\n", len(config.PublicAPIs))
}

// loadPagePermissions carrega as permissões de páginas do arquivo MD
func loadPagePermissions(config *Config) error {
	file, err := os.Open("data/permissoes_paginas.md")
	if err != nil {
		return fmt.Errorf("erro ao abrir arquivo de permissões de páginas: %v", err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	inTable := false

	for scanner.Scan() {
		line := scanner.Text()

		// Verificar se estamos na seção da tabela
		if strings.Contains(line, "| Página | Perfis com acesso |") {
			inTable = true
			continue
		}

		// Pular a linha de separação da tabela
		if strings.Contains(line, "|---") {
			continue
		}

		if inTable && strings.HasPrefix(line, "|") {
			parts := strings.Split(line, "|")
			if len(parts) >= 3 {
				pagePath := strings.TrimSpace(parts[1])
				rolesStr := strings.TrimSpace(parts[2])

				// Remover as aspas e formatação markdown
				pagePath = strings.Trim(pagePath, "`")
				// Remover a barra inicial
				pagePath = strings.TrimPrefix(pagePath, "/")

				// Pular rotas de API que podem estar no arquivo de páginas
				if strings.HasPrefix(pagePath, "api/") {
					continue
				}

				// Separar os perfis
				roles := strings.Split(rolesStr, ",")

				// Adicionar a página a cada perfil
				for _, role := range roles {
					role = strings.TrimSpace(role)
					if role != "" {
						// Criar o perfil se não existir
						if _, ok := config.Roles[role]; !ok {
							config.Roles[role] = RoleConfig{
								Pages: []string{},
								APIs:  []string{},
							}
						}

						// Adicionar a página ao perfil
						roleConfig := config.Roles[role]
						roleConfig.Pages = append(roleConfig.Pages, pagePath)
						config.Roles[role] = roleConfig
					}
				}
			}
		}

		// Sair do processamento da tabela quando encontrar uma linha em branco
		if inTable && strings.TrimSpace(line) == "" {
			inTable = false
		}
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("erro ao ler arquivo de permissões de páginas: %v", err)
	}

	return nil
}

// loadAPIPermissions carrega as permissões de APIs do arquivo MD
func loadAPIPermissions(config *Config) error {
	file, err := os.Open("data/permissoes_api.md")
	if err != nil {
		return fmt.Errorf("erro ao abrir arquivo de permissões de APIs: %v", err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	inTable := false

	for scanner.Scan() {
		line := scanner.Text()

		// Verificar se estamos na seção da tabela
		if strings.Contains(line, "| Rota da API | Perfis com acesso |") {
			inTable = true
			continue
		}

		// Pular a linha de separação da tabela
		if strings.Contains(line, "|---") {
			continue
		}

		if inTable && strings.HasPrefix(line, "|") {
			parts := strings.Split(line, "|")
			if len(parts) >= 3 {
				apiPath := strings.TrimSpace(parts[1])
				rolesStr := strings.TrimSpace(parts[2])

				// Remover as aspas e formatação markdown
				apiPath = strings.Trim(apiPath, "`")
				// Remover a barra inicial
				apiPath = strings.TrimPrefix(apiPath, "/")

				// Separar os perfis
				roles := strings.Split(rolesStr, ",")

				// Adicionar a API a cada perfil
				for _, role := range roles {
					role = strings.TrimSpace(role)
					if role != "" {
						// Criar o perfil se não existir
						if _, ok := config.Roles[role]; !ok {
							config.Roles[role] = RoleConfig{
								Pages: []string{},
								APIs:  []string{},
							}
						}

						// Adicionar a API ao perfil
						roleConfig := config.Roles[role]
						roleConfig.APIs = append(roleConfig.APIs, apiPath)
						config.Roles[role] = roleConfig
					}
				}
			}
		}

		// Sair do processamento da tabela quando encontrar uma linha em branco
		if inTable && strings.TrimSpace(line) == "" {
			inTable = false
		}
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("erro ao ler arquivo de permissões de APIs: %v", err)
	}

	return nil
}

// saveConfig salva a configuração em um arquivo YAML
func saveConfig(config *Config, filePath string) error {
	// Converter para YAML
	data, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("erro ao converter para YAML: %v", err)
	}

	// Adicionar cabeçalho
	header := "# Sistema de Permissões Centralizado\n# Rede Tradição - Sistema de Manutenção\n\n"
	data = []byte(header + string(data))

	// Salvar no arquivo
	if err := ioutil.WriteFile(filePath, data, 0644); err != nil {
		return fmt.Errorf("erro ao salvar arquivo: %v", err)
	}

	return nil
}
