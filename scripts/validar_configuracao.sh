#!/bin/bash

# Script para validar se todas as configurações estão corretas
# e não há valores hardcoded no projeto

# Cores para saída
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Função para exibir mensagens com timestamp
log() {
    echo -e "${CYAN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

# Função para exibir mensagens de sucesso
success() {
    echo -e "${GREEN}[✓] $1${NC}"
}

# Função para exibir mensagens de erro
error() {
    echo -e "${RED}[✗] $1${NC}"
}

# Função para exibir mensagens de aviso
warning() {
    echo -e "${YELLOW}[!] $1${NC}"
}

log "Iniciando validação de configurações de segurança..."

# Verificar se o arquivo .env existe
if [ ! -f .env ]; then
    error "Arquivo .env não encontrado!"
    echo "Por favor, crie o arquivo .env baseado no .env.example"
    exit 1
else
    success "Arquivo .env encontrado"
fi

# Verificar se as variáveis obrigatórias estão no .env
log "Verificando variáveis obrigatórias no arquivo .env..."

required_vars=("DB_HOST" "DB_PORT" "DB_USER" "DB_PASS" "DB_NAME")
missing_vars=()

for var in "${required_vars[@]}"; do
    if ! grep -q "^${var}=" .env; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -gt 0 ]; then
    error "Variáveis obrigatórias não encontradas no .env:"
    for var in "${missing_vars[@]}"; do
        echo "  - $var"
    done
    exit 1
else
    success "Todas as variáveis obrigatórias encontradas no .env"
fi

# Verificar se há valores hardcoded nos arquivos de configuração
log "Verificando valores hardcoded nos arquivos..."

hardcoded_found=false

# Lista de strings que não devem aparecer hardcoded
hardcoded_strings=("67573962" "fcobdj_tradicao" "postgres-ag-br1-03.conteige.cloud" "54243")

# Lista de arquivos para verificar (excluindo .env, .env.example e este script)
files_to_check=(
    "internal/config/config.go"
    "internal/config/database.go"
    "internal/database/connection.go"
    "internal/database/connection_pool.go"
    "internal/database/postgresql.go"
    "internal/database/database.go"
)

for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        for string in "${hardcoded_strings[@]}"; do
            if grep -q "$string" "$file"; then
                error "Valor hardcoded encontrado em $file: $string"
                hardcoded_found=true
            fi
        done
    fi
done

if [ "$hardcoded_found" = true ]; then
    error "Valores hardcoded encontrados! Corrija antes de continuar."
    exit 1
else
    success "Nenhum valor hardcoded encontrado nos arquivos de configuração"
fi

# Verificar se o .env.example não contém credenciais reais
log "Verificando se .env.example não contém credenciais reais..."

if [ -f .env.example ]; then
    for string in "${hardcoded_strings[@]}"; do
        if grep -q "$string" .env.example; then
            warning "Credencial real encontrada em .env.example: $string"
            warning "O arquivo .env.example deve conter apenas valores de exemplo"
        fi
    done
fi

# Verificar se o arquivo .env não está sendo commitado
log "Verificando configuração do Git..."

if [ -f .gitignore ]; then
    if grep -q "^\.env$" .gitignore; then
        success "Arquivo .env está no .gitignore"
    else
        warning "Arquivo .env não está no .gitignore - adicione para evitar commit de credenciais"
    fi
else
    warning "Arquivo .gitignore não encontrado"
fi

log "Validação concluída!"
success "Configurações de segurança validadas com sucesso!"

echo ""
echo "Resumo da validação:"
echo "- Arquivo .env: ✓ Encontrado"
echo "- Variáveis obrigatórias: ✓ Todas presentes"
echo "- Valores hardcoded: ✓ Nenhum encontrado"
echo "- Segurança: ✓ Configurada corretamente"
