package main

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	_ "github.com/lib/pq"
)

func runTestFunctionality() {
	// Dados de conexão
	host := "postgres-ag-br1-03.conteige.cloud"
	port := "54243"
	user := "fcobdj_tradicao"
	password := "67573962"
	dbname := "fcobdj_tradicao"

	// Construir string de conexão
	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		host, port, user, password, dbname)

	fmt.Println("Iniciando testes de funcionalidade do banco de dados...")

	// Conecta ao banco de dados
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		log.Fatalf("Erro ao abrir conexão com o banco: %v", err)
	}
	defer db.Close()

	// Verifica se a conexão está funcionando
	err = db.Ping()
	if err != nil {
		log.Fatalf("Erro ao pingar o banco de dados: %v", err)
	}

	fmt.Println("✓ Conexão com o banco de dados PostgreSQL estabelecida com sucesso!")

	// Teste 1: Verificar se a tabela de usuários existe
	_, err = db.Exec("SELECT 1 FROM information_schema.tables WHERE table_name = 'users'")
	if err != nil {
		fmt.Println("✗ Erro ao verificar tabela de usuários:", err)
	} else {
		fmt.Println("✓ Tabela de usuários verificada com sucesso!")
	}

	// Teste 2: Contar número de usuários
	var userCount int
	err = db.QueryRow("SELECT COUNT(*) FROM users").Scan(&userCount)
	if err != nil {
		fmt.Println("✗ Erro ao contar usuários:", err)
	} else {
		fmt.Printf("✓ Banco de dados contém %d usuários\n", userCount)
	}

	// Teste 3: Verificar tabelas de manutenção
	tables := []string{"maintenance_orders", "stations", "users", "equipment"}
	for _, table := range tables {
		var exists bool
		err = db.QueryRow(`
			SELECT EXISTS (
				SELECT FROM information_schema.tables
				WHERE table_name = $1
			)`, table).Scan(&exists)

		if err != nil {
			fmt.Printf("✗ Erro ao verificar tabela %s: %v\n", table, err)
		} else if exists {
			fmt.Printf("✓ Tabela %s existe\n", table)
		} else {
			fmt.Printf("✗ Tabela %s não existe\n", table)
		}
	}

	// Teste 4: Testar inserção e leitura (temporária)
	fmt.Println("\nTestando operações de leitura e escrita...")

	// Criar tabela temporária para teste
	_, err = db.Exec(`
		CREATE TEMPORARY TABLE test_table (
			id SERIAL PRIMARY KEY,
			name TEXT NOT NULL,
			created_at TIMESTAMP DEFAULT NOW()
		)
	`)

	if err != nil {
		fmt.Println("✗ Erro ao criar tabela temporária:", err)
	} else {
		fmt.Println("✓ Tabela temporária criada com sucesso")

		// Inserir dados
		testName := fmt.Sprintf("Teste %s", time.Now().Format("15:04:05"))
		_, err = db.Exec("INSERT INTO test_table (name) VALUES ($1)", testName)

		if err != nil {
			fmt.Println("✗ Erro ao inserir dados:", err)
		} else {
			fmt.Println("✓ Dados inseridos com sucesso")

			// Ler dados
			var readName string
			err = db.QueryRow("SELECT name FROM test_table ORDER BY id DESC LIMIT 1").Scan(&readName)

			if err != nil {
				fmt.Println("✗ Erro ao ler dados:", err)
			} else if readName == testName {
				fmt.Println("✓ Dados lidos com sucesso e correspondem aos inseridos")
			} else {
				fmt.Printf("✗ Dados lidos (%s) não correspondem aos inseridos (%s)\n", readName, testName)
			}
		}
	}

	fmt.Println("\n----- Resumo dos testes -----")
	fmt.Println("✓ Conexão com o banco estabelecida")
	fmt.Println("✓ Operações básicas testadas")
	fmt.Println("Testes concluídos!")
}
