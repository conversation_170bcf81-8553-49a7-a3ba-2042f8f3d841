-- <PERSON><PERSON><PERSON> da tabela de especialidades
CREATE TABLE IF NOT EXISTS technician_specialties (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    equipment_types TEXT[] NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON> da tabela de vinculação técnico-filial
CREATE TABLE IF NOT EXISTS technician_branches (
    technician_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    branch_id INTEGER NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
    specialty_id INTEGER NOT NULL REFERENCES technician_specialties(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (technician_id, branch_id, specialty_id)
);
