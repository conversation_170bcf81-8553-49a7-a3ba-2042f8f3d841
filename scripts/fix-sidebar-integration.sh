#!/bin/bash

# Script para corrigir problemas de integração da sidebar no projeto Shell Tradição
# Este script remove referências ao arquivo sidebar-fix.js que causa conflitos
# e verifica se a integração da sidebar está correta em todas as páginas

echo "🔧 Iniciando processo de correção da integração da sidebar..."

# Contador de arquivos modificados
MODIFIED_COUNT=0

# 1. Remover referências ao sidebar-fix.js
echo "🔍 Procurando referências ao sidebar-fix.js..."
FILES_WITH_SIDEBAR_FIX=$(grep -l "sidebar-fix.js" --include="*.html" -r web/templates/)

if [ -n "$FILES_WITH_SIDEBAR_FIX" ]; then
    echo "📄 Encontradas referências nos seguintes arquivos:"
    echo "$FILES_WITH_SIDEBAR_FIX"
    
    for FILE in $FILES_WITH_SIDEBAR_FIX; do
        echo "✂️  Removendo referência em $FILE"
        # Substituir a linha que contém sidebar-fix.js
        sed -i '/sidebar-fix.js/d' "$FILE"
        MODIFIED_COUNT=$((MODIFIED_COUNT + 1))
    done
else
    echo "✅ Nenhuma referência ao sidebar-fix.js encontrada."
fi

# 2. Verificar se as páginas estão usando o template sidebar corretamente
echo "🔍 Verificando a integração correta do template sidebar..."
FILES_WITH_WRONG_SIDEBAR=$(grep -l "<div class=\"sidebar" --include="*.html" -r web/templates/ | grep -v "sidebar.html")

if [ -n "$FILES_WITH_WRONG_SIDEBAR" ]; then
    echo "⚠️  Os seguintes arquivos podem ter implementações personalizadas da sidebar:"
    echo "$FILES_WITH_WRONG_SIDEBAR"
    echo "⚠️  Verifique manualmente se esses arquivos devem usar {{ template \"sidebar\" . }} em vez disso."
else
    echo "✅ Todas as páginas parecem estar usando o template sidebar corretamente."
fi

# 3. Verificando arquivos CSS conflitantes
echo "🔍 Verificando arquivos CSS que podem conflitar com sidebar.css..."
FILES_WITH_SIDEBAR_CSS=$(grep -l "\.sidebar" --include="*.css" -r web/static/css/ | grep -v "sidebar.css" | grep -v "sidebar-profile.css")

if [ -n "$FILES_WITH_SIDEBAR_CSS" ]; then
    echo "⚠️  Os seguintes arquivos CSS podem ter estilos conflitantes:"
    echo "$FILES_WITH_SIDEBAR_CSS"
    echo "⚠️  Verifique manualmente esses arquivos para remover estilos conflitantes."
else
    echo "✅ Nenhum arquivo CSS com potencial conflito encontrado."
fi

# 4. Verificar se o localStorage está sendo manipulado incorretamente
echo "🔍 Verificando manipulação incorreta do localStorage para sidebar..."
FILES_WITH_SIDEBAR_STORAGE=$(grep -l "localStorage.*sidebar" --include="*.js" -r web/static/js/ | grep -v "sidebar.js")

if [ -n "$FILES_WITH_SIDEBAR_STORAGE" ]; then
    echo "⚠️  Os seguintes arquivos JavaScript podem manipular localStorage da sidebar:"
    echo "$FILES_WITH_SIDEBAR_STORAGE"
    echo "⚠️  Verifique manualmente esses arquivos para corrigir conflitos."
else
    echo "✅ Nenhuma manipulação incorreta do localStorage encontrada."
fi

# Resultado final
echo ""
echo "🏁 Processo de correção concluído!"
echo "📊 $MODIFIED_COUNT arquivos foram modificados."
echo "📚 A documentação está disponível em docs/sidebar-integration.md"
echo ""
echo "ℹ️  Para mais informações sobre a integração da sidebar, consulte a documentação."

exit 0 