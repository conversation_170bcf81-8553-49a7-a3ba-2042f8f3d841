@echo off
echo Sincronizando arquivos para o Kali Linux...
echo Esta operação pode demorar alguns minutos, dependendo do tamanho do projeto.

REM Cria a pasta de destino no Kali Linux
ssh -p 2222 root@************ "mkdir -p /root/projeto"

REM Sincroniza os arquivos para o Kali Linux
REM Exclui arquivos binários e pastas grandes
scp -P 2222 -r .\api .\cmd .\handlers .\internal .\migrations .\services .\web .\*.go .\go.* root@************:/root/projeto/

REM Cria o script de compilação otimizada
ssh -p 2222 root@************ "cat > /root/projeto/build.sh << 'EOF'
#!/bin/bash

# Configurações de otimização
export GOGC=off
export GOAMD64=v3
export GOMAXPROCS=8
export GOCACHE=/tmp/go-cache

# Limpar binários antigos
mkdir -p bin
rm -f bin/app

# Compilar com otimizações
echo \"Compilando com otimizações...\"
time go build -o bin/app -ldflags=\"-s -w\" ./cmd/main.go

# Verificar o tamanho do binário
echo \"Tamanho do binário:\"
ls -lh bin/app

echo \"Compilação concluída!\"
EOF"

REM Torna o script executável
ssh -p 2222 root@************ "chmod +x /root/projeto/build.sh"

echo Sincronização concluída!
