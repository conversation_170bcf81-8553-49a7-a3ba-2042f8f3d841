package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"tradicao/internal/models"
	"tradicao/internal/repository"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func mainListFilialEquipment() {
	// Verificar se foi fornecido um ID de filial
	if len(os.Args) < 2 {
		log.Fatalf("Uso: %s <filial_id>", os.Args[0])
	}

	filialID, err := strconv.Atoi(os.Args[1])
	if err != nil {
		log.Fatalf("ID de filial inválido: %v", err)
	}

	// Configurar conexão com o banco de dados
	dsn := "postgresql://fcobdj_tradicao:<EMAIL>:54243/fcobdj_tradicao?sslmode=disable"

	// Configurar logger do GORM
	gormLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold: time.Second,
			LogLevel:      logger.Info,
			Colorful:      true,
		},
	)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Criar repositório de equipamentos
	equipmentRepo := repository.NewEquipmentRepository(db)

	// Buscar equipamentos da filial
	equipments, err := equipmentRepo.FindByBranch(context.Background(), uint(filialID))
	if err != nil {
		log.Fatalf("Erro ao buscar equipamentos da filial %d: %v", filialID, err)
	}

	// Exibir equipamentos
	fmt.Printf("=== Equipamentos da Filial %d ===\n", filialID)
	fmt.Println("ID\tNome\tTipo\tNúmero de Série\tStatus\tFilial")
	fmt.Println("--------------------------------------------------")

	if len(equipments) == 0 {
		fmt.Println("Nenhum equipamento encontrado para esta filial.")
	} else {
		for _, equipment := range equipments {
			fmt.Printf("%d\t%s\t%s\t%s\t%s\t%d\n",
				equipment.ID,
				equipment.Name,
				equipment.Type,
				equipment.SerialNumber,
				equipment.Status,
				equipment.BranchID)
		}
	}

	// Agora vamos verificar se o usuário <EMAIL> tem o BranchID correto
	var user models.User
	result := db.Where("email = ?", "<EMAIL>").First(&user)
	if result.Error != nil {
		log.Printf("Erro ao buscar usuário <EMAIL>: %v", result.Error)
	} else {
		fmt.Printf("\n=== Informações do Usuário ===\n")
		fmt.Printf("ID: %d\n", user.ID)
		fmt.Printf("Nome: %s\n", user.Name)
		fmt.Printf("Email: %s\n", user.Email)
		fmt.Printf("Perfil: %s\n", user.Role)
		if user.BranchID != nil {
			fmt.Printf("BranchID: %d\n", *user.BranchID)
		} else {
			fmt.Printf("BranchID: nil\n")
		}
	}
}
