package main

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// Extensões de arquivos temporários comuns
var tempExtensions = []string{
	".tmp", ".bak", ".old", ".backup", ".swp", ".swo",
	".log", ".cache", "~", ".DS_Store", "Thumbs.db",
}

// Padrões de nomes de arquivos que provavelmente são temporários
var tempPatterns = []string{
	"untitled", "temp", "tmp", "draft", "copy of",
	"backup", ".#", "teste", "exemplo", "rascunho",
}

func runCleanup() {
	startPath := "."
	if len(os.Args) > 1 {
		startPath = os.Args[1]
	}

	fmt.Println("Iniciando varredura de arquivos temporários em:", startPath)
	fmt.Println("Data e hora:", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Println()

	var tempFiles []string
	var emptyDirs []string
	var duplicateFiles []string

	// Mapeia arquivos encontrados para detecção de duplicatas
	workflowFiles := make(map[string][]string)

	err := filepath.Walk(startPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			fmt.Printf("Erro acessando %s: %v\n", path, err)
			return filepath.SkipDir
		}

		// Ignora diretórios ocultos e node_modules
		if info.IsDir() {
			base := filepath.Base(path)
			if strings.HasPrefix(base, ".") || base == "node_modules" || base == "vendor" {
				return filepath.SkipDir
			}

			// Verifica diretórios vazios
			empty, err := isDirEmpty(path)
			if err == nil && empty && path != "." {
				emptyDirs = append(emptyDirs, path)
			}
			return nil
		}

		// Verifica arquivos temporários por extensão
		ext := strings.ToLower(filepath.Ext(path))
		for _, tempExt := range tempExtensions {
			if ext == tempExt {
				tempFiles = append(tempFiles, path)
				return nil
			}
		}

		// Verifica arquivos temporários por padrão no nome
		base := strings.ToLower(filepath.Base(path))
		for _, pattern := range tempPatterns {
			if strings.Contains(base, pattern) {
				tempFiles = append(tempFiles, path)
				return nil
			}
		}

		// Captura arquivos de workflow duplicados
		if strings.Contains(path, "workflow") || strings.Contains(path, "Workflow") {
			fileName := filepath.Base(path)
			workflowFiles[fileName] = append(workflowFiles[fileName], path)
		}

		return nil
	})

	if err != nil {
		fmt.Printf("Erro durante a varredura: %v\n", err)
		return
	}

	// Identifica arquivos de workflow duplicados
	for _, paths := range workflowFiles {
		if len(paths) > 1 {
			// O primeiro arquivo é mantido, os demais são considerados duplicados
			for i := 1; i < len(paths); i++ {
				duplicateFiles = append(duplicateFiles, paths[i])
			}
		}
	}

	// Exibe resultados
	fmt.Println("=== RELATÓRIO DE VARREDURA ===")

	// Arquivos temporários
	fmt.Printf("\n1. Arquivos temporários encontrados: %d\n", len(tempFiles))
	for _, file := range tempFiles {
		fmt.Printf("   - %s\n", file)
	}

	// Diretórios vazios
	fmt.Printf("\n2. Diretórios vazios encontrados: %d\n", len(emptyDirs))
	for _, dir := range emptyDirs {
		fmt.Printf("   - %s\n", dir)
	}

	// Arquivos duplicados
	fmt.Printf("\n3. Arquivos de workflow duplicados: %d\n", len(duplicateFiles))
	for _, file := range duplicateFiles {
		fmt.Printf("   - %s\n", file)
	}

	// Recomendações
	fmt.Println("\n=== RECOMENDAÇÕES ===")

	if len(tempFiles) > 0 || len(emptyDirs) > 0 || len(duplicateFiles) > 0 {
		fmt.Println("Para remover os arquivos temporários e duplicados, execute:")
		fmt.Println("go run scripts/cleanup.go --remove")

		if len(emptyDirs) > 0 {
			fmt.Println("\nPara remover os diretórios vazios, execute:")
			fmt.Println("go run scripts/cleanup.go --remove-empty-dirs")
		}
	} else {
		fmt.Println("Nenhum arquivo desnecessário encontrado. O projeto está limpo!")
	}
}

// Verifica se um diretório está vazio
// Retorna true se o diretório não contiver nenhum arquivo ou subdiretório
// Útil para identificar diretórios que podem ser removidos durante a limpeza
func isDirEmpty(path string) (bool, error) {
	f, err := os.Open(path)
	if err != nil {
		return false, err
	}
	defer f.Close()

	_, err = f.Readdirnames(1)
	if err == io.EOF {
		return true, nil
	}
	if err != nil {
		return false, err
	}

	return false, nil
}

// readLines lê todas as linhas de um arquivo
// Atualmente não utilizada, mas mantida para uso futuro
func readLines(filePath string) ([]string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var lines []string
	buf := make([]byte, 1024*1024) // Buffer de 1MB
	bytesReadTotal := 0
	for {
		n, err := file.Read(buf[bytesReadTotal:])
		bytesReadTotal += n
		if err != nil {
			if err == io.EOF {
				break
			}
			return nil, err
		}
		if bytesReadTotal == len(buf) {
			// Buffer cheio, processa ou realoca se necessário (simplificado aqui)
			log.Printf("Aviso: Buffer cheio ao ler %s, pode truncar linhas longas", filePath)
			break
		}
	}

	content := string(buf[:bytesReadTotal])
	lines = strings.Split(content, "\n")
	return lines, nil
}
