#!/usr/bin/env python3
import re

# Função para extrair informações de uma linha
def extract_info(line):
    # Padrão para extrair nome, endereço, cidade/estado e telefone
    pattern = r'(.*?) - (.*?), (.*?), (.*?)/(.*?) - (.*)'
    match = re.match(pattern, line)
    
    if match:
        name = match.group(1)
        address = match.group(2)
        neighborhood = match.group(3)
        city = match.group(4)
        state = match.group(5)
        phone = match.group(6)
        
        # Extrair o número da filial (se aplicável)
        filial_num = None
        if name.startswith("Filial "):
            filial_num = name.split(" ")[1]
        
        # Gerar email
        if name == "Matriz":
            email = "<EMAIL>"
        else:
            email = f"filial{filial_num}@tradicao.com"
        
        return {
            "name": name,
            "address": address,
            "neighborhood": neighborhood,
            "city": city,
            "state": state,
            "phone": phone,
            "email": email
        }
    return None

# Ler o arquivo
with open("./data/filiais_lista.txt", "r", encoding="utf-8") as f:
    lines = f.readlines()

# Processar cada linha
filiais = []
for line in lines:
    line = line.strip()
    if line:
        info = extract_info(line)
        if info:
            filiais.append(info)

# Gerar comandos SQL
sql_commands = []
for i, filial in enumerate(filiais):
    # Calcular o ID (Matriz = 100, Filial 01 = 101, etc.)
    if filial["name"] == "Matriz":
        id = 100
    else:
        filial_num = filial["name"].split(" ")[1]
        id = 100 + int(filial_num)
    
    # Gerar comando SQL
    sql = f"""UPDATE branches SET 
    address = '{filial["address"]}',
    city = '{filial["city"]}',
    state = '{filial["state"]}',
    phone = '{filial["phone"]}',
    email = '{filial["email"]}'
WHERE id = {id};"""
    sql_commands.append(sql)

# Escrever os comandos SQL em um arquivo
with open("update_filiais.sql", "w", encoding="utf-8") as f:
    for sql in sql_commands:
        f.write(sql + "\n\n")

print(f"Gerados {len(sql_commands)} comandos SQL para atualização das filiais.")
