//go:build test_server

package main

import (
	"fmt"
	"html/template"
	"log"
	"net/http"
)

type PageData struct {
	Title      string
	ActivePage string
	User       map[string]string
}

func main() {
	// Rota principal que mostra a dashboard com menu lateral
	http.HandleFunc("/dashboard", func(w http.ResponseWriter, r *http.Request) {
		tmpl, err := template.ParseFiles(
			"web/templates/layouts/base_layout.html",
			"web/templates/layouts/sidebar.html",
			"web/templates/dashboard/dashboard.html",
		)
		if err != nil {
			log.Printf("Erro ao carregar templates: %v", err)
			http.Error(w, "Erro interno do servidor", http.StatusInternalServerError)
			return
		}

		data := PageData{
			Title:      "Dashboard - Rede Tradição Shell",
			ActivePage: "dashboard",
			User: map[string]string{
				"Name": "Usuário Demonstração",
				"Role": "Administrador",
			},
		}

		// Renderiza a página
		if err := tmpl.ExecuteTemplate(w, "base_layout.html", data); err != nil {
			log.Printf("Erro ao renderizar template: %v", err)
			http.Error(w, "Erro interno do servidor", http.StatusInternalServerError)
		}
	})

	// Rota para a página de login
	http.HandleFunc("/login", func(w http.ResponseWriter, r *http.Request) {
		tmpl, err := template.ParseFiles("web/templates/login/login.html")
		if err != nil {
			log.Printf("Erro ao carregar template de login: %v", err)
			http.Error(w, "Erro interno do servidor", http.StatusInternalServerError)
			return
		}
		if err := tmpl.Execute(w, nil); err != nil {
			log.Printf("Erro ao renderizar template de login: %v", err)
			http.Error(w, "Erro interno do servidor", http.StatusInternalServerError)
		}
	})

	// Rota para a página inicial
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		http.Redirect(w, r, "/dashboard", http.StatusSeeOther)
	})

	// Arquivos estáticos
	fs := http.FileServer(http.Dir("web/static"))
	http.Handle("/static/", http.StripPrefix("/static/", fs))

	// Inicia o servidor na porta 5000
	port := "5000"
	log.Printf("Servidor de teste iniciado na porta %s\n", port)
	if err := http.ListenAndServe(fmt.Sprintf("0.0.0.0:%s", port), nil); err != nil {
		log.Fatalf("Erro ao iniciar servidor: %v", err)
	}
}
