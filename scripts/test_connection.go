package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	_ "github.com/lib/pq"
)

func runTestConnection() {
	// Usar variáveis de ambiente ou valores padrão
	host := os.Getenv("DB_HOST")
	port := os.Getenv("DB_PORT")
	user := os.Getenv("DB_USER")
	password := os.Getenv("DB_PASS")
	dbname := os.Getenv("DB_NAME")

	if host == "" || port == "" || user == "" || password == "" || dbname == "" {
		log.Println("Variáveis de ambiente não encontradas, usando configuração padrão")
		host = "postgres-ag-br1-03.conteige.cloud"
		port = "54243"
		user = "fcobdj_tradicao"
		password = "67573962"
		dbname = "fcobdj_tradicao"
	}

	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		host, port, user, password, dbname)

	fmt.Println("Testando conexão com o banco de dados PostgreSQL...")

	db, err := sql.Open("postgres", connStr)
	if err != nil {
		log.Fatalf("Erro ao abrir conexão: %v", err)
	}
	defer db.Close()

	err = db.Ping()
	if err != nil {
		log.Fatalf("Erro ao conectar: %v", err)
	}

	fmt.Println("Conexão estabelecida com sucesso!")
}
