package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"tradicao/internal/models"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func setupSecurity() {
	// Conectar ao banco de dados
	dsn := os.Getenv("DATABASE_URL")
	if dsn == "" {
		log.Fatal("Variável de ambiente DATABASE_URL não configurada")
	}

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Auto-migrar os modelos de segurança
	db.AutoMigrate(
		&models.User{},
		&models.SecurityPolicy{},
		&models.AuditLog{},
	)

	// Verificar se existe um usuário admin
	var adminCount int64
	db.Model(&models.User{}).Where("role = ?", models.RoleAdmin).Count(&adminCount)

	if adminCount == 0 {
		// Criar admin padrão se não existir
		adminPassword := os.Getenv("ADMIN_PASSWORD")
		if adminPassword == "" {
			adminPassword = "Admin@123" // Senha padrão para desenvolvimento
			fmt.Println("AVISO: Usando senha padrão para admin. Altere imediatamente em produção!")
		}

		// Hash da senha
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(adminPassword), bcrypt.DefaultCost)
		if err != nil {
			log.Fatalf("Erro ao gerar hash da senha: %v", err)
		}

		// Criar usuário admin
		admin := models.User{
			Name:      "Administrador",
			Email:     "<EMAIL>",
			Password:  string(hashedPassword),
			Role:      models.RoleAdmin,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}

		if err := db.Create(&admin).Error; err != nil {
			log.Fatalf("Erro ao criar usuário admin: %v", err)
		}

		fmt.Println("Usuário admin criado com sucesso!")
	}

	// Verificar se existe uma política de segurança
	var policyCount int64
	db.Model(&models.SecurityPolicy{}).Count(&policyCount)

	if policyCount == 0 {
		// Criar política padrão se não existir
		policy := models.SecurityPolicy{
			Name:                       "Política Padrão",
			PasswordMinLength:          8,
			PasswordRequireUppercase:   true,
			PasswordRequireNumber:      true,
			PasswordRequireSpecialChar: true,
			PasswordExpiryDays:         90,
			MaxLoginAttempts:           5,
			LockoutDurationMinutes:     30,
			Enable2FA:                  false,
			SessionTimeoutMinutes:      60,
			CreatedAt:                  time.Now(),
			UpdatedAt:                  time.Now(),
		}

		if err := db.Create(&policy).Error; err != nil {
			log.Fatalf("Erro ao criar política de segurança: %v", err)
		}

		fmt.Println("Política de segurança padrão criada com sucesso!")
	}

	fmt.Println("Configuração de segurança concluída!")
}
