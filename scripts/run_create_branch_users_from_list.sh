#!/bin/bash

# Cores para saída
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Iniciando script para criar usuários de filiais a partir da lista...${NC}"

# Verificar se o Go está instalado
if ! command -v go &> /dev/null; then
    echo -e "${RED}Go não está instalado. Por favor, instale o Go para executar este script.${NC}"
    exit 1
fi

# Verificar se o arquivo de lista de filiais existe
if [ ! -f "data/filiais_lista.txt" ]; then
    echo -e "${RED}Arquivo de lista de filiais não encontrado em data/filiais_lista.txt${NC}"
    echo -e "${YELLOW}Por favor, crie o arquivo com a lista de filiais no formato:${NC}"
    echo -e "Filial XX - Endereço, Bairro, Cidade/UF - (XX)XXXX-XXXX"
    exit 1
fi

# Diretório do projeto
PROJECT_DIR=$(pwd)
echo -e "Diretório do projeto: ${PROJECT_DIR}"

# Compilar e executar o script
echo -e "${YELLOW}Executando script de criação de usuários...${NC}"
go run scripts/create_branch_users_from_list.go

# Verificar resultado
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Script executado com sucesso!${NC}"
    echo -e "${YELLOW}Usuários de filiais foram criados com a senha padrão: tradicaosistema${NC}"
    echo -e "${YELLOW}Recomendamos alterar essas senhas após o primeiro login.${NC}"
else
    echo -e "${RED}Erro ao executar o script.${NC}"
    exit 1
fi

echo -e "${GREEN}Processo concluído!${NC}"
