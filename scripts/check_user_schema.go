package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	_ "github.com/lib/pq"
)

func runCheckUserSchema() {
	// Usar variáveis de ambiente ou valores padrão
	host := os.Getenv("DB_HOST")
	port := os.Getenv("DB_PORT")
	user := os.Getenv("DB_USER")
	password := os.Getenv("DB_PASS")
	dbname := os.Getenv("DB_NAME")

	if host == "" || port == "" || user == "" || password == "" || dbname == "" {
		log.Fatal("ERRO: Variáveis de ambiente obrigatórias não encontradas! Configure: DB_HOST, DB_PORT, DB_USER, DB_PASS, DB_NAME")
	}

	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		host, port, user, password, dbname)

	fmt.Println("Conectando ao banco de dados PostgreSQL...")

	db, err := sql.Open("postgres", connStr)
	if err != nil {
		log.Fatalf("Erro ao abrir conexão: %v", err)
	}
	defer db.Close()

	err = db.Ping()
	if err != nil {
		log.Fatalf("Erro ao conectar: %v", err)
	}

	// Verificar estrutura da tabela users
	fmt.Println("\nEstrutura da tabela 'users':")
	rows, err := db.Query(`
		SELECT column_name, data_type, character_maximum_length
		FROM information_schema.columns
		WHERE table_name = 'users'
		ORDER BY ordinal_position;
	`)
	if err != nil {
		log.Fatalf("Erro ao consultar estrutura: %v", err)
	}
	defer rows.Close()

	fmt.Printf("%-20s %-15s %-10s\n", "COLUNA", "TIPO", "TAMANHO")
	fmt.Println("------------------------------------------------")

	for rows.Next() {
		var colName, dataType string
		var maxLength sql.NullInt64

		if err := rows.Scan(&colName, &dataType, &maxLength); err != nil {
			log.Fatalf("Erro ao ler linha: %v", err)
		}

		lenStr := "NULL"
		if maxLength.Valid {
			lenStr = fmt.Sprintf("%d", maxLength.Int64)
		}

		fmt.Printf("%-20s %-15s %-10s\n", colName, dataType, lenStr)
	}

	// Verificar alguns registros da tabela users
	fmt.Println("\nRegistros da tabela 'users':")
	userRows, err := db.Query(`
		SELECT id, name, email, type
		FROM users
		LIMIT 5;
	`)
	if err != nil {
		log.Fatalf("Erro ao consultar registros: %v", err)
	}
	defer userRows.Close()

	fmt.Printf("%-5s %-20s %-30s %-15s\n", "ID", "NOME", "EMAIL", "TIPO")
	fmt.Println("----------------------------------------------------------------")

	for userRows.Next() {
		var id int
		var name, email, userType string

		if err := userRows.Scan(&id, &name, &email, &userType); err != nil {
			log.Fatalf("Erro ao ler registro: %v", err)
		}

		fmt.Printf("%-5d %-20s %-30s %-15s\n", id, name, email, userType)
	}
}
