//go:build check_all_users

package main

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"tradicao/internal/models"
)

func main() {
	// Carregar variáveis de ambiente do arquivo .env
	if err := godotenv.Load(); err != nil {
		log.Printf("Aviso: Não foi possível carregar o arquivo .env: %v", err)
	}

	// Inicializar conexão com o banco de dados usando a string de conexão completa
	databaseURL := os.Getenv("DATABASE_URL")
	if databaseURL == "" {
		// Tentar construir a string de conexão a partir de variáveis individuais
		dbHost := os.Getenv("DB_HOST")
		dbPort := os.Getenv("DB_PORT")
		dbUser := os.Getenv("DB_USER")
		dbPass := os.Getenv("DB_PASS")
		dbName := os.Getenv("DB_NAME")

		if dbHost != "" && dbPort != "" && dbUser != "" && dbPass != "" && dbName != "" {
			databaseURL = fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
				dbHost, dbPort, dbUser, dbPass, dbName)
		} else {
			log.Fatalf("Variáveis de ambiente de banco de dados não definidas")
		}
	}

	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Buscar todos os usuários
	var users []models.User
	if err := db.Find(&users).Error; err != nil {
		log.Fatalf("Erro ao buscar usuários: %v", err)
	}

	fmt.Printf("Encontrados %d usuários no total\n\n", len(users))
	fmt.Println("Lista de usuários:")
	fmt.Println("-----------------------------")
	fmt.Printf("%-5s | %-15s | %-30s | %-15s\n", "ID", "Nome", "Email", "Tipo")
	fmt.Println("-----------------------------")

	for _, user := range users {
		fmt.Printf("%-5d | %-15s | %-30s | %-15s\n", user.ID, user.Name, user.Email, user.Role)
	}
}
