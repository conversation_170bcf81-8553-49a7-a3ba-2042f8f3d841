package main

import (
	"fmt"
	"path/filepath"
	"strings"
)

func runListAllTemplates() {
	// Diretório de templates
	templatesDir := "web/templates"

	// Encontrar todos os arquivos HTML
	templates, err := findTemplateFiles(templatesDir)
	if err != nil {
		fmt.Printf("Erro ao ler diretório de templates: %v\n", err)
		return
	}

	// Exibir templates encontrados
	fmt.Println("\n=== TEMPLATES ENCONTRADOS ===")
	for i, template := range templates {
		relativePath := strings.Replace(template, templatesDir+"/", "", 1)
		fmt.Printf("%d. %s\n", i+1, relativePath)
	}

	// Gerar código de rotas
	fmt.Println("\n=== CÓDIGO PARA ROTAS ===")
	for _, template := range templates {
		relativePath := strings.Replace(template, templatesDir+"/", "", 1)
		baseName := filepath.Base(relativePath)
		routeName := strings.TrimSuffix(baseName, filepath.Ext(baseName))

		// Ignorar templates parciais (que começam com _ ou são utilizados com "define")
		if strings.HasPrefix(baseName, "_") {
			continue
		}

		fmt.Printf(`protected.GET("/view/%s", func(c *gin.Context) {
	user, _ := c.Get("user")
	c.HTML(http.StatusOK, "%s", gin.H{
		"title":      "%s - Visualização",
		"page":       "%s",
		"ActivePage": "%s",
		"User":       user,
	})
})
`, routeName, baseName, strings.Title(routeName), routeName, routeName)
	}
}
