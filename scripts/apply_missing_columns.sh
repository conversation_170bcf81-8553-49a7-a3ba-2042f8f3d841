#!/bin/bash

# Script para aplicar a migração que adiciona colunas faltantes

# Cores para saída
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Iniciando aplicação de migração para adicionar colunas faltantes...${NC}"

# Verificar se o arquivo de migração existe
if [ ! -f "migrations/atlas/20250505_add_missing_columns.sql" ]; then
    echo -e "${RED}Erro: Arquivo de migração não encontrado!${NC}"
    exit 1
fi

# Obter variáveis de conexão do banco de dados remoto
# Estas informações devem ser atualizadas com os dados corretos do banco remoto
DB_HOST=${DB_HOST:-"db.tradicao.com"}
DB_PORT=${DB_PORT:-"5432"}
DB_NAME=${DB_NAME:-"tradicao"}
DB_USER=${DB_USER:-"tradicao_user"}
DB_PASSWORD=${DB_PASSWORD:-"tradicao_password"}

echo -e "${YELLOW}Conectando ao banco de dados: ${DB_NAME} em ${DB_HOST}:${DB_PORT}${NC}"

# Executar a migração
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f migrations/atlas/20250505_add_missing_columns.sql

# Verificar se a migração foi bem-sucedida
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Migração aplicada com sucesso!${NC}"

    # Verificar se as colunas foram adicionadas
    echo -e "${YELLOW}Verificando colunas na tabela maintenance_orders...${NC}"
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT column_name FROM information_schema.columns WHERE table_name = 'maintenance_orders' ORDER BY column_name;"

    echo -e "${YELLOW}Verificando tabela ordem_manutencao_dados...${NC}"
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'ordem_manutencao_dados');"

    echo -e "${GREEN}Processo concluído com sucesso!${NC}"
else
    echo -e "${RED}Erro ao aplicar a migração!${NC}"
    exit 1
fi

echo -e "${YELLOW}Agora você pode reiniciar o servidor para aplicar as alterações.${NC}"
