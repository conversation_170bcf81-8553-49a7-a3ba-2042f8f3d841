package main

import (
	"fmt"
	"log"
	"os"
	"strings"
)

func main() {
	// Carregar permissões do arquivo
	permissoes, err := carregarPermissoes()
	if err != nil {
		log.Fatalf("Erro ao carregar permissões: %v", err)
	}

	// Imp<PERSON>ir todas as permissões
	fmt.Println("=== Permissões Carregadas ===")
	for pagina, roles := range permissoes {
		fmt.Printf("%-30s -> %v\n", pagina, roles)
	}

	// Verificar permissão específica
	if len(os.Args) > 1 {
		pagina := os.Args[1]
		perfil := "tecnico"
		if len(os.Args) > 2 {
			perfil = os.Args[2]
		}

		// Remover a barra inicial, se houver
		pagina = strings.TrimPrefix(pagina, "/")

		// Verificar se a página existe nas permissões
		roles, existe := permissoes[pagina]
		if !existe {
			fmt.Printf("Página '%s' não encontrada nas permissões\n", pagina)
			return
		}

		// Verificar se o perfil tem permissão
		temPermissao := false
		for _, role := range roles {
			if role == perfil {
				temPermissao = true
				break
			}
		}

		fmt.Printf("\nVerificação específica:\n")
		fmt.Printf("Página: %s\n", pagina)
		fmt.Printf("Perfil: %s\n", perfil)
		fmt.Printf("Perfis com permissão: %v\n", roles)
		fmt.Printf("Tem permissão: %t\n", temPermissao)
	}
}

func carregarPermissoes() (map[string][]string, error) {
	permissoes := make(map[string][]string)

	// Carregar permissões do arquivo
	file, err := os.Open("data/permissoes_paginas.md")
	if err != nil {
		return nil, fmt.Errorf("erro ao abrir arquivo de permissões: %v", err)
	}
	defer file.Close()

	// Ler o arquivo linha por linha
	content, err := os.ReadFile("data/permissoes_paginas.md")
	if err != nil {
		return nil, fmt.Errorf("erro ao ler arquivo de permissões: %v", err)
	}

	lines := strings.Split(string(content), "\n")
	inTable := false

	for _, line := range lines {
		// Verificar se estamos na seção da tabela
		if strings.Contains(line, "| Página | Perfis com acesso |") {
			inTable = true
			continue
		}

		// Pular a linha de separação da tabela
		if strings.Contains(line, "|---") {
			continue
		}

		if inTable && strings.HasPrefix(line, "|") {
			parts := strings.Split(line, "|")
			if len(parts) >= 3 {
				pagePath := strings.TrimSpace(parts[1])
				rolesStr := strings.TrimSpace(parts[2])

				// Remover as aspas e formatação markdown
				pagePath = strings.Trim(pagePath, "`")
				// Remover a barra inicial
				pagePath = strings.TrimPrefix(pagePath, "/")

				// Separar os perfis
				roles := strings.Split(rolesStr, ",")

				// Armazenar a lista de perfis limpos
				var cleanRoles []string
				for _, role := range roles {
					role = strings.TrimSpace(role)
					if role != "" {
						cleanRoles = append(cleanRoles, role)
					}
				}

				// Adicionar ao mapa
				permissoes[pagePath] = cleanRoles
			}
		}

		// Sair do processamento da tabela quando encontrar uma linha em branco
		if inTable && strings.TrimSpace(line) == "" {
			inTable = false
		}
	}

	return permissoes, nil
}
