#!/bin/bash

# Cores para saída
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Iniciando reorganização de filiais e usuários...${NC}"

# Verificar se o Go está instalado
if ! command -v go &> /dev/null; then
    echo -e "${RED}Go não está instalado. Por favor, instale o Go para executar este script.${NC}"
    exit 1
fi

# Verificar se o arquivo de lista de filiais existe
if [ ! -f "data/filiais_lista.txt" ]; then
    echo -e "${RED}Arquivo de lista de filiais não encontrado em data/filiais_lista.txt${NC}"
    echo -e "${YELLOW}Criando arquivo a partir da lista original...${NC}"

    # Verificar se o arquivo original existe
    if [ ! -f "filiais/filiais " ]; then
        echo -e "${RED}Arquivo original de filiais não encontrado em filiais/filiais${NC}"
        exit 1
    fi

    # Copiar o conteúdo do arquivo original
    cp "filiais/filiais " "data/filiais_lista.txt"
    echo -e "${GREEN}Arquivo de lista de filiais criado com sucesso!${NC}"
fi

# Diretório do projeto
PROJECT_DIR=$(pwd)
echo -e "Diretório do projeto: ${PROJECT_DIR}"

# Fazer backup do banco de dados antes de executar o script
echo -e "${YELLOW}ATENÇÃO: Este script irá remover todas as filiais e usuários de filiais existentes.${NC}"
echo -e "${YELLOW}Recomendamos fazer um backup do banco de dados antes de continuar.${NC}"
echo -e "${YELLOW}Continuando automaticamente...${NC}"

# Remover filiais e usuários existentes
echo -e "${YELLOW}Removendo filiais e usuários existentes...${NC}"
go run scripts/remove_branch_users.go

# Verificar resultado
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Remoção concluída com sucesso!${NC}"
else
    echo -e "${RED}Erro ao remover filiais e usuários.${NC}"
    echo -e "${YELLOW}Continuando mesmo assim...${NC}"
fi

# Criar novas filiais e usuários com IDs organizados
echo -e "${YELLOW}Criando novas filiais e usuários com IDs organizados...${NC}"
go run scripts/create_organized_branch_users.go

# Verificar resultado
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Criação concluída com sucesso!${NC}"
    echo -e "${YELLOW}Filiais e usuários foram criados com IDs organizados.${NC}"
    echo -e "${YELLOW}Senha padrão para todos os usuários: tradicaosistema${NC}"
else
    echo -e "${RED}Erro ao criar filiais e usuários.${NC}"
    exit 1
fi

echo -e "${GREEN}Processo de reorganização concluído!${NC}"
