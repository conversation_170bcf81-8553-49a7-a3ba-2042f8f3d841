package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"strconv"

	_ "github.com/lib/pq"
)

func mainListEquipment() {
	// Verificar se foi fornecido um ID de filial
	var branchID int
	var err error
	if len(os.Args) > 1 {
		branchID, err = strconv.Atoi(os.Args[1])
		if err != nil {
			log.Fatalf("ID de filial inválido: %v", err)
		}
	}

	// Conectar ao banco de dados
	connStr := "postgresql://fcobdj_tradicao:<EMAIL>:54243/fcobdj_tradicao?sslmode=disable"
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}
	defer db.Close()

	// Verificar a conexão
	err = db.Ping()
	if err != nil {
		log.Fatalf("Erro ao verificar a conexão com o banco de dados: %v", err)
	}

	// Consultar equipamentos
	var rows *sql.Rows
	if branchID > 0 {
		rows, err = db.Query("SELECT id, name, type, serial_number, status, branch_id FROM equipment WHERE branch_id = $1", branchID)
		fmt.Printf("=== Equipamentos da Filial %d ===\n", branchID)
	} else {
		rows, err = db.Query("SELECT id, name, type, serial_number, status, branch_id FROM equipment")
		fmt.Println("=== Todos os Equipamentos ===")
	}

	if err != nil {
		log.Fatalf("Erro ao consultar equipamentos: %v", err)
	}
	defer rows.Close()

	// Imprimir cabeçalho
	fmt.Println("ID\tNome\tTipo\tNúmero de Série\tStatus\tFilial")
	fmt.Println("--------------------------------------------------")

	// Imprimir equipamentos
	for rows.Next() {
		var id, branchID int
		var name, equipType, serialNumber, status string
		err := rows.Scan(&id, &name, &equipType, &serialNumber, &status, &branchID)
		if err != nil {
			log.Printf("Erro ao ler equipamento: %v", err)
			continue
		}
		fmt.Printf("%d\t%s\t%s\t%s\t%s\t%d\n", id, name, equipType, serialNumber, status, branchID)
	}

	if err = rows.Err(); err != nil {
		log.Fatalf("Erro ao iterar sobre os resultados: %v", err)
	}
}
