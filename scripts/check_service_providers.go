package main

import (
	"fmt"
	"log"
	"os"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// Configurar conexão com o banco de dados
	dsn := "postgresql://fcobdj_tradicao:<EMAIL>:54243/fcobdj_tradicao?sslmode=disable"
	
	// Configurar logger do GORM
	gormLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold: 0,
			LogLevel:      logger.Info,
			Colorful:      true,
		},
	)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Verificar se a tabela service_providers existe
	var tableExists bool
	db.Raw(`
		SELECT EXISTS (
			SELECT FROM information_schema.tables 
			WHERE table_name = 'service_providers'
		)
	`).Scan(&tableExists)

	if !tableExists {
		fmt.Println("A tabela service_providers não existe!")
		return
	}

	// Verificar a estrutura da tabela service_providers
	var columns []struct {
		ColumnName string `gorm:"column:column_name"`
		DataType   string `gorm:"column:data_type"`
	}

	db.Raw(`
		SELECT column_name, data_type
		FROM information_schema.columns
		WHERE table_name = 'service_providers'
		ORDER BY ordinal_position
	`).Scan(&columns)

	fmt.Println("=== Estrutura da tabela service_providers ===")
	fmt.Println("Coluna\t\t\tTipo")
	fmt.Println("--------------------------------------------------")
	for _, column := range columns {
		fmt.Printf("%s\t\t\t%s\n", column.ColumnName, column.DataType)
	}

	// Listar os registros da tabela service_providers
	type ServiceProvider struct {
		ID        uint   `gorm:"primaryKey"`
		Name      string
		Email     string
		Phone     string
		UserID    *uint
		CreatedAt string
		UpdatedAt string
	}

	var providers []ServiceProvider
	db.Table("service_providers").Find(&providers)

	fmt.Println("\n=== Registros da tabela service_providers ===")
	fmt.Println("ID\tNome\t\tEmail\t\tTelefone\t\tUserID")
	fmt.Println("--------------------------------------------------")
	if len(providers) == 0 {
		fmt.Println("Nenhum registro encontrado!")
	} else {
		for _, provider := range providers {
			userIDStr := "NULL"
			if provider.UserID != nil {
				userIDStr = fmt.Sprintf("%d", *provider.UserID)
			}
			fmt.Printf("%d\t%s\t\t%s\t\t%s\t\t%s\n", provider.ID, provider.Name, provider.Email, provider.Phone, userIDStr)
		}
	}
}
