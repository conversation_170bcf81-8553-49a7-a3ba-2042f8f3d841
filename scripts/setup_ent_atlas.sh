#!/bin/bash

# Script para configurar o ENT e Atlas para usar o novo banco de dados
# Uso: ./setup_ent_atlas.sh

# Configurações
LOG_DIR="logs"
mkdir -p $LOG_DIR
LOG_FILE="$LOG_DIR/setup_ent_atlas_$(date +%Y%m%d_%H%M%S).log"

# Função para registrar mensagens
log() {
    echo "$(date +"%Y-%m-%d %H:%M:%S") - $1" | tee -a $LOG_FILE
}

log "Iniciando configuração do ENT e Atlas..."

# 1. Criar diretório para o ENT se não existir
mkdir -p ent/schema
log "Diretório ent/schema criado."

# 2. Criar diretório para migrações Atlas se não existir
mkdir -p migrations/atlas
log "Diretório migrations/atlas criado."

# 3. Criar arquivo de configuração do Atlas
cat > atlas.hcl << 'EOF'
// Atlas configuration file
// See: https://atlasgo.io/atlas-schema/projects

variable "db_url" {
  type = string
}

variable "db_user" {
  type = string
}

variable "db_pass" {
  type = string
}

env "dev" {
  url = "postgres://${var.db_user}:${var.db_pass}@localhost:5432/tradicao_ent?sslmode=disable"
  
  migration {
    dir = "file://migrations/atlas"
    format = "sql"
    version_table = "schema_versions"
  }
  
  backup {
    dir = "file://backups/dev"
    retention = "7d"
  }

  src = "schema:*"
}

env "staging" {
  url = "postgres://${var.db_user}:${var.db_pass}@localhost:5432/tradicao_staging?sslmode=disable"
  
  migration {
    dir = "file://migrations/atlas"
    format = "sql"
    version_table = "schema_versions"
  }
  
  backup {
    dir = "file://backups/staging"
    retention = "14d"
  }

  src = "schema:*"
}

env "prod" {
  url = "postgres://${var.db_user}:${var.db_pass}@localhost:5432/tradicao?sslmode=disable"
  
  migration {
    dir = "file://migrations/atlas"
    format = "sql"
    version_table = "schema_versions"
  }
  
  backup {
    dir = "file://backups/prod"
    retention = "30d"
  }

  src = "schema:*"
}

// Definição do schema
schema "public" {
  charset = "utf8mb4"
  collate = "utf8mb4_unicode_ci"
}
EOF
log "Arquivo de configuração atlas.hcl criado."

# 4. Criar arquivo de schema do Atlas
cat > migrations/atlas/schema.hcl << 'EOF'
// Atlas Schema para o projeto Tradição

schema "public" {
  comment = "Esquema principal do banco de dados Tradição"
}

// Definição das tabelas principais
table "branches" {
  schema = schema.public
  comment = "Tabela de filiais/postos"
  
  column "id" {
    type = int
    auto_increment = true
  }
  column "name" {
    type = varchar(100)
    null = false
  }
  column "code" {
    type = varchar(20)
    null = false
    unique = true
  }
  column "address" {
    type = varchar(255)
    null = true
  }
  column "city" {
    type = varchar(100)
    null = true
  }
  column "state" {
    type = varchar(2)
    null = true
  }
  column "zip_code" {
    type = varchar(20)
    null = true
  }
  column "phone" {
    type = varchar(20)
    null = true
  }
  column "email" {
    type = varchar(100)
    null = true
  }
  column "type" {
    type = varchar(20)
    null = true
    default = "urban"
  }
  column "is_active" {
    type = boolean
    null = true
    default = true
  }
  column "manager_id" {
    type = int
    null = true
  }
  column "latitude" {
    type = decimal(10, 8)
    null = true
  }
  column "longitude" {
    type = decimal(11, 8)
    null = true
  }
  column "opening_hours" {
    type = varchar(255)
    null = true
  }
  column "created_at" {
    type = timestamp
    null = true
    default = sql("CURRENT_TIMESTAMP")
  }
  column "updated_at" {
    type = timestamp
    null = true
    default = sql("CURRENT_TIMESTAMP")
  }
  column "deleted_at" {
    type = timestamp
    null = true
  }
  
  primary_key {
    columns = [column.id]
  }
  
  foreign_key "fk_branches_manager" {
    columns = [column.manager_id]
    ref_columns = [table.users.column.id]
    on_delete = "SET NULL"
    on_update = "CASCADE"
  }
}

table "users" {
  schema = schema.public
  comment = "Tabela de usuários do sistema"
  
  column "id" {
    type = int
    auto_increment = true
  }
  column "name" {
    type = varchar(100)
    null = false
  }
  column "email" {
    type = varchar(100)
    null = false
    unique = true
  }
  column "password" {
    type = varchar(255)
    null = false
  }
  column "role" {
    type = varchar(50)
    null = false
  }
  column "branch_id" {
    type = int
    null = true
  }
  column "failed_attempts" {
    type = int
    null = true
    default = 0
  }
  column "blocked" {
    type = boolean
    null = true
    default = false
  }
  column "totp_secret" {
    type = varchar(255)
    null = true
    default = ""
  }
  column "totp_enabled" {
    type = boolean
    null = true
    default = false
  }
  column "last_password_change" {
    type = timestamp
    null = true
  }
  column "force_password_change" {
    type = boolean
    null = true
    default = false
  }
  column "created_at" {
    type = timestamp
    null = true
    default = sql("CURRENT_TIMESTAMP")
  }
  column "updated_at" {
    type = timestamp
    null = true
    default = sql("CURRENT_TIMESTAMP")
  }
  column "deleted_at" {
    type = timestamp
    null = true
  }
  
  primary_key {
    columns = [column.id]
  }
  
  foreign_key "fk_users_branch" {
    columns = [column.branch_id]
    ref_columns = [table.branches.column.id]
    on_delete = "SET NULL"
    on_update = "CASCADE"
  }
}

table "equipment" {
  schema = schema.public
  comment = "Tabela de equipamentos"
  
  column "id" {
    type = int
    auto_increment = true
  }
  column "name" {
    type = varchar(100)
    null = false
  }
  column "serial_number" {
    type = varchar(100)
    null = true
    unique = true
  }
  column "model" {
    type = varchar(100)
    null = true
  }
  column "brand" {
    type = varchar(100)
    null = true
  }
  column "type" {
    type = varchar(50)
    null = false
  }
  column "installation_date" {
    type = timestamp
    null = true
  }
  column "last_maintenance" {
    type = timestamp
    null = true
  }
  column "last_preventive" {
    type = timestamp
    null = true
  }
  column "next_preventive" {
    type = timestamp
    null = true
  }
  column "status" {
    type = varchar(20)
    null = true
    default = "active"
  }
  column "location" {
    type = varchar(255)
    null = true
  }
  column "notes" {
    type = text
    null = true
  }
  column "branch_id" {
    type = int
    null = true
  }
  column "created_at" {
    type = timestamp
    null = true
    default = sql("CURRENT_TIMESTAMP")
  }
  column "updated_at" {
    type = timestamp
    null = true
    default = sql("CURRENT_TIMESTAMP")
  }
  column "deleted_at" {
    type = timestamp
    null = true
  }
  
  primary_key {
    columns = [column.id]
  }
  
  foreign_key "fk_equipment_branch" {
    columns = [column.branch_id]
    ref_columns = [table.branches.column.id]
    on_delete = "SET NULL"
    on_update = "CASCADE"
  }
}

table "maintenance_orders" {
  schema = schema.public
  comment = "Tabela de ordens de manutenção"
  
  column "id" {
    type = int
    auto_increment = true
  }
  column "title" {
    type = varchar(100)
    null = false
  }
  column "description" {
    type = text
    null = true
  }
  column "status" {
    type = varchar(20)
    null = true
    default = "pending"
  }
  column "priority" {
    type = varchar(20)
    null = true
    default = "medium"
  }
  column "branch_id" {
    type = int
    null = true
  }
  column "equipment_id" {
    type = int
    null = true
  }
  column "requester_id" {
    type = int
    null = true
  }
  column "approver_id" {
    type = int
    null = true
  }
  column "technician_id" {
    type = int
    null = true
  }
  column "cancellation_reason" {
    type = text
    null = true
  }
  column "start_date" {
    type = timestamp
    null = true
  }
  column "end_date" {
    type = timestamp
    null = true
  }
  column "estimated_time" {
    type = int
    null = true
  }
  column "actual_time" {
    type = int
    null = true
  }
  column "created_at" {
    type = timestamp
    null = true
    default = sql("CURRENT_TIMESTAMP")
  }
  column "updated_at" {
    type = timestamp
    null = true
    default = sql("CURRENT_TIMESTAMP")
  }
  column "deleted_at" {
    type = timestamp
    null = true
  }
  
  primary_key {
    columns = [column.id]
  }
  
  foreign_key "fk_maintenance_orders_branch" {
    columns = [column.branch_id]
    ref_columns = [table.branches.column.id]
    on_delete = "SET NULL"
    on_update = "CASCADE"
  }
  
  foreign_key "fk_maintenance_orders_equipment" {
    columns = [column.equipment_id]
    ref_columns = [table.equipment.column.id]
    on_delete = "SET NULL"
    on_update = "CASCADE"
  }
  
  foreign_key "fk_maintenance_orders_requester" {
    columns = [column.requester_id]
    ref_columns = [table.users.column.id]
    on_delete = "SET NULL"
    on_update = "CASCADE"
  }
  
  foreign_key "fk_maintenance_orders_approver" {
    columns = [column.approver_id]
    ref_columns = [table.users.column.id]
    on_delete = "SET NULL"
    on_update = "CASCADE"
  }
  
  foreign_key "fk_maintenance_orders_technician" {
    columns = [column.technician_id]
    ref_columns = [table.users.column.id]
    on_delete = "SET NULL"
    on_update = "CASCADE"
  }
}

// Definição dos índices
index "idx_users_email" {
  table = table.users
  columns = [column.email]
  unique = true
}

index "idx_users_role" {
  table = table.users
  columns = [column.role]
}

index "idx_users_branch_id" {
  table = table.users
  columns = [column.branch_id]
}

index "idx_branches_manager_id" {
  table = table.branches
  columns = [column.manager_id]
}

index "idx_branches_is_active" {
  table = table.branches
  columns = [column.is_active]
}

index "idx_branches_city_state" {
  table = table.branches
  columns = [column.city, column.state]
}

index "idx_equipment_branch_id" {
  table = table.equipment
  columns = [column.branch_id]
}

index "idx_equipment_type" {
  table = table.equipment
  columns = [column.type]
}

index "idx_equipment_status" {
  table = table.equipment
  columns = [column.status]
}

index "idx_maintenance_orders_branch_id" {
  table = table.maintenance_orders
  columns = [column.branch_id]
}

index "idx_maintenance_orders_equipment_id" {
  table = table.maintenance_orders
  columns = [column.equipment_id]
}

index "idx_maintenance_orders_status" {
  table = table.maintenance_orders
  columns = [column.status]
}

index "idx_maintenance_orders_priority" {
  table = table.maintenance_orders
  columns = [column.priority]
}
EOF
log "Arquivo de schema Atlas criado em migrations/atlas/schema.hcl."

# 5. Criar arquivo de migração inicial
cat > migrations/atlas/001_initial_schema.sql << 'EOF'
-- Migração inicial para o esquema do projeto Tradição

-- Criar tabelas
-- Tabela: branches
CREATE TABLE branches (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) NOT NULL UNIQUE,
    address VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(2),
    zip_code VARCHAR(20),
    phone VARCHAR(20),
    email VARCHAR(100),
    type VARCHAR(20) DEFAULT 'urban',
    is_active BOOLEAN DEFAULT TRUE,
    manager_id INTEGER,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    opening_hours VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Tabela: users
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    branch_id INTEGER,
    failed_attempts INTEGER DEFAULT 0,
    blocked BOOLEAN DEFAULT FALSE,
    totp_secret VARCHAR(255) DEFAULT '',
    totp_enabled BOOLEAN DEFAULT FALSE,
    last_password_change TIMESTAMP,
    force_password_change BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Tabela: equipment
CREATE TABLE equipment (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    serial_number VARCHAR(100),
    model VARCHAR(100),
    brand VARCHAR(100),
    type VARCHAR(50) NOT NULL,
    installation_date TIMESTAMP,
    last_maintenance TIMESTAMP,
    last_preventive TIMESTAMP,
    next_preventive TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active',
    location VARCHAR(255),
    notes TEXT,
    branch_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Tabela: maintenance_orders
CREATE TABLE maintenance_orders (
    id SERIAL PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'pending',
    priority VARCHAR(20) DEFAULT 'medium',
    branch_id INTEGER,
    equipment_id INTEGER,
    requester_id INTEGER,
    approver_id INTEGER,
    technician_id INTEGER,
    cancellation_reason TEXT,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    estimated_time INTEGER,
    actual_time INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Criar índices
CREATE UNIQUE INDEX idx_users_email ON users (email);
CREATE INDEX idx_users_role ON users (role);
CREATE INDEX idx_users_branch_id ON users (branch_id);
CREATE INDEX idx_branches_manager_id ON branches (manager_id);
CREATE INDEX idx_branches_is_active ON branches (is_active);
CREATE INDEX idx_branches_city_state ON branches (city, state);
CREATE INDEX idx_equipment_branch_id ON equipment (branch_id);
CREATE INDEX idx_equipment_type ON equipment (type);
CREATE INDEX idx_equipment_status ON equipment (status);
CREATE INDEX idx_maintenance_orders_branch_id ON maintenance_orders (branch_id);
CREATE INDEX idx_maintenance_orders_equipment_id ON maintenance_orders (equipment_id);
CREATE INDEX idx_maintenance_orders_status ON maintenance_orders (status);
CREATE INDEX idx_maintenance_orders_priority ON maintenance_orders (priority);

-- Adicionar chaves estrangeiras
ALTER TABLE users ADD CONSTRAINT fk_users_branch FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE branches ADD CONSTRAINT fk_branches_manager FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE equipment ADD CONSTRAINT fk_equipment_branch FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE maintenance_orders ADD CONSTRAINT fk_maintenance_orders_branch FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE maintenance_orders ADD CONSTRAINT fk_maintenance_orders_equipment FOREIGN KEY (equipment_id) REFERENCES equipment(id) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE maintenance_orders ADD CONSTRAINT fk_maintenance_orders_requester FOREIGN KEY (requester_id) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE maintenance_orders ADD CONSTRAINT fk_maintenance_orders_approver FOREIGN KEY (approver_id) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE maintenance_orders ADD CONSTRAINT fk_maintenance_orders_technician FOREIGN KEY (technician_id) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE;

-- Adicionar constraints de validação
ALTER TABLE users ADD CONSTRAINT check_user_role CHECK (role IN ('admin', 'manager', 'financial', 'branch_user', 'technician', 'provider'));
ALTER TABLE maintenance_orders ADD CONSTRAINT check_maintenance_order_status CHECK (status IN ('pending', 'approved', 'in_progress', 'completed', 'cancelled', 'rejected'));
ALTER TABLE maintenance_orders ADD CONSTRAINT check_maintenance_order_priority CHECK (priority IN ('low', 'medium', 'high', 'critical'));
EOF
log "Arquivo de migração inicial criado em migrations/atlas/001_initial_schema.sql."

# 6. Criar arquivo .env para configuração do banco de dados
cat > .env << 'EOF'
# Configurações do Banco de Dados
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASS=postgres
DB_NAME=tradicao_ent

# Configurações da Aplicação
APP_PORT=8080
APP_ENV=development
APP_SECRET=your-secret-key

# Configurações de Log
LOG_LEVEL=debug
LOG_FILE=logs/app.log
EOF
log "Arquivo .env criado com configurações para o novo banco de dados."

# 7. Criar script para executar migrações Atlas
cat > scripts/run_atlas_migrations.sh << 'EOF'
#!/bin/bash

# Script para executar migrações Atlas
# Uso: ./scripts/run_atlas_migrations.sh [env]

# Configurações
ENV=${1:-dev}
LOG_DIR="logs"
mkdir -p $LOG_DIR
LOG_FILE="$LOG_DIR/atlas_migrations_$(date +%Y%m%d_%H%M%S).log"

# Carregar variáveis de ambiente
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
else
    echo "Arquivo .env não encontrado."
    exit 1
fi

echo "Executando migrações Atlas para ambiente: $ENV"
echo "Data/Hora: $(date)" | tee -a $LOG_FILE

# Verificar se o Atlas está instalado
if ! command -v atlas &> /dev/null; then
    echo "Atlas não está instalado. Instalando..."
    curl -sSf https://atlasgo.sh | sh
fi

# Executar migrações
atlas migrate apply \
    --env $ENV \
    --var "db_user=$DB_USER" \
    --var "db_pass=$DB_PASS" | tee -a $LOG_FILE

if [ $? -eq 0 ]; then
    echo "Migrações executadas com sucesso."
else
    echo "Erro ao executar migrações."
    exit 1
fi

echo "Data/Hora de conclusão: $(date)" | tee -a $LOG_FILE
EOF
chmod +x scripts/run_atlas_migrations.sh
log "Script para executar migrações Atlas criado em scripts/run_atlas_migrations.sh."

log "Configuração do ENT e Atlas concluída com sucesso."
log "Data/Hora de conclusão: $(date)"
