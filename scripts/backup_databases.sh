#!/bin/bash

# Script para fazer backup dos bancos de dados antes da limpeza
# Uso: sudo ./backup_databases.sh

# Configurações
BACKUP_DIR="backups/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

echo "Iniciando backup dos bancos de dados..."
echo "Data/Hora: $(date)"

# Backup do banco fcobdj_tradicao
echo "Fazendo backup do banco fcobdj_tradicao..."
sudo -u postgres pg_dump fcobdj_tradicao > $BACKUP_DIR/fcobdj_tradicao_backup.sql
if [ $? -eq 0 ]; then
    echo "Backup do banco fcobdj_tradicao concluído com sucesso."
else
    echo "ERRO: Falha no backup do banco fcobdj_tradicao."
    exit 1
fi

# Backup do banco tradicao
echo "Fazendo backup do banco tradicao..."
sudo -u postgres pg_dump tradicao > $BACKUP_DIR/tradicao_backup.sql
if [ $? -eq 0 ]; then
    echo "Backup do banco tradicao concluído com sucesso."
else
    echo "ERRO: Falha no backup do banco tradicao."
    exit 1
fi

echo "Todos os backups foram concluídos com sucesso."
echo "Backups salvos em: $BACKUP_DIR"
echo "Data/Hora de conclusão: $(date)"
