#!/bin/bash

# Cores para saída
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Iniciando script para atualizar IDs dos usuários de filiais...${NC}"

# Verificar se o Go está instalado
if ! command -v go &> /dev/null; then
    echo -e "${RED}Go não está instalado. Por favor, instale o Go para executar este script.${NC}"
    exit 1
fi

# Diretório do projeto
PROJECT_DIR=$(pwd)
echo -e "Diretório do projeto: ${PROJECT_DIR}"

# Fazer backup do banco de dados antes de executar o script
echo -e "${YELLOW}ATENÇÃO: Este script irá modificar os IDs dos usuários no banco de dados.${NC}"
echo -e "${YELLOW}Recomendamos fazer um backup do banco de dados antes de continuar.${NC}"
echo -e "${YELLOW}Deseja continuar? (s/n)${NC}"
read -r resposta

if [[ "$resposta" != "s" && "$resposta" != "S" ]]; then
    echo -e "${RED}Operação cancelada pelo usuário.${NC}"
    exit 1
fi

# Compilar e executar o script
echo -e "${YELLOW}Executando script de atualização de IDs...${NC}"
go run scripts/update_user_ids.go

# Verificar resultado
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Script executado com sucesso!${NC}"
    echo -e "${YELLOW}Os IDs dos usuários de filiais foram atualizados.${NC}"
else
    echo -e "${RED}Erro ao executar o script.${NC}"
    exit 1
fi

echo -e "${GREEN}Processo concluído!${NC}"
