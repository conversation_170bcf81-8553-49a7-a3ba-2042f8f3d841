package main

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"time"
)

// LintIssue represents a linting issue found by staticcheck
type LintIssue struct {
	Code     string `json:"code"`
	Severity string `json:"severity"`
	Message  string `json:"message"`
	File     string `json:"location.file"`
	Line     int    `json:"location.line"`
	Column   int    `json:"location.column"`
	Fixed    bool   `json:"-"`
}

// LintReport contains the results of the linting process
type LintReport struct {
	TotalIssues     int
	FixedIssues     int
	RemainingIssues int
	IssuesByFile    map[string][]LintIssue
	StartTime       time.Time
	EndTime         time.Time
}

func main() {
	runLintRobot()
}

func runLintRobot() {
	fmt.Println("=== Lint Robot ===")
	fmt.Println("Verificando e corrigindo problemas de lint no código...")

	report := &LintReport{
		IssuesByFile: make(map[string][]LintIssue),
		StartTime:    time.Now(),
	}

	// Verificar se staticcheck está instalado
	if !isStaticCheckInstalled() {
		fmt.Println("Staticcheck não está instalado. Instalando...")
		installStaticCheck()
	}

	// Executar staticcheck e capturar saída
	issues := runStaticCheck()
	report.TotalIssues = len(issues)

	// Agrupar problemas por arquivo
	for _, issue := range issues {
		report.IssuesByFile[issue.File] = append(report.IssuesByFile[issue.File], issue)
	}

	// Tentar corrigir problemas automaticamente
	fixIssues(report)

	// Gerar relatório
	report.EndTime = time.Now()
	generateReport(report)
}

// Verifica se staticcheck está instalado
func isStaticCheckInstalled() bool {
	cmd := exec.Command("staticcheck", "-version")
	err := cmd.Run()
	return err == nil
}

// Instala staticcheck
func installStaticCheck() {
	fmt.Println("Instalando staticcheck...")
	cmd := exec.Command("go", "install", "honnef.co/go/tools/cmd/staticcheck@latest")

	var out bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &out

	err := cmd.Run()
	if err != nil {
		fmt.Printf("Erro ao instalar staticcheck: %v\n", err)
		fmt.Println(out.String())
		os.Exit(1)
	}

	fmt.Println("Staticcheck instalado com sucesso!")
}

// Executa staticcheck e retorna os problemas encontrados
func runStaticCheck() []LintIssue {
	fmt.Println("Executando staticcheck...")

	cmd := exec.Command("staticcheck", "-f", "json", "./...")
	var out bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = os.Stderr

	err := cmd.Run()
	if err != nil {
		// Ignoramos o erro porque staticcheck retorna erro se encontrar problemas
		fmt.Printf("Staticcheck encontrou problemas (código de saída: %v)\n", err)
	}

	// Parsear saída JSON
	var issues []LintIssue
	scanner := bufio.NewScanner(&out)
	for scanner.Scan() {
		line := scanner.Text()
		if line == "" {
			continue
		}

		var issue LintIssue
		if err := json.Unmarshal([]byte(line), &issue); err != nil {
			fmt.Printf("Erro ao parsear saída do staticcheck: %v\n", err)
			continue
		}

		issues = append(issues, issue)
	}

	return issues
}

// Tenta corrigir problemas automaticamente
func fixIssues(report *LintReport) {
	fmt.Println("Tentando corrigir problemas automaticamente...")

	for file, issues := range report.IssuesByFile {
		// Ler o conteúdo do arquivo
		content, err := os.ReadFile(file)
		if err != nil {
			fmt.Printf("Erro ao ler arquivo %s: %v\n", file, err)
			continue
		}

		// Fazer uma cópia do conteúdo original
		originalContent := make([]byte, len(content))
		copy(originalContent, content)

		// Aplicar correções
		modified := false
		for i, issue := range issues {
			fixed := false

			// Tentar corrigir com base no código do problema
			switch {
			case strings.HasPrefix(issue.Code, "ST1000"): // Missing package comment
				content, fixed = fixMissingPackageComment(content, issue)
			case strings.HasPrefix(issue.Code, "SA4006"): // Assign to _ instead
				content, fixed = fixAssignToBlank(content, issue)
			case strings.HasPrefix(issue.Code, "SA1019"): // Deprecated function
				content, fixed = fixDeprecatedFunction(content, issue)
			case strings.HasPrefix(issue.Code, "ST1005"): // Incorrectly formatted error string
				content, fixed = fixErrorStringFormat(content, issue)
			case strings.HasPrefix(issue.Code, "SA4010"): // Redundant type conversion
				content, fixed = fixRedundantConversion(content, issue)
			}

			// Atualizar status de correção
			if fixed {
				report.IssuesByFile[file][i].Fixed = true
				report.FixedIssues++
				modified = true
			}
		}

		// Se o arquivo foi modificado, salvar as alterações
		if modified {
			err = os.WriteFile(file, content, 0644)
			if err != nil {
				fmt.Printf("Erro ao salvar alterações no arquivo %s: %v\n", file, err)
			}
		}
	}

	report.RemainingIssues = report.TotalIssues - report.FixedIssues
}

// Corrige comentários de pacote ausentes
func fixMissingPackageComment(content []byte, _ LintIssue) ([]byte, bool) {
	lines := bytes.Split(content, []byte("\n"))

	// Encontrar a linha do pacote
	packageLineIdx := -1
	packageName := ""
	packageRegex := regexp.MustCompile(`^package\s+(\w+)`)

	for i, line := range lines {
		matches := packageRegex.FindSubmatch(line)
		if len(matches) > 1 {
			packageLineIdx = i
			packageName = string(matches[1])
			break
		}
	}

	if packageLineIdx == -1 || packageName == "" {
		return content, false
	}

	// Adicionar comentário de pacote
	comment := []byte(fmt.Sprintf("// Package %s provides functionality for the %s module.", packageName, packageName))
	newLines := make([][]byte, 0, len(lines)+1)

	// Adicionar comentário antes da declaração do pacote
	newLines = append(newLines, comment)
	newLines = append(newLines, lines[packageLineIdx:]...)

	return bytes.Join(newLines, []byte("\n")), true
}

// Corrige atribuições não utilizadas
func fixAssignToBlank(content []byte, issue LintIssue) ([]byte, bool) {
	lines := bytes.Split(content, []byte("\n"))
	if issue.Line <= 0 || issue.Line > len(lines) {
		return content, false
	}

	line := lines[issue.Line-1]

	// Procurar por padrão de atribuição não utilizada
	varRegex := regexp.MustCompile(`(\w+)\s*:?=`)
	matches := varRegex.FindSubmatch(line)

	if len(matches) < 2 {
		return content, false
	}

	unusedVar := string(matches[1])

	// Substituir variável não utilizada por _
	newLine := bytes.ReplaceAll(line, []byte(unusedVar), []byte("_"))
	lines[issue.Line-1] = newLine

	return bytes.Join(lines, []byte("\n")), true
}

// Corrige funções obsoletas
func fixDeprecatedFunction(content []byte, issue LintIssue) ([]byte, bool) {
	// Este é um caso mais complexo que geralmente requer intervenção manual
	// Aqui apenas identificamos alguns casos comuns

	lines := bytes.Split(content, []byte("\n"))
	if issue.Line <= 0 || issue.Line > len(lines) {
		return content, false
	}

	line := lines[issue.Line-1]

	// Alguns casos comuns de substituição
	replacements := map[string]string{
		"io/ioutil.ReadFile":  "os.ReadFile",
		"io/ioutil.WriteFile": "os.WriteFile",
		"io/ioutil.ReadDir":   "os.ReadDir",
		"io/ioutil.TempFile":  "os.CreateTemp",
		"io/ioutil.TempDir":   "os.MkdirTemp",
		"io/ioutil.NopCloser": "io.NopCloser",
		"io/ioutil.ReadAll":   "io.ReadAll",
	}

	fixed := false
	for old, new := range replacements {
		if bytes.Contains(line, []byte(old)) {
			line = bytes.ReplaceAll(line, []byte(old), []byte(new))
			fixed = true

			// Se substituímos ioutil, precisamos atualizar os imports
			if strings.HasPrefix(old, "io/ioutil.") {
				content = updateImports(content, "io/ioutil", "io", "os")
			}
		}
	}

	if fixed {
		lines[issue.Line-1] = line
		return bytes.Join(lines, []byte("\n")), true
	}

	return content, false
}

// Atualiza as importações
func updateImports(content []byte, oldImport string, newImports ...string) []byte {
	// Encontrar bloco de importação
	importRegex := regexp.MustCompile(`import\s*\(([\s\S]*?)\)`)
	matches := importRegex.FindSubmatch(content)

	if len(matches) < 2 {
		return content
	}

	importBlock := string(matches[1])
	oldImportRegex := regexp.MustCompile(`\s*"` + oldImport + `"\s*`)

	// Remover importação antiga
	newImportBlock := oldImportRegex.ReplaceAllString(importBlock, "")

	// Adicionar novas importações se ainda não existirem
	for _, newImport := range newImports {
		if !strings.Contains(newImportBlock, `"`+newImport+`"`) {
			newImportBlock += fmt.Sprintf("\n\t\"%s\"", newImport)
		}
	}

	// Substituir bloco de importação
	newContent := importRegex.ReplaceAllString(string(content), "import ("+newImportBlock+")")

	return []byte(newContent)
}

// Corrige strings de erro formatadas incorretamente
func fixErrorStringFormat(content []byte, issue LintIssue) ([]byte, bool) {
	lines := bytes.Split(content, []byte("\n"))
	if issue.Line <= 0 || issue.Line > len(lines) {
		return content, false
	}

	line := lines[issue.Line-1]

	// Procurar por padrões comuns de erro formatado incorretamente
	errorRegex := regexp.MustCompile(`(errors\.New|fmt\.Errorf)\(["']([A-Z])([^"']*)["']\)`)
	matches := errorRegex.FindSubmatch(line)

	if len(matches) < 4 {
		return content, false
	}

	// Converter primeira letra para minúscula
	firstChar := bytes.ToLower(matches[2])
	restOfMessage := matches[3]

	// Verificar se a mensagem termina com ponto
	if bytes.HasSuffix(restOfMessage, []byte(".")) {
		restOfMessage = restOfMessage[:len(restOfMessage)-1]
	}

	// Construir nova linha
	funcName := matches[1]
	newLine := bytes.ReplaceAll(
		line,
		matches[0],
		[]byte(fmt.Sprintf("%s(\"%s%s\")", funcName, firstChar, restOfMessage)),
	)

	lines[issue.Line-1] = newLine
	return bytes.Join(lines, []byte("\n")), true
}

// Corrige conversões de tipo redundantes
func fixRedundantConversion(content []byte, issue LintIssue) ([]byte, bool) {
	lines := bytes.Split(content, []byte("\n"))
	if issue.Line <= 0 || issue.Line > len(lines) {
		return content, false
	}

	line := lines[issue.Line-1]

	// Procurar por padrões de conversão redundante
	conversionRegex := regexp.MustCompile(`(\w+)\((\w+)\)`)
	matches := conversionRegex.FindSubmatch(line)

	if len(matches) < 3 {
		return content, false
	}

	// Verificar se o tipo da variável é o mesmo da conversão
	// Isso é uma simplificação, na prática precisaríamos analisar o código
	// para determinar o tipo da variável

	// Por enquanto, apenas registramos que não conseguimos corrigir automaticamente
	return content, false
}

// Gera um relatório dos problemas encontrados e corrigidos
func generateReport(report *LintReport) {
	fmt.Println("\n=== Relatório de Lint ===")
	fmt.Printf("Tempo de execução: %v\n", report.EndTime.Sub(report.StartTime))
	fmt.Printf("Total de problemas encontrados: %d\n", report.TotalIssues)
	fmt.Printf("Problemas corrigidos automaticamente: %d\n", report.FixedIssues)
	fmt.Printf("Problemas que requerem correção manual: %d\n", report.RemainingIssues)

	if report.TotalIssues > 0 {
		fmt.Println("\nDetalhes por arquivo:")

		for file, issues := range report.IssuesByFile {
			fmt.Printf("\n%s:\n", file)

			for _, issue := range issues {
				status := "Não corrigido"
				if issue.Fixed {
					status = "Corrigido automaticamente"
				}

				fmt.Printf("  Linha %d: %s (%s) - %s\n",
					issue.Line,
					issue.Message,
					issue.Code,
					status)
			}
		}
	}

	// Salvar relatório em arquivo
	saveReportToFile(report)
}

// Salva o relatório em um arquivo
func saveReportToFile(report *LintReport) {
	reportDir := "reports"
	if err := os.MkdirAll(reportDir, 0755); err != nil {
		fmt.Printf("Erro ao criar diretório de relatórios: %v\n", err)
		return
	}

	reportFile := filepath.Join(reportDir, fmt.Sprintf("lint_report_%s.txt",
		report.StartTime.Format("2006-01-02_15-04-05")))

	file, err := os.Create(reportFile)
	if err != nil {
		fmt.Printf("Erro ao criar arquivo de relatório: %v\n", err)
		return
	}
	defer file.Close()

	// Redirecionar saída para o arquivo
	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	// Gerar relatório novamente
	fmt.Println("=== Relatório de Lint ===")
	fmt.Printf("Data: %s\n", report.StartTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("Tempo de execução: %v\n", report.EndTime.Sub(report.StartTime))
	fmt.Printf("Total de problemas encontrados: %d\n", report.TotalIssues)
	fmt.Printf("Problemas corrigidos automaticamente: %d\n", report.FixedIssues)
	fmt.Printf("Problemas que requerem correção manual: %d\n", report.RemainingIssues)

	if report.TotalIssues > 0 {
		fmt.Println("\nDetalhes por arquivo:")

		for file, issues := range report.IssuesByFile {
			fmt.Printf("\n%s:\n", file)

			for _, issue := range issues {
				status := "Não corrigido"
				if issue.Fixed {
					status = "Corrigido automaticamente"
				}

				fmt.Printf("  Linha %d: %s (%s) - %s\n",
					issue.Line,
					issue.Message,
					issue.Code,
					status)
			}
		}
	}

	// Restaurar stdout e copiar para o arquivo
	w.Close()
	os.Stdout = oldStdout
	io.Copy(file, r)

	fmt.Printf("Relatório salvo em: %s\n", reportFile)
}
