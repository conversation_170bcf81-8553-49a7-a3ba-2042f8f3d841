#!/bin/bash

# Script para iniciar o servidor com as rotas de gerenciamento de vínculos

# Diretório do projeto
PROJECT_DIR="/root/projeto_linux"

# Verificar se o diretório existe
if [ ! -d "$PROJECT_DIR" ]; then
    echo "Diretório do projeto não encontrado: $PROJECT_DIR"
    exit 1
fi

# Ir para o diretório do projeto
cd "$PROJECT_DIR"

# Iniciar o servidor
go run cmd/main.go internal/routes/setup_link_management.go
