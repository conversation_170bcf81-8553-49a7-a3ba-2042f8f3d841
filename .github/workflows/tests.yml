name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: tradicao_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Check out code
      uses: actions/checkout@v3

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.20'

    - name: Install dependencies
      run: go mod download

    - name: Run unit tests
      run: |
        export GIN_MODE=test
        export DB_TEST=true
        export DB_HOST=localhost
        export DB_PORT=5432
        export DB_USER=postgres
        export DB_PASSWORD=postgres
        export DB_NAME=tradicao_test
        go test -v ./internal/...

    - name: Run API tests
      run: |
        export GIN_MODE=test
        export DB_TEST=true
        export DB_HOST=localhost
        export DB_PORT=5432
        export DB_USER=postgres
        export DB_PASSWORD=postgres
        export DB_NAME=tradicao_test
        go test -v ./tests/handlers/...

    - name: Run integration tests
      run: |
        export GIN_MODE=test
        export DB_TEST=true
        export DB_HOST=localhost
        export DB_PORT=5432
        export DB_USER=postgres
        export DB_PASSWORD=postgres
        export DB_NAME=tradicao_test
        go test -v ./tests/integration/...

  lint:
    name: Lint
    runs-on: ubuntu-latest
    steps:
    - name: Check out code
      uses: actions/checkout@v3

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.20'

    - name: Install staticcheck
      run: go install honnef.co/go/tools/cmd/staticcheck@latest

    - name: Run staticcheck
      run: staticcheck ./...

    - name: Run go vet
      run: go vet ./...

    - name: Run go fmt
      run: |
        if [ "$(gofmt -l . | wc -l)" -gt 0 ]; then
          gofmt -l .
          echo "Code is not formatted with gofmt"
          exit 1
        fi
