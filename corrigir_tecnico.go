package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/lib/pq"
	"golang.org/x/crypto/bcrypt"
)

func main() {
	// Configurações do banco de dados
	host := "postgres-ag-br1-03.conteige.cloud"
	port := "54243"
	user := "fcobdj_tradicao"
	password := "67573962"
	dbname := "fcobdj_tradicao"

	// Construir string de conexão
	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		host, port, user, password, dbname)

	// Conectar ao banco de dados
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}
	defer db.Close()

	// Verificar a conexão
	err = db.Ping()
	if err != nil {
		log.Fatalf("Erro ao verificar conexão com o banco de dados: %v", err)
	}
	fmt.Println("Conexão com o banco de dados estabelecida com sucesso!")

	// Email do usuário técnico
	email := "<EMAIL>"

	// 1. Verificar o usuário
	var userID int
	var userName, userEmail, userType string
	var serviceProviderID sql.NullInt64

	queryUser := `
		SELECT id, name, email, type, service_provider_id
		FROM users
		WHERE email = $1
	`

	err = db.QueryRow(queryUser, email).Scan(&userID, &userName, &userEmail, &userType, &serviceProviderID)
	if err != nil {
		log.Fatalf("Erro ao consultar usuário: %v", err)
	}

	fmt.Println("=== Informações do Usuário ===")
	fmt.Printf("ID: %d\n", userID)
	fmt.Printf("Nome: %s\n", userName)
	fmt.Printf("Email: %s\n", userEmail)
	fmt.Printf("Tipo: %s\n", userType)
	
	if serviceProviderID.Valid {
		fmt.Printf("ID do Prestador: %d\n", serviceProviderID.Int64)
	} else {
		fmt.Println("Usuário não está associado a nenhum prestador de serviço")
	}

	// 2. Verificar se o prestador de serviço existe
	if serviceProviderID.Valid {
		var providerName string
		queryProvider := `
			SELECT name FROM service_providers WHERE id = $1
		`
		err = db.QueryRow(queryProvider, serviceProviderID.Int64).Scan(&providerName)
		if err != nil {
			if err == sql.ErrNoRows {
				fmt.Printf("⚠️ PROBLEMA DETECTADO: Prestador com ID %d não existe\n", serviceProviderID.Int64)
				
				// Corrigir: Criar o prestador de serviço
				fmt.Println("Criando prestador de serviço...")
				
				queryCreateProvider := `
					INSERT INTO service_providers (name, company_name, document, status, created_at, updated_at)
					VALUES ($1, $2, $3, $4, NOW(), NOW())
					RETURNING id
				`
				
				var newProviderID int64
				err = db.QueryRow(queryCreateProvider, 
					"Prestadora Técnico Manutenção", 
					"Prestadora Técnico Manutenção LTDA", 
					"12345678901234", 
					"active").Scan(&newProviderID)
				
				if err != nil {
					log.Fatalf("Erro ao criar prestador: %v", err)
				}
				
				fmt.Printf("✅ Prestador criado com ID: %d\n", newProviderID)
				
				// Atualizar o ID do prestador no usuário
				queryUpdateUser := `
					UPDATE users
					SET service_provider_id = $1
					WHERE id = $2
				`
				
				_, err = db.Exec(queryUpdateUser, newProviderID, userID)
				if err != nil {
					log.Fatalf("Erro ao atualizar usuário: %v", err)
				}
				
				fmt.Printf("✅ Usuário atualizado com novo ID de prestador: %d\n", newProviderID)
				serviceProviderID.Int64 = newProviderID
				serviceProviderID.Valid = true
			} else {
				log.Fatalf("Erro ao consultar prestador: %v", err)
			}
		} else {
			fmt.Printf("Prestador encontrado: %s (ID: %d)\n", providerName, serviceProviderID.Int64)
		}
	} else {
		// Corrigir: Associar o usuário a um prestador de serviço
		fmt.Println("⚠️ PROBLEMA DETECTADO: Usuário técnico não está associado a nenhum prestador de serviço")
		
		// Criar um prestador de serviço
		fmt.Println("Criando prestador de serviço...")
		
		queryCreateProvider := `
			INSERT INTO service_providers (name, company_name, document, status, created_at, updated_at)
			VALUES ($1, $2, $3, $4, NOW(), NOW())
			RETURNING id
		`
		
		var newProviderID int64
		err = db.QueryRow(queryCreateProvider, 
			"Prestadora Técnico Manutenção", 
			"Prestadora Técnico Manutenção LTDA", 
			"12345678901234", 
			"active").Scan(&newProviderID)
		
		if err != nil {
			log.Fatalf("Erro ao criar prestador: %v", err)
		}
		
		fmt.Printf("✅ Prestador criado com ID: %d\n", newProviderID)
		
		// Atualizar o usuário
		queryUpdateUser := `
			UPDATE users
			SET service_provider_id = $1
			WHERE id = $2
		`
		
		_, err = db.Exec(queryUpdateUser, newProviderID, userID)
		if err != nil {
			log.Fatalf("Erro ao atualizar usuário: %v", err)
		}
		
		fmt.Printf("✅ Usuário atualizado com ID de prestador: %d\n", newProviderID)
		serviceProviderID.Int64 = newProviderID
		serviceProviderID.Valid = true
	}

	// 3. Verificar e redefinir a senha
	fmt.Println("\nVerificando senha do usuário...")
	
	// Gerar nova senha
	newPassword := "tradicaosistema"
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		log.Fatalf("Erro ao gerar hash da senha: %v", err)
	}
	
	// Atualizar senha
	queryUpdatePassword := `
		UPDATE users
		SET password = $1
		WHERE id = $2
	`
	
	_, err = db.Exec(queryUpdatePassword, string(hashedPassword), userID)
	if err != nil {
		log.Fatalf("Erro ao atualizar senha: %v", err)
	}
	
	fmt.Printf("✅ Senha redefinida para: %s\n", newPassword)
	
	fmt.Println("\n=== CORREÇÃO CONCLUÍDA ===")
	fmt.Println("O usuário técnico agora deve conseguir fazer login com:")
	fmt.Printf("Email: %s\n", userEmail)
	fmt.Printf("Senha: %s\n", newPassword)
	fmt.Println("Prestador de serviço associado com ID:", serviceProviderID.Int64)
}
