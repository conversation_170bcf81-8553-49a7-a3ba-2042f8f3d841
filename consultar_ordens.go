package main

import (
	"fmt"
	"log"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

type MaintenanceOrder struct {
	ID                uint      `gorm:"primaryKey"`
	BranchID          uint      `gorm:"column:branch_id"`
	EquipmentID       uint      `gorm:"column:equipment_id"`
	TechnicianID      *uint     `gorm:"column:technician_id"`
	ServiceProviderID *uint     `gorm:"column:service_provider_id"`
	Number            string    `gorm:"column:number"`
	Problem           string    `gorm:"column:problem"`
	Status            string    `gorm:"column:status"`
	CreatedAt         time.Time `gorm:"column:created_at"`
}

func (MaintenanceOrder) TableName() string {
	return "maintenance_orders"
}

func main() {
	// Configuração de conexão com o banco de dados
	dsn := "host=db.tradicao.com user=tradicao password=tradicao2024 dbname=tradicao port=5432 sslmode=disable"

	// Configurar logger para mostrar todas as consultas SQL
	newLogger := logger.New(
		log.New(log.Writer(), "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: false,
			Colorful:                  true,
		},
	)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: newLogger,
	})

	if err != nil {
		log.Fatalf("Falha ao conectar ao banco de dados: %v", err)
	}

	// Buscar todas as ordens de manutenção
	var ordens []MaintenanceOrder
	if err := db.Find(&ordens).Error; err != nil {
		log.Fatalf("Erro ao buscar ordens: %v", err)
	}

	fmt.Printf("Total de ordens encontradas: %d\n\n", len(ordens))
	fmt.Println("Lista de todas as ordens:")
	fmt.Println("ID | Branch | Equipment | Technician | Provider | Number | Status")
	fmt.Println("------------------------------------------------------------------")

	for _, ordem := range ordens {
		techID := uint(0)
		if ordem.TechnicianID != nil {
			techID = *ordem.TechnicianID
		}

		providerID := uint(0)
		if ordem.ServiceProviderID != nil {
			providerID = *ordem.ServiceProviderID
		}

		fmt.Printf("%d | %d | %d | %d | %d | %s | %s\n",
			ordem.ID,
			ordem.BranchID,
			ordem.EquipmentID,
			techID,
			providerID,
			ordem.Number,
			ordem.Status)
	}

	// Buscar ordens associadas ao técnico específico
	var tecnicoID uint = 94 // <NAME_EMAIL>
	var ordensDoTecnico []MaintenanceOrder

	if err := db.Where("technician_id = ? OR service_provider_id IN (SELECT service_provider_id FROM users WHERE id = ?)",
		tecnicoID, tecnicoID).Find(&ordensDoTecnico).Error; err != nil {
		log.Fatalf("Erro ao buscar ordens do técnico: %v", err)
	}

	fmt.Printf("\n\nOrdens associadas ao técnico ID %d (<EMAIL>):\n", tecnicoID)
	fmt.Println("ID | Branch | Equipment | Technician | Provider | Number | Status")
	fmt.Println("------------------------------------------------------------------")

	for _, ordem := range ordensDoTecnico {
		techID := uint(0)
		if ordem.TechnicianID != nil {
			techID = *ordem.TechnicianID
		}

		providerID := uint(0)
		if ordem.ServiceProviderID != nil {
			providerID = *ordem.ServiceProviderID
		}

		fmt.Printf("%d | %d | %d | %d | %d | %s | %s\n",
			ordem.ID,
			ordem.BranchID,
			ordem.EquipmentID,
			techID,
			providerID,
			ordem.Number,
			ordem.Status)
	}

	// Selecionar a ordem mais recente do técnico para manter
	var ordemParaManter MaintenanceOrder
	if err := db.Where("technician_id = ?", tecnicoID).Order("id DESC").First(&ordemParaManter).Error; err != nil {
		log.Fatalf("Erro ao buscar ordem para manter: %v", err)
	}

	techID := uint(0)
	if ordemParaManter.TechnicianID != nil {
		techID = *ordemParaManter.TechnicianID
	}

	providerID := uint(0)
	if ordemParaManter.ServiceProviderID != nil {
		providerID = *ordemParaManter.ServiceProviderID
	}

	fmt.Printf("\n\nOrdem selecionada para manter:\n")
	fmt.Println("ID | Branch | Equipment | Technician | Provider | Number | Status")
	fmt.Println("------------------------------------------------------------------")
	fmt.Printf("%d | %d | %d | %d | %d | %s | %s\n",
		ordemParaManter.ID,
		ordemParaManter.BranchID,
		ordemParaManter.EquipmentID,
		techID,
		providerID,
		ordemParaManter.Number,
		ordemParaManter.Status)

	// Excluir todas as ordens exceto a selecionada
	fmt.Printf("\nPara excluir todas as ordens exceto a ordem %d, execute o seguinte comando SQL:\n", ordemParaManter.ID)
	fmt.Printf("DELETE FROM maintenance_orders WHERE id != %d;\n", ordemParaManter.ID)
}
