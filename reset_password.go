package main

import (
	"fmt"
	"log"
	"os"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// User representa um usuário no sistema
type User struct {
	ID                uint   `json:"id" gorm:"primaryKey"`
	Name              string `json:"name"`
	Email             string `json:"email" gorm:"unique"`
	Password          string `json:"-"`
	Type              string `json:"type"`
	ServiceProviderID *uint  `json:"service_provider_id,omitempty"`
}

func main() {
	// Configurações do banco de dados
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		"postgres-ag-br1-03.conteige.cloud",
		"54243",
		"fcobdj_tradicao",
		"67573962",
		"fcobdj_tradicao",
	)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Email do usuário técnico
	email := "<EMAIL>"

	// Buscar usuário pelo email
	var user User
	result := db.Where("email = ?", email).First(&user)
	if result.Error != nil {
		log.Fatalf("Erro ao buscar usuário: %v", result.Error)
	}

	fmt.Printf("Usuário encontrado: ID=%d, Nome=%s, Email=%s, Tipo=%s\n", 
		user.ID, user.Name, user.Email, user.Type)
	
	if user.ServiceProviderID != nil {
		fmt.Printf("ID do Prestador de Serviço: %d\n", *user.ServiceProviderID)
	} else {
		fmt.Println("Usuário não está associado a nenhum prestador de serviço")
	}

	// Gerar nova senha
	newPassword := "tradicaosistema"
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		log.Fatalf("Erro ao gerar hash da senha: %v", err)
	}

	// Atualizar senha do usuário
	result = db.Model(&user).Update("password", string(hashedPassword))
	if result.Error != nil {
		log.Fatalf("Erro ao atualizar senha: %v", result.Error)
	}

	fmt.Printf("Senha do usuário %s redefinida com sucesso para: %s\n", user.Email, newPassword)
}
