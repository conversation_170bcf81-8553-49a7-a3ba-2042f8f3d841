# Implementação do Sistema de Permissões para Acesso às Ordens

## Visão Geral

Este documento descreve a implementação do sistema de permissões para acesso às ordens de manutenção, incluindo a criação da tabela `technician_orders` e os componentes relacionados.

## Componentes Implementados

### 1. Tabela `technician_orders`

A tabela `technician_orders` foi criada para armazenar relacionamentos explícitos entre técnicos e ordens de manutenção. Ela tem a seguinte estrutura:

```sql
CREATE TABLE technician_orders (
    id SERIAL PRIMARY KEY,
    technician_id INTEGER NOT NULL,
    order_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    notes TEXT,
    UNIQUE(technician_id, order_id)
);
```

### 2. Modelo `TechnicianOrder`

O modelo `TechnicianOrder` foi criado para representar um relacionamento entre um técnico e uma ordem de manutenção:

```go
type TechnicianOrder struct {
    ID           uint      `json:"id" gorm:"primaryKey"`
    TechnicianID uint      `json:"technician_id" gorm:"column:technician_id;not null"`
    OrderID      uint      `json:"order_id" gorm:"column:order_id;not null"`
    CreatedAt    time.Time `json:"created_at" gorm:"column:created_at;default:CURRENT_TIMESTAMP"`
    CreatedBy    *uint     `json:"created_by,omitempty" gorm:"column:created_by"`
    Notes        string    `json:"notes,omitempty" gorm:"column:notes"`

    // Relacionamentos
    Technician *User             `json:"technician,omitempty" gorm:"foreignKey:TechnicianID"`
    Order      *MaintenanceOrder `json:"order,omitempty" gorm:"foreignKey:OrderID"`
    Creator    *User             `json:"creator,omitempty" gorm:"foreignKey:CreatedBy"`
}
```

### 3. Repositório `TechnicianOrderRepository`

O repositório `TechnicianOrderRepository` foi criado para gerenciar operações com a tabela `technician_orders`:

```go
type TechnicianOrderRepository interface {
    // Create cria um novo relacionamento entre técnico e ordem
    Create(technicianID, orderID, createdBy uint, notes string) error

    // Delete remove um relacionamento entre técnico e ordem
    Delete(technicianID, orderID uint) error

    // GetByTechnician retorna todas as ordens associadas a um técnico
    GetByTechnician(technicianID uint) ([]TechnicianOrder, error)

    // GetByOrder retorna todos os técnicos associados a uma ordem
    GetByOrder(orderID uint) ([]TechnicianOrder, error)

    // Exists verifica se existe um relacionamento entre técnico e ordem
    Exists(technicianID, orderID uint) (bool, error)

    // GetAll retorna todos os relacionamentos
    GetAll() ([]TechnicianOrder, error)
}
```

### 4. Serviço `TechnicianOrderService`

O serviço `TechnicianOrderService` foi criado para gerenciar a lógica de negócio relacionada aos relacionamentos entre técnicos e ordens:

```go
type TechnicianOrderService struct {
    repo models.TechnicianOrderRepository
}
```

### 5. Handler `TechnicianOrderHandler`

O handler `TechnicianOrderHandler` foi criado para gerenciar as requisições HTTP relacionadas aos relacionamentos entre técnicos e ordens:

```go
type TechnicianOrderHandler struct {
    service *services.TechnicianOrderService
}
```

### 6. Rotas API

As seguintes rotas foram configuradas para gerenciar os relacionamentos entre técnicos e ordens:

- `POST /api/technician-orders/assign`: Atribuir uma ordem a um técnico
- `DELETE /api/technician-orders/technician/:technicianId/order/:orderId`: Remover uma ordem de um técnico
- `GET /api/technician-orders/technician/:technicianId`: Obter todas as ordens associadas a um técnico
- `GET /api/technician-orders/order/:orderId`: Obter todos os técnicos associados a uma ordem
- `POST /api/technician-orders/batch-assign`: Atribuir várias ordens a um técnico

## Scripts de Migração e População de Dados

### 1. Script de Migração

O script `migrations/technician_orders.sql` foi criado para criar a tabela `technician_orders` no banco de dados:

```sql
-- Migração para criar a tabela technician_orders
-- Esta tabela armazena relacionamentos explícitos entre técnicos e ordens de manutenção

-- Verificar se a tabela já existe
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'technician_orders') THEN
        -- Criar a tabela technician_orders
        CREATE TABLE technician_orders (
            id SERIAL PRIMARY KEY,
            technician_id INTEGER NOT NULL,
            order_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER,
            notes TEXT,
            UNIQUE(technician_id, order_id)
        );
        
        -- Adicionar índices para melhorar a performance
        CREATE INDEX idx_technician_orders_technician_id ON technician_orders(technician_id);
        CREATE INDEX idx_technician_orders_order_id ON technician_orders(order_id);
        
        -- Adicionar chaves estrangeiras
        ALTER TABLE technician_orders 
            ADD CONSTRAINT fk_technician_orders_technician 
            FOREIGN KEY (technician_id) 
            REFERENCES users(id) 
            ON DELETE CASCADE;
            
        ALTER TABLE technician_orders 
            ADD CONSTRAINT fk_technician_orders_order 
            FOREIGN KEY (order_id) 
            REFERENCES maintenance_orders(id) 
            ON DELETE CASCADE;
            
        ALTER TABLE technician_orders 
            ADD CONSTRAINT fk_technician_orders_created_by 
            FOREIGN KEY (created_by) 
            REFERENCES users(id) 
            ON DELETE SET NULL;
            
        RAISE NOTICE 'Tabela technician_orders criada com sucesso';
    ELSE
        RAISE NOTICE 'Tabela technician_orders já existe';
    END IF;
END
$$;
```

### 2. Script de População de Dados

O script `scripts/populate_technician_orders.sh` foi criado para popular a tabela `technician_orders` com dados iniciais, especialmente para as ordens problemáticas (16, 17, 18, 19):

```bash
#!/bin/bash

# Script para popular a tabela technician_orders com dados iniciais
# Este script deve ser executado a partir da raiz do projeto

# ... (código omitido para brevidade)

# Criar atribuições para as ordens problemáticas (16, 17, 18, 19)
problem_orders=(16 17 18 19)

for tech_id in "${technician_ids[@]}"; do
    for order_id in "${problem_orders[@]}"; do
        # Verificar se a ordem existe
        order_exists=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT EXISTS (SELECT FROM maintenance_orders WHERE id = $order_id);")
        
        if [[ $order_exists == *"t"* ]]; then
            # Verificar se a atribuição já existe
            assignment_exists=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT EXISTS (SELECT FROM technician_orders WHERE technician_id = $tech_id AND order_id = $order_id);")
            
            if [[ $assignment_exists == *"f"* ]]; then
                # Inserir atribuição
                PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "INSERT INTO technician_orders (technician_id, order_id, notes) VALUES ($tech_id, $order_id, 'Atribuição automática para ordem problemática');"
                log "Atribuída ordem $order_id ao técnico $tech_id"
            else
                log "Ordem $order_id já está atribuída ao técnico $tech_id"
            fi
        else
            warning "Ordem $order_id não existe no sistema"
        fi
    done
done
```

## Como Usar o Sistema

### 1. Executar a Migração

Para criar a tabela `technician_orders` no banco de dados:

```bash
chmod +x scripts/run_migration.sh
./scripts/run_migration.sh
```

### 2. Popular a Tabela com Dados Iniciais

Para popular a tabela `technician_orders` com dados iniciais:

```bash
chmod +x scripts/populate_technician_orders.sh
./scripts/populate_technician_orders.sh
```

### 3. Atribuir uma Ordem a um Técnico via API

Para atribuir uma ordem a um técnico via API:

```bash
curl -X POST http://localhost:8080/api/technician-orders/assign \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer SEU_TOKEN_JWT" \
  -d '{
    "technician_id": 94,
    "order_id": 18,
    "notes": "Atribuição manual via API"
  }'
```

### 4. Verificar Atribuições de um Técnico via API

Para verificar as atribuições de um técnico via API:

```bash
curl -X GET http://localhost:8080/api/technician-orders/technician/94 \
  -H "Authorization: Bearer SEU_TOKEN_JWT"
```

## Conclusão

A implementação do sistema de permissões para acesso às ordens de manutenção resolve os problemas identificados, removendo verificações hardcoded e implementando uma solução mais flexível e robusta baseada em tabela. Isso garante que apenas usuários autorizados tenham acesso às ordens de manutenção, seguindo as regras de negócio do sistema.
