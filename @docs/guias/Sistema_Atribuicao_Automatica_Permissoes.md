# Sistema de Atribuição Automática de Permissões

## Visão Geral

Este documento descreve o sistema de atribuição automática de permissões implementado para garantir que, quando uma ordem de manutenção é criada ou atualizada, todos os profissionais que precisam ter acesso a ela (técnico designado, prestadora e outros técnicos da mesma empresa) recebam automaticamente as permissões necessárias através da tabela `technician_orders`.

## Componentes do Sistema

### 1. Serviço de Atribuição de Permissões

O serviço `PermissionAssignmentService` é responsável por atribuir permissões automaticamente quando uma ordem de manutenção é criada ou atualizada. Ele possui os seguintes métodos principais:

- `AssignPermissionsForNewOrder`: Atribui permissões automaticamente quando uma nova ordem é criada
- `AssignPermissionsForUpdatedOrder`: Atribui permissões automaticamente quando uma ordem é atualizada
- `assignPermissionToTechnician`: Atribui permissão a um técnico para acessar uma ordem
- `getTechniciansFromProvider`: Retorna todos os técnicos vinculados a uma prestadora

### 2. Integração com o Fluxo de Criação de Ordens

O sistema de atribuição automática de permissões está integrado aos seguintes pontos do fluxo de criação e atualização de ordens:

- `OrderServiceAdapter.CreateOrder`: Quando uma nova ordem é criada
- `OrderServiceAdapter.UpdateOrder`: Quando uma ordem existente é atualizada
- `OrderServiceAdapter.AssignProvider`: Quando um prestador é atribuído a uma ordem

### 3. Tabela `technician_orders`

A tabela `technician_orders` armazena os relacionamentos entre técnicos e ordens de manutenção, permitindo que o sistema de permissões verifique se um técnico tem acesso a uma ordem específica.

```sql
CREATE TABLE technician_orders (
    id SERIAL PRIMARY KEY,
    technician_id INTEGER NOT NULL,
    order_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    notes TEXT,
    UNIQUE(technician_id, order_id)
);
```

## Fluxo de Atribuição Automática de Permissões

### 1. Criação de Nova Ordem

Quando uma nova ordem de manutenção é criada, o sistema realiza as seguintes ações:

1. Verifica se a ordem tem um técnico atribuído
2. Verifica se a ordem tem uma prestadora atribuída
3. Atribui permissão ao técnico designado
4. Busca todos os técnicos vinculados à mesma prestadora
5. Atribui permissão a todos os técnicos da prestadora

```go
// Atribuir permissões automaticamente
technicianOrderRepo := repository.NewTechnicianOrderRepository(tx)
technicianOrderService := NewTechnicianOrderService(technicianOrderRepo)
permissionAssignmentService := NewPermissionAssignmentService(
    tx, 
    technicianOrderRepo,
    technicianOrderService,
)

// Atribuir permissões para a nova ordem
if err := permissionAssignmentService.AssignPermissionsForNewOrder(order); err != nil {
    log.Printf("Aviso: Erro ao atribuir permissões automaticamente para ordem %d: %v", 
        order.ID, err)
    // Continuar mesmo com erro na atribuição de permissões
}
```

### 2. Atualização de Ordem Existente

Quando uma ordem de manutenção é atualizada, o sistema verifica se houve mudança no técnico ou prestador atribuído:

1. Verifica se houve mudança no técnico atribuído
2. Verifica se houve mudança na prestadora atribuída
3. Se houve mudança, atualiza as permissões correspondentes

```go
// Verificar se houve mudança no técnico ou prestador de serviço
technicianChanged := (existingOrder.TechnicianID == nil && order.TechnicianID != nil) ||
    (existingOrder.TechnicianID != nil && order.TechnicianID == nil) ||
    (existingOrder.TechnicianID != nil && order.TechnicianID != nil && *existingOrder.TechnicianID != *order.TechnicianID)

providerChanged := (existingOrder.ServiceProviderID == nil && order.ServiceProviderID != nil) ||
    (existingOrder.ServiceProviderID != nil && order.ServiceProviderID == nil) ||
    (existingOrder.ServiceProviderID != nil && order.ServiceProviderID != nil && *existingOrder.ServiceProviderID != *order.ServiceProviderID)

// Se houve mudança no técnico ou prestador, atualizar permissões
if technicianChanged || providerChanged {
    // Atualizar permissões
    // ...
}
```

### 3. Atribuição de Prestador

Quando um prestador é atribuído a uma ordem, o sistema atualiza as permissões para todos os técnicos vinculados a esse prestador:

1. Verifica se houve mudança no prestador atribuído
2. Se houve mudança, atualiza as permissões para todos os técnicos do novo prestador

```go
// Verificar se houve mudança no prestador
providerChanged := (oldProviderID == nil && providerIDPtr != nil) ||
    (oldProviderID != nil && providerIDPtr == nil) ||
    (oldProviderID != nil && providerIDPtr != nil && *oldProviderID != *providerIDPtr)

// Se houve mudança no prestador, atualizar permissões
if providerChanged {
    // Atualizar permissões
    // ...
}
```

## Logs Detalhados

O sistema implementa logs detalhados para rastrear as atribuições automáticas de permissões:

```go
log.Printf("[PERMISSION-ASSIGNMENT] Iniciando atribuição automática de permissões para ordem #%d", order.ID)
log.Printf("[PERMISSION-ASSIGNMENT] Ordem #%d não tem técnico atribuído, pulando atribuição de permissões", order.ID)
log.Printf("[PERMISSION-ASSIGNMENT] Erro ao atribuir permissão ao técnico %d para ordem #%d: %v", technicianID, order.ID, err)
log.Printf("[PERMISSION-ASSIGNMENT] Permissão atribuída com sucesso: Técnico %d -> Ordem #%d", technicianID, orderID)
log.Printf("[PERMISSION-ASSIGNMENT] Encontrados %d técnicos vinculados à prestadora %d", len(technicians), providerID)
```

## Benefícios do Sistema

1. **Transparência**: O processo ocorre de forma transparente durante a criação da ordem, sem necessidade de intervenção manual.
2. **Consistência**: Garante que todos os profissionais que precisam ter acesso à ordem recebam as permissões necessárias.
3. **Rastreabilidade**: Os logs detalhados permitem rastrear todas as atribuições de permissão.
4. **Flexibilidade**: O sistema se adapta automaticamente a mudanças na atribuição de técnicos e prestadores.

## Como Funciona na Prática

1. **Criação de Ordem**: Quando uma filial cria uma nova ordem de manutenção, o sistema automaticamente atribui permissões ao técnico designado e a todos os técnicos da prestadora responsável.

2. **Mudança de Técnico**: Se o técnico atribuído a uma ordem for alterado, o sistema mantém as permissões do técnico anterior e adiciona permissões para o novo técnico.

3. **Mudança de Prestadora**: Se a prestadora atribuída a uma ordem for alterada, o sistema atribui permissões a todos os técnicos da nova prestadora.

## Conclusão

O sistema de atribuição automática de permissões garante que, assim que uma ordem seja criada e atribuída, todos os profissionais que precisam ter acesso a ela recebam automaticamente as permissões necessárias através da tabela `technician_orders`. Isso elimina a necessidade de atribuição manual de permissões e garante que todos os técnicos relevantes tenham acesso às ordens que precisam.
