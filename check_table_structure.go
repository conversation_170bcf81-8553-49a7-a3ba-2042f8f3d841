package main

import (
	"fmt"
	"log"
	"os"

	"tradicao/internal/database"
)

func main() {
	// Inicializar conexão com o banco de dados
	db := database.GetGormDB()
	if db == nil {
		log.Fatal("Falha ao conectar ao banco de dados")
	}

	// Verificar colunas da tabela maintenance_orders
	var colunas []string
	if err := db.Raw("SELECT column_name FROM information_schema.columns WHERE table_name = 'maintenance_orders' ORDER BY ordinal_position").Pluck("column_name", &colunas).Error; err != nil {
		log.Fatalf("Erro ao verificar colunas da tabela: %v", err)
	}

	fmt.Println("Colunas da tabela maintenance_orders:")
	for i, coluna := range colunas {
		fmt.Printf("%d. %s\n", i+1, coluna)
	}

	// Verificar se a coluna 'title' existe
	var temColuna bool
	for _, coluna := range colunas {
		if coluna == "title" {
			temColuna = true
			break
		}
	}

	if temColuna {
		fmt.Println("\nA coluna 'title' existe na tabela.")
	} else {
		fmt.Println("\nA coluna 'title' NÃO existe na tabela.")
	}

	os.Exit(0)
}
