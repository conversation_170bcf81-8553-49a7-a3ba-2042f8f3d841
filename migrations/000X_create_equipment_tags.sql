-- +migrate Up
CREATE TABLE IF NOT EXISTS equipment_tags (
    equipment_id BIGINT NOT NULL,
    tag_id BIGINT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (equipment_id, tag_id),
    FOREIGN KEY (equipment_id) REFERENCES equipment(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
);

CREATE INDEX idx_equipment_tags_equipment_id ON equipment_tags(equipment_id);
CREATE INDEX idx_equipment_tags_tag_id ON equipment_tags(tag_id);

-- +migrate Down
DROP TABLE IF EXISTS equipment_tags; 