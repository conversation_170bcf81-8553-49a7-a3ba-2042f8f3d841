-- Removendo a tabela usuarios e atualizando referências
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    avatar_url VARCHAR(255),
    station_id INTEGER,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de filiais
CREATE TABLE IF NOT EXISTS branches (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    address VARCHAR(255),
    cnpj VARCHAR(20),
    manager_id INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de equipamentos
CREATE TABLE IF NOT EXISTS equipment (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50),
    serial_number VARCHAR(50),
    branch_id INTEGER REFERENCES branches(id),
    status VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de ordens de manutenção
CREATE TABLE IF NOT EXISTS maintenance_orders (
    id SERIAL PRIMARY KEY,
    number VARCHAR(20) UNIQUE,
    equipment_id INTEGER REFERENCES equipment(id),
    branch_id INTEGER REFERENCES branches(id),
    description TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'open',
    priority VARCHAR(20),
    service_provider_id INTEGER REFERENCES users(id),
    opening_date DATE,
    start_date DATE,
    repair_date DATE,
    total_cost DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de eventos do calendário
CREATE TABLE IF NOT EXISTS calendar_events (
    id SERIAL PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP,
    user_id INTEGER REFERENCES users(id),
    maintenance_order_id INTEGER REFERENCES maintenance_orders(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de tokens de autenticação
CREATE TABLE IF NOT EXISTS branch_auth_tokens (
    id SERIAL PRIMARY KEY,
    branch_id INTEGER REFERENCES branches(id),
    token VARCHAR(100) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_used BOOLEAN DEFAULT FALSE
);

-- Índices para otimização
CREATE INDEX IF NOT EXISTS idx_equipment_branch ON equipment(branch_id);
-- Simplesmente comentar a criação do índice problemático
-- CREATE INDEX IF NOT EXISTS idx_maintenance_orders_equip ON maintenance_orders(equipment_id);
CREATE INDEX IF NOT EXISTS idx_calendar_events_user ON calendar_events(user_id);
CREATE INDEX IF NOT EXISTS idx_branch_auth_tokens ON branch_auth_tokens(token);

-- Inserir usuário administrador padrão
INSERT INTO users (name, email, password, role, is_active)
VALUES (
    'Administrador',
    '<EMAIL>',
    '$2a$10$XOPbrlUPQdwdJUpSrIF6X.LbE14qsMmKGq6jCuMQzlCMkTzxqZjhC',
    'admin',
    true
) ON CONFLICT (email) DO NOTHING;

-- Views removidas para evitar conflitos
