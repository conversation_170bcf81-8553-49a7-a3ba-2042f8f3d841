-- Migração para criar a tabela de transferências de equipamentos

-- Criar a tabela equipment_transfers
CREATE TABLE IF NOT EXISTS equipment_transfers (
    id SERIAL PRIMARY KEY,
    equipment_id INTEGER NOT NULL,
    source_branch_id INTEGER NOT NULL,
    destination_branch_id INTEGER NOT NULL,
    requested_by_user_id INTEGER NOT NULL,
    approved_by_user_id INTEGER,
    justification TEXT NOT NULL,
    authorized_by VARCHAR(255) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pendente',
    requested_at TIMESTAMP NOT NULL,
    approved_at TIMESTAMP,
    completed_at TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    deleted_at TIMESTAMP,
    
    CONSTRAINT fk_equipment_transfers_equipment FOREIGN KEY (equipment_id)
        REFERENCES equipment (id) ON DELETE CASCADE,
    CONSTRAINT fk_equipment_transfers_source_branch FOREIGN KEY (source_branch_id)
        REFERENCES branches (id) ON DELETE CASCADE,
    CONSTRAINT fk_equipment_transfers_destination_branch FOREIGN KEY (destination_branch_id)
        REFERENCES branches (id) ON DELETE CASCADE,
    CONSTRAINT fk_equipment_transfers_requested_by FOREIGN KEY (requested_by_user_id)
        REFERENCES users (id) ON DELETE CASCADE,
    CONSTRAINT fk_equipment_transfers_approved_by FOREIGN KEY (approved_by_user_id)
        REFERENCES users (id) ON DELETE SET NULL
);

-- Criar índices para melhorar a performance
CREATE INDEX idx_equipment_transfers_equipment_id ON equipment_transfers(equipment_id);
CREATE INDEX idx_equipment_transfers_source_branch_id ON equipment_transfers(source_branch_id);
CREATE INDEX idx_equipment_transfers_destination_branch_id ON equipment_transfers(destination_branch_id);
CREATE INDEX idx_equipment_transfers_status ON equipment_transfers(status);
CREATE INDEX idx_equipment_transfers_requested_at ON equipment_transfers(requested_at);
CREATE INDEX idx_equipment_transfers_deleted_at ON equipment_transfers(deleted_at);
