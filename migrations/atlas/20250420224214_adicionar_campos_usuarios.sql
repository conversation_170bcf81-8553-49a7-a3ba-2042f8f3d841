-- migrate:up

ALTER TABLE usuarios
ADD COLUMN IF NOT EXISTS ativo BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS atualizado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS ultimo_acesso TIMESTAMP;

-- migrate:down

ALTER TABLE usuarios
DROP COLUMN IF EXISTS ativo,
DROP COLUMN IF EXISTS criado_em,
DROP COLUMN IF EXISTS atualizado_em,
DROP COLUMN IF EXISTS ultimo_acesso;
