-- Migração para criar tabelas relacionadas à funcionalidade de ordem técnico
-- migrate:up

-- Tabela para dados de manutenção
CREATE TABLE IF NOT EXISTS ordem_manutencao_dados (
    id SERIAL PRIMARY KEY,
    ordem_id INTEGER NOT NULL,
    descricao TEXT NOT NULL,
    pecas_utilizadas TEXT,
    observacoes TEXT,
    data_registro TIMESTAMP NOT NULL,
    tecnico_id INTEGER NOT NULL,
    tecnico_nome VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Índice para ordem_id em ordem_manutencao_dados
CREATE INDEX IF NOT EXISTS idx_ordem_manutencao_dados_ordem_id ON ordem_manutencao_dados(ordem_id);

-- Tabela para dados de custos
CREATE TABLE IF NOT EXISTS ordem_custos_dados (
    id SERIAL PRIMARY KEY,
    ordem_id INTEGER NOT NULL,
    pecas DECIMAL(10, 2) NOT NULL,
    mao_obra DECIMAL(10, 2) NOT NULL,
    deslocamento DECIMAL(10, 2) NOT NULL,
    total DECIMAL(10, 2) NOT NULL,
    data_registro TIMESTAMP NOT NULL,
    tecnico_id INTEGER NOT NULL,
    tecnico_nome VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Índice para ordem_id em ordem_custos_dados
CREATE INDEX IF NOT EXISTS idx_ordem_custos_dados_ordem_id ON ordem_custos_dados(ordem_id);

-- Tabela para dados de cronograma
CREATE TABLE IF NOT EXISTS ordem_cronograma_dados (
    id SERIAL PRIMARY KEY,
    ordem_id INTEGER NOT NULL,
    data_inicio VARCHAR(10),
    hora_inicio VARCHAR(5),
    data_fim VARCHAR(10),
    hora_fim VARCHAR(5),
    status VARCHAR(20) NOT NULL,
    data_registro TIMESTAMP NOT NULL,
    tecnico_id INTEGER NOT NULL,
    tecnico_nome VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Índice para ordem_id em ordem_cronograma_dados
CREATE INDEX IF NOT EXISTS idx_ordem_cronograma_dados_ordem_id ON ordem_cronograma_dados(ordem_id);

-- Tabela para mensagens de chat
CREATE TABLE IF NOT EXISTS ordem_mensagens_chat (
    id SERIAL PRIMARY KEY,
    ordem_id INTEGER NOT NULL,
    mensagem TEXT NOT NULL,
    remetente VARCHAR(100) NOT NULL,
    remetente_id INTEGER NOT NULL,
    data_envio TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Índice para ordem_id em ordem_mensagens_chat
CREATE INDEX IF NOT EXISTS idx_ordem_mensagens_chat_ordem_id ON ordem_mensagens_chat(ordem_id);

-- Tabela para histórico de alterações (se ainda não existir)
CREATE TABLE IF NOT EXISTS historico_ordem (
    id SERIAL PRIMARY KEY,
    ordem_id INTEGER NOT NULL,
    usuario_id INTEGER NOT NULL,
    usuario_nome VARCHAR(100) NOT NULL,
    data_evento TIMESTAMP NOT NULL,
    tipo_evento VARCHAR(50) NOT NULL,
    valor_anterior TEXT,
    valor_novo TEXT,
    observacao TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Índice para ordem_id em historico_ordem
CREATE INDEX IF NOT EXISTS idx_historico_ordem_ordem_id ON historico_ordem(ordem_id);

-- migrate:down

-- Remover tabelas
DROP TABLE IF EXISTS ordem_mensagens_chat;
DROP TABLE IF EXISTS ordem_cronograma_dados;
DROP TABLE IF EXISTS ordem_custos_dados;
DROP TABLE IF EXISTS ordem_manutencao_dados;
DROP TABLE IF EXISTS historico_ordem;
