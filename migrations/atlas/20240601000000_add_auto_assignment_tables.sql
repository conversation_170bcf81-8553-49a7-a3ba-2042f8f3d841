-- migrate:up

-- Função auxiliar para verificar se uma coluna existe
CREATE OR REPLACE FUNCTION column_exists(ptable text, pcolumn text) RETURNS boolean AS $$
DECLARE
    exists boolean;
BEGIN
    SELECT count(*) > 0 INTO exists
    FROM information_schema.columns
    WHERE table_name = ptable AND column_name = pcolumn;
    RETURN exists;
END;
$$ LANGUAGE plpgsql;

-- 1. C<PERSON>r tabela de tipos de equipamento padronizados
CREATE TABLE IF NOT EXISTS equipment_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. <PERSON><PERSON>r tabela de relacionamento entre prestadores e tipos de equipamento
CREATE TABLE IF NOT EXISTS provider_equipment_types (
    id SERIAL PRIMARY KEY,
    service_provider_id INTEGER NOT NULL,
    equipment_type_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (service_provider_id) REFERENCES service_providers(id) ON DELETE CASCADE,
    FOREIGN KEY (equipment_type_id) REFERENCES equipment_types(id) ON DELETE CASCADE,
    UNIQUE (service_provider_id, equipment_type_id)
);

-- 3. Criar tabela de relacionamento entre prestadores e filiais
CREATE TABLE IF NOT EXISTS provider_branches (
    id SERIAL PRIMARY KEY,
    service_provider_id INTEGER NOT NULL,
    branch_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (service_provider_id) REFERENCES service_providers(id) ON DELETE CASCADE,
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE,
    UNIQUE (service_provider_id, branch_id)
);

-- 4. Criar índices para melhorar a performance
CREATE INDEX IF NOT EXISTS idx_provider_equipment_types_provider_id ON provider_equipment_types(service_provider_id);
CREATE INDEX IF NOT EXISTS idx_provider_equipment_types_equipment_type_id ON provider_equipment_types(equipment_type_id);
CREATE INDEX IF NOT EXISTS idx_provider_branches_provider_id ON provider_branches(service_provider_id);
CREATE INDEX IF NOT EXISTS idx_provider_branches_branch_id ON provider_branches(branch_id);

-- 5. Inserir os tipos de equipamento padronizados
INSERT INTO equipment_types (name, description, created_at, updated_at)
VALUES
    ('Fritadeira', 'Equipamento para fritura de alimentos', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Micro-ondas', 'Forno de micro-ondas para aquecimento rápido', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Forno', 'Forno convencional para cozimento', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Speed Oven', 'Forno de alta velocidade (Turbo Chef, Prática, Cibo)', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Ar condicionado', 'Sistema de climatização', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Refrigerador', 'Equipamento para refrigeração e conservação de alimentos', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Computador', 'Equipamento de informática', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Máquina de cartão', 'Terminal para pagamento com cartão', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Impressora', 'Equipamento para impressão de documentos', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Secador de mão', 'Equipamento para secagem de mãos em banheiros', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Televisor', 'Equipamento para exibição de conteúdo audiovisual', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Expositor de lanche', 'Vitrine para exposição de produtos', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Porta automática', 'Sistema de porta com abertura automática', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Sistema de segurança', 'Equipamentos de segurança e vigilância', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (name) DO NOTHING;

-- migrate:down

-- Remover índices
DROP INDEX IF EXISTS idx_provider_equipment_types_provider_id;
DROP INDEX IF EXISTS idx_provider_equipment_types_equipment_type_id;
DROP INDEX IF EXISTS idx_provider_branches_provider_id;
DROP INDEX IF EXISTS idx_provider_branches_branch_id;

-- Remover tabelas
DROP TABLE IF EXISTS provider_branches;
DROP TABLE IF EXISTS provider_equipment_types;
DROP TABLE IF EXISTS equipment_types;

-- Remover função auxiliar
DROP FUNCTION IF EXISTS column_exists(text, text);
