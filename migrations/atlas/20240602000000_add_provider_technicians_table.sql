-- Create provider_technicians table for managing relationships between service providers and technicians
CREATE TABLE IF NOT EXISTS provider_technicians (
    id SERIAL PRIMARY KEY,
    provider_id INTEGER NOT NULL,
    technician_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_provider_technicians_provider FOREIGN KEY (provider_id) REFERENCES service_providers(id) ON DELETE CASCADE,
    CONSTRAINT fk_provider_technicians_technician FOREIG<PERSON> KEY (technician_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT unique_provider_technician UNIQUE (provider_id, technician_id)
);

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_provider_technicians_provider_id ON provider_technicians(provider_id);
CREATE INDEX IF NOT EXISTS idx_provider_technicians_technician_id ON provider_technicians(technician_id);

-- Add comment to table
COMMENT ON TABLE provider_technicians IS 'Stores relationships between service providers and technicians';
