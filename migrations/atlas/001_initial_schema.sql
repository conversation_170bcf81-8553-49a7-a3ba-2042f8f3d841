-- Migração inicial para o esquema do projeto Tradição
-- Gerado automaticamente por generate_atlas_schema.go

-- Criar tabelas
-- Tabela: branches
CREATE TABLE branches (
    id SERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    code VA<PERSON><PERSON>R(20) NOT NULL UNIQUE,
    address VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(2),
    zip_code VARCHAR(20),
    phone VARCHAR(20),
    email VARCHAR(100),
    type VARCHAR(20) DEFAULT 'urban',
    is_active BOOLEAN DEFAULT TRUE,
    manager_id INTEGER REFERENCES users(id),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    opening_hours VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Tabela: users
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    name VA<PERSON>HAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    branch_id INTEGER REFERENCES branches(id),
    failed_attempts INTEGER DEFAULT 0,
    blocked BOOLEAN DEFAULT FALSE,
    totp_secret VARCHAR(255) DEFAULT '',
    totp_enabled BOOLEAN DEFAULT FALSE,
    last_password_change TIMESTAMP,
    force_password_change BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Criar índices
CREATE UNIQUE INDEX idx_users_email ON users (email);
CREATE INDEX idx_users_role ON users (role);
CREATE INDEX idx_users_branch_id ON users (branch_id);
CREATE INDEX idx_branches_manager_id ON branches (manager_id);
CREATE INDEX idx_branches_is_active ON branches (is_active);
CREATE INDEX idx_branches_city_state ON branches (city, state);

-- Adicionar constraints
ALTER TABLE users ADD CONSTRAINT check_user_role CHECK (role IN ('admin', 'manager', 'financial', 'branch_user', 'technician', 'provider'));
