// Atlas Schema para o projeto Tradição
// Gerado automaticamente por generate_atlas_schema.go

schema "public" {
  comment = "Esquema principal do banco de dados Tradição"
}

// Definição das tabelas principais

table "branches" {
  schema = schema.public
  comment = "Tabela de filiais/postos"
  
  column "id" {
    type = int
    auto_increment = true
    primary_key = true
  }

  column "name" {
    type = varchar(100)
    null = false
  }

  column "code" {
    type = varchar(20)
    null = false
    unique = true
  }

  column "address" {
    type = varchar(255)
  }

  column "city" {
    type = varchar(100)
  }

  column "state" {
    type = varchar(2)
  }

  column "zip_code" {
    type = varchar(20)
  }

  column "phone" {
    type = varchar(20)
  }

  column "email" {
    type = varchar(100)
  }

  column "type" {
    type = varchar(20)
    default = "urban"
  }

  column "is_active" {
    type = bool
    default = true
  }

  column "manager_id" {
    type = int
    foreign_key { references = table.users.column.id }
  }

  column "latitude" {
    type = decimal(10, 8)
  }

  column "longitude" {
    type = decimal(11, 8)
  }

  column "opening_hours" {
    type = varchar(255)
  }

  column "created_at" {
    type = timestamp
    default = now()
  }

  column "updated_at" {
    type = timestamp
    default = now()
  }

  column "deleted_at" {
    type = timestamp
  }
}

table "users" {
  schema = schema.public
  comment = "Tabela de usuários do sistema"
  
  column "id" {
    type = int
    auto_increment = true
    primary_key = true
  }

  column "name" {
    type = varchar(100)
    null = false
  }

  column "email" {
    type = varchar(100)
    null = false
    unique = true
  }

  column "password" {
    type = varchar(255)
    null = false
  }

  column "role" {
    type = varchar(50)
    null = false
  }

  column "branch_id" {
    type = int
    foreign_key { references = table.branches.column.id }
  }

  column "failed_attempts" {
    type = int
    default = 0
  }

  column "blocked" {
    type = bool
    default = false
  }

  column "totp_secret" {
    type = varchar(255)
    default = ""
  }

  column "totp_enabled" {
    type = bool
    default = false
  }

  column "last_password_change" {
    type = timestamp
  }

  column "force_password_change" {
    type = bool
    default = false
  }

  column "created_at" {
    type = timestamp
    default = now()
  }

  column "updated_at" {
    type = timestamp
    default = now()
  }

  column "deleted_at" {
    type = timestamp
  }
}

// Índices

index "idx_users_email" {
  table = table.users
  columns = [column.email]
  unique = true
}

index "idx_users_role" {
  table = table.users
  columns = [column.role]
}

index "idx_users_branch_id" {
  table = table.users
  columns = [column.branch_id]
}

index "idx_branches_manager_id" {
  table = table.branches
  columns = [column.manager_id]
}

index "idx_branches_is_active" {
  table = table.branches
  columns = [column.is_active]
}

index "idx_branches_city_state" {
  table = table.branches
  columns = [column.city, column.state]
}
