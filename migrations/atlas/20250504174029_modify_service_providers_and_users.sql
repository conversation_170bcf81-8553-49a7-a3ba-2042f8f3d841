-- migrate:up

-- 1. Atualizar a tabela service_providers com novos campos
ALTER TABLE service_providers
    ADD COLUMN IF NOT EXISTS company_name VA<PERSON>HAR(255),
    ADD COLUMN IF NOT EXISTS cnpj VARCHAR(20) UNIQUE,
    ADD COLUMN IF NOT EXISTS address VARCHAR(255),
    ADD COLUMN IF NOT EXISTS city VARCHAR(100),
    ADD COLUMN IF NOT EXISTS state VARCHAR(2),
    ADD COLUMN IF NOT EXISTS zip_code VARCHAR(20),
    ADD COLUMN IF NOT EXISTS contact_name VARCHAR(100),
    ADD COLUMN IF NOT EXISTS contact_email VARCHAR(100),
    ADD COLUMN IF NOT EXISTS contact_phone VARCHAR(20),
    ADD COLUMN IF NOT EXISTS specialties TEXT,
    ADD COLUMN IF NOT EXISTS area_of_expertise VARCHAR(100),
    ADD COLUMN IF NOT EXISTS average_rating DECIMAL(3,2),
    ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active',
    ADD COLUMN IF NOT EXISTS logo_url VARCHAR(255),
    ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- 2. Adicionar campo service_provider_id à tabela users
ALTER TABLE users
    ADD COLUMN IF NOT EXISTS service_provider_id INTEGER,
    ADD CONSTRAINT fk_user_service_provider FOREIGN KEY (service_provider_id) REFERENCES service_providers(id) ON DELETE SET NULL;

-- 3. Criar tabela service_provider_managers
CREATE TABLE IF NOT EXISTS service_provider_managers (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    service_provider_id INTEGER NOT NULL,
    role VARCHAR(20) NOT NULL, -- 'owner', 'manager', 'admin'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_spm_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_spm_provider FOREIGN KEY (service_provider_id) REFERENCES service_providers(id) ON DELETE CASCADE,
    CONSTRAINT uk_user_provider UNIQUE (user_id, service_provider_id)
);

-- 4. Criar índices para otimização
CREATE INDEX IF NOT EXISTS idx_users_service_provider ON users(service_provider_id);
CREATE INDEX IF NOT EXISTS idx_spm_user ON service_provider_managers(user_id);
CREATE INDEX IF NOT EXISTS idx_spm_provider ON service_provider_managers(service_provider_id);

-- migrate:down

-- 1. Remover índices
DROP INDEX IF EXISTS idx_users_service_provider;
DROP INDEX IF EXISTS idx_spm_user;
DROP INDEX IF EXISTS idx_spm_provider;

-- 2. Remover tabela service_provider_managers
DROP TABLE IF EXISTS service_provider_managers;

-- 3. Remover campo service_provider_id da tabela users
ALTER TABLE users
    DROP CONSTRAINT IF EXISTS fk_user_service_provider,
    DROP COLUMN IF EXISTS service_provider_id;

-- 4. Remover campos adicionados à tabela service_providers
ALTER TABLE service_providers
    DROP COLUMN IF EXISTS company_name,
    DROP COLUMN IF EXISTS cnpj,
    DROP COLUMN IF EXISTS address,
    DROP COLUMN IF EXISTS city,
    DROP COLUMN IF EXISTS state,
    DROP COLUMN IF EXISTS zip_code,
    DROP COLUMN IF EXISTS contact_name,
    DROP COLUMN IF EXISTS contact_email,
    DROP COLUMN IF EXISTS contact_phone,
    DROP COLUMN IF EXISTS specialties,
    DROP COLUMN IF EXISTS area_of_expertise,
    DROP COLUMN IF EXISTS average_rating,
    DROP COLUMN IF EXISTS status,
    DROP COLUMN IF EXISTS logo_url,
    DROP COLUMN IF EXISTS created_at,
    DROP COLUMN IF EXISTS updated_at;
