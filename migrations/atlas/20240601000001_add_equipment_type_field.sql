-- migrate:up

-- Adicionar campo de tipo de equipamento à tabela de equipamentos, se ainda não existir
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'equipment' AND column_name = 'equipment_type_id'
    ) THEN
        ALTER TABLE equipment ADD COLUMN equipment_type_id INTEGER;
        ALTER TABLE equipment ADD CONSTRAINT fk_equipment_type FOREIGN KEY (equipment_type_id) REFERENCES equipment_types(id);
        CREATE INDEX idx_equipment_type_id ON equipment(equipment_type_id);
    END IF;
END $$;

-- migrate:down

-- Remover campo de tipo de equipamento da tabela de equipamentos
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'equipment' AND column_name = 'equipment_type_id'
    ) THEN
        ALTER TABLE equipment DROP CONSTRAINT IF EXISTS fk_equipment_type;
        DROP INDEX IF EXISTS idx_equipment_type_id;
        ALTER TABLE equipment DROP COLUMN IF EXISTS equipment_type_id;
    END IF;
END $$;
