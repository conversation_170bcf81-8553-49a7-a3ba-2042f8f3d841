-- Migração para adicionar colunas faltantes nas tabelas
-- migrate:up

-- Função para verificar se uma coluna existe
CREATE OR REPLACE FUNCTION column_exists(t_name text, c_name text) RETURNS boolean AS $$
DECLARE
    exists_bool boolean;
BEGIN
    SELECT COUNT(*) > 0 INTO exists_bool
    FROM information_schema.columns
    WHERE table_name = t_name AND column_name = c_name;
    RETURN exists_bool;
END;
$$ LANGUAGE plpgsql;

-- Adicionar colunas faltantes na tabela maintenance_orders
DO $$
BEGIN
    -- Coluna technician_id
    IF NOT column_exists('maintenance_orders', 'technician_id') THEN
        ALTER TABLE maintenance_orders ADD COLUMN technician_id INTEGER;
    END IF;

    -- Coluna service_provider_id
    IF NOT column_exists('maintenance_orders', 'service_provider_id') THEN
        ALTER TABLE maintenance_orders ADD COLUMN service_provider_id INTEGER;
    END IF;

    -- <PERSON>una created_by_user_id
    IF NOT column_exists('maintenance_orders', 'created_by_user_id') THEN
        ALTER TABLE maintenance_orders ADD COLUMN created_by_user_id INTEGER;
    END IF;

    -- <PERSON>una assigned_to_user_id
    IF NOT column_exists('maintenance_orders', 'assigned_to_user_id') THEN
        ALTER TABLE maintenance_orders ADD COLUMN assigned_to_user_id INTEGER;
    END IF;

    -- Coluna title (se não existir)
    IF NOT column_exists('maintenance_orders', 'title') THEN
        ALTER TABLE maintenance_orders ADD COLUMN title VARCHAR(255);
    END IF;

    -- Coluna number (se não existir)
    IF NOT column_exists('maintenance_orders', 'number') THEN
        ALTER TABLE maintenance_orders ADD COLUMN number VARCHAR(50);
    END IF;

    -- Coluna problem (se não existir)
    IF NOT column_exists('maintenance_orders', 'problem') THEN
        ALTER TABLE maintenance_orders ADD COLUMN problem TEXT;
    END IF;

    -- Coluna estimated_cost (se não existir)
    IF NOT column_exists('maintenance_orders', 'estimated_cost') THEN
        ALTER TABLE maintenance_orders ADD COLUMN estimated_cost DECIMAL(10,2) DEFAULT 0;
    END IF;

    -- Coluna actual_cost (se não existir)
    IF NOT column_exists('maintenance_orders', 'actual_cost') THEN
        ALTER TABLE maintenance_orders ADD COLUMN actual_cost DECIMAL(10,2) DEFAULT 0;
    END IF;

    -- Coluna due_date (se não existir)
    IF NOT column_exists('maintenance_orders', 'due_date') THEN
        ALTER TABLE maintenance_orders ADD COLUMN due_date TIMESTAMP;
    END IF;

    -- Coluna open_date (se não existir)
    IF NOT column_exists('maintenance_orders', 'open_date') THEN
        ALTER TABLE maintenance_orders ADD COLUMN open_date TIMESTAMP;
    END IF;

    -- Coluna completion_date (se não existir)
    IF NOT column_exists('maintenance_orders', 'completion_date') THEN
        ALTER TABLE maintenance_orders ADD COLUMN completion_date TIMESTAMP;
    END IF;

    -- Coluna approval_status (se não existir)
    IF NOT column_exists('maintenance_orders', 'approval_status') THEN
        ALTER TABLE maintenance_orders ADD COLUMN approval_status VARCHAR(50);
    END IF;

    -- Coluna approval_date (se não existir)
    IF NOT column_exists('maintenance_orders', 'approval_date') THEN
        ALTER TABLE maintenance_orders ADD COLUMN approval_date TIMESTAMP;
    END IF;

    -- Coluna payment_status (se não existir)
    IF NOT column_exists('maintenance_orders', 'payment_status') THEN
        ALTER TABLE maintenance_orders ADD COLUMN payment_status VARCHAR(50);
    END IF;

    -- Coluna payment_date (se não existir)
    IF NOT column_exists('maintenance_orders', 'payment_date') THEN
        ALTER TABLE maintenance_orders ADD COLUMN payment_date TIMESTAMP;
    END IF;

    -- Coluna rating (se não existir)
    IF NOT column_exists('maintenance_orders', 'rating') THEN
        ALTER TABLE maintenance_orders ADD COLUMN rating INTEGER;
    END IF;

    -- Coluna partial_functioning (se não existir)
    IF NOT column_exists('maintenance_orders', 'partial_functioning') THEN
        ALTER TABLE maintenance_orders ADD COLUMN partial_functioning BOOLEAN DEFAULT FALSE;
    END IF;

    -- Coluna extra_equipment (se não existir)
    IF NOT column_exists('maintenance_orders', 'extra_equipment') THEN
        ALTER TABLE maintenance_orders ADD COLUMN extra_equipment BOOLEAN DEFAULT FALSE;
    END IF;

    -- Coluna same_day (se não existir)
    IF NOT column_exists('maintenance_orders', 'same_day') THEN
        ALTER TABLE maintenance_orders ADD COLUMN same_day BOOLEAN DEFAULT FALSE;
    END IF;

    -- Coluna part_replacement (se não existir)
    IF NOT column_exists('maintenance_orders', 'part_replacement') THEN
        ALTER TABLE maintenance_orders ADD COLUMN part_replacement BOOLEAN DEFAULT FALSE;
    END IF;
END $$;

-- Verificar se a tabela ordem_manutencao_dados existe, se não, criar
CREATE TABLE IF NOT EXISTS ordem_manutencao_dados (
    id SERIAL PRIMARY KEY,
    ordem_id INTEGER NOT NULL,
    descricao TEXT NOT NULL,
    pecas_utilizadas TEXT,
    observacoes TEXT,
    data_registro TIMESTAMP NOT NULL,
    tecnico_id INTEGER NOT NULL,
    tecnico_nome VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Adicionar índices para melhorar a performance
DO $$
BEGIN
    -- Índice para technician_id em maintenance_orders
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'maintenance_orders' AND indexname = 'idx_maintenance_orders_technician_id'
    ) THEN
        CREATE INDEX idx_maintenance_orders_technician_id ON maintenance_orders(technician_id);
    END IF;

    -- Índice para service_provider_id em maintenance_orders
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'maintenance_orders' AND indexname = 'idx_maintenance_orders_service_provider_id'
    ) THEN
        CREATE INDEX idx_maintenance_orders_service_provider_id ON maintenance_orders(service_provider_id);
    END IF;

    -- Índice para ordem_id em ordem_manutencao_dados
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'ordem_manutencao_dados' AND indexname = 'idx_ordem_manutencao_dados_ordem_id'
    ) THEN
        CREATE INDEX idx_ordem_manutencao_dados_ordem_id ON ordem_manutencao_dados(ordem_id);
    END IF;

    -- Índice para tecnico_id em ordem_manutencao_dados
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'ordem_manutencao_dados' AND indexname = 'idx_ordem_manutencao_dados_tecnico_id'
    ) THEN
        CREATE INDEX idx_ordem_manutencao_dados_tecnico_id ON ordem_manutencao_dados(tecnico_id);
    END IF;
END $$;

-- Remover a função temporária
DROP FUNCTION IF EXISTS column_exists(text, text);

-- migrate:down
-- Não implementamos o rollback para evitar perda de dados
