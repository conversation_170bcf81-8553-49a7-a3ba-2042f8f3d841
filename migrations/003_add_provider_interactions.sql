ALTER TABLE maintenance_orders ADD COLUMN provider_id INT;
ALTER TABLE maintenance_orders ADD COLUMN provider_notes TEXT;
ALTER TABLE maintenance_orders ADD COLUMN start_date DATE;
ALTER TABLE maintenance_orders ADD COLUMN repair_date DATE;
ALTER TABLE maintenance_orders ADD COLUMN requires_parts BOOLEAN;
ALTER TABLE maintenance_orders ADD COLUMN parts_available BOOLEAN;
ALTER TABLE maintenance_orders ADD COLUMN execution_authorized BOOLEAN;
ALTER TABLE maintenance_orders ADD COLUMN authorized_by VARCHAR(100);
ALTER TABLE maintenance_orders ADD COLUMN labor_cost DECIMAL(10, 2);
ALTER TABLE maintenance_orders ADD COLUMN total_cost DECIMAL(10, 2);

CREATE TABLE IF NOT EXISTS order_parts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    part_name VARCHAR(100) NOT NULL,
    part_cost DECIMAL(10, 2) NOT NULL,
    FOREIGN KEY (order_id) REFERENCES maintenance_orders(id)
);

CREATE TABLE IF NOT EXISTS order_media (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    media_url VARCHAR(255) NOT NULL,
    FOREIGN KEY (order_id) REFERENCES maintenance_orders(id)
);