-- Script para atualizar todas as filiais com dados completos
UPDATE branches SET 
    city = 'São Paulo',
    state = 'SP',
    phone = CONCAT('(11) 9', LPAD(CAST(id AS TEXT), 4, '0'), '-', LPAD(CAST((id % 9999) AS TEXT), 4, '0')),
    email = CONCAT('filial', LPAD(CAST((id - 100) AS TEXT), 2, '0'), '@tradicao.com'),
    address = CONCAT('Av. Paulista, ', CAST((id * 10) AS TEXT))
WHERE id >= 101;

-- Atualizar a Matriz separadamente
UPDATE branches SET 
    city = 'São Paulo',
    state = 'SP',
    phone = '(11) 3333-4444',
    email = '<EMAIL>',
    address = 'Av. Paulista, 1000'
WHERE id = 100;
