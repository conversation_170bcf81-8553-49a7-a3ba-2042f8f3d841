-- <PERSON>ript para atualizar as filiais com os dados do arquivo data/filiais_lista.txt

-- Matriz
UPDATE branches SET 
    address = 'Rua Bento Gonçalves 2410',
    city = 'Marau',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 100;

-- Filial 01
UPDATE branches SET 
    address = 'Rua 14 de Julho 500',
    city = 'Sananduva',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 101;

-- Filial 02
UPDATE branches SET 
    address = 'BR 163, km104 s/n',
    city = 'São José do Cedro',
    state = 'SC',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 102;

-- Filial 03
UPDATE branches SET 
    address = 'EST BR 386, KM245 23591',
    city = 'Soledade',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 103;

-- Filial 04
UPDATE branches SET 
    address = 'Av. <PERSON> 1841',
    city = 'Sananduva',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 104;

-- Filial 05
UPDATE branches SET 
    address = 'Av. Flores da Cunha 2962',
    city = 'Carazinho',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 105;

-- Filial 06
UPDATE branches SET 
    address = 'Av. Brasil Oeste 2210',
    city = 'Passo Fundo',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 106;

-- Filial 07
UPDATE branches SET 
    address = 'Avenida Expedicionário 111',
    city = 'Sarandi',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 107;

-- Filial 09
UPDATE branches SET 
    address = 'Rua Sergipe 1441',
    city = 'Erechim',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 109;

-- Filial 10
UPDATE branches SET 
    address = 'Av. La Salle 139',
    city = 'Xanxerê',
    state = 'SC',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 110;

-- Filial 11
UPDATE branches SET 
    address = 'Rod RST 128, KM24 5480',
    city = 'Teutônia',
    state = 'RS',
    phone = '(54)3531-1227',
    email = '<EMAIL>'
WHERE id = 111;

-- Filial 12
UPDATE branches SET 
    address = 'Avenida Doutor Waldomiro Graeff 1350',
    city = 'Não-Me-Toque',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 112;

-- Filial 13
UPDATE branches SET 
    address = 'Rua Espírito Santo 45',
    city = 'Erechim',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 113;

-- Filial 14
UPDATE branches SET 
    address = 'Rodovia RS 135, Km 47 s/n',
    city = 'Getúlio Vargas',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 114;

-- Filial 15
UPDATE branches SET 
    address = 'Rodovia RS324, KM 109 s/n',
    city = 'Ronda Alta',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 115;

-- Filial 16
UPDATE branches SET 
    address = 'Av Osvaldo Júlio Werlang 169',
    city = 'Espumoso',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 116;

-- Filial 17
UPDATE branches SET 
    address = 'Av. Getúlio Dorneles Vargas, N 3019',
    city = 'Chapecó',
    state = 'SC',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 117;

-- Filial 18
UPDATE branches SET 
    address = 'Rua Fagundes dos Reis 125',
    city = 'Passo Fundo',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 118;

-- Filial 19
UPDATE branches SET 
    address = 'Rua Alôncio De Camargo 30',
    city = 'Passo Fundo',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 119;

-- Filial 20
UPDATE branches SET 
    address = 'Rodovia RS 135 46',
    city = 'Getúlio Vargas',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 120;

-- Filial 21
UPDATE branches SET 
    address = 'Av. Jose Oscar Salazar 2569',
    city = 'Erechim',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 121;

-- Filial 22
UPDATE branches SET 
    address = 'Av. Willibaldo Koenig 926',
    city = 'Mormaço',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 122;

-- Filial 23
UPDATE branches SET 
    address = 'Rua Alberto Schabbach 450',
    city = 'Santa Clara do Sul',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 123;

-- Filial 24
UPDATE branches SET 
    address = 'AV. FERNANDO MACHADO 375',
    city = 'Chapecó',
    state = 'SC',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 124;

-- Filial 25
UPDATE branches SET 
    address = 'Rua Paulo Polita 109',
    city = 'Passo Fundo',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 125;

-- Filial 26
UPDATE branches SET 
    address = 'RUA WALDEMAR RANGRAB 1401',
    city = 'São Miguel do Oeste',
    state = 'SC',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 126;

-- Filial 27
UPDATE branches SET 
    address = 'Av Pedro Pinto de Souza 558',
    city = 'Erechim',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 127;

-- Filial 28
UPDATE branches SET 
    address = 'AV. JULIO DE CASTILHOS 1502',
    city = 'Três Passos',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 128;

-- Filial 29
UPDATE branches SET 
    address = 'Rua Antônio Prado 104',
    city = 'Passo Fundo',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 129;

-- Filial 30
UPDATE branches SET 
    address = 'Estr. Perimetral Sul (Deputado Guaraci Marinho) s/n',
    city = 'Passo Fundo',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 130;

-- Filial 31
UPDATE branches SET 
    address = 'EST BR 472, KM122 139',
    city = 'Boa Vista do Buricá',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 131;

-- Filial 32
UPDATE branches SET 
    address = 'Rodovia Contorno Viário Armindo Echer, 950',
    city = 'São Lourenço do Oeste',
    state = 'SC',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 132;

-- Filial 33
UPDATE branches SET 
    address = 'Av. Rocha Loires, 1170',
    city = 'Nonoai',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 133;

-- Filial 34
UPDATE branches SET 
    address = 'Rua Amazonas 2650',
    city = 'Lajeado',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 134;

-- Filial 35
UPDATE branches SET 
    address = 'RODOVIA RST 472, KM: 16 1475',
    city = 'Palmitinho',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 135;

-- Filial 36
UPDATE branches SET 
    address = 'Benjamin Constant 3378',
    city = 'Lajeado',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 136;

-- Filial 37
UPDATE branches SET 
    address = 'BR-283 s/n',
    city = 'Capinzal',
    state = 'SC',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 137;

-- Filial 38
UPDATE branches SET 
    address = 'Rodovia SC 480 3100',
    city = 'Chapecó',
    state = 'SC',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 138;

-- Filial 39
UPDATE branches SET 
    address = 'Rua Cap. Frederico Teixeira Guimarães, 610',
    city = 'Palmas',
    state = 'PR',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 139;

-- Filial 40
UPDATE branches SET 
    address = 'Av. Brasil 1501',
    city = 'Saudades',
    state = 'SC',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 140;

-- Filial 41
UPDATE branches SET 
    address = 'Avenida Brasil 1740',
    city = 'Xanxerê',
    state = 'SC',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 141;

-- Filial 42
UPDATE branches SET 
    address = 'Av. Rio Branco 845',
    city = 'Estrela',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 142;

-- Filial 43
UPDATE branches SET 
    address = 'Avenida Venâncio Aires 1869',
    city = 'Santo Ângelo',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 143;

-- Filial 44
UPDATE branches SET 
    address = 'Rua Tiradentes 710',
    city = 'São Lourenço do Oeste',
    state = 'SC',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 144;

-- Filial 45
UPDATE branches SET 
    address = 'RS 153, km 01 s/n',
    city = 'Passo Fundo',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 145;

-- Filial 46
UPDATE branches SET 
    address = 'Rodovia SC 355, R. Caçador 97',
    city = 'Treze Tílias',
    state = 'SC',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 146;

-- Filial 47
UPDATE branches SET 
    address = 'Rua Santos Dumont 431',
    city = 'Erechim',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 147;

-- Filial 48
UPDATE branches SET 
    address = 'Pinheiros 40, RS-453',
    city = 'Estrela',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 148;

-- Filial 49
UPDATE branches SET 
    address = 'Rua Barão do Rio Branco 336',
    city = 'Joaçaba',
    state = 'SC',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 149;

-- Filial 50
UPDATE branches SET 
    address = 'AV. Fernando Ferrari, 703',
    city = 'Espumoso',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 150;

-- Filial 51
UPDATE branches SET 
    address = 'BR 158 855',
    city = 'Pato Branco',
    state = 'PR',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 151;

-- Filial 52
UPDATE branches SET 
    address = 'Av. Coronel Dico, 118',
    city = 'Ijuí',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 152;

-- Filial 53
UPDATE branches SET 
    address = 'Av. Renê Frey, 50',
    city = 'Fraiburgo',
    state = 'SC',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 153;

-- Filial 54
UPDATE branches SET 
    address = 'RST 453 KM 59, s/n',
    city = 'Westfália',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 154;

-- Filial 55
UPDATE branches SET 
    address = 'Rua Frei Caneca 919',
    city = 'Passo Fundo',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 155;

-- Filial 56
UPDATE branches SET 
    address = 'Rua Antoninho Lima 10',
    city = 'Passo Fundo',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 156;

-- Filial 57
UPDATE branches SET 
    address = 'Rod. RST509 5027',
    city = 'Santa Maria',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 157;

-- Filial 58
UPDATE branches SET 
    address = 'Rua Dahne de Abreu 2261',
    city = 'Horizontina',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 158;

-- Filial 59
UPDATE branches SET 
    address = 'Rua Padre Cacique, 442',
    city = 'Canela',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 159;

-- Filial 60
UPDATE branches SET 
    address = 'Estr. Buarque de Macedo 345',
    city = 'Nova Prata',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 160;

-- Filial 61
UPDATE branches SET 
    address = 'Av. Maravilha 1211',
    city = 'Maravilha',
    state = 'SC',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 161;

-- Filial 62
UPDATE branches SET 
    address = 'Santo Granzotto 182',
    city = 'Aratiba',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 162;

-- Filial 63
UPDATE branches SET 
    address = 'Rod. BR 153, KM 15',
    city = 'Severiano de Almeida',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 163;

-- Filial 64
UPDATE branches SET 
    address = 'Rua Pinheiro Machado 2300',
    city = 'Santa Maria',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 164;

-- Filial 65
UPDATE branches SET 
    address = 'Av. Nossa Sra. Medianeira 648',
    city = 'Santa Maria',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 165;

-- Filial 66
UPDATE branches SET 
    address = 'R. Ramiro Barcelos 1235',
    city = 'Santa Cruz do Sul',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 166;

-- Filial 67
UPDATE branches SET 
    address = 'R. Carlos Mauricio Werlang 155',
    city = 'Santa Cruz do Sul',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 167;

-- Filial 68
UPDATE branches SET 
    address = 'Rua 13 de Maio 19',
    city = 'Ijuí',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 168;

-- Filial 69
UPDATE branches SET 
    address = 'Av. Flores da Cunha 2725',
    city = 'Carazinho',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 169;

-- Filial 70
UPDATE branches SET 
    address = 'Av. Fernando Machado 1294',
    city = 'Chapecó',
    state = 'SC',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 170;

-- Filial 71
UPDATE branches SET 
    address = 'Lateral da Rodovia BR 386, Linha Porongos',
    city = 'Estrela',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 171;

-- Filial 72
UPDATE branches SET 
    address = 'Rua Portão 3101',
    city = 'Estância Velha',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 172;

-- Filial 73
UPDATE branches SET 
    address = 'Rod. RS 324, KM 72 + 600 metros',
    city = 'Trindade do Sul',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 173;

-- Filial 74
UPDATE branches SET 
    address = 'Rod. SC 155, 3838',
    city = 'Abelardo Luz',
    state = 'SC',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 174;

-- Filial 75
UPDATE branches SET 
    address = 'Av. Getúlio Vargas 1166',
    city = 'Abelardo Luz',
    state = 'SC',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 175;

-- Filial 76
UPDATE branches SET 
    address = 'Rua Padre Cacique 442',
    city = 'Canela',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 176;

-- Filial 77
UPDATE branches SET 
    address = 'Estr. Buarque de Macedo 345',
    city = 'Nova Prata',
    state = 'RS',
    phone = '(54)3196-8351',
    email = '<EMAIL>'
WHERE id = 177;
