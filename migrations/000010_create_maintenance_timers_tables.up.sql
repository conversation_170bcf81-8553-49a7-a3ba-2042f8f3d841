-- Adicionar coluna status à tabela branches se não existir
ALTER TABLE branches ADD COLUMN IF NOT EXISTS status VARCHAR(50) DEFAULT 'active';

-- Tabela para armazenar os timers de manutenção
CREATE TABLE IF NOT EXISTS maintenance_timers (
    id SERIAL PRIMARY KEY,
    branch_id INTEGER NOT NULL,
    equipment_type VARCHAR(100) NOT NULL,
    maintenance_order_id INTEGER NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    total_seconds INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    FOREIG<PERSON> KEY (branch_id) REFERENCES branches(id),
    FOREIGN KEY (maintenance_order_id) REFERENCES maintenance_orders(id)
);

-- Índices para otimizar consultas
CREATE INDEX IF NOT EXISTS idx_maintenance_timers_branch_id ON maintenance_timers (branch_id);
CREATE INDEX IF NOT EXISTS idx_maintenance_timers_maintenance_order_id ON maintenance_timers (maintenance_order_id);
CREATE INDEX IF NOT EXISTS idx_maintenance_timers_is_active ON maintenance_timers (is_active);

-- Tabela para armazenar estatísticas de manutenção por filial
CREATE TABLE IF NOT EXISTS branch_maintenance_stats (
    id SERIAL PRIMARY KEY,
    branch_id INTEGER NOT NULL,
    equipment_type VARCHAR(100) NOT NULL,
    total_maintenance_time INTEGER DEFAULT 0,
    total_maintenance_count INTEGER DEFAULT 0,
    avg_maintenance_time INTEGER DEFAULT 0,
    last_updated TIMESTAMP NOT NULL,
    FOREIGN KEY (branch_id) REFERENCES branches(id),
    UNIQUE (branch_id, equipment_type)
);

-- Índice para otimizar consultas de estatísticas
CREATE INDEX IF NOT EXISTS idx_branch_maintenance_stats_branch_id ON branch_maintenance_stats (branch_id);