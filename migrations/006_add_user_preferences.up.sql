-- Adicionando coluna phone à tabela users
ALTER TABLE users ADD COLUMN IF NOT EXISTS phone VARCHAR(50);

-- Adicionando colunas de preferências à tabela users
ALTER TABLE users ADD COLUMN IF NOT EXISTS prefer_email_notifications B<PERSON><PERSON><PERSON>N DEFAULT TRUE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS prefer_push_notifications BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS theme VARCHAR(20) DEFAULT 'dark';

-- Atualizando usuários existentes com valores padrão
UPDATE users SET 
    phone = '',
    prefer_email_notifications = TRUE,
    prefer_push_notifications = TRUE,
    theme = 'dark'
WHERE phone IS NULL;