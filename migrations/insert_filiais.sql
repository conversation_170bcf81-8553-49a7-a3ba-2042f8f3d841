-- Script para inserir as filiais no banco de dados tradicao_ent
-- Seguindo a regra: ID 100 = <PERSON>riz, ID 101 = Filial 01, etc.

-- Matriz
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (100, '<PERSON>riz', '<PERSON>TRI<PERSON>', '<PERSON><PERSON> Gonçalves 2410', 'Marau', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 01
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (101, 'Filial 01', 'FIL01', 'Rua 14 de Julho 500', 'Sananduva', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 02
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (102, 'Filial 02', 'FIL02', 'BR 163, km104 s/n', 'São José do Cedro', 'SC', '(54)3196-8351', '<EMAIL>', true);

-- Filial 03
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (103, 'Filial 03', 'FIL03', 'EST BR 386, KM245 23591', 'Soledade', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 04
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (104, 'Filial 04', 'FIL04', 'Av. Julio de Castilhos 1841', 'Sananduva', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 05
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (105, 'Filial 05', 'FIL05', 'Av. Flores da Cunha 2962', 'Carazinho', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 06
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (106, 'Filial 06', 'FIL06', 'Av. Brasil Oeste 2210', 'Passo Fundo', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 07
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (107, 'Filial 07', 'FIL07', 'Avenida Expedicionário 111', 'Sarandi', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 09
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (109, 'Filial 09', 'FIL09', 'Rua Sergipe 1441', 'Erechim', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 10
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (110, 'Filial 10', 'FIL10', 'Av. La Salle 139', 'Xanxerê', 'SC', '(54)3196-8351', '<EMAIL>', true);

-- Filial 11
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (111, 'Filial 11', 'FIL11', 'Rod RST 128, KM24 5480', 'Teutônia', 'RS', '(54)3531-1227', '<EMAIL>', true);

-- Filial 12
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (112, 'Filial 12', 'FIL12', 'Avenida Doutor Waldomiro Graeff 1350', 'Não-Me-Toque', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 13
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (113, 'Filial 13', 'FIL13', 'Rua Espírito Santo 45', 'Erechim', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 14
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (114, 'Filial 14', 'FIL14', 'Rodovia RS 135, Km 47 s/n', 'Getúlio Vargas', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 15
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (115, 'Filial 15', 'FIL15', 'Rodovia RS324, KM 109 s/n', 'Ronda Alta', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 16
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (116, 'Filial 16', 'FIL16', 'Av Osvaldo Júlio Werlang 169', 'Espumoso', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 17
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (117, 'Filial 17', 'FIL17', 'Av. Getúlio Dorneles Vargas, N 3019', 'Chapecó', 'SC', '(54)3196-8351', '<EMAIL>', true);

-- Filial 18
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (118, 'Filial 18', 'FIL18', 'Rua Fagundes dos Reis 125', 'Passo Fundo', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 19
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (119, 'Filial 19', 'FIL19', 'Rua Alôncio De Camargo 30', 'Passo Fundo', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 20
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (120, 'Filial 20', 'FIL20', 'Rodovia RS 135 46', 'Getúlio Vargas', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 21
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (121, 'Filial 21', 'FIL21', 'Av. Jose Oscar Salazar 2569', 'Erechim', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 22
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (122, 'Filial 22', 'FIL22', 'Av. Willibaldo Koenig 926', 'Mormaço', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 23
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (123, 'Filial 23', 'FIL23', 'Rua Alberto Schabbach 450', 'Santa Clara do Sul', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 24
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (124, 'Filial 24', 'FIL24', 'AV. FERNANDO MACHADO 375', 'Chapecó', 'SC', '(54)3196-8351', '<EMAIL>', true);

-- Filial 25
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (125, 'Filial 25', 'FIL25', 'Rua Paulo Polita 109', 'Passo Fundo', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 26
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (126, 'Filial 26', 'FIL26', 'RUA WALDEMAR RANGRAB 1401', 'São Miguel do Oeste', 'SC', '(54)3196-8351', '<EMAIL>', true);

-- Filial 27
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (127, 'Filial 27', 'FIL27', 'Av Pedro Pinto de Souza 558', 'Erechim', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 28
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (128, 'Filial 28', 'FIL28', 'AV. JULIO DE CASTILHOS 1502', 'Três Passos', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 29
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (129, 'Filial 29', 'FIL29', 'Rua Antônio Prado 104', 'Passo Fundo', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 30
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (130, 'Filial 30', 'FIL30', 'Estr. Perimetral Sul (Deputado Guaraci Marinho) s/n', 'Passo Fundo', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 31
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (131, 'Filial 31', 'FIL31', 'EST BR 472, KM122 139', 'Boa Vista do Buricá', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 32
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (132, 'Filial 32', 'FIL32', 'Rodovia Contorno Viário Armindo Echer, 950', 'São Lourenço do Oeste', 'SC', '(54)3196-8351', '<EMAIL>', true);

-- Filial 33
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (133, 'Filial 33', 'FIL33', 'Av. Rocha Loires, 1170', 'Nonoai', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 34
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (134, 'Filial 34', 'FIL34', 'Rua Amazonas 2650', 'Lajeado', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 35
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (135, 'Filial 35', 'FIL35', 'RODOVIA RST 472, KM: 16 1475', 'Palmitinho', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 36
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (136, 'Filial 36', 'FIL36', 'Benjamin Constant 3378', 'Lajeado', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 37
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (137, 'Filial 37', 'FIL37', 'BR-283 s/n', 'Capinzal', 'SC', '(54)3196-8351', '<EMAIL>', true);

-- Filial 38
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (138, 'Filial 38', 'FIL38', 'Rodovia SC 480 3100', 'Chapecó', 'SC', '(54)3196-8351', '<EMAIL>', true);

-- Filial 39
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (139, 'Filial 39', 'FIL39', 'Rua Cap. Frederico Teixeira Guimarães, 610', 'Palmas', 'PR', '(54)3196-8351', '<EMAIL>', true);

-- Filial 40
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (140, 'Filial 40', 'FIL40', 'Av. Brasil 1501', 'Saudades', 'SC', '(54)3196-8351', '<EMAIL>', true);

-- Filial 41
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (141, 'Filial 41', 'FIL41', 'Avenida Brasil 1740', 'Xanxerê', 'SC', '(54)3196-8351', '<EMAIL>', true);

-- Filial 42
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (142, 'Filial 42', 'FIL42', 'Av. Rio Branco 845', 'Estrela', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 43
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (143, 'Filial 43', 'FIL43', 'Avenida Venâncio Aires 1869', 'Santo Ângelo', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 44
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (144, 'Filial 44', 'FIL44', 'Rua Tiradentes 710', 'São Lourenço do Oeste', 'SC', '(54)3196-8351', '<EMAIL>', true);

-- Filial 45
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (145, 'Filial 45', 'FIL45', 'RS 153, km 01 s/n', 'Passo Fundo', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 46
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (146, 'Filial 46', 'FIL46', 'Rodovia SC 355, R. Caçador 97', 'Treze Tílias', 'SC', '(54)3196-8351', '<EMAIL>', true);

-- Filial 47
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (147, 'Filial 47', 'FIL47', 'Rua Santos Dumont 431', 'Erechim', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 48
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (148, 'Filial 48', 'FIL48', 'Pinheiros 40, RS-453', 'Estrela', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 49
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (149, 'Filial 49', 'FIL49', 'Rua Barão do Rio Branco 336', 'Joaçaba', 'SC', '(54)3196-8351', '<EMAIL>', true);

-- Filial 50
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (150, 'Filial 50', 'FIL50', 'AV. Fernando Ferrari, 703', 'Espumoso', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 51
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (151, 'Filial 51', 'FIL51', 'BR 158 855', 'Pato Branco', 'PR', '(54)3196-8351', '<EMAIL>', true);

-- Filial 52
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (152, 'Filial 52', 'FIL52', 'Av. Coronel Dico, 118', 'Ijuí', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 53
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (153, 'Filial 53', 'FIL53', 'Av. Renê Frey, 50', 'Fraiburgo', 'SC', '(54)3196-8351', '<EMAIL>', true);

-- Filial 54
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (154, 'Filial 54', 'FIL54', 'RST 453 KM 59, s/n', 'Westfália', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 55
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (155, 'Filial 55', 'FIL55', 'Rua Frei Caneca 919', 'Passo Fundo', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 56
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (156, 'Filial 56', 'FIL56', 'Rua Antoninho Lima 10', 'Passo Fundo', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 57
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (157, 'Filial 57', 'FIL57', 'Rod. RST509 5027', 'Santa Maria', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 58
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (158, 'Filial 58', 'FIL58', 'Rua Dahne de Abreu 2261', 'Horizontina', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 59
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (159, 'Filial 59', 'FIL59', 'Rua Padre Cacique, 442', 'Canela', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 60
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (160, 'Filial 60', 'FIL60', 'Estr. Buarque de Macedo 345', 'Nova Prata', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 61
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (161, 'Filial 61', 'FIL61', 'Av. Maravilha 1211', 'Maravilha', 'SC', '(54)3196-8351', '<EMAIL>', true);

-- Filial 62
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (162, 'Filial 62', 'FIL62', 'Santo Granzotto 182', 'Aratiba', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 63
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (163, 'Filial 63', 'FIL63', 'Rod. BR 153, KM 15', 'Severiano de Almeida', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 64
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (164, 'Filial 64', 'FIL64', 'Rua Pinheiro Machado 2300', 'Santa Maria', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 65
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (165, 'Filial 65', 'FIL65', 'Av. Nossa Sra. Medianeira 648', 'Santa Maria', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 66
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (166, 'Filial 66', 'FIL66', 'R. Ramiro Barcelos 1235', 'Santa Cruz do Sul', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 67
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (167, 'Filial 67', 'FIL67', 'R. Carlos Mauricio Werlang 155', 'Santa Cruz do Sul', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 68
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (168, 'Filial 68', 'FIL68', 'Rua 13 de Maio 19', 'Ijuí', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 69
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (169, 'Filial 69', 'FIL69', 'Av. Flores da Cunha 2725', 'Carazinho', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 70
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (170, 'Filial 70', 'FIL70', 'Av. Fernando Machado 1294', 'Chapecó', 'SC', '(54)3196-8351', '<EMAIL>', true);

-- Filial 71
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (171, 'Filial 71', 'FIL71', 'Lateral da Rodovia BR 386, Linha Porongos', 'Estrela', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 72
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (172, 'Filial 72', 'FIL72', 'Rua Portão 3101', 'Estância Velha', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 73
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (173, 'Filial 73', 'FIL73', 'Rod. RS 324, KM 72 + 600 metros', 'Trindade do Sul', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 74
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (174, 'Filial 74', 'FIL74', 'Rod. SC 155, 3838', 'Abelardo Luz', 'SC', '(54)3196-8351', '<EMAIL>', true);

-- Filial 75
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (175, 'Filial 75', 'FIL75', 'Av. Getúlio Vargas 1166', 'Abelardo Luz', 'SC', '(54)3196-8351', '<EMAIL>', true);

-- Filial 76
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (176, 'Filial 76', 'FIL76', 'Rua Padre Cacique 442', 'Canela', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Filial 77
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (177, 'Filial 77', 'FIL77', 'Estr. Buarque de Macedo 345', 'Nova Prata', 'RS', '(54)3196-8351', '<EMAIL>', true);

-- Configurar a sequência para continuar a partir do último ID
SELECT setval('branches_id_seq', (SELECT MAX(id) FROM branches), true);
