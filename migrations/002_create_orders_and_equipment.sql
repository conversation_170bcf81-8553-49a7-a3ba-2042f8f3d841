CREATE TABLE IF NOT EXISTS equipment (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    branch_id INT NOT NULL,
    FOREIGN KEY (branch_id) REFERENCES branches(id)
);

CREATE TABLE IF NOT EXISTS maintenance_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    equipment_id INT NOT NULL,
    branch_id INT NOT NULL,
    defect_description TEXT NOT NULL,
    start_date DATE NOT NULL,
    has_backup BOOLEAN NOT NULL,
    is_inoperable BOOLEAN NOT NULL,
    status ENUM('open', 'in_progress', 'closed') DEFAULT 'open',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (equipment_id) REFERENCES equipment(id),
    FOREIG<PERSON> KEY (branch_id) REFERENCES branches(id)
);

CREATE TABLE IF NOT EXISTS order_media (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    media_url VARCHAR(255) NOT NULL,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (order_id) REFERENCES maintenance_orders(id)
);