-- Migração para adicionar suporte a 2FA (TOTP)

-- Adicionar colunas para autenticação de dois fatores na tabela users
ALTER TABLE users ADD COLUMN IF NOT EXISTS totp_secret VARCHAR(255) DEFAULT '';
ALTER TABLE users ADD COLUMN IF NOT EXISTS totp_enabled BOOLEAN DEFAULT FALSE;

-- Adici<PERSON>r índice para melhorar o desempenho das consultas
CREATE INDEX IF NOT EXISTS idx_users_totp_enabled ON users(totp_enabled);

-- Atualizar a política de segurança para permitir 2FA (se existir)
UPDATE security_policies SET enable_2fa = TRUE WHERE id > 0;