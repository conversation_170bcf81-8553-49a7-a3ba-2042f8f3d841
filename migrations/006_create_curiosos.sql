-- Criar tabela curiosos

CREATE TABLE IF NOT EXISTS curiosos (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    fato_curioso TEXT NOT NULL,
    nivel_curiosidade INTEGER CHECK (nivel_curiosidade BETWEEN 1 AND 10),
    data_descoberta TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    verificado BOOLEAN DEFAULT FALSE,
    categoria VARCHAR(50),
    fonte VARCHAR(255),
    imagem_url VARCHAR(255),
    likes INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Adicionar índice
CREATE INDEX IF NOT EXISTS idx_curiosos_categoria ON curiosos(categoria);

-- Adicionar alguns dados iniciais de exemplo
INSERT INTO curiosos (nome, fato_curioso, nivel_curiosidade, categoria, fonte, verificado)
VALUES 
    ('Água nos postos', 'Bombas de combustível precisam ser calibradas regularmente para garantir a medição correta', 8, 'Combustíveis', 'Manual Técnico Shell', TRUE),
    ('Óleo lubrificante', 'O primeiro óleo lubrificante comercial foi criado em 1866', 7, 'Lubrificantes', 'História da Indústria Petrolífera', TRUE),
    ('Gasolina colorida', 'A gasolina é naturalmente incolor, sendo adicionados corantes para identificação', 6, 'Combustíveis', 'ANP Brasil', TRUE),
    ('Biodiesel', 'O Brasil é o terceiro maior produtor de biodiesel do mundo', 9, 'Biocombustíveis', 'EMBRAPA', TRUE);