-- Schema inicial para PostgreSQL

-- Tabela de usuários
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL, -- 'admin', 'manager', 'technician', 'station_staff'
    avatar_url VARCHAR(255),
    station_id INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON> de postos
CREATE TABLE IF NOT EXISTS stations (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(30) NOT NULL UNIQUE,
    type VARCHAR(30) NOT NULL, -- 'posto', 'terminal', 'base'
    address VARCHAR(200) NOT NULL,
    city VARCHAR(100) NOT NULL,
    state VARCHAR(2) NOT NULL,
    postal_code VA<PERSON>HAR(10),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    manager_id INTEGER,
    phone_number VARCHAR(20),
    email VARCHAR(100),
    opening_hours VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de ordens de manutenção
CREATE TABLE IF NOT EXISTS maintenance_orders (
    id SERIAL PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    type VARCHAR(30) NOT NULL, -- 'preventiva', 'corretiva', 'inspecao', etc.
    station_id INTEGER NOT NULL,
    status VARCHAR(30) NOT NULL, -- 'pendente', 'em_andamento', 'concluida', 'cancelada'
    priority VARCHAR(30) NOT NULL, -- 'baixa', 'media', 'alta', 'critica'
    created_by INTEGER NOT NULL,
    assigned_to INTEGER,
    equipment VARCHAR(100) NOT NULL,
    start_date TIMESTAMP,
    completed_date TIMESTAMP,
    estimated_time INTEGER, -- tempo estimado em minutos
    actual_time INTEGER, -- tempo real em minutos
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (station_id) REFERENCES stations(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (assigned_to) REFERENCES users(id)
);

-- Tabela de notas de manutenção
CREATE TABLE IF NOT EXISTS maintenance_notes (
    id SERIAL PRIMARY KEY,
    order_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES maintenance_orders(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Tabela de materiais utilizados
CREATE TABLE IF NOT EXISTS maintenance_materials (
    id SERIAL PRIMARY KEY,
    order_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    quantity DECIMAL(10, 2) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    cost DECIMAL(10, 2),
    added_by INTEGER NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES maintenance_orders(id) ON DELETE CASCADE,
    FOREIGN KEY (added_by) REFERENCES users(id)
);

-- Tabela de fotos de manutenção
CREATE TABLE IF NOT EXISTS maintenance_photos (
    id SERIAL PRIMARY KEY,
    order_id INTEGER NOT NULL,
    photo_url VARCHAR(255) NOT NULL,
    uploaded_by INTEGER NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES maintenance_orders(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id)
);

-- Índices
CREATE INDEX IF NOT EXISTS idx_maintenance_orders_station_id ON maintenance_orders(station_id);
CREATE INDEX IF NOT EXISTS idx_maintenance_orders_assigned_to ON maintenance_orders(assigned_to);
CREATE INDEX IF NOT EXISTS idx_maintenance_orders_status ON maintenance_orders(status);
CREATE INDEX IF NOT EXISTS idx_maintenance_orders_type ON maintenance_orders(type);
CREATE INDEX IF NOT EXISTS idx_maintenance_orders_created_at ON maintenance_orders(created_at);
CREATE INDEX IF NOT EXISTS idx_maintenance_notes_order_id ON maintenance_notes(order_id);
CREATE INDEX IF NOT EXISTS idx_maintenance_materials_order_id ON maintenance_materials(order_id);
CREATE INDEX IF NOT EXISTS idx_maintenance_photos_order_id ON maintenance_photos(order_id);

-- Inserir usuário admin inicial
INSERT INTO users (name, email, password, role, is_active, created_at, updated_at)
VALUES (
    'Administrador',
    '<EMAIL>',
    -- Senha: admin123 (bcrypt)
    '$2a$10$Zc.OU7hVgKNI1AK/h.3mGelrLDOE9vdV18yTAKhP8rnmF5JQbQfiK',
    'admin',
    TRUE,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (email) DO NOTHING;