    -- migrations/  

    -- Adiciona a coluna branch_id à tabela users
    ALTER TABLE users
    ADD COLUMN branch_id INTEGER;

    -- Adiciona a restrição de chave estrangeira.
    -- VERIFIQUE: Certifique-se de que a tabela 'branches' existe e a chave primária é 'id'.
    -- Se for diferente, ajuste 'branches(id)' abaixo.
    ALTER TABLE users
    ADD CONSTRAINT fk_users_branch
    FOREIGN KEY (branch_id) REFERENCES branches(id)
    ON DELETE SET NULL; -- Define como NULL se a filial for deletada. Mude para RESTRICT se não puder deletar filial com usuários.