
-- Tabela de filiais
DO $$ BEGIN
    CREATE TABLE IF NOT EXISTS branches (
        id SERIAL PRIMARY KEY,
        name VA<PERSON>HAR(100) NOT NULL,
        address VARCHAR(255) NOT NULL,
        city VARCHAR(100) NOT NULL,
        state VARCHAR(2) NOT NULL,
        zip_code VARCHAR(10) NOT NULL,
        phone VARCHAR(20),
        email VARCHAR(100),
        manager_id INTEGER REFERENCES users(id),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        deleted_at TIMESTAMP
    );
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Table branches already exists';
END $$;

-- Tabela de vínculo entre filiais e prestadores
DO $$ BEGIN
    CREATE TABLE IF NOT EXISTS branch_provider_links (
        id SERIAL PRIMARY KEY,
        branch_id INTEGER NOT NULL REFERENCES branches(id),
        provider_id INTEGER NOT NULL REFERENCES providers(id),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(branch_id, provider_id)
    );
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Table branch_provider_links already exists';
END $$;

-- Tabela de tokens de autenticação temporária
DO $$ BEGIN
    CREATE TABLE IF NOT EXISTS branch_auth_tokens (
        id SERIAL PRIMARY KEY,
        branch_id INTEGER NOT NULL REFERENCES branches(id),
        token VARCHAR(100) NOT NULL UNIQUE,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_used BOOLEAN DEFAULT FALSE
    );
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Table branch_auth_tokens already exists';
END $$;

-- Adicionar índices para melhorar performance
DO $$ BEGIN
    CREATE INDEX idx_branches_manager_id ON branches(manager_id);
    CREATE INDEX idx_branches_is_active ON branches(is_active);
    CREATE INDEX idx_branch_provider_links_branch_id ON branch_provider_links(branch_id);
    CREATE INDEX idx_branch_provider_links_provider_id ON branch_provider_links(provider_id);
    CREATE INDEX idx_branch_auth_tokens_branch_id ON branch_auth_tokens(branch_id);
    CREATE INDEX idx_branch_auth_tokens_token ON branch_auth_tokens(token);
    CREATE INDEX idx_branch_auth_tokens_expires_at ON branch_auth_tokens(expires_at);
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Indexes already exist';
END $$;

-- Adicionar campo branch_id à tabela de equipamentos
DO $$ BEGIN
    ALTER TABLE equipment ADD COLUMN IF NOT EXISTS branch_id INTEGER REFERENCES branches(id);
    CREATE INDEX idx_equipment_branch_id ON equipment(branch_id);
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Column branch_id already exists in table equipment';
END $$;

-- Adicionar campo branch_id à tabela de ordens de manutenção
DO $$ BEGIN
    ALTER TABLE maintenance_orders ADD COLUMN IF NOT EXISTS branch_id INTEGER REFERENCES branches(id);
    CREATE INDEX idx_maintenance_orders_branch_id ON maintenance_orders(branch_id);
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Column branch_id already exists in table maintenance_orders';
END $$;
