-- Migração para criar a tabela technician_orders
-- Esta tabela armazena relacionamentos explícitos entre técnicos e ordens de manutenção

-- Verificar se a tabela já existe
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'technician_orders') THEN
        -- Criar a tabela technician_orders
        CREATE TABLE technician_orders (
            id SERIAL PRIMARY KEY,
            technician_id INTEGER NOT NULL,
            order_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER,
            notes TEXT,
            UNIQUE(technician_id, order_id)
        );

        -- Ad<PERSON><PERSON>r índices para melhorar a performance
        CREATE INDEX idx_technician_orders_technician_id ON technician_orders(technician_id);
        CREATE INDEX idx_technician_orders_order_id ON technician_orders(order_id);

        -- Adicionar coment<PERSON>rios para documentação
        COMMENT ON TABLE technician_orders IS 'Armazena relacionamentos explícitos entre técnicos e ordens de manutenção';
        COMMENT ON COLUMN technician_orders.technician_id IS 'ID do técnico (referência à tabela users)';
        COMMENT ON COLUMN technician_orders.order_id IS 'ID da ordem de manutenção (referência à tabela maintenance_orders)';
        COMMENT ON COLUMN technician_orders.created_at IS 'Data e hora de criação do registro';
        COMMENT ON COLUMN technician_orders.created_by IS 'ID do usuário que criou o registro';
        COMMENT ON COLUMN technician_orders.notes IS 'Notas ou observações sobre a atribuição';

        -- Adicionar chaves estrangeiras
        ALTER TABLE technician_orders 
            ADD CONSTRAINT fk_technician_orders_technician 
            FOREIGN KEY (technician_id) 
            REFERENCES users(id) 
            ON DELETE CASCADE;

        ALTER TABLE technician_orders 
            ADD CONSTRAINT fk_technician_orders_order 
            FOREIGN KEY (order_id) 
            REFERENCES maintenance_orders(id) 
            ON DELETE CASCADE;

        ALTER TABLE technician_orders 
            ADD CONSTRAINT fk_technician_orders_created_by 
            FOREIGN KEY (created_by) 
            REFERENCES users(id) 
            ON DELETE SET NULL;

        RAISE NOTICE 'Tabela technician_orders criada com sucesso';
    ELSE
        RAISE NOTICE 'Tabela technician_orders já existe';
    END IF;
END
$$;

-- Inserir dados iniciais para as ordens problemáticas (16, 17, 18, 19)
-- Isso garante que os técnicos que precisam acessar essas ordens continuem tendo acesso
DO $$
DECLARE
    technician_ids INTEGER[];
    order_ids INTEGER[] := ARRAY[16, 17, 18, 19];
    tech_id INTEGER;
    order_id INTEGER;
BEGIN
    -- Obter todos os IDs de técnicos
    SELECT ARRAY_AGG(id) INTO technician_ids
    FROM users
    WHERE type = 'tecnico';

    -- Se não encontrou técnicos, usar um valor padrão para evitar erro
    IF technician_ids IS NULL THEN
        RAISE NOTICE 'Nenhum técnico encontrado no sistema';
        RETURN;
    END IF;

    -- Para cada técnico e ordem, inserir um registro se não existir
    FOREACH tech_id IN ARRAY technician_ids
    LOOP
        FOREACH order_id IN ARRAY order_ids
        LOOP
            -- Verificar se a ordem existe
            IF EXISTS (SELECT 1 FROM maintenance_orders WHERE id = order_id) THEN
                -- Inserir apenas se não existir
                INSERT INTO technician_orders (technician_id, order_id, notes)
                SELECT tech_id, order_id, 'Migração automática - acesso de emergência'
                WHERE NOT EXISTS (
                    SELECT 1 FROM technician_orders 
                    WHERE technician_id = tech_id AND order_id = order_id
                );
                
                RAISE NOTICE 'Acesso concedido: Técnico % -> Ordem %', tech_id, order_id;
            ELSE
                RAISE NOTICE 'Ordem % não existe no sistema', order_id;
            END IF;
        END LOOP;
    END LOOP;
END
$$;
