-- <PERSON><PERSON><PERSON> para criar a tabela technician_orders
-- Esta tabela armazena relacionamentos explícitos entre técnicos e ordens de manutenção

-- Verificar se a tabela já existe
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'technician_orders') THEN
        -- Criar a tabela technician_orders
        CREATE TABLE technician_orders (
            id SERIAL PRIMARY KEY,
            technician_id INTEGER NOT NULL,
            order_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER,
            notes TEXT,
            UNIQUE(technician_id, order_id)
        );

        -- <PERSON><PERSON><PERSON><PERSON> chaves estrangeiras
        ALTER TABLE technician_orders 
            ADD CONSTRAINT fk_technician_orders_technician 
            FOR<PERSON><PERSON><PERSON> KEY (technician_id) 
            REFERENCES users(id) 
            ON DELETE CASCADE;

        ALTER TABLE technician_orders 
            ADD CONSTRAINT fk_technician_orders_order 
            FOREIGN KEY (order_id) 
            REFERENCES maintenance_orders(id) 
            ON DELETE CASCADE;

        ALTER TABLE technician_orders 
            ADD CONSTRAINT fk_technician_orders_created_by 
            FOREIGN KEY (created_by) 
            REFERENCES users(id) 
            ON DELETE SET NULL;

        -- Migrar atribuições existentes
        INSERT INTO technician_orders (technician_id, order_id, notes)
        SELECT technician_id, id, 'Migrado automaticamente'
        FROM maintenance_orders
        WHERE technician_id IS NOT NULL;

        RAISE NOTICE 'Tabela technician_orders criada com sucesso e atribuições existentes migradas';
    ELSE
        RAISE NOTICE 'Tabela technician_orders já existe';
    END IF;
END
$$;

-- Criar índices para melhorar a performance
CREATE INDEX IF NOT EXISTS idx_technician_orders_technician_id ON technician_orders(technician_id);
CREATE INDEX IF NOT EXISTS idx_technician_orders_order_id ON technician_orders(order_id);

-- Adicionar comentários para documentação
COMMENT ON TABLE technician_orders IS 'Armazena relacionamentos entre técnicos e ordens de manutenção';
COMMENT ON COLUMN technician_orders.technician_id IS 'ID do técnico (referência à tabela users)';
COMMENT ON COLUMN technician_orders.order_id IS 'ID da ordem de manutenção (referência à tabela maintenance_orders)';
COMMENT ON COLUMN technician_orders.created_at IS 'Data e hora da criação do relacionamento';
COMMENT ON COLUMN technician_orders.created_by IS 'ID do usuário que criou o relacionamento (referência à tabela users)';
COMMENT ON COLUMN technician_orders.notes IS 'Observações sobre o relacionamento';
