-- <PERSON><PERSON><PERSON> de usuários principal do sistema
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    avatar_url VARCHAR(255),
    station_id INTEGER,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Inserir usuários padrão
INSERT INTO users (name, email, password, role, is_active)
VALUES
    ('Administrador', '<EMAIL>', '$2a$10$sVYA8o1.T6cSt0sWoC2Dv.hNwNGVpgGEjwb7vFwcfBVvG0vCgjPBO', 'admin', true),
    ('Admin Teste', '<EMAIL>', '$2a$10$UdguD1hJvDkemphtqjD77.0CIeRCTjZNFgVRUEe7hzzHnJifrisAG', 'admin', true),
    ('Gerente Teste', '<EMAIL>', '$2a$10$q8ga1HaXOWf5gKXF8r5EVeuDHne4Lp.jowQCtl435tZV4Bx2ADHhe', 'gerente', true),
    ('Técnico Teste', '<EMAIL>', '$2a$10$5Glp7ghRRb6.gwQJe5GhCefa2ul7Sf65F7gcaEe9xkkl.M5LfQa2W', 'tecnico', true),
    ('Funcionário Teste', '<EMAIL>', '$2a$10$LTyVMlI7o5wtpNbkbDe7jugm21/OEHUvxphrFp75GFIFMC2L0.b3y', 'funcionario', true),
    ('Financeiro Teste', '<EMAIL>', '$2a$10$iUMmQk/i8bnl0VHhg9N0nOkEyL27KQ7AiRJlb9.UkqyXpucOPGPuG', 'financeiro', true),
    ('Filial Teste', '<EMAIL>', '$2a$10$iUMmQk/i8bnl0VHhg9N0nOkEyL27KQ7AiRJlb9.UkqyXpucOPGPuG', 'filial', true),
    ('Prestador Teste', '<EMAIL>', '$2a$10$iUMmQk/i8bnl0VHhg9N0nOkEyL27KQ7AiRJlb9.UkqyXpucOPGPuG', 'prestadores', true)
ON CONFLICT (email) DO NOTHING;