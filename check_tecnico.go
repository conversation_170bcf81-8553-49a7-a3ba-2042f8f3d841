package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/lib/pq"
	"golang.org/x/crypto/bcrypt"
)

func main() {
	// Configurações do banco de dados
	host := "postgres-ag-br1-03.conteige.cloud"
	port := "54243"
	user := "fcobdj_tradicao"
	password := "67573962"
	dbname := "fcobdj_tradicao"

	// Construir string de conexão
	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		host, port, user, password, dbname)

	// Conectar ao banco de dados
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}
	defer db.Close()

	// Verificar a conexão
	err = db.Ping()
	if err != nil {
		log.Fatalf("Erro ao verificar conexão com o banco de dados: %v", err)
	}
	fmt.Println("Conexão com o banco de dados estabelecida com sucesso!")

	// Verificar o usuário técnico
	email := "<EMAIL>"

	// Primeiro, vamos verificar a estrutura da tabela users
	fmt.Println("Verificando estrutura da tabela users...")

	columnsQuery := `
		SELECT column_name, data_type
		FROM information_schema.columns
		WHERE table_name = 'users'
		ORDER BY ordinal_position
	`

	rows, err := db.Query(columnsQuery)
	if err != nil {
		log.Fatalf("Erro ao consultar estrutura da tabela: %v", err)
	}
	defer rows.Close()

	fmt.Println("Colunas da tabela users:")
	for rows.Next() {
		var columnName, dataType string
		if err := rows.Scan(&columnName, &dataType); err != nil {
			log.Fatalf("Erro ao ler colunas: %v", err)
		}
		fmt.Printf("- %s (%s)\n", columnName, dataType)
	}

	// Consultar informações do usuário
	var id int
	var name, userEmail, role, userPassword string
	var blocked bool
	var branchID sql.NullInt64

	query := `
		SELECT id, name, email, password, type, blocked, branch_id
		FROM users
		WHERE email = $1
	`

	err = db.QueryRow(query, email).Scan(&id, &name, &userEmail, &userPassword, &role, &blocked, &branchID)
	if err != nil {
		if err == sql.ErrNoRows {
			fmt.Printf("Usuário com email %s não encontrado\n", email)
		} else {
			log.Fatalf("Erro ao consultar usuário: %v", err)
		}
		return
	}

	// Exibir informações do usuário
	fmt.Println("=== Informações do Usuário ===")
	fmt.Printf("ID: %d\n", id)
	fmt.Printf("Nome: %s\n", name)
	fmt.Printf("Email: %s\n", userEmail)
	fmt.Printf("Perfil: %s\n", role)
	fmt.Printf("Bloqueado: %t\n", blocked)

	if branchID.Valid {
		fmt.Printf("ID da Filial: %d\n", branchID.Int64)
	} else {
		fmt.Println("ID da Filial: NULL")
	}

	// Verificar se o usuário está associado a um prestador de serviço
	// Consultando a tabela technicians para ver se o usuário é um técnico
	var technicianID sql.NullInt64
	var providerID sql.NullInt64

	queryTechnician := `
		SELECT id, provider_id
		FROM technicians
		WHERE user_id = $1
	`

	err = db.QueryRow(queryTechnician, id).Scan(&technicianID, &providerID)
	if err != nil {
		if err == sql.ErrNoRows {
			fmt.Println("Usuário não está associado a nenhum técnico")
		} else {
			fmt.Printf("Erro ao consultar técnico: %v\n", err)
		}
	} else {
		fmt.Printf("ID do Técnico: %d\n", technicianID.Int64)
		if providerID.Valid {
			fmt.Printf("ID do Prestador: %d\n", providerID.Int64)
		} else {
			fmt.Println("ID do Prestador: NULL")
		}
	}

	// Verificar a senha
	testPassword := "tradicaosistema"
	err = bcrypt.CompareHashAndPassword([]byte(userPassword), []byte(testPassword))
	if err != nil {
		fmt.Printf("A senha '%s' NÃO corresponde à senha armazenada\n", testPassword)
	} else {
		fmt.Printf("A senha '%s' corresponde à senha armazenada\n", testPassword)
	}

	// Verificar informações do prestador de serviço
	if providerID.Valid {
		var providerName string
		queryProvider := `
			SELECT name FROM providers WHERE id = $1
		`
		err = db.QueryRow(queryProvider, providerID.Int64).Scan(&providerName)
		if err != nil {
			if err == sql.ErrNoRows {
				fmt.Printf("Prestador com ID %d não encontrado\n", providerID.Int64)
			} else {
				fmt.Printf("Erro ao consultar prestador: %v\n", err)
			}
		} else {
			fmt.Printf("Prestador associado: %s (ID: %d)\n", providerName, providerID.Int64)
		}
	}

	// Verificar se o usuário está associado a uma filial
	if branchID.Valid {
		var branchName string
		queryBranch := `
			SELECT name FROM branches WHERE id = $1
		`
		err = db.QueryRow(queryBranch, branchID.Int64).Scan(&branchName)
		if err != nil {
			if err == sql.ErrNoRows {
				fmt.Printf("Filial com ID %d não encontrada\n", branchID.Int64)
			} else {
				fmt.Printf("Erro ao consultar filial: %v\n", err)
			}
		} else {
			fmt.Printf("Filial associada: %s (ID: %d)\n", branchName, branchID.Int64)
		}
	}
}
