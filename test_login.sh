#!/bin/bash

# Script para testar o login do usuário técnico
echo "Testando login do usuário técnico..."

# Dados de login
EMAIL="<EMAIL>"
SENHA="tradicaosistema"

# Fazer requisição de login
curl -s -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"$EMAIL\",\"password\":\"$SENHA\"}" \
  -c cookies.txt \
  -o response.json

# Exibir resposta
echo "Resposta do servidor:"
cat response.json
echo ""

# Verificar se o login foi bem-sucedido
if grep -q "token" response.json; then
  echo "✅ Login bem-sucedido!"
  # Extrair o token para uso posterior
  TOKEN=$(grep -o '"token":"[^"]*' response.json | cut -d'"' -f4)
  echo $TOKEN > token_tecnico.txt
  echo "Token salvo em token_tecnico.txt"
else
  echo "❌ Falha no login. Verifique a resposta acima para mais detalhes."
fi
