# Memórias do Sistema de Gestão Tradição

## Contexto do Projeto
- O Sistema de Gestão Tradição é uma plataforma para gerenciamento de manutenção de equipamentos em rede de postos de combustível.
- Desenvolvido em Go com framework Gin, PostgreSQL e frontend com Bootstrap.
- Foco em gestão de ordens de serviçoA, equipamentos, filiais e técnicos.
- Implementa sistema de notificações em tempo real e calendário de manutenções.
- Utiliza autenticação JWT com sistema de permissões baseado em perfis.

## Preferências de Comunicação
- **Idioma**: Comunicar SEMPRE em português brasileiro técnico e claro.
- **Nível de Detalhe**: Fornecer explicações detalhadas e passo a passo.
- **Formato de Código**: Apresentar exemplos de código completos e bem comentados.
- **Abordagem**: Realizar análise profunda antes de propor soluções.
- **Personalização**: Criar conteúdo totalmente personalizado sem basear-se em documentações genéricas.

## Configurações Técnicas
- **Banco de Dados**: PostgreSQL remoto com configurações padrão (sem arquivo .env).
- **Inicialização**: Servidor e banco de dados via shell script (não usar Docker).
- **Identificadores**: IDs numéricos com estratégia de geração documentada.
- **Tecnologias Proibidas**: Não utilizar Python ou Docker no projeto.
- **Backups**: Criar em pasta 'progressos' com nomes descritivos e documentação detalhada.
- **Ambiente de Desenvolvimento**: Usuário possui Windows 11, VS Code, Augment e Kali-Linux na mesma rede.

## Padrões de Arquitetura
- **Estrutura de Diretórios**: Manter organização existente sem duplicação.
- **Análise Prévia**: Verificar existência de arquivos/componentes antes de criar novos.
- **Edição de Código**: Preferir editar arquivos existentes em partes pequenas e verificáveis.
- **Abstração**: Fortalecer camadas de abstração no código.
- **Sistema de Permissões**: Implementar centralizado com análise prévia completa.
- **Isolamento de Dados**: Cada filial deve acessar apenas seus próprios equipamentos.
- **Visibilidade**: Cada perfil (filial, técnico) deve ver apenas ordens relacionadas a ele.

## Padrões de Frontend
- **Estilo Visual**: Fundo escuro com elementos de interface compactos.
- **Responsividade**: Todas as interfaces devem ser responsivas.
- **Componentes**: Separados para o painel financeiro.
- **Modais**: Utilizar para componentes simples em vez de templates separados.
- **Cards**: Executar showModal quando clicados.
- **Sidebar**: Manter template como 'layouts/sidebar.html'.
- **Documentação**: Componentes HTML na mesma subpasta do template correspondente.

## Requisitos de Implementação
- **Equipamentos**: Implementar edição e transferência entre filiais corretamente.
- **Dados Reais**: Remover equipamentos fictícios.
- **Notificações**: Configurar apropriadamente por perfil de usuário.
- **Dashboard**: Criar nova página inicial com informações sobre o sistema.
- **Ordem Técnica**: Substituir 'calendário' por 'ordem técnica' para perfil técnico.
- **Minha Conta**: Redesenhar implementando rotas API para equipamentos.
- **Galeria**: Corrigir problemas de formatação visual.
- **Financeiro**: Corrigir exibição detalhada do card de cotações.
- **Ferramentas Financeiras**: Excluir esta seção.

## Modificações de Rotas
- Alterar '/calendario-flip' para '/ordemtecnica'.
- ManutencaoOrdem deve ter o mesmo visual da página calendario.
- Implementar rotas API para equipamentos.
- Criar páginas de edição para '/minha-conta'.

## Credenciais e Segurança
- **Acesso ao Sistema**: <EMAIL> / tradicaosistema
- **Admin**: <EMAIL> (senha deve ser alterada para senha123)
- **JWT**: Implementar com refresh tokens e blacklist para logout.
- **Permissões**: Sistema granular baseado em perfis e recursos.
- **Proteção**: Implementar contra CSRF, XSS e SQL Injection.
- **Auditoria**: Registrar operações sensíveis.

## Necessidades de Documentação
- Documentação detalhada para precificação (funcionalidades e detalhes técnicos).
- Instruções para deployment e configuração de ambiente distribuído.
- Documentação de componentes HTML.
- Estratégias de IDs e geração.

## Preferências de Desenvolvimento
- Análises profundas do código antes de implementar soluções.
- Instruções simplificadas devido ao conhecimento limitado de programação do usuário.
- Execução de comandos em terminais separados em vez de modificar scripts.
- Não executar servidores em segundo plano.
- Analisar e corrigir todos os erros antes de testar implementações.

## Regras de Arquivos
- Respeitar regras em .cursor\rules\backend.mdc e .cursor\rules\frontend.mdc.
- Criar arquivos separados para componentes do painel financeiro.
- Utilizar Bootstrap e extensões compatíveis com Go.
- Manter exatamente o mesmo layout entre páginas relacionadas.
