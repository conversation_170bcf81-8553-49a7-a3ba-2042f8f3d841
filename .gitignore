# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
bin/

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
vendor/

# Go workspace file
go.work

# Environment variables
.env

# IDE files
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Database files
*.postgresql*

# Temporary files
tmp/
temp/

# Generated files
*.gen.go
# Não ignorar a pasta ent/ pois contém código gerado necessário
# ent/

# Build output
dist/
build/

# OS specific files
.DS_Store
Thumbs.db

# Arquivos específicos do projeto
*.Zone.Identifier

# Arquivos de backup
backups/
*.bak
*.backup

# Scripts locais de desenvolvimento
dev_scripts/

# Arquivos de configuração local
.env.local
.env.development.local
.env.test.local
.env.production.local

sync_config.jsonc
node_modules
