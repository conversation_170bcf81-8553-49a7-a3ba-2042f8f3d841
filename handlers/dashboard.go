package handlers

import (
	"net/http"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

func Dashboard(c *gin.Context) {
	c.HTML(http.StatusOK, "dashboard.html", gin.H{
		"title": "Dashboard",
	})
}

// SaveSettings handles the saving of user settings
func SaveSettings(c *gin.Context) {
	emailNotifications := c.PostForm("email_notifications")
	smsNotifications := c.PostForm("sms_notifications")
	language := c.PostForm("language")
	twoFactorAuth := c.PostForm("two_factor_auth")

	// Logic to save settings to the database
	err := services.SaveUserSettings(emailNotifications, smsNotifications, language, twoFactorAuth)
	if err != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{"success": false, "message": "Erro ao salvar configurações."})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{"success": true})
}
