# Padronização de Nomenclatura

Este documento estabelece as convenções de nomenclatura para o Sistema de Gestão Tradição, visando garantir consistência em todo o código, documentação e interface do usuário.

## Princípios Gerais

1. **Consistência**: Usar o mesmo termo para o mesmo conceito em todo o sistema
2. **Clareza**: Escolher termos que comuniquem claramente o propósito
3. **Contexto**: Adaptar a nomenclatura ao contexto (código vs. interface)
4. **Idioma**: Seguir convenções de idioma específicas para cada contexto

## Convenções de Idioma

### Código-Fonte (Backend e Frontend)

- **Idioma Base**: Inglês
- **Comentários**: Português
- **Documentação de Código**: Português
- **Mensagens de Erro Técnicas**: Inglês
- **Mensagens para o Usuário**: Português

### Interface do Usuário

- **Idioma**: Português brasileiro
- **Termos Técnicos**: Manter em inglês apenas quando não houver tradução adequada

### Documentação

- **Idioma**: Português brasileiro
- **Termos Técnicos**: Manter em inglês apenas quando necessário, com explicação em português

## Termos Padronizados

A tabela abaixo define os termos padronizados para conceitos-chave no sistema:

| Conceito | Termo no Código | Termo na Interface | Descrição |
|----------|----------------|-------------------|-----------|
| Filial | branch | Filial | Unidade de negócio (posto de combustível) |
| Equipamento | equipment | Equipamento | Dispositivo físico mantido pelo sistema |
| Ordem de Serviço | order | Ordem de Serviço | Solicitação de manutenção ou serviço |
| Técnico | technician | Técnico | Profissional que executa serviços |
| Prestador de Serviço | service_provider | Prestador de Serviço | Empresa que fornece serviços técnicos |
| Usuário | user | Usuário | Pessoa que utiliza o sistema |
| Perfil | profile | Perfil | Conjunto de permissões e acesso |
| Permissão | permission | Permissão | Autorização para executar ação específica |
| Notificação | notification | Notificação | Mensagem informativa para usuários |
| Calendário | calendar | Calendário | Visualização de eventos programados |
| Dashboard | dashboard | Painel | Visão consolidada de informações |
| Relatório | report | Relatório | Documento com informações consolidadas |
| Configuração | setting | Configuração | Parâmetro ajustável do sistema |
| Galeria | gallery | Galeria | Visualização de filiais em formato de galeria |
| Consulta | query | Consulta | Busca de informações no sistema |

## Convenções de Nomenclatura por Contexto

### Banco de Dados

- **Tabelas**: Plural, snake_case (ex: `users`, `equipment_types`)
- **Colunas**: Singular, snake_case (ex: `first_name`, `created_at`)
- **Chaves Primárias**: `id` (padrão)
- **Chaves Estrangeiras**: `[tabela_singular]_id` (ex: `user_id`, `branch_id`)
- **Índices**: `idx_[tabela]_[coluna(s)]` (ex: `idx_users_email`)
- **Constraints**: `fk_[tabela]_[tabela_referenciada]` (ex: `fk_orders_branches`)

### Código Go

- **Pacotes**: Minúsculas, sem underscores (ex: `handlers`, `services`)
- **Interfaces**: CamelCase, geralmente com sufixo "er" (ex: `UserService`, `OrderRepository`)
- **Structs**: CamelCase (ex: `User`, `ServiceOrder`)
- **Métodos**: CamelCase (ex: `GetUserByID`, `CreateOrder`)
- **Variáveis**: camelCase (ex: `userID`, `orderStatus`)
- **Constantes**: ALL_CAPS com underscores (ex: `MAX_RETRY_ATTEMPTS`, `DEFAULT_TIMEOUT`)
- **Erros**: CamelCase com prefixo "Err" (ex: `ErrUserNotFound`, `ErrInvalidInput`)

### Frontend (HTML/CSS/JS)

- **IDs HTML**: kebab-case (ex: `user-profile`, `order-details`)
- **Classes CSS**: kebab-case (ex: `sidebar-menu`, `order-card`)
- **Variáveis JavaScript**: camelCase (ex: `userData`, `orderList`)
- **Funções JavaScript**: camelCase (ex: `getUserData`, `updateOrderStatus`)
- **Componentes**: PascalCase (ex: `UserProfile`, `OrderDetails`)
- **Arquivos de Template**: kebab-case (ex: `user-profile.html`, `order-details.html`)

### URLs e Endpoints

- **APIs**: `/api/v1/[recurso]` (ex: `/api/v1/users`, `/api/v1/orders`)
- **Recursos**: Plural, kebab-case (ex: `/branches`, `/service-orders`)
- **Ações**: Verbos HTTP (GET, POST, PUT, DELETE)
- **Parâmetros de Query**: camelCase (ex: `?userId=123`, `?startDate=2023-01-01`)
- **Parâmetros de Path**: camelCase (ex: `/users/{userId}`, `/orders/{orderId}`)

### Arquivos e Diretórios

- **Diretórios**: Minúsculas, sem espaços (ex: `handlers`, `services`)
- **Arquivos Go**: snake_case (ex: `user_handler.go`, `order_service.go`)
- **Arquivos de Template**: kebab-case (ex: `user-profile.html`, `order-details.html`)
- **Arquivos CSS/JS**: kebab-case (ex: `main-styles.css`, `order-functions.js`)
- **Arquivos de Configuração**: Minúsculas, com extensão apropriada (ex: `config.yaml`, `database.json`)
- **Arquivos de Documentação**: CamelCase ou Title Case, com extensão .md (ex: `UserGuide.md`, `API_Reference.md`)

## Casos Especiais e Exceções

### Termos Técnicos

Alguns termos técnicos devem ser mantidos em inglês mesmo na interface, por serem amplamente reconhecidos:

- Login/Logout
- Dashboard
- Upload/Download
- Cache
- Backup

### Acrônimos

- Em variáveis camelCase, acrônimos com mais de 2 letras seguem a regra normal (ex: `htmlContent`, `apiKey`)
- Acrônimos de 2 letras em camelCase são mantidos em maiúsculas (ex: `userID`, `orderUI`)
- Em PascalCase, acrônimos são mantidos em maiúsculas (ex: `HTMLParser`, `APIClient`)

### Pluralização

- Nomes de tabelas são pluralizados (ex: `users`, `orders`)
- Nomes de recursos em APIs são pluralizados (ex: `/api/v1/users`, `/api/v1/orders`)
- Nomes de structs e interfaces são singulares (ex: `User`, `Order`)

## Processo de Transição

A padronização de nomenclatura será implementada gradualmente:

1. **Novos Desenvolvimentos**: Seguir estritamente estas convenções
2. **Refatorações**: Aplicar convenções durante refatorações planejadas
3. **Correções**: Aplicar convenções ao corrigir bugs
4. **Documentação**: Atualizar documentação para refletir as convenções

Durante o período de transição, manter um mapeamento de termos antigos para novos para facilitar a compreensão.

## Verificação de Conformidade

Para garantir a conformidade com estas convenções:

1. **Revisão de Código**: Verificar nomenclatura durante revisões de código
2. **Linters**: Configurar linters para verificar convenções de nomenclatura
3. **Documentação**: Manter este documento atualizado com novos termos e convenções
4. **Treinamento**: Treinar novos desenvolvedores nestas convenções

## Conclusão

A adoção consistente destas convenções de nomenclatura contribuirá para um código mais legível, manutenível e coeso. Todos os membros da equipe devem se familiarizar com estas convenções e aplicá-las em seu trabalho.

Este documento deve ser revisado e atualizado periodicamente para refletir a evolução do sistema e as melhores práticas da indústria.
