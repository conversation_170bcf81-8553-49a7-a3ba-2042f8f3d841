# Documentação do Sistema de Gestão Tradição

## Visão Geral

O Sistema de Gestão Tradição é uma plataforma completa para gerenciamento de manutenção de equipamentos em rede de postos de combustível. O sistema foi desenvolvido utilizando Go com o framework Gin para o backend e Bootstrap para o frontend, seguindo princípios de arquitetura limpa e boas práticas de desenvolvimento.

Esta documentação fornece informações detalhadas sobre a arquitetura, componentes, sistemas e funcionalidades do Sistema de Gestão Tradição.

## Estrutura da Documentação

### [Arquitetura](./arquitetura/)
- [Visão Geral](./arquitetura/visao_geral.md) - Visão geral da arquitetura do sistema
- [Backend](./arquitetura/backend.md) - Detalhes da arquitetura do backend
- [Frontend](./arquitetura/frontend.md) - <PERSON>alhes da arquitetura do frontend
- [Banco de Dados](./arquitetura/banco_dados.md) - Estrutura e relacionamentos do banco de dados

### [Guias](./guias/)
- [Desenvolvimento](./guias/desenvolvimento.md) - Guia para desenvolvimento no sistema
- [Templates](./guias/templates.md) - Guia para criação de templates
- [Banco de Dados](./guias/banco_dados.md) - Guia para trabalhar com o banco de dados
- [Contribuição](./guias/contribuicao.md) - Guia para contribuir com o projeto

### [Sistemas](./sistemas/)
- [Autenticação](./sistemas/autenticacao.md) - Sistema de autenticação e segurança
- [Permissões](./sistemas/permissoes.md) - Sistema de verificação de permissões
- [Filiais](./sistemas/filiais.md) - Sistema de gestão de filiais
- [Equipamentos](./sistemas/equipamentos.md) - Sistema de gestão de equipamentos
- [Ordens de Manutenção](./sistemas/ordens_manutencao.md) - Sistema de ordens de manutenção
- [Notificações](./sistemas/notificacoes.md) - Sistema de notificações
- [Calendário](./sistemas/calendario.md) - Sistema de calendário e agendamento
- [Financeiro](./sistemas/financeiro.md) - Sistema financeiro
- [Técnicos](./sistemas/tecnicos.md) - Sistema de gestão de técnicos

### [API](./api/)
- [Visão Geral](./api/overview.md) - Visão geral da API do sistema
- [Autenticação](./api/autenticacao.md) - API de autenticação
- [Filiais](./api/filiais.md) - API de filiais
- [Equipamentos](./api/equipamentos.md) - API de equipamentos
- [Ordens](./api/ordens.md) - API de ordens de manutenção

### [Componentes](./componentes/)
- [Sidebar](./componentes/sidebar.md) - Componente de sidebar
- [Cards](./componentes/cards.md) - Componentes de cards
- [Tabelas](./componentes/tabelas.md) - Componentes de tabelas
- [Formulários](./componentes/formularios.md) - Componentes de formulários
- [Modais](./componentes/modais.md) - Componentes de modais

### [Soluções](./solucoes/)
- [Problemas Conhecidos](./solucoes/problemas_conhecidos.md) - Lista de problemas conhecidos
- [Correções Implementadas](./solucoes/correcoes_implementadas.md) - Correções implementadas
- [Troubleshooting](./solucoes/troubleshooting.md) - Guia de troubleshooting

### [Padronização](./padronizacao/)
- [Nomenclatura](./padronizacao/nomenclatura.md) - Padronização de nomenclatura
- [Estrutura de Código](./padronizacao/estrutura_codigo.md) - Padronização da estrutura de código
- [Estrutura de Documentação](./padronizacao/estrutura_documentacao.md) - Padronização da estrutura de documentação
- [Convenções Git](./padronizacao/convencoes_git.md) - Convenções para uso do Git

## Como Usar Esta Documentação

Esta documentação foi organizada para facilitar a navegação e o acesso às informações relevantes para diferentes perfis de usuários:

- **Desenvolvedores**: Consulte as seções de Arquitetura, Guias e Padronização para entender como o sistema está estruturado e como contribuir.
- **Administradores**: Consulte as seções de Sistemas e Soluções para entender como o sistema funciona e como resolver problemas.
- **Integradores**: Consulte a seção de API para entender como integrar com o sistema.

## Convenções

Nesta documentação, seguimos as seguintes convenções:

- Termos técnicos em inglês são mantidos em sua forma original.
- Nomes de arquivos, diretórios e comandos são formatados como `código`.
- Exemplos de código são apresentados em blocos de código com a linguagem especificada.
- Notas importantes são destacadas como blocos de nota.

## Contribuição

Para contribuir com esta documentação, siga as diretrizes no [Guia de Contribuição](./guias/contribuicao.md).

## Histórico de Versões

- **v1.0.0** (Data Atual) - Versão inicial da documentação unificada.