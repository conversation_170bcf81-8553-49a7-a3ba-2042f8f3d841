# Padronização de Nomenclatura no Sistema de Gestão Tradição

Este documento define as convenções de nomenclatura para o Sistema de Gestão Tradição, visando garantir consistência e facilitar a manutenção do código.

## 1. Princípios Gerais

### 1.1 Idioma

- **Código-fonte**: Todo o código-fonte (nomes de variáveis, funções, comentários) deve ser escrito em inglês.
- **Interface do usuário**: Todos os textos visíveis ao usuário devem ser em português.
- **Documentação**: A documentação técnica deve ser em português, mas pode conter termos técnicos em inglês quando necessário.

### 1.2 Convenções de Caso

- **Go (Backend)**:
  - Tipos e funções exportadas: PascalCase (ex: `MaintenanceOrder`, `GetOrderByID`)
  - Variáveis e funções não exportadas: camelCase (ex: `orderID`, `getStatus`)
  - Constantes: SNAKE_CASE_MAIÚSCULO (ex: `MAX_RETRY_COUNT`)
  - Acrônimos: Tratar como uma palavra (ex: `HttpClient`, não `HTTPClient`)

- **JavaScript (Frontend)**:
  - Variáveis e funções: camelCase (ex: `orderDetails`, `loadOrders`)
  - Classes e componentes: PascalCase (ex: `OrderDetails`, `MaintenanceForm`)
  - Constantes: SNAKE_CASE_MAIÚSCULO (ex: `MAX_ITEMS_PER_PAGE`)

- **API (URLs e parâmetros)**:
  - URLs: kebab-case (ex: `/maintenance-orders`, `/service-providers`)
  - Parâmetros de query: snake_case (ex: `?page_size=10&order_by=date`)
  - Campos de JSON: snake_case (ex: `{ "order_id": 123, "due_date": "2023-01-01" }`)

### 1.3 Abreviações

- Evitar abreviações, exceto para casos muito comuns:
  - `ID` para identificador
  - `URL` para Uniform Resource Locator
  - `HTML`, `CSS`, `JSON` para formatos conhecidos

### 1.4 Termos Padronizados

O sistema utiliza uma abordagem de nomenclatura padronizada para se referir a entidades principais:

1. **No código-fonte (backend)**: Uso exclusivo de termos em inglês (ex: "branch", "equipment", "maintenance order")
2. **Na interface do usuário**: Uso de termos em português (ex: "Filial", "Equipamento", "Ordem de Manutenção")

Exemplos específicos:
- **Modelos**: `Branch` (em vez de `Filial` ou `Station`)
- **Repositórios**: `IBranchRepository`, `BranchRepository` (em vez de `FilialRepository` ou `StationRepository`)
- **Serviços**: `BranchService` (em vez de `FilialService` ou `StationService`)
- **Rotas de API**: `/api/branches` (em vez de `/api/filiais` ou `/api/stations`)
- **Variáveis e parâmetros**: `branch`, `branchID`, `branchName` (em vez de `filial`, `filialID`, etc.)

## 2. Entidades Principais

### 2.1 Ordens de Manutenção

#### Modelo: `MaintenanceOrder`

| Campo Backend | Tipo | Campo API | Campo Frontend | Descrição |
|---------------|------|-----------|----------------|-----------|
| `ID` | uint | `id` | `id` | Identificador único |
| `Number` | string | `number` | `number` | Número da ordem |
| `Title` | string | `title` | `title` | Título da ordem |
| `Description` | string | `description` | `description` | Descrição detalhada |
| `Problem` | string | `problem` | `problem` | Descrição do problema |
| `Status` | OrderStatus | `status` | `status` | Status atual da ordem |
| `Priority` | PriorityLevel | `priority` | `priority` | Nível de prioridade |
| `BranchID` | uint | `branch_id` | `branchId` | ID da filial |
| `EquipmentID` | uint | `equipment_id` | `equipmentId` | ID do equipamento |
| `TechnicianID` | *uint | `technician_id` | `technicianId` | ID do técnico |
| `DueDate` | time.Time | `due_date` | `dueDate` | Data de vencimento |
| `CreatedAt` | time.Time | `created_at` | `createdAt` | Data de criação |
| `UpdatedAt` | time.Time | `updated_at` | `updatedAt` | Data de atualização |

### 2.2 Filiais

#### Modelo: `Branch`

| Campo Backend | Tipo | Campo API | Campo Frontend | Descrição |
|---------------|------|-----------|----------------|-----------|
| `ID` | uint | `id` | `id` | Identificador único |
| `Name` | string | `name` | `name` | Nome da filial |
| `Code` | string | `code` | `code` | Código da filial |
| `Address` | string | `address` | `address` | Endereço |
| `City` | string | `city` | `city` | Cidade |
| `State` | string | `state` | `state` | Estado (UF) |
| `ZipCode` | string | `zip_code` | `zipCode` | CEP |
| `Phone` | string | `phone` | `phone` | Telefone |
| `Email` | string | `email` | `email` | E-mail |
| `IsActive` | bool | `is_active` | `isActive` | Status de ativação |
| `BranchType` | string | `branch_type` | `branchType` | Tipo de filial |
| `CreatedAt` | time.Time | `created_at` | `createdAt` | Data de criação |
| `UpdatedAt` | time.Time | `updated_at` | `updatedAt` | Data de atualização |

### 2.3 Equipamentos

#### Modelo: `Equipment`

| Campo Backend | Tipo | Campo API | Campo Frontend | Descrição |
|---------------|------|-----------|----------------|-----------|
| `ID` | uint | `id` | `id` | Identificador único |
| `Name` | string | `name` | `name` | Nome do equipamento |
| `SerialNumber` | string | `serial_number` | `serialNumber` | Número de série |
| `Model` | string | `model` | `model` | Modelo |
| `Brand` | string | `brand` | `brand` | Marca |
| `Type` | string | `type` | `type` | Tipo de equipamento |
| `Status` | string | `status` | `status` | Status do equipamento |
| `BranchID` | uint | `branch_id` | `branchId` | ID da filial |
| `Location` | string | `location` | `location` | Localização dentro da filial |
| `CreatedAt` | time.Time | `created_at` | `createdAt` | Data de criação |
| `UpdatedAt` | time.Time | `updated_at` | `updatedAt` | Data de atualização |

## 3. Enumerações

### 3.1 Status de Ordem (`OrderStatus`)

| Valor Backend | Valor API | Valor UI |
|---------------|-----------|----------|
| `StatusPending` | `pending` | "Pendente" |
| `StatusInProgress` | `in_progress` | "Em Andamento" |
| `StatusCompleted` | `completed` | "Concluída" |
| `StatusCancelled` | `cancelled` | "Cancelada" |
| `StatusApproved` | `approved` | "Aprovada" |
| `StatusRejected` | `rejected` | "Rejeitada" |

### 3.2 Níveis de Prioridade (`PriorityLevel`)

| Valor Backend | Valor API | Valor UI |
|---------------|-----------|----------|
| `PriorityLow` | `low` | "Baixa" |
| `PriorityMedium` | `medium` | "Média" |
| `PriorityHigh` | `high` | "Alta" |
| `PriorityCritical` | `critical` | "Crítica" |

## 4. Compatibilidade com Código Legado

Para manter compatibilidade com código existente, foram implementados:

1. **Redirecionamentos de API**:
   - `/api/filiais` → `/api/branches` (status 307 Temporary Redirect)
   - `/api/stations` → `/api/branches` (status 307 Temporary Redirect)

2. **Adaptadores e Conversores**:
   - Métodos de conversão entre `Branch`, `Filial` e `Station`
   - Adaptadores para repositórios e serviços

3. **Modelos de Compatibilidade**:
   - `FilialSummary` e `StationSummary` como aliases para `BranchSummary`

## 5. Implementação

A padronização será implementada em várias fases:

1. **Fase 1**: Atualização dos modelos e estruturas de dados
2. **Fase 2**: Padronização das APIs e rotas
3. **Fase 3**: Padronização da interface do usuário
4. **Fase 4**: Padronização de serviços e repositórios
5. **Fase 5**: Documentação e limpeza

## 6. Respostas de API

### 6.1 Formato Padrão de Sucesso

```json
{
  "success": true,
  "data": { ... },
  "meta": {
    "total": 100,
    "page": 1,
    "page_size": 10,
    "total_pages": 10
  },
  "message": "Operação realizada com sucesso"
}
```

### 6.2 Formato Padrão de Erro

```json
{
  "success": false,
  "error": {
    "code": "invalid_input",
    "message": "Dados inválidos",
    "details": "O campo 'title' é obrigatório"
  }
}
```

## 7. Nomes de Handlers

### 7.1 Padrão RESTful

- `ListXXX` para listar recursos (GET /xxx)
- `GetXXX` para obter um recurso específico (GET /xxx/:id)
- `CreateXXX` para criar um recurso (POST /xxx)
- `UpdateXXX` para atualizar um recurso (PUT/PATCH /xxx/:id)
- `DeleteXXX` para excluir um recurso (DELETE /xxx/:id)

### 7.2 Ações Específicas

- `AssignXXX` para atribuir recursos
- `ApproveXXX` para aprovar recursos
- `RejectXXX` para rejeitar recursos
- `CancelXXX` para cancelar recursos

## 8. Tradução de Termos

| Termo em Inglês | Termo em Português (UI) |
|-----------------|-------------------------|
| Branch | Filial |
| Equipment | Equipamento |
| Maintenance Order | Ordem de Manutenção |
| Technician | Técnico |
| Service Provider | Prestador de Serviço |
| Status | Status |
| Priority | Prioridade |
| Due Date | Data de Vencimento |
| Created At | Data de Criação |
| Updated At | Data de Atualização |

## 9. Exemplos de Uso

### Exemplo 1: Obter uma filial pelo ID

```go
// Correto (usando o termo "branch")
branch, err := branchService.GetBranchByID(id)

// Obsoleto (usando o termo "filial")
filial, err := filialService.GetFilialByID(id)
```

### Exemplo 2: Criar uma nova ordem de manutenção

```go
// Correto (usando nomenclatura padronizada)
order := &models.MaintenanceOrder{
    Title:       "Manutenção Preventiva",
    Problem:     "Verificação periódica do equipamento",
    Status:      models.StatusPending,
    Priority:    models.PriorityMedium,
    BranchID:    branchID,
    EquipmentID: equipmentID,
    DueDate:     dueDate,
}
err := maintenanceService.CreateOrder(order)

// Obsoleto (usando nomenclatura inconsistente)
ordem := &models.OrdemManutencao{
    Titulo:        "Manutenção Preventiva",
    Descricao:     "Verificação periódica do equipamento",
    Status:        "pendente",
    Prioridade:    "media",
    FilialID:      filialID,
    EquipamentoID: equipamentoID,
    DataVencimento: dataVencimento,
}
err := manutencaoService.CriarOrdem(ordem)
```

### Exemplo 3: Chamada de API no frontend

```javascript
// Correto (usando nomenclatura padronizada)
fetch('/api/maintenance-orders')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // Usar os termos em português na interface
      renderOrdensManutencao(data.data);
    }
  });

// Correto (mapeamento de campos)
function mapOrderFromAPI(apiOrder) {
  return {
    id: apiOrder.id,
    title: apiOrder.title,
    problem: apiOrder.problem,
    status: apiOrder.status,
    priority: apiOrder.priority,
    branchId: apiOrder.branch_id,
    equipmentId: apiOrder.equipment_id,
    dueDate: new Date(apiOrder.due_date)
  };
}
```

## 10. Próximos Passos

1. **Remoção gradual de código obsoleto**: Remover gradualmente os modelos e interfaces obsoletos
2. **Atualização da documentação**: Atualizar toda a documentação para refletir a nova convenção
3. **Treinamento da equipe**: Garantir que todos os desenvolvedores sigam a nova convenção
4. **Implementação de validadores**: Criar ferramentas para validar a conformidade com as convenções

## 11. Conclusão

Esta padronização de nomenclatura visa melhorar a consistência e a manutenibilidade do código, facilitando o desenvolvimento e a compreensão do sistema por novos desenvolvedores. Ao seguir estas convenções, garantimos um código mais limpo, mais fácil de manter e com menos bugs relacionados a inconsistências de nomenclatura.
