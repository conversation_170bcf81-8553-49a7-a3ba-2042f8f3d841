# Análise e Planejamento da Implementação

## 1. Análise do Sistema Atual

### 1.1. Estrutura Atual de Prestadores e Técnicos

Atualmente, o sistema trata prestadores e técnicos como perfis de usuário separados, sem uma relação clara de hierarquia entre eles:

- **Prestadores de Serviço**: Representados pela entidade `ServiceProvider`
- **Técnicos**: Representados como usuários com perfil "tecnico"
- **Vinculação**: Existe uma tabela de vinculação entre filiais e prestadores (`branch_provider_links`)
- **Especialidades**: Técnicos têm especialidades e podem ser vinculados a filiais

### 1.2. Tabelas Relevantes no Banco de Dados

- **users**: Armazena todos os usuários do sistema, incluindo técnicos
- **service_providers**: Armazena informações sobre prestadores de serviço
- **technician_specialties**: Armazena especialidades de técnicos
- **technician_branches**: Vincula técnicos a filiais com especialidades
- **branch_provider_links**: Vincula filiais a prestadores de serviço

### 1.3. Sistema de Permissões

O sistema já possui perfis definidos para prestadores e técnicos:

- **Perfil "prestadores"**: Tem acesso a páginas como dashboard, calendario-flip, minha-conta, etc.
- **Perfil "tecnico"**: Tem acesso a páginas como dashboard, ordemtecnica, minha-conta, etc.

### 1.4. Limitações do Sistema Atual

- Não há uma relação clara de hierarquia entre prestadores e técnicos
- Prestadores não podem gerenciar seus técnicos
- Técnicos não estão vinculados a prestadores
- Não há uma página específica para prestadores gerenciarem seus técnicos
- Não há suporte para upload de logomarca para prestadores
- Não há suporte para upload de foto de perfil para técnicos

## 2. Modelo Proposto

### 2.1. Nova Estrutura

1. **Prestadores (Empresas)**:
   - Entidades empresariais que prestam serviços para a rede Tradição
   - Funcionam como "matriz" que gerencia seus técnicos
   - Podem ter múltiplos técnicos vinculados
   - Podem ter uma logomarca

2. **Técnicos**:
   - Funcionários vinculados a uma empresa prestadora
   - Designados apenas para realizar manutenções
   - Visualizam apenas ordens de serviço designadas a eles
   - Podem ter uma foto de perfil

### 2.2. Fluxo de Cadastro de Técnicos

1. Prestadora acessa sua área administrativa
2. Seleciona a opção "Gerenciar Técnicos" > "Adicionar Novo Técnico"
3. Preenche informações do técnico (nome, email, telefone, especialidades, foto)
4. Sistema gera senha temporária e cria conta para o técnico
5. Sistema envia email com credenciais temporárias
6. Técnico acessa o sistema e é forçado a trocar a senha no primeiro acesso

## 3. Plano de Implementação

### 3.1. Alterações no Banco de Dados

1. **Modificação da tabela `service_providers`**:
   - Adicionar campo `logo_url` para armazenar logomarca
   - Adicionar campos para informações empresariais completas
   - Remover campo `user_id` (se existir) para desacoplar de usuários individuais

2. **Modificação da tabela `users`**:
   - Adicionar campo `service_provider_id` para vincular técnicos a prestadores
   - Verificar se já existe campo `avatar_url` para fotos de perfil

3. **Criação de tabela `service_provider_managers`**:
   - Criar tabela para gestores de prestadores
   - Definir relações com `users` e `service_providers`

### 3.2. Implementação de Modelos e Repositórios

1. **Atualização dos modelos existentes**:
   - Modificar `ServiceProvider` para representar empresas
   - Atualizar `User` para incluir vínculo com prestador

2. **Criação de novos modelos**:
   - Implementar `ServiceProviderManager` para gestores

3. **Expansão dos repositórios existentes**:
   - Adicionar métodos para gerenciar técnicos vinculados a prestadores
   - Implementar métodos para gerenciar logomarcas e avatares

### 3.3. Implementação de Serviços

1. **Expansão do serviço de prestadores**:
   - Adicionar métodos para gerenciar técnicos
   - Implementar lógica para cadastro de técnicos com senha temporária

2. **Modificação do serviço de técnicos**:
   - Adaptar para incluir relação com prestadores
   - Implementar métodos para obter ordens designadas

### 3.4. Implementação de Handlers e Rotas

1. **Criação de handlers para prestadores**:
   - Implementar handlers para gerenciar técnicos
   - Implementar handler para upload de logomarca

2. **Atualização de handlers existentes**:
   - Adaptar handlers de técnicos para incluir relação com prestadores

3. **Configuração de novas rotas**:
   - Adicionar rotas para gerenciamento de técnicos por prestadores
   - Configurar rotas para upload de logomarca e avatar

### 3.5. Atualização do Sistema de Permissões

1. **Atualização do arquivo de permissões**:
   - Revisar e atualizar permissões para prestadores e técnicos
   - Adicionar permissões para novas rotas e recursos

### 3.6. Implementação de Interface de Usuário

1. **Criação de página para prestadoras**:
   - Implementar página "Minha Equipe" para gerenciamento de técnicos
   - Implementar componentes para upload de logomarca

2. **Atualização de templates existentes**:
   - Modificar sidebar para exibir logomarca ou avatar
   - Atualizar páginas de perfil para incluir upload de imagem

### 3.7. Implementação do Sistema de Upload de Imagens

1. **Criação de serviço de upload**:
   - Implementar serviço para gerenciar uploads de arquivos
   - Configurar diretórios para armazenar imagens

2. **Implementação de handlers para upload**:
   - Implementar handlers para upload de logomarca e avatar
   - Configurar validação de arquivos

### 3.8. Testes e Verificação

1. **Testes de banco de dados**:
   - Verificar se migrações preservam dados existentes
   - Testar consultas com novos campos e relações

2. **Testes de autenticação e permissões**:
   - Verificar se usuários existentes mantêm acesso
   - Testar novas permissões e rotas

3. **Testes de interface**:
   - Verificar se componentes de upload funcionam corretamente
   - Testar exibição de logomarcas e avatares

## 4. Verificação de Compatibilidade

### 4.1. Potenciais Problemas e Mitigações

1. **Modificação da Tabela `service_providers`**:
   - **Risco**: Alterar a estrutura da tabela pode afetar código existente
   - **Mitigação**: Criar uma migração que preserve dados existentes e adicione novos campos

2. **Relação entre Prestadores e Técnicos**:
   - **Risco**: Mudar a relação pode afetar consultas e lógica de negócio existentes
   - **Mitigação**: Implementar adaptadores para manter compatibilidade com código existente

3. **Sistema de Permissões**:
   - **Risco**: Modificar permissões pode afetar acesso a recursos
   - **Mitigação**: Atualizar cuidadosamente o arquivo de permissões e testar todas as rotas

4. **Upload de Arquivos**:
   - **Risco**: Adicionar novo tipo de upload (logomarca) pode conflitar com sistema existente
   - **Mitigação**: Reutilizar a lógica existente, adaptando para o novo caso de uso

### 4.2. Verificações Antes da Implementação

- [x] Analisar estrutura atual do banco de dados
- [x] Verificar sistema de permissões existente
- [x] Analisar interfaces de usuário existentes
- [x] Verificar sistema de upload de arquivos existente
- [x] Identificar potenciais problemas e mitigações

## 5. Próximos Passos

Após a conclusão da análise e planejamento, os próximos passos são:

1. Implementar as alterações no banco de dados
2. Implementar os modelos e repositórios
3. Implementar os serviços
4. Implementar os handlers e rotas
5. Atualizar o sistema de permissões
6. Implementar a interface de usuário
7. Implementar o sistema de upload de imagens
8. Realizar testes e verificação
9. Documentar para usuários finais
