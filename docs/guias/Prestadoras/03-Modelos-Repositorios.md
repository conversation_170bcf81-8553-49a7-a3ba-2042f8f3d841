# Modelos e Repositórios

## 1. Visão Geral

Nesta etapa, implementaremos os modelos e repositórios necessários para o sistema de prestadoras e técnicos. Isso inclui:

1. Atualização dos modelos existentes
2. Criação de novos modelos
3. Expansão dos repositórios existentes
4. Criação de novos repositórios

## 2. Atualização dos Modelos Existentes

### 2.1. Modelo `ServiceProvider`

O modelo `ServiceProvider` precisa ser atualizado para representar empresas prestadoras, com campos para informações empresariais completas e logomarca.

```go
// models/service_provider.go

package models

import (
    "time"
)

// ServiceProvider representa uma empresa prestadora de serviço
type ServiceProvider struct {
    ID              uint      `json:"id" gorm:"primaryKey"`
    Name            string    `json:"name" gorm:"not null"`
    CompanyName     string    `json:"company_name" gorm:"not null"`
    CNPJ            string    `json:"cnpj" gorm:"not null;unique"`
    Address         string    `json:"address"`
    City            string    `json:"city"`
    State           string    `json:"state"`
    ZipCode         string    `json:"zip_code"`
    ContactName     string    `json:"contact_name"`
    ContactEmail    string    `json:"contact_email"`
    ContactPhone    string    `json:"contact_phone"`
    Specialties     string    `json:"specialties"` // JSON ou texto delimitado
    AreaOfExpertise string    `json:"area_of_expertise"`
    AverageRating   float64   `json:"average_rating"`
    Status          string    `json:"status"` // ativo/inativo
    LogoURL         string    `json:"logo_url"`
    CreatedAt       time.Time `json:"created_at"`
    UpdatedAt       time.Time `json:"updated_at"`
    
    // Relações
    Technicians     []User    `json:"technicians,omitempty" gorm:"foreignKey:ServiceProviderID"`
    Managers        []ServiceProviderManager `json:"managers,omitempty" gorm:"foreignKey:ServiceProviderID"`
}
```

### 2.2. Modelo `User`

O modelo `User` precisa ser atualizado para incluir um campo `ServiceProviderID` que vincula técnicos a prestadoras.

```go
// models/user.go

package models

import (
    "time"
)

// User representa um usuário no sistema
type User struct {
    // Campos existentes...
    ID                uint      `json:"id" gorm:"primaryKey"`
    Name              string    `json:"name" gorm:"not null"`
    Email             string    `json:"email" gorm:"not null;unique"`
    Password          string    `json:"-" gorm:"not null"`
    Role              string    `json:"role" gorm:"not null"`
    AvatarURL         string    `json:"avatar_url"`
    BranchID          *uint     `json:"branch_id,omitempty" gorm:"index"`
    
    // Novo campo para vincular técnicos a prestadoras
    ServiceProviderID *uint     `json:"service_provider_id,omitempty" gorm:"index"`
    ServiceProvider   *ServiceProvider `json:"service_provider,omitempty" gorm:"foreignKey:ServiceProviderID"`
    
    // Outros campos existentes...
    ForcePasswordChange bool      `json:"force_password_change" gorm:"default:false"`
    LastPasswordChange  time.Time `json:"last_password_change"`
    CreatedAt           time.Time `json:"created_at"`
    UpdatedAt           time.Time `json:"updated_at"`
}
```

## 3. Criação de Novos Modelos

### 3.1. Modelo `ServiceProviderManager`

O modelo `ServiceProviderManager` representa um gestor de empresa prestadora, vinculando um usuário a uma prestadora com um papel específico.

```go
// models/service_provider_manager.go

package models

import (
    "time"
)

// ServiceProviderManager representa um gestor de empresa prestadora
type ServiceProviderManager struct {
    ID                uint      `json:"id" gorm:"primaryKey"`
    UserID            uint      `json:"user_id" gorm:"not null"`
    ServiceProviderID uint      `json:"service_provider_id" gorm:"not null"`
    Role              string    `json:"role" gorm:"not null"` // 'owner', 'manager', 'admin'
    CreatedAt         time.Time `json:"created_at"`
    UpdatedAt         time.Time `json:"updated_at"`
    
    // Relações
    User              User      `json:"user" gorm:"foreignKey:UserID"`
    ServiceProvider   ServiceProvider `json:"service_provider" gorm:"foreignKey:ServiceProviderID"`
}
```

### 3.2. Modelo `TechnicianRegistration`

O modelo `TechnicianRegistration` é usado para registrar novos técnicos, incluindo informações básicas e especialidades.

```go
// models/technician_registration.go

package models

// TechnicianRegistration representa os dados para registro de um novo técnico
type TechnicianRegistration struct {
    Name        string   `json:"name" binding:"required"`
    Email       string   `json:"email" binding:"required,email"`
    Phone       string   `json:"phone"`
    Specialties []string `json:"specialties"`
}
```

## 4. Expansão dos Repositórios Existentes

### 4.1. Repositório `ServiceProviderRepository`

O repositório `ServiceProviderRepository` precisa ser expandido para incluir métodos para gerenciar técnicos e logomarcas.

```go
// repository/service_provider_repository.go

package repository

import (
    "errors"

    "github.com/seu-usuario/seu-projeto/models"
    "gorm.io/gorm"
)

// ServiceProviderRepository define a interface para operações com prestadores
type ServiceProviderRepository interface {
    // Métodos existentes
    FindAll() ([]models.ServiceProvider, error)
    FindByID(id uint) (*models.ServiceProvider, error)
    Create(provider *models.ServiceProvider) error
    Update(provider *models.ServiceProvider) error
    Delete(id uint) error
    
    // Novos métodos para gerenciar técnicos
    GetTechnicians(providerID uint) ([]models.User, error)
    AddTechnician(providerID uint, userID uint) error
    RemoveTechnician(userID uint) error
    
    // Novos métodos para gerenciar gestores
    GetManagers(providerID uint) ([]models.ServiceProviderManager, error)
    AddManager(manager models.ServiceProviderManager) error
    RemoveManager(managerID uint) error
    
    // Novo método para atualizar logomarca
    UpdateLogo(providerID uint, logoURL string) error
}

// GormServiceProviderRepository implementa ServiceProviderRepository usando GORM
type GormServiceProviderRepository struct {
    db *gorm.DB
}

// NewGormServiceProviderRepository cria um novo GormServiceProviderRepository
func NewGormServiceProviderRepository(db *gorm.DB) *GormServiceProviderRepository {
    return &GormServiceProviderRepository{db: db}
}

// Implementação dos métodos existentes...

// GetTechnicians retorna todos os técnicos de um prestador
func (r *GormServiceProviderRepository) GetTechnicians(providerID uint) ([]models.User, error) {
    var technicians []models.User
    err := r.db.Where("service_provider_id = ? AND role = ?", providerID, "tecnico").Find(&technicians).Error
    return technicians, err
}

// AddTechnician adiciona um técnico a um prestador
func (r *GormServiceProviderRepository) AddTechnician(providerID uint, userID uint) error {
    return r.db.Model(&models.User{}).Where("id = ?", userID).Update("service_provider_id", providerID).Error
}

// RemoveTechnician remove um técnico de um prestador
func (r *GormServiceProviderRepository) RemoveTechnician(userID uint) error {
    return r.db.Model(&models.User{}).Where("id = ?", userID).Update("service_provider_id", nil).Error
}

// GetManagers retorna todos os gestores de um prestador
func (r *GormServiceProviderRepository) GetManagers(providerID uint) ([]models.ServiceProviderManager, error) {
    var managers []models.ServiceProviderManager
    err := r.db.Where("service_provider_id = ?", providerID).Preload("User").Find(&managers).Error
    return managers, err
}

// AddManager adiciona um gestor a um prestador
func (r *GormServiceProviderRepository) AddManager(manager models.ServiceProviderManager) error {
    return r.db.Create(&manager).Error
}

// RemoveManager remove um gestor de um prestador
func (r *GormServiceProviderRepository) RemoveManager(managerID uint) error {
    return r.db.Delete(&models.ServiceProviderManager{}, managerID).Error
}

// UpdateLogo atualiza a URL da logo de um prestador
func (r *GormServiceProviderRepository) UpdateLogo(providerID uint, logoURL string) error {
    return r.db.Model(&models.ServiceProvider{}).
        Where("id = ?", providerID).
        Update("logo_url", logoURL).Error
}
```

### 4.2. Repositório `UserRepository`

O repositório `UserRepository` precisa ser expandido para incluir métodos para gerenciar avatares.

```go
// repository/user_repository.go

package repository

import (
    "github.com/seu-usuario/seu-projeto/models"
    "gorm.io/gorm"
)

// UserRepository define a interface para operações com usuários
type UserRepository interface {
    // Métodos existentes
    FindAll() ([]models.User, error)
    FindByID(id uint) (*models.User, error)
    FindByEmail(email string) (*models.User, error)
    Create(user *models.User) error
    Update(user *models.User) error
    Delete(id uint) error
    
    // Novo método para atualizar avatar
    UpdateAvatar(userID uint, avatarURL string) error
}

// GormUserRepository implementa UserRepository usando GORM
type GormUserRepository struct {
    db *gorm.DB
}

// NewGormUserRepository cria um novo GormUserRepository
func NewGormUserRepository(db *gorm.DB) *GormUserRepository {
    return &GormUserRepository{db: db}
}

// Implementação dos métodos existentes...

// UpdateAvatar atualiza a URL do avatar de um usuário
func (r *GormUserRepository) UpdateAvatar(userID uint, avatarURL string) error {
    return r.db.Model(&models.User{}).
        Where("id = ?", userID).
        Update("avatar_url", avatarURL).Error
}
```

## 5. Criação de Novos Repositórios

### 5.1. Repositório `ServiceProviderManagerRepository`

O repositório `ServiceProviderManagerRepository` gerencia operações com gestores de prestadoras.

```go
// repository/service_provider_manager_repository.go

package repository

import (
    "github.com/seu-usuario/seu-projeto/models"
    "gorm.io/gorm"
)

// ServiceProviderManagerRepository define a interface para operações com gestores de prestadoras
type ServiceProviderManagerRepository interface {
    FindAll() ([]models.ServiceProviderManager, error)
    FindByID(id uint) (*models.ServiceProviderManager, error)
    FindByUserID(userID uint) ([]models.ServiceProviderManager, error)
    FindByProviderID(providerID uint) ([]models.ServiceProviderManager, error)
    Create(manager *models.ServiceProviderManager) error
    Update(manager *models.ServiceProviderManager) error
    Delete(id uint) error
    IsProviderManager(providerID uint, userID uint) (bool, error)
}

// GormServiceProviderManagerRepository implementa ServiceProviderManagerRepository usando GORM
type GormServiceProviderManagerRepository struct {
    db *gorm.DB
}

// NewGormServiceProviderManagerRepository cria um novo GormServiceProviderManagerRepository
func NewGormServiceProviderManagerRepository(db *gorm.DB) *GormServiceProviderManagerRepository {
    return &GormServiceProviderManagerRepository{db: db}
}

// FindAll retorna todos os gestores de prestadoras
func (r *GormServiceProviderManagerRepository) FindAll() ([]models.ServiceProviderManager, error) {
    var managers []models.ServiceProviderManager
    err := r.db.Preload("User").Preload("ServiceProvider").Find(&managers).Error
    return managers, err
}

// FindByID retorna um gestor de prestadora pelo ID
func (r *GormServiceProviderManagerRepository) FindByID(id uint) (*models.ServiceProviderManager, error) {
    var manager models.ServiceProviderManager
    err := r.db.Preload("User").Preload("ServiceProvider").First(&manager, id).Error
    return &manager, err
}

// FindByUserID retorna gestores de prestadoras pelo ID do usuário
func (r *GormServiceProviderManagerRepository) FindByUserID(userID uint) ([]models.ServiceProviderManager, error) {
    var managers []models.ServiceProviderManager
    err := r.db.Where("user_id = ?", userID).Preload("ServiceProvider").Find(&managers).Error
    return managers, err
}

// FindByProviderID retorna gestores de prestadoras pelo ID da prestadora
func (r *GormServiceProviderManagerRepository) FindByProviderID(providerID uint) ([]models.ServiceProviderManager, error) {
    var managers []models.ServiceProviderManager
    err := r.db.Where("service_provider_id = ?", providerID).Preload("User").Find(&managers).Error
    return managers, err
}

// Create cria um novo gestor de prestadora
func (r *GormServiceProviderManagerRepository) Create(manager *models.ServiceProviderManager) error {
    return r.db.Create(manager).Error
}

// Update atualiza um gestor de prestadora
func (r *GormServiceProviderManagerRepository) Update(manager *models.ServiceProviderManager) error {
    return r.db.Save(manager).Error
}

// Delete remove um gestor de prestadora
func (r *GormServiceProviderManagerRepository) Delete(id uint) error {
    return r.db.Delete(&models.ServiceProviderManager{}, id).Error
}

// IsProviderManager verifica se um usuário é gestor de uma prestadora
func (r *GormServiceProviderManagerRepository) IsProviderManager(providerID uint, userID uint) (bool, error) {
    var count int64
    err := r.db.Model(&models.ServiceProviderManager{}).
        Where("service_provider_id = ? AND user_id = ?", providerID, userID).
        Count(&count).Error
    return count > 0, err
}
```

## 6. Verificação da Implementação

Após implementar os modelos e repositórios, verifique se:

1. Os modelos estão corretamente definidos com os campos necessários
2. As relações entre os modelos estão corretamente configuradas
3. Os repositórios implementam todos os métodos necessários
4. Os métodos dos repositórios funcionam corretamente

## 7. Solução de Problemas Comuns

### 7.1. Erro ao Definir Relações entre Modelos

Se ocorrer um erro ao definir relações entre modelos, verifique se:

1. Os nomes dos campos estão corretos
2. Os tipos dos campos estão corretos
3. As tags `gorm` estão corretamente definidas

### 7.2. Erro ao Implementar Métodos dos Repositórios

Se ocorrer um erro ao implementar métodos dos repositórios, verifique se:

1. Os nomes dos campos estão corretos
2. As consultas SQL estão corretas
3. Os tipos de retorno estão corretos

## 8. Próximos Passos

Após concluir a implementação dos modelos e repositórios, os próximos passos são:

1. Implementar os serviços
2. Implementar os handlers e rotas
3. Atualizar o sistema de permissões
4. Implementar a interface de usuário
5. Implementar o sistema de upload de imagens
6. Realizar testes e verificação
7. Documentar para usuários finais
