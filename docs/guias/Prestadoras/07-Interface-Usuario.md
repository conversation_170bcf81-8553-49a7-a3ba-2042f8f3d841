# Interface de Usuário

## 1. Visão Geral

Esta etapa envolve a implementação da interface de usuário para o sistema de prestadoras e técnicos. <PERSON><PERSON> inclui:

1. Criação de página para gerenciamento de técnicos
2. Criação de página para perfil da empresa
3. Atualização do sidebar para exibir logomarca ou avatar
4. Atualização da página "Minha Conta" para incluir upload de imagem

## 2. Página "Minha Equipe"

### 2.1. Objetivo

A página "Minha Equipe" permite que prestadoras gerenciem seus técnicos, incluindo:
- Visualização de todos os técnicos vinculados à empresa
- Adição de novos técnicos
- Edição de informações de técnicos existentes
- Remoção de técnicos
- Visualização de ordens de serviço atribuídas a cada técnico

### 2.2. Estrutura da Página

A página "Minha Equipe" é composta por:

1. **Cabeçalho**:
   - Título da página
   - Botão para adicionar novo técnico
   - Filtros para busca e ordenação

2. **Lista de Técnicos**:
   - Cards com informações básicas de cada técnico
   - Foto do técnico
   - Nome e especialidades
   - Status (ativo/inativo)
   - Botões para editar e remover

3. **Modal de Cadastro/Edição**:
   - Formulário para informações do técnico
   - Upload de foto
   - Seleção de especialidades
   - Opção para enviar email de convite

4. **Seção de Estatísticas**:
   - Total de técnicos
   - Técnicos ativos/inativos
   - Ordens em andamento
   - Ordens concluídas

### 2.3. Arquivos Necessários

- `web/templates/prestadoras/minha_equipe.html` - Template HTML principal
- `web/templates/prestadoras/components/modal_tecnico.html` - Componente modal
- `web/templates/prestadoras/components/card_tecnico.html` - Componente card
- `web/static/css/prestadoras/minha_equipe.css` - Estilos CSS
- `web/static/js/prestadoras/minha_equipe.js` - Funcionalidades JavaScript

## 3. Página "Perfil da Empresa"

### 3.1. Objetivo

A página "Perfil da Empresa" permite que prestadoras gerenciem suas informações empresariais, incluindo:
- Visualização e edição de informações básicas
- Upload de logomarca
- Gerenciamento de especialidades oferecidas
- Visualização de estatísticas de desempenho

### 3.2. Estrutura da Página

A página "Perfil da Empresa" é composta por:

1. **Seção de Informações Básicas**:
   - Formulário com campos para nome, CNPJ, endereço, etc.
   - Botão para salvar alterações

2. **Seção de Logomarca**:
   - Exibição da logomarca atual
   - Botão para upload de nova logomarca
   - Prévia da imagem selecionada

3. **Seção de Especialidades**:
   - Lista de especialidades oferecidas
   - Opção para adicionar/remover especialidades

4. **Seção de Estatísticas**:
   - Gráficos de desempenho
   - Histórico de ordens de serviço
   - Avaliações de clientes

### 3.3. Arquivos Necessários

- `web/templates/prestadoras/perfil_empresa.html` - Template HTML principal
- `web/templates/prestadoras/components/upload_logo.html` - Componente de upload
- `web/static/css/prestadoras/perfil_empresa.css` - Estilos CSS
- `web/static/js/prestadoras/perfil_empresa.js` - Funcionalidades JavaScript
- `web/static/js/prestadoras/upload_logo.js` - Funcionalidades para upload

## 4. Atualização do Sidebar

### 4.1. Objetivo

Atualizar o sidebar para:
- Exibir logomarca para prestadoras
- Exibir avatar para técnicos
- Incluir link para a página "Minha Equipe"

### 4.2. Modificações Necessárias

1. **Exibição de Imagem**:
   - Verificar o perfil do usuário
   - Exibir logomarca para prestadoras
   - Exibir avatar para técnicos
   - Usar imagem padrão se não houver imagem

2. **Links de Navegação**:
   - Adicionar link para "Minha Equipe" para prestadoras
   - Manter links existentes para cada perfil

### 4.3. Arquivos a Modificar

- `web/templates/layouts/sidebar.html` - Template do sidebar

## 5. Atualização da Página "Minha Conta"

### 5.1. Objetivo

Atualizar a página "Minha Conta" para:
- Incluir upload de logomarca para prestadoras
- Incluir upload de avatar para técnicos

### 5.2. Modificações Necessárias

1. **Componente de Upload para Prestadoras**:
   - Exibição da logomarca atual
   - Botão para upload de nova logomarca
   - Prévia da imagem selecionada

2. **Componente de Upload para Técnicos**:
   - Exibição do avatar atual
   - Botão para upload de novo avatar
   - Prévia da imagem selecionada

### 5.3. Arquivos a Modificar

- `web/templates/minhaconta/minha_conta.html` - Template da página
- `web/static/css/minha_conta.css` - Estilos CSS
- `web/static/js/minha_conta.js` - Funcionalidades JavaScript

## 6. Componentes Reutilizáveis

### 6.1. Componente de Upload de Imagem

Criar um componente reutilizável para upload de imagem que pode ser usado tanto para logomarcas quanto para avatares.

### 6.2. Componente de Card de Técnico

Criar um componente reutilizável para exibir informações de técnicos em formato de card.

### 6.3. Componente de Modal

Criar um componente reutilizável para modais de cadastro e edição.

## 7. Integração com APIs

### 7.1. APIs para Prestadoras

Integrar a interface com as seguintes APIs:
- `GET /api/providers/me` - Obter dados do próprio prestador
- `PUT /api/providers/me` - Atualizar dados do prestador
- `GET /api/providers/me/technicians` - Listar técnicos do prestador
- `POST /api/providers/me/technicians` - Adicionar técnico
- `DELETE /api/providers/me/technicians/:id` - Remover técnico
- `POST /api/providers/me/logo` - Upload de logomarca

### 7.2. APIs para Técnicos

Integrar a interface com as seguintes APIs:
- `GET /api/technicians/me/provider` - Obter prestador do técnico
- `GET /api/technicians/me/orders` - Listar ordens do técnico
- `POST /api/user/avatar` - Upload de avatar

## 8. Verificação da Implementação

Após implementar a interface de usuário, verifique se:

1. As páginas são exibidas corretamente para cada perfil
2. Os componentes de upload funcionam corretamente
3. As integrações com APIs funcionam corretamente
4. A interface é responsiva em diferentes tamanhos de tela
5. A experiência do usuário é intuitiva e agradável

## 9. Solução de Problemas Comuns

### 9.1. Problemas de Layout

Se ocorrerem problemas de layout, verifique:
- CSS está corretamente carregado
- Estrutura HTML está correta
- Responsividade está implementada

### 9.2. Problemas de Integração

Se ocorrerem problemas de integração com APIs, verifique:
- URLs das APIs estão corretas
- Formato dos dados está correto
- Autenticação está funcionando

## 10. Próximos Passos

Após concluir a implementação da interface de usuário, os próximos passos são:

1. Realizar testes e verificação
2. Documentar para usuários finais
