# Handlers e Rotas

## 1. Visão Geral

Nesta etapa, implementaremos os handlers e rotas necessários para o sistema de prestadoras e técnicos. Isso inclui:

1. Criação de handlers para prestadores
2. Atualização de handlers existentes
3. Configuração de novas rotas

## 2. Criação de Handlers para Prestadores

### 2.1. Handler para Prestadores

O handler para prestadores gerencia as requisições relacionadas a prestadores, incluindo gerenciamento de técnicos e upload de logomarca.

```go
// handlers/service_provider_handler.go

package handlers

import (
    "net/http"
    "strconv"

    "github.com/gin-gonic/gin"
    "github.com/seu-usuario/seu-projeto/models"
    "github.com/seu-usuario/seu-projeto/services"
)

// ServiceProviderHandler gerencia requisições relacionadas a prestadores
type ServiceProviderHandler struct {
    service       *services.ServiceProviderService
    userService   *services.UserService
    uploadService *services.FileUploadService
    emailService  services.EmailService
}

// NewServiceProviderHandler cria um novo ServiceProviderHandler
func NewServiceProviderHandler(
    service *services.ServiceProviderService,
    userService *services.UserService,
    uploadService *services.FileUploadService,
    emailService services.EmailService,
) *ServiceProviderHandler {
    return &ServiceProviderHandler{
        service:       service,
        userService:   userService,
        uploadService: uploadService,
        emailService:  emailService,
    }
}

// ListProviders lista todos os prestadores
func (h *ServiceProviderHandler) ListProviders(c *gin.Context) {
    providers, err := h.service.FindAll()
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao listar prestadores", "details": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, providers)
}

// GetProviderByID retorna um prestador pelo ID
func (h *ServiceProviderHandler) GetProviderByID(c *gin.Context) {
    id, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
        return
    }
    
    provider, err := h.service.FindByID(uint(id))
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": "Prestador não encontrado", "details": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, provider)
}

// CreateProvider cria um novo prestador
func (h *ServiceProviderHandler) CreateProvider(c *gin.Context) {
    var provider models.ServiceProvider
    if err := c.ShouldBindJSON(&provider); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
        return
    }
    
    if err := h.service.Create(&provider); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao criar prestador", "details": err.Error()})
        return
    }
    
    c.JSON(http.StatusCreated, provider)
}

// UpdateProvider atualiza um prestador
func (h *ServiceProviderHandler) UpdateProvider(c *gin.Context) {
    id, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
        return
    }
    
    var provider models.ServiceProvider
    if err := c.ShouldBindJSON(&provider); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
        return
    }
    
    provider.ID = uint(id)
    
    if err := h.service.Update(&provider); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar prestador", "details": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, provider)
}

// DeleteProvider remove um prestador
func (h *ServiceProviderHandler) DeleteProvider(c *gin.Context) {
    id, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
        return
    }
    
    if err := h.service.Delete(uint(id)); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao remover prestador", "details": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"message": "Prestador removido com sucesso"})
}

// GetProviderTechnicians retorna os técnicos de um prestador
func (h *ServiceProviderHandler) GetProviderTechnicians(c *gin.Context) {
    id, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
        return
    }
    
    // Verificar se o usuário logado tem permissão para este prestador
    userID := c.GetUint("userID")
    if !h.service.IsProviderManager(uint(id), userID) {
        c.JSON(http.StatusForbidden, gin.H{"error": "Sem permissão para gerenciar este prestador"})
        return
    }
    
    technicians, err := h.service.GetTechnicians(uint(id))
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao listar técnicos", "details": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, technicians)
}

// AddTechnician adiciona um técnico a um prestador
func (h *ServiceProviderHandler) AddTechnician(c *gin.Context) {
    // Obter ID do prestador
    providerID, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "ID do prestador inválido"})
        return
    }
    
    // Verificar se o usuário logado tem permissão para este prestador
    userID := c.GetUint("userID")
    if !h.service.IsProviderManager(uint(providerID), userID) {
        c.JSON(http.StatusForbidden, gin.H{"error": "Sem permissão para gerenciar este prestador"})
        return
    }
    
    // Obter dados do técnico
    var technicianData models.TechnicianRegistration
    if err := c.ShouldBindJSON(&technicianData); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
        return
    }
    
    // Validar dados
    if technicianData.Name == "" || technicianData.Email == "" {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Nome e email são obrigatórios"})
        return
    }
    
    // Adicionar técnico
    newUser, tempPassword, err := h.service.AddTechnician(uint(providerID), technicianData)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao adicionar técnico", "details": err.Error()})
        return
    }
    
    // Retornar dados do técnico e senha temporária
    c.JSON(http.StatusCreated, gin.H{
        "message": "Técnico adicionado com sucesso",
        "technician": newUser,
        "temp_password": tempPassword, // Apenas para ambiente de desenvolvimento
    })
}

// RemoveTechnician remove um técnico de um prestador
func (h *ServiceProviderHandler) RemoveTechnician(c *gin.Context) {
    // Obter ID do prestador
    providerID, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "ID do prestador inválido"})
        return
    }
    
    // Obter ID do técnico
    technicianID, err := strconv.ParseUint(c.Param("technicianId"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "ID do técnico inválido"})
        return
    }
    
    // Verificar se o usuário logado tem permissão para este prestador
    userID := c.GetUint("userID")
    if !h.service.IsProviderManager(uint(providerID), userID) {
        c.JSON(http.StatusForbidden, gin.H{"error": "Sem permissão para gerenciar este prestador"})
        return
    }
    
    // Remover técnico
    if err := h.service.RemoveTechnician(uint(providerID), uint(technicianID)); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao remover técnico", "details": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"message": "Técnico removido com sucesso"})
}

// UploadProviderLogo faz upload da logomarca de um prestador
func (h *ServiceProviderHandler) UploadProviderLogo(c *gin.Context) {
    // Obter ID do prestador
    providerID, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "ID do prestador inválido"})
        return
    }
    
    // Verificar se o usuário logado tem permissão para este prestador
    userID := c.GetUint("userID")
    if !h.service.IsProviderManager(uint(providerID), userID) {
        c.JSON(http.StatusForbidden, gin.H{"error": "Sem permissão para gerenciar este prestador"})
        return
    }
    
    // Obter arquivo do formulário
    file, err := c.FormFile("logo")
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Arquivo não fornecido ou inválido"})
        return
    }
    
    // Verificar tamanho máximo (2MB)
    if file.Size > 2*1024*1024 {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Tamanho máximo de arquivo excedido (2MB)"})
        return
    }
    
    // Fazer upload do arquivo
    logoURL, err := h.uploadService.UploadFile(file, services.UploadTypeLogo)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao fazer upload do arquivo", "details": err.Error()})
        return
    }
    
    // Obter prestador atual para verificar se já tem logo
    provider, err := h.service.FindByID(uint(providerID))
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao obter dados do prestador"})
        return
    }
    
    // Se já existir uma logo, remover o arquivo antigo
    if provider.LogoURL != "" {
        h.uploadService.DeleteFile(provider.LogoURL)
    }
    
    // Atualizar URL da logo no banco de dados
    if err := h.service.UpdateProviderLogo(uint(providerID), logoURL); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar logo do prestador"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "message": "Logo atualizada com sucesso",
        "logo_url": logoURL,
    })
}

// GetProviderManagers retorna os gestores de um prestador
func (h *ServiceProviderHandler) GetProviderManagers(c *gin.Context) {
    id, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
        return
    }
    
    // Verificar se o usuário logado tem permissão para este prestador
    userID := c.GetUint("userID")
    if !h.service.IsProviderManager(uint(id), userID) {
        c.JSON(http.StatusForbidden, gin.H{"error": "Sem permissão para gerenciar este prestador"})
        return
    }
    
    managers, err := h.service.GetManagers(uint(id))
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao listar gestores", "details": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, managers)
}

// AddManager adiciona um gestor a um prestador
func (h *ServiceProviderHandler) AddManager(c *gin.Context) {
    // Obter ID do prestador
    providerID, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "ID do prestador inválido"})
        return
    }
    
    // Verificar se o usuário logado tem permissão para este prestador
    userID := c.GetUint("userID")
    if !h.service.IsProviderManager(uint(providerID), userID) {
        c.JSON(http.StatusForbidden, gin.H{"error": "Sem permissão para gerenciar este prestador"})
        return
    }
    
    // Obter dados do gestor
    var manager models.ServiceProviderManager
    if err := c.ShouldBindJSON(&manager); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
        return
    }
    
    // Definir ID do prestador
    manager.ServiceProviderID = uint(providerID)
    
    // Adicionar gestor
    if err := h.service.AddManager(manager); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao adicionar gestor", "details": err.Error()})
        return
    }
    
    c.JSON(http.StatusCreated, gin.H{"message": "Gestor adicionado com sucesso"})
}

// RemoveManager remove um gestor de um prestador
func (h *ServiceProviderHandler) RemoveManager(c *gin.Context) {
    // Obter ID do prestador
    providerID, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "ID do prestador inválido"})
        return
    }
    
    // Obter ID do gestor
    managerID, err := strconv.ParseUint(c.Param("managerId"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "ID do gestor inválido"})
        return
    }
    
    // Verificar se o usuário logado tem permissão para este prestador
    userID := c.GetUint("userID")
    if !h.service.IsProviderManager(uint(providerID), userID) {
        c.JSON(http.StatusForbidden, gin.H{"error": "Sem permissão para gerenciar este prestador"})
        return
    }
    
    // Remover gestor
    if err := h.service.RemoveManager(uint(managerID)); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao remover gestor", "details": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"message": "Gestor removido com sucesso"})
}
```

### 2.2. Handler para Upload de Avatar

O handler para upload de avatar gerencia o upload de fotos de perfil para técnicos.

```go
// handlers/user_handler.go

// Adicionar ao UserHandler existente

// UploadUserAvatar faz upload do avatar de um usuário
func (h *UserHandler) UploadUserAvatar(c *gin.Context) {
    // Obter ID do usuário
    userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "ID do usuário inválido"})
        return
    }
    
    // Verificar permissão (usuário só pode alterar seu próprio avatar ou ser admin)
    loggedUserID := c.GetUint("userID")
    loggedUserRole := c.GetString("userRole")
    
    if loggedUserID != uint(userID) && loggedUserRole != "admin" {
        c.JSON(http.StatusForbidden, gin.H{"error": "Sem permissão para alterar este avatar"})
        return
    }
    
    // Obter arquivo do formulário
    file, err := c.FormFile("avatar")
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Arquivo não fornecido ou inválido"})
        return
    }
    
    // Verificar tamanho máximo (1MB)
    if file.Size > 1*1024*1024 {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Tamanho máximo de arquivo excedido (1MB)"})
        return
    }
    
    // Fazer upload do arquivo
    avatarURL, err := h.uploadService.UploadFile(file, services.UploadTypeAvatar)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao fazer upload do arquivo", "details": err.Error()})
        return
    }
    
    // Obter usuário atual para verificar se já tem avatar
    user, err := h.userService.GetUserByID(uint(userID))
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao obter dados do usuário"})
        return
    }
    
    // Se já existir um avatar, remover o arquivo antigo
    if user.AvatarURL != "" {
        h.uploadService.DeleteFile(user.AvatarURL)
    }
    
    // Atualizar URL do avatar no banco de dados
    if err := h.userService.UpdateUserAvatar(uint(userID), avatarURL); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar avatar do usuário"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "message": "Avatar atualizado com sucesso",
        "avatar_url": avatarURL,
    })
}
```

## 3. Atualização de Handlers Existentes

### 3.1. Handler para Técnicos

O handler para técnicos precisa ser atualizado para incluir métodos para obter o prestador de um técnico e as ordens designadas a ele.

```go
// handlers/technician_handler.go

// Adicionar ao TechnicianHandler existente

// GetTechnicianProvider retorna o prestador de um técnico
func (h *TechnicianHandler) GetTechnicianProvider(c *gin.Context) {
    id, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
        return
    }
    
    provider, err := h.service.GetServiceProvider(uint(id))
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": "Prestador não encontrado", "details": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, provider)
}

// GetTechnicianOrders retorna as ordens de serviço de um técnico
func (h *TechnicianHandler) GetTechnicianOrders(c *gin.Context) {
    id, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
        return
    }
    
    orders, err := h.service.GetOrdersByTechnician(uint(id))
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao listar ordens", "details": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, orders)
}
```

## 4. Configuração de Novas Rotas

### 4.1. Rotas para Prestadores

```go
// routes/service_provider_routes.go

package routes

import (
    "github.com/gin-gonic/gin"
    "github.com/seu-usuario/seu-projeto/handlers"
    "github.com/seu-usuario/seu-projeto/middleware"
)

// SetupServiceProviderRoutes configura as rotas para prestadores
func SetupServiceProviderRoutes(router *gin.Engine, providerHandler *handlers.ServiceProviderHandler, authMiddleware gin.HandlerFunc) {
    // Grupo de rotas para prestadores
    providers := router.Group("/api/providers")
    providers.Use(authMiddleware)
    
    // Rotas básicas
    providers.GET("", providerHandler.ListProviders)
    providers.POST("", providerHandler.CreateProvider)
    providers.GET("/:id", providerHandler.GetProviderByID)
    providers.PUT("/:id", providerHandler.UpdateProvider)
    providers.DELETE("/:id", providerHandler.DeleteProvider)
    
    // Rotas para técnicos
    technicians := providers.Group("/:id/technicians")
    {
        technicians.GET("", providerHandler.GetProviderTechnicians)
        technicians.POST("", providerHandler.AddTechnician)
        technicians.DELETE("/:technicianId", providerHandler.RemoveTechnician)
    }
    
    // Rotas para gestores
    managers := providers.Group("/:id/managers")
    {
        managers.GET("", providerHandler.GetProviderManagers)
        managers.POST("", providerHandler.AddManager)
        managers.DELETE("/:managerId", providerHandler.RemoveManager)
    }
    
    // Rota para upload de logomarca
    providers.POST("/:id/logo", providerHandler.UploadProviderLogo)
}
```

### 4.2. Rotas para Técnicos

```go
// routes/technician_routes.go

package routes

import (
    "github.com/gin-gonic/gin"
    "github.com/seu-usuario/seu-projeto/handlers"
    "github.com/seu-usuario/seu-projeto/middleware"
)

// SetupTechnicianRoutes configura as rotas para técnicos
func SetupTechnicianRoutes(router *gin.Engine, technicianHandler *handlers.TechnicianHandler, authMiddleware gin.HandlerFunc) {
    // Grupo de rotas para técnicos
    technician := router.Group("/api/technicians")
    technician.Use(authMiddleware)
    
    // Rotas de listagem
    technician.GET("", technicianHandler.ListTechnicians)
    technician.GET("/:id", technicianHandler.GetTechnicianByID)
    
    // Rota para obter o prestador do técnico
    technician.GET("/:id/provider", technicianHandler.GetTechnicianProvider)
    
    // Rota para ordens de serviço do técnico
    technician.GET("/:id/orders", technicianHandler.GetTechnicianOrders)
    
    // Manter rotas existentes...
}
```

### 4.3. Rotas para Upload de Avatar

```go
// routes/user_routes.go

// Adicionar às rotas de usuários existentes

// Rota para upload de avatar
users.POST("/:id/avatar", userHandler.UploadUserAvatar)

// Rota específica para o usuário atual
api.POST("/user/avatar", func(c *gin.Context) {
    // Redirecionar para a rota de upload com o ID do usuário logado
    userID := c.GetUint("userID")
    c.Params = append(c.Params, gin.Param{Key: "id", Value: fmt.Sprintf("%d", userID)})
    userHandler.UploadUserAvatar(c)
})
```

### 4.4. Configuração para Servir Arquivos Estáticos

```go
// main.go

// Configurar diretório de uploads para ser acessível publicamente
router.Static("/uploads", "./uploads")
```

### 4.5. Middleware para Verificar Troca de Senha

```go
// middleware/password_change_middleware.go

package middleware

import (
    "net/http"
    "strings"

    "github.com/gin-gonic/gin"
)

// PasswordChangeMiddleware verifica se o usuário precisa trocar a senha
func PasswordChangeMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // Verificar se é uma rota de autenticação ou troca de senha
        path := c.Request.URL.Path
        if path == "/api/auth/login" || path == "/api/auth/change-password" || 
           path == "/change-password" || strings.HasPrefix(path, "/static/") ||
           strings.HasPrefix(path, "/uploads/") {
            c.Next()
            return
        }
        
        // Verificar se o usuário está autenticado
        userID, exists := c.Get("userID")
        if !exists {
            c.Next()
            return
        }
        
        // Verificar se o usuário precisa trocar a senha
        forceChange, exists := c.Get("forcePasswordChange")
        if exists && forceChange.(bool) {
            // Se for uma requisição API
            if strings.HasPrefix(path, "/api/") {
                c.JSON(http.StatusForbidden, gin.H{
                    "error": "Troca de senha obrigatória",
                    "redirect": "/change-password",
                })
                c.Abort()
                return
            }
            
            // Se for uma requisição de página
            c.Redirect(http.StatusFound, "/change-password")
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

## 5. Integração no Main

```go
// main.go

// Inicializar serviços
uploadService := services.NewFileUploadService("./uploads")
emailService := services.NewSMTPEmailService(
    config.SMTPHost,
    config.SMTPPort,
    config.SMTPUsername,
    config.SMTPPassword,
    config.SMTPFrom,
)

// Inicializar handlers
serviceProviderHandler := handlers.NewServiceProviderHandler(
    serviceProviderService,
    userService,
    uploadService,
    emailService,
)

// Configurar middleware de troca de senha
router.Use(middleware.PasswordChangeMiddleware())

// Configurar rotas
routes.SetupServiceProviderRoutes(router, serviceProviderHandler, middleware.AuthMiddleware())
routes.SetupTechnicianRoutes(router, technicianHandler, middleware.AuthMiddleware())

// Configurar diretório de uploads
router.Static("/uploads", "./uploads")
```

## 6. Verificação da Implementação

Após implementar os handlers e rotas, verifique se:

1. Os handlers estão corretamente implementados
2. As rotas estão corretamente configuradas
3. O middleware de troca de senha funciona corretamente
4. Os arquivos estáticos são servidos corretamente

## 7. Solução de Problemas Comuns

### 7.1. Erro 404 (Not Found)

Se ocorrer um erro 404 ao acessar uma rota, verifique se:

1. A rota está corretamente configurada
2. O handler está corretamente implementado
3. O grupo de rotas está corretamente definido

### 7.2. Erro 403 (Forbidden)

Se ocorrer um erro 403 ao acessar uma rota, verifique se:

1. O usuário tem permissão para acessar a rota
2. O middleware de autenticação está corretamente configurado
3. O middleware de troca de senha está corretamente configurado

### 7.3. Erro ao Fazer Upload de Arquivo

Se ocorrer um erro ao fazer upload de arquivo, verifique se:

1. O diretório de uploads existe e tem permissões corretas
2. O formulário está corretamente configurado com `enctype="multipart/form-data"`
3. O nome do campo no formulário corresponde ao nome esperado pelo handler

## 8. Próximos Passos

Após concluir a implementação dos handlers e rotas, os próximos passos são:

1. Atualizar o sistema de permissões
2. Implementar a interface de usuário
3. Implementar o sistema de upload de imagens
4. Realizar testes e verificação
5. Documentar para usuários finais
