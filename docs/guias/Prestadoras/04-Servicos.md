# Serviços

## 1. Visão Geral

Esta etapa envolve a implementação dos serviços necessários para o sistema de prestadoras e técnicos. Os serviços encapsulam a lógica de negócio e utilizam os repositórios para acessar os dados. Isso inclui:

1. Expansão do serviço de prestadores
2. Modificação do serviço de técnicos
3. Criação de serviço para upload de arquivos
4. Criação de serviço para envio de emails

## 2. Expansão do Serviço de Prestadores

### 2.1. Funcionalidades a Implementar

O serviço de prestadores (`ServiceProviderService`) precisa ser expandido para incluir as seguintes funcionalidades:

- **Gerenciamento de Técnicos**:
  - Listar técnicos de um prestador
  - Adicionar técnico a um prestador com geração de senha temporária
  - Remover técnico de um prestador

- **Gerenciamento de Gestores**:
  - Listar gestores de um prestador
  - Adicionar gestor a um prestador
  - Remover gestor de um prestador

- **Gerenciamento de Logomarca**:
  - Atualizar logomarca de um prestador

- **Verificação de Permissões**:
  - Verificar se um usuário é gestor de um prestador

### 2.2. Dependências

O serviço de prestadores depende de:

- Repositório de prestadores
- Repositório de usuários
- Repositório de gestores de prestadores
- Serviço de email
- Serviço de upload de arquivos

### 2.3. Fluxo de Cadastro de Técnicos

O fluxo de cadastro de técnicos inclui:

1. Verificar se o email já está em uso
2. Gerar senha temporária aleatória
3. Criar hash da senha para armazenamento
4. Criar novo usuário com perfil "tecnico" e vínculo com o prestador
5. Definir flag `ForcePasswordChange` como `true`
6. Salvar usuário no banco de dados
7. Enviar email com credenciais temporárias
8. Retornar dados do usuário e senha temporária

## 3. Modificação do Serviço de Técnicos

O serviço de técnicos precisa ser modificado para incluir métodos para obter o prestador de um técnico e as ordens designadas a ele.


## 4. Criação de Serviço para Upload de Arquivos

### 4.1. Funcionalidades a Implementar

O serviço de upload de arquivos (`FileUploadService`) gerencia o upload de imagens para logomarcas de prestadoras e avatares de técnicos, incluindo:

- **Upload de Arquivos**:
  - Validar tipo de arquivo (apenas imagens)
  - Gerar nome único para o arquivo
  - Salvar arquivo no diretório apropriado
  - Retornar URL relativa para armazenar no banco de dados

- **Exclusão de Arquivos**:
  - Remover arquivo existente quando substituído

### 4.2. Tipos de Upload

O serviço suporta dois tipos de upload:

- **Avatares**: Para fotos de perfil de técnicos
- **Logos**: Para logomarcas de prestadoras

### 4.3. Validações

O serviço inclui validações para:

- Tipo de arquivo (apenas jpg, jpeg, png, gif)
- Tamanho máximo (1MB para avatares, 2MB para logos)

## 5. Criação de Serviço para Envio de Emails

### 5.1. Funcionalidades a Implementar

O serviço de envio de emails (`EmailService`) gerencia o envio de emails para técnicos com credenciais temporárias, incluindo:

- **Envio de Convite**:
  - Criar template de email com instruções
  - Incluir credenciais temporárias
  - Enviar email para o técnico

### 5.2. Template de Email

O email de convite inclui:

- Saudação personalizada
- Credenciais temporárias (email e senha)
- Instruções para primeiro acesso
- Link para o sistema
- Informações de contato para suporte

## 6. Verificação da Implementação

Após implementar os serviços, verifique se:

1. Os serviços encapsulam corretamente a lógica de negócio
2. Os serviços utilizam corretamente os repositórios
3. Os métodos dos serviços funcionam corretamente
4. O serviço de upload de arquivos funciona corretamente
5. O serviço de envio de emails funciona corretamente

## 7. Solução de Problemas Comuns

### 7.1. Erro ao Enviar Email

Se ocorrer um erro ao enviar email, verifique se:

1. As configurações SMTP estão corretas
2. O servidor SMTP está acessível
3. As credenciais SMTP estão corretas
4. O formato do email está correto

### 7.2. Erro ao Fazer Upload de Arquivo

Se ocorrer um erro ao fazer upload de arquivo, verifique se:

1. O diretório de uploads existe e tem permissões corretas
2. O arquivo é uma imagem válida
3. O tamanho do arquivo não excede o limite

## 8. Próximos Passos

Após concluir a implementação dos serviços, os próximos passos são:

1. Implementar os handlers e rotas
2. Atualizar o sistema de permissões
3. Implementar a interface de usuário
4. Implementar o sistema de upload de imagens
5. Realizar testes e verificação
6. Documentar para usuários finais
