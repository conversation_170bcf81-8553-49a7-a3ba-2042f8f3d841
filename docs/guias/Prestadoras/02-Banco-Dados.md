# Alterações no Banco de Dados

## 1. Visão Geral das Alterações

Para implementar o novo modelo de prestadoras e técnicos, é necessário realizar as seguintes alterações no banco de dados:

1. Modificar a tabela `service_providers` para representar empresas prestadoras
2. Adicionar campo `service_provider_id` à tabela `users` para vincular técnicos a prestadoras
3. Criar tabela `service_provider_managers` para gestores de prestadoras

## 2. Migrações do Banco de Dados

### 2.1. Modificação da Tabela `service_providers`

A tabela `service_providers` precisa ser modificada para representar empresas prestadoras, com campos para informações empresariais completas e logomarca.

```sql
-- Migração para modificar a tabela service_providers
ALTER TABLE service_providers
ADD COLUMN IF NOT EXISTS company_name VARCHAR(255),
ADD COLUMN IF NOT EXISTS cnpj VARCHAR(20),
ADD COLUMN IF NOT EXISTS address VARCHAR(255),
ADD COLUMN IF NOT EXISTS city VARCHAR(100),
ADD COLUMN IF NOT EXISTS state VARCHAR(50),
ADD COLUMN IF NOT EXISTS zip_code VARCHAR(20),
ADD COLUMN IF NOT EXISTS contact_name VARCHAR(100),
ADD COLUMN IF NOT EXISTS contact_email VARCHAR(100),
ADD COLUMN IF NOT EXISTS contact_phone VARCHAR(20),
ADD COLUMN IF NOT EXISTS specialties TEXT,
ADD COLUMN IF NOT EXISTS area_of_expertise VARCHAR(255),
ADD COLUMN IF NOT EXISTS average_rating FLOAT DEFAULT 0,
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active',
ADD COLUMN IF NOT EXISTS logo_url VARCHAR(255);
```

### 2.2. Modificação da Tabela `users`

A tabela `users` precisa ser modificada para incluir um campo `service_provider_id` que vincula técnicos a prestadoras.

```sql
-- Migração para adicionar campo service_provider_id à tabela users
ALTER TABLE users
ADD COLUMN IF NOT EXISTS service_provider_id INTEGER,
ADD CONSTRAINT fk_service_provider
    FOREIGN KEY (service_provider_id)
    REFERENCES service_providers(id)
    ON DELETE SET NULL;

-- Criar índice para otimizar consultas
CREATE INDEX IF NOT EXISTS idx_users_service_provider_id
ON users(service_provider_id);
```

### 2.3. Criação da Tabela `service_provider_managers`

A tabela `service_provider_managers` armazena informações sobre os gestores de prestadoras, vinculando usuários a prestadoras com um papel específico.

```sql
-- Migração para criar a tabela service_provider_managers
CREATE TABLE IF NOT EXISTS service_provider_managers (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    service_provider_id INTEGER NOT NULL,
    role VARCHAR(50) NOT NULL, -- 'owner', 'manager', 'admin'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (service_provider_id) REFERENCES service_providers(id) ON DELETE CASCADE,
    UNIQUE(user_id, service_provider_id)
);

-- Criar índices para otimizar consultas
CREATE INDEX IF NOT EXISTS idx_service_provider_managers_user_id
ON service_provider_managers(user_id);

CREATE INDEX IF NOT EXISTS idx_service_provider_managers_service_provider_id
ON service_provider_managers(service_provider_id);
```

## 3. Implementação das Migrações

### 3.1. Usando Atlas para Migrações

Se o projeto utiliza Atlas para migrações, crie um arquivo de migração com as alterações acima.

1. Crie um novo arquivo de migração:

```bash
atlas migrate new modify_service_providers_and_users
```

2. Edite o arquivo de migração gerado com as alterações SQL acima.

3. Aplique a migração:

```bash
atlas migrate apply
```

### 3.2. Usando SQL Direto

Se o projeto não utiliza Atlas, você pode executar as alterações SQL diretamente no banco de dados.

1. Conecte-se ao banco de dados:

```bash
psql -U <username> -d <database_name>
```

2. Execute as alterações SQL acima.

## 4. Verificação das Alterações

Após aplicar as migrações, verifique se as alterações foram aplicadas corretamente:

### 4.1. Verificar Tabela `service_providers`

```sql
-- Verificar se os novos campos foram adicionados à tabela service_providers
SELECT column_name, data_type
FROM information_schema.columns
WHERE table_name = 'service_providers'
ORDER BY column_name;
```

### 4.2. Verificar Tabela `users`

```sql
-- Verificar se o campo service_provider_id foi adicionado à tabela users
SELECT column_name, data_type
FROM information_schema.columns
WHERE table_name = 'users' AND column_name = 'service_provider_id';

-- Verificar se a chave estrangeira foi criada
SELECT
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM
    information_schema.table_constraints AS tc
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = 'users'
    AND kcu.column_name = 'service_provider_id';
```

### 4.3. Verificar Tabela `service_provider_managers`

```sql
-- Verificar se a tabela service_provider_managers foi criada
SELECT table_name
FROM information_schema.tables
WHERE table_name = 'service_provider_managers';

-- Verificar se os campos foram criados corretamente
SELECT column_name, data_type
FROM information_schema.columns
WHERE table_name = 'service_provider_managers'
ORDER BY column_name;
```

## 5. Solução de Problemas Comuns

### 5.1. Erro ao Adicionar Chave Estrangeira

Se ocorrer um erro ao adicionar a chave estrangeira `service_provider_id` à tabela `users`, verifique se:

1. A tabela `service_providers` existe
2. A coluna `id` na tabela `service_providers` é do mesmo tipo que `service_provider_id` na tabela `users`
3. Não há valores em `service_provider_id` que não existem em `service_providers.id`

### 5.2. Erro ao Criar Tabela `service_provider_managers`

Se ocorrer um erro ao criar a tabela `service_provider_managers`, verifique se:

1. As tabelas `users` e `service_providers` existem
2. As colunas `id` nas tabelas `users` e `service_providers` são do tipo correto
3. Não há outra tabela com o mesmo nome

### 5.3. Erro ao Adicionar Colunas à Tabela `service_providers`

Se ocorrer um erro ao adicionar colunas à tabela `service_providers`, verifique se:

1. A tabela `service_providers` existe
2. As colunas que você está tentando adicionar não existem
3. Os tipos de dados são compatíveis com o banco de dados PostgreSQL

## 6. Próximos Passos

Após concluir as alterações no banco de dados, os próximos passos são:

1. Implementar os modelos e repositórios
2. Implementar os serviços
3. Implementar os handlers e rotas
4. Atualizar o sistema de permissões
5. Implementar a interface de usuário
6. Implementar o sistema de upload de imagens
7. Realizar testes e verificação
8. Documentar para usuários finais
