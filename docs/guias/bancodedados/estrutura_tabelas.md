# Estrutura Detalhada das Tabelas do Banco de Dados

Este documento descreve a estrutura detalhada de cada tabela no banco de dados `tradicao_ent`.

## Tabela: branches (Filiais)

A tabela `branches` armazena informações sobre as filiais/postos.

### Colunas

| Coluna | Tipo | Descrição | Obrigatório | Padrão |
|--------|------|-----------|-------------|--------|
| id | SERIAL | Identificador único da filial | Sim | Auto incremento |
| name | VARCHAR(100) | Nome da filial | Sim | - |
| code | VARCHAR(20) | Código único da filial | Sim | - |
| address | VARCHAR(255) | Endereço completo | Não | NULL |
| city | VARCHAR(100) | Cidade | Não | NULL |
| state | VARCHAR(2) | Estado (sigla) | Não | NULL |
| zip_code | VARCHAR(20) | CEP | Não | NULL |
| phone | VARCHAR(20) | Telefone de contato | Não | NULL |
| email | VARCHAR(100) | Email de contato | Não | NULL |
| type | VARCHAR(20) | Tipo de filial (urban, rural, etc.) | Não | 'urban' |
| is_active | BOOLEAN | Indica se a filial está ativa | Não | TRUE |
| manager_id | INTEGER | ID do gerente da filial (referência a users.id) | Não | NULL |
| latitude | DECIMAL(10,8) | Latitude para geolocalização | Não | NULL |
| longitude | DECIMAL(11,8) | Longitude para geolocalização | Não | NULL |
| opening_hours | VARCHAR(255) | Horário de funcionamento | Não | NULL |
| created_at | TIMESTAMP | Data de criação do registro | Não | CURRENT_TIMESTAMP |
| updated_at | TIMESTAMP | Data da última atualização | Não | CURRENT_TIMESTAMP |
| deleted_at | TIMESTAMP | Data de exclusão lógica | Não | NULL |

### Índices

| Nome | Tipo | Colunas | Descrição |
|------|------|---------|-----------|
| branches_pkey | PRIMARY KEY | id | Chave primária |
| branches_code_key | UNIQUE | code | Garante código único |
| idx_branches_city_state | INDEX | city, state | Otimiza consultas por localização |
| idx_branches_is_active | INDEX | is_active | Otimiza consultas por status |
| idx_branches_manager_id | INDEX | manager_id | Otimiza consultas por gerente |

### Chaves Estrangeiras

| Nome | Coluna | Referência | Descrição |
|------|--------|------------|-----------|
| fk_branches_manager | manager_id | users(id) | Referência ao gerente da filial |

## Tabela: users (Usuários)

A tabela `users` armazena informações sobre os usuários do sistema, incluindo técnicos e prestadores de serviço.

### Colunas

| Coluna | Tipo | Descrição | Obrigatório | Padrão |
|--------|------|-----------|-------------|--------|
| id | SERIAL | Identificador único do usuário | Sim | Auto incremento |
| name | VARCHAR(100) | Nome completo do usuário | Sim | - |
| email | VARCHAR(100) | Email do usuário (único) | Sim | - |
| password | VARCHAR(255) | Senha criptografada | Sim | - |
| role | VARCHAR(50) | Função/papel do usuário (admin, manager, technician, provider, etc.) | Sim | - |
| branch_id | INTEGER | ID da filial associada (referência a branches.id) | Não | NULL |
| failed_attempts | INTEGER | Número de tentativas falhas de login | Não | 0 |
| blocked | BOOLEAN | Indica se o usuário está bloqueado | Não | FALSE |
| totp_secret | VARCHAR(255) | Segredo para autenticação de dois fatores | Não | '' |
| totp_enabled | BOOLEAN | Indica se a autenticação de dois fatores está ativada | Não | FALSE |
| last_password_change | TIMESTAMP | Data da última alteração de senha | Não | NULL |
| force_password_change | BOOLEAN | Indica se o usuário deve alterar a senha no próximo login | Não | FALSE |
| created_at | TIMESTAMP | Data de criação do registro | Não | CURRENT_TIMESTAMP |
| updated_at | TIMESTAMP | Data da última atualização | Não | CURRENT_TIMESTAMP |
| deleted_at | TIMESTAMP | Data de exclusão lógica | Não | NULL |

### Índices

| Nome | Tipo | Colunas | Descrição |
|------|------|---------|-----------|
| users_pkey | PRIMARY KEY | id | Chave primária |
| idx_users_email | UNIQUE | email | Garante email único |
| idx_users_role | INDEX | role | Otimiza consultas por função |
| idx_users_branch_id | INDEX | branch_id | Otimiza consultas por filial |

### Chaves Estrangeiras

| Nome | Coluna | Referência | Descrição |
|------|--------|------------|-----------|
| fk_users_branch | branch_id | branches(id) | Referência à filial do usuário |

## Tabela: equipment (Equipamentos)

A tabela `equipment` armazena informações sobre os equipamentos.

### Colunas

| Coluna | Tipo | Descrição | Obrigatório | Padrão |
|--------|------|-----------|-------------|--------|
| id | SERIAL | Identificador único do equipamento | Sim | Auto incremento |
| name | VARCHAR(100) | Nome do equipamento | Sim | - |
| serial_number | VARCHAR(100) | Número de série (único) | Não | NULL |
| model | VARCHAR(100) | Modelo do equipamento | Não | NULL |
| brand | VARCHAR(100) | Marca do equipamento | Não | NULL |
| type | VARCHAR(50) | Tipo do equipamento | Sim | - |
| installation_date | TIMESTAMP | Data de instalação | Não | NULL |
| last_maintenance | TIMESTAMP | Data da última manutenção | Não | NULL |
| last_preventive | TIMESTAMP | Data da última manutenção preventiva | Não | NULL |
| next_preventive | TIMESTAMP | Data da próxima manutenção preventiva | Não | NULL |
| status | VARCHAR(20) | Status do equipamento (active, inactive, maintenance) | Não | 'active' |
| location | VARCHAR(255) | Localização dentro da filial | Não | NULL |
| notes | TEXT | Observações sobre o equipamento | Não | NULL |
| branch_id | INTEGER | ID da filial associada (referência a branches.id) | Não | NULL |
| created_at | TIMESTAMP | Data de criação do registro | Não | CURRENT_TIMESTAMP |
| updated_at | TIMESTAMP | Data da última atualização | Não | CURRENT_TIMESTAMP |
| deleted_at | TIMESTAMP | Data de exclusão lógica | Não | NULL |

### Índices

| Nome | Tipo | Colunas | Descrição |
|------|------|---------|-----------|
| equipment_pkey | PRIMARY KEY | id | Chave primária |
| idx_equipment_serial_number | UNIQUE | serial_number | Garante número de série único |
| idx_equipment_branch_id | INDEX | branch_id | Otimiza consultas por filial |
| idx_equipment_type | INDEX | type | Otimiza consultas por tipo |
| idx_equipment_status | INDEX | status | Otimiza consultas por status |

### Chaves Estrangeiras

| Nome | Coluna | Referência | Descrição |
|------|--------|------------|-----------|
| fk_equipment_branch | branch_id | branches(id) | Referência à filial do equipamento |

## Tabela: maintenance_orders (Ordens de Manutenção)

A tabela `maintenance_orders` armazena informações sobre as ordens de manutenção.

### Colunas

| Coluna | Tipo | Descrição | Obrigatório | Padrão |
|--------|------|-----------|-------------|--------|
| id | SERIAL | Identificador único da ordem | Sim | Auto incremento |
| title | VARCHAR(100) | Título da ordem | Sim | - |
| description | TEXT | Descrição detalhada | Não | NULL |
| status | VARCHAR(20) | Status da ordem (pending, approved, in_progress, completed, cancelled) | Não | 'pending' |
| priority | VARCHAR(20) | Prioridade (low, medium, high, critical) | Não | 'medium' |
| branch_id | INTEGER | ID da filial associada (referência a branches.id) | Não | NULL |
| equipment_id | INTEGER | ID do equipamento (referência a equipment.id) | Não | NULL |
| requester_id | INTEGER | ID do solicitante (referência a users.id) | Não | NULL |
| approver_id | INTEGER | ID do aprovador (referência a users.id) | Não | NULL |
| technician_id | INTEGER | ID do técnico responsável (referência a users.id) | Não | NULL |
| cancellation_reason | TEXT | Motivo do cancelamento | Não | NULL |
| start_date | TIMESTAMP | Data de início | Não | NULL |
| end_date | TIMESTAMP | Data de conclusão | Não | NULL |
| estimated_time | INTEGER | Tempo estimado (em minutos) | Não | NULL |
| actual_time | INTEGER | Tempo real (em minutos) | Não | NULL |
| created_at | TIMESTAMP | Data de criação do registro | Não | CURRENT_TIMESTAMP |
| updated_at | TIMESTAMP | Data da última atualização | Não | CURRENT_TIMESTAMP |
| deleted_at | TIMESTAMP | Data de exclusão lógica | Não | NULL |

### Índices

| Nome | Tipo | Colunas | Descrição |
|------|------|---------|-----------|
| maintenance_orders_pkey | PRIMARY KEY | id | Chave primária |
| idx_maintenance_orders_branch_id | INDEX | branch_id | Otimiza consultas por filial |
| idx_maintenance_orders_equipment_id | INDEX | equipment_id | Otimiza consultas por equipamento |
| idx_maintenance_orders_status | INDEX | status | Otimiza consultas por status |
| idx_maintenance_orders_priority | INDEX | priority | Otimiza consultas por prioridade |

### Chaves Estrangeiras

| Nome | Coluna | Referência | Descrição |
|------|--------|------------|-----------|
| fk_maintenance_orders_branch | branch_id | branches(id) | Referência à filial |
| fk_maintenance_orders_equipment | equipment_id | equipment(id) | Referência ao equipamento |
| fk_maintenance_orders_requester | requester_id | users(id) | Referência ao solicitante |
| fk_maintenance_orders_approver | approver_id | users(id) | Referência ao aprovador |
| fk_maintenance_orders_technician | technician_id | users(id) | Referência ao técnico |

## Tabela: schema_versions (Versões do Schema)

A tabela `schema_versions` é usada pelo Atlas para controlar as versões do schema do banco de dados.

### Colunas

| Coluna | Tipo | Descrição | Obrigatório | Padrão |
|--------|------|-----------|-------------|--------|
| version | VARCHAR(255) | Versão da migração | Sim | - |
| description | VARCHAR(255) | Descrição da migração | Sim | - |
| type | VARCHAR(20) | Tipo da migração | Sim | - |
| applied_at | TIMESTAMP | Data de aplicação | Não | CURRENT_TIMESTAMP |

### Índices

| Nome | Tipo | Colunas | Descrição |
|------|------|---------|-----------|
| schema_versions_pkey | PRIMARY KEY | version | Chave primária |
