# Migração de Filiais para o Banco de Dados ENT

Este documento descreve o processo de migração das filiais para o banco de dados `tradicao_ent` utilizando a estrutura ENT+Atlas.

## Estrutura da Tabela

A tabela `branches` no banco de dados `tradicao_ent` possui a seguinte estrutura:

```sql
CREATE TABLE branches (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) NOT NULL UNIQUE,
    address VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(2),
    zip_code VARCHAR(20),
    phone VARCHAR(20),
    email VARCHAR(100),
    type VARCHAR(20) DEFAULT 'urban',
    is_active BOOLEAN DEFAULT TRUE,
    manager_id INTEGER,
    latitude NUMERIC(10,8),
    longitude NUMERIC(11,8),
    opening_hours VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);
```

## Regras de Migração

Para a migração das filiais, foram segu<PERSON> as seguintes regras:

1. O ID do registro inicia no 100, onde o 100 é a filial Matriz.
2. O ID 101 é a filial 01, ID 102 é a Filial 02, e assim por diante, seguindo a mesma dezena do ID idêntico ao número do nome da Filial.
3. Cada filial recebeu um código único no formato "MATRIZ" para a matriz e "FILxx" para as filiais, onde xx é o número da filial.

## Processo de Migração

A migração foi realizada através do script SQL `migrations/insert_filiais.sql`, que contém os comandos INSERT para cada filial com seus respectivos dados.

Exemplo de comando INSERT utilizado:

```sql
-- Matriz
INSERT INTO branches (id, name, code, address, city, state, phone, email, is_active)
VALUES (100, 'Matriz', 'MATRIZ', 'Rua Bento Gonçalves 2410', 'Marau', 'RS', '(54)3196-8351', '<EMAIL>', true);
```

Após a inserção de todos os registros, a sequência `branches_id_seq` foi configurada para continuar a partir do último ID inserido:

```sql
SELECT setval('branches_id_seq', (SELECT MAX(id) FROM branches), true);
```

## Resultado da Migração

Foram migradas 77 filiais para o banco de dados `tradicao_ent`. A sequência `branches_id_seq` foi configurada para iniciar a partir do ID 177, garantindo que novos registros não conflitem com os existentes.

## Verificação

Para verificar os dados migrados, você pode executar as seguintes consultas:

```sql
-- Listar as primeiras 10 filiais
SELECT id, name, code, city, state FROM branches ORDER BY id LIMIT 10;

-- Contar o total de filiais
SELECT COUNT(*) FROM branches;

-- Verificar o valor atual da sequência
SELECT last_value FROM branches_id_seq;
```

## Relacionamentos

A tabela `branches` possui os seguintes relacionamentos:

1. **Usuários**: Cada filial pode ter um gerente (manager_id referencia users.id)
2. **Equipamentos**: Equipamentos estão associados a filiais (equipment.branch_id referencia branches.id)
3. **Ordens de Manutenção**: Ordens estão associadas a filiais (maintenance_orders.branch_id referencia branches.id)

## Próximos Passos

Após a migração das filiais, os próximos passos incluem:

1. Migração dos usuários associados às filiais
2. Migração dos equipamentos associados às filiais
3. Migração das ordens de manutenção associadas às filiais
4. Configuração dos relacionamentos entre as entidades
