# Documentação do Banco de Dados

Esta pasta contém a documentação detalhada do banco de dados do sistema de gerenciamento de manutenção de equipamentos em postos/filiais.

## Índice

1. [Estrutura Detalhada das Tabelas](estrutura_tabelas.md) - Descrição detalhada de cada tabela, suas colunas, índices e chaves estrangeiras.

2. [Relacionamentos do Banco de Dados](relacionamentos.md) - Documentação dos relacionamentos entre as tabelas, incluindo diagramas e exemplos de consultas.

3. [Migração para ENT e Atlas](migracao_ent_atlas.md) - Descrição do processo de migração do banco de dados para o ENT e Atlas.

## Visão Geral do Banco de Dados

O banco de dados `tradicao_ent` é um banco de dados PostgreSQL que armazena informações sobre filiais, usuários, equipamentos e ordens de manutenção. Ele foi projetado para suportar o sistema de gerenciamento de manutenção de equipamentos em postos/filiais.

### Tabelas Principais

- **branches** (Filiais): Armazena informações sobre as filiais/postos.
- **users** (Usuários): Armazena informações sobre os usuários do sistema, incluindo técnicos e prestadores de serviço.
- **equipment** (Equipamentos): Armazena informações sobre os equipamentos.
- **maintenance_orders** (Ordens de Manutenção): Armazena informações sobre as ordens de manutenção.

### Relacionamentos Principais

- Filiais têm relacionamentos com equipamentos, usuários e ordens de manutenção.
- Usuários podem ser técnicos/prestadores associados a filiais.
- Equipamentos pertencem a filiais.
- Ordens de manutenção estão associadas a filiais, equipamentos e usuários.

## Tecnologias Utilizadas

- **PostgreSQL**: Sistema de gerenciamento de banco de dados relacional.
- **ENT**: ORM (Object-Relational Mapping) para Go que gera código fortemente tipado.
- **Atlas**: Ferramenta de migração de banco de dados que gerencia as alterações no esquema.

## Convenções de Nomenclatura

- Todas as tabelas e colunas estão em inglês.
- Nomes de tabelas estão no plural (por exemplo, `users` em vez de `user`).
- Chaves primárias são chamadas de `id`.
- Chaves estrangeiras seguem o padrão `[tabela_referenciada]_id` (por exemplo, `branch_id`).
- Índices seguem o padrão `idx_[tabela]_[coluna(s)]` (por exemplo, `idx_users_email`).
- Chaves estrangeiras seguem o padrão `fk_[tabela]_[referência]` (por exemplo, `fk_users_branch`).

## Manutenção do Banco de Dados

Para manter o banco de dados:

1. **Backups**: Execute regularmente o script `scripts/backup_databases.sh` para fazer backups do banco de dados.

2. **Migrações**: Use o Atlas para gerenciar alterações no esquema do banco de dados. Os arquivos de migração estão em `migrations/atlas/`.

3. **Geração de Código ENT**: Após alterações nos esquemas do ENT, gere o código usando `go generate ./ent`.

## Consultas Comuns

Exemplos de consultas comuns podem ser encontrados no documento [Relacionamentos do Banco de Dados](relacionamentos.md).
