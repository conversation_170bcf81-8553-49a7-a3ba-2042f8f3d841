# Regras de Desenvolvimento do Projeto

Este documento estabelece regras e boas práticas a serem seguidas no desenvolvimento do projeto para evitar erros e garantir a qualidade do código.

## 1. Regras Gerais de Desenvolvimento

### 1.1 Análise Prévia
- Antes de criar qualquer arquivo novo, realize uma análise detalhada do projeto para verificar se já existe uma solução implementada.
- Verifique alternativas para resolver o problema sem criar novos arquivos.
- Evite absolutamente duplicações de nomes de arquivos em qualquer parte do projeto.

### 1.2 Estrutura de Arquivos
- Respeite a estrutura de diretórios do projeto:
  ```
  web/
  ├── static/
  │   ├── css/      # Estilos CSS
  │   ├── js/       # Scripts JavaScript
  │   └── images/   # Imagens e ícones
  └── templates/
      ├── layouts/  # Templates base e componentes
      └── [secao]/  # Templates específicos por seção
  ```
- Mantenha CSS e JavaScript em arquivos separados, nunca embutidos no HTML.
- Nomeie arquivos de acordo com sua função e contexto.

### 1.3 Convenções de Nomenclatura
- Utilize nomes descritivos e significativos para arquivos, funções e variáveis.
- Mantenha consistência com as convenções já adotadas no projeto.
- Para o banco de dados:
  - Todas as tabelas e colunas estão em inglês.
  - Nomes de tabelas estão no plural (por exemplo, `users` em vez de `user`).
  - Chaves primárias são chamadas de `id`.
  - Chaves estrangeiras seguem o padrão `[tabela_referenciada]_id`.
  - Índices seguem o padrão `idx_[tabela]_[coluna(s)]`.

### 1.4 Verificação antes de Commit
- Verifique 4 vezes o código antes de fazer commit.
- Confirme se as alterações não quebram funcionalidades existentes.
- Valide se o código segue os padrões do projeto.
- Certifique-se de que não há duplicações.

## 2. Regras para Templates HTML/CSS

### 2.1 Estrutura de Templates
- Todo template deve ser definido com um nome único usando `{{ define "nome_do_template" }}...{{ end }}`.
- O nome usado na renderização (`c.HTML(http.StatusOK, "nome_do_template", data)`) deve corresponder exatamente ao nome definido no template.
- Cada template completo deve incluir:
  ```html
  {{ define "secao/pagina.html" }}
  <!DOCTYPE html>
  <html lang="pt-br">
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Título - Rede Tradição Shell</title>
      <!-- CSS -->
  </head>
  <body>
      {{ template "sidebar" . }}
      <div class="content-with-sidebar">
          <div class="main-content">
              <!-- Conteúdo -->
          </div>
      </div>
      <!-- Scripts -->
  </body>
  </html>
  {{ end }}
  ```

### 2.2 Design System
- Utilize o Design System Shell com suas cores e tipografia:
  - Cores principais: `--shell-red` (#ED1C24), `--shell-yellow` (#FDB813), `--shell-dark` (#333333)
  - Fontes: 'Rajdhani' (títulos) e 'Share Tech Mono' (elementos técnicos)
- Inclua sempre o sidebar em todas as páginas usando `{{ template "sidebar" . }}`.
- Siga a estrutura de layout com `div.content-with-sidebar > div.main-content`.
- Use componentes padrão (botões, cards, tabelas) conforme definidos no Design System.

### 2.3 Acessibilidade e Responsividade
- Garanta que todos os elementos HTML tenham estrutura semântica adequada.
- Use atributos ARIA quando necessário.
- Forneça textos alternativos para imagens.
- Implemente navegação por teclado.
- Siga a abordagem "Mobile first".
- Use unidades relativas (rem, em, %).
- Teste em diferentes breakpoints: xs (0px), sm (576px), md (768px), lg (992px), xl (1200px).

## 3. Regras de Segurança Frontend

### 3.1 Proteção contra Vulnerabilidades
- Sempre sanitize inputs de usuário para prevenir XSS.
- Implemente proteção CSRF em todos os formulários.
- Configure Content Security Policy adequadamente.
- Valide e sanitize todos os dados antes do uso.

### 3.2 Gestão de Estados e Erros
- Implemente tratamento de erros em todas as requisições.
- Use classes para organização de código JavaScript.
- Mantenha o código modular e fácil de manter.
- Implemente cache quando apropriado para melhorar performance.

## 4. Regras para Banco de Dados

### 4.1 Estrutura e Migrações
- Use ENT para definição de modelos e ORM.
- Use Atlas para gerenciar migrações de banco de dados.
- Siga a estrutura definida nos documentos de referência.
- Mantenha a integridade referencial entre as tabelas.

### 4.2 Consultas e Performance
- Crie índices para otimizar consultas frequentes.
- Evite consultas N+1 usando relações apropriadas.
- Implemente soft delete (coluna `deleted_at`) em vez de exclusão permanente.
- Mantenha colunas de auditoria (`created_at`, `updated_at`).

### 4.3 Modificações no Banco
- Documente todas as alterações no esquema do banco de dados.
- Crie migrações para todas as alterações.
- Faça backup antes de modificações significativas.
- Teste as migrações em ambiente de desenvolvimento antes de aplicar em produção.

## 5. Boas Práticas de Código

### 5.1 Organização do Código
- Mantenha arquivos com menos de 400-500 linhas; refatore caso contrário.
- Divida funções longas em funções menores e mais específicas.
- Documente funções e métodos complexos.
- Prefira soluções simples e diretas.

### 5.2 Tratamento de Erros
- Implemente logging adequado para facilitar a depuração.
- Captura e tratamento apropriado de erros em todas as camadas.
- Em caso de erros persistentes, analise a causa raiz antes de implementar correções.
- Adicione logs estratégicos para rastrear o fluxo de dados.

### 5.3 Testes
- Escreva testes para funcionalidades críticas.
- Inclua testes unitários e de integração.
- Verifique a acessibilidade e responsividade das interfaces.
- Teste em diferentes dispositivos e navegadores.

## 6. Checklist antes de Implementar Novas Funcionalidades

- [ ] Verificar se a funcionalidade já existe de alguma forma
- [ ] Analisar a necessidade real da criação de novos arquivos
- [ ] Verificar se a implementação segue o padrão do projeto
- [ ] Confirmar que não há duplicidade de nomes ou funções
- [ ] Validar se o código segue as regras de segurança
- [ ] Testar a funcionalidade em diferentes cenários
- [ ] Verificar compatibilidade com diferentes dispositivos

Ao seguir estas regras e boas práticas, podemos minimizar erros e manter um código de alta qualidade e fácil manutenção. 