# Guia de Acessibilidade e Responsividade

## 1. Acessibilidade (WCAG 2.1)

### 1.1 Estrutura Semântica
```html
<!-- Exemplo de estrutura semântica -->
<header role="banner">
    <nav role="navigation" aria-label="Menu principal">
        <!-- Menu items -->
    </nav>
</header>

<main role="main">
    <article>
        <h1><PERSON><PERSON><PERSON><PERSON></h1>
        <!-- Con<PERSON><PERSON>do -->
    </article>
</main>

<footer role="contentinfo">
    <!-- Rodapé -->
</footer>
```

### 1.2 Atributos ARIA
```html
<!-- Exemplo de uso de ARIA -->
<button 
    aria-label="Fechar modal"
    aria-expanded="false"
    aria-controls="modal-content">
    <i class="fas fa-times"></i>
</button>

<div 
    id="modal-content"
    role="dialog"
    aria-labelledby="modal-title"
    aria-describedby="modal-description">
    <h2 id="modal-title">Título do Modal</h2>
    <p id="modal-description">Descrição do conteúdo</p>
</div>
```

### 1.3 Formulários Acessíveis
```html
<form class="form-shell">
    <div class="form-group">
        <label for="nome" id="nome-label">Nome Completo</label>
        <input 
            type="text" 
            id="nome"
            name="nome"
            aria-required="true"
            aria-describedby="nome-help"
            class="form-control">
        <span id="nome-help" class="help-text">
            Digite seu nome completo como está no documento
        </span>
    </div>
</form>
```

### 1.4 Tabelas Acessíveis
```html
<table class="table-shell" role="grid">
    <caption>Título da Tabela</caption>
    <thead>
        <tr>
            <th scope="col">Nome</th>
            <th scope="col">Email</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td headers="nome">João Silva</td>
            <td headers="email"><EMAIL></td>
        </tr>
    </tbody>
</table>
```

## 2. Responsividade

### 2.1 Breakpoints
```css
/* Breakpoints do Design System */
:root {
    --breakpoint-xs: 0;      /* Mobile first */
    --breakpoint-sm: 576px;  /* Tablets pequenos */
    --breakpoint-md: 768px;  /* Tablets */
    --breakpoint-lg: 992px;  /* Desktops */
    --breakpoint-xl: 1200px; /* Desktops grandes */
}

/* Exemplo de uso */
@media (min-width: var(--breakpoint-md)) {
    .container {
        max-width: 720px;
    }
}
```

### 2.2 Grid System
```html
<div class="row">
    <!-- Colunas responsivas -->
    <div class="col-12 col-md-6 col-lg-4">
        <!-- Conteúdo -->
    </div>
    <div class="col-12 col-md-6 col-lg-4">
        <!-- Conteúdo -->
    </div>
    <div class="col-12 col-md-12 col-lg-4">
        <!-- Conteúdo -->
    </div>
</div>
```

### 2.3 Imagens Responsivas
```html
<!-- Imagem responsiva -->
<img 
    src="imagem.jpg"
    srcset="imagem-small.jpg 300w,
            imagem-medium.jpg 600w,
            imagem-large.jpg 900w"
    sizes="(max-width: 576px) 300px,
           (max-width: 992px) 600px,
           900px"
    alt="Descrição da imagem"
    class="img-fluid">
```

### 2.4 Menu Responsivo
```html
<nav class="navbar-shell">
    <button 
        class="navbar-toggler"
        aria-expanded="false"
        aria-controls="navbar-content">
        <span class="navbar-toggler-icon"></span>
    </button>
    
    <div id="navbar-content" class="navbar-collapse">
        <ul class="navbar-nav">
            <li class="nav-item">
                <a href="#" class="nav-link">Item 1</a>
            </li>
            <!-- Mais itens -->
        </ul>
    </div>
</nav>
```

## 3. Boas Práticas

### 3.1 Acessibilidade
- Usar cores com contraste adequado (WCAG AA)
- Fornecer textos alternativos para imagens
- Garantir navegação por teclado
- Implementar skip links
- Usar landmarks HTML5
- Testar com leitores de tela

### 3.2 Responsividade
- Mobile first
- Usar unidades relativas (rem, em, %)
- Evitar media queries desnecessárias
- Otimizar imagens para diferentes dispositivos
- Testar em diferentes dispositivos
- Considerar performance

## 4. Testes

### 4.1 Testes de Acessibilidade
```javascript
// Exemplo de teste com axe-core
axe.run(document.body, {
    rules: {
        'color-contrast': { enabled: true },
        'image-alt': { enabled: true }
    }
}, function(err, results) {
    if (err) throw err;
    console.log(results);
});
```

### 4.2 Testes de Responsividade
```javascript
// Exemplo de teste com Cypress
describe('Responsividade', () => {
    it('deve ajustar layout em diferentes breakpoints', () => {
        // Mobile
        cy.viewport(375, 667);
        cy.get('.navbar-toggler').should('be.visible');
        
        // Tablet
        cy.viewport(768, 1024);
        cy.get('.navbar-collapse').should('be.visible');
        
        // Desktop
        cy.viewport(1920, 1080);
        cy.get('.navbar-nav').should('have.class', 'flex-row');
    });
});
```

## 5. Ferramentas Recomendadas

### 5.1 Acessibilidade
- WAVE (Web Accessibility Evaluation Tool)
- axe DevTools
- NVDA (leitor de tela)
- Lighthouse

### 5.2 Responsividade
- Chrome DevTools
- Responsively App
- Browser Stack
- Lambda Test

## 6. Checklist de Implementação

### 6.1 Acessibilidade
- [ ] Estrutura semântica HTML5
- [ ] Atributos ARIA quando necessário
- [ ] Contraste de cores adequado
- [ ] Navegação por teclado
- [ ] Textos alternativos
- [ ] Formulários acessíveis
- [ ] Tabelas acessíveis
- [ ] Skip links
- [ ] Testes com leitores de tela

### 6.2 Responsividade
- [ ] Mobile first
- [ ] Breakpoints definidos
- [ ] Grid system implementado
- [ ] Imagens responsivas
- [ ] Menu responsivo
- [ ] Fontes responsivas
- [ ] Testes em diferentes dispositivos
- [ ] Performance otimizada 