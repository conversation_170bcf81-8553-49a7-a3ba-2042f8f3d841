# Documentação do Componente de Transferência de Equipamentos

## 1. Visão Geral

O componente de transferência de equipamentos permite que filiais solicitem e gerenciem a transferência de equipamentos entre si, seguindo o design system Shell e garantindo uma experiência de usuário consistente.

### 1.1 Funcionalidades Principais

- Solicitação de transferência de equipamentos
- Visualização de transferências enviadas e recebidas
- Aprovação/rejeição de transferências
- Notificações para filiais envolvidas
- Histórico de transferências

## 2. Interface do Usuário

### 2.1 Modal de Nova Transferência

```html
<!-- Estrutura do Modal -->
<div class="modal fade" id="modalTransferirEquipamento">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content bg-dark">
            <!-- Cabeçalho -->
            <div class="modal-header border-bottom border-secondary">
                <h5 class="modal-title text-shell-yellow">
                    <i class="fas fa-exchange-alt me-2"></i>
                    Nova Transferência de Equipamento
                </h5>
                <button type="button" class="btn-close btn-close-white"></button>
            </div>
            
            <!-- Corpo -->
            <div class="modal-body">
                <form id="formTransferirEquipamento">
                    <!-- Campos do formulário -->
                </form>
            </div>
            
            <!-- Rodapé -->
            <div class="modal-footer border-top border-secondary">
                <button type="button" class="btn-shell">Cancelar</button>
                <button type="submit" class="btn-shell-yellow">Solicitar</button>
            </div>
        </div>
    </div>
</div>
```

### 2.2 Campos do Formulário

1. **Equipamento**
   - Select com lista de equipamentos ativos da filial
   - Formato: `Nome - Tipo (Número de Série)`

2. **Filial de Destino**
   - Select com lista de filiais disponíveis
   - Exclui a filial atual do usuário

3. **Justificativa**
   - Textarea para descrição do motivo da transferência
   - Obrigatório

4. **Autorizado por**
   - Input para nome do responsável pela autorização
   - Obrigatório

## 3. Implementação

### 3.1 Arquivos Relacionados

- `web/templates/minhaconta/minha_conta.html`: Template principal
- `web/static/js/minha_conta.js`: JavaScript principal
- `web/static/css/minha_conta.css`: Estilos CSS
- `internal/handlers/equipment_transfer_handler.go`: Handler da API
- `internal/models/equipment_transfer.go`: Modelo de dados

### 3.2 Endpoints da API

```go
// POST /api/equipment-transfers
// Solicita uma nova transferência
type TransferRequest struct {
    EquipmentID         int    `json:"equipment_id"`
    DestinationBranchID int    `json:"destination_branch_id"`
    Justification      string `json:"justification"`
    AuthorizedBy       string `json:"authorized_by"`
}

// GET /api/equipment-transfers
// Lista transferências da filial
type TransferResponse struct {
    ID                  int       `json:"id"`
    Equipment          Equipment `json:"equipment"`
    SourceBranch       Branch    `json:"source_branch"`
    DestinationBranch  Branch    `json:"destination_branch"`
    Status             string    `json:"status"`
    CreatedAt          time.Time `json:"created_at"`
    // ... outros campos
}
```

### 3.3 Estados de Carregamento

O componente utiliza classes CSS para indicar estados de carregamento:

```css
.form-select.loading {
    background-image: url("data:image/svg+xml,..."); /* Spinner amarelo */
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1.5rem;
    pointer-events: none;
    opacity: 0.7;
}
```

## 4. Fluxo de Dados

1. **Abertura do Modal**
   - Evento: `show.bs.modal`
   - Ações:
     - Carrega lista de equipamentos
     - Carrega lista de filiais
     - Reseta formulário

2. **Carregamento de Dados**
   - `loadEquipmentsForTransfer()`: Busca equipamentos ativos
   - `loadBranchesForTransfer()`: Busca filiais disponíveis

3. **Submissão do Formulário**
   - Validação dos campos
   - Envio da requisição POST
   - Atualização da lista de transferências
   - Exibição de feedback

## 5. Manutenção

### 5.1 Possíveis Problemas

1. **Carregamento de Dados**
   - Verificar conexão com a API
   - Validar permissões do usuário
   - Confirmar formato dos dados

2. **Validação de Formulário**
   - Testar campos obrigatórios
   - Verificar formatação dos dados
   - Validar regras de negócio

3. **Estado do Equipamento**
   - Confirmar disponibilidade
   - Verificar status atual
   - Validar permissões da filial

### 5.2 Soluções Comuns

1. **Erro no Carregamento**
   ```javascript
   // Adicionar logs detalhados
   console.error('Erro ao carregar equipamentos:', error);
   // Mostrar mensagem amigável
   showToast('error', 'Erro ao carregar equipamentos. Tente novamente.');
   ```

2. **Validação de Dados**
   ```javascript
   // Verificar campos obrigatórios
   if (!equipmentId || !destinationBranchId || !justification || !authorizedBy) {
       showToast('error', 'Por favor, preencha todos os campos.');
       return;
   }
   ```

3. **Tratamento de Erros da API**
   ```javascript
   fetch('/api/equipment-transfers', {
       // ... configurações
   })
   .then(response => {
       if (!response.ok) throw new Error('Erro ao solicitar transferência');
       return response.json();
   })
   .catch(error => {
       console.error('Erro:', error);
       showToast('error', 'Erro ao solicitar transferência. Tente novamente.');
   });
   ```

## 6. Melhorias Futuras

1. **Performance**
   - Implementar cache de dados
   - Otimizar carregamento de listas
   - Adicionar paginação para histórico

2. **Usabilidade**
   - Adicionar preview do equipamento
   - Implementar busca nos selects
   - Melhorar feedback visual

3. **Funcionalidades**
   - Adicionar anexos à transferência
   - Implementar histórico detalhado
   - Adicionar comentários/observações

## 7. Testes

### 7.1 Testes Unitários

```go
func TestTransferRequest_Validate(t *testing.T) {
    // Testes para validação de dados
}

func TestTransferHandler_Create(t *testing.T) {
    // Testes para criação de transferência
}
```

### 7.2 Testes de Interface

```javascript
describe('Transfer Modal', () => {
    it('should load equipment list', () => {
        // Teste de carregamento de equipamentos
    });

    it('should validate form fields', () => {
        // Teste de validação de formulário
    });
});
```

## 8. Referências

- [Design System Shell](docs/design-system-shell.md)
- [API Documentation](docs/api/equipment-transfers.md)
- [Database Schema](docs/database/schema.md) 