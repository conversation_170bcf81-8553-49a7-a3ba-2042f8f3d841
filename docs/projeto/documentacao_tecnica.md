# Documentação Técnica - Sistema de Gestão de Manutenção

## 1. Visão Geral do Sistema

### 1.1 Arquitetura
- **Backend**: Go (Gin Framework)
- **Frontend**: HTML5, CSS3, JavaScript
- **Banco de Dados**: PostgreSQL
- **Autenticação**: JWT
- **Design System**: Shell Design System

### 1.2 Estrutura do Projeto
```
projeto_linux/
├── cmd/                 # Ponto de entrada da aplicação
├── internal/           # Código interno do projeto
│   ├── config/        # Configurações
│   ├── controllers/   # Controladores
│   ├── handlers/      # Manipuladores HTTP
│   ├── middleware/    # Middlewares
│   ├── models/        # Modelos de dados
│   ├── repository/    # Camada de acesso a dados
│   ├── routes/        # Definição de rotas
│   └── service/       # Lógica de negócios
├── web/               # Frontend
│   ├── static/        # Arquivos estáticos
│   │   ├── css/       # Estilos
│   │   ├── js/        # Scripts
│   │   └── images/    # Imagens
│   └── templates/     # Templates HTML
├── migrations/        # Migrações do banco de dados
├── docs/             # Documentação
└── scripts/          # Scripts de automação
```

## 2. Design System

### 2.1 Cores
```css
:root {
    /* Cores Principais */
    --shell-red: #ED1C24;
    --shell-red-hover: #CE181F;
    --shell-red-light: #ff6b6e;
    --shell-yellow: #FDB813;
    --shell-yellow-hover: #e0a100;
    --shell-yellow-light: #ffe07a;
    --shell-dark: #333333;
    --shell-dark-hover: #222222;
    --shell-light: #f8f9fa;
    --shell-light-hover: #e9ecef;
    --shell-gray: #808080;
    --shell-gray-light: #d9d9d9;

    /* Cores de Status */
    --shell-success: #28a745;
    --shell-info: #17a2b8;
    --shell-warning: #ffc107;
    --shell-danger: #dc3545;
}
```

### 2.2 Tipografia
```css
/* Fontes */
font-family: 'Rajdhani', sans-serif; /* Títulos e destaques */
font-family: 'Share Tech Mono', monospace; /* Elementos técnicos */

/* Tamanhos */
--font-size-xs: 0.75rem;   /* 12px */
--font-size-sm: 0.875rem;  /* 14px */
--font-size-md: 1rem;      /* 16px */
--font-size-lg: 1.25rem;   /* 20px */
--font-size-xl: 1.5rem;    /* 24px */
--font-size-xxl: 2rem;     /* 32px */

/* Pesos */
--font-weight-normal: 400;
--font-weight-medium: 500;
--font-weight-semibold: 600;
--font-weight-bold: 700;
```

## 3. Componentes do Sistema

### 3.1 Autenticação e Autorização
- **JWT**: Implementação de autenticação baseada em tokens
- **Middleware**: Verificação de permissões por rota
- **Perfis**: Admin, Técnico, Filial, Financeiro

### 3.2 Gestão de Ordens de Serviço
- **Criação**: Formulário padronizado com validação
- **Atribuição**: Sistema de notificações
- **Acompanhamento**: Status e histórico
- **Documentação**: Upload de arquivos

### 3.3 Gestão de Equipamentos
- **Cadastro**: Informações detalhadas
- **Manutenção**: Histórico e custos
- **Transferência**: Entre filiais com notificação

### 3.4 Relatórios e Dashboards
- **Métricas**: KPIs em tempo real
- **Gráficos**: Visualização de dados
- **Exportação**: Formatos diversos

## 4. Padrões de Código

### 4.1 Backend (Go)
```go
// Estrutura de um handler
type Handler struct {
    service ServiceInterface
}

// Método padrão
func (h *Handler) Method(c *gin.Context) {
    // Validação
    if err := c.ShouldBindJSON(&data); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // Processamento
    result, err := h.service.Process(data)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    // Resposta
    c.JSON(http.StatusOK, result)
}
```

### 4.2 Frontend (HTML/Templates)
```html
{{ define "secao/pagina.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Título da Página - Rede Tradição Shell</title>
    
    <!-- Recursos CSS -->
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/pagina.css">
</head>
<body>
    {{ template "sidebar" . }}
    <div class="content-with-sidebar">
        <div class="main-content">
            <!-- Conteúdo aqui -->
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/pagina.js"></script>
</body>
</html>
{{ end }}
```

## 5. Segurança

### 5.1 Autenticação
- Tokens JWT com expiração
- Refresh tokens
- Proteção contra CSRF

### 5.2 Autorização
- RBAC (Role-Based Access Control)
- Permissões granulares
- Log de acessos

### 5.3 Dados
- Criptografia de senhas
- Sanitização de inputs
- Validação de dados

## 6. Performance

### 6.1 Otimizações
- Cache de consultas frequentes
- Lazy loading de componentes
- Compressão de assets

### 6.2 Monitoramento
- Logs estruturados
- Métricas de performance
- Alertas automáticos

## 7. Deployment

### 7.1 Requisitos
- PostgreSQL 12+
- Go 1.18+
- Node.js 14+ (para build)

### 7.2 Processo
1. Build do frontend
2. Compilação do backend
3. Migração do banco
4. Configuração do servidor
5. Inicialização dos serviços

## 8. Manutenção

### 8.1 Backup
- Banco de dados diário
- Código fonte versionado
- Documentação atualizada

### 8.2 Atualizações
- Processo de deploy
- Rollback automático
- Testes automatizados

## 9. Troubleshooting

### 9.1 Logs
- Estrutura de logs
- Níveis de log
- Rotação de arquivos

### 9.2 Erros Comuns
- Soluções documentadas
- Workarounds
- Contatos de suporte 