# Guia de Manutenção - Transferência de Equipamentos

## 1. Diagnóstico de Problemas

### 1.1 Problemas de Carregamento

#### Sintomas
- Modal não abre
- Selects não carregam dados
- Spinner de carregamento permanece visível

#### Verificações
1. Console do navegador para erros JavaScript
2. Network tab para falhas nas requisições
3. Logs do servidor para erros de API
4. Status do banco de dados

#### Soluções
```javascript
// Adicionar logs para debug
console.log('Modal aberto, carregando dados...');
console.log('Branch ID:', document.getElementById('currentBranchId').value);

// Verificar resposta da API
fetch('/api/equipments?branch_id=${branchId}&status=ativo')
    .then(response => {
        console.log('Status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Dados recebidos:', data);
    });
```

### 1.2 Problemas de Validação

#### Sintomas
- Formulário não submete
- Mensagens de erro não aparecem
- Dados inválidos são enviados

#### Verificações
1. Estado dos campos do formulário
2. Funcionamento das validações
3. Formato dos dados enviados
4. Resposta do servidor

#### Soluções
```javascript
// Implementar validação detalhada
function validateTransferForm() {
    const errors = [];
    const equipmentId = document.getElementById('equipmentSelect').value;
    const destinationBranchId = document.getElementById('destinationBranchSelect').value;
    const justification = document.getElementById('justification').value;
    const authorizedBy = document.getElementById('authorizedBy').value;

    if (!equipmentId) errors.push('Selecione um equipamento');
    if (!destinationBranchId) errors.push('Selecione uma filial de destino');
    if (!justification.trim()) errors.push('Informe a justificativa');
    if (!authorizedBy.trim()) errors.push('Informe quem autorizou');

    return errors;
}
```

### 1.3 Problemas de Estado

#### Sintomas
- Equipamentos indisponíveis aparecem
- Filial atual aparece como destino
- Status incorreto após transferência

#### Verificações
1. Filtros da API
2. Estado do equipamento no banco
3. Permissões do usuário
4. Cache do navegador

#### Soluções
```javascript
// Implementar verificação de estado
function verifyEquipmentState(equipmentId) {
    return fetch(`/api/equipments/${equipmentId}/status`)
        .then(response => response.json())
        .then(data => {
            if (data.status !== 'ativo') {
                throw new Error('Equipamento não está disponível');
            }
            return true;
        });
}
```

## 2. Monitoramento

### 2.1 Logs Importantes

```go
// Handler
func (h *TransferHandler) Create(c *gin.Context) {
    logger := h.logger.With(
        "action", "create_transfer",
        "user_id", getUserID(c),
        "branch_id", getBranchID(c),
    )

    logger.Info("Iniciando criação de transferência")
    // ... código ...
    logger.Error("Erro ao criar transferência", "error", err)
}
```

### 2.2 Métricas

1. **Performance**
   - Tempo de carregamento do modal
   - Tempo de resposta da API
   - Taxa de sucesso das transferências

2. **Uso**
   - Número de transferências por dia
   - Taxa de aprovação/rejeição
   - Equipamentos mais transferidos

3. **Erros**
   - Taxa de falha nas requisições
   - Erros de validação mais comuns
   - Timeouts e falhas de conexão

## 3. Manutenção Preventiva

### 3.1 Checklist Diário

- [ ] Verificar logs de erro
- [ ] Monitorar tempo de resposta
- [ ] Verificar transferências pendentes
- [ ] Validar integridade dos dados

### 3.2 Checklist Semanal

- [ ] Analisar métricas de uso
- [ ] Verificar consistência do banco
- [ ] Limpar dados temporários
- [ ] Atualizar cache de filiais

### 3.3 Checklist Mensal

- [ ] Revisar logs antigos
- [ ] Otimizar consultas
- [ ] Atualizar documentação
- [ ] Verificar backups

## 4. Procedimentos de Atualização

### 4.1 Atualização de Frontend

1. Fazer backup dos arquivos
2. Atualizar arquivos JavaScript
3. Atualizar arquivos CSS
4. Limpar cache do navegador
5. Testar funcionalidades

```bash
# Exemplo de script de atualização
cp web/static/js/minha_conta.js web/static/js/minha_conta.js.bak
cp web/static/css/minha_conta.css web/static/css/minha_conta.css.bak
# Atualizar arquivos
# Limpar cache
```

### 4.2 Atualização de Backend

1. Fazer backup do banco
2. Verificar migrações pendentes
3. Atualizar código
4. Executar testes
5. Reiniciar serviço

```bash
# Exemplo de procedimento
pg_dump -U postgres tradicao > backup_transferencias.sql
go test ./...
# Atualizar código
systemctl restart tradicao-api
```

## 5. Recuperação de Desastres

### 5.1 Backup e Restore

```sql
-- Backup das transferências
CREATE TABLE equipment_transfers_backup AS 
SELECT * FROM equipment_transfers;

-- Restaurar dados
INSERT INTO equipment_transfers 
SELECT * FROM equipment_transfers_backup;
```

### 5.2 Rollback de Mudanças

```bash
# Reverter alterações de frontend
git checkout HEAD^ web/static/js/minha_conta.js
git checkout HEAD^ web/static/css/minha_conta.css

# Reverter alterações de backend
git checkout HEAD^ internal/handlers/equipment_transfer_handler.go
```

### 5.3 Procedimento de Emergência

1. Identificar o problema
2. Notificar usuários afetados
3. Aplicar solução temporária
4. Investigar causa raiz
5. Implementar correção definitiva

## 6. Contatos

### 6.1 Equipe de Suporte
- Nível 1: <EMAIL>
- Nível 2: <EMAIL>
- Emergência: (11) 99999-9999

### 6.2 Responsáveis
- Frontend: [Nome do Desenvolvedor]
- Backend: [Nome do Desenvolvedor]
- Banco de Dados: [Nome do DBA]

## 7. Anexos

### 7.1 Queries Úteis

```sql
-- Verificar transferências pendentes
SELECT e.name, sb.name as source, db.name as destination, t.created_at
FROM equipment_transfers t
JOIN equipments e ON t.equipment_id = e.id
JOIN branches sb ON t.source_branch_id = sb.id
JOIN branches db ON t.destination_branch_id = db.id
WHERE t.status = 'pending'
ORDER BY t.created_at DESC;

-- Verificar equipamentos mais transferidos
SELECT e.name, COUNT(*) as transfer_count
FROM equipment_transfers t
JOIN equipments e ON t.equipment_id = e.id
GROUP BY e.id, e.name
ORDER BY transfer_count DESC
LIMIT 10;
```

### 7.2 Scripts de Manutenção

```bash
#!/bin/bash
# cleanup_transfers.sh
# Remove transferências antigas canceladas

echo "Iniciando limpeza..."
psql -U postgres tradicao -c "
DELETE FROM equipment_transfers 
WHERE status = 'cancelled' 
AND created_at < NOW() - INTERVAL '6 months';"
echo "Limpeza concluída"
``` 