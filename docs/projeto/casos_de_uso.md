# Casos de Uso do Sistema de Gestão de Manutenção

## 1. Gestão de Usuários

### 1.1 Cadastro de Usuário
- **Ator**: Administrador
- **Descrição**: Cadastrar um novo usuário no sistema
- **Pré-condições**: Usuário administrador autenticado
- **Fluxo Principal**:
  1. Administrador acessa menu de cadastro de usuários
  2. Sistema exibe formulário de cadastro
  3. Administrador preenche dados do usuário
  4. Sistema valida dados
  5. Sistema cria usuário
  6. Sistema envia email de boas-vindas
- **Fluxos Alternativos**:
  - Dados inválidos: Sistema exibe mensagem de erro
  - Email já cadastrado: Sistema sugere recuperação de senha
- **Pós-condições**: Usuário cadastrado e notificado

### 1.2 Autenticação
- **Ator**: Usuário
- **Descrição**: Realizar login no sistema
- **Pré-condições**: Usuário cadastrado
- **Fluxo Principal**:
  1. Usuário acessa tela de login
  2. Usuário informa credenciais
  3. Sistema valida credenciais
  4. Sistema gera token JWT
  5. Sistema redireciona para dashboard
- **Fluxos Alternativos**:
  - Credenciais inválidas: Sistema exibe mensagem de erro
  - Conta bloqueada: Sistema informa contato do administrador
- **Pós-condições**: Usuário autenticado e com acesso ao sistema

## 2. Gestão de Ordens de Serviço

### 2.1 Criar Ordem de Serviço
- **Ator**: Gestor
- **Descrição**: Criar uma nova ordem de serviço
- **Pré-condições**: Gestor autenticado
- **Fluxo Principal**:
  1. Gestor acessa menu de ordens
  2. Gestor seleciona "Nova Ordem"
  3. Sistema exibe formulário
  4. Gestor preenche dados da ordem
  5. Sistema valida dados
  6. Sistema cria ordem
  7. Sistema notifica técnico designado
- **Fluxos Alternativos**:
  - Dados incompletos: Sistema exibe campos obrigatórios
  - Equipamento em manutenção: Sistema alerta sobre conflito
- **Pós-condições**: Ordem criada e técnico notificado

### 2.2 Atualizar Status da Ordem
- **Ator**: Técnico
- **Descrição**: Atualizar o status de uma ordem de serviço
- **Pré-condições**: Técnico autenticado e com ordem atribuída
- **Fluxo Principal**:
  1. Técnico acessa detalhes da ordem
  2. Técnico seleciona novo status
  3. Sistema valida transição
  4. Sistema atualiza status
  5. Sistema registra histórico
  6. Sistema notifica gestor
- **Fluxos Alternativos**:
  - Status inválido: Sistema exibe opções permitidas
  - Ordem finalizada: Sistema solicita avaliação
- **Pós-condições**: Status atualizado e notificações enviadas

## 3. Gestão de Equipamentos

### 3.1 Cadastrar Equipamento
- **Ator**: Gestor
- **Descrição**: Cadastrar um novo equipamento
- **Pré-condições**: Gestor autenticado
- **Fluxo Principal**:
  1. Gestor acessa menu de equipamentos
  2. Gestor seleciona "Novo Equipamento"
  3. Sistema exibe formulário
  4. Gestor preenche dados do equipamento
  5. Sistema valida dados
  6. Sistema cria registro
  7. Sistema gera QR Code
- **Fluxos Alternativos**:
  - Serial duplicado: Sistema alerta sobre duplicidade
  - Dados técnicos incompletos: Sistema sugere complemento
- **Pós-condições**: Equipamento cadastrado e identificado

### 3.2 Transferir Equipamento
- **Ator**: Gestor
- **Descrição**: Transferir equipamento entre filiais
- **Pré-condições**: Gestor autenticado e equipamento disponível
- **Fluxo Principal**:
  1. Gestor acessa detalhes do equipamento
  2. Gestor seleciona "Transferir"
  3. Sistema exibe formulário
  4. Gestor seleciona filial destino
  5. Sistema valida disponibilidade
  6. Sistema registra transferência
  7. Sistema notifica filial destino
- **Fluxos Alternativos**:
  - Equipamento em manutenção: Sistema bloqueia transferência
  - Filial sem capacidade: Sistema alerta sobre restrição
- **Pós-condições**: Equipamento transferido e filial notificada

### 3.3 Aprovar Transferência
- **Ator**: Gestor da Filial Destino
- **Descrição**: Aprovar ou rejeitar transferência de equipamento
- **Pré-condições**: Transferência pendente e gestor autenticado
- **Fluxo Principal**:
  1. Gestor acessa solicitações de transferência
  2. Sistema exibe detalhes da transferência
  3. Gestor avalia solicitação
  4. Gestor aprova ou rejeita
  5. Sistema atualiza status
  6. Sistema notifica filial origem
- **Fluxos Alternativos**:
  - Rejeição: Sistema registra motivo
  - Cancelamento: Sistema permite cancelamento pela filial origem
- **Pós-condições**: Status atualizado e notificações enviadas

## 4. Relatórios e Dashboards

### 4.1 Gerar Relatório
- **Ator**: Gestor
- **Descrição**: Gerar relatório personalizado
- **Pré-condições**: Gestor autenticado
- **Fluxo Principal**:
  1. Gestor acessa menu de relatórios
  2. Gestor seleciona tipo de relatório
  3. Sistema exibe opções de filtro
  4. Gestor define parâmetros
  5. Sistema processa dados
  6. Sistema gera relatório
  7. Sistema disponibiliza download
- **Fluxos Alternativos**:
  - Sem dados: Sistema informa período vazio
  - Dados extensos: Sistema sugere agendamento
- **Pós-condições**: Relatório gerado e disponível para download

### 4.2 Visualizar Dashboard
- **Ator**: Usuário
- **Descrição**: Visualizar dashboard personalizado
- **Pré-condições**: Usuário autenticado
- **Fluxo Principal**:
  1. Usuário acessa dashboard
  2. Sistema carrega widgets configurados
  3. Sistema atualiza dados em tempo real
  4. Usuário interage com visualizações
  5. Sistema responde a interações
- **Fluxos Alternativos**:
  - Sem permissão: Sistema exibe widgets básicos
  - Dados indisponíveis: Sistema mostra mensagem de carregamento
- **Pós-condições**: Dashboard exibido e interativo 