# Problemas Conhecidos e Soluções

Este documento cataloga os problemas conhecidos no Sistema de Gestão Tradição, suas causas, impactos e soluções implementadas ou propostas. O objetivo é fornecer uma referência centralizada para questões recorrentes e suas resoluções.

## 1. Duplicação na Página Galeria

### Descrição
A página Galeria exibe listagens duplicadas de filiais para alguns usuários, especialmente técnicos.

### Causa Raiz
Entradas duplicadas na tabela `technician_branches`, que associa técnicos a filiais. Quando um técnico é associado à mesma filial múltiplas vezes, a consulta SQL retorna resultados duplicados.

### Impacto
- Confusão para usuários ao visualizar a galeria
- Possível seleção incorreta de filiais
- Inconsistência visual na interface

### Solução Implementada
Uma correção foi implementada no arquivo `filial_filter_service.go`, linha 158, onde a consulta SQL foi modificada para usar `DISTINCT` e evitar duplicações:

```sql
-- Antes
SELECT b.* FROM branches b
JOIN technician_branches tb ON b.id = tb.branch_id
WHERE tb.technician_id = $1

-- Depois
SELECT DISTINCT b.* FROM branches b
JOIN technician_branches tb ON b.id = tb.branch_id
WHERE tb.technician_id = $1
```

Adicionalmente, foi implementada uma limpeza na tabela `technician_branches` para remover entradas duplicadas existentes.

### Status
✅ Corrigido

### Documentação Relacionada
- [Correções no Sistema de Técnicos](@Implementações/Sistema_Tecnicos/Correcoes_Sistema_Tecnicos.md)

## 2. Erro 403 para Técnicos em Ordens Específicas

### Descrição
Usuários com perfil 'técnico' enfrentam erro 403 (Acesso Negado) ao tentar acessar detalhes de ordens específicas, mesmo quando essas ordens estão atribuídas a eles.

### Causa Raiz
O sistema de permissões verifica incorretamente o acesso às ordens. A verificação é baseada apenas na associação do técnico à filial, sem considerar a atribuição direta da ordem ao técnico.

### Impacto
- Técnicos não conseguem acessar ordens atribuídas a eles
- Fluxo de trabalho interrompido
- Aumento de chamados de suporte

### Solução Implementada
Foi implementada uma verificação adicional no middleware de permissões que verifica se a ordem está atribuída diretamente ao técnico, mesmo que ele não esteja associado à filial:

```go
// Verificação adicional para técnicos
if userProfile == "tecnico" {
    // Verifica se a ordem está atribuída ao técnico
    isAssigned, err := s.orderService.IsOrderAssignedToTechnician(orderID, userID)
    if err == nil && isAssigned {
        return true
    }
}
```

### Status
✅ Corrigido

### Documentação Relacionada
- [Correção do Sistema de Permissões](@docs/guias/Sistema_Permissoes_Correcao.md)

## 3. Ordens Falsas para Técnicos

### Descrição
Usuários com perfil 'técnico' estão vendo ordens de serviço falsas ou simuladas em suas contas, que não correspondem a ordens reais no sistema.

### Causa Raiz
Verificações hardcoded no código para ordens com IDs específicos (16-19) estão criando um comportamento inconsistente. Adicionalmente, não há uma tabela de associação clara entre técnicos e ordens.

### Impacto
- Confusão para técnicos ao ver ordens que não existem
- Tentativas de trabalhar em ordens inexistentes
- Inconsistência nos relatórios e dashboards

### Solução Implementada
Foi criada uma nova tabela `technician_orders` para associar técnicos a ordens de forma clara e consistente. As verificações hardcoded para ordens específicas foram removidas e substituídas por um sistema de atribuição baseado nesta tabela.

```sql
CREATE TABLE technician_orders (
    id SERIAL PRIMARY KEY,
    technician_id INTEGER NOT NULL,
    order_id INTEGER NOT NULL,
    assigned_at TIMESTAMP NOT NULL DEFAULT NOW(),
    assigned_by INTEGER NOT NULL,
    UNIQUE(technician_id, order_id),
    FOREIGN KEY (technician_id) REFERENCES users(id),
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (assigned_by) REFERENCES users(id)
);
```

Adicionalmente, foi implementado um trigger que cria automaticamente um registro na tabela `technician_orders` quando uma ordem é criada, se um técnico for especificado.

### Status
✅ Corrigido

### Documentação Relacionada
- [Correção de Ordens para Técnicos](Implementações/Correção_Ordens_Tecnico/Documentação_Implementação.md)

## 4. Problemas na Consulta SQL da Página Galeria

### Descrição
A página Galeria apresenta um erro na consulta SQL no arquivo `filial_filter_service.go`, linha 158, onde o sistema tenta acessar uma tabela 'user' que não existe no banco de dados.

### Causa Raiz
A consulta SQL está referenciando uma tabela 'user' em vez da tabela correta 'users'.

### Impacto
- Erros ao carregar a página Galeria
- Dados incompletos ou incorretos exibidos
- Logs de erro no sistema

### Solução Implementada
A consulta SQL foi corrigida para referenciar a tabela correta:

```sql
-- Antes
SELECT b.* FROM branches b
JOIN technician_branches tb ON b.id = tb.branch_id
JOIN user u ON tb.technician_id = u.id
WHERE tb.technician_id = $1

-- Depois
SELECT b.* FROM branches b
JOIN technician_branches tb ON b.id = tb.branch_id
JOIN users u ON tb.technician_id = u.id
WHERE tb.technician_id = $1
```

### Status
✅ Corrigido

### Documentação Relacionada
- [Correções no Sistema de Técnicos](@Implementações/Sistema_Tecnicos/Correcoes_Sistema_Tecnicos.md)

## 5. Ordem #18 Bloqueada no Código

### Descrição
A ordem de serviço #18 está bloqueada no código, com verificações hardcoded que impedem seu acesso normal.

### Causa Raiz
Verificações de emergência foram adicionadas ao código para lidar com um problema específico com a ordem #18, mas não foram removidas após a resolução do problema.

### Impacto
- Ordem #18 inacessível para usuários
- Comportamento inconsistente do sistema
- Código difícil de manter

### Solução Implementada
As verificações hardcoded para a ordem #18 foram removidas e substituídas por um sistema genérico de verificação de permissões:

```go
// Antes
if orderID == 18 {
    return c.JSON(http.StatusForbidden, gin.H{"error": "Acesso negado"})
}

// Depois
// Verificação genérica baseada em permissões
if !hasPermission(userID, orderID) {
    return c.JSON(http.StatusForbidden, gin.H{"error": "Acesso negado"})
}
```

### Status
✅ Corrigido

### Documentação Relacionada
- [Correção de Ordens para Técnicos](Implementações/Correção_Ordens_Tecnico/Documentação_Implementação.md)

## 6. Inconsistência na Nomenclatura (Filial/Branch/Posto)

### Descrição
O sistema utiliza termos diferentes (filial, branch, posto) para se referir ao mesmo conceito em diferentes partes do código e interface.

### Causa Raiz
Desenvolvimento por diferentes equipes sem padronização clara de nomenclatura.

### Impacto
- Confusão para desenvolvedores e usuários
- Dificuldade de manutenção
- Inconsistência na interface

### Solução Proposta
Padronizar a nomenclatura em todo o sistema:
- Backend: Usar consistentemente o termo 'branch'
- Frontend: Usar consistentemente o termo 'Filial' na interface
- Documentação: Documentar claramente a convenção

Um plano de refatoração gradual foi estabelecido para implementar esta padronização sem impactar o funcionamento do sistema.

### Status
🔄 Em Implementação

### Documentação Relacionada
- [Padronização de Nomenclatura](docs/guias/padronizacao_nomenclatura.md)

## 7. Problemas com Duração de Token JWT

### Descrição
Tokens JWT expiram muito rapidamente (10 minutos) para usuários não-admin/gerente/filial, causando desconexões frequentes.

### Causa Raiz
Configuração de duração de token muito curta para perfis específicos.

### Impacto
- Desconexões frequentes para usuários
- Frustração e perda de produtividade
- Aumento de chamados de suporte

### Solução Proposta
Ajustar a duração do token JWT para um valor mais razoável (2 horas) para todos os perfis, mantendo o refresh token para sessões mais longas.

### Status
🔄 Em Implementação

### Documentação Relacionada
- [Sistema de Autenticação](Implementações/Sistema_Autenticacao/README.md)

## Conclusão

Este documento cataloga os principais problemas conhecidos no Sistema de Gestão Tradição e suas soluções. É um documento vivo que deve ser atualizado conforme novos problemas são identificados e resolvidos.

Para reportar novos problemas ou sugerir melhorias nas soluções existentes, entre em contato com a equipe de desenvolvimento.
