# Sistema Unificado de Permissões

Este documento descreve o sistema unificado de permissões implementado no projeto Tradição.

## Visão Geral

O Sistema Unificado de Permissões é uma implementação robusta e centralizada para controlar o acesso a diferentes partes do sistema. Ele oferece uma solução flexível e fácil de manter, com suporte para auditoria de acessos. As permissões são definidas em um único arquivo YAML, permitindo uma visão clara e centralizada de todas as permissões do sistema.

## Estrutura do Sistema

O sistema de permissões é composto pelos seguintes componentes:

1. **Arquivo de Configuração**: `data/permissions.yaml`
2. **Pacote de Permissões**: `internal/permissions`
3. **Middleware de Permissões**: `internal/middleware`
4. **Sistema de Auditoria**: Registra todas as verificações de permissão

### Arquivo de Configuração (permissions.yaml)

O arquivo YAML define todas as permissões do sistema, organizadas por papéis (roles). Cada papel tem um conjunto de permissões para páginas e APIs.

Exemplo:

```yaml
# Definição de papéis e permissões
roles:
  admin:
    description: "Administrador do Sistema"
    pages:
      - "*"  # Acesso a todas as páginas
    apis:
      - "*"   # Acesso a todas as APIs

  filial:
    description: "Gestor de Filial/Posto"
    pages:
      - "dashboard"
      - "calendario-flip"
      - "manutencao"
      - "nova-manutencao"
      - "minha-conta"
      - "orders"
      - "maintenance/view"
    apis:
      - "api/user/me"
      - "api/auth/logout"
      - "api/equipments"
      - "api/equipments/:id"
      - "api/equipments/types"
      - "api/equipments/filial/:id"
      - "api/maintenance"
      - "api/maintenance/:id"
      - "api/stations"
      - "api/notifications/settings"
      - "api/notifications/subscribe"

# Páginas públicas (acessíveis sem autenticação)
public_pages:
  - ""  # Página inicial
  - "login"
  - "logout"
  - "acesso_negado"
  - "register"
  - "password-reset"
  - "change-password"

# APIs públicas (acessíveis sem autenticação)
public_apis:
  - "api/login"
  - "api/register"
  - "api/password/reset/request"
  - "api/password/reset"
```

### Pacote de Permissões (internal/permissions)

O pacote `permissions` contém a lógica para carregar, validar e verificar permissões:

- `config.go`: Define a estrutura do arquivo de configuração
- `unified_service.go`: Implementa o serviço unificado de permissões
- `unified_middleware.go`: Implementa o middleware unificado para verificação de permissões
- `audit_service.go`: Implementa o serviço de auditoria de permissões
- `types.go`: Define os tipos usados pelo sistema de permissões

### Middleware de Permissões (internal/middleware)

O middleware integra o sistema de permissões com o framework Gin:

- `resource_permissions.go`: Middleware para verificação de permissões de recursos

## Como Usar

### Verificar Permissão de Página

```go
// Obter o serviço unificado de permissões
unifiedService := permissions.GetGlobalUnifiedService()

// Verificar se um usuário tem permissão para acessar uma página
if unifiedService.HasPagePermission(userID, userRole, "/dashboard", ip, userAgent) {
    // Usuário tem permissão
}

// Obter lista de perfis com permissão para acessar uma página
roles := unifiedService.GetPermittedRoles("/dashboard", permissions.PagePermission)
```

### Verificar Permissão de API

```go
// Obter o serviço unificado de permissões
unifiedService := permissions.GetGlobalUnifiedService()

// Verificar se um usuário tem permissão para acessar uma API
if unifiedService.HasAPIPermission(userID, userRole, "/api/equipments", method, ip, userAgent) {
    // Usuário tem permissão
}

// Obter lista de perfis com permissão para acessar uma API
roles := unifiedService.GetPermittedRoles("/api/equipments", permissions.APIPermission)
```

### Verificar Permissão de Recurso

```go
// Obter o serviço unificado de permissões
unifiedService := permissions.GetGlobalUnifiedService()

// Verificar se um usuário tem permissão para realizar uma ação em um recurso
if unifiedService.HasResourcePermission(userID, userRole, permissions.ResourceOrder, orderID, permissions.ActionView, ip, userAgent) {
    // Usuário tem permissão
}
```

### Usar Middleware de Permissões

```go
// Obter o middleware unificado de permissões
unifiedMiddleware := permissions.GetGlobalUnifiedMiddleware()

// Proteger rotas com middleware de permissões
router.Use(middleware.AuthMiddleware(), unifiedMiddleware.PageAccessMiddleware())

// Proteger APIs com middleware de permissões
api := router.Group("/api")
api.Use(unifiedMiddleware.APIAccessMiddleware())

// Proteger recursos com middleware de permissões
orders := api.Group("/orders")
orders.GET("/:id", middleware.ResourcePermissionMiddleware(permissions.ResourceOrder, permissions.ActionView))
orders.PUT("/:id", middleware.ResourcePermissionMiddleware(permissions.ResourceOrder, permissions.ActionUpdate))
orders.DELETE("/:id", middleware.ResourcePermissionMiddleware(permissions.ResourceOrder, permissions.ActionDelete))
```

## Como Adicionar Novas Permissões

Para adicionar novas permissões, edite o arquivo `data/permissions.yaml`:

1. Para adicionar um novo papel:

```yaml
roles:
  novo_papel:
    description: "Descrição do Novo Papel"
    pages:
      - "pagina1"
      - "pagina2"
    apis:
      - "api/rota1"
      - "api/rota2"
```

2. Para adicionar permissões a um papel existente:

```yaml
roles:
  papel_existente:
    pages:
      - "nova_pagina"
    apis:
      - "api/nova_rota"
```

3. Para adicionar uma página pública:

```yaml
public_pages:
  - "nova_pagina_publica"
```

4. Para adicionar uma API pública:

```yaml
public_apis:
  - "api/nova_rota_publica"
```

## Sistema de Auditoria

O sistema de auditoria registra todas as verificações de permissão, permitindo rastrear tentativas de acesso não autorizado e facilitando a investigação de incidentes de segurança.

### Registros de Auditoria

O sistema registra os seguintes tipos de eventos:

- **Acesso a Páginas**: Registra tentativas de acesso a páginas
- **Acesso a APIs**: Registra tentativas de acesso a APIs
- **Verificação de Permissão**: Registra verificações de permissão para recursos específicos

### Consultar Logs de Auditoria

```go
// Obter o serviço de auditoria
auditService := permissions.GetGlobalAuditService()

// Consultar logs de auditoria
logs, err := auditService.GetLogs(startDate, endDate, userID, resourceType, resourceID, action, allowed)
```

## Tipos de Recursos e Ações

O sistema define os seguintes tipos de recursos:

- **ResourceOrder**: Ordens de manutenção
- **ResourceEquipment**: Equipamentos
- **ResourceBranch**: Filiais
- **ResourceTechnician**: Técnicos
- **ResourceServiceProvider**: Prestadores de serviço

E as seguintes ações:

- **ActionView**: Visualizar um recurso
- **ActionCreate**: Criar um recurso
- **ActionUpdate**: Atualizar um recurso
- **ActionDelete**: Excluir um recurso
- **ActionAssign**: Atribuir um recurso a um usuário
