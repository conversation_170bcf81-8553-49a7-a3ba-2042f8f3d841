# Exemplo de Padronização: Modelo de Ordem de Manutenção

Este documento demonstra como aplicar as convenções de nomenclatura ao modelo de Ordem de Manutenção.

## Modelo Atual (Inconsistente)

Atualmente, o sistema possui múltiplos modelos para representar ordens de manutenção:

### `MaintenanceOrder` (internal/models/maintenance_order.go)

```go
type MaintenanceOrder struct {
    ID                 uint          `gorm:"primaryKey" json:"id"`
    BranchID           uint          `gorm:"column:branch_id" json:"branch_id"`
    BranchName         string        `gorm:"-" json:"branch_name"`
    EquipmentID        uint          `gorm:"column:equipment_id" json:"equipment_id"`
    ServiceProviderID  *uint         `gorm:"column:service_provider_id" json:"service_provider_id,omitempty"`
    TechnicianID       *uint         `json:"technician_id" gorm:"column:technician_id"`
    CreatedByUserID    uint          `gorm:"column:created_by_user_id" json:"created_by_user_id"`
    CreatedByName      string        `gorm:"-" json:"created_by_name"`
    Title              string        `gorm:"column:title" json:"title"`
    Description        string        `gorm:"column:description" json:"description"`
    Number             string        `gorm:"column:number" json:"number"`
    Problem            string        `gorm:"column:problem" json:"problem"`
    Status             OrderStatus   `gorm:"column:status" json:"status"`
    Priority           PriorityLevel `gorm:"column:priority" json:"priority"`
    // ...
}
```

### `OrdemManutencaoExpandida` (internal/models/ordem_expandida.go)

```go
type OrdemManutencaoExpandida struct {
    ID              uint            `json:"id"`
    Titulo          string          `json:"titulo"`
    Descricao       string          `json:"descricao"`
    FilialID        uint            `json:"filial_id"`
    FilialNome      string          `json:"filial_nome"`
    EquipamentoID   uint            `json:"equipamento_id"`
    EquipamentoNome string          `json:"equipamento_nome"`
    Status          StatusOrdem     `json:"status"`
    Prioridade      PrioridadeOrdem `json:"prioridade"`
    Tipo            string          `json:"tipo"`
    SolicitanteID   uint            `json:"solicitante_id"`
    SolicitanteNome string          `json:"solicitante_nome"`
    // ...
}
```

## Modelo Padronizado

### Passo 1: Definir o Modelo Principal

```go
// MaintenanceOrder representa uma ordem de manutenção no sistema
type MaintenanceOrder struct {
    ID                uint          `gorm:"primaryKey" json:"id"`
    Number            string        `gorm:"column:number" json:"number"`
    Title             string        `gorm:"column:title" json:"title"`
    Description       string        `gorm:"column:description" json:"description"`
    Problem           string        `gorm:"column:problem" json:"problem"`
    Status            OrderStatus   `gorm:"column:status" json:"status"`
    Priority          PriorityLevel `gorm:"column:priority" json:"priority"`
    BranchID          uint          `gorm:"column:branch_id" json:"branch_id"`
    EquipmentID       uint          `gorm:"column:equipment_id" json:"equipment_id"`
    ServiceProviderID *uint         `gorm:"column:service_provider_id" json:"service_provider_id,omitempty"`
    TechnicianID      *uint         `gorm:"column:technician_id" json:"technician_id,omitempty"`
    CreatedByUserID   uint          `gorm:"column:created_by_user_id" json:"created_by_user_id"`
    DueDate           time.Time     `gorm:"column:due_date" json:"due_date"`
    OpenDate          time.Time     `gorm:"column:open_date" json:"open_date"`
    CompletionDate    *time.Time    `gorm:"column:completion_date" json:"completion_date,omitempty"`
    EstimatedCost     float64       `gorm:"column:estimated_cost" json:"estimated_cost"`
    ActualCost        float64       `gorm:"column:actual_cost" json:"actual_cost"`
    CreatedAt         time.Time     `gorm:"column:created_at" json:"created_at"`
    UpdatedAt         time.Time     `gorm:"column:updated_at" json:"updated_at"`
    DeletedAt         *time.Time    `gorm:"column:deleted_at" json:"deleted_at,omitempty"`
    
    // Campos virtuais (não armazenados no banco)
    BranchName        string        `gorm:"-" json:"branch_name,omitempty"`
    EquipmentName     string        `gorm:"-" json:"equipment_name,omitempty"`
    TechnicianName    string        `gorm:"-" json:"technician_name,omitempty"`
    CreatedByName     string        `gorm:"-" json:"created_by_name,omitempty"`
}

// TableName especifica o nome da tabela no banco de dados
func (MaintenanceOrder) TableName() string {
    return "maintenance_orders"
}
```

### Passo 2: Definir Enumerações Padronizadas

```go
// OrderStatus representa o status de uma ordem de manutenção
type OrderStatus string

// Constantes para status de ordem
const (
    StatusPending    OrderStatus = "pending"
    StatusScheduled  OrderStatus = "scheduled"
    StatusInProgress OrderStatus = "in_progress"
    StatusCompleted  OrderStatus = "completed"
    StatusCancelled  OrderStatus = "cancelled"
    StatusVerified   OrderStatus = "verified"
    StatusRejected   OrderStatus = "rejected"
    StatusApproved   OrderStatus = "approved"
)

// PriorityLevel representa o nível de prioridade de uma ordem
type PriorityLevel string

// Constantes para níveis de prioridade
const (
    PriorityLow      PriorityLevel = "low"
    PriorityMedium   PriorityLevel = "medium"
    PriorityHigh     PriorityLevel = "high"
    PriorityCritical PriorityLevel = "critical"
)
```

### Passo 3: Criar Funções de Conversão

```go
// ToAPIResponse converte o modelo para o formato de resposta da API
func (m *MaintenanceOrder) ToAPIResponse() map[string]interface{} {
    return map[string]interface{}{
        "id":                 m.ID,
        "number":             m.Number,
        "title":              m.Title,
        "description":        m.Description,
        "problem":            m.Problem,
        "status":             m.Status,
        "priority":           m.Priority,
        "branch_id":          m.BranchID,
        "branch_name":        m.BranchName,
        "equipment_id":       m.EquipmentID,
        "equipment_name":     m.EquipmentName,
        "technician_id":      m.TechnicianID,
        "technician_name":    m.TechnicianName,
        "service_provider_id": m.ServiceProviderID,
        "created_by_user_id": m.CreatedByUserID,
        "created_by_name":    m.CreatedByName,
        "due_date":           m.DueDate.Format("2006-01-02"),
        "open_date":          m.OpenDate.Format("2006-01-02"),
        "completion_date":    formatNullableDate(m.CompletionDate),
        "estimated_cost":     m.EstimatedCost,
        "actual_cost":        m.ActualCost,
        "created_at":         m.CreatedAt.Format(time.RFC3339),
        "updated_at":         m.UpdatedAt.Format(time.RFC3339),
    }
}

// FromLegacyOrdem converte um modelo legado para o modelo padronizado
func FromLegacyOrdem(ordem *OrdemManutencaoExpandida) *MaintenanceOrder {
    status := mapLegacyStatus(ordem.Status)
    priority := mapLegacyPriority(ordem.Prioridade)
    
    return &MaintenanceOrder{
        ID:              ordem.ID,
        Title:           ordem.Titulo,
        Description:     ordem.Descricao,
        BranchID:        ordem.FilialID,
        BranchName:      ordem.FilialNome,
        EquipmentID:     ordem.EquipamentoID,
        EquipmentName:   ordem.EquipamentoNome,
        Status:          status,
        Priority:        priority,
        CreatedByUserID: ordem.SolicitanteID,
        CreatedByName:   ordem.SolicitanteNome,
        // Outros campos...
    }
}

// Funções auxiliares para mapeamento
func mapLegacyStatus(status StatusOrdem) OrderStatus {
    statusMap := map[StatusOrdem]OrderStatus{
        "pendente":     StatusPending,
        "agendada":     StatusScheduled,
        "em_andamento": StatusInProgress,
        "concluida":    StatusCompleted,
        "cancelada":    StatusCancelled,
        "verificada":   StatusVerified,
        "rejeitada":    StatusRejected,
        "aprovada":     StatusApproved,
    }
    
    if mappedStatus, ok := statusMap[status]; ok {
        return mappedStatus
    }
    return StatusPending
}

func mapLegacyPriority(prioridade PrioridadeOrdem) PriorityLevel {
    priorityMap := map[PrioridadeOrdem]PriorityLevel{
        "baixa":   PriorityLow,
        "media":   PriorityMedium,
        "alta":    PriorityHigh,
        "critica": PriorityCritical,
    }
    
    if mappedPriority, ok := priorityMap[prioridade]; ok {
        return mappedPriority
    }
    return PriorityMedium
}

func formatNullableDate(date *time.Time) string {
    if date == nil {
        return ""
    }
    return date.Format("2006-01-02")
}
```

## Uso no Frontend

### Mapeamento de Dados da API

```javascript
// Função para mapear dados da API para o formato do frontend
function mapOrderFromAPI(apiOrder) {
    return {
        id: apiOrder.id,
        number: apiOrder.number,
        title: apiOrder.title,
        description: apiOrder.description,
        problem: apiOrder.problem,
        status: apiOrder.status,
        priority: apiOrder.priority,
        branchId: apiOrder.branch_id,
        branchName: apiOrder.branch_name,
        equipmentId: apiOrder.equipment_id,
        equipmentName: apiOrder.equipment_name,
        technicianId: apiOrder.technician_id,
        technicianName: apiOrder.technician_name,
        dueDate: apiOrder.due_date ? new Date(apiOrder.due_date) : null,
        openDate: apiOrder.open_date ? new Date(apiOrder.open_date) : null,
        completionDate: apiOrder.completion_date ? new Date(apiOrder.completion_date) : null,
        estimatedCost: parseFloat(apiOrder.estimated_cost),
        actualCost: parseFloat(apiOrder.actual_cost),
        createdAt: new Date(apiOrder.created_at),
        updatedAt: new Date(apiOrder.updated_at)
    };
}

// Função para formatar status para exibição
function formatStatus(status) {
    const statusMap = {
        'pending': 'Pendente',
        'scheduled': 'Agendada',
        'in_progress': 'Em Andamento',
        'completed': 'Concluída',
        'cancelled': 'Cancelada',
        'verified': 'Verificada',
        'rejected': 'Rejeitada',
        'approved': 'Aprovada'
    };
    
    return statusMap[status] || status;
}

// Função para formatar prioridade para exibição
function formatPriority(priority) {
    const priorityMap = {
        'low': 'Baixa',
        'medium': 'Média',
        'high': 'Alta',
        'critical': 'Crítica'
    };
    
    return priorityMap[priority] || priority;
}
```

### Exemplo de Uso em Componente

```javascript
// Carregar ordens de manutenção
async function loadOrders() {
    try {
        const response = await fetch('/api/maintenance-orders');
        const data = await response.json();
        
        if (data.success) {
            // Mapear dados da API para o formato do frontend
            const orders = data.data.map(mapOrderFromAPI);
            
            // Renderizar ordens na interface
            renderOrders(orders);
        } else {
            showError(data.error.message);
        }
    } catch (error) {
        showError('Erro ao carregar ordens de manutenção');
    }
}

// Renderizar ordens na interface
function renderOrders(orders) {
    const container = document.getElementById('orders-container');
    container.innerHTML = '';
    
    orders.forEach(order => {
        const card = document.createElement('div');
        card.className = 'order-card';
        card.innerHTML = `
            <h3>${order.title}</h3>
            <div class="order-details">
                <p><strong>Número:</strong> ${order.number}</p>
                <p><strong>Equipamento:</strong> ${order.equipmentName}</p>
                <p><strong>Filial:</strong> ${order.branchName}</p>
                <p><strong>Status:</strong> <span class="status-${order.status}">${formatStatus(order.status)}</span></p>
                <p><strong>Prioridade:</strong> <span class="priority-${order.priority}">${formatPriority(order.priority)}</span></p>
                <p><strong>Data Prevista:</strong> ${formatDate(order.dueDate)}</p>
            </div>
            <div class="order-actions">
                <button class="btn-view" data-id="${order.id}">Visualizar</button>
                <button class="btn-edit" data-id="${order.id}">Editar</button>
            </div>
        `;
        
        container.appendChild(card);
    });
}
```
