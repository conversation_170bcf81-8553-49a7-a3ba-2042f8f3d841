# Documentação de Relacionamentos do Banco de Dados

Este documento descreve os principais relacionamentos entre as entidades do sistema de gerenciamento de manutenção de equipamentos em postos/filiais.

## Visão Geral do Esquema

O banco de dados `tradicao_ent` contém as seguintes tabelas principais:

- `branches`: Filiais/postos
- `users`: Usuários do sistema (incluindo técnicos/prestadores)
- `equipment`: Equipamentos
- `maintenance_orders`: Ordens de manutenção

## Relacionamentos Principais

### 1. Filiais (branches) e Equipamentos

Cada equipamento pertence a uma filial específica.

**Implementação:**
- A tabela `equipment` tem uma coluna `branch_id` que referencia `branches(id)`
- Chave estrangeira: `fk_equipment_branch`
- Índice: `idx_equipment_branch_id`

**Consulta de exemplo:**
```sql
-- Listar todos os equipamentos de uma filial específica
SELECT * FROM equipment WHERE branch_id = 1;
```

### 2. Filiais (branches) e Técnicos/Prestadores

Os técnicos/prestadores (que são usuários com role específica) podem estar associados a uma filial.

**Implementação:**
- A tabela `users` tem uma coluna `branch_id` que referencia `branches(id)`
- Chave estrangeira: `fk_users_branch`
- Índice: `idx_users_branch_id`

**Consultas de exemplo:**
```sql
-- Listar todos os técnicos de uma filial específica
SELECT * FROM users WHERE branch_id = 1 AND role = 'technician';

-- Listar todos os prestadores de uma filial específica
SELECT * FROM users WHERE branch_id = 1 AND role = 'provider';
```

Além disso, cada filial pode ter um gerente designado:
- A tabela `branches` tem uma coluna `manager_id` que referencia `users(id)`
- Chave estrangeira: `fk_branches_manager`
- Índice: `idx_branches_manager_id`

### 3. Filiais (branches) e Ordens de Manutenção

As ordens de manutenção estão associadas a uma filial específica.

**Implementação:**
- A tabela `maintenance_orders` tem uma coluna `branch_id` que referencia `branches(id)`
- Chave estrangeira: `fk_maintenance_orders_branch`
- Índice: `idx_maintenance_orders_branch_id`

**Consulta de exemplo:**
```sql
-- Listar todas as ordens de manutenção de uma filial específica
SELECT * FROM maintenance_orders WHERE branch_id = 1;
```

### 4. Ordens de Manutenção e Usuários

As ordens de manutenção têm vários relacionamentos com usuários:

1. **Solicitante** (quem criou a ordem):
   - Coluna: `requester_id` referencia `users(id)`
   - Chave estrangeira: `fk_maintenance_orders_requester`

2. **Técnico** (responsável pela execução):
   - Coluna: `technician_id` referencia `users(id)`
   - Chave estrangeira: `fk_maintenance_orders_technician`

3. **Aprovador** (quem aprovou a ordem):
   - Coluna: `approver_id` referencia `users(id)`
   - Chave estrangeira: `fk_maintenance_orders_approver`

**Consultas de exemplo:**
```sql
-- Listar todas as ordens criadas por um usuário específico
SELECT * FROM maintenance_orders WHERE requester_id = 1;

-- Listar todas as ordens atribuídas a um técnico específico
SELECT * FROM maintenance_orders WHERE technician_id = 2;
```

### 5. Ordens de Manutenção e Equipamentos

Cada ordem de manutenção está associada a um equipamento específico.

**Implementação:**
- A tabela `maintenance_orders` tem uma coluna `equipment_id` que referencia `equipment(id)`
- Chave estrangeira: `fk_maintenance_orders_equipment`
- Índice: `idx_maintenance_orders_equipment_id`

**Consulta de exemplo:**
```sql
-- Listar todas as ordens de manutenção para um equipamento específico
SELECT * FROM maintenance_orders WHERE equipment_id = 1;

-- Listar o histórico de manutenção de um equipamento
SELECT * FROM maintenance_orders 
WHERE equipment_id = 1 
ORDER BY created_at DESC;
```

## Fluxo de Criação de Ordem de Manutenção

Quando um usuário cria uma ordem de manutenção:

1. O sistema registra o ID do usuário na coluna `requester_id`
2. A ordem é associada à filial do usuário (ou à filial especificada)
3. A ordem é associada ao equipamento que precisa de manutenção
4. Opcionalmente, um técnico pode ser designado imediatamente ou posteriormente

## Diagrama de Relacionamentos

```
+-------------+       +-------------+       +-------------+
|   branches  |       |    users    |       |  equipment  |
+-------------+       +-------------+       +-------------+
| id          |<----->| id          |       | id          |
| name        |       | name        |       | name        |
| ...         |<----->| branch_id   |       | branch_id   |<----+
| manager_id  |------>| ...         |       | ...         |     |
+-------------+       +-------------+       +-------------+     |
      ^                  ^   ^   ^                ^             |
      |                  |   |   |                |             |
      |                  |   |   |                |             |
      |                  |   |   |                |             |
      |                  |   |   |                |             |
      |                  |   |   |                |             |
+-----+------------------+---+---+----------------+-------------+
|            maintenance_orders                                 |
+----------------------------------------------------------------+
| id                                                             |
| branch_id                                                      |
| equipment_id                                                   |
| requester_id                                                   |
| technician_id                                                  |
| approver_id                                                    |
| ...                                                            |
+----------------------------------------------------------------+
```

## Notas Importantes

1. **Integridade Referencial**: Todas as chaves estrangeiras têm a opção `ON DELETE SET NULL`, o que significa que se uma entidade referenciada for excluída, a referência será definida como NULL em vez de falhar.

2. **Soft Delete**: As tabelas principais implementam o conceito de "soft delete" através da coluna `deleted_at`. Registros com esta coluna preenchida são considerados excluídos logicamente, mas permanecem no banco de dados.

3. **Índices**: Índices foram criados para otimizar as consultas mais comuns, especialmente aquelas que envolvem relacionamentos entre tabelas.

4. **Nomenclatura**: Todas as tabelas e colunas seguem uma nomenclatura consistente em inglês para facilitar a integração com ferramentas como ENT e Atlas.
