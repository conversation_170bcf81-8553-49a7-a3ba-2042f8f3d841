# IMPORTANTE: Uso de Banco de Dados no Sistema Tradição

## Regra Fundamental: PROIBIDO Uso de Banco de Dados Local

**O uso de banco de dados local está EXPRESSAMENTE PROIBIDO neste projeto.**

Todas as conexões devem ser feitas exclusivamente com o banco de dados remoto especificado no arquivo `.env`.

## Configuração Correta

O sistema foi modificado para garantir que apenas o banco de dados remoto seja utilizado. As seguintes alterações foram implementadas:

1. Verificação em todos os pontos de conexão para bloquear tentativas de uso de banco de dados local
2. Priorização do uso da variável `DATABASE_URL` para conexão
3. Uso de valores padrão que apontam para o banco remoto quando as variáveis de ambiente não estão definidas

## Configuração Padrão do Banco de Dados

Quando as variáveis de ambiente não estão definidas, o sistema usa os seguintes valores padrão:

```
Host: postgres-ag-br1-03.conteige.cloud
Porta: 54243
Usuário: fcobdj_tradicao
Senha: 67573962
Nome do banco: fcobdj_tradicao
```

## Arquivo .env

O arquivo `.env` deve conter as seguintes variáveis para conexão com o banco de dados:

```
DB_HOST=postgres-ag-br1-03.conteige.cloud
DB_PORT=54243
DB_USER=fcobdj_tradicao
DB_PASS=67573962
DB_NAME=fcobdj_tradicao

# String de conexão completa para PostgreSQL
DATABASE_URL=postgresql://fcobdj_tradicao:<EMAIL>:54243/fcobdj_tradicao?sslmode=disable
```

## Motivos para esta Restrição

1. **Consistência de Dados**: Garantir que todos os desenvolvedores trabalhem com os mesmos dados
2. **Evitar Problemas de Migração**: Prevenir problemas de esquema de banco de dados inconsistente
3. **Segurança**: Manter os dados em um ambiente controlado e seguro
4. **Simplificação**: Eliminar a necessidade de configurar e manter bancos de dados locais

## O que Acontece se Tentar Usar Banco de Dados Local?

Se o sistema detectar uma tentativa de conexão com um banco de dados local (host = "localhost" ou "127.0.0.1"), ele irá:

1. Exibir uma mensagem de erro clara
2. Encerrar a aplicação imediatamente
3. Registrar o erro nos logs

## Arquivos Modificados

Os seguintes arquivos foram modificados para implementar esta restrição:

1. `internal/database/database.go`
2. `internal/database/connection.go`
3. `internal/database/connection_pool.go`
4. `internal/config/database.go`
5. `internal/config/config.go`
6. `internal/database/postgresql.go`

## Como Testar Conexão com o Banco de Dados

Para verificar se a conexão com o banco de dados remoto está funcionando corretamente, execute:

```bash
go run scripts/verify_user.go <EMAIL>
```

Este comando deve retornar informações sobre o usuário técnico, confirmando que a conexão com o banco de dados remoto está funcionando.

## Suporte e Dúvidas

Em caso de dúvidas ou problemas com a conexão ao banco de dados, consulte a documentação ou entre em contato com a equipe de suporte.

**LEMBRE-SE: Nunca tente modificar o código para usar um banco de dados local. Esta restrição é fundamental para o funcionamento correto do sistema.**
