# Informações do Banco de Dados 'fcobdj_tradicao'
Data de extração: Mon Apr 21 08:11:46 AM -03 2025

## Lista de Tabelas

| Nome da Tabela | Número de Registros |
|---------------|---------------------|
| branch_auth_tokens | 0 |
| branches | 0 |
| calendar_events | 0 |
| equipment | 0 |
| maintenance_orders | 0 |
| users | 8 |

## Estrutura das Tabelas

### Tabela: branch_auth_tokens

#### Colunas

| Coluna | Tipo | Nullable | Default |
|--------|------|----------|---------|
| id | | | integer | | NO | nextval('branch_auth_tokens_id_seq'::regclass) |
| branch_id | | | integer | | YES | |
| token | | | character | varying | NO | |
| expires_at | | | timestamp | without time zone | NO | |
| created_at | | | timestamp | without time zone | YES | CURRENT_TIMESTAMP |
| is_used | | | boolean | | YES | false |

#### Índices

| Nome do Índice | Definição |
|---------------|-----------|
| branch_auth_tokens_pkey | | CREATE UNIQUE INDEX branch_auth_tokens_pkey ON public.branch_auth_tokens USING btree (id) |
| branch_auth_tokens_token_key | | CREATE UNIQUE INDEX branch_auth_tokens_token_key ON public.branch_auth_tokens USING btree (token) |
| idx_branch_auth_tokens | | CREATE INDEX idx_branch_auth_tokens ON public.branch_auth_tokens USING btree (token) |

#### Chaves Estrangeiras

| Nome da Constraint | Coluna | Tabela Referenciada | Coluna Referenciada |
|-------------------|--------|---------------------|---------------------|
| branch_auth_tokens_branch_id_fkey | | | branch_id | | |

#### Amostra de Dados (até 5 registros)

Nenhum dado encontrado nesta tabela.

---

### Tabela: branches

#### Colunas

| Coluna | Tipo | Nullable | Default |
|--------|------|----------|---------|
| id | | | integer | | NO | nextval('branches_id_seq'::regclass) |
| name | | | character | varying | NO | |
| address | | | character | varying | YES | |
| cnpj | | | character | varying | YES | |
| manager_id | | | integer | | YES | |
| created_at | | | timestamp | without time zone | YES | CURRENT_TIMESTAMP |
| updated_at | | | timestamp | without time zone | YES | CURRENT_TIMESTAMP |

#### Índices

| Nome do Índice | Definição |
|---------------|-----------|
| branches_pkey | | CREATE UNIQUE INDEX branches_pkey ON public.branches USING btree (id) |

#### Chaves Estrangeiras

| Nome da Constraint | Coluna | Tabela Referenciada | Coluna Referenciada |
|-------------------|--------|---------------------|---------------------|
| branches_manager_id_fkey | | | manager_id | | |

#### Amostra de Dados (até 5 registros)

Nenhum dado encontrado nesta tabela.

---

### Tabela: calendar_events

#### Colunas

| Coluna | Tipo | Nullable | Default |
|--------|------|----------|---------|
| id | | | integer | | NO | nextval('calendar_events_id_seq'::regclass) |
| title | | | character | varying | NO | |
| description | | | text | | YES | |
| start_date | | | timestamp | without time zone | NO | |
| end_date | | | timestamp | without time zone | YES | |
| user_id | | | integer | | YES | |
| maintenance_order_id | | | integer | | YES | |
| created_at | | | timestamp | without time zone | YES | CURRENT_TIMESTAMP |

#### Índices

| Nome do Índice | Definição |
|---------------|-----------|
| calendar_events_pkey | | CREATE UNIQUE INDEX calendar_events_pkey ON public.calendar_events USING btree (id) |
| idx_calendar_events_user | | CREATE INDEX idx_calendar_events_user ON public.calendar_events USING btree (user_id) |

#### Chaves Estrangeiras

| Nome da Constraint | Coluna | Tabela Referenciada | Coluna Referenciada |
|-------------------|--------|---------------------|---------------------|
| calendar_events_user_id_fkey | | | user_id | | |
| calendar_events_maintenance_order_id_fkey | | | maintenance_order_id | | |

#### Amostra de Dados (até 5 registros)

Nenhum dado encontrado nesta tabela.

---

### Tabela: equipment

#### Colunas

| Coluna | Tipo | Nullable | Default |
|--------|------|----------|---------|
| id | | | integer | | NO | nextval('equipment_id_seq'::regclass) |
| name | | | character | varying | NO | |
| type | | | character | varying | YES | |
| serial_number | | | character | varying | YES | |
| branch_id | | | integer | | YES | |
| status | | | character | varying | YES | |
| created_at | | | timestamp | without time zone | YES | CURRENT_TIMESTAMP |
| updated_at | | | timestamp | without time zone | YES | CURRENT_TIMESTAMP |

#### Índices

| Nome do Índice | Definição |
|---------------|-----------|
| equipment_pkey | | CREATE UNIQUE INDEX equipment_pkey ON public.equipment USING btree (id) |
| idx_equipment_branch | | CREATE INDEX idx_equipment_branch ON public.equipment USING btree (branch_id) |

#### Chaves Estrangeiras

| Nome da Constraint | Coluna | Tabela Referenciada | Coluna Referenciada |
|-------------------|--------|---------------------|---------------------|
| equipment_branch_id_fkey | | | branch_id | | |

#### Amostra de Dados (até 5 registros)

Nenhum dado encontrado nesta tabela.

---

### Tabela: maintenance_orders

#### Colunas

| Coluna | Tipo | Nullable | Default |
|--------|------|----------|---------|
| id | | | integer | | NO | nextval('maintenance_orders_id_seq'::regclass) |
| number | | | character | varying | YES | |
| equipment_id | | | integer | | YES | |
| branch_id | | | integer | | YES | |
| description | | | text | | NO | |
| status | | | character | varying | YES | 'open'::character varying |
| priority | | | character | varying | YES | |
| service_provider_id | | | integer | | YES | |
| opening_date | | | date | | YES | |
| start_date | | | date | | YES | |
| repair_date | | | date | | YES | |
| total_cost | | | numeric | | YES | |
| created_at | | | timestamp | without time zone | YES | CURRENT_TIMESTAMP |
| updated_at | | | timestamp | without time zone | YES | CURRENT_TIMESTAMP |

#### Índices

| Nome do Índice | Definição |
|---------------|-----------|
| maintenance_orders_number_key | | CREATE UNIQUE INDEX maintenance_orders_number_key ON public.maintenance_orders USING btree (number) |
| maintenance_orders_pkey | | CREATE UNIQUE INDEX maintenance_orders_pkey ON public.maintenance_orders USING btree (id) |

#### Chaves Estrangeiras

| Nome da Constraint | Coluna | Tabela Referenciada | Coluna Referenciada |
|-------------------|--------|---------------------|---------------------|
| maintenance_orders_equipment_id_fkey | | | equipment_id | | |
| maintenance_orders_branch_id_fkey | | | branch_id | | |
| maintenance_orders_service_provider_id_fkey | | | service_provider_id | | |

#### Amostra de Dados (até 5 registros)

Nenhum dado encontrado nesta tabela.

---

### Tabela: users

#### Colunas

| Coluna | Tipo | Nullable | Default |
|--------|------|----------|---------|
| id | | | integer | | NO | nextval('users_id_seq'::regclass) |
| name | | | character | varying | NO | |
| email | | | character | varying | NO | |
| password | | | character | varying | NO | |
| role | | | character | varying | NO | |
| avatar_url | | | character | varying | YES | |
| station_id | | | integer | | YES | |
| is_active | | | boolean | | YES | true |
| created_at | | | timestamp | without time zone | YES | CURRENT_TIMESTAMP |
| updated_at | | | timestamp | without time zone | YES | CURRENT_TIMESTAMP |

#### Índices

| Nome do Índice | Definição |
|---------------|-----------|
| users_email_key | | CREATE UNIQUE INDEX users_email_key ON public.users USING btree (email) |
| users_pkey | | CREATE UNIQUE INDEX users_pkey ON public.users USING btree (id) |

#### Chaves Estrangeiras

Nenhuma chave estrangeira encontrada para esta tabela.

#### Amostra de Dados (até 5 registros)

```
  1 | Administrador     | <EMAIL>             | $2a$10$XOPbrlUPQdwdJUpSrIF6X.LbE14qsMmKGq6jCuMQzlCMkTzxqZjhC | admin       |            |            | t         | 2025-04-18 09:44:53.874581 | 2025-04-18 09:44:53.874581
  3 | Admin Teste       | <EMAIL>       | $2a$10$UdguD1hJvDkemphtqjD77.0CIeRCTjZNFgVRUEe7hzzHnJifrisAG | admin       |            |            | t         | 2025-04-18 09:45:16.927779 | 2025-04-18 09:45:16.927779
  4 | Gerente Teste     | <EMAIL>     | $2a$10$q8ga1HaXOWf5gKXF8r5EVeuDHne4Lp.jowQCtl435tZV4Bx2ADHhe | gerente     |            |            | t         | 2025-04-18 09:45:16.927779 | 2025-04-18 09:45:16.927779
  5 | Técnico Teste     | <EMAIL>     | $2a$10$5Glp7ghRRb6.gwQJe5GhCefa2ul7Sf65F7gcaEe9xkkl.M5LfQa2W | tecnico     |            |            | t         | 2025-04-18 09:45:16.927779 | 2025-04-18 09:45:16.927779
  6 | Funcionário Teste | <EMAIL> | $2a$10$LTyVMlI7o5wtpNbkbDe7jugm21/OEHUvxphrFp75GFIFMC2L0.b3y | funcionario |            |            | t         | 2025-04-18 09:45:16.927779 | 2025-04-18 09:45:16.927779
```

---

## Visões

Nenhuma visão encontrada neste banco de dados.

## Funções

Nenhuma função encontrada neste banco de dados.

## Triggers

Nenhum trigger encontrado neste banco de dados.

## Sequências

| Nome da Sequência | Valor Atual | Valor Mínimo | Valor Máximo | Incremento |
|-------------------|-------------|--------------|--------------|------------|
| branch_auth_tokens_id_seq |           1 |  |  |  |
| branches_id_seq |           1 |  |  |  |
| calendar_events_id_seq |           1 |  |  |  |
| equipment_id_seq |           1 |  |  |  |
| maintenance_orders_id_seq |           1 |  |  |  |
| users_id_seq |           9 |  |  |  |
