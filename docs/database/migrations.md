# Migrações de Banco de Dados

Este documento descreve as migrações de banco de dados do Sistema de Gestão Tradição, incluindo as migrações mais recentes relacionadas ao sistema unificado de permissões.

## Visão Geral

O Sistema de Gestão Tradição utiliza migrações de banco de dados para gerenciar alterações na estrutura do banco de dados de forma controlada e versionada. As migrações são executadas em ordem cronológica para garantir que o banco de dados esteja sempre no estado correto.

## Migrações Recentes

### 1. Criação da Tabela `technician_orders`

Esta migração cria a tabela `technician_orders` para armazenar relacionamentos explícitos entre técnicos e ordens de manutenção.

```sql
-- Migração para criar a tabela technician_orders
-- Esta tabela armazena relacionamentos explícitos entre técnicos e ordens de manutenção

-- Verificar se a tabela já existe
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'technician_orders') THEN
        -- Criar a tabela technician_orders
        CREATE TABLE technician_orders (
            id SERIAL PRIMARY KEY,
            technician_id INTEGER NOT NULL,
            order_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER,
            notes TEXT,
            UNIQUE(technician_id, order_id)
        );
        
        -- Adicionar índices para melhorar a performance
        CREATE INDEX idx_technician_orders_technician_id ON technician_orders(technician_id);
        CREATE INDEX idx_technician_orders_order_id ON technician_orders(order_id);
        
        -- Adicionar chaves estrangeiras
        ALTER TABLE technician_orders 
            ADD CONSTRAINT fk_technician_orders_technician 
            FOREIGN KEY (technician_id) 
            REFERENCES users(id) 
            ON DELETE CASCADE;
            
        ALTER TABLE technician_orders 
            ADD CONSTRAINT fk_technician_orders_order 
            FOREIGN KEY (order_id) 
            REFERENCES maintenance_orders(id) 
            ON DELETE CASCADE;
            
        ALTER TABLE technician_orders 
            ADD CONSTRAINT fk_technician_orders_created_by 
            FOREIGN KEY (created_by) 
            REFERENCES users(id) 
            ON DELETE SET NULL;
            
        RAISE NOTICE 'Tabela technician_orders criada com sucesso';
    ELSE
        RAISE NOTICE 'Tabela technician_orders já existe';
    END IF;
END
$$;
```

### 2. Adição de Constraint para `technician_id` em `maintenance_orders`

Esta migração adiciona uma constraint de chave estrangeira para a coluna `technician_id` na tabela `maintenance_orders`.

```sql
-- Migração para adicionar constraint de chave estrangeira para technician_id em maintenance_orders

-- Verificar se a constraint já existe
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_maintenance_orders_technician' 
        AND table_name = 'maintenance_orders'
    ) THEN
        -- Adicionar constraint
        ALTER TABLE maintenance_orders 
            ADD CONSTRAINT fk_maintenance_orders_technician 
            FOREIGN KEY (technician_id) 
            REFERENCES users(id);
            
        RAISE NOTICE 'Constraint fk_maintenance_orders_technician adicionada com sucesso';
    ELSE
        RAISE NOTICE 'Constraint fk_maintenance_orders_technician já existe';
    END IF;
END
$$;
```

### 3. Criação da Tabela `permission_audit_logs`

Esta migração cria a tabela `permission_audit_logs` para armazenar logs de auditoria de verificações de permissão.

```sql
-- Migração para criar a tabela permission_audit_logs
-- Esta tabela armazena logs de auditoria de verificações de permissão

-- Verificar se a tabela já existe
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'permission_audit_logs') THEN
        -- Criar a tabela permission_audit_logs
        CREATE TABLE permission_audit_logs (
            id SERIAL PRIMARY KEY,
            user_id INTEGER REFERENCES users(id),
            user_role VARCHAR(50),
            resource_type VARCHAR(50),
            resource_id INTEGER,
            action VARCHAR(50),
            allowed BOOLEAN,
            ip_address VARCHAR(50),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Adicionar índices para melhorar a performance
        CREATE INDEX idx_permission_audit_logs_user_id ON permission_audit_logs(user_id);
        CREATE INDEX idx_permission_audit_logs_resource ON permission_audit_logs(resource_type, resource_id);
        CREATE INDEX idx_permission_audit_logs_created_at ON permission_audit_logs(created_at);
            
        RAISE NOTICE 'Tabela permission_audit_logs criada com sucesso';
    ELSE
        RAISE NOTICE 'Tabela permission_audit_logs já existe';
    END IF;
END
$$;
```

## Como Executar Migrações

As migrações podem ser executadas de duas formas:

### 1. Usando o Script de Migração

```bash
chmod +x scripts/run_migration.sh
./scripts/run_migration.sh
```

### 2. Executando Manualmente

```bash
PGPASSWORD=67573962 psql -h postgres-ag-br1-03.conteige.cloud -p 54243 -U fcobdj_tradicao -d fcobdj_tradicao -f migrations/technician_orders.sql
```

## Boas Práticas para Migrações

1. **Sempre teste migrações em ambiente de desenvolvimento antes de aplicá-las em produção**
2. **Mantenha migrações idempotentes (podem ser executadas múltiplas vezes sem efeitos colaterais)**
3. **Inclua verificações para evitar erros se a estrutura já existir**
4. **Documente claramente o propósito de cada migração**
5. **Mantenha migrações pequenas e focadas em uma única alteração**
6. **Inclua migrações para reverter alterações (rollback) quando possível**

## Histórico de Migrações

| Data | Descrição | Arquivo |
|------|-----------|---------|
| 2025-04-20 | Criação da tabela technician_orders | migrations/technician_orders.sql |
| 2025-04-21 | Adição de constraint para technician_id em maintenance_orders | migrations/maintenance_orders_constraint.sql |
| 2025-04-21 | Criação da tabela permission_audit_logs | migrations/permission_audit_logs.sql |
