# Informações do Banco de Dados 'tradicao'
Data de atualização: 2025-04-21

## Visão Geral

O banco de dados 'tradicao' é utilizado pelo Sistema de Gestão Tradição para armazenar informações sobre filiais, equipamentos, ordens de manutenção, técnicos, prestadores de serviço e permissões de usuários.

## Lista de Tabelas

| Nome da Tabela | Descrição |
|----------------|-----------|
| users | Armazena informações de todos os usuários do sistema |
| branches | Armazena informações sobre filiais/postos |
| equipment | Armazena informações sobre equipamentos |
| maintenance_orders | Armazena informações sobre ordens de manutenção |
| service_providers | Armazena informações sobre prestadores de serviço |
| technician_orders | Tabela de junção que relaciona técnicos a ordens de manutenção |
| technician_branches | Tabela de junção que relaciona técnicos a filiais |
| branch_providers | Tabela de junção que relaciona prestadores de serviço a filiais |
| technician_specialties | Armazena informações sobre especialidades técnicas |
| maintenance_activities | Armazena atividades realizadas em ordens de manutenção |
| attachments | Armazena anexos (documentos, imagens) relacionados a ordens |
| notifications | Armazena notificações enviadas aos usuários |
| permission_audit_logs | Armazena logs de auditoria de verificações de permissão |
| calendar_events | Armazena eventos do calendário |
| security_policies | Armazena políticas de segurança |
| cities | Armazena informações sobre cidades |
| branch_auth_tokens | Armazena tokens de autenticação para filiais |

## Tabelas Principais e Suas Relações

### 1. Usuários e Autenticação

#### Tabela: users

Armazena informações de todos os usuários do sistema, incluindo administradores, gerentes, técnicos, prestadores de serviço e usuários de filial.

**Relações Importantes:**
- Um usuário pode estar associado a uma filial (branch_id)
- Um usuário pode estar associado a um prestador de serviço (service_provider_id)
- Um usuário técnico pode estar associado a múltiplas filiais através da tabela technician_branches
- Um usuário técnico pode estar associado a múltiplas ordens através da tabela technician_orders

### 2. Filiais e Prestadores de Serviço

#### Tabela: branches

Armazena informações sobre filiais/postos.

**Relações Importantes:**
- Uma filial pode ter múltiplos equipamentos
- Uma filial pode ter múltiplas ordens de manutenção
- Uma filial pode ter múltiplos técnicos associados através da tabela technician_branches
- Uma filial pode ter múltiplos prestadores de serviço associados através da tabela branch_providers

#### Tabela: service_providers

Armazena informações sobre prestadores de serviço.

**Relações Importantes:**
- Um prestador de serviço pode estar associado a um usuário (user_id)
- Um prestador de serviço pode estar associado a múltiplas filiais através da tabela branch_providers
- Um prestador de serviço pode ter múltiplos técnicos associados (users com service_provider_id)
- Um prestador de serviço pode estar associado a múltiplas ordens de manutenção

### 3. Equipamentos e Ordens de Manutenção

#### Tabela: equipment

Armazena informações sobre equipamentos.

**Relações Importantes:**
- Um equipamento pertence a uma filial (branch_id)
- Um equipamento pode ter múltiplas ordens de manutenção

#### Tabela: maintenance_orders

Armazena informações sobre ordens de manutenção.

**Relações Importantes:**
- Uma ordem pertence a uma filial (branch_id)
- Uma ordem pode estar associada a um equipamento (equipment_id)
- Uma ordem pode estar associada a um prestador de serviço (service_provider_id)
- Uma ordem pode estar associada a um técnico diretamente (technician_id)
- Uma ordem pode estar associada a múltiplos técnicos através da tabela technician_orders
- Uma ordem pode ter múltiplas atividades de manutenção
- Uma ordem pode ter múltiplos anexos

### 4. Tabelas de Junção

#### Tabela: technician_orders

Tabela de junção que relaciona técnicos a ordens de manutenção.

**Colunas Principais:**
- technician_id: ID do técnico (referência a users.id)
- order_id: ID da ordem (referência a maintenance_orders.id)
- created_by: ID do usuário que criou a atribuição (referência a users.id)
- notes: Notas sobre a atribuição

#### Tabela: technician_branches

Tabela de junção que relaciona técnicos a filiais e especialidades.

**Colunas Principais:**
- technician_id: ID do técnico (referência a users.id)
- branch_id: ID da filial (referência a branches.id)
- specialty_id: ID da especialidade (referência a technician_specialties.id)

#### Tabela: branch_providers

Tabela de junção que relaciona prestadores de serviço a filiais.

**Colunas Principais:**
- service_provider_id: ID do prestador de serviço (referência a service_providers.id)
- branch_id: ID da filial (referência a branches.id)

### 5. Auditoria e Segurança

#### Tabela: permission_audit_logs

Armazena logs de auditoria de verificações de permissão.

**Colunas Principais:**
- user_id: ID do usuário que realizou a ação (referência a users.id)
- user_role: Papel do usuário (admin, gerente, técnico, etc.)
- resource_type: Tipo de recurso (order, equipment, branch, etc.)
- resource_id: ID do recurso
- action: Ação realizada (view, create, update, delete, assign)
- allowed: Se a ação foi permitida ou negada
- ip_address: Endereço IP do usuário
- user_agent: User-Agent do navegador do usuário
- created_at: Data e hora da verificação

## Fluxos de Dados Principais

### 1. Fluxo de Atribuição de Ordens

1. Uma ordem de manutenção é criada por um usuário de filial, gerente ou administrador
2. A ordem é atribuída a um prestador de serviço (service_provider_id)
3. O sistema cria automaticamente registros na tabela technician_orders para todos os técnicos vinculados ao prestador de serviço
4. Os técnicos podem visualizar as ordens atribuídas a eles

### 2. Fluxo de Verificação de Permissões

1. Um usuário tenta acessar uma ordem de manutenção
2. O sistema verifica se o usuário é administrador ou gerente (acesso total)
3. Para técnicos, o sistema verifica:
   - Se o técnico está atribuído diretamente à ordem (maintenance_orders.technician_id)
   - Se existe um registro na tabela technician_orders
   - Se o técnico pertence ao prestador atribuído à ordem
4. Para prestadores, o sistema verifica se estão atribuídos diretamente à ordem
5. Para usuários de filial, o sistema verifica se a ordem pertence à filial do usuário
6. O resultado da verificação é registrado na tabela permission_audit_logs

## Considerações Importantes

1. A tabela technician_orders é fundamental para o sistema de permissões, pois estabelece explicitamente quais técnicos têm acesso a quais ordens
2. A tabela permission_audit_logs permite rastrear todas as verificações de permissão, facilitando a investigação de incidentes de segurança
3. Existe uma redundância intencional entre maintenance_orders.technician_id e a tabela technician_orders para garantir compatibilidade com código legado
4. A relação entre users e service_providers é bidirecional: um prestador de serviço está associado a um usuário, e um usuário técnico pode estar associado a um prestador de serviço
