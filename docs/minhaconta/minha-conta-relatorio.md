# Relatório Detalhado da Página "Minha Conta"

## Funcionalidades Principais
- Edição de perfil do usuário (nome, email, telefone, etc.)
- Alteração de senha com validação
- Preferências do usuário (notificações, temas, etc.)
- Visualização e gerenciamento de notificações
- Atualização da foto de perfil (avatar)
- Logout da aplicação
- Visualização de atividades recentes e histórico de segurança
- Visualização e gerenciamento de equipamentos da filial
- Contadores de tempo ativos para manutenção e status

## Endpoints de API Utilizados
- GET /api/user/me: Carregar dados do usuário
- PUT /api/user/password: Atualizar senha
- PUT /api/user/profile: Atualizar perfil
- POST /api/user/avatar: Atualizar avatar
- POST /api/notifications/{id}/dismiss: Descartar notificação
- POST /api/notifications/mark-all-read: <PERSON><PERSON> todas notificações como lidas
- PUT /api/user/preferences: Salvar preferências
- POST /api/auth/logout: Logout

## Componentes da Interface
- Cards de dashboard para perfil, status, atividades, histórico, segurança
- Painel de notificações com preview e modal detalhado
- Modal para visualização e cadastro de equipamentos
- Contadores de tempo com atualização dinâmica
- Botões de ação estilizados conforme design Shell
- Layout responsivo com grid e flexbox
- Avatar do usuário com upload e preview

## Estilo e Responsividade
- Uso do sistema de design Shell com cores vermelha, amarela, cinza e escura
- Tipografia com fontes 'Rajdhani' e 'Share Tech Mono'
- Layout adaptável para diferentes tamanhos de tela (desktop, tablet, mobile)
- Animações suaves em cards e botões para melhor interatividade
- Uso de variáveis CSS para cores e espaçamentos consistentes

## Observações
- Código JavaScript dividido entre funcionalidades principais e modais/equipamentos
- Uso de Bootstrap para modais e tooltips
- Necessidade de melhoria na modularidade e organização do JS
- Potencial para otimização do CSS e melhoria da acessibilidade

Este relatório servirá como base para o redesign e reestruturação da página "minha conta", garantindo que todas as funcionalidades atuais sejam mantidas e aprimoradas com um visual moderno e responsivo.
