# Mapeamento da Documentação do Sistema de Gestão Tradição

Este documento serve como um índice central para toda a documentação existente do Sistema de Gestão Tradição. Ele mapeia os documentos disponíveis, avalia seu status e fornece recomendações para uso.

## Legenda de Status

- **Atual**: Documento atualizado e consistente com o estado atual do sistema
- **Parcial**: Documento parcialmente atualizado, contém algumas informações desatualizadas
- **Desatualizado**: Documento significativamente desatualizado
- **Verificar**: Documento que precisa ser verificado quanto à sua atualidade
- **Duplicado**: Documento que duplica informações presentes em outro documento

## 1. Documentação de Arquitetura e Visão Geral

| Documento | Caminho | Status | Última Atualização | Recomendação |
|-----------|---------|--------|-------------------|--------------|
| Arquitetura do Sistema | docs/projeto/arquitetura.md | Atual | Desconhecida | Documento principal para entender a arquitetura |
| Documentação Técnica | docs/projeto/documentacao_tecnica.md | Parcial | Desconhecida | Complementar à arquitetura do sistema |
| Fluxos de Trabalho | docs/projeto/fluxos_trabalho.md | Parcial | Desconhecida | Bom para entender os processos de negócio |
| Resumo das Implementações | Implementações/Resumo_Implementacoes.md | Atual | Desconhecida | Visão geral das implementações |
| README Principal | README.md | Parcial | Desconhecida | Introdução básica ao projeto |

## 2. Documentação de Banco de Dados

| Documento | Caminho | Status | Última Atualização | Recomendação |
|-----------|---------|--------|-------------------|--------------|
| Estrutura de Tabelas | docs/guias/bancodedados/estrutura_tabelas.md | Parcial | Desconhecida | Referência para estrutura de tabelas |
| Migração ENT Atlas | docs/guias/bancodedados/migracao_ent_atlas.md | Atual | Desconhecida | Guia para migração para ENT e Atlas |
| Relacionamentos | docs/guias/bancodedados/relacionamentos.md | Parcial | Desconhecida | Complementar à estrutura de tabelas |
| README Banco de Dados | docs/guias/bancodedados/README.md | Atual | Desconhecida | Visão geral do banco de dados |
| Informações do Banco | docs/database/tradicao_database_info.md | Desatualizado | Desconhecida | Contém informações potencialmente obsoletas |
| Migração de Filiais | docs/guias/bancodedados/migracao_filiais.md | Verificar | Desconhecida | Específico para migração de filiais |

## 3. Documentação de Frontend e Templates

| Documento | Caminho | Status | Última Atualização | Recomendação |
|-----------|---------|--------|-------------------|--------------|
| Guia Completo de Templates | docs/guias/templates/guia-completo-templates.md | Atual | Desconhecida | Referência principal para templates |
| README Templates | web/templates/README.md | Atual | Desconhecida | Regras para templates do Gin |
| Acessibilidade e Responsividade | docs/guias/templates/acessibilidade-responsividade.md | Atual | Desconhecida | Boas práticas para acessibilidade |
| Segurança Frontend | docs/guias/templates/seguranca-frontend.md | Atual | Desconhecida | Boas práticas de segurança frontend |
| README Dashboard | docs/guias/templates/dashboard/README.md | Atual | Desconhecida | Documentação do dashboard |
| README Galeria | docs/guias/templates/galeria/README.md | Atual | Desconhecida | Documentação da galeria |
| README Consulta | docs/guias/templates/consulta/README.md | Atual | Desconhecida | Documentação da consulta |
| README Login | docs/guias/templates/login/README.md | Atual | Desconhecida | Documentação do login |

## 4. Documentação de Sistemas e Implementações

| Documento | Caminho | Status | Última Atualização | Recomendação |
|-----------|---------|--------|-------------------|--------------|
| Sistema de Atribuição Automática | Implementações/Sistema_Atribuicao_Automatica/README.md | Atual | Desconhecida | Documentação do sistema de atribuição |
| Sistema de Autenticação | Implementações/Sistema_Autenticacao/README.md | Atual | Desconhecida | Documentação do sistema de autenticação |
| Sistema de Permissões | Implementações/Sistema_Permissoes/README.md | Atual | Desconhecida | Documentação do sistema de permissões |
| Sistema de Notificações | Implementações/Sistema_Notificacoes/README.md | Atual | Desconhecida | Documentação do sistema de notificações |
| Sistema de Gestão de Filiais | Implementações/Sistema_Gestao_Filiais/README.md | Atual | Desconhecida | Documentação do sistema de filiais |
| Sistema de Gestão de Equipamentos | Implementações/Sistema_Gestao_Equipamentos/README.md | Atual | Desconhecida | Documentação do sistema de equipamentos |
| Sistema de Ordens de Manutenção | Implementações/Sistema_Ordens_Manutencao/README.md | Atual | Desconhecida | Documentação do sistema de ordens |
| Sistema de Calendário | Implementações/Sistema_Calendario/README.md | Atual | Desconhecida | Documentação do sistema de calendário |
| Sistema Financeiro | Implementações/Sistema_Financeiro/README.md | Atual | Desconhecida | Documentação do sistema financeiro |

## 5. Documentação de Correções e Problemas Conhecidos

| Documento | Caminho | Status | Última Atualização | Recomendação |
|-----------|---------|--------|-------------------|--------------|
| Correções no Sistema de Técnicos | @Implementações/Sistema_Tecnicos/Correcoes_Sistema_Tecnicos.md | Atual | Desconhecida | Correções para problemas com técnicos |
| Correção de Ordens para Técnicos | Implementações/Correção_Ordens_Tecnico/Documentação_Implementação.md | Atual | Desconhecida | Correções para ordens de técnicos |
| Correção do Sistema de Permissões | @docs/guias/Sistema_Permissoes_Correcao.md | Atual | Desconhecida | Correções para o sistema de permissões |
| Guia de Diagnóstico e Resolução | web/templates/minhaconta/Guia de Diagnóstico e Resolução de Erros - Sistema de Alteração de Senha.md | Atual | Desconhecida | Troubleshooting para alteração de senha |

## 6. Documentação de Padronização e Convenções

| Documento | Caminho | Status | Última Atualização | Recomendação |
|-----------|---------|--------|-------------------|--------------|
| Padronização de Nomenclatura | docs/guias/padronizacao_nomenclatura.md | Atual | Desconhecida | Referência principal para nomenclatura |
| Estrutura de Documentação | Implementações/Estrutura_Documentacao.md | Atual | Desconhecida | Padrões para documentação |
| Regras de Desenvolvimento | docs/guias/regras-desenvolvimento.md | Atual | Desconhecida | Regras gerais de desenvolvimento |
| Padronização do Sistema de Filiais | Implementações/Sistema_Gestao_Filiais/Padronizacao_Sistema_Filiais.md | Atual | Desconhecida | Específico para padronização de filiais |

## 7. Documentação de Componentes Específicos

| Documento | Caminho | Status | Última Atualização | Recomendação |
|-----------|---------|--------|-------------------|--------------|
| Transferência de Equipamentos | docs/projeto/componentes/transferencia_equipamentos.md | Verificar | Desconhecida | Componente de transferência de equipamentos |
| Documentação Minha Conta | web/templates/minhaconta/DOCUMENTACAO_COMPLETA.md | Atual | Desconhecida | Documentação completa do módulo Minha Conta |
| Guia de Implementação Minha Conta | web/templates/minhaconta/GUIA_IMPLEMENTACAO.md | Atual | Desconhecida | Guia para implementação do módulo Minha Conta |
| Guia do Desenvolvedor Minha Conta | web/templates/minhaconta/GUIA_DESENVOLVEDOR.md | Atual | Desconhecida | Guia para desenvolvedores do módulo Minha Conta |

## 8. Documentação Duplicada ou Potencialmente Redundante

| Documento Original | Documento Duplicado | Recomendação |
|--------------------|---------------------|--------------|
| docs/README.md | @docs/README.md | Consolidar em docs/README.md |
| Implementações/Sistema_Permissoes/README.md | @docs/guias/Sistema_Permissoes_Implementacao.md | Verificar diferenças e consolidar |
| docs/guias/templates/dashboard/README.md | web/templates/dashboard/dashboard_new_doc.md | Verificar diferenças e consolidar |
| docs/guias/templates/galeria/README.md | web/templates/galeria/README.md | Verificar diferenças e consolidar |

## Próximos Passos Recomendados

1. **Verificação de Atualidade**: Determinar a data da última atualização de cada documento, possivelmente através do histórico de versão ou metadados do arquivo.

2. **Validação com Especialistas**: Validar o status de cada documento com especialistas no domínio correspondente.

3. **Atualização do Índice Central**: Criar um índice central que aponte para os documentos mais atualizados e relevantes.

4. **Plano de Consolidação**: Desenvolver um plano para consolidar documentos duplicados ou redundantes.

5. **Identificação de Lacunas**: Identificar áreas que carecem de documentação adequada.

6. **Padronização Gradual**: Implementar gradualmente a padronização da documentação, começando pelos documentos mais críticos.

## Notas Adicionais

- Este mapeamento é um documento vivo e deve ser atualizado conforme a documentação evolui.
- A consolidação de documentos deve ser feita com cautela, garantindo que informações importantes não sejam perdidas.
- Recomenda-se adicionar metadados (data de última atualização, autor, status) a todos os documentos para facilitar o gerenciamento futuro.
