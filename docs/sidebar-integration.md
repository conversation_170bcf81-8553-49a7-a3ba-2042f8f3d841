# Documentação de Integração da Sidebar

## Visão Geral

O menu lateral (sidebar) do Sistema Shell Tradição é implementado como um componente independente que pode ser incluído em qualquer página. Este documento descreve como a sidebar deve ser integrada corretamente nas páginas do sistema para evitar conflitos e comportamentos inesperados.

## Arquivos do Sistema de Sidebar

### Templates HTML
- **web/templates/layouts/sidebar.html**: Contém a estrutura HTML da sidebar e já inclui os arquivos CSS e JS necessários.
- **web/templates/layouts/base_sidemenu.html**: Contém uma versão alternativa do menu lateral com mais opções gráficas.

### Arquivos CSS
- **web/static/css/sidebar.css**: Estilos principais da sidebar.
- **web/static/css/sidebar-profile.css**: Estilos específicos para o componente de perfil de usuário na sidebar.
- **web/static/css/sidemenu.css**: Estilos para a versão alternativa do menu lateral.

### Arquivos JavaScript
- **web/static/js/sidebar.js**: Script principal que controla o comportamento da sidebar.
- **web/static/js/sidebar-fix.js**: ⚠️ NÃO UTILIZAR - Este script foi descontinuado e causa conflitos.

## Opções de Menu Lateral

O sistema oferece duas versões principais do menu lateral:

### 1. Menu Lateral Padrão (sidebar.html)
- **Arquivo**: `web/templates/layouts/sidebar.html`
- **Características**: 
  - Design minimalista
  - Suporta até 10-12 itens de menu
  - Inclui perfil de usuário
  - Pode ser colapsado/expandido
  - Usa ícones do Font Awesome
  - 3 seções separadas por divisores

### 2. Menu Lateral Avançado (base_sidemenu.html)
- **Arquivo**: `web/templates/layouts/base_sidemenu.html`
- **Características**:
  - Efeitos visuais adicionais (gradientes, animações)
  - Suporte para submenu
  - Barra de ícones no topo
  - Mais completo visualmente
  - Requer mais configuração

## Como Integrar Corretamente

### 1. Incluir a Sidebar em uma Página

Para incluir a sidebar em uma página, use o seguinte código:

```html
{{ template "sidebar" . }}
```

Este código inclui automaticamente todos os arquivos CSS e JavaScript necessários para a sidebar funcionar corretamente.

### 2. Configurar a Área de Conteúdo

O conteúdo da página deve estar dentro de uma div com a classe `content-with-sidebar`:

```html
<div class="content-with-sidebar">
    <div class="main-content">
        <!-- Conteúdo da página aqui -->
    </div>
</div>
```

### 3. Regras CSS Importantes

A classe `.content-with-sidebar` deve ter o seguinte CSS para posicionar o conteúdo corretamente:

```css
.content-with-sidebar {
    margin-left: var(--sidebar-width);
    width: calc(100% - var(--sidebar-width));
    transition: margin-left 0.3s ease, width 0.3s ease;
}

.tradicio-sidebar.collapsed + .content-with-sidebar,
.content-with-sidebar.collapsed {
    margin-left: var(--sidebar-collapsed-width);
    width: calc(100% - var(--sidebar-collapsed-width));
}
```

## Problemas Comuns e Soluções

### Sidebar Abrindo e Fechando Sozinha

Esse problema ocorre quando:
1. Múltiplos scripts JavaScript estão manipulando a sidebar simultaneamente.
2. Há conflitos no localStorage onde o estado da sidebar é armazenado.

**Soluções:**
- Certifique-se de que apenas o arquivo `sidebar.js` está sendo carregado.
- Não inclua `sidebar-fix.js` em nenhuma página.
- Se o problema persistir, adicione código para limpar o localStorage:
  ```javascript
  localStorage.removeItem('sidebar-collapsed');
  localStorage.removeItem('sidebar-state-' + userRole);
  ```

### Menu Lateral Não Responde ou Não Colapsa

Verifique se:
1. O ID `sidebarToggle` está presente no botão toggle.
2. As classes CSS estão aplicadas corretamente.
3. O evento de clique está sendo registrado no JavaScript.

### Visual do Menu Quebrado ou Incorreto

Se o menu lateral estiver com o visual incorreto:
1. Verifique se não há estilos inline no HTML sobrescrevendo os estilos dos arquivos CSS.
2. Garanta que todas as classes estão corretas.
3. Verifique a ordem de carregamento dos arquivos CSS.

## Modificações no Comportamento da Sidebar

Se for necessário modificar o comportamento da sidebar:

1. **NÃO crie scripts adicionais** - modifique o arquivo `sidebar.js` existente.
2. **NÃO utilize múltiplos arquivos CSS** - faça modificações nos arquivos existentes.
3. **NÃO adicione estilos inline** que possam conflitar com os estilos existentes.
4. **Documente mudanças importantes** neste arquivo.

## Histórico de Problemas Resolvidos

### Abril 2025 - Conflito de Scripts

**Problema:** O menu lateral abria e fechava automaticamente ao carregar a página.

**Causa:** Dashboard carregava `sidebar-fix.js` enquanto o template `sidebar.html` já carregava `sidebar.js`, criando conflito.

**Solução:**
1. Remoção do script `sidebar-fix.js` da página dashboard.
2. Adição de código para limpar entradas conflitantes no localStorage.
3. Documentação do processo de integração da sidebar.

### Abril 2025 - Restauração do Menu Completo

**Problema:** O menu lateral tinha apenas 3 itens (versão minimalista).

**Causa:** Versão simplificada do menu foi implementada em `sidebar.html`.

**Solução:**
1. Restauração da versão anterior do menu com mais itens.
2. Remoção de estilos inline que causavam conflitos visuais.
3. Atualização da documentação com informações sobre as diferentes versões disponíveis. 