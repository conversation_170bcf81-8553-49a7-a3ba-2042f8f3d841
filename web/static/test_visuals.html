<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Visual - Tema Shell (Styles.css)</title>
    <!-- <PERSON> para o CSS principal do tema (styles.css) -->
    <link rel="stylesheet" href="css/styles.css">
    <!-- <PERSON> para Bootstrap (se não importado dentro de styles.css) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Link para Font Awesome (se não importado dentro de styles.css) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        /* Estilos apenas para espaçamento/layout desta página de teste */
        body {
            padding: 30px;
            background-color: #f8f9fa; /* Fundo claro base para contraste inicial */
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .test-section h2 {
            border-bottom: 1px solid #ccc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
         /* Aplicar tema escuro se a classe estiver no body */
         body.dark-theme {
             background-color: var(--shell-dark, #333);
             color: var(--shell-light, #f8f9fa);
         }
         body.dark-theme .test-section {
            border: 1px solid #444;
            background-color: #222;
         }
         body.dark-theme h2 {
            color: var(--shell-yellow, #FDB813);
            border-bottom: 1px solid #555;
         }
          body.dark-theme label {
              color: var(--shell-light, #f8f9fa);
          }
    </style>
</head>
<!-- Adicione a classe 'dark-theme' ao body manualmente para testar o tema escuro -->
<!-- <body class="dark-theme"> -->
<body class="dark-theme">

    <h1>Página de Teste Visual (Baseado em styles.css)</h1>
    <p>Teste adicionando a classe <code>dark-theme</code> à tag <code>&lt;body&gt;</code> para ver a versão escura.</p>

    <div class="test-section">
        <h2>Botões</h2>
        <button class="btn btn-shell-red mb-2">Botão Shell Red (.btn-shell-red)</button>
        <button class="btn btn-shell-yellow mb-2">Botão Shell Yellow (.btn-shell-yellow)</button>
        <hr>
        <p>Botões Bootstrap Padrão (para comparação):</p>
        <button class="btn btn-primary mb-2">Primary</button>
        <button class="btn btn-secondary mb-2">Secondary</button>
        <button class="btn btn-success mb-2">Success</button>
        <button class="btn btn-danger mb-2">Danger</button>
        <button class="btn btn-warning mb-2">Warning</button>
        <button class="btn btn-info mb-2">Info</button>
        <button class="btn btn-light mb-2">Light</button>
        <button class="btn btn-dark mb-2">Dark</button>
        <button class="btn btn-link mb-2">Link</button>
    </div>

    <div class="test-section">
        <h2>Campos de Formulário</h2>
        <div class="mb-3">
            <label for="text-input" class="form-label">Campo de Texto:</label>
            <input type="text" id="text-input" class="form-control" placeholder="Digite algo aqui...">
        </div>
        <div class="mb-3">
            <label for="password-input" class="form-label">Campo de Senha:</label>
            <input type="password" id="password-input" class="form-control" placeholder="Digite sua senha...">
        </div>
        <div class="mb-3">
             <label for="select-input" class="form-label">Seleção:</label>
             <select id="select-input" class="form-select">
                 <option value="">-- Escolha uma opção --</option>
                 <option value="1">Opção 1</option>
                 <option value="2">Opção 2</option>
                 <option value="3">Opção 3</option>
             </select>
        </div>
         <div class="mb-3">
             <label class="form-label">Caixas de Seleção:</label>
             <div class="form-check">
                 <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                 <label class="form-check-label" for="flexCheckDefault">
                     Seleção A
                 </label>
             </div>
             <div class="form-check">
                 <input class="form-check-input" type="checkbox" value="" id="flexCheckChecked" checked>
                 <label class="form-check-label" for="flexCheckChecked">
                     Seleção B (Marcada)
                 </label>
             </div>
         </div>
         <div class="mb-3">
             <label class="form-label">Botões de Rádio:</label>
             <div class="form-check">
                 <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault1">
                 <label class="form-check-label" for="flexRadioDefault1">
                     Opção X
                 </label>
             </div>
             <div class="form-check">
                 <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault2" checked>
                 <label class="form-check-label" for="flexRadioDefault2">
                     Opção Y (Selecionada)
                 </label>
             </div>
         </div>
    </div>

    <div class="test-section">
        <h2>Card (estilo .card-shell)</h2>
        <div class="card card-shell" style="width: 18rem;">
            <div class="card-header">
                Cabeçalho Amarelo
            </div>
            <div class="card-body">
                <h5 class="card-title">Título do Card</h5>
                <p class="card-text">Este é um exemplo de card usando a classe <code>.card-shell</code> definida em styles.css.</p>
                <a href="#" class="btn btn-shell-red">Botão Shell Red</a>
            </div>
            <div class="card-footer">
                Rodapé Claro
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>Alertas</h2>
        <div class="alert alert-shell" role="alert">
          Alerta Shell Amarelo (.alert-shell)
        </div>
        <div class="alert alert-shell-danger" role="alert">
          Alerta Shell Danger (.alert-shell-danger)
        </div>
         <div class="alert alert-primary" role="alert">
           Alerta Bootstrap Padrão Primary
         </div>
    </div>

     <div class="test-section">
         <h2>Badges</h2>
         <span class="badge badge-shell">Badge Shell (.badge-shell)</span>
         <span class="badge badge-shell-red">Badge Shell Red (.badge-shell-red)</span>
         <span class="badge bg-secondary">Badge Bootstrap</span>
     </div>

     <div class="test-section">
         <h2>Spinner</h2>
         <div class="shell-spinner"></div>
     </div>

    <!-- ============================================== -->
    <!-- Componentes Adicionados do Dashboard          -->
    <!-- ============================================== -->

    <div class="test-section">
        <h2>Stats Cards (Dashboard)</h2>
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card stats-card-pending">
                    <div class="stats-card-title">Pendentes</div>
                    <div class="stats-card-value">5</div>
                    <div class="stats-card-trend">
                        <i class="fas fa-arrow-up me-1 trend-up"></i> 8% desde ontem
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card stats-card-progress">
                    <div class="stats-card-title">Em Andamento</div>
                    <div class="stats-card-value">8</div>
                    <div class="stats-card-trend">
                        <i class="fas fa-arrow-up me-1 trend-up"></i> 12% desde ontem
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card stats-card-completed">
                    <div class="stats-card-title">Concluídas</div>
                    <div class="stats-card-value">12</div>
                    <div class="stats-card-trend">
                        <i class="fas fa-arrow-up me-1 trend-up"></i> 15% desde ontem
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card stats-card-rejected">
                    <div class="stats-card-title">Canceladas</div>
                    <div class="stats-card-value">2</div>
                    <div class="stats-card-trend">
                        <i class="fas fa-arrow-down me-1 trend-down"></i> 5% desde ontem
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>Shell Header (Dashboard)</h2>
        <div class="shell-header">
            <h4>Ordens de Serviço Abertas</h4>
        </div>
    </div>

    <div class="test-section">
        <h2>Service Order Item Card (Dashboard)</h2>
        <div class="service-orders-grid">
             <div class="service-order-item" data-status="pending" data-priority="urgent">
                 <div class="order-priority urgent" title="Prioridade: Urgente"></div>
                 <div class="order-title">OS-2025-004: Vazamento no Tanque</div>
                 <div class="order-status pending">Pendente</div>
                 <div class="order-info">
                     <div><i class="fas fa-map-marker-alt"></i> Posto Shell Guarulhos</div>
                     <div><i class="fas fa-calendar-alt"></i> 03/04/2025</div>
                 </div>
             </div>
             <div class="service-order-item" data-status="completed" data-priority="low">
                 <div class="order-priority low" title="Prioridade: Baixa"></div>
                 <div class="order-title">OS-2025-003: Reparo Loja</div>
                 <div class="order-status completed">Concluída</div>
                 <div class="order-info">
                     <div><i class="fas fa-map-marker-alt"></i> Posto Shell Morumbi</div>
                     <div><i class="fas fa-calendar-alt"></i> 01/04/2025</div>
                 </div>
             </div>
              <div class="service-order-item" data-status="in-progress" data-priority="medium">
                 <div class="order-priority medium" title="Prioridade: Média"></div>
                 <div class="order-title">OS-2025-002: Calibração</div>
                 <div class="order-status in-progress">Em Andamento</div>
                 <div class="order-info">
                     <div><i class="fas fa-map-marker-alt"></i> Posto Shell Centro</div>
                     <div><i class="fas fa-calendar-alt"></i> 02/04/2025</div>
                 </div>
             </div>
        </div>
    </div>

    <!-- ===================================================== -->
    <!-- Componentes Adicionados do Calendário (Adaptados)    -->
    <!-- ===================================================== -->

    <div class="test-section">
        <h2>Botão Voltar (Adaptado do Calendário)</h2>
        <button class="btn-shell-back">
            <i class="fas fa-arrow-left"></i> Voltar
        </button>
    </div>

    <div class="test-section">
        <h2>List Item Card (Adaptado do Calendário)</h2>
        <div class="list-item-card status-urgent mb-2">
            <div class="list-item-content">
                <div class="item-title">Revisão Urgente (Urgente)</div>
                <div class="item-details">
                    <i class="fas fa-calendar-alt"></i> 25/03/2025 - <i class="fas fa-clock"></i> 10:00h
                    <br><i class="fas fa-map-marker-alt"></i> Posto Shell Centro
                </div>
            </div>
        </div>
        <div class="list-item-card status-warning mb-2">
             <div class="list-item-content">
                <div class="item-title">Verificar Estoque (Warning)</div>
                 <div class="item-details">
                     <i class="fas fa-info-circle"></i> Checar níveis antes das 14h
                 </div>
            </div>
        </div>
        <div class="list-item-card status-success mb-2">
            <div class="list-item-content">
                <div class="item-title">Manutenção Concluída (Success)</div>
                 <div class="item-details">
                     <i class="fas fa-check-circle"></i> Bomba #2 OK.
                 </div>
            </div>
        </div>
        <div class="list-item-card status-scheduled mb-2">
            <div class="list-item-content">
                 <div class="item-title">Limpeza Agendada (Scheduled - Red)</div>
                 <div class="item-details">
                     <i class="fas fa-calendar-alt"></i> 30/04/2025
                 </div>
             </div>
        </div>
         <div class="list-item-card">
            <div class="list-item-content">
                 <div class="item-title">Item Padrão (Info/Azul)</div>
                 <div class="item-details">
                     <i class="fas fa-info"></i> Detalhe informativo.
                 </div>
             </div>
        </div>
    </div>

    <!-- Adicionar JS do Bootstrap se for usar componentes interativos -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 