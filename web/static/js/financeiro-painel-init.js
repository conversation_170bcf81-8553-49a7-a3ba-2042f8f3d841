/**
 * Inicialização e eventos do painel financeiro
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOMContentLoaded - Iniciando inicialização do painel financeiro');

    // Inicializa os contadores animados
    console.log('Iniciando contadores...');
    initCounters();

    // Inicializa os gráficos
    console.log('Iniciando gráficos...');
    initCharts();

    // Adiciona eventos aos cards de ferramentas
    console.log('Iniciando cards de ferramentas...');
    initToolCards();

    // Adiciona eventos aos botões de ação
    console.log('Iniciando botões de ação...');
    initActionButtons();
});

/**
 * Inicializa os contadores animados
 */
function initCounters() {
    const counters = document.querySelectorAll('.counter-value');

    counters.forEach(counter => {
        const target = parseFloat(counter.getAttribute('data-target'));
        const prefix = counter.getAttribute('data-prefix') || '';
        const suffix = counter.getAttribute('data-suffix') || '';
        const duration = 2000; // duração da animação em ms
        const decimals = counter.getAttribute('data-decimals') || 0;
        const startTimestamp = performance.now();

        function updateCounter(timestamp) {
            const progress = Math.min((timestamp - startTimestamp) / duration, 1);
            const easeProgress = progress === 1 ? 1 : 1 - Math.pow(2, -10 * progress);
            const currentValue = target * easeProgress;

            counter.textContent = `${prefix}${currentValue.toFixed(decimals)}${suffix}`;

            if (progress < 1) {
                requestAnimationFrame(updateCounter);
            }
        }

        requestAnimationFrame(updateCounter);
    });
}

/**
 * Inicializa os gráficos do painel
 */
function initCharts() {
    // Gráfico de tempo de parada
    const downtimeCtx = document.getElementById('downtimeChart');
    if (downtimeCtx) {
        new Chart(downtimeCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
                datasets: [{
                    label: 'Tempo de Parada (horas)',
                    data: [12, 19, 8, 15, 10, 6],
                    backgroundColor: 'rgba(237, 28, 36, 0.2)',
                    borderColor: '#ED1C24',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: '#ED1C24',
                    pointBorderColor: '#ED1C24',
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            color: '#ccc',
                            font: {
                                family: "'Rajdhani', sans-serif",
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: '#333',
                        titleColor: '#ED1C24',
                        bodyColor: '#fff',
                        borderColor: '#555',
                        borderWidth: 1
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#aaa'
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#aaa'
                        }
                    }
                }
            }
        });
    }

    // Gráfico de payoff de equipamentos
    const payoffCtx = document.getElementById('payoffChart');
    if (payoffCtx) {
        new Chart(payoffCtx, {
            type: 'bar',
            data: {
                labels: ['Bombas', 'Compressores', 'Geradores', 'Tanques', 'Filtros'],
                datasets: [{
                    label: 'Tempo de Payoff (meses)',
                    data: [18, 24, 36, 48, 12],
                    backgroundColor: [
                        'rgba(253, 184, 19, 0.7)',
                        'rgba(237, 28, 36, 0.7)',
                        'rgba(2, 117, 216, 0.7)',
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(111, 66, 193, 0.7)'
                    ],
                    borderColor: [
                        '#FDB813',
                        '#ED1C24',
                        '#0275d8',
                        '#28a745',
                        '#6f42c1'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: '#333',
                        titleColor: '#FDB813',
                        bodyColor: '#fff',
                        borderColor: '#555',
                        borderWidth: 1
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#aaa'
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#aaa'
                        }
                    }
                }
            }
        });
    }

    // Gráfico de custo médio por tipo
    const costCtx = document.getElementById('costChart');
    if (costCtx) {
        new Chart(costCtx, {
            type: 'doughnut',
            data: {
                labels: ['Bombas', 'Compressores', 'Geradores', 'Tanques', 'Filtros'],
                datasets: [{
                    data: [35, 25, 20, 15, 5],
                    backgroundColor: [
                        '#FDB813',
                        '#ED1C24',
                        '#0275d8',
                        '#28a745',
                        '#6f42c1'
                    ],
                    borderColor: '#333',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            color: '#ccc',
                            font: {
                                family: "'Rajdhani', sans-serif",
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: '#333',
                        titleColor: '#FDB813',
                        bodyColor: '#fff',
                        borderColor: '#555',
                        borderWidth: 1
                    }
                }
            }
        });
    }
}

/**
 * Inicializa os cards de ferramentas
 */
function initToolCards() {
    console.log('Procurando cards de ferramentas...');
    const toolCards = document.querySelectorAll('.finance-tool-card');
    console.log('Cards encontrados:', toolCards.length);

    toolCards.forEach(card => {
        console.log('Adicionando evento ao card:', card.getAttribute('data-tool'));
        card.addEventListener('click', function() {
            console.log('Card clicado:', this.getAttribute('data-tool'));
            const tool = this.getAttribute('data-tool');
            showToolModal(tool);
        });
    });
}

/**
 * Inicializa os botões de ação
 */
function initActionButtons() {
    console.log('Iniciando botões de ação...');

    // Botão de atualizar dados
    const refreshBtn = document.getElementById('refreshDataBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            console.log('Atualizando dados...');
            refreshDashboardData();
        });
    }

    // Botão de ver todos os pagamentos
    const viewAllPaymentsBtn = document.getElementById('viewAllPaymentsBtn');
    if (viewAllPaymentsBtn) {
        viewAllPaymentsBtn.addEventListener('click', function() {
            console.log('Abrindo lista de pagamentos...');
            showAllPayments();
        });
    }

    // Botão de ver todas as preventivas
    const viewAllPreventivesBtn = document.getElementById('viewAllPreventivesBtn');
    if (viewAllPreventivesBtn) {
        viewAllPreventivesBtn.addEventListener('click', function() {
            console.log('Abrindo lista de preventivas...');
            showAllPreventives();
        });
    }

    // Botão de ver relatório completo
    const viewAllMetricsBtn = document.getElementById('viewAllMetricsBtn');
    if (viewAllMetricsBtn) {
        viewAllMetricsBtn.addEventListener('click', function() {
            console.log('Abrindo relatório completo...');
            showFullReport();
        });
    }

    // Botões de processamento de pagamento
    const processPaymentBtns = document.querySelectorAll('.payment-footer .btn-shell-red');
    processPaymentBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            console.log('Processando pagamento...');
            const card = this.closest('.payment-card');
            const amount = card.querySelector('.payment-amount').textContent;
            const title = card.querySelector('.payment-title').textContent;
            processPayment(amount, title);
        });
    });

    // Botões de detalhes de pagamento
    const paymentDetailsBtns = document.querySelectorAll('.payment-footer .btn-outline-shell');
    paymentDetailsBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            console.log('Abrindo detalhes do pagamento...');
            const card = this.closest('.payment-card');
            const title = card.querySelector('.payment-title').textContent;
            showPaymentDetails(title);
        });
    });

    // Botões de preparar orçamento
    const prepareBudgetBtns = document.querySelectorAll('.preventive-footer .btn-shell-yellow');
    prepareBudgetBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            console.log('Preparando orçamento...');
            const card = this.closest('.preventive-card');
            const title = card.querySelector('.preventive-title').textContent;
            const estimate = card.querySelector('.preventive-estimate').textContent;
            prepareBudget(title, estimate);
        });
    });

    // Botões de detalhes de preventiva
    const preventiveDetailsBtns = document.querySelectorAll('.preventive-footer .btn-outline-shell');
    preventiveDetailsBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            console.log('Abrindo detalhes da preventiva...');
            const card = this.closest('.preventive-card');
            const title = card.querySelector('.preventive-title').textContent;
            showPreventiveDetails(title);
        });
    });

    // Botões de análise detalhada
    const detailedAnalysisBtns = document.querySelectorAll('.metrics-footer .btn-outline-shell');
    detailedAnalysisBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            console.log('Abrindo análise detalhada...');
            const card = this.closest('.metrics-card');
            const title = card.querySelector('.metrics-title').textContent;
            showDetailedAnalysis(title);
        });
    });

    // Inicializa o DateRangePicker
    const dateRangePicker = document.getElementById('dateRangePicker');
    if (dateRangePicker) {
        $(dateRangePicker).daterangepicker({
            startDate: moment().subtract(29, 'days'),
            endDate: moment(),
            ranges: {
               'Hoje': [moment(), moment()],
               'Ontem': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
               'Últimos 7 dias': [moment().subtract(6, 'days'), moment()],
               'Últimos 30 dias': [moment().subtract(29, 'days'), moment()],
               'Este mês': [moment().startOf('month'), moment().endOf('month')],
               'Mês passado': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            },
            locale: {
                format: 'DD/MM/YYYY',
                applyLabel: 'Aplicar',
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período personalizado'
            }
        }, function(start, end) {
            console.log('Período selecionado:', start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'));
            updateDashboardData(start, end);
        });
    }
}

/**
 * Fecha o modal
 * @param {HTMLElement} modal - Elemento do modal
 */
function closeModal(modal) {
    modal.style.opacity = '0';
    modal.querySelector('.tool-modal-content').style.transform = 'translateY(20px)';

    setTimeout(() => {
        document.body.removeChild(modal);
    }, 300);
}

/**
 * Mostra o modal da ferramenta selecionada
 * @param {string} tool - Nome da ferramenta
 */
function showToolModal(tool) {
    console.log('Mostrando modal para ferramenta:', tool);

    // Cria o modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = `toolModal-${tool}`;
    modal.setAttribute('tabindex', '-1');
    modal.setAttribute('role', 'dialog');
    modal.setAttribute('aria-labelledby', `toolModalLabel-${tool}`);
    modal.setAttribute('aria-hidden', 'true');

    // Conteúdo do modal baseado na ferramenta
    let modalContent = '';
    console.log('Criando conteúdo do modal para:', tool);

    switch(tool) {
        case 'cotacoes':
            console.log('Criando conteúdo de cotações');
            modalContent = createCotacoesContent();
            break;
        case 'calculadora':
            console.log('Criando conteúdo da calculadora');
            modalContent = createCalculadoraContent();
            break;
        case 'impostos':
            console.log('Criando conteúdo de impostos');
            modalContent = createImpostosContent();
            break;
        case 'historico':
            console.log('Criando conteúdo de histórico');
            modalContent = '<div class="alert alert-info">Conteúdo do histórico de preços em desenvolvimento.</div>';
            break;
        case 'noticias':
            console.log('Criando conteúdo de notícias');
            modalContent = createNoticiasContent();
            break;
        case 'relatorios':
            console.log('Criando conteúdo de relatórios');
            modalContent = createRelatoriosContent();
            break;
    }

    // Estrutura do modal
    modal.innerHTML = `
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="toolModalLabel-${tool}">
                        ${getToolTitle(tool)}
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    ${modalContent}
                </div>
            </div>
        </div>
    `;

    // Adiciona o modal ao body
    console.log('Adicionando modal ao body');
    document.body.appendChild(modal);

    // Inicializa o modal do Bootstrap
    console.log('Inicializando modal do Bootstrap');
    $(modal).modal('show');

    // Remove o modal quando fechado
    $(modal).on('hidden.bs.modal', function() {
        console.log('Modal fechado, removendo do DOM');
        document.body.removeChild(modal);
    });
}

/**
 * Retorna o título da ferramenta
 * @param {string} tool - Nome da ferramenta
 * @returns {string} - Título da ferramenta
 */
function getToolTitle(tool) {
    const titles = {
        'cotacoes': 'Cotações',
        'calculadora': 'Calculadora de Margem',
        'impostos': 'Impostos',
        'historico': 'Histórico de Preços',
        'noticias': 'Notícias do Mercado',
        'relatorios': 'Relatórios'
    };
    return titles[tool] || tool;
}

/**
 * Atualiza os dados do dashboard
 * @param {moment} start - Data inicial
 * @param {moment} end - Data final
 */
function updateDashboardData(start, end) {
    console.log('Atualizando dados do dashboard...');
    // TODO: Implementar chamada à API para atualizar dados
    showLoadingSpinner();
    setTimeout(() => {
        hideLoadingSpinner();
        showSuccessMessage('Dados atualizados com sucesso!');
    }, 1500);
}

/**
 * Atualiza os dados do dashboard
 */
function refreshDashboardData() {
    console.log('Atualizando dados...');
    showLoadingSpinner();
    setTimeout(() => {
        hideLoadingSpinner();
        showSuccessMessage('Dados atualizados com sucesso!');
    }, 1500);
}

/**
 * Mostra todos os pagamentos
 */
function showAllPayments() {
    console.log('Mostrando todos os pagamentos...');
    window.location.href = '/financeiro/pagamentos';
}

/**
 * Mostra todas as preventivas
 */
function showAllPreventives() {
    console.log('Mostrando todas as preventivas...');
    window.location.href = '/financeiro/preventivas';
}

/**
 * Mostra o relatório completo
 */
function showFullReport() {
    console.log('Mostrando relatório completo...');
    window.location.href = '/financeiro/relatorios';
}

/**
 * Processa um pagamento
 * @param {string} amount - Valor do pagamento
 * @param {string} title - Título do pagamento
 */
function processPayment(amount, title) {
    console.log('Processando pagamento:', { amount, title });
    showModal('Processar Pagamento', `
        <div class="payment-process-form">
            <h4>${title}</h4>
            <p>Valor: ${amount}</p>
            <div class="form-group">
                <label>Método de Pagamento</label>
                <select class="form-control">
                    <option>Transferência Bancária</option>
                    <option>Boleto</option>
                    <option>Cartão de Crédito</option>
                    <option>PIX</option>
                </select>
            </div>
            <div class="form-group">
                <label>Data de Pagamento</label>
                <input type="date" class="form-control">
            </div>
            <div class="form-actions">
                <button class="btn-shell-red">Confirmar Pagamento</button>
                <button class="btn-outline-shell" data-dismiss="modal">Cancelar</button>
            </div>
        </div>
    `);
}

/**
 * Mostra os detalhes de um pagamento
 * @param {string} title - Título do pagamento
 */
function showPaymentDetails(title) {
    console.log('Mostrando detalhes do pagamento:', title);
    showModal('Detalhes do Pagamento', `
        <div class="payment-details-content">
            <h4>${title}</h4>
            <div class="details-section">
                <h5>Informações do Serviço</h5>
                <p>Data do Serviço: 25/03/2025</p>
                <p>Responsável: João Silva</p>
                <p>Status: Aguardando Pagamento</p>
            </div>
            <div class="details-section">
                <h5>Histórico</h5>
                <ul>
                    <li>25/03/2025 - Serviço Realizado</li>
                    <li>26/03/2025 - Nota Fiscal Emitida</li>
                    <li>27/03/2025 - Aprovação Pendente</li>
                </ul>
            </div>
        </div>
    `);
}

/**
 * Prepara um orçamento
 * @param {string} title - Título do orçamento
 * @param {string} estimate - Valor estimado
 */
function prepareBudget(title, estimate) {
    console.log('Preparando orçamento:', { title, estimate });
    showModal('Preparar Orçamento', `
        <div class="budget-form">
            <h4>${title}</h4>
            <p>Estimativa: ${estimate}</p>
            <div class="form-group">
                <label>Detalhamento dos Serviços</label>
                <textarea class="form-control" rows="4"></textarea>
            </div>
            <div class="form-group">
                <label>Prazo de Execução (dias)</label>
                <input type="number" class="form-control" min="1">
            </div>
            <div class="form-actions">
                <button class="btn-shell-yellow">Gerar Orçamento</button>
                <button class="btn-outline-shell" data-dismiss="modal">Cancelar</button>
            </div>
        </div>
    `);
}

/**
 * Mostra os detalhes de uma preventiva
 * @param {string} title - Título da preventiva
 */
function showPreventiveDetails(title) {
    console.log('Mostrando detalhes da preventiva:', title);
    showModal('Detalhes da Preventiva', `
        <div class="preventive-details-content">
            <h4>${title}</h4>
            <div class="details-section">
                <h5>Escopo do Serviço</h5>
                <ul>
                    <li>Inspeção de Bombas</li>
                    <li>Calibração de Sensores</li>
                    <li>Limpeza de Filtros</li>
                    <li>Verificação de Conexões</li>
                </ul>
            </div>
            <div class="details-section">
                <h5>Histórico de Manutenções</h5>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Data</th>
                            <th>Serviço</th>
                            <th>Custo</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Jan/2025</td>
                            <td>Manutenção Preventiva</td>
                            <td>R$ 9.650,00</td>
                        </tr>
                        <tr>
                            <td>Out/2024</td>
                            <td>Manutenção Preventiva</td>
                            <td>R$ 10.200,00</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    `);
}

/**
 * Mostra a análise detalhada
 * @param {string} title - Título da análise
 */
function showDetailedAnalysis(title) {
    console.log('Mostrando análise detalhada:', title);
    showModal('Análise Detalhada', `
        <div class="analysis-content">
            <h4>${title}</h4>
            <div class="analysis-section">
                <h5>Indicadores</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="indicator">
                            <div class="indicator-label">Média</div>
                            <div class="indicator-value">4.2h</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="indicator">
                            <div class="indicator-label">Desvio</div>
                            <div class="indicator-value">±0.8h</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="indicator">
                            <div class="indicator-label">Tendência</div>
                            <div class="indicator-value negative">↑ 5%</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="analysis-section">
                <h5>Gráfico Detalhado</h5>
                <canvas id="detailedChart" width="100%" height="300"></canvas>
            </div>
        </div>
    `);
}

/**
 * Mostra um modal
 * @param {string} title - Título do modal
 * @param {string} content - Conteúdo HTML do modal
 */
function showModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">${title}</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    $(modal).modal('show');
    $(modal).on('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

/**
 * Mostra o spinner de carregamento
 */
function showLoadingSpinner() {
    const spinner = document.createElement('div');
    spinner.className = 'loading-overlay';
    spinner.innerHTML = `
        <div class="spinner-border text-light" role="status">
            <span class="sr-only">Carregando...</span>
        </div>
    `;
    document.body.appendChild(spinner);
}

/**
 * Esconde o spinner de carregamento
 */
function hideLoadingSpinner() {
    const spinner = document.querySelector('.loading-overlay');
    if (spinner) {
        document.body.removeChild(spinner);
    }
}

/**
 * Mostra uma mensagem de sucesso
 * @param {string} message - Mensagem a ser exibida
 */
function showSuccessMessage(message) {
    const toast = document.createElement('div');
    toast.className = 'toast-message success';
    toast.innerHTML = `
        <i class="fas fa-check-circle"></i>
        <span>${message}</span>
    `;
    document.body.appendChild(toast);
    setTimeout(() => {
        toast.classList.add('show');
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }, 100);
}
