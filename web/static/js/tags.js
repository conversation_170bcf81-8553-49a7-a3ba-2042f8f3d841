document.addEventListener('DOMContentLoaded', function() {
    // Gerenciamento de Categorias
    const categoryModal = new bootstrap.Modal(document.getElementById('categoryModal'));
    
    document.getElementById('saveCategory').addEventListener('click', async function() {
        const categoryId = document.getElementById('categoryId').value;
        const categoryData = {
            name: document.getElementById('categoryName').value,
            description: document.getElementById('categoryDescription').value
        };

        try {
            const response = await fetch(`/api/tags/categories${categoryId ? '/' + categoryId : ''}`, {
                method: categoryId ? 'PUT' : 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(categoryData)
            });

            if (response.ok) {
                showToast('success', 'Categoria salva com sucesso!');
                setTimeout(() => location.reload(), 1500);
            } else {
                showToast('error', 'Erro ao salvar categoria');
            }
        } catch (error) {
            console.error('Erro:', error);
            showToast('error', 'Erro ao salvar categoria');
        }
    });

    // Gerenciamento de Tags
    const tagModal = new bootstrap.Modal(document.getElementById('tagModal'));
    
    document.getElementById('saveTag').addEventListener('click', async function() {
        const tagId = document.getElementById('tagId').value;
        const tagData = {
            name: document.getElementById('tagName').value,
            category_id: document.getElementById('tagCategory').value,
            description: document.getElementById('tagDescription').value,
            color: document.getElementById('tagColor').value
        };

        try {
            const response = await fetch(`/api/tags${tagId ? '/' + tagId : ''}`, {
                method: tagId ? 'PUT' : 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(tagData)
            });

            if (response.ok) {
                showToast('success', 'Tag salva com sucesso!');
                setTimeout(() => location.reload(), 1500);
            } else {
                showToast('error', 'Erro ao salvar tag');
            }
        } catch (error) {
            console.error('Erro:', error);
            showToast('error', 'Erro ao salvar tag');
        }
    });

    // Editar Tag
    document.querySelectorAll('.edit-tag').forEach(button => {
        button.addEventListener('click', async function() {
            const tagId = this.dataset.tagId;
            try {
                const response = await fetch(`/api/tags/${tagId}`);
                if (response.ok) {
                    const tag = await response.json();
                    document.getElementById('tagId').value = tag.id;
                    document.getElementById('tagName').value = tag.name;
                    document.getElementById('tagCategory').value = tag.category_id;
                    document.getElementById('tagDescription').value = tag.description;
                    document.getElementById('tagColor').value = tag.color;
                    tagModal.show();
                }
            } catch (error) {
                console.error('Erro:', error);
                showToast('error', 'Erro ao carregar tag');
            }
        });
    });

    // Deletar Tag
    document.querySelectorAll('.delete-tag').forEach(button => {
        button.addEventListener('click', async function() {
            const tagId = this.dataset.tagId;
            
            Swal.fire({
                title: 'Confirmar Exclusão',
                text: 'Tem certeza que deseja excluir esta tag?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ED1C24',
                cancelButtonColor: '#808080',
                confirmButtonText: 'Sim, excluir',
                cancelButtonText: 'Cancelar'
            }).then(async (result) => {
                if (result.isConfirmed) {
                    try {
                        const response = await fetch(`/api/tags/${tagId}`, {
                            method: 'DELETE'
                        });

                        if (response.ok) {
                            showToast('success', 'Tag excluída com sucesso!');
                            setTimeout(() => location.reload(), 1500);
                        } else {
                            showToast('error', 'Erro ao excluir tag');
                        }
                    } catch (error) {
                        console.error('Erro:', error);
                        showToast('error', 'Erro ao excluir tag');
                    }
                }
            });
        });
    });

    // Filtrar Tags por Categoria
    document.querySelectorAll('#categoryList a').forEach(link => {
        link.addEventListener('click', async function(e) {
            e.preventDefault();
            const categoryId = this.dataset.categoryId;
            
            // Atualizar estado visual dos links
            document.querySelectorAll('#categoryList a').forEach(a => a.classList.remove('active'));
            this.classList.add('active');

            try {
                const response = await fetch(`/api/tags?category_id=${categoryId}`);
                if (response.ok) {
                    const tags = await response.json();
                    const tagList = document.getElementById('tagList');
                    
                    if (tags.length === 0) {
                        tagList.innerHTML = `
                            <div class="col-12">
                                <div class="alert alert-info">
                                    Nenhuma tag encontrada para esta categoria.
                                </div>
                            </div>
                        `;
                        return;
                    }

                    tagList.innerHTML = tags.map(tag => `
                        <div class="col-md-4 mb-3">
                            <div class="card card-shell">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="card-title mb-0">${tag.name}</h6>
                                        <div class="btn-group">
                                            <button class="shell-btn shell-btn-sm shell-btn-warning edit-tag" data-tag-id="${tag.id}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="shell-btn shell-btn-sm shell-btn-danger delete-tag" data-tag-id="${tag.id}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <p class="card-text small text-muted">${tag.description || ''}</p>
                                    <span class="badge-shell" style="background-color: ${tag.color}">${tag.category.name}</span>
                                </div>
                            </div>
                        </div>
                    `).join('');

                    // Reativar os listeners nos novos botões
                    attachTagEventListeners();
                }
            } catch (error) {
                console.error('Erro:', error);
                showToast('error', 'Erro ao carregar tags');
            }
        });
    });
});

// Função para reativar os listeners após atualização dinâmica
function attachTagEventListeners() {
    // Reativar listeners de edição
    document.querySelectorAll('.edit-tag').forEach(button => {
        button.addEventListener('click', async function() {
            const tagId = this.dataset.tagId;
            try {
                const response = await fetch(`/api/tags/${tagId}`);
                if (response.ok) {
                    const tag = await response.json();
                    document.getElementById('tagId').value = tag.id;
                    document.getElementById('tagName').value = tag.name;
                    document.getElementById('tagCategory').value = tag.category_id;
                    document.getElementById('tagDescription').value = tag.description;
                    document.getElementById('tagColor').value = tag.color;
                    const tagModal = new bootstrap.Modal(document.getElementById('tagModal'));
                    tagModal.show();
                }
            } catch (error) {
                console.error('Erro:', error);
                showToast('error', 'Erro ao carregar tag');
            }
        });
    });

    // Reativar listeners de exclusão
    document.querySelectorAll('.delete-tag').forEach(button => {
        button.addEventListener('click', function() {
            const tagId = this.dataset.tagId;
            
            Swal.fire({
                title: 'Confirmar Exclusão',
                text: 'Tem certeza que deseja excluir esta tag?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ED1C24',
                cancelButtonColor: '#808080',
                confirmButtonText: 'Sim, excluir',
                cancelButtonText: 'Cancelar'
            }).then(async (result) => {
                if (result.isConfirmed) {
                    try {
                        const response = await fetch(`/api/tags/${tagId}`, {
                            method: 'DELETE'
                        });

                        if (response.ok) {
                            showToast('success', 'Tag excluída com sucesso!');
                            setTimeout(() => location.reload(), 1500);
                        } else {
                            showToast('error', 'Erro ao excluir tag');
                        }
                    } catch (error) {
                        console.error('Erro:', error);
                        showToast('error', 'Erro ao excluir tag');
                    }
                }
            });
        });
    });
} 