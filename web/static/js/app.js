/**
 * Aplicação principal
 * Gerencia autenticação, navegação e funcionalidades globais
 */
const app = (function() {
  // Configurações
  const config = {
    apiUrl: '/api',
    wsUrl: `ws://${window.location.host}/ws`,
    debug: true
  };
  
  // Estado da aplicação
  const state = {
    user: null,
    authenticated: false,
    loading: false,
    notifications: [],
    sidebarVisible: true
  };
  
  /**
   * Inicializa a aplicação
   */
  function init() {
    logger('Inicializando aplicação');
    
    // Verificar autenticação
    checkAuthentication()
      .then(() => {
        // Configurar listeners de eventos
        setupEventListeners();
        
        // Inicializar elementos da UI
        initUI();
        
        // Inicializar módulos
        initModules();
        
        // Destacar item ativo no menu
        highlightActiveMenuItem();
        
        // Notificar que a aplicação foi carregada
        document.dispatchEvent(new CustomEvent('app:loaded'));
      })
      .catch(error => {
        logger('Erro na inicialização:', error);
        handleAuthFailure();
      });
  }
  
  /**
   * Configura os listeners de eventos globais
   */
  function setupEventListeners() {
    // Event listener para links de página
    document.addEventListener('click', function(event) {
      const link = event.target.closest('a[data-page]');
      if (link && !link.hasAttribute('data-no-navigate')) {
        event.preventDefault();
        const page = link.getAttribute('data-page');
        navigateTo(page);
      }
    });
    
    // Listener para o botão toggle da sidebar
    const sidebarToggle = document.getElementById('sidebar-toggle');
    if (sidebarToggle) {
      sidebarToggle.addEventListener('click', toggleSidebar);
    }
    
    // Listener para botão de logout
    const logoutBtn = document.querySelector('.logout-btn');
    if (logoutBtn) {
      logoutBtn.addEventListener('click', function(e) {
        e.preventDefault();
        logout();
      });
    }
  }
  
  /**
   * Inicializa os módulos da aplicação
   */
  function initModules() {
    // Inicializar módulo de tema se existir
    if (window.themeModule) {
      themeModule.init(app);
    }
    
    // Carregar módulo específico para a página atual
    loadPageModule();
  }
  
  /**
   * Carrega o módulo específico para a página atual
   */
  function loadPageModule() {
    const body = document.body;
    const currentPage = body.getAttribute('data-page');
    
    if (!currentPage) return;
    
    // Mapear páginas para seus módulos
    const pageModules = {
      'dashboard': window.dashboardModule,
      'maintenance': window.maintenanceModule,
      'reports': window.reportsModule,
      'calendar': window.pumpCalendarModule
    };
    
    const module = pageModules[currentPage];
    if (module && typeof module.init === 'function') {
      logger(`Inicializando módulo para página: ${currentPage}`);
      module.init(app);
    }
  }
  
  /**
   * Verifica se o usuário está autenticado
   */
  function checkAuthentication() {
    return new Promise((resolve, reject) => {
      // Se estamos na página de login, não precisamos verificar autenticação
      if (window.location.pathname.includes('login')) {
        state.authenticated = false;
        return resolve();
      }
      
      // Verifica na API se o usuário está autenticado
      fetch(`${config.apiUrl}/user/me`)
        .then(response => {
          if (!response.ok) {
            throw new Error('Usuário não autenticado');
          }
          return response.json();
        })
        .then(data => {
          // Adaptação da resposta aos formatos possíveis
          // A API pode retornar { success, data, message } ou diretamente o objeto de usuário
          if (data.success && data.data) {
            // Formato success/data
            state.user = data.data;
            state.authenticated = true;
            updateUserInfo(state.user);
            resolve();
          } else if (data.id && data.email) {
            // API retornou o objeto de usuário diretamente
            state.user = data;
            state.authenticated = true;
            updateUserInfo(state.user);
            resolve();
          } else if (data.error || data.message) {
            // Resposta de erro
            throw new Error(data.error || data.message || 'Falha na autenticação');
          } else {
            // Formato desconhecido
            console.warn("Formato de resposta não reconhecido:", data);
            throw new Error('Resposta desconhecida do servidor');
          }
        })
        .catch(error => {
          state.authenticated = false;
          logger('Erro ao verificar autenticação:', error);
          reject(error);
        });
    });
  }
  
  /**
   * Inicializa elementos da UI
   */
  function initUI() {
    // Inicializar tooltips do Bootstrap se disponível
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
      const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
      tooltips.forEach(tooltip => new bootstrap.Tooltip(tooltip));
    }
  }
  
  /**
   * Destaca o item de menu ativo
   */
  function highlightActiveMenuItem() {
    const currentPage = document.body.getAttribute('data-page');
    if (!currentPage) return;
    
    const menuItems = document.querySelectorAll('.sidebar-menu a');
    menuItems.forEach(item => {
      item.classList.remove('active');
      
      const itemPage = item.getAttribute('data-page');
      if (itemPage === currentPage) {
        item.classList.add('active');
      }
    });
  }
  
  /**
   * Atualiza informações do usuário na UI
   */
  function updateUserInfo(user) {
    if (!user) return;
    
    // Atualizar nome do usuário na sidebar
    const userNameElement = document.getElementById('user-name');
    if (userNameElement) {
      userNameElement.textContent = user.name || user.email || 'Usuário';
    }
    
    // Atualizar papel/função do usuário
    const userRoleElement = document.getElementById('user-role');
    if (userRoleElement && user.role) {
      userRoleElement.textContent = window.utils ? 
        utils.translateRole(user.role) : user.role;
    }
    
    // Exibir/ocultar elementos com base no papel do usuário
    const roleElements = document.querySelectorAll('[data-role]');
    roleElements.forEach(el => {
      const requiredRoles = el.getAttribute('data-role').split(',');
      const hasRequiredRole = requiredRoles.includes(user.role);
      
      el.style.display = hasRequiredRole ? '' : 'none';
    });
  }
  
  /**
   * Mostra a sidebar
   */
  function showSidebar() {
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
      sidebar.classList.add('show');
      state.sidebarVisible = true;
    }
  }
  
  /**
   * Oculta a sidebar
   */
  function hideSidebar() {
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
      sidebar.classList.remove('show');
      state.sidebarVisible = false;
    }
  }
  
  /**
   * Alterna visibilidade da sidebar
   */
  function toggleSidebar() {
    if (state.sidebarVisible) {
      hideSidebar();
    } else {
      showSidebar();
    }
  }
  
  /**
   * Lida com falha de autenticação
   */
  function handleAuthFailure() {
    // Se não estivermos já na página de login, redirecionar
    if (!window.location.pathname.includes('login')) {
      window.location.href = '/login';
    }
  }
  
  /**
   * Exibe uma notificação na interface
   */
  function showNotification(message, type = 'info', duration = 5000) {
    // Verificar se o container de notificações existe
    let container = document.querySelector('.notifications-container');
    if (!container) {
      container = document.createElement('div');
      container.className = 'notifications-container';
      document.body.appendChild(container);
    }
    
    // Criar elemento de notificação
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    // ID único para a notificação
    const id = Date.now();
    notification.dataset.id = id;
    
    // Conteúdo da notificação
    notification.innerHTML = `
      <span class="notification-close">&times;</span>
      <div>${message}</div>
    `;
    
    // Adicionar ao container
    container.appendChild(notification);
    
    // Registrar no estado
    state.notifications.push({
      id,
      message,
      type,
      timestamp: new Date()
    });
    
    // Configurar botão de fechar
    const closeBtn = notification.querySelector('.notification-close');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => dismissNotification(notification));
    }
    
    // Auto-remover após o tempo definido
    if (duration > 0) {
      setTimeout(() => dismissNotification(notification), duration);
    }
    
    return id;
  }
  
  /**
   * Remove uma notificação da interface
   */
  function dismissNotification(notification) {
    // Se for um ID, encontrar o elemento
    if (typeof notification === 'number') {
      notification = document.querySelector(`.notification[data-id="${notification}"]`);
    }
    
    if (!notification) return;
    
    // Animação de saída
    notification.style.opacity = '0';
    notification.style.transform = 'translateX(100%)';
    
    // Remover do DOM após a animação
    setTimeout(() => {
      if (notification && notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
      
      // Remover do estado
      const id = parseInt(notification.dataset.id);
      state.notifications = state.notifications.filter(n => n.id !== id);
    }, 300);
  }
  
  /**
   * Ativa/desativa indicador de carregamento
   */
  function toggleLoading(show = true) {
    state.loading = show;
    
    // Se houver um elemento de loader global, mostrar/ocultar
    const loader = document.querySelector('.page-loader');
    if (loader) {
      loader.style.display = show ? 'flex' : 'none';
    }
  }
  
  /**
   * Faz logout do usuário
   */
  function logout() {
    toggleLoading(true);
    
    fetch(`${config.apiUrl}/auth/logout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    .then(response => response.json())
    .then(data => {
      toggleLoading(false);
      
      if (data.success) {
        // Limpar estado da aplicação
        state.user = null;
        state.authenticated = false;
        
        // Redirecionar para login
        window.location.href = '/login';
      } else {
        showNotification(data.message || 'Erro ao fazer logout', 'error');
      }
    })
    .catch(error => {
      toggleLoading(false);
      logger('Erro ao fazer logout:', error);
      showNotification('Erro ao fazer logout. Tente novamente.', 'error');
    });
  }
  
  /**
   * Navega para outra página da aplicação
   */
  function navigateTo(page, params = {}) {
    // Construir URL com parâmetros de query se houver
    let url = `/${page}`;
    
    const queryParams = Object.keys(params)
      .filter(key => params[key] != null)
      .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(params[key]))
      .join('&');
    
    if (queryParams) {
      url += `?${queryParams}`;
    }
    
    // Navegar para a URL
    window.location.href = url;
  }
  
  /**
   * Registra mensagens de log se debug estiver ativado
   */
  function logger(...args) {
    if (config.debug) {
      console.log('[App]', ...args);
    }
  }
  
  // API pública
  return {
    init,
    showNotification,
    dismissNotification,
    toggleLoading,
    toggleSidebar,
    navigateTo,
    logout,
    logger,
    state,
    config
  };
})();

// Inicializar quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', app.init);