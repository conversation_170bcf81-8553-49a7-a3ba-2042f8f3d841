/**
 * Novo script de inicialização para o painel financeiro
 */

// Quando o documento estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando painel financeiro...');

    // Inicializar os cards de ferramentas
    initToolCards();

    // Inicializar os botões de ação
    initActionButtons();

    // Inicializar os gráficos do dashboard
    initDashboardCharts();
});

/**
 * Inicializa os cards de ferramentas
 */
function initToolCards() {
    console.log('Procurando cards de ferramentas...');
    const toolCards = document.querySelectorAll('.finance-tool-card');
    console.log('Cards encontrados:', toolCards.length);

    // Referências aos elementos para o flip
    const flipContainer = document.querySelector('.finance-flip-container');
    const btnVoltarFerramentas = document.getElementById('btnVoltarFerramentas');
    const toolNavItems = document.querySelectorAll('.tool-nav-item');
    const toolContentPanels = document.querySelectorAll('.tool-content-panel');

    if (flipContainer) {
        console.log('Inicializando flip container...');

        // Garantir que o container de flip esteja na posição inicial
        flipContainer.classList.remove('flipped');

        // Esconder o botão de voltar inicialmente
        if (btnVoltarFerramentas) {
            btnVoltarFerramentas.classList.add('d-none');
        }

        // Esconder todos os painéis de conteúdo inicialmente
        toolContentPanels.forEach(panel => {
            panel.classList.remove('active');
            panel.style.display = 'none';
        });

        // Função para voltar à visualização de cards
        function voltarParaCards() {
            flipContainer.classList.remove('flipped');
            if (btnVoltarFerramentas) {
                btnVoltarFerramentas.classList.add('d-none');
            }

            // Limpar seleção de cards de cotações
            const currencyCards = document.querySelectorAll('.mini-card.selected');
            currencyCards.forEach(card => {
                card.classList.remove('selected');
            });

            // Limpar gráfico
            if (window.currencyChart) {
                window.currencyChart.data.datasets = [];
                window.currencyChart.update();
            }

            // Resetar variáveis globais
            if (typeof selectedCurrencies !== 'undefined') {
                selectedCurrencies = ['usd'];
            }
        }

        toolCards.forEach(card => {
            console.log('Adicionando evento ao card:', card.getAttribute('data-tool'));
            card.addEventListener('click', function(e) {
                // Evitar propagação para não acionar outros eventos
                e.stopPropagation();

                const toolId = this.getAttribute('data-tool');
                console.log('Card clicado:', toolId);

                // Carregar conteúdo antes de fazer o flip
                loadToolContent(toolId);

                // Ativar a navegação correspondente
                activateToolNav(toolId);

                // Adicionar classe para o efeito de flip
                flipContainer.classList.add('flipped');

                // Mostrar botão de voltar
                btnVoltarFerramentas.classList.remove('d-none');
            });
        });

        // Event Listener para o botão voltar
        if (btnVoltarFerramentas) {
            btnVoltarFerramentas.addEventListener('click', function(e) {
                e.stopPropagation();
                voltarParaCards();
            });
        }

        // Adicionar evento de clique no documento para fechar o card quando clicar fora
        document.addEventListener('click', function(e) {
            // Verificar se o container está virado e se o clique foi fora do conteúdo
            if (flipContainer.classList.contains('flipped')) {
                // Verificar se o clique foi dentro do conteúdo da ferramenta
                const toolContent = document.querySelector('.tool-content-container');
                const btnVoltar = document.getElementById('btnVoltarFerramentas');

                // Se o clique não foi no conteúdo nem no botão voltar, fechar o card
                if (toolContent && !toolContent.contains(e.target) &&
                    btnVoltar && !btnVoltar.contains(e.target) &&
                    !e.target.closest('.mini-card') && // Ignorar cliques nos mini-cards
                    !e.target.closest('.chart-section') && // Ignorar cliques na seção do gráfico
                    !e.target.closest('.config-section') && // Ignorar cliques na seção de configurações
                    !e.target.closest('.cotacoes-table-container')) { // Ignorar cliques na tabela
                    voltarParaCards();
                }
            }
        });

        // Adicionar tecla ESC para voltar
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && flipContainer.classList.contains('flipped')) {
                voltarParaCards();
            }
        });

        // Event Listeners para a navegação de ferramentas
        toolNavItems.forEach(navItem => {
            navItem.addEventListener('click', function() {
                const toolId = this.getAttribute('data-tool');
                loadToolContent(toolId);
                activateToolNav(toolId);
            });
        });
    } else {
        console.log('Flip container não encontrado, usando modal como fallback');
        toolCards.forEach(card => {
            card.addEventListener('click', function() {
                const tool = this.getAttribute('data-tool');
                showToolModal(tool);
            });
        });
    }
}

/**
 * Ativa a navegação da ferramenta
 * @param {string} toolId - ID da ferramenta
 */
function activateToolNav(toolId) {
    const toolNavItems = document.querySelectorAll('.tool-nav-item');
    const toolContentPanels = document.querySelectorAll('.tool-content-panel');

    // Remover classe active de todos os itens de navegação
    toolNavItems.forEach(item => {
        item.classList.remove('active');
    });

    // Adicionar classe active ao item correspondente
    const activeNavItem = document.querySelector(`.tool-nav-item[data-tool="${toolId}"]`);
    if (activeNavItem) {
        activeNavItem.classList.add('active');
    }

    // Esconder todos os painéis de conteúdo
    toolContentPanels.forEach(panel => {
        panel.classList.remove('active');
        panel.style.display = 'none';
    });

    // Mostrar o painel ativo
    const activePanel = document.getElementById(`${toolId}-content`);
    if (activePanel) {
        activePanel.classList.add('active');
        activePanel.style.display = 'block';
    }
}

/**
 * Carrega o conteúdo da ferramenta
 * @param {string} toolId - ID da ferramenta
 */
function loadToolContent(toolId) {
    console.log('Carregando conteúdo para:', toolId);
    const contentPanel = document.getElementById(`${toolId}-content`);
    if (!contentPanel) {
        console.error('Painel de conteúdo não encontrado para:', toolId);
        return;
    }

    const contentBody = contentPanel.querySelector('.tool-content-body');
    if (!contentBody) {
        console.error('Corpo do conteúdo não encontrado para:', toolId);
        return;
    }

    // Se o conteúdo já foi carregado, não fazer nada
    if (contentBody.dataset.loaded === 'true') {
        console.log('Conteúdo já carregado para:', toolId);
        return;
    }

    console.log('Gerando conteúdo para:', toolId);

    // Carregar conteúdo específico baseado no ID da ferramenta
    try {
        switch (toolId) {
            case 'cotacoes':
                // Carregar o novo layout de cotações
                fetch('/templates/financeiro/cotacoes-new.html')
                    .then(response => response.text())
                    .then(html => {
                        contentBody.innerHTML = html;

                        // Adicionar os estilos
                        if (!document.querySelector('link[href="/static/css/financeiro-cotacoes-new.css"]')) {
                            const link = document.createElement('link');
                            link.rel = 'stylesheet';
                            link.href = '/static/css/financeiro-cotacoes-new.css';
                            document.head.appendChild(link);
                        }

                        // Adicionar o script
                        if (!document.querySelector('script[src="/static/js/financeiro-cotacoes-new.js"]')) {
                            const script = document.createElement('script');
                            script.src = '/static/js/financeiro-cotacoes-new.js';
                            document.body.appendChild(script);
                        }

                        // Inicializar a página
                        setTimeout(() => {
                            if (typeof initCotacoesPage === 'function') {
                                initCotacoesPage();
                            }
                        }, 500);
                    })
                    .catch(error => {
                        console.error('Erro ao carregar o template de cotações:', error);
                        contentBody.innerHTML = `
                            <div class="alert alert-danger">
                                <h4>Erro ao carregar cotações</h4>
                                <p>Não foi possível carregar o conteúdo. Por favor, tente novamente mais tarde.</p>
                            </div>
                        `;
                    });
                break;
            case 'calculadora-margem':
                createCalculadoraContent(contentBody);
                break;
            case 'impostos':
                createImpostosContent(contentBody);
                break;
            case 'historico':
                createHistoricoContent(contentBody);
                break;
            case 'noticias':
                createNoticiasContent(contentBody);
                break;
            case 'relatorios':
                createRelatoriosContent(contentBody);
                break;
            default:
                contentBody.innerHTML = `
                    <div class="text-center p-5">
                        <div class="mb-4">
                            <i class="fas fa-cogs fa-4x text-warning"></i>
                        </div>
                        <h4 class="text-light mb-3">Ferramenta em Desenvolvimento</h4>
                        <p class="text-secondary">Esta funcionalidade estará disponível em breve!</p>
                    </div>
                `;
        }

        // Marcar como carregado
        contentBody.dataset.loaded = 'true';
        console.log('Conteúdo carregado com sucesso para:', toolId);
    } catch (error) {
        console.error('Erro ao carregar conteúdo para:', toolId, error);
        contentBody.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                Erro ao carregar o conteúdo: ${error.message}
            </div>
        `;
    }
}

/**
 * Inicializa os botões de ação
 */
function initActionButtons() {
    console.log('Iniciando botões de ação...');

    // Botão de atualizar dados
    const refreshBtn = document.getElementById('refreshDataBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            console.log('Atualizando dados...');
            refreshDashboardData();
        });
    }

    // Botão de ver todos os pagamentos
    const viewAllPaymentsBtn = document.getElementById('viewAllPaymentsBtn');
    if (viewAllPaymentsBtn) {
        viewAllPaymentsBtn.addEventListener('click', function() {
            console.log('Abrindo lista de pagamentos...');
            showAllPayments();
        });
    }

    // Botão de ver todas as preventivas
    const viewAllPreventivesBtn = document.getElementById('viewAllPreventivesBtn');
    if (viewAllPreventivesBtn) {
        viewAllPreventivesBtn.addEventListener('click', function() {
            console.log('Abrindo lista de preventivas...');
            showAllPreventives();
        });
    }

    // Botão de ver relatório completo
    const viewAllMetricsBtn = document.getElementById('viewAllMetricsBtn');
    if (viewAllMetricsBtn) {
        viewAllMetricsBtn.addEventListener('click', function() {
            console.log('Abrindo relatório completo...');
            showFullReport();
        });
    }
}

/**
 * Inicializa os gráficos do dashboard
 */
function initDashboardCharts() {
    console.log('Inicializando gráficos do dashboard...');

    // Gráfico de receitas e despesas
    const revenueExpenseCtx = document.getElementById('revenueExpenseChart');
    if (revenueExpenseCtx) {
        new Chart(revenueExpenseCtx, {
            type: 'bar',
            data: {
                labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
                datasets: [
                    {
                        label: 'Receitas',
                        data: [12500, 13200, 14800, 13900, 15200, 16500],
                        backgroundColor: 'rgba(65, 184, 131, 0.7)',
                        borderColor: 'rgba(65, 184, 131, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Despesas',
                        data: [9800, 10200, 11500, 10800, 11900, 12200],
                        backgroundColor: 'rgba(225, 82, 65, 0.7)',
                        borderColor: 'rgba(225, 82, 65, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#aaa'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#aaa'
                        }
                    }
                },
                plugins: {
                    legend: {
                        labels: {
                            color: '#ccc'
                        }
                    }
                }
            }
        });
    }

    // Gráfico de margem de lucro
    const profitMarginCtx = document.getElementById('profitMarginChart');
    if (profitMarginCtx) {
        new Chart(profitMarginCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
                datasets: [
                    {
                        label: 'Margem de Lucro (%)',
                        data: [21.6, 22.7, 22.3, 22.5, 21.7, 26.1],
                        backgroundColor: 'rgba(253, 184, 19, 0.2)',
                        borderColor: 'rgba(253, 184, 19, 1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#aaa',
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#aaa'
                        }
                    }
                },
                plugins: {
                    legend: {
                        labels: {
                            color: '#ccc'
                        }
                    }
                }
            }
        });
    }
}

/**
 * Atualiza os dados do dashboard
 */
function refreshDashboardData() {
    console.log('Atualizando dados...');
    showLoadingSpinner();
    setTimeout(() => {
        hideLoadingSpinner();
        showSuccessMessage('Dados atualizados com sucesso!');
    }, 1500);
}

/**
 * Mostra o spinner de carregamento
 */
function showLoadingSpinner() {
    const spinner = document.createElement('div');
    spinner.className = 'loading-spinner';
    spinner.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    document.body.appendChild(spinner);
}

/**
 * Esconde o spinner de carregamento
 */
function hideLoadingSpinner() {
    const spinner = document.querySelector('.loading-spinner');
    if (spinner) {
        document.body.removeChild(spinner);
    }
}

/**
 * Mostra uma mensagem de sucesso
 * @param {string} message - Mensagem a ser exibida
 */
function showSuccessMessage(message) {
    const toast = document.createElement('div');
    toast.className = 'toast-message success';
    toast.innerHTML = `
        <i class="fas fa-check-circle"></i>
        <span>${message}</span>
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

/**
 * Mostra uma mensagem de erro
 * @param {string} message - Mensagem a ser exibida
 */
function showErrorMessage(message) {
    const toast = document.createElement('div');
    toast.className = 'toast-message error';
    toast.innerHTML = `
        <i class="fas fa-exclamation-circle"></i>
        <span>${message}</span>
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Funções auxiliares para criar conteúdo das ferramentas
function createCalculadoraContent(container) {
    // Implementação futura
    if (container) {
        container.innerHTML = `
            <div class="text-center p-5">
                <div class="mb-4">
                    <i class="fas fa-calculator fa-4x text-warning"></i>
                </div>
                <h4 class="text-light mb-3">Calculadora de Margem</h4>
                <p class="text-secondary">Esta funcionalidade estará disponível em breve!</p>
            </div>
        `;
    }
}

function createImpostosContent(container) {
    // Implementação futura
    if (container) {
        container.innerHTML = `
            <div class="text-center p-5">
                <div class="mb-4">
                    <i class="fas fa-file-invoice-dollar fa-4x text-warning"></i>
                </div>
                <h4 class="text-light mb-3">Impostos</h4>
                <p class="text-secondary">Esta funcionalidade estará disponível em breve!</p>
            </div>
        `;
    }
}



function createNoticiasContent(container) {
    // Implementação futura
    if (container) {
        container.innerHTML = `
            <div class="text-center p-5">
                <div class="mb-4">
                    <i class="fas fa-newspaper fa-4x text-warning"></i>
                </div>
                <h4 class="text-light mb-3">Notícias do Mercado</h4>
                <p class="text-secondary">Esta funcionalidade estará disponível em breve!</p>
            </div>
        `;
    }
}

function createRelatoriosContent(container) {
    // Implementação futura
    if (container) {
        container.innerHTML = `
            <div class="text-center p-5">
                <div class="mb-4">
                    <i class="fas fa-chart-pie fa-4x text-warning"></i>
                </div>
                <h4 class="text-light mb-3">Relatórios</h4>
                <p class="text-secondary">Esta funcionalidade estará disponível em breve!</p>
            </div>
        `;
    }
}

// Funções auxiliares para navegação
function showAllPayments() {
    console.log('Mostrando todos os pagamentos...');
    window.location.href = '/financeiro/pagamentos';
}

function showAllPreventives() {
    console.log('Mostrando todas as preventivas...');
    window.location.href = '/financeiro/preventivas';
}

function showFullReport() {
    console.log('Mostrando relatório completo...');
    window.location.href = '/financeiro/relatorios';
}
