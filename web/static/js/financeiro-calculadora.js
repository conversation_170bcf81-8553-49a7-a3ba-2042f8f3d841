/**
 * Cria o conteúdo da ferramenta de Calculadora de Margem
 * @param {HTMLElement} container - Container onde o conteúdo será inserido
 */
function createCalculadoraContent(container) {
    // Estrutura HTML da calculadora
    container.innerHTML = `
        <div class="tool-section">
            <h3 class="tool-section-title">
                <i class="fas fa-calculator"></i> Calculadora de Margem de Lucro
                <span class="help-icon-wrapper">
                    <button class="help-icon" id="calculadora-help" title="Ajuda">
                        <i class="fas fa-question-circle"></i>
                    </button>
                </span>
            </h3>
            <div class="tool-form">
                <!-- Seção de Custos Fixos -->
                <div class="form-group">
                    <label>Preço de Compra (R$/litro)</label>
                    <input type="number" id="precoCusto" step="0.01" value="4.25">
                </div>
                <div class="form-group">
                    <label>Preço de Venda (R$/litro)</label>
                    <input type="number" id="precoVenda" step="0.01" value="6.79">
                </div>

                <!-- Seção de Custos Variáveis -->
                <div class="form-section">
                    <h4>Custos Variáveis</h4>
                    <div id="custosVariaveis">
                        <div class="custo-item">
                            <div class="form-group">
                                <label>Transporte</label>
                                <input type="number" class="custo-valor" step="0.01" value="0.15">
                            </div>
                        </div>
                        <div class="custo-item">
                            <div class="form-group">
                                <label>Impostos</label>
                                <input type="number" class="custo-valor" step="0.01" value="1.25">
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn-outline-shell" id="addCustoBtn">
                        <i class="fas fa-plus"></i> Adicionar Custo
                    </button>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn-shell-yellow" id="calcularBtn">Calcular</button>
                    <button type="button" class="btn-outline-shell" id="resetBtn">Limpar</button>
                </div>
            </div>
        </div>

        <!-- Resultados -->
        <div class="tool-section" id="resultadosSection" style="display: none;">
            <h3 class="tool-section-title">
                <i class="fas fa-chart-pie"></i> Resultados
            </h3>
            <div class="results-grid">
                <div class="result-card">
                    <div class="result-title">Margem Bruta</div>
                    <div class="result-value" id="margemBruta">R$ 0,00</div>
                    <div class="result-percent" id="margemBrutaPercent">0%</div>
                </div>
                <div class="result-card">
                    <div class="result-title">Margem Líquida</div>
                    <div class="result-value" id="margemLiquida">R$ 0,00</div>
                    <div class="result-percent" id="margemLiquidaPercent">0%</div>
                </div>
                <div class="result-card">
                    <div class="result-title">Total de Custos</div>
                    <div class="result-value" id="totalCustos">R$ 0,00</div>
                    <div class="result-percent" id="totalCustosPercent">0%</div>
                </div>
            </div>

            <div class="chart-container">
                <canvas id="margemChart" width="100%" height="250"></canvas>
            </div>
        </div>

        <!-- Modal de Ajuda para Calculadora -->
        <div class="modal" id="modal-calculadora-help">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title">Guia de Uso - Calculadora de Margem</h4>
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <h5>Como usar a Calculadora de Margem</h5>
                        <p>Esta ferramenta ajuda a calcular margens de lucro para diferentes produtos e cenários de preço.</p>

                        <h6>Instruções de uso:</h6>
                        <ol>
                            <li><strong>Preencha o preço de compra e venda:</strong> Insira os valores por litro/unidade do produto.</li>
                            <li><strong>Adicione custos variáveis:</strong> Inclua todos os custos adicionais como transporte, impostos, etc.</li>
                            <li><strong>Calcule a margem:</strong> Clique no botão "Calcular" para visualizar os resultados.</li>
                        </ol>

                        <h6>Métricas importantes:</h6>
                        <ul>
                            <li><strong>Margem Bruta:</strong> Diferença entre preço de venda e preço de compra.</li>
                            <li><strong>Margem Líquida:</strong> Margem bruta menos custos variáveis.</li>
                            <li><strong>Total de Custos:</strong> Soma de todos os custos (preço de compra + custos variáveis).</li>
                        </ul>

                        <h6>Dicas para uma melhor análise:</h6>
                        <ul>
                            <li>Mantenha uma margem líquida mínima de 10-15% para garantir rentabilidade.</li>
                            <li>Compare margens entre diferentes produtos para priorizar vendas.</li>
                            <li>Realize simulações alterando preços e custos para encontrar o ponto ótimo.</li>
                            <li>Considere o volume de vendas ao analisar os resultados (margens menores podem ser compensadas por volumes maiores).</li>
                        </ul>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn-shell-yellow" data-dismiss="modal">Entendi</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Adicionar estilos inline para o botão de ajuda
    const styleElement = document.createElement('style');
    styleElement.textContent = `
        .help-icon-wrapper {
            display: inline-block;
            margin-left: 10px;
            vertical-align: middle;
        }
        .help-icon {
            background: transparent;
            border: none;
            color: var(--shell-yellow);
            font-size: 18px;
            padding: 0;
            cursor: pointer;
            transition: transform 0.2s ease, color 0.2s ease;
            outline: none;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }
        .help-icon:hover {
            color: var(--shell-yellow-hover);
            transform: scale(1.1);
        }
        .help-icon:active {
            transform: scale(0.95);
        }
    `;
    document.head.appendChild(styleElement);

    // Adicionar eventos
    const addCustoBtn = container.querySelector('#addCustoBtn');
    const calcularBtn = container.querySelector('#calcularBtn');
    const resetBtn = container.querySelector('#resetBtn');
    const custosVariaveis = container.querySelector('#custosVariaveis');

    // Evento para adicionar novo custo
    addCustoBtn.addEventListener('click', function() {
        const novoCusto = document.createElement('div');
        novoCusto.className = 'custo-item';
        novoCusto.innerHTML = `
            <div class="form-group">
                <label>Novo Custo</label>
                <div class="input-group">
                    <input type="text" class="custo-nome" placeholder="Nome do custo">
                    <input type="number" class="custo-valor" step="0.01" value="0.00">
                    <button class="btn-remove-custo"><i class="fas fa-times"></i></button>
                </div>
            </div>
        `;
        custosVariaveis.appendChild(novoCusto);

        // Adicionar evento para remover custo
        novoCusto.querySelector('.btn-remove-custo').addEventListener('click', function() {
            custosVariaveis.removeChild(novoCusto);
        });
    });

    // Evento para calcular margem
    calcularBtn.addEventListener('click', function() {
        const precoCusto = parseFloat(container.querySelector('#precoCusto').value) || 0;
        const precoVenda = parseFloat(container.querySelector('#precoVenda').value) || 0;

        // Calcular total de custos variáveis
        let totalCustosVariaveis = 0;
        const custoInputs = container.querySelectorAll('.custo-valor');
        custoInputs.forEach(input => {
            totalCustosVariaveis += parseFloat(input.value) || 0;
        });

        // Calcular margens
        const margemBruta = precoVenda - precoCusto;
        const margemLiquida = margemBruta - totalCustosVariaveis;
        const totalCustos = precoCusto + totalCustosVariaveis;

        // Calcular percentuais
        const margemBrutaPercent = (margemBruta / precoVenda) * 100;
        const margemLiquidaPercent = (margemLiquida / precoVenda) * 100;
        const totalCustosPercent = (totalCustos / precoVenda) * 100;

        // Atualizar resultados
        container.querySelector('#margemBruta').textContent = `R$ ${margemBruta.toFixed(2)}`;
        container.querySelector('#margemBrutaPercent').textContent = `${margemBrutaPercent.toFixed(2)}%`;

        container.querySelector('#margemLiquida').textContent = `R$ ${margemLiquida.toFixed(2)}`;
        container.querySelector('#margemLiquidaPercent').textContent = `${margemLiquidaPercent.toFixed(2)}%`;

        container.querySelector('#totalCustos').textContent = `R$ ${totalCustos.toFixed(2)}`;
        container.querySelector('#totalCustosPercent').textContent = `${totalCustosPercent.toFixed(2)}%`;

        // Mostrar seção de resultados
        container.querySelector('#resultadosSection').style.display = 'block';

        // Criar gráfico
        createMargemChart(precoCusto, totalCustosVariaveis, margemLiquida);
    });

    // Evento para resetar formulário
    resetBtn.addEventListener('click', function() {
        container.querySelector('#precoCusto').value = "4.25";
        container.querySelector('#precoVenda').value = "6.79";

        // Manter apenas os custos originais
        const custoItems = container.querySelectorAll('.custo-item');
        for (let i = 2; i < custoItems.length; i++) {
            custosVariaveis.removeChild(custoItems[i]);
        }

        // Resetar valores dos custos originais
        container.querySelectorAll('.custo-valor')[0].value = "0.15";
        container.querySelectorAll('.custo-valor')[1].value = "1.25";

        // Esconder seção de resultados
        container.querySelector('#resultadosSection').style.display = 'none';
    });

    // Adiciona evento ao botão de ajuda
    const btnCalculadoraHelp = container.querySelector('#calculadora-help');
    btnCalculadoraHelp.addEventListener('click', function(event) {
        // Prevenir comportamento padrão que pode estar causando abertura automática
        event.preventDefault();
        event.stopPropagation();

        // Exibir o modal de ajuda
        const modal = container.querySelector('#modal-calculadora-help');
        modal.style.display = 'block';
    });

    // Fechar o modal quando clicar no botão de fechar
    const closeButtons = container.querySelectorAll('[data-dismiss="modal"]');
    closeButtons.forEach(button => {
        button.addEventListener('click', function(event) {
            // Prevenir propagação
            event.preventDefault();
            event.stopPropagation();

            const modal = this.closest('.modal');
            modal.style.display = 'none';
        });
    });

    // Fechar o modal quando clicar fora dele
    window.addEventListener('click', function(event) {
        const modal = container.querySelector('#modal-calculadora-help');
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });

    // Garantir que o modal comece fechado
    const modal = container.querySelector('#modal-calculadora-help');
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * Cria o gráfico da calculadora de margem
 * @param {number} precoCusto - Preço de custo
 * @param {number} custosVariaveis - Total de custos variáveis
 * @param {number} margemLiquida - Margem líquida
 */
function createMargemChart(precoCusto, custosVariaveis, margemLiquida) {
    const ctx = document.getElementById('margemChart');
    if (!ctx) return;

    // Destruir gráfico existente se houver
    if (window.margemChart) {
        window.margemChart.destroy();
    }

    // Criar novo gráfico
    window.margemChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Custo de Aquisição', 'Custos Variáveis', 'Margem Líquida'],
            datasets: [{
                data: [precoCusto, custosVariaveis, margemLiquida],
                backgroundColor: ['#ED1C24', '#FDB813', '#28a745'],
                borderColor: '#333',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    labels: {
                        color: '#ccc',
                        font: {
                            family: "'Rajdhani', sans-serif",
                            size: 12
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(2);
                            return `${label}: R$ ${value.toFixed(2)} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

/**
 * Cria conteúdo para a ferramenta de cotações
 * @param {HTMLElement} container - Container onde o conteúdo será inserido
 */
function createCotacoesContent(container) {
    container.innerHTML = `
        <div class="row">
            <div class="col-12 mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="text-light m-0">Cotações em Tempo Real</h5>
                    <div>
                        <span class="badge bg-secondary me-2">Última atualização: Hoje, 16:22</span>
                        <button class="btn btn-sm btn-outline-warning">
                            <i class="fas fa-sync-alt"></i> Atualizar
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card bg-dark border-secondary">
                    <div class="card-header bg-dark border-secondary d-flex justify-content-between">
                        <h5 class="text-warning">Petróleo (Brent)</h5>
                        <span class="badge bg-success">+1.2%</span>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h2 class="text-light mb-0">$79.45 <small class="text-success">↑ $0.98</small></h2>
                            <div class="text-end">
                                <div class="small text-secondary">Abertura: $78.50</div>
                                <div class="small text-secondary">Máxima: $79.78</div>
                                <div class="small text-secondary">Mínima: $78.32</div>
                            </div>
                        </div>
                        <div style="height: 180px;">
                            <canvas id="brentChart"></canvas>
                        </div>
                        <div class="d-flex justify-content-between mt-3">
                            <div>
                                <div class="small text-secondary">1 Mês</div>
                                <div class="text-warning">+3.8%</div>
                            </div>
                            <div>
                                <div class="small text-secondary">3 Meses</div>
                                <div class="text-danger">-2.1%</div>
                            </div>
                            <div>
                                <div class="small text-secondary">6 Meses</div>
                                <div class="text-warning">+5.7%</div>
                            </div>
                            <div>
                                <div class="small text-secondary">1 Ano</div>
                                <div class="text-warning">+12.3%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card bg-dark border-secondary">
                    <div class="card-header bg-dark border-secondary d-flex justify-content-between">
                        <h5 class="text-warning">Dólar (USD/BRL)</h5>
                        <span class="badge bg-danger">-0.8%</span>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h2 class="text-light mb-0">R$ 5.44 <small class="text-danger">↓ R$ 0.04</small></h2>
                            <div class="text-end">
                                <div class="small text-secondary">Abertura: R$ 5.49</div>
                                <div class="small text-secondary">Máxima: R$ 5.51</div>
                                <div class="small text-secondary">Mínima: R$ 5.43</div>
                            </div>
                        </div>
                        <div style="height: 180px;">
                            <canvas id="dolarChart"></canvas>
                        </div>
                        <div class="d-flex justify-content-between mt-3">
                            <div>
                                <div class="small text-secondary">1 Mês</div>
                                <div class="text-warning">+1.2%</div>
                            </div>
                            <div>
                                <div class="small text-secondary">3 Meses</div>
                                <div class="text-warning">+2.8%</div>
                            </div>
                            <div>
                                <div class="small text-secondary">6 Meses</div>
                                <div class="text-danger">-1.5%</div>
                            </div>
                            <div>
                                <div class="small text-secondary">1 Ano</div>
                                <div class="text-danger">-3.7%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card bg-dark border-secondary">
                    <div class="card-header bg-dark border-secondary">
                        <h5 class="text-warning">Combustíveis - Preços Médios Nacionais</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-dark table-hover">
                                <thead>
                                    <tr>
                                        <th>Combustível</th>
                                        <th>Preço Médio</th>
                                        <th>Variação</th>
                                        <th>Tendência</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Gasolina Comum</td>
                                        <td>R$ 5.52/litro</td>
                                        <td class="text-warning">+0.7%</td>
                                        <td><i class="fas fa-arrow-up text-warning"></i> Alta</td>
                                    </tr>
                                    <tr>
                                        <td>Gasolina Aditivada</td>
                                        <td>R$ 5.89/litro</td>
                                        <td class="text-warning">+0.8%</td>
                                        <td><i class="fas fa-arrow-up text-warning"></i> Alta</td>
                                    </tr>
                                    <tr>
                                        <td>Etanol</td>
                                        <td>R$ 3.25/litro</td>
                                        <td class="text-warning">+0.5%</td>
                                        <td><i class="fas fa-arrow-up text-warning"></i> Alta</td>
                                    </tr>
                                    <tr>
                                        <td>Diesel</td>
                                        <td>R$ 6.25/litro</td>
                                        <td class="text-warning">+1.2%</td>
                                        <td><i class="fas fa-arrow-up text-warning"></i> Alta</td>
                                    </tr>
                                    <tr>
                                        <td>Diesel S10</td>
                                        <td>R$ 6.35/litro</td>
                                        <td class="text-warning">+1.4%</td>
                                        <td><i class="fas fa-arrow-up text-warning"></i> Alta</td>
                                    </tr>
                                    <tr>
                                        <td>GNV</td>
                                        <td>R$ 4.85/m³</td>
                                        <td class="text-secondary">0.0%</td>
                                        <td><i class="fas fa-minus text-secondary"></i> Estável</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card bg-dark border-secondary">
                    <div class="card-header bg-dark border-secondary">
                        <h5 class="text-warning">Indicadores Econômicos</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-dark table-hover">
                                <thead>
                                    <tr>
                                        <th>Indicador</th>
                                        <th>Valor</th>
                                        <th>Variação</th>
                                        <th>Data</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>IPCA (Anual)</td>
                                        <td>4.25%</td>
                                        <td class="text-warning">+0.15 p.p.</td>
                                        <td>Jul/2025</td>
                                    </tr>
                                    <tr>
                                        <td>Selic</td>
                                        <td>10.50%</td>
                                        <td class="text-secondary">0.00 p.p.</td>
                                        <td>Ago/2025</td>
                                    </tr>
                                    <tr>
                                        <td>PIB (Prev. Anual)</td>
                                        <td>2.8%</td>
                                        <td class="text-success">+0.3 p.p.</td>
                                        <td>Ago/2025</td>
                                    </tr>
                                    <tr>
                                        <td>Euro (EUR/BRL)</td>
                                        <td>R$ 5.98</td>
                                        <td class="text-danger">-0.5%</td>
                                        <td>Hoje</td>
                                    </tr>
                                    <tr>
                                        <td>IBOVESPA</td>
                                        <td>132.458 pts</td>
                                        <td class="text-warning">+0.6%</td>
                                        <td>Hoje</td>
                                    </tr>
                                    <tr>
                                        <td>CDI (Anual)</td>
                                        <td>10.40%</td>
                                        <td class="text-secondary">0.00 p.p.</td>
                                        <td>Ago/2025</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Inicializar gráficos
    setTimeout(() => {
        const brentCtx = document.getElementById('brentChart');
        const dolarCtx = document.getElementById('dolarChart');

        if (brentCtx) {
            new Chart(brentCtx, {
                type: 'line',
                data: {
                    labels: ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00'],
                    datasets: [{
                        label: 'Preço (USD)',
                        data: [78.5, 78.9, 78.7, 79.2, 78.8, 79.1, 79.6, 79.45],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#aaa'
                            }
                        },
                        y: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#aaa'
                            }
                        }
                    }
                }
            });
        }

        if (dolarCtx) {
            new Chart(dolarCtx, {
                type: 'line',
                data: {
                    labels: ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00'],
                    datasets: [{
                        label: 'Cotação (R$)',
                        data: [5.49, 5.51, 5.50, 5.48, 5.47, 5.45, 5.44, 5.44],
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#aaa'
                            }
                        },
                        y: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#aaa'
                            }
                        }
                    }
                }
            });
        }
    }, 100);

    // Adicionar evento para o botão de atualizar
    const btnAtualizar = container.querySelector('.btn-outline-warning');
    if (btnAtualizar) {
        btnAtualizar.addEventListener('click', function() {
            // Simular atualização
            const loadingToast = document.createElement('div');
            loadingToast.className = 'toast-message info show';
            loadingToast.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i> Atualizando cotações...';
            document.body.appendChild(loadingToast);

            setTimeout(() => {
                loadingToast.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(loadingToast);

                    // Mostrar toast de sucesso
                    const successToast = document.createElement('div');
                    successToast.className = 'toast-message success show';
                    successToast.innerHTML = '<i class="fas fa-check-circle"></i> Cotações atualizadas!';
                    document.body.appendChild(successToast);

                    setTimeout(() => {
                        successToast.classList.remove('show');
                        setTimeout(() => {
                            document.body.removeChild(successToast);
                        }, 300);
                    }, 2000);
                }, 300);
            }, 1500);
        });
    }
}

/**
 * Cria conteúdo para a ferramenta de impostos
 * @param {HTMLElement} container - Container onde o conteúdo será inserido
 */
function createImpostosContent(container) {
    container.innerHTML = `
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card bg-dark border-secondary">
                    <div class="card-header bg-dark border-secondary">
                        <h5 class="text-warning">Consulta de Impostos</h5>
                    </div>
                    <div class="card-body">
                        <form id="formConsultaImpostos">
                            <div class="mb-3">
                                <label class="form-label text-light">Estado</label>
                                <select class="form-control bg-dark text-light border-secondary" id="selectEstado">
                                    <option value="">Selecione o Estado</option>
                                    <option value="AC">Acre</option>
                                    <option value="AL">Alagoas</option>
                                    <option value="AP">Amapá</option>
                                    <option value="AM">Amazonas</option>
                                    <option value="BA">Bahia</option>
                                    <option value="CE">Ceará</option>
                                    <option value="DF">Distrito Federal</option>
                                    <option value="ES">Espírito Santo</option>
                                    <option value="GO">Goiás</option>
                                    <option value="MA">Maranhão</option>
                                    <option value="MT">Mato Grosso</option>
                                    <option value="MS">Mato Grosso do Sul</option>
                                    <option value="MG">Minas Gerais</option>
                                    <option value="PA">Pará</option>
                                    <option value="PB">Paraíba</option>
                                    <option value="PR">Paraná</option>
                                    <option value="PE">Pernambuco</option>
                                    <option value="PI">Piauí</option>
                                    <option value="RJ">Rio de Janeiro</option>
                                    <option value="RN">Rio Grande do Norte</option>
                                    <option value="RS">Rio Grande do Sul</option>
                                    <option value="RO">Rondônia</option>
                                    <option value="RR">Roraima</option>
                                    <option value="SC">Santa Catarina</option>
                                    <option value="SP">São Paulo</option>
                                    <option value="SE">Sergipe</option>
                                    <option value="TO">Tocantins</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-light">Tipo de Combustível</label>
                                <select class="form-control bg-dark text-light border-secondary" id="selectCombustivel">
                                    <option value="">Selecione o Combustível</option>
                                    <option value="gasolina">Gasolina Comum</option>
                                    <option value="gasolina_ad">Gasolina Aditivada</option>
                                    <option value="etanol">Etanol</option>
                                    <option value="diesel">Diesel</option>
                                    <option value="diesel_s10">Diesel S10</option>
                                    <option value="gnv">GNV</option>
                                </select>
                            </div>
                            <button type="button" class="btn btn-shell-yellow w-100" id="btnConsultarImpostos">
                                <i class="fas fa-search"></i> Consultar
                            </button>
                        </form>

                        <div class="mt-4">
                            <h6 class="text-warning mb-3">Impostos Aplicáveis</h6>
                            <div class="d-flex flex-column gap-2">
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-danger me-2">ICMS</span>
                                    <span class="text-light small">Imposto sobre Circulação de Mercadorias e Serviços</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-warning me-2">PIS</span>
                                    <span class="text-light small">Programa de Integração Social</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-warning me-2">COFINS</span>
                                    <span class="text-light small">Contribuição para o Financiamento da Seguridade Social</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-primary me-2">CIDE</span>
                                    <span class="text-light small">Contribuição de Intervenção no Domínio Econômico</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-8 mb-4" id="resultadoImpostos" style="display: none;">
                <div class="card bg-dark border-secondary">
                    <div class="card-header bg-dark border-secondary d-flex justify-content-between">
                        <h5 class="text-warning">Alíquotas de Impostos</h5>
                        <span id="estadoCombustivel" class="badge bg-secondary">SP - Gasolina</span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-7">
                                <div class="table-responsive">
                                    <table class="table table-dark table-striped">
                                        <thead>
                                            <tr>
                                                <th>Imposto</th>
                                                <th>Alíquota (%)</th>
                                                <th>Base de Cálculo</th>
                                            </tr>
                                        </thead>
                                        <tbody id="tabelaImpostos">
                                            <!-- Preenchido via JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-5">
                                <div class="chart-container" style="height: 200px;">
                                    <canvas id="impostosChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <h6 class="text-warning">Exemplo de Cálculo</h6>
                            <div class="card bg-dark border-secondary">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-dark table-sm">
                                                <tbody>
                                                    <tr>
                                                        <td>Valor Base</td>
                                                        <td class="text-end">R$ 5,00/litro</td>
                                                    </tr>
                                                    <tr>
                                                        <td>ICMS (18%)</td>
                                                        <td class="text-end">R$ 0,90/litro</td>
                                                    </tr>
                                                    <tr>
                                                        <td>PIS/COFINS (9.25%)</td>
                                                        <td class="text-end">R$ 0,46/litro</td>
                                                    </tr>
                                                    <tr>
                                                        <td>CIDE</td>
                                                        <td class="text-end">R$ 0,10/litro</td>
                                                    </tr>
                                                    <tr class="border-top">
                                                        <td><strong>Total Impostos</strong></td>
                                                        <td class="text-end"><strong>R$ 1,46/litro</strong></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>% sobre preço final</strong></td>
                                                        <td class="text-end"><strong>29,2%</strong></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <div id="exemploCalcChart" style="height: 180px;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3 p-3 border border-warning rounded">
                            <h6 class="text-warning">Observações:</h6>
                            <ul class="text-light small mb-0">
                                <li>Valores atualizados conforme legislação vigente.</li>
                                <li>As alíquotas podem sofrer alterações sem aviso prévio.</li>
                                <li>Consulte a legislação estadual para informações detalhadas.</li>
                                <li>A CIDE pode variar conforme decretos do governo federal.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Mapeamento de alíquotas de ICMS por estado
    const icmsPorEstado = {
        'AC': 25, 'AL': 29, 'AP': 25, 'AM': 25, 'BA': 28, 'CE': 29, 'DF': 28, 'ES': 27,
        'GO': 30, 'MA': 30.5, 'MT': 25, 'MS': 30, 'MG': 31, 'PA': 28, 'PB': 29, 'PR': 29,
        'PE': 29, 'PI': 30, 'RJ': 34, 'RN': 29, 'RS': 30, 'RO': 26, 'RR': 25, 'SC': 25,
        'SP': 25, 'SE': 29, 'TO': 29
    };

    // Adicionar event listeners
    const btnConsultar = container.querySelector('#btnConsultarImpostos');
    if (btnConsultar) {
        btnConsultar.addEventListener('click', function() {
            const selectEstado = container.querySelector('#selectEstado');
            const selectCombustivel = container.querySelector('#selectCombustivel');
            const estado = selectEstado.value;
            const combustivel = selectCombustivel.value;

            if (!estado || !combustivel) {
                alert('Por favor, selecione o estado e o tipo de combustível');
                return;
            }

            // Obter a alíquota de ICMS para o estado selecionado
            const icmsAliquota = icmsPorEstado[estado] || 25; // Valor padrão se não encontrar

            // Dados dos impostos (alíquotas e base de cálculo)
            const impostos = {
                'ICMS': { aliquota: `${icmsAliquota}%`, base: 'Valor da operação' },
                'PIS': { aliquota: '1.65%', base: 'Receita bruta' },
                'COFINS': { aliquota: '7.6%', base: 'Receita bruta' },
                'CIDE': { aliquota: 'R$ 0,10/litro', base: 'Volume comercializado' }
            };

            // Atualizar tabela
            const tbody = container.querySelector('#tabelaImpostos');
            tbody.innerHTML = '';

            for (const [imposto, dados] of Object.entries(impostos)) {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${imposto}</td>
                    <td>${dados.aliquota}</td>
                    <td>${dados.base}</td>
                `;
                tbody.appendChild(tr);
            }

            // Atualizar título com estado e combustível
            const estadoNome = selectEstado.options[selectEstado.selectedIndex].text;
            const combustivelNome = selectCombustivel.options[selectCombustivel.selectedIndex].text;
            container.querySelector('#estadoCombustivel').textContent = `${estadoNome} - ${combustivelNome}`;

            // Criar gráfico de pizza para distribuição dos impostos
            setTimeout(() => {
                const ctx = document.getElementById('impostosChart');
                if (ctx) {
                    const icmsValor = icmsAliquota;
                    const pisConfinsValor = 9.25; // PIS + COFINS
                    const cideValor = 2; // Estimativa do impacto percentual da CIDE

                    new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: ['ICMS', 'PIS/COFINS', 'CIDE'],
                            datasets: [{
                                data: [icmsValor, pisConfinsValor, cideValor],
                                backgroundColor: ['#dc3545', '#ffc107', '#0d6efd'],
                                borderColor: '#333',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'right',
                                    labels: {
                                        color: '#ccc',
                                        font: {
                                            family: "'Rajdhani', sans-serif",
                                            size: 11
                                        }
                                    }
                                },
                                title: {
                                    display: true,
                                    text: 'Distribuição de Impostos',
                                    color: '#ccc',
                                    font: {
                                        family: "'Rajdhani', sans-serif",
                                        size: 14
                                    }
                                }
                            }
                        }
                    });
                }

                const exemploCtx = document.getElementById('exemploCalcChart');
                if (exemploCtx) {
                    new Chart(exemploCtx, {
                        type: 'pie',
                        data: {
                            labels: ['Valor sem impostos', 'Impostos'],
                            datasets: [{
                                data: [3.54, 1.46],
                                backgroundColor: ['#28a745', '#dc3545'],
                                borderColor: '#333',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                    labels: {
                                        color: '#ccc',
                                        font: {
                                            family: "'Rajdhani', sans-serif",
                                            size: 11
                                        }
                                    }
                                }
                            }
                        }
                    });
                }
            }, 100);

            // Mostrar resultado
            container.querySelector('#resultadoImpostos').style.display = 'block';
        });
    }
}

/**
 * Cria conteúdo para a ferramenta de notícias
 * @param {HTMLElement} container - Container onde o conteúdo será inserido
 */
function createNoticiasContent(container) {
    container.innerHTML = `
        <div class="row">
            <div class="col-12 mb-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="text-light m-0">Últimas Notícias do Mercado</h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-warning">
                            <i class="fas fa-sync-alt"></i> Atualizar
                        </button>
                        <select class="form-select form-select-sm bg-dark text-light border-secondary" style="width: 150px">
                            <option value="all">Todas as categorias</option>
                            <option value="petroleo">Petróleo</option>
                            <option value="combustiveis">Combustíveis</option>
                            <option value="economia">Economia</option>
                            <option value="regulacoes">Regulações</option>
                        </select>
                    </div>
                </div>

                <div class="card bg-dark border-secondary mb-3">
                    <div class="card-header bg-dark border-secondary d-flex justify-content-between">
                        <div>
                            <span class="badge bg-warning me-2">Destaque</span>
                            <span class="text-muted small">Hoje, 16:45</span>
                        </div>
                        <span class="badge bg-secondary">Petróleo</span>
                    </div>
                    <div class="card-body">
                        <h5 class="text-warning">Preço do petróleo sobe com tensões no Oriente Médio</h5>
                        <p class="text-light">O preço do petróleo Brent subiu mais de 2% nesta quinta-feira, impulsionado por novos conflitos na região do Oriente Médio e preocupações com a oferta global.</p>
                        <p class="text-secondary mb-0">Fonte: Reuters</p>
                    </div>
                </div>

                <div class="card bg-dark border-secondary mb-3">
                    <div class="card-header bg-dark border-secondary d-flex justify-content-between">
                        <div>
                            <span class="text-muted small">Hoje, 09:30</span>
                        </div>
                        <span class="badge bg-secondary">Combustíveis</span>
                    </div>
                    <div class="card-body">
                        <h5 class="text-warning">Governo anuncia nova política de preços para combustíveis</h5>
                        <p class="text-light">O Ministério de Minas e Energia anunciou hoje uma nova política para estabilização dos preços de combustíveis no mercado interno, com mecanismos automáticos de ajuste.</p>
                        <p class="text-secondary mb-0">Fonte: Agência Brasil</p>
                    </div>
                </div>

                <div class="card bg-dark border-secondary mb-3">
                    <div class="card-header bg-dark border-secondary d-flex justify-content-between">
                        <div>
                            <span class="text-muted small">Ontem, 14:20</span>
                        </div>
                        <span class="badge bg-secondary">Economia</span>
                    </div>
                    <div class="card-body">
                        <h5 class="text-warning">Inflação de combustíveis pressiona IPCA de julho</h5>
                        <p class="text-light">O IPCA de julho registrou alta de 0,53%, acima das expectativas do mercado, com pressão significativa do grupo de transportes, especialmente combustíveis, que subiram 2,8% no mês.</p>
                        <p class="text-secondary mb-0">Fonte: IBGE</p>
                    </div>
                </div>

                <div class="card bg-dark border-secondary mb-3">
                    <div class="card-header bg-dark border-secondary d-flex justify-content-between">
                        <div>
                            <span class="text-muted small">02/08/2025</span>
                        </div>
                        <span class="badge bg-secondary">Regulações</span>
                    </div>
                    <div class="card-body">
                        <h5 class="text-warning">ANP aprova novas regras para qualidade de combustíveis</h5>
                        <p class="text-light">A Agência Nacional do Petróleo aprovou nesta semana novas regras para monitoramento e controle da qualidade dos combustíveis comercializados no país, incluindo testes mais rigorosos.</p>
                        <p class="text-secondary mb-0">Fonte: ANP</p>
                    </div>
                </div>

                <div class="card bg-dark border-secondary">
                    <div class="card-header bg-dark border-secondary d-flex justify-content-between">
                        <div>
                            <span class="text-muted small">01/08/2025</span>
                        </div>
                        <span class="badge bg-secondary">Combustíveis</span>
                    </div>
                    <div class="card-body">
                        <h5 class="text-warning">Etanol ganha competitividade frente à gasolina em cinco estados</h5>
                        <p class="text-light">O etanol está mais vantajoso que a gasolina em cinco estados brasileiros, segundo pesquisa da ANP. A relação de preços está abaixo de 70% em São Paulo, Minas Gerais, Mato Grosso, Goiás e Paraná.</p>
                        <p class="text-secondary mb-0">Fonte: Valor Econômico</p>
                    </div>
                </div>
            </div>

            <div class="col-12 text-center mt-2">
                <button class="btn btn-shell-yellow">
                    <i class="fas fa-plus"></i> Carregar Mais Notícias
                </button>
            </div>
        </div>
    `;

    // Adicionar eventos
    const btnAtualizar = container.querySelector('.btn-outline-warning');
    if (btnAtualizar) {
        btnAtualizar.addEventListener('click', function() {
            // Simular atualização
            const loadingToast = document.createElement('div');
            loadingToast.className = 'toast-message success show';
            loadingToast.innerHTML = '<i class="fas fa-check-circle"></i> Notícias atualizadas com sucesso!';
            document.body.appendChild(loadingToast);

            setTimeout(() => {
                loadingToast.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(loadingToast);
                }, 300);
            }, 2000);
        });
    }

    const filterSelect = container.querySelector('.form-select');
    if (filterSelect) {
        filterSelect.addEventListener('change', function() {
            // Simular filtragem
            const loadingToast = document.createElement('div');
            loadingToast.className = 'toast-message info show';
            loadingToast.innerHTML = '<i class="fas fa-filter"></i> Filtrando notícias por categoria...';
            document.body.appendChild(loadingToast);

            setTimeout(() => {
                loadingToast.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(loadingToast);
                }, 300);
            }, 1500);
        });
    }
}



/**
 * Cria conteúdo para a ferramenta de relatórios
 * @param {HTMLElement} container - Container onde o conteúdo será inserido
 */
function createRelatoriosContent(container) {
    container.innerHTML = `
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card bg-dark border-secondary h-100">
                    <div class="card-header bg-dark border-secondary">
                        <h5 class="text-warning">Gerar Relatório</h5>
                    </div>
                    <div class="card-body">
                        <form id="formGerarRelatorio">
                            <div class="mb-3">
                                <label class="form-label text-light">Tipo de Relatório</label>
                                <select class="form-control bg-dark text-light border-secondary" id="selectTipoRelatorio">
                                    <option value="margem">Análise de Margens</option>
                                    <option value="vendas">Volume de Vendas</option>
                                    <option value="precos">Comparativo de Preços</option>
                                    <option value="impostos">Carga Tributária</option>
                                    <option value="eficiencia">Eficiência Operacional</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-light">Período</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-dark border-secondary text-light">
                                        <i class="fas fa-calendar-alt"></i>
                                    </span>
                                    <input type="text" class="form-control bg-dark text-light border-secondary"
                                           id="periodoRelatorio" value="01/07/2025 - 31/07/2025">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-light">Filiais</label>
                                <select class="form-control bg-dark text-light border-secondary" id="selectFiliaisRelatorio" multiple size="4">
                                    <option value="all" selected>Todas as Filiais</option>
                                    <option value="sp">São Paulo</option>
                                    <option value="rj">Rio de Janeiro</option>
                                    <option value="mg">Belo Horizonte</option>
                                    <option value="rs">Porto Alegre</option>
                                    <option value="pr">Curitiba</option>
                                </select>
                                <small class="text-muted">Ctrl+Clique para selecionar múltiplas</small>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-light">Formato</label>
                                <div class="d-flex gap-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="formatoRelatorio" id="formatoPDF" value="pdf" checked>
                                        <label class="form-check-label text-light" for="formatoPDF">
                                            PDF
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="formatoRelatorio" id="formatoExcel" value="excel">
                                        <label class="form-check-label text-light" for="formatoExcel">
                                            Excel
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="formatoRelatorio" id="formatoCSV" value="csv">
                                        <label class="form-check-label text-light" for="formatoCSV">
                                            CSV
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-shell-yellow w-100" id="btnGerarRelatorio">
                                <i class="fas fa-file-export"></i> Gerar Relatório
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-8 mb-4">
                <div class="card bg-dark border-secondary">
                    <div class="card-header bg-dark border-secondary">
                        <h5 class="text-warning">Relatórios Recentes</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-dark table-hover">
                                <thead>
                                    <tr>
                                        <th>Nome</th>
                                        <th>Tipo</th>
                                        <th>Data</th>
                                        <th>Formato</th>
                                        <th>Tamanho</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Análise de Margens - Julho</td>
                                        <td>Margens</td>
                                        <td>01/08/2025</td>
                                        <td>PDF</td>
                                        <td>1.2 MB</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-light me-1">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-light">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Volume de Vendas - Q2</td>
                                        <td>Vendas</td>
                                        <td>15/07/2025</td>
                                        <td>Excel</td>
                                        <td>3.5 MB</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-light me-1">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-light">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Comparativo de Preços - Junho</td>
                                        <td>Preços</td>
                                        <td>01/07/2025</td>
                                        <td>PDF</td>
                                        <td>0.9 MB</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-light me-1">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-light">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Carga Tributária - Q2</td>
                                        <td>Impostos</td>
                                        <td>10/07/2025</td>
                                        <td>PDF</td>
                                        <td>1.8 MB</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-light me-1">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-light">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Eficiência Operacional - Maio</td>
                                        <td>Eficiência</td>
                                        <td>05/06/2025</td>
                                        <td>Excel</td>
                                        <td>2.4 MB</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-light me-1">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-light">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="card bg-dark border-secondary mt-3">
                    <div class="card-header bg-dark border-secondary">
                        <h5 class="text-warning">Relatórios Agendados</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-dark table-sm">
                                <thead>
                                    <tr>
                                        <th>Nome</th>
                                        <th>Frequência</th>
                                        <th>Próximo</th>
                                        <th>Status</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Análise de Margens Mensal</td>
                                        <td>Mensal</td>
                                        <td>01/09/2025</td>
                                        <td>
                                            <span class="badge bg-success">Ativo</span>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-light me-1">
                                                <i class="fas fa-pause"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-light">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Volume de Vendas Trimestral</td>
                                        <td>Trimestral</td>
                                        <td>01/10/2025</td>
                                        <td>
                                            <span class="badge bg-success">Ativo</span>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-light me-1">
                                                <i class="fas fa-pause"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-light">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Adicionar eventos
    const btnGerar = container.querySelector('#btnGerarRelatorio');
    if (btnGerar) {
        btnGerar.addEventListener('click', function() {
            // Simular geração de relatório
            const loadingToast = document.createElement('div');
            loadingToast.className = 'toast-message info show';
            loadingToast.innerHTML = '<i class="fas fa-cog fa-spin"></i> Gerando relatório...';
            document.body.appendChild(loadingToast);

            setTimeout(() => {
                loadingToast.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(loadingToast);

                    // Mostrar toast de sucesso
                    const successToast = document.createElement('div');
                    successToast.className = 'toast-message success show';
                    successToast.innerHTML = '<i class="fas fa-check-circle"></i> Relatório gerado com sucesso!';
                    document.body.appendChild(successToast);

                    setTimeout(() => {
                        successToast.classList.remove('show');
                        setTimeout(() => {
                            document.body.removeChild(successToast);
                        }, 300);
                    }, 2000);
                }, 300);
            }, 3000);
        });
    }

    // Adicionar eventos para botões de download e visualização
    const downloadButtons = container.querySelectorAll('.fas.fa-download');
    downloadButtons.forEach(btn => {
        btn.parentElement.addEventListener('click', function() {
            // Simular download
            const loadingToast = document.createElement('div');
            loadingToast.className = 'toast-message info show';
            loadingToast.innerHTML = '<i class="fas fa-download"></i> Preparando download...';
            document.body.appendChild(loadingToast);

            setTimeout(() => {
                loadingToast.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(loadingToast);
                }, 300);
            }, 1500);
        });
    });
}

// Inicializar componentes
document.addEventListener('DOMContentLoaded', () => {
    // Referências aos elementos
    const flipContainer = document.querySelector('.finance-flip-container');
    const btnVoltarFerramentas = document.getElementById('btnVoltarFerramentas');
    const toolNavItems = document.querySelectorAll('.tool-nav-item');
    const toolContentPanels = document.querySelectorAll('.tool-content-panel');

    // Função para inicializar a animação do painel flip
    function initFinanceFlip() {
        // Event Listeners para os cards de ferramenta
        const toolCards = document.querySelectorAll('.finance-tool-card');

        toolCards.forEach(card => {
            card.addEventListener('click', function() {
                const toolId = this.getAttribute('data-tool');

                // Carregar conteúdo antes de fazer o flip
                loadToolContent(toolId);

                // Ativar a navegação correspondente
                activateToolNav(toolId);

                // Adicionar classe para o efeito de flip
                flipContainer.classList.add('flipped');

                // Mostrar botão de voltar
                btnVoltarFerramentas.classList.remove('d-none');
            });
        });

        // Event Listener para o botão voltar
        btnVoltarFerramentas.addEventListener('click', function() {
            flipContainer.classList.remove('flipped');
            btnVoltarFerramentas.classList.add('d-none');
        });

        // Event Listeners para a navegação de ferramentas
        toolNavItems.forEach(navItem => {
            navItem.addEventListener('click', function() {
                const toolId = this.getAttribute('data-tool');
                loadToolContent(toolId);
                activateToolNav(toolId);
            });
        });
    }

    // Função para ativar a navegação da ferramenta
    function activateToolNav(toolId) {
        // Remover classe active de todos os itens de navegação
        toolNavItems.forEach(item => {
            item.classList.remove('active');
        });

        // Adicionar classe active ao item correspondente
        const activeNavItem = document.querySelector(`.tool-nav-item[data-tool="${toolId}"]`);
        if (activeNavItem) {
            activeNavItem.classList.add('active');
        }

        // Esconder todos os painéis de conteúdo
        toolContentPanels.forEach(panel => {
            panel.classList.remove('active');
        });

        // Mostrar o painel ativo
        const activePanel = document.getElementById(`${toolId}-content`);
        if (activePanel) {
            activePanel.classList.add('active');
        }
    }

    // Função para carregar o conteúdo da ferramenta
    function loadToolContent(toolId) {
        const contentPanel = document.getElementById(`${toolId}-content`);
        if (!contentPanel) return;

        const contentBody = contentPanel.querySelector('.tool-content-body');
        if (!contentBody || contentBody.dataset.loaded === 'true') return;

        // Carregar conteúdo específico baseado no ID da ferramenta
        switch (toolId) {
            case 'calculadora-margem':
                createCalculadoraContent(contentBody);
                break;
            case 'cotacoes':
                createCotacoesContent(contentBody);
                break;
            case 'impostos':
                createImpostosContent(contentBody);
                break;
            case 'historico':
                createHistoricoContent(contentBody);
                break;
            case 'noticias':
                createNoticiasContent(contentBody);
                break;
            case 'relatorios':
                createRelatoriosContent(contentBody);
                break;
            default:
                contentBody.innerHTML = `
                    <div class="text-center p-5">
                        <div class="mb-4">
                            <i class="fas fa-cogs fa-4x text-warning"></i>
                        </div>
                        <h4 class="text-light mb-3">Ferramenta em Desenvolvimento</h4>
                        <p class="text-secondary">Esta funcionalidade estará disponível em breve!</p>
                    </div>
                `;
        }

        // Marcar como carregado
        contentBody.dataset.loaded = 'true';
    }

    // Inicializar o flip das ferramentas financeiras
    if (flipContainer) {
        initFinanceFlip();
    }
});

// Adicionar estilos CSS para os controles do gráfico
const styleElement = document.createElement('style');
styleElement.textContent = `
    .chart-container {
        position: relative;
        width: 100%;
        height: 400px;
        margin-bottom: 1rem;
    }

    .chart-controls {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 10;
        background-color: rgba(51, 51, 51, 0.7);
        border-radius: 4px;
        padding: 5px;
    }

    .chart-tools .btn {
        margin: 0 2px;
        font-size: 0.8rem;
        padding: 0.2rem 0.5rem;
        color: #f8f9fa;
        border-color: #666;
    }

    .chart-tools .btn:hover {
        background-color: #555;
        border-color: #888;
    }

    .chart-tools .btn.active {
        background-color: #FDB813;
        border-color: #FDB813;
        color: #333;
    }

    .aviso-normalizacao {
        margin-bottom: 10px;
        padding: 5px 10px;
        background-color: rgba(51, 51, 51, 0.7);
        border-radius: 4px;
        border-left: 3px solid #FDB813;
    }

    /* Estilo para o modo de tela cheia */
    .fullscreen-chart {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 9999;
        background-color: #333;
        padding: 20px;
        display: flex;
        flex-direction: column;
    }

    .fullscreen-chart .chart-controls {
        position: absolute;
        top: 20px;
        right: 20px;
    }

    .fullscreen-chart canvas {
        flex: 1;
        width: 100% !important;
        height: calc(100% - 80px) !important;
    }

    .fullscreen-chart .fullscreen-close {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: #ED1C24;
        color: white;
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        font-size: 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10001;
    }

    /* Garantir que o fundo da página esteja correto */
    body {
        background-color: #333;
        color: #f8f9fa;
        min-height: 100vh;
    }

    .content-with-sidebar {
        min-height: calc(100vh - 60px);
        background-color: #333;
    }

    /* Tornar responsivo */
    @media (max-width: 768px) {
        .chart-container {
            height: 300px;
        }

        .chart-controls {
            top: 5px;
            right: 5px;
        }

        .chart-tools .btn {
            font-size: 0.7rem;
            padding: 0.1rem 0.3rem;
            margin: 0 1px;
        }
    }
`;

document.head.appendChild(styleElement);
