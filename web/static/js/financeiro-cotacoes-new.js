/**
 * Novo script para a página de cotações financeiras
 */

// Variáveis globais
let currencyChart = null;
let selectedCurrencies = ['usd'];
let currentPeriod = '7d';
let showPercentage = false;

// Dados de exemplo para o gráfico
const sampleData = {
    '7d': {
        'usd': [5.42, 5.41, 5.43, 5.45, 5.44, 5.40, 5.42],
        'eur': [5.89, 5.88, 5.90, 5.92, 5.91, 5.87, 5.89],
        'brent': [78.35, 78.20, 78.50, 78.90, 78.75, 78.10, 78.35],
        'wti': [74.82, 74.70, 74.95, 75.20, 75.10, 74.60, 74.82],
        'petr4': [38.25, 38.10, 38.30, 38.50, 38.40, 38.05, 38.25],
        'gbp': [6.87, 6.85, 6.89, 6.92, 6.90, 6.84, 6.87],
        'jpy': [0.036, 0.0359, 0.0361, 0.0362, 0.0361, 0.0358, 0.036],
        'chf': [6.12, 6.10, 6.14, 6.16, 6.15, 6.09, 6.12],
        'aud': [3.58, 3.57, 3.59, 3.61, 3.60, 3.56, 3.58],
        'cad': [3.98, 3.97, 3.99, 4.01, 4.00, 3.96, 3.98],
        'gold': [2345.60, 2340.50, 2350.75, 2360.20, 2355.80, 2335.40, 2345.60],
        'silver': [27.85, 27.75, 27.95, 28.10, 28.00, 27.65, 27.85],
        'gas': [2.15, 2.14, 2.16, 2.18, 2.17, 2.13, 2.15],
        'ethanol': [3.45, 3.44, 3.46, 3.48, 3.47, 3.43, 3.45],
        'corn': [4.28, 4.27, 4.29, 4.31, 4.30, 4.26, 4.28],
        'copper': [4.12, 4.10, 4.14, 4.16, 4.15, 4.09, 4.12],
        'aluminum': [2456.75, 2450.50, 2460.80, 2470.20, 2465.60, 2445.30, 2456.75]
    },
    '1m': generateRandomDataSets(30),
    '3m': generateRandomDataSets(90),
    '6m': generateRandomDataSets(180),
    '1y': generateRandomDataSets(365)
};

// Cores para as moedas
const currencyColors = {
    'usd': '#4CAF50',
    'eur': '#2196F3',
    'brent': '#FF9800',
    'wti': '#F44336',
    'petr4': '#9C27B0',
    'gbp': '#607D8B',
    'jpy': '#E91E63',
    'chf': '#00BCD4',
    'aud': '#8BC34A',
    'cad': '#FFEB3B',
    'gold': '#FFC107',
    'silver': '#9E9E9E',
    'gas': '#795548',
    'ethanol': '#3F51B5',
    'corn': '#CDDC39',
    'copper': '#FF5722',
    'aluminum': '#03A9F4'
};

// Nomes das moedas
const currencyNames = {
    'usd': 'Dólar Americano',
    'eur': 'Euro',
    'brent': 'Petróleo Brent',
    'wti': 'Petróleo WTI',
    'petr4': 'Petrobrás',
    'gbp': 'Libra Esterlina',
    'jpy': 'Iene Japonês',
    'chf': 'Franco Suíço',
    'aud': 'Dólar Australiano',
    'cad': 'Dólar Canadense',
    'gold': 'Ouro',
    'silver': 'Prata',
    'gas': 'Gás Natural',
    'ethanol': 'Etanol',
    'corn': 'Milho',
    'copper': 'Cobre',
    'aluminum': 'Alumínio'
};

/**
 * Gera conjuntos de dados aleatórios para todas as moedas
 * @param {number} days - Número de dias
 * @returns {Object} - Objeto com dados para cada moeda
 */
function generateRandomDataSets(days) {
    const currencies = Object.keys(currencyColors);
    const result = {};

    currencies.forEach(currency => {
        let baseValue;
        switch (currency) {
            case 'usd': baseValue = 5.4; break;
            case 'eur': baseValue = 5.9; break;
            case 'brent': baseValue = 78; break;
            case 'wti': baseValue = 75; break;
            case 'petr4': baseValue = 38; break;
            case 'gbp': baseValue = 6.9; break;
            case 'jpy': baseValue = 0.036; break;
            case 'chf': baseValue = 6.1; break;
            case 'aud': baseValue = 3.6; break;
            case 'cad': baseValue = 4.0; break;
            case 'gold': baseValue = 2345; break;
            case 'silver': baseValue = 28; break;
            case 'gas': baseValue = 2.1; break;
            case 'ethanol': baseValue = 3.4; break;
            case 'corn': baseValue = 4.3; break;
            case 'copper': baseValue = 4.1; break;
            case 'aluminum': baseValue = 2450; break;
            default: baseValue = 1.0;
        }

        result[currency] = generateRandomValues(baseValue * 0.9, baseValue * 1.1, days);
    });

    return result;
}

/**
 * Inicializa o gráfico de cotações
 */
function initCurrencyChart() {
    const ctx = document.getElementById('currencyChart');
    if (!ctx) {
        console.error('Elemento do gráfico não encontrado');
        return;
    }

    // Configurações do gráfico
    const chartConfig = {
        type: 'line',
        data: {
            labels: generateDateLabels(currentPeriod),
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#FDB813',
                    bodyColor: '#fff',
                    borderColor: '#FDB813',
                    borderWidth: 1,
                    padding: 10,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                const value = context.parsed.y;
                                const formattedValue = showPercentage ?
                                    value.toFixed(2) + '%' :
                                    formatCurrencyValue(value, context.dataset.id);
                                label += formattedValue;
                            }
                            return label;
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.05)'
                    },
                    ticks: {
                        color: '#aaa',
                        maxRotation: 0,
                        autoSkip: true,
                        maxTicksLimit: 10
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.05)'
                    },
                    ticks: {
                        color: '#aaa',
                        callback: function(value) {
                            if (showPercentage) {
                                return value.toFixed(1) + '%';
                            }

                            // Simplificar valores grandes
                            if (value >= 1000) {
                                return (value / 1000).toFixed(1) + 'k';
                            }
                            return value.toFixed(2);
                        }
                    }
                }
            },
            elements: {
                point: {
                    radius: 2,
                    hoverRadius: 4,
                    hitRadius: 10
                },
                line: {
                    tension: 0.3
                }
            }
        }
    };

    // Criar o gráfico
    currencyChart = new Chart(ctx, chartConfig);

    // Adicionar dataset inicial
    selectedCurrencies.forEach(currency => {
        addCurrencyDataset(currencyChart.data, currency);
    });

    // Atualizar o gráfico
    currencyChart.update();

    // Inicializar eventos
    initChartEvents();
    initCardEvents();
}

/**
 * Adiciona um dataset de moeda ao gráfico
 * @param {Object} chartData - Dados do gráfico
 * @param {string} currencyCode - Código da moeda
 */
function addCurrencyDataset(chartData, currencyCode) {
    // Verificar se o dataset já existe
    const existingDataset = chartData.datasets.find(dataset => dataset.id === currencyCode);
    if (existingDataset) return;

    // Obter os valores para o período atual
    const values = sampleData[currentPeriod][currencyCode] || [];
    if (values.length === 0) return;

    // Criar o dataset
    const dataset = {
        id: currencyCode,
        label: currencyNames[currencyCode] || currencyCode.toUpperCase(),
        data: showPercentage ? calculatePercentageValues(values, currencyCode) : values,
        borderColor: currencyColors[currencyCode] || '#FDB813',
        backgroundColor: hexToRgba(currencyColors[currencyCode], 0.1) || 'rgba(253, 184, 19, 0.1)',
        borderWidth: 2,
        fill: true,
        pointBackgroundColor: currencyColors[currencyCode] || '#FDB813',
        pointBorderColor: '#fff',
        pointBorderWidth: 1,
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: currencyColors[currencyCode] || '#FDB813',
        pointHoverBorderWidth: 2
    };

    // Adicionar o dataset
    chartData.datasets.push(dataset);
}

/**
 * Remove um dataset de moeda do gráfico
 * @param {string} currencyCode - Código da moeda
 */
function removeCurrencyDataset(currencyCode) {
    // Verificar se o dataset existe
    const datasetIndex = currencyChart.data.datasets.findIndex(dataset => dataset.id === currencyCode);
    if (datasetIndex === -1) return;

    // Remover o dataset
    currencyChart.data.datasets.splice(datasetIndex, 1);

    // Remover da lista de moedas selecionadas
    const currencyIndex = selectedCurrencies.indexOf(currencyCode);
    if (currencyIndex > -1) {
        selectedCurrencies.splice(currencyIndex, 1);
    }

    // Atualizar o gráfico
    currencyChart.update();
}

/**
 * Atualiza o período do gráfico
 * @param {string} period - Período (7d, 1m, 3m, 6m, 1y)
 */
function updateChartPeriod(period) {
    // Atualizar o período atual
    currentPeriod = period;

    // Atualizar os labels
    currencyChart.data.labels = generateDateLabels(period);

    // Atualizar os datasets
    currencyChart.data.datasets.forEach(dataset => {
        const currencyCode = dataset.id;
        const values = sampleData[period][currencyCode] || [];

        if (values.length > 0) {
            dataset.data = showPercentage ?
                calculatePercentageValues(values, currencyCode) :
                values;
        }
    });

    // Atualizar o gráfico
    currencyChart.update();
}

/**
 * Calcula valores percentuais com base no valor inicial
 * @param {Array} values - Array de valores
 * @returns {Array} - Array de valores percentuais
 */
function calculatePercentageValues(values, currencyCode) {
    if (!values || values.length === 0) return [];

    const initialValue = values[0];
    return values.map(value => ((value - initialValue) / initialValue) * 100);
}

/**
 * Gera labels de data para o gráfico
 * @param {string} period - Período (7d, 1m, 3m, 6m, 1y)
 * @returns {Array} - Array de labels de data
 */
function generateDateLabels(period) {
    const today = new Date();
    const labels = [];
    let days;

    switch (period) {
        case '7d': days = 7; break;
        case '1m': days = 30; break;
        case '3m': days = 90; break;
        case '6m': days = 180; break;
        case '1y': days = 365; break;
        default: days = 7;
    }

    for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(today.getDate() - i);

        // Formatar a data de acordo com o período
        let label;
        if (period === '7d' || period === '1m') {
            label = date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
        } else {
            label = date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
        }

        labels.push(label);
    }

    return labels;
}

/**
 * Formata o valor da moeda de acordo com o código
 * @param {number} value - Valor a ser formatado
 * @param {string} currencyCode - Código da moeda
 * @returns {string} - Valor formatado
 */
function formatCurrencyValue(value, currencyCode) {
    if (currencyCode === 'usd' || currencyCode === 'eur' || currencyCode === 'gbp' ||
        currencyCode === 'aud' || currencyCode === 'cad' || currencyCode === 'petr4') {
        return 'R$ ' + value.toFixed(2);
    } else if (currencyCode === 'jpy') {
        return 'R$ ' + value.toFixed(3);
    } else if (currencyCode === 'brent' || currencyCode === 'wti' ||
               currencyCode === 'gold' || currencyCode === 'silver' ||
               currencyCode === 'gas' || currencyCode === 'copper') {
        return '$ ' + value.toFixed(2);
    } else if (currencyCode === 'ethanol') {
        return 'R$ ' + value.toFixed(2);
    } else if (currencyCode === 'corn') {
        return '$ ' + value.toFixed(2);
    } else if (currencyCode === 'aluminum') {
        return '$ ' + value.toFixed(2);
    }

    return value.toFixed(2);
}

/**
 * Converte uma cor hexadecimal para RGBA
 * @param {string} hex - Cor em formato hexadecimal
 * @param {number} alpha - Valor de transparência (0-1)
 * @returns {string} - Cor em formato RGBA
 */
function hexToRgba(hex, alpha) {
    if (!hex) return `rgba(0, 0, 0, ${alpha})`;

    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);

    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

/**
 * Inicializa os eventos do gráfico
 */
function initChartEvents() {
    // Botões de período
    const periodButtons = document.querySelectorAll('.period-button');
    periodButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remover classe active de todos os botões
            periodButtons.forEach(btn => btn.classList.remove('active'));
            // Adicionar classe active ao botão clicado
            this.classList.add('active');

            // Atualizar o gráfico com o novo período
            updateChartPeriod(this.getAttribute('data-period'));
        });
    });

    // Toggle para exibição em percentual
    const percentageToggle = document.getElementById('percentageToggle');
    if (percentageToggle) {
        percentageToggle.addEventListener('change', function() {
            showPercentage = this.checked;

            // Atualizar todos os datasets
            currencyChart.data.datasets.forEach(dataset => {
                const currencyCode = dataset.id;
                const values = sampleData[currentPeriod][currencyCode] || [];

                if (values.length > 0) {
                    dataset.data = showPercentage ?
                        calculatePercentageValues(values, currencyCode) :
                        values;
                }
            });

            // Atualizar o gráfico
            currencyChart.update();
        });
    }

    // Criar e adicionar a legenda
    updateChartLegend();
}

/**
 * Inicializa os eventos dos mini-cards de cotações
 */
function initCardEvents() {
    console.log('Inicializando eventos dos mini-cards');

    // Adicionar evento de clique aos mini-cards
    const miniCards = document.querySelectorAll('.mini-card');
    console.log('Mini-cards encontrados:', miniCards.length);

    // Remover eventos existentes para evitar duplicação
    miniCards.forEach(card => {
        const clone = card.cloneNode(true);
        if (card.parentNode) {
            card.parentNode.replaceChild(clone, card);
        }
    });

    // Obter os cards novamente após a substituição
    const newMiniCards = document.querySelectorAll('.mini-card');

    newMiniCards.forEach(card => {
        // Obter o código da moeda do atributo data-currency
        const currencyCode = card.getAttribute('data-currency');
        if (!currencyCode) return;

        console.log('Adicionando evento ao mini-card:', currencyCode);

        // Adicionar evento de clique diretamente
        card.onclick = function(e) {
            console.log('Mini-card clicado:', currencyCode);
            e.stopPropagation(); // Evitar propagação para não acionar outros eventos

            const isSelected = this.classList.contains('selected');

            if (isSelected) {
                // Remover do gráfico
                console.log('Removendo do gráfico:', currencyCode);
                this.classList.remove('selected');

                // Não permitir remover se for a única moeda
                if (selectedCurrencies.length <= 1) return;

                const index = selectedCurrencies.indexOf(currencyCode);
                if (index > -1) {
                    selectedCurrencies.splice(index, 1);
                }

                removeCurrencyDataset(currencyCode);
                updateChartLegend();
            } else {
                // Adicionar ao gráfico
                console.log('Adicionando ao gráfico:', currencyCode);
                this.classList.add('selected');

                if (!selectedCurrencies.includes(currencyCode)) {
                    selectedCurrencies.push(currencyCode);
                    addCurrencyDataset(currencyChart.data, currencyCode);
                    currencyChart.update();
                    updateChartLegend();
                }
            }
        };

        // Marcar como selecionado se já estiver no gráfico
        if (selectedCurrencies.includes(currencyCode)) {
            card.classList.add('selected');
        }
    });

    // Adicionar evento de clique às linhas da tabela
    initTableRowEvents();
}

/**
 * Inicializa os eventos das linhas da tabela
 */
function initTableRowEvents() {
    const tableRows = document.querySelectorAll('.cotacoes-table tbody tr');
    console.log('Linhas de tabela encontradas:', tableRows.length);

    tableRows.forEach(row => {
        // Obter o código da moeda do atributo data-currency
        const currencyCode = row.getAttribute('data-currency');
        if (!currencyCode) return;

        console.log('Adicionando evento à linha da tabela:', currencyCode);

        // Adicionar evento de clique
        row.addEventListener('click', function() {
            console.log('Linha da tabela clicada:', currencyCode);

            const isSelected = this.classList.contains('selected');

            if (isSelected) {
                // Remover do gráfico
                console.log('Removendo do gráfico:', currencyCode);
                this.classList.remove('selected');

                // Não permitir remover se for a única moeda
                if (selectedCurrencies.length <= 1) return;

                removeCurrencyDataset(currencyCode);

                // Remover seleção do mini-card correspondente
                const miniCard = document.querySelector(`.mini-card[data-currency="${currencyCode}"]`);
                if (miniCard) miniCard.classList.remove('selected');

                updateChartLegend();
            } else {
                // Adicionar ao gráfico
                console.log('Adicionando ao gráfico:', currencyCode);
                this.classList.add('selected');

                if (!selectedCurrencies.includes(currencyCode)) {
                    selectedCurrencies.push(currencyCode);
                    addCurrencyDataset(currencyChart.data, currencyCode);
                    currencyChart.update();

                    // Adicionar seleção ao mini-card correspondente
                    const miniCard = document.querySelector(`.mini-card[data-currency="${currencyCode}"]`);
                    if (miniCard) miniCard.classList.add('selected');

                    updateChartLegend();
                }
            }
        });

        // Marcar como selecionado se já estiver no gráfico
        if (selectedCurrencies.includes(currencyCode)) {
            row.classList.add('selected');
        }
    });
}

/**
 * Atualiza a legenda do gráfico
 */
function updateChartLegend() {
    const legendItems = document.querySelector('.legend-items');
    if (!legendItems) return;

    // Limpar a legenda atual
    legendItems.innerHTML = '';

    // Adicionar um item para cada moeda selecionada
    selectedCurrencies.forEach(currencyCode => {
        const legendItem = document.createElement('div');
        legendItem.className = 'legend-item';
        legendItem.setAttribute('data-currency', currencyCode);

        const colorDot = document.createElement('span');
        colorDot.className = 'legend-color';
        colorDot.style.backgroundColor = currencyColors[currencyCode] || '#FDB813';

        const label = document.createElement('span');
        label.className = 'legend-label';
        label.textContent = currencyNames[currencyCode] || currencyCode.toUpperCase();

        legendItem.appendChild(colorDot);
        legendItem.appendChild(label);

        // Adicionar evento de clique para remover a moeda do gráfico
        legendItem.addEventListener('click', function() {
            const code = this.getAttribute('data-currency');

            // Não permitir remover se for a única moeda
            if (selectedCurrencies.length <= 1) return;

            // Remover do gráfico
            removeCurrencyDataset(code);

            // Remover a classe selected do mini-card correspondente
            const miniCard = document.querySelector(`.mini-card[data-currency="${code}"]`);
            if (miniCard) miniCard.classList.remove('selected');

            // Remover a classe selected da linha da tabela correspondente
            const tableRow = document.querySelector(`.cotacoes-table tr[data-currency="${code}"]`);
            if (tableRow) tableRow.classList.remove('selected');

            // Atualizar a legenda
            updateChartLegend();
        });

        legendItems.appendChild(legendItem);
    });
}

/**
 * Inicializa a busca na tabela
 */
function initTableSearch() {
    const searchInput = document.getElementById('cotacoes-search');
    if (!searchInput) return;

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('.cotacoes-table tbody tr');

        tableRows.forEach(row => {
            const text = row.querySelector('td').textContent.toLowerCase();

            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
}

/**
 * Inicializa os filtros da tabela
 */
function initTableFilters() {
    const filterButtons = document.querySelectorAll('.table-filter button');
    if (filterButtons.length === 0) return;

    // Definir quais linhas são moedas e quais são commodities
    const tableRows = document.querySelectorAll('.cotacoes-table tbody tr');
    tableRows.forEach(row => {
        const text = row.querySelector('td').textContent.toLowerCase();

        // Classificar como moeda ou commodity
        if (text.includes('dólar') || text.includes('euro') || text.includes('libra') ||
            text.includes('iene') || text.includes('franco') || text.includes('brl')) {
            row.setAttribute('data-type', 'moedas');
        } else {
            row.setAttribute('data-type', 'commodities');
        }
    });

    // Adicionar eventos aos botões de filtro
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remover classe active de todos os botões
            filterButtons.forEach(btn => btn.classList.remove('active'));
            // Adicionar classe active ao botão clicado
            this.classList.add('active');

            const filter = this.getAttribute('data-filter');

            // Aplicar filtro
            tableRows.forEach(row => {
                if (filter === 'all') {
                    row.style.display = '';
                } else if (row.getAttribute('data-type') === filter) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    });
}

/**
 * Gera valores aleatórios dentro de um intervalo
 * @param {number} min - Valor mínimo
 * @param {number} max - Valor máximo
 * @param {number} count - Quantidade de valores
 * @returns {Array} - Array de valores aleatórios
 */
function generateRandomValues(min, max, count) {
    const values = [];
    let lastValue = min + Math.random() * (max - min);

    for (let i = 0; i < count; i++) {
        // Gerar um valor próximo ao anterior para criar uma tendência
        const change = (Math.random() - 0.5) * (max - min) * 0.05;
        lastValue = Math.max(min, Math.min(max, lastValue + change));
        values.push(parseFloat(lastValue.toFixed(2)));
    }

    return values;
}

/**
 * Inicializa a página de cotações
 */
function initCotacoesPage() {
    console.log('Inicializando página de cotações');

    // Inicializar o gráfico
    if (document.getElementById('currencyChart')) {
        initCurrencyChart();

        // Inicializar eventos dos cards e linhas da tabela
        setTimeout(() => {
            initCardEvents();
        }, 300);
    }

    // Inicializar a busca na tabela
    initTableSearch();

    // Inicializar os filtros da tabela
    initTableFilters();
}

// Inicializar quando o documento estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    initCotacoesPage();

    // Adicionar evento para reinicializar quando o conteúdo for carregado dinamicamente
    document.addEventListener('contentLoaded', function() {
        console.log('Evento contentLoaded detectado, reinicializando...');
        setTimeout(() => {
            initCotacoesPage();
        }, 300);
    });
});
