/**
 * <PERSON><PERSON><PERSON><PERSON> para a página de Dashboard
 * Gerencia métricas, gráficos e dados recentes
 */
(function() {
    // Estado da aplicação
    let state = {
        charts: {},
        recentOrders: [],
        refreshInterval: null
    };

    // Variáveis globais
    const userRole = window.userRole; // Será definido no template
    let currentOrders = []; // Armazena as ordens atuais

    // Inicializa o módulo
    function init() {
        console.log("Dashboard module initialized");
        loadDashboardData();
        setupAutoRefresh();
    }

    // Carrega todos os dados do dashboard
    function loadDashboardData() {
        loadMetrics();
        loadRecentOrders();
        initializeCharts();
    }

    // Configura atualização automática dos dados
    function setupAutoRefresh() {
        // Atualiza os dados a cada 5 minutos
        state.refreshInterval = setInterval(loadDashboardData, 5 * 60 * 1000);
    }

    // Carrega as métricas para o dashboard
    function loadMetrics() {
        console.log("Loading metrics data...");
        // Simulação de dados
        const statusCounts = {
            pending: 5,
            inProgress: 8,
            completed: 12,
            canceled: 2
        };
        
        updateMetricCards(statusCounts);
    }

    // Atualiza os cards de métricas no dashboard
    function updateMetricCards(statusCounts) {
        console.log("Updating metric cards with:", statusCounts);
        // Implementar quando os elementos estiverem prontos no DOM
    }

    // Inicializa ou atualiza os gráficos do dashboard
    function initializeCharts() {
        console.log("Initializing charts...");
        
        // Simulação de dados para o gráfico de status
        const statusData = {
            labels: ['Pendentes', 'Em Progresso', 'Concluídas', 'Canceladas'],
            datasets: [{
                data: [5, 8, 12, 2],
                backgroundColor: ['#FDB813', '#1E88E5', '#43A047', '#E53935']
            }]
        };
        
        initStatusChart(statusData);
        initPriorityChart();
    }

    // Inicializa ou atualiza o gráfico de status
    function initStatusChart(statusData) {
        console.log("Initializing status chart with:", statusData);
        // Implementar quando a biblioteca de gráficos estiver disponível
    }

    // Inicializa ou atualiza o gráfico de prioridades
    function initPriorityChart() {
        console.log("Initializing priority chart...");
        // Implementar quando a biblioteca de gráficos estiver disponível
    }

    // Carrega as ordens de manutenção mais recentes
    function loadRecentOrders() {
        console.log("Loading recent orders...");
        
        // Simulação de dados
        const recentOrders = [
            {
                id: 1,
                title: "Manutenção de Bomba #3",
                station: "Posto Shell Centro",
                status: "in_progress",
                priority: "high",
                created_at: "2025-03-20T14:30:00Z"
            },
            {
                id: 2,
                title: "Calibração de Bombas",
                station: "Posto Shell Rodovia",
                status: "pending",
                priority: "medium",
                created_at: "2025-03-21T09:15:00Z"
            }
        ];
        
        state.recentOrders = recentOrders;
        renderRecentOrdersTable(recentOrders);
    }

    // Renderiza a tabela de ordens recentes
    function renderRecentOrdersTable(orders) {
        console.log("Rendering recent orders table with:", orders);
        // Implementar quando os elementos estiverem prontos no DOM
    }

    // Retorna a cor para o status
    function getStatusColor(status) {
        const colors = {
            pending: 'warning',
            in_progress: 'info',
            completed: 'success',
            canceled: 'danger'
        };
        
        return colors[status] || 'secondary';
    }

    // Traduz os valores de status para exibição
    function translateStatus(status) {
        const translations = {
            pending: 'Pendente',
            in_progress: 'Em Progresso',
            completed: 'Concluída',
            canceled: 'Cancelada'
        };
        
        return translations[status] || status;
    }

    // Funções de atualização de métricas
    function updateMetrics(data) {
        if (userRole === "branch") {
            document.getElementById("pendingOrdersCount").textContent = data.pending || 0;
            document.getElementById("inProgressOrdersCount").textContent = data.inProgress || 0;
            document.getElementById("completedOrdersCount").textContent = data.completed || 0;
            document.getElementById("rejectedOrdersCount").textContent = data.rejected || 0;
        } else if (userRole === "provider") {
            document.getElementById("assignedOrdersCount").textContent = data.assigned || 0;
            document.getElementById("executingOrdersCount").textContent = data.executing || 0;
            document.getElementById("completedTodayCount").textContent = data.completedToday || 0;
        }
    }

    // Funções de filtro
    document.getElementById("orderStatusFilter")?.addEventListener("change", filterOrders);
    document.getElementById("orderPriorityFilter")?.addEventListener("change", filterOrders);
    document.getElementById("providerFilter")?.addEventListener("change", filterOrders);
    document.getElementById("orderSearch")?.addEventListener("input", filterOrders);

    function filterOrders() {
        const status = document.getElementById("orderStatusFilter")?.value;
        const priority = document.getElementById("orderPriorityFilter")?.value;
        const provider = document.getElementById("providerFilter")?.value;
        const search = document.getElementById("orderSearch")?.value.toLowerCase();

        const filtered = currentOrders.filter(order => {
            const matchStatus = status === "all" || order.status === status;
            const matchPriority = priority === "all" || order.priority === priority;
            const matchProvider = provider === "all" || order.providerId === provider;
            const matchSearch = !search || 
                order.id.toString().includes(search) || 
                order.title.toLowerCase().includes(search);

            return matchStatus && matchPriority && matchProvider && matchSearch;
        });

        renderOrders(filtered);
    }

    // Funções de renderização
    function renderOrders(orders) {
        const grid = document.getElementById("serviceOrdersGrid");
        grid.innerHTML = "";

        if (orders.length === 0) {
            grid.innerHTML = `
                <div class="order-detail-placeholder">
                    <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                    <p>Nenhuma ordem encontrada</p>
                </div>`;
            return;
        }

        orders.forEach(order => {
            const card = createOrderCard(order);
            grid.appendChild(card);
        });
    }

    function createOrderCard(order) {
        const card = document.createElement("div");
        card.className = "order-card";
        card.innerHTML = `
            <div class="order-card-header">
                <span class="order-id">#${order.id}</span>
                <span class="order-status ${order.status}">${formatStatus(order.status)}</span>
            </div>
            <div class="order-card-body">
                <h5 class="order-title">${order.title}</h5>
                <p class="order-description">${order.description}</p>
            </div>
            <div class="order-card-footer">
                <span class="order-priority ${order.priority}">${formatPriority(order.priority)}</span>
                <button class="shell-btn shell-btn-sm" onclick="viewOrderDetails(${order.id})">
                    <i class="fas fa-eye"></i> Detalhes
                </button>
            </div>`;
        return card;
    }

    // Funções de formatação
    function formatStatus(status) {
        const statusMap = {
            pending: "Pendente",
            in_progress: "Em Andamento",
            completed: "Concluída",
            rejected: "Cancelada"
        };
        return statusMap[status] || status;
    }

    function formatPriority(priority) {
        const priorityMap = {
            low: "Baixa",
            medium: "Média",
            high: "Alta",
            urgent: "Urgente"
        };
        return priorityMap[priority] || priority;
    }

    // Funções de detalhes da ordem
    function viewOrderDetails(orderId) {
        const container = document.getElementById("orderDetailContainer");
        const content = document.getElementById("orderDetailContent");
        
        // Mostrar loading
        content.innerHTML = `
            <div class="text-center">
                <div class="shell-spinner"></div>
                <p class="mt-2">Carregando detalhes...</p>
            </div>`;
        container.style.display = "block";

        // Simular chamada à API
        setTimeout(() => {
            const order = currentOrders.find(o => o.id === orderId);
            if (order) {
                renderOrderDetails(order);
            } else {
                content.innerHTML = `
                    <div class="alert alert-danger">
                        Ordem não encontrada
                    </div>`;
            }
        }, 500);
    }

    function closeOrderDetails() {
        document.getElementById("orderDetailContainer").style.display = "none";
    }

    // Inicialização
    document.getElementById("refreshDataBtn")?.addEventListener("click", refreshData);

    function refreshData() {
        // Simular chamada à API
        setTimeout(() => {
            const data = {
                pending: Math.floor(Math.random() * 10),
                inProgress: Math.floor(Math.random() * 15),
                completed: Math.floor(Math.random() * 20),
                rejected: Math.floor(Math.random() * 5),
                assigned: Math.floor(Math.random() * 8),
                executing: Math.floor(Math.random() * 12),
                completedToday: Math.floor(Math.random() * 6)
            };
            updateMetrics(data);
        }, 500);
    }

    // Carregar dados iniciais
    refreshData();

    // Exporta a API pública
    window.DashboardModule = {
        init
    };

    // Inicializa o módulo quando o DOM estiver pronto
    document.addEventListener('DOMContentLoaded', init);
})();