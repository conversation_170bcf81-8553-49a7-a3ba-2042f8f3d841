// JavaScript para a página de detalhes da ordem de serviço

// Variáveis Globais
let costsModalInstance = null;
let interactionModalInstance = null;
let assignProviderModalInstance = null;

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando página de detalhes da ordem de serviço...');
    initializeModals();
    initializeForms();
    initializeButtons();
    addAnimationEffects();
});

// Funções de Inicialização
function initializeModals() {
    // Inicializar modais do Bootstrap
    const costsModal = document.getElementById('costsModal');
    if (costsModal) {
        costsModalInstance = new bootstrap.Modal(costsModal);
    }

    const interactionModal = document.getElementById('interactionModal');
    if (interactionModal) {
        interactionModalInstance = new bootstrap.Modal(interactionModal);
    }

    const assignProviderModal = document.getElementById('assignProviderModal');
    if (assignProviderModal) {
        assignProviderModalInstance = new bootstrap.Modal(assignProviderModal);
    }
}

function initializeForms() {
    // Inicializar formulários
    const addCostForm = document.getElementById('addCostForm');
    if (addCostForm) {
        addCostForm.addEventListener('submit', handleAddCostSubmit);
    }

    const addInteractionForm = document.getElementById('addInteractionForm');
    if (addInteractionForm) {
        addInteractionForm.addEventListener('submit', handleAddInteractionSubmit);
    }

    const assignProviderForm = document.getElementById('assignProviderForm');
    if (assignProviderForm) {
        assignProviderForm.addEventListener('submit', handleAssignProviderSubmit);
    }

    // Inicializar formulário de manutenção
    const saveMaintenanceBtn = document.getElementById('saveMaintenanceBtn');
    if (saveMaintenanceBtn) {
        saveMaintenanceBtn.addEventListener('click', handleSaveMaintenanceDescription);
    }
}

function initializeButtons() {
    // Botões de ação
    const addCostBtn = document.getElementById('addCostBtn');
    if (addCostBtn) {
        addCostBtn.addEventListener('click', function() {
            openCostsModal(this.dataset.orderId);
        });
    }

    const addInteractionBtn = document.getElementById('addInteractionBtn');
    if (addInteractionBtn) {
        addInteractionBtn.addEventListener('click', function() {
            openInteractionModal(this.dataset.orderId);
        });
    }

    const assignProviderBtn = document.getElementById('assignProviderBtn');
    if (assignProviderBtn) {
        assignProviderBtn.addEventListener('click', function() {
            openAssignProviderModal(this.dataset.orderId);
        });
    }

    // Botões de status
    const startOrderBtn = document.getElementById('startOrderBtn');
    if (startOrderBtn) {
        startOrderBtn.addEventListener('click', function() {
            updateOrderStatus(this.dataset.orderId, 'in_progress');
        });
    }

    const completeOrderBtn = document.getElementById('completeOrderBtn');
    if (completeOrderBtn) {
        completeOrderBtn.addEventListener('click', function() {
            updateOrderStatus(this.dataset.orderId, 'pending_approval');
        });
    }

    const resubmitOrderBtn = document.getElementById('resubmitOrderBtn');
    if (resubmitOrderBtn) {
        resubmitOrderBtn.addEventListener('click', function() {
            updateOrderStatus(this.dataset.orderId, 'pending_approval');
        });
    }

    const approveOrderBtn = document.getElementById('approveOrderBtn');
    if (approveOrderBtn) {
        approveOrderBtn.addEventListener('click', function() {
            updateOrderStatus(this.dataset.orderId, 'approved');
        });
    }

    const rejectOrderBtn = document.getElementById('rejectOrderBtn');
    if (rejectOrderBtn) {
        rejectOrderBtn.addEventListener('click', function() {
            updateOrderStatus(this.dataset.orderId, 'revision_needed');
        });
    }

    const cancelOrderBtn = document.getElementById('cancelOrderBtn');
    if (cancelOrderBtn) {
        cancelOrderBtn.addEventListener('click', function() {
            confirmCancelOrder(this.dataset.orderId);
        });
    }

    const invoiceOrderBtn = document.getElementById('invoiceOrderBtn');
    if (invoiceOrderBtn) {
        invoiceOrderBtn.addEventListener('click', function() {
            openInvoiceModal(this.dataset.orderId);
        });
    }

    const markAsPaidBtn = document.getElementById('markAsPaidBtn');
    if (markAsPaidBtn) {
        markAsPaidBtn.addEventListener('click', function() {
            markAsPaid(this.dataset.orderId);
        });
    }

    const printOrderBtn = document.getElementById('printOrderBtn');
    if (printOrderBtn) {
        printOrderBtn.addEventListener('click', printOrder);
    }
}

function addAnimationEffects() {
    // Adicionar efeitos de animação aos cards
    const cards = document.querySelectorAll('.order-card, .maintenance-title-section, .order-sidebar');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 10px 20px rgba(0, 0, 0, 0.3)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });

    // Adicionar eventos de clique aos cards
    const historyCard = document.querySelector('.order-card:nth-child(1)');
    if (historyCard) {
        historyCard.addEventListener('click', function() {
            // Redirecionar para a página de histórico do equipamento
            const equipmentId = CURRENT_EQUIPMENT_ID || 0;
            if (equipmentId > 0) {
                window.location.href = `/equipment/${equipmentId}/history`;
            } else {
                showToast('Equipamento não encontrado', 'warning');
            }
        });
    }

    const scheduleCard = document.querySelector('.order-card:nth-child(4)');
    if (scheduleCard) {
        scheduleCard.addEventListener('click', function() {
            // Redirecionar para a página de calendário
            window.location.href = '/calendario';
        });
    }
}

// Funções de Manipulação de Formulários
async function handleSaveMaintenanceDescription() {
    const orderId = this.dataset.orderId;
    const description = document.getElementById('maintenanceDescription').value;

    if (!description.trim()) {
        showToast('A descrição não pode estar vazia.', 'warning');
        return;
    }

    try {
        const response = await fetch(`/api/orders/${orderId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                description: description
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
        }

        showToast('Descrição da manutenção atualizada com sucesso!', 'success');
    } catch (error) {
        console.error('Erro ao atualizar descrição:', error);
        showToast(`Erro ao atualizar descrição: ${error.message}`, 'error');
    }
}

async function handleAddCostSubmit(event) {
    event.preventDefault();

    const orderId = document.getElementById('costOrderId').value;
    const description = document.getElementById('costDescription').value;
    const quantity = document.getElementById('costQuantity').value;
    const unitPrice = document.getElementById('costUnitPrice').value;

    if (!description || !quantity || !unitPrice) {
        showToast('Todos os campos são obrigatórios.', 'warning');
        return;
    }

    try {
        const response = await fetch(`/api/orders/${orderId}/costs`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                description: description,
                quantity: parseFloat(quantity),
                unit_price: parseFloat(unitPrice)
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
        }

        // Limpar formulário
        document.getElementById('costDescription').value = '';
        document.getElementById('costQuantity').value = '1';
        document.getElementById('costUnitPrice').value = '';

        // Fechar modal e atualizar lista de custos
        costsModalInstance.hide();
        await loadCostsForOrder(orderId);

        showToast('Custo adicionado com sucesso!', 'success');
    } catch (error) {
        console.error('Erro ao adicionar custo:', error);
        showToast(`Erro ao adicionar custo: ${error.message}`, 'error');
    }
}

async function handleAddInteractionSubmit(event) {
    event.preventDefault();

    const orderId = document.getElementById('interactionOrderId').value;
    const message = document.getElementById('interactionMessage').value;

    if (!message.trim()) {
        showToast('A mensagem não pode estar vazia.', 'warning');
        return;
    }

    try {
        const response = await fetch(`/api/orders/${orderId}/interactions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
        }

        // Limpar formulário
        document.getElementById('interactionMessage').value = '';

        // Fechar modal e atualizar lista de interações
        interactionModalInstance.hide();
        await loadInteractionsForOrder(orderId);

        showToast('Interação adicionada com sucesso!', 'success');
    } catch (error) {
        console.error('Erro ao adicionar interação:', error);
        showToast(`Erro ao adicionar interação: ${error.message}`, 'error');
    }
}

async function handleAssignProviderSubmit(event) {
    event.preventDefault();

    const orderId = document.getElementById('assignOrderId').value;
    const providerId = document.getElementById('providerSelect').value;

    if (!providerId) {
        showToast('Selecione um prestador.', 'warning');
        return;
    }

    try {
        const response = await fetch(`/api/orders/${orderId}/assign`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                provider_id: parseInt(providerId)
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
        }

        // Fechar modal e recarregar página
        assignProviderModalInstance.hide();
        showToast('Prestador atribuído com sucesso!', 'success');

        setTimeout(() => {
            window.location.reload();
        }, 1500);
    } catch (error) {
        console.error('Erro ao atribuir prestador:', error);
        showToast(`Erro ao atribuir prestador: ${error.message}`, 'error');
    }
}

// Funções de Manipulação de Modais
function openCostsModal(orderId) {
    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de abrir modal de custos para a ordem #18 que é inválida/hardcoded');
        showToast('Ordem #18 não está disponível. Por favor, selecione outra ordem.', 'error');
        return;
    }

    const costsModalElement = document.getElementById('costsModal');
    if (!costsModalElement) {
        showToast('Modal de custos não encontrado. Entre em contato com o suporte.', 'error');
        return;
    }

    document.getElementById('costOrderId').value = orderId;
    document.getElementById('costModalOrderId').textContent = `#${orderId}`;
    document.getElementById('costDescription').value = '';
    document.getElementById('costQuantity').value = '1';
    document.getElementById('costUnitPrice').value = '';

    costsModalInstance.show();
}

function openInteractionModal(orderId) {
    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de abrir modal de interações para a ordem #18 que é inválida/hardcoded');
        showToast('Ordem #18 não está disponível. Por favor, selecione outra ordem.', 'error');
        return;
    }

    const interactionModalElement = document.getElementById('interactionModal');
    if (!interactionModalElement) {
        showToast('Modal de interações não encontrado. Entre em contato com o suporte.', 'error');
        return;
    }

    document.getElementById('interactionOrderId').value = orderId;
    document.getElementById('interactionModalOrderId').textContent = `#${orderId}`;
    document.getElementById('interactionMessage').value = '';

    interactionModalInstance.show();
}

function openAssignProviderModal(orderId) {
    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de abrir modal de atribuição para a ordem #18 que é inválida/hardcoded');
        showToast('Ordem #18 não está disponível. Por favor, selecione outra ordem.', 'error');
        return;
    }

    const assignProviderModalElement = document.getElementById('assignProviderModal');
    if (!assignProviderModalElement) {
        showToast('Modal de atribuição não encontrado. Entre em contato com o suporte.', 'error');
        return;
    }

    document.getElementById('assignOrderId').value = orderId;
    document.getElementById('assignModalOrderId').textContent = `#${orderId}`;

    // Carregar lista de prestadores
    loadProviders();

    assignProviderModalInstance.show();
}

function openInvoiceModal(orderId) {
    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de abrir modal de nota fiscal para a ordem #18 que é inválida/hardcoded');
        showToast('Ordem #18 não está disponível. Por favor, selecione outra ordem.', 'error');
        return;
    }

    const invoiceModalElement = document.getElementById('invoiceModal');
    if (!invoiceModalElement) {
        showToast('Modal de nota fiscal não encontrado. Entre em contato com o suporte.', 'error');
        return;
    }

    document.getElementById('invoiceOrderId').value = orderId;
    document.getElementById('invoiceModalOrderId').textContent = `#${orderId}`;
    document.getElementById('invoiceNumber').value = '';
    document.getElementById('invoiceAmount').value = '';
    document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];

    const invoiceModalInstance = new bootstrap.Modal(invoiceModalElement);
    invoiceModalInstance.show();
}

// Funções de API
async function loadCostsForOrder(orderId) {
    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de carregar custos da ordem #18 que é inválida/hardcoded');
        showErrorInCostsDisplay(new Error('Ordem #18 não está disponível. Por favor, selecione outra ordem.'));
        return;
    }

    try {
        const response = await fetch(`/api/orders/${orderId}/costs`);
        if (!response.ok) throw new Error(`Erro HTTP ${response.status}`);

        const costs = await response.json();
        updateCostsDisplay(costs);
    } catch (error) {
        console.error("Erro ao carregar custos:", error);
        showErrorInCostsDisplay(error);
    }
}

async function loadInteractionsForOrder(orderId) {
    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de carregar interações da ordem #18 que é inválida/hardcoded');
        showErrorInInteractionsDisplay(new Error('Ordem #18 não está disponível. Por favor, selecione outra ordem.'));
        return;
    }

    try {
        const response = await fetch(`/api/orders/${orderId}/interactions`);
        if (!response.ok) throw new Error(`Erro HTTP ${response.status}`);

        const interactions = await response.json();
        updateInteractionsDisplay(interactions);
    } catch (error) {
        console.error("Erro ao carregar interações:", error);
        showErrorInInteractionsDisplay(error);
    }
}

async function loadProviders() {
    try {
        const response = await fetch('/api/providers');
        if (!response.ok) throw new Error(`Erro HTTP ${response.status}`);

        const providers = await response.json();
        updateProviderSelect(providers);
    } catch (error) {
        console.error("Erro ao carregar prestadores:", error);
        showErrorInProviderSelect(error);
    }
}

async function updateOrderStatus(orderId, newStatus) {
    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de atualizar status da ordem #18 que é inválida/hardcoded');
        showToast('Ordem #18 não está disponível. Por favor, selecione outra ordem.', 'error');
        return;
    }

    if (!confirm(`Tem certeza que deseja alterar o status para ${formatStatusJS(newStatus)}?`)) {
        return;
    }

    try {
        const response = await fetch(`/api/orders/${orderId}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: newStatus,
                reason: ''
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
        }

        showToast(`Status atualizado para ${formatStatusJS(newStatus)}!`, 'success');
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    } catch (error) {
        console.error('Erro ao atualizar status:', error);
        showToast(`Erro ao atualizar status: ${error.message}`, 'error');
    }
}

async function confirmCancelOrder(orderId) {
    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de cancelar a ordem #18 que é inválida/hardcoded');
        showToast('Ordem #18 não está disponível. Por favor, selecione outra ordem.', 'error');
        return;
    }

    const reason = prompt('Por favor, informe o motivo do cancelamento:');
    if (reason === null) return; // Usuário cancelou o prompt

    if (!reason.trim()) {
        showToast('O motivo do cancelamento é obrigatório.', 'warning');
        return;
    }

    try {
        const response = await fetch(`/api/orders/${orderId}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: 'cancelled',
                reason: reason
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
        }

        showToast('Ordem cancelada com sucesso!', 'success');
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    } catch (error) {
        console.error('Erro ao cancelar ordem:', error);
        showToast(`Erro ao cancelar ordem: ${error.message}`, 'error');
    }
}

async function markAsPaid(orderId) {
    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de marcar como paga a ordem #18 que é inválida/hardcoded');
        showToast('Ordem #18 não está disponível. Por favor, selecione outra ordem.', 'error');
        return;
    }

    if (!confirm('Tem certeza que deseja marcar esta ordem como paga?')) {
        return;
    }

    try {
        const response = await fetch(`/api/orders/${orderId}/mark-paid`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
        }

        showToast('Ordem marcada como paga com sucesso!', 'success');
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    } catch (error) {
        console.error('Erro ao marcar como pago:', error);
        showToast(`Erro ao marcar como pago: ${error.message}`, 'error');
    }
}

// Funções de Atualização de Interface
function updateCostsDisplay(costs) {
    const container = document.getElementById('costsContainer');
    if (!container) return;

    if (!costs || costs.length === 0) {
        container.innerHTML = '<p class="text-center text-muted">Nenhum custo registrado.</p>';
        return;
    }

    let html = '';
    let total = 0;

    costs.forEach(cost => {
        total += parseFloat(cost.total_price || 0);
        html += `
            <div class="cost-item">
                <div class="cost-item-header">
                    <div class="cost-item-title">${cost.description}</div>
                    <div class="cost-item-price">${formatCurrencyJS(cost.total_price)}</div>
                </div>
                <div class="cost-item-details">
                    <div>${cost.quantity} x ${formatCurrencyJS(cost.unit_price)}</div>
                    <div>${formatDateTimeJS(cost.created_at)}</div>
                </div>
            </div>
        `;
    });

    html += `
        <div class="cost-total">
            Total: ${formatCurrencyJS(total)}
        </div>
    `;

    container.innerHTML = html;
}

function updateInteractionsDisplay(interactions) {
    const container = document.getElementById('interactionsContainer');
    if (!container) return;

    if (!interactions || interactions.length === 0) {
        container.innerHTML = '<p class="text-center text-muted">Nenhuma interação registrada.</p>';
        return;
    }

    let html = '';

    interactions.forEach(interaction => {
        html += `
            <div class="interaction-item">
                <div class="interaction-message">${interaction.message}</div>
                <div class="interaction-meta">
                    <i class="fas fa-user"></i> ${interaction.user_name} - ${formatDateTimeJS(interaction.created_at)}
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

function updateProviderSelect(providers) {
    const providerSelect = document.getElementById('providerSelect');
    if (!providerSelect) return;

    if (!providers || providers.length === 0) {
        providerSelect.innerHTML = '<option value="" selected disabled>Nenhum prestador disponível</option>';
        providerSelect.disabled = true;
        return;
    }

    providerSelect.innerHTML = '<option value="" selected disabled>Selecione um prestador</option>';
    providers.forEach(provider => {
        const option = document.createElement('option');
        option.value = provider.id;
        option.textContent = provider.name;
        providerSelect.appendChild(option);
    });

    providerSelect.disabled = false;
}

// Funções de Exibição de Erros
function showErrorInCostsDisplay(error) {
    const container = document.getElementById('costsContainer');
    if (!container) return;

    container.innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Erro ao carregar custos: ${error.message}
        </div>
    `;
}

function showErrorInInteractionsDisplay(error) {
    const container = document.getElementById('interactionsContainer');
    if (!container) return;

    container.innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Erro ao carregar interações: ${error.message}
        </div>
    `;
}

function showErrorInProviderSelect(error) {
    const providerSelect = document.getElementById('providerSelect');
    if (!providerSelect) return;

    providerSelect.innerHTML = '<option value="" selected disabled>Erro ao carregar prestadores</option>';
    providerSelect.disabled = true;
    showToast(`Erro ao carregar prestadores: ${error.message}`, 'error');
}

// Funções Utilitárias
function formatCurrencyJS(value) {
    const numValue = Number(value);
    if (isNaN(numValue)) return "R$ 0,00";
    return numValue.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
}

function formatDateTimeJS(dateString) {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "Data inválida";

    return date.toLocaleString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function formatStatusJS(status) {
    const statusMap = {
        'pending': 'Pendente',
        'in_progress': 'Em Andamento',
        'pending_approval': 'Pendente Aprovação',
        'revision_needed': 'Revisão Necessária',
        'approved': 'Aprovada',
        'invoiced': 'Faturada',
        'paid': 'Paga',
        'cancelled': 'Cancelada'
    };

    return statusMap[status] || status;
}

// Função para exibir toasts
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toastPlacement');
    if (!toastContainer) return;

    const toastId = 'toast-' + Date.now();
    const bgClass = type === 'success' ? 'bg-success' :
                   type === 'error' ? 'bg-danger' :
                   type === 'warning' ? 'bg-warning' : 'bg-info';

    const toastHtml = `
        <div id="${toastId}" class="toast ${bgClass} text-white" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header ${bgClass} text-white">
                <strong class="me-auto">${type === 'success' ? 'Sucesso' :
                                         type === 'error' ? 'Erro' :
                                         type === 'warning' ? 'Aviso' : 'Informação'}</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Fechar"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
    toast.show();

    // Remover o toast após ser ocultado
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

// Função para imprimir a ordem
function printOrder() {
    window.print();
}

// Fim do arquivo