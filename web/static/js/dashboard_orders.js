
/**
 * Gerenciamento de Ordens de Serviço no Dashboard
 * Controla a exibição, filtragem e interação com ordens de serviço
 */
document.addEventListener('DOMContentLoaded', function() {
    // Estado do módulo
    const state = {
        orders: [],
        filteredOrders: [],
        filters: {
            status: 'all',
            priority: 'all'
        }
    };

    // Inicializa o módulo
    function init() {
        loadOrders();
        setupEventListeners();
    }

    // Carrega as ordens de serviço
    function loadOrders() {
        // Buscar ordens do servidor via API
        fetch('/api/ordens?exclude_ids=18')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    // Filtrar a ordem #18 dos resultados
                    state.orders = data.data.filter(order => order.id !== 18 && order.id !== "18");
                    state.filteredOrders = [...state.orders];
                    renderOrders();
                } else {
                    console.error('Erro ao carregar ordens:', data.message || 'Erro desconhecido');
                    // Exibir mensagem de erro na interface
                    const ordersContainer = document.getElementById('orders-container');
                    if (ordersContainer) {
                        ordersContainer.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Erro ao carregar ordens: ${data.message || 'Erro desconhecido'}
                            </div>
                        `;
                    }
                }
            })
            .catch(error => {
                console.error('Erro ao carregar ordens:', error);
                // Exibir mensagem de erro na interface
                const ordersContainer = document.getElementById('orders-container');
                if (ordersContainer) {
                    ordersContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Erro ao carregar ordens: ${error.message}
                        </div>
                    `;
                }
            });
    }

    // Configura os listeners de eventos
    function setupEventListeners() {
        // Filtros
        const statusFilter = document.getElementById('filter-status');
        const priorityFilter = document.getElementById('filter-priority');

        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                state.filters.status = statusFilter.value;
                applyFilters();
            });
        }

        if (priorityFilter) {
            priorityFilter.addEventListener('change', () => {
                state.filters.priority = priorityFilter.value;
                applyFilters();
            });
        }
    }

    // Aplica os filtros às ordens
    function applyFilters() {
        state.filteredOrders = state.orders.filter(order => {
            // Filtra por status
            if (state.filters.status !== 'all' && order.status !== state.filters.status) {
                return false;
            }

            // Filtra por prioridade
            if (state.filters.priority !== 'all' && order.priority !== state.filters.priority) {
                return false;
            }

            return true;
        });

        renderOrders();
    }

    // Renderiza as ordens na interface
    function renderOrders() {
        const ordersContainer = document.getElementById('orders-container');
        if (!ordersContainer) return;

        // Limpa o container
        ordersContainer.innerHTML = '';

        // Status e prioridades para exibição
        const statusLabels = {
            'pending': { text: 'Pendente', class: 'bg-danger' },
            'in-progress': { text: 'Em Andamento', class: 'bg-warning text-dark' },
            'scheduled': { text: 'Agendada', class: 'bg-primary' },
            'completed': { text: 'Concluída', class: 'bg-success' }
        };

        const priorityLabels = {
            'high': { text: 'Alta', class: 'bg-danger' },
            'medium': { text: 'Média', class: 'bg-warning text-dark' },
            'low': { text: 'Baixa', class: 'bg-success' }
        };

        // Adiciona cada ordem filtrada
        state.filteredOrders.forEach(order => {
            const statusInfo = statusLabels[order.status];
            const priorityInfo = priorityLabels[order.priority];

            const orderCard = document.createElement('div');
            orderCard.className = `order-card ${order.priority}-priority`;
            orderCard.setAttribute('draggable', 'true');
            orderCard.setAttribute('data-order-id', order.id);

            orderCard.innerHTML = `
                <div class="order-header">
                    <div>
                        <div class="order-title">${order.title}</div>
                        <div class="order-date"><i class="far fa-calendar-alt"></i> ${order.date}</div>
                    </div>
                    <span class="badge bg-light text-dark">${order.id}</span>
                </div>
                <div class="order-station"><i class="fas fa-gas-pump"></i> ${order.station}</div>
                <div>
                    <span class="order-status ${statusInfo.class}">${statusInfo.text}</span>
                    <span class="order-priority ${priorityInfo.class}">${priorityInfo.text}</span>
                </div>
                <div class="order-technician"><i class="fas fa-user-cog"></i> ${order.technician}</div>
                <div class="order-nextservice"><i class="fas fa-calendar-check"></i> Próxima preventiva: ${order.nextService}</div>
                <div class="order-actions">
                    <button class="order-action-btn view-btn"><i class="fas fa-eye"></i> Visualizar</button>
                    <button class="order-action-btn edit-btn"><i class="fas fa-edit"></i> Editar</button>
                </div>
            `;

            // Adiciona eventos
            setupOrderCardEvents(orderCard, order);

            ordersContainer.appendChild(orderCard);
        });
    }

    // Configura eventos para um card de ordem
    function setupOrderCardEvents(orderCard, order) {
        // Botão visualizar
        const viewBtn = orderCard.querySelector('.view-btn');
        if (viewBtn) {
            viewBtn.addEventListener('click', () => {
                showOrderDetails(order);
            });
        }

        // Botão editar
        const editBtn = orderCard.querySelector('.edit-btn');
        if (editBtn) {
            editBtn.addEventListener('click', () => {
                editOrder(order);
            });
        }

        // Eventos de arrastar e soltar
        orderCard.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', order.id);
            orderCard.classList.add('dragging');
        });

        orderCard.addEventListener('dragend', () => {
            orderCard.classList.remove('dragging');
        });
    }

    // Exibe os detalhes de uma ordem
    function showOrderDetails(order) {
        // Verificar se o ID é 18 e bloqueá-lo
        if (order.id === 18 || order.id === "18") {
            console.error('Tentativa de mostrar detalhes da ordem #18 que é inválida/hardcoded');
            alert('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
            return;
        }

        const orderDetailContent = document.getElementById('order-detail-content');
        if (!orderDetailContent) return;

        // Mostrar indicador de carregamento
        orderDetailContent.innerHTML = '<div class="text-center p-4"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">Carregando detalhes da ordem...</p></div>';

        // Buscar detalhes completos da ordem via API
        fetch(`/api/ordens/${order.id}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    // Processar dados para o formato esperado
                    const orderDetail = {
                        ...order,
                        description: data.data.description || data.data.problema || order.description || 'Sem descrição',
                        budget: data.data.budget || data.data.orcamento || 'Não especificado',
                        attended: data.data.status === 'completed' || data.data.status === 'concluido' || data.data.status === 'concluída' ? 'Sim' : 'Não',
                        interactions: data.data.interactions || data.data.interacoes || []
                    };

                    // Renderizar os detalhes
                    renderOrderDetails(orderDetail);
                } else {
                    console.error('Erro ao carregar detalhes da ordem:', data.message || 'Erro desconhecido');
                    orderDetailContent.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Erro ao carregar detalhes da ordem: ${data.message || 'Erro desconhecido'}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Erro ao carregar detalhes da ordem:', error);
                orderDetailContent.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Erro ao carregar detalhes da ordem: ${error.message}
                    </div>
                `;
            });
    }

    // Renderiza os detalhes da ordem
    function renderOrderDetails(orderDetail) {
        const orderDetailContent = document.getElementById('order-detail-content');
        if (!orderDetailContent) return;

        // Status para exibição
        const statusClasses = {
            'pending': 'bg-danger',
            'in-progress': 'bg-warning text-dark',
            'scheduled': 'bg-primary',
            'completed': 'bg-success'
        };

        const priorityClasses = {
            'high': 'bg-danger',
            'medium': 'bg-warning text-dark',
            'low': 'bg-success'
        };

        // Tradução dos status
        const statusTranslation = {
            'pending': 'Pendente',
            'in-progress': 'Em Andamento',
            'scheduled': 'Agendada',
            'completed': 'Concluída'
        };

        const priorityTranslation = {
            'high': 'Alta',
            'medium': 'Média',
            'low': 'Baixa'
        };

        const statusClass = statusClasses[orderDetail.status] || 'bg-secondary';
        const priorityClass = priorityClasses[orderDetail.priority] || 'bg-secondary';

        // Constrói o HTML
        let html = `
            <div class="order-detail">
                <div class="order-detail-header">
                    <h5>${orderDetail.title}</h5>
                    <span class="badge bg-light text-dark">${orderDetail.id}</span>
                </div>

                <div class="order-detail-info">
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <p><strong>Data:</strong> ${orderDetail.date}</p>
                            <p><strong>Posto:</strong> ${orderDetail.station}</p>
                            <p><strong>Status:</strong> <span class="badge ${statusClass}">${statusTranslation[orderDetail.status]}</span></p>
                            <p><strong>Prioridade:</strong> <span class="badge ${priorityClass}">${priorityTranslation[orderDetail.priority]}</span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Técnico:</strong> ${orderDetail.technician}</p>
                            <p><strong>Atendida:</strong> ${orderDetail.attended}</p>
                            <p><strong>Orçamento:</strong> ${orderDetail.budget}</p>
                            <p><strong>Próxima Preventiva:</strong> ${orderDetail.nextService}</p>
                        </div>
                    </div>

                    <div class="order-description mt-3">
                        <p><strong>Descrição:</strong></p>
                        <p>${orderDetail.description}</p>
                    </div>

                    <div class="order-interactions mt-4">
                        <h6>Interações</h6>
                        <div class="interactions-list">
        `;

        orderDetail.interactions.forEach(interaction => {
            html += `
                <div class="interaction-item">
                    <div class="interaction-header">
                        <strong>${interaction.user}</strong>
                        <span>${interaction.date}</span>
                    </div>
                    <div class="interaction-message">
                        ${interaction.message}
                    </div>
                </div>
            `;
        });

        html += `
                        </div>
                    </div>

                    <div class="order-actions mt-4">
                        <textarea class="form-control mb-2" placeholder="Digite uma observação..."></textarea>
                        <div class="d-flex justify-content-between">
                            <button class="btn btn-sm btn-outline-light">Adicionar Foto</button>
                            <button class="btn btn-sm btn-primary">Enviar</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        orderDetailContent.innerHTML = html;

        // Rola até a área de detalhes
        document.getElementById('order-detail-container').scrollIntoView({ behavior: 'smooth' });
    }

    // Editar uma ordem
    function editOrder(order) {
        // Verificar se o ID é 18 e bloqueá-lo
        if (order.id === 18 || order.id === "18") {
            console.error('Tentativa de editar a ordem #18 que é inválida/hardcoded');
            alert('Ordem #18 não está disponível para edição. Por favor, selecione outra ordem.');
            return;
        }

        // Em produção redirecionaria para formulário de edição
        console.log('Editando ordem:', order.id);
        alert(`Em uma implementação completa, aqui abriria o formulário de edição para a ordem ${order.id}`);
    }

    // Inicializa o módulo
    init();
});
/**
 * Dashboard Orders - JavaScript para gerenciar ordens de serviço
 */
document.addEventListener('DOMContentLoaded', function() {
    // Referências aos elementos DOM
    const orderGrid = document.getElementById('serviceOrdersGrid');
    const statusFilter = document.getElementById('orderStatusFilter');
    const priorityFilter = document.getElementById('orderPriorityFilter');
    const dateFilter = document.getElementById('orderDateFilter');
    const orderDetailContainer = document.getElementById('orderDetailContainer');
    const orderDetailContent = document.getElementById('orderDetailContent');

    // Array para armazenar ordens de serviço carregadas da API
    let serviceOrders = [];

    // Função para obter a classe CSS com base na prioridade
    function getPriorityClass(priority) {
        switch(priority.toLowerCase()) {
            case 'urgente': return 'bg-danger';
            case 'alta': return 'bg-warning text-dark';
            case 'média': return 'bg-info text-dark';
            case 'normal': return 'bg-primary';
            default: return 'bg-secondary';
        }
    }

    // Função para obter a classe CSS com base no status
    function getStatusClass(status) {
        switch(status.toLowerCase()) {
            case 'pendente': return 'bg-warning text-dark';
            case 'em andamento': return 'bg-info text-dark';
            case 'concluída': return 'bg-success';
            case 'agendada': return 'bg-primary';
            case 'cancelada': return 'bg-danger';
            default: return 'bg-secondary';
        }
    }

    // Função para renderizar os cards de ordens de serviço
    function renderOrderCards(orders) {
        if (!orderGrid) return;

        orderGrid.innerHTML = '';

        // Filtrar a ordem #18
        const filteredOrders = orders.filter(order => order.id !== 18 && order.id !== "18");

        if (filteredOrders.length === 0) {
            orderGrid.innerHTML = '<div class="alert alert-info">Nenhuma ordem de serviço encontrada com os filtros atuais.</div>';
            return;
        }

        filteredOrders.forEach(order => {
            const priorityClass = getPriorityClass(order.priority);
            const statusClass = getStatusClass(order.status);

            const card = document.createElement('div');
            card.className = 'service-order-card';
            card.setAttribute('draggable', 'true');
            card.setAttribute('data-order-id', order.id);

            card.innerHTML = `
                <div class="d-flex justify-content-between align-items-start">
                    <h5>${order.title}</h5>
                    <span class="badge ${priorityClass}">${order.priority}</span>
                </div>
                <p class="mb-1"><small>ID: ${order.id}</small></p>
                <p class="mb-1"><small>${order.date}</small></p>
                <div class="order-details">
                    <p class="mb-1">Posto: ${order.station}</p>
                    <p class="mb-1">Status: <span class="badge ${statusClass}">${order.status}</span></p>
                    <p class="mb-1">Próxima preventiva: ${order.preventiveDate}</p>
                    <p class="mb-0">${order.description}</p>
                </div>
                <div class="order-actions">
                    <button class="btn btn-sm btn-outline-primary view-order-btn">Visualizar</button>
                    <button class="btn btn-sm btn-outline-warning edit-order-btn">Editar</button>
                </div>
            `;

            // Adicionar eventos
            card.addEventListener('dragstart', handleDragStart);

            // Adicionar evento para o botão de visualizar
            card.querySelector('.view-order-btn').addEventListener('click', function() {
                // Verificar se o ID é 18 e bloqueá-lo
                if (order.id === 18 || order.id === "18") {
                    console.error('Tentativa de visualizar a ordem #18 que é inválida/hardcoded');
                    alert('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
                    return;
                }
                showOrderDetails(order);
            });

            // Adicionar evento para o botão de editar
            card.querySelector('.edit-order-btn').addEventListener('click', function() {
                // Verificar se o ID é 18 e bloqueá-lo
                if (order.id === 18 || order.id === "18") {
                    console.error('Tentativa de editar a ordem #18 que é inválida/hardcoded');
                    alert('Ordem #18 não está disponível para edição. Por favor, selecione outra ordem.');
                    return;
                }
                editOrder(order);
            });

            orderGrid.appendChild(card);
        });
    }

    // Função para aplicar filtros às ordens de serviço
    function applyFilters() {
        const statusValue = statusFilter ? statusFilter.value : 'all';
        const priorityValue = priorityFilter ? priorityFilter.value : 'all';
        const dateValue = dateFilter ? dateFilter.value : 'all';

        let filteredOrders = [...serviceOrders];

        // Filtrar por status
        if (statusValue !== 'all') {
            filteredOrders = filteredOrders.filter(order =>
                order.status.toLowerCase() === statusValue.toLowerCase()
            );
        }

        // Filtrar por prioridade
        if (priorityValue !== 'all') {
            filteredOrders = filteredOrders.filter(order =>
                order.priority.toLowerCase() === priorityValue.toLowerCase()
            );
        }

        // Filtrar por data
        if (dateValue !== 'all') {
            const today = new Date();
            const oneDay = 24 * 60 * 60 * 1000;

            if (dateValue === 'today') {
                filteredOrders = filteredOrders.filter(order => {
                    const orderDate = order.createdAt;
                    return orderDate.toDateString() === today.toDateString();
                });
            } else if (dateValue === 'this_week') {
                const startOfWeek = new Date(today);
                startOfWeek.setDate(today.getDate() - today.getDay());

                filteredOrders = filteredOrders.filter(order => {
                    const orderDate = order.createdAt;
                    return orderDate >= startOfWeek;
                });
            } else if (dateValue === 'this_month') {
                const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

                filteredOrders = filteredOrders.filter(order => {
                    const orderDate = order.createdAt;
                    return orderDate >= startOfMonth;
                });
            }
        }

        renderOrderCards(filteredOrders);
    }

    // Função para iniciar o arrastar de um card
    function handleDragStart(e) {
        e.dataTransfer.setData('text/plain', e.currentTarget.getAttribute('data-order-id'));
        e.dataTransfer.effectAllowed = 'move';
    }

    // Função para mostrar detalhes de uma ordem
    function showOrderDetails(order) {
        if (!orderDetailContainer || !orderDetailContent) return;

        // Criar HTML para interações
        let interactionsHTML = '';
        if (order.interactions && order.interactions.length > 0) {
            order.interactions.forEach(interaction => {
                interactionsHTML += `
                    <div class="interaction-message">
                        <strong>${interaction.user} (${interaction.datetime}):</strong> ${interaction.message}
                    </div>
                `;
            });
        } else {
            interactionsHTML = '<p class="text-muted">Nenhuma interação registrada.</p>';
        }

        // Criar o HTML para os detalhes da ordem
        const detailHTML = `
            <div class="order-detail-header">
                <h4>${order.title}</h4>
                <span class="badge ${getPriorityClass(order.priority)}">${order.priority}</span>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="order-detail-section">
                        <h5>Informações da Ordem</h5>
                        <p><strong>ID:</strong> ${order.id}</p>
                        <p><strong>Data:</strong> ${order.date}</p>
                        <p><strong>Status:</strong> <span class="badge ${getStatusClass(order.status)}">${order.status}</span></p>
                        <p><strong>Posto:</strong> ${order.station}</p>
                        <p><strong>Descrição:</strong> ${order.description}</p>
                        <p><strong>Próxima Preventiva:</strong> ${order.preventiveDate}</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="order-detail-section">
                        <h5>Informações Técnicas</h5>
                        <p><strong>Técnico:</strong> ${order.technician || 'A definir'}</p>
                        <p><strong>Tempo Estimado:</strong> ${order.estimatedTime || 'A definir'}</p>
                        <p><strong>Peças Necessárias:</strong> ${order.partsNeeded || 'A definir'}</p>
                        <p><strong>Orçamento:</strong> ${order.budget || 'A definir'}</p>
                    </div>
                </div>
            </div>

            <div class="interaction-section">
                <h5>Interações</h5>
                <div class="interaction-messages">
                    ${interactionsHTML}
                </div>
                <div class="interaction-form">
                    <div class="form-group">
                        <label for="newMessage">Nova Mensagem:</label>
                        <textarea class="form-control" id="newMessage" rows="2"></textarea>
                    </div>
                    <button class="btn btn-primary mt-2" id="sendMessageBtn">Enviar</button>
                </div>
            </div>

            <div class="text-center mt-4">
                <button class="btn btn-danger me-2" id="rejectOrderBtn">Reprovar</button>
                <button class="btn btn-success" id="approveOrderBtn">Aprovar</button>
            </div>
        `;

        // Mostrar o container e preencher o conteúdo
        orderDetailContainer.style.display = 'block';
        orderDetailContent.innerHTML = detailHTML;

        // Adicionar eventos aos botões
        document.getElementById('sendMessageBtn').addEventListener('click', function() {
            sendMessage(order.id);
        });

        document.getElementById('approveOrderBtn').addEventListener('click', function() {
            approveOrder(order);
        });

        document.getElementById('rejectOrderBtn').addEventListener('click', function() {
            rejectOrder(order);
        });

        // Rolar até o container de detalhes
        orderDetailContainer.scrollIntoView({ behavior: 'smooth' });
    }

    // Função para enviar uma nova mensagem
    function sendMessage(orderId) {
        // Verificar se o ID é 18 e bloqueá-lo
        if (orderId === 18 || orderId === "18") {
            console.error('Tentativa de enviar mensagem para a ordem #18 que é inválida/hardcoded');
            alert('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
            return;
        }

        const messageInput = document.getElementById('newMessage');
        const message = messageInput.value.trim();

        if (!message) {
            alert('Por favor, digite uma mensagem.');
            return;
        }

        // Simular envio para a API
        console.log(`Mensagem para a ordem ${orderId}: ${message}`);

        // Adicionar mensagem à interface
        const now = new Date();
        const timeString = `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;

        const messageContainer = document.querySelector('.interaction-messages');
        if (messageContainer.querySelector('.text-muted')) {
            messageContainer.innerHTML = '';
        }

        const messageElement = document.createElement('div');
        messageElement.className = 'interaction-message';
        messageElement.innerHTML = `<strong>Usuário (${timeString}):</strong> ${message}`;

        messageContainer.appendChild(messageElement);
        messageInput.value = '';
    }

    // Função para aprovar uma ordem
    function approveOrder(order) {
        // Verificar se o ID é 18 e bloqueá-lo
        if (order.id === 18 || order.id === "18") {
            console.error('Tentativa de aprovar a ordem #18 que é inválida/hardcoded');
            alert('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
            return;
        }

        // Verificar se a ordem tem todos os dados necessários
        const hasAllData = order.title && order.date && order.station &&
                          order.description && order.technician &&
                          order.budget && order.interactions.length > 0;

        if (hasAllData) {
            if (confirm(`Confirma a APROVAÇÃO da ordem ${order.id}?`)) {
                // Simular chamada à API
                console.log(`Ordem ${order.id} APROVADA`);
                alert(`Ordem ${order.id} foi APROVADA com sucesso!`);

                // Atualizar status na interface
                const updatedOrders = serviceOrders.map(o => {
                    if (o.id === order.id) {
                        return { ...o, status: 'Aprovada' };
                    }
                    return o;
                });

                // Recarregar cards com a ordem atualizada
                renderOrderCards(updatedOrders);

                // Fechar detalhes
                orderDetailContainer.style.display = 'none';
            }
        } else {
            alert(`Não é possível aprovar a ordem ${order.id}. Dados incompletos ou falta de interação.`);
        }
    }

    // Função para rejeitar uma ordem
    function rejectOrder(order) {
        // Verificar se o ID é 18 e bloqueá-lo
        if (order.id === 18 || order.id === "18") {
            console.error('Tentativa de rejeitar a ordem #18 que é inválida/hardcoded');
            alert('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
            return;
        }

        const reason = prompt(`Informe o motivo da REPROVAÇÃO da ordem ${order.id}:`);

        if (reason === null) {
            return; // Usuário cancelou
        }

        if (reason.trim() === '') {
            alert('É necessário informar um motivo para a reprovação.');
            return;
        }

        // Simular chamada à API
        console.log(`Ordem ${order.id} REPROVADA. Motivo: ${reason}`);
        alert(`Ordem ${order.id} foi REPROVADA.\nMotivo: ${reason}`);

        // Atualizar status na interface
        const updatedOrders = serviceOrders.map(o => {
            if (o.id === order.id) {
                const updatedOrder = { ...o, status: 'Reprovada' };
                updatedOrder.interactions.push({
                    user: 'Sistema',
                    datetime: new Date().toLocaleDateString('pt-BR'),
                    message: `Ordem reprovada. Motivo: ${reason}`
                });
                return updatedOrder;
            }
            return o;
        });

        // Recarregar cards com a ordem atualizada
        renderOrderCards(updatedOrders);

        // Fechar detalhes
        orderDetailContainer.style.display = 'none';
    }

    // Função para editar uma ordem
    function editOrder(order) {
        // Verificar se o ID é 18 e bloqueá-lo
        if (order.id === 18 || order.id === "18") {
            console.error('Tentativa de editar a ordem #18 que é inválida/hardcoded');
            alert('Ordem #18 não está disponível para edição. Por favor, selecione outra ordem.');
            return;
        }

        alert(`Funcionalidade de edição da ordem ${order.id} será implementada em breve.`);
    }

    // Adicionar listeners para os filtros
    if (statusFilter) {
        statusFilter.addEventListener('change', applyFilters);
    }

    if (priorityFilter) {
        priorityFilter.addEventListener('change', applyFilters);
    }

    if (dateFilter) {
        dateFilter.addEventListener('change', applyFilters);
    }

    // Função para carregar ordens de serviço da API
    function loadServiceOrders() {
        // Mostrar indicador de carregamento
        if (orderGrid) {
            orderGrid.innerHTML = '<div class="text-center p-4"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">Carregando ordens de serviço...</p></div>';
        }

        // Buscar ordens do servidor via API
        fetch('/api/ordens?exclude_ids=18')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    // Filtrar a ordem #18 dos resultados
                    const filteredData = data.data.filter(order => order.id !== 18 && order.id !== "18");

                    // Processar dados para o formato esperado
                    serviceOrders = filteredData.map(order => {
                        // Converter datas para objetos Date
                        const createdAt = new Date(order.created_at || order.data);

                        return {
                            id: order.id,
                            title: order.title || order.titulo || order.problem || 'Ordem de Serviço',
                            date: new Date(order.due_date || order.data).toLocaleDateString('pt-BR'),
                            createdAt: createdAt,
                            status: order.status_display || order.status || 'Pendente',
                            priority: order.priority_display || order.prioridade || 'Normal',
                            station: order.branch_name || order.filial || 'Não especificado',
                            description: order.description || order.problema || 'Sem descrição',
                            technician: order.technician_name || order.responsavel || 'Não atribuído',
                            estimatedTime: order.estimated_time || order.tempo_estimado || 'Não especificado',
                            partsNeeded: order.parts_needed || order.pecas || 'Não especificado',
                            budget: order.budget || order.orcamento || 'Não especificado',
                            interactions: order.interactions || order.interacoes || [],
                            preventiveDate: order.preventive_date || order.data_preventiva || 'Não agendada'
                        };
                    });

                    // Renderizar os cards
                    renderOrderCards(serviceOrders);
                } else {
                    console.error('Erro ao carregar ordens:', data.message || 'Erro desconhecido');
                    if (orderGrid) {
                        orderGrid.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Erro ao carregar ordens: ${data.message || 'Erro desconhecido'}
                            </div>
                        `;
                    }
                }
            })
            .catch(error => {
                console.error('Erro ao carregar ordens:', error);
                if (orderGrid) {
                    orderGrid.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Erro ao carregar ordens: ${error.message}
                        </div>
                    `;
                }
            });
    }

    // Inicializar carregando as ordens
    loadServiceOrders();
});
