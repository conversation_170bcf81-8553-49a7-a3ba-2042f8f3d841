/**
 * Mobile Sidebar Toggle - Sistema de Manutenção Shell
 * Adiciona um botão de toggle para o menu lateral em dispositivos móveis
 */
document.addEventListener('DOMContentLoaded', function() {
    // Verificar se é um dispositivo móvel (largura <= 768px)
    const isMobile = window.innerWidth <= 768;
    
    if (isMobile) {
        // Verificar se o botão já existe
        if (!document.getElementById('mobile-toggle')) {
            // Criar o botão de toggle mobile
            const mobileToggle = document.createElement('button');
            mobileToggle.id = 'mobile-toggle';
            mobileToggle.className = 'mobile-menu-toggle';
            mobileToggle.innerHTML = '<i class="fas fa-bars"></i>';
            mobileToggle.setAttribute('aria-label', 'Abrir menu');
            
            // Adicionar o botão ao body
            document.body.appendChild(mobileToggle);
            
            // Adicionar evento de clique
            mobileToggle.addEventListener('click', function() {
                const sidebar = document.querySelector('.tradicio-sidebar');
                if (sidebar) {
                    sidebar.classList.toggle('mobile-open');
                }
            });
            
            // Fechar sidebar ao clicar fora
            document.addEventListener('click', function(event) {
                const sidebar = document.querySelector('.tradicio-sidebar');
                if (sidebar && !sidebar.contains(event.target) && 
                    event.target !== mobileToggle && 
                    sidebar.classList.contains('mobile-open')) {
                    sidebar.classList.remove('mobile-open');
                }
            });
        }
    }
    
    // Adicionar listener para redimensionamento da janela
    window.addEventListener('resize', function() {
        const currentIsMobile = window.innerWidth <= 768;
        const mobileToggle = document.getElementById('mobile-toggle');
        
        // Adicionar ou remover o botão conforme o tamanho da tela
        if (currentIsMobile && !mobileToggle) {
            // Recarregar a página para adicionar o botão
            window.location.reload();
        } else if (!currentIsMobile && mobileToggle) {
            // Remover o botão em telas maiores
            mobileToggle.remove();
        }
    });
});
