/**
 * Dashboard Nova - Shell Tradição
 * Script para funcionalidades da nova dashboard
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard Nova inicializada');

    // Inicializa todas as funcionalidades
    initDateTime();
    initLEDMarquee();
    initRiddleCard();
    initModalLinks();
    initParticles();

    // Adiciona classes de animação aos elementos
    animateElements();
});

/**
 * Inicializa o relógio e data
 */
function initDateTime() {
    const dateElement = document.getElementById('current-date');
    const timeElement = document.getElementById('current-time');

    // Atualiza a data e hora
    function updateDateTime() {
        const now = new Date();

        // Formata a data: DD/MM/YYYY
        const day = String(now.getDate()).padStart(2, '0');
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const year = now.getFullYear();
        const dateString = `${day}/${month}/${year}`;

        // Formata a hora: HH:MM:SS
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');
        const timeString = `${hours}:${minutes}:${seconds}`;

        // Atualiza os elementos
        if (dateElement) dateElement.textContent = dateString;
        if (timeElement) timeElement.textContent = timeString;
    }

    // Atualiza imediatamente e depois a cada segundo
    updateDateTime();
    setInterval(updateDateTime, 1000);
}

/**
 * Inicializa o letreiro LED com mensagens
 */
function initLEDMarquee() {
    const marqueeElement = document.getElementById('led-marquee');
    if (!marqueeElement) return;

    // Array de mensagens para o letreiro
    const messages = [
        "Bem-vindo ao Sistema Shell Tradição - Excelência em Gestão de Postos! 🚀",
        "Dica: Use o menu lateral para navegar entre as seções do sistema. ⚡",
        "Você sabia? A Shell foi fundada em 1907 como Royal Dutch Shell! 🐚",
        "Segurança em primeiro lugar: Mantenha o motor desligado durante o abastecimento! 🔒",
        "Dica: Manter a calibragem correta dos pneus economiza combustível! 💡",
        "Juntos somos mais fortes! Equipe Rede Tradição Shell 💪",
        "Você sabia? O Brasil tem mais de 41 mil postos de combustível! 🇧🇷",
        "Qualidade e confiança: Nossa marca registrada desde sempre! ✨",
        "Dica: Faça a manutenção preventiva regularmente! 🔧",
        "Atendimento de excelência: Nossa prioridade é você! 🤝",
        "Curiosidade: A gasolina foi inventada por acidente em 1859! 📚",
        "Inovação e tradição: O combustível do nosso sucesso! 🚀",
        "Você sabia? O primeiro carro flex foi lançado no Brasil em 2003! 🚙",
        "Compromisso com o meio ambiente: Pensando no futuro! 🌱"
    ];

    // Embaralha o array de mensagens
    const shuffledMessages = shuffleArray([...messages]);

    // Cria o texto do letreiro
    marqueeElement.textContent = shuffledMessages.join(' • ');
}

/**
 * Inicializa o card de charada com efeito flip
 */
function initRiddleCard() {
    const riddleCard = document.querySelector('.riddle-card');
    const flipButton = document.getElementById('flip-riddle');
    const flipBackButton = document.getElementById('flip-back');

    if (!riddleCard || !flipButton || !flipBackButton) return;

    // Array de charadas relacionadas a postos, conveniência e veículos
    const riddles = [
        {
            question: "O que é, o que é: tem tanque, mas não é de guerra; tem bomba, mas não explode?",
            answer: "Um posto de gasolina!"
        },
        {
            question: "O que é, o que é: quanto mais caro, mais feliz fica o dono do posto?",
            answer: "O combustível!"
        },
        {
            question: "O que é, o que é: fica parado enquanto abastece?",
            answer: "O carro no posto de gasolina!"
        },
        {
            question: "O que é, o que é: tem motor mas não anda, tem bomba mas não explode, tem mangueira mas não molha plantas?",
            answer: "Uma bomba de combustível!"
        },
        {
            question: "O que é, o que é: quanto mais você abastece, mais vazio fica seu bolso?",
            answer: "O tanque do carro!"
        },
        {
            question: "O que é, o que é: anda deitado e trabalha em pé?",
            answer: "O combustível nos canos e mangueiras!"
        },
        {
            question: "O que é, o que é: quanto mais seco, mais molhado fica?",
            answer: "Uma toalha! Como as que usamos para limpar os vidros dos carros."
        },
        {
            question: "O que é, o que é: tem pneus mas não anda, tem volante mas não vira?",
            answer: "Um carro na oficina do posto!"
        },
        {
            question: "O que é, o que é: é posto, mas não é colocado?",
            answer: "Um posto de gasolina!"
        },
        {
            question: "O que é, o que é: tem escala mas não é música, tem números mas não é telefone?",
            answer: "A bomba de combustível!"
        }
    ];

    // Seleciona uma charada aleatória
    const randomRiddle = riddles[Math.floor(Math.random() * riddles.length)];

    // Atualiza o conteúdo da charada
    const questionElement = document.getElementById('riddle-question');
    const answerElement = document.getElementById('riddle-answer');

    if (questionElement) questionElement.textContent = randomRiddle.question;
    if (answerElement) answerElement.textContent = randomRiddle.answer;

    // Adiciona eventos para o efeito flip
    flipButton.addEventListener('click', function() {
        riddleCard.classList.add('flipped');
    });

    flipBackButton.addEventListener('click', function() {
        riddleCard.classList.remove('flipped');
    });
}

/**
 * Inicializa os links para os modais
 */
function initModalLinks() {
    // Modal do Manual
    const manualLink = document.getElementById('show-manual');
    const manualModal = new bootstrap.Modal(document.getElementById('manualModal'));

    if (manualLink) {
        manualLink.addEventListener('click', function(e) {
            e.preventDefault();
            manualModal.show();
        });
    }

    // Modal do FAQ
    const faqLink = document.getElementById('show-faq');
    const faqModal = new bootstrap.Modal(document.getElementById('faqModal'));

    if (faqLink) {
        faqLink.addEventListener('click', function(e) {
            e.preventDefault();
            faqModal.show();
        });
    }

    // Modal do Changelog
    const changelogLink = document.getElementById('show-changelog');
    const changelogModal = new bootstrap.Modal(document.getElementById('changelogModal'));

    if (changelogLink) {
        changelogLink.addEventListener('click', function(e) {
            e.preventDefault();
            changelogModal.show();
        });
    }
}

/**
 * Inicializa as partículas de fundo
 */
function initParticles() {
    if (typeof particlesJS !== 'undefined' && document.getElementById('particles-container')) {
        particlesJS('particles-container', {
            "particles": {
                "number": {
                    "value": 30,
                    "density": {
                        "enable": true,
                        "value_area": 800
                    }
                },
                "color": {
                    "value": "#fdb813"
                },
                "shape": {
                    "type": "circle",
                    "stroke": {
                        "width": 0,
                        "color": "#000000"
                    },
                    "polygon": {
                        "nb_sides": 5
                    }
                },
                "opacity": {
                    "value": 0.3,
                    "random": true,
                    "anim": {
                        "enable": true,
                        "speed": 1,
                        "opacity_min": 0.1,
                        "sync": false
                    }
                },
                "size": {
                    "value": 5,
                    "random": true,
                    "anim": {
                        "enable": true,
                        "speed": 2,
                        "size_min": 1,
                        "sync": false
                    }
                },
                "line_linked": {
                    "enable": false
                },
                "move": {
                    "enable": true,
                    "speed": 1,
                    "direction": "top",
                    "random": true,
                    "straight": false,
                    "out_mode": "out",
                    "bounce": false,
                    "attract": {
                        "enable": false,
                        "rotateX": 600,
                        "rotateY": 1200
                    }
                }
            },
            "interactivity": {
                "detect_on": "canvas",
                "events": {
                    "onhover": {
                        "enable": true,
                        "mode": "bubble"
                    },
                    "onclick": {
                        "enable": true,
                        "mode": "repulse"
                    },
                    "resize": true
                },
                "modes": {
                    "bubble": {
                        "distance": 100,
                        "size": 6,
                        "duration": 0.3,
                        "opacity": 0.8,
                        "speed": 3
                    },
                    "repulse": {
                        "distance": 200,
                        "duration": 0.4
                    }
                }
            },
            "retina_detect": true
        });
    } else {
        console.warn('particlesJS não está disponível ou o container não foi encontrado');
    }
}

/**
 * Adiciona classes de animação aos elementos
 */
function animateElements() {
    // Adiciona classes de animação com atraso para criar efeito cascata
    const animatedElements = document.querySelectorAll('.fade-in');
    animatedElements.forEach((element, index) => {
        setTimeout(() => {
            element.style.opacity = '1';
        }, 100 * (index + 1));
    });
}

/**
 * Embaralha um array
 * @param {Array} array - Array a ser embaralhado
 * @returns {Array} - Array embaralhado
 */
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}
