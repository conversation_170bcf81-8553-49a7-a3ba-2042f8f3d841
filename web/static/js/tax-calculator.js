import { IMPOSTOS_COMBUSTIVEIS, getImpostosCombustivel } from './impostos.js';

class TaxCalculator {
    constructor() {
        this.form = document.getElementById('taxCalculatorForm');
        this.stateSelect = document.getElementById('stateSelect');
        this.fuelTypeSelect = document.getElementById('fuelTypeSelect');
        this.baseValue = document.getElementById('baseValue');
        this.errorContainer = document.getElementById('errorContainer');
        this.resultsContainer = document.getElementById('resultsContainer');
        this.taxResultsBody = document.getElementById('taxResultsBody');
        this.totalTaxRate = document.getElementById('totalTaxRate');
        this.totalTaxValue = document.getElementById('totalTaxValue');

        this.initializeSelects();
        this.bindEvents();
    }

    initializeSelects() {
        // Preenche select de estados
        const estados = Object.keys(IMPOSTOS_COMBUSTIVEIS['Gasolina C (comum ou aditivada)']);
        estados.sort().forEach(estado => {
            const option = document.createElement('option');
            option.value = estado;
            option.textContent = estado;
            this.stateSelect.appendChild(option);
        });

        // Preenche select de combustíveis
        const combustiveis = Object.keys(IMPOSTOS_COMBUSTIVEIS);
        combustiveis.sort().forEach(combustivel => {
            const option = document.createElement('option');
            option.value = combustivel;
            option.textContent = combustivel;
            this.fuelTypeSelect.appendChild(option);
        });
    }

    bindEvents() {
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.calculateTaxes();
        });

        this.baseValue.addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/[^0-9.,]/g, '');
        });
    }

    calculateTaxes() {
        try {
            const state = this.stateSelect.value;
            const fuelType = this.fuelTypeSelect.value;
            const baseValue = parseFloat(this.baseValue.value.replace(',', '.'));

            if (!state || !fuelType || isNaN(baseValue)) {
                this.showError('Por favor, preencha todos os campos corretamente.');
                return;
            }

            const taxData = getImpostosCombustivel(fuelType, state);
            if (!taxData) {
                this.showError('Dados de impostos não encontrados para a combinação selecionada.');
                return;
            }

            this.displayResults(taxData, baseValue);
        } catch (error) {
            this.showError('Erro ao calcular impostos: ' + error.message);
        }
    }

    displayResults(taxData, baseValue) {
        this.errorContainer.classList.add('d-none');
        this.resultsContainer.classList.remove('d-none');
        this.taxResultsBody.innerHTML = '';

        let totalTaxRate = 0;
        let totalTaxValue = 0;

        Object.entries(taxData).forEach(([taxName, taxRate]) => {
            if (taxName !== 'total') {
                const taxValue = (baseValue * taxRate) / 100;
                totalTaxRate += taxRate;
                totalTaxValue += taxValue;

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${this.formatTaxName(taxName)}</td>
                    <td>${taxRate.toFixed(2)}%</td>
                    <td>R$ ${taxValue.toFixed(2)}</td>
                `;
                this.taxResultsBody.appendChild(row);
            }
        });

        this.totalTaxRate.textContent = `${totalTaxRate.toFixed(2)}%`;
        this.totalTaxValue.textContent = `R$ ${totalTaxValue.toFixed(2)}`;
    }

    formatTaxName(taxName) {
        const taxNames = {
            cide: 'CIDE',
            pisCofinsProduto: 'PIS/COFINS Produto',
            pisCofinsAeac: 'PIS/COFINS AEAC',
            mistura: 'Mistura',
            cidePisCof: 'CIDE + PIS/COFINS',
            icms: 'ICMS',
            pisCofinsDiesel: 'PIS/COFINS Diesel',
            pisCofinsBio: 'PIS/COFINS Biodiesel'
        };
        return taxNames[taxName] || taxName;
    }

    showError(message) {
        this.errorContainer.classList.remove('d-none');
        this.resultsContainer.classList.add('d-none');
        this.errorContainer.querySelector('.alert').textContent = message;
    }
}

// Inicializa a calculadora quando o documento estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    // Elementos do DOM
    const form = document.getElementById('taxCalculatorForm');
    const stateSelect = document.getElementById('state');
    const fuelTypeSelect = document.getElementById('fuelType');
    const baseValueInput = document.getElementById('baseValue');
    const errorMessage = document.getElementById('errorMessage');
    const resultsContainer = document.getElementById('resultsContainer');
    const resultsTableBody = document.getElementById('resultsTableBody');
    const totalTaxElement = document.getElementById('totalTax');
    const finalValueElement = document.getElementById('finalValue');
    
    // Inicializa a calculadora
    const calculadora = new CalculadoraImpostos();
    
    // Preenche os selects com os dados disponíveis
    preencherSelects();
    
    // Função para preencher os selects
    function preencherSelects() {
        // Estados
        const estados = calculadora.getEstadosDisponiveis();
        estados.forEach(estado => {
            const option = document.createElement('option');
            option.value = estado;
            option.textContent = estado;
            stateSelect.appendChild(option);
        });
        
        // Tipos de combustível
        const combustiveis = calculadora.getTiposCombustiveisDisponiveis();
        combustiveis.forEach(combustivel => {
            const option = document.createElement('option');
            option.value = combustivel.id;
            option.textContent = combustivel.nome;
            fuelTypeSelect.appendChild(option);
        });
    }
    
    // Função para formatar valores monetários
    function formatarMoeda(valor) {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).format(valor);
    }
    
    // Função para formatar percentuais
    function formatarPercentual(valor) {
        return new Intl.NumberFormat('pt-BR', {
            style: 'percent',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(valor);
    }
    
    // Função para exibir mensagem de erro
    function mostrarErro(mensagem) {
        errorMessage.textContent = mensagem;
        errorMessage.style.display = 'block';
        resultsContainer.style.display = 'none';
    }
    
    // Função para limpar mensagem de erro
    function limparErro() {
        errorMessage.textContent = '';
        errorMessage.style.display = 'none';
    }
    
    // Função para exibir resultados
    function exibirResultados(resultados) {
        // Limpa a tabela
        resultsTableBody.innerHTML = '';
        
        // Adiciona cada imposto na tabela
        Object.entries(resultados.impostos).forEach(([nome, valor]) => {
            const row = document.createElement('tr');
            
            const nomeCell = document.createElement('td');
            nomeCell.textContent = nome;
            
            const aliquotaCell = document.createElement('td');
            aliquotaCell.textContent = formatarPercentual(resultados.aliquotas[nome]);
            
            const valorCell = document.createElement('td');
            valorCell.textContent = formatarMoeda(valor);
            
            row.appendChild(nomeCell);
            row.appendChild(aliquotaCell);
            row.appendChild(valorCell);
            
            resultsTableBody.appendChild(row);
        });
        
        // Atualiza totais
        totalTaxElement.textContent = formatarMoeda(resultados.totalImpostos);
        finalValueElement.textContent = formatarMoeda(resultados.valorFinal);
        
        // Exibe o container de resultados
        resultsContainer.style.display = 'block';
        resultsContainer.classList.add('fade-in');
    }
    
    // Event listener para o formulário
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Limpa mensagens de erro
        limparErro();
        
        // Validação dos campos
        const estado = stateSelect.value;
        const tipoCombustivel = fuelTypeSelect.value;
        const valorBase = parseFloat(baseValueInput.value);
        
        if (!estado || !tipoCombustivel || isNaN(valorBase) || valorBase <= 0) {
            mostrarErro('Por favor, preencha todos os campos corretamente.');
            return;
        }
        
        try {
            // Calcula os impostos
            const resultados = calculadora.calcularImpostos(tipoCombustivel, estado, valorBase);
            
            // Exibe os resultados
            exibirResultados(resultados);
        } catch (error) {
            mostrarErro('Erro ao calcular impostos: ' + error.message);
        }
    });
});