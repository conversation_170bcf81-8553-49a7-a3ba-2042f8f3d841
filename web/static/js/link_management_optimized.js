/**
 * Script otimizado para a página de Gerenciamento de Relações
 * Este arquivo contém todas as funcionalidades JavaScript para a página de gerenciamento
 * de relações entre filiais, prestadoras e técnicos.
 */

// Variáveis globais para armazenar dados
let allBranches = [];
let allProviders = [];
let allTechnicians = [];
let allSpecialties = [];
let providerBranchLinks = [];
let technicianBranchLinks = [];
let providerTechnicianLinks = [];

// Variáveis para paginação
let providerBranchPagination = null;
let technicianBranchPagination = null;
let providerTechnicianPagination = null;

// Inicialização quando o documento estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar componentes
    initTabs();

    // Carregar dados iniciais
    loadInitialData();

    // Configurar listeners de eventos
    setupEventListeners();
});

/**
 * Inicializa as abas da página
 */
function initTabs() {
    // Usar o Bootstrap Tabs API que já está incluído
    const firstTab = document.querySelector('#provider-branch-tab');
    if (firstTab) {
        const tab = new bootstrap.Tab(firstTab);
        tab.show();
    }

    // Adicionar evento para carregar o conteúdo correto quando a aba for alterada
    const tabs = document.querySelectorAll('button[data-bs-toggle="tab"]');
    tabs.forEach(tab => {
        tab.addEventListener('shown.bs.tab', function (event) {
            const targetId = event.target.getAttribute('data-bs-target').replace('#', '');

            // Renderizar o conteúdo apropriado com base na aba selecionada
            if (targetId === 'provider-branch') {
                renderProviderBranchTable();
            } else if (targetId === 'technician-branch') {
                renderTechnicianBranchTable();
            } else if (targetId === 'provider-technician') {
                renderProviderTechnicianTable();
            }
        });
    });
}

/**
 * Carrega os dados iniciais necessários para a página
 */
async function loadInitialData() {
    try {
        // Mostrar indicador de carregamento
        showLoading(true);
        console.log("Iniciando carregamento de dados...");

        // Verificar se há dados em cache
        const cacheKey = 'link_management_data_v1';
        const cachedData = localStorage.getItem(cacheKey);
        const cacheTimestamp = localStorage.getItem(cacheKey + '_timestamp');
        const cacheExpiry = 30 * 60 * 1000; // 30 minutos em milissegundos

        // Verificar se o cache é válido
        if (cachedData && cacheTimestamp && (Date.now() - parseInt(cacheTimestamp)) < cacheExpiry) {
            console.log("Usando dados do cache local");
            const data = JSON.parse(cachedData);
            allBranches = data.branches || [];
            allProviders = data.providers || [];
            allTechnicians = data.technicians || [];
            allSpecialties = data.specialties || [];

            // Preencher dropdowns de filtros
            populateFilterDropdowns();

            // Carregar vínculos (estes sempre são carregados do servidor para garantir dados atualizados)
            await loadLinks();

            // Ocultar indicador de carregamento
            showLoading(false);
            return;
        }

        // Carregar dados em paralelo usando Promise.all
        try {
            console.log("Carregando dados básicos em paralelo...");

            const [branchesResponse, providersResponse, techniciansResponse, specialtiesResponse] =
                await Promise.all([
                    fetch('/api/branches'),
                    fetch('/api/providers'),
                    fetch('/api/technicians'),
                    fetch('/api/technicians/specialties')
                ]);

            // Processar respostas
            if (branchesResponse.ok) {
                allBranches = await branchesResponse.json();
                console.log(`${allBranches.length} filiais carregadas com sucesso`);
            } else {
                console.error('Erro ao carregar filiais:', branchesResponse.status);
                allBranches = [];
                showErrorMessage('Erro ao carregar filiais. Algumas funcionalidades podem estar limitadas.');
            }

            if (providersResponse.ok) {
                allProviders = await providersResponse.json();
                console.log(`${allProviders.length} prestadoras carregadas com sucesso`);
            } else {
                console.error('Erro ao carregar prestadoras:', providersResponse.status);
                allProviders = [];
                showErrorMessage('Erro ao carregar prestadoras. Algumas funcionalidades podem estar limitadas.');
            }

            if (techniciansResponse.ok) {
                allTechnicians = await techniciansResponse.json();
                console.log(`${allTechnicians.length} técnicos carregados com sucesso`);
            } else {
                console.error('Erro ao carregar técnicos:', techniciansResponse.status);
                allTechnicians = [];
                showErrorMessage('Erro ao carregar técnicos. Algumas funcionalidades podem estar limitadas.');
            }

            if (specialtiesResponse.ok) {
                allSpecialties = await specialtiesResponse.json();
                console.log(`${allSpecialties.length} especialidades carregadas com sucesso`);
            } else {
                console.error('Erro ao carregar especialidades:', specialtiesResponse.status);
                allSpecialties = [];
                showErrorMessage('Erro ao carregar especialidades. Algumas funcionalidades podem estar limitadas.');
            }

            // Armazenar dados em cache
            const dataToCache = {
                branches: allBranches,
                providers: allProviders,
                technicians: allTechnicians,
                specialties: allSpecialties
            };

            try {
                localStorage.setItem(cacheKey, JSON.stringify(dataToCache));
                localStorage.setItem(cacheKey + '_timestamp', Date.now().toString());
                console.log("Dados armazenados em cache local");
            } catch (cacheError) {
                console.warn("Não foi possível armazenar dados em cache:", cacheError);
            }

        } catch (error) {
            console.error('Erro ao carregar dados básicos:', error);
            showErrorMessage('Erro ao carregar dados básicos. Por favor, recarregue a página.');
        }

        // Preencher dropdowns de filtros
        console.log("Preenchendo dropdowns de filtros...");
        populateFilterDropdowns();

        // Carregar vínculos
        await loadLinks();

        console.log("Carregamento de dados concluído com sucesso");
        // Ocultar indicador de carregamento
        showLoading(false);
    } catch (error) {
        console.error('Erro geral ao carregar dados iniciais:', error);
        showErrorMessage('Erro ao carregar dados. Por favor, recarregue a página.', 'Erro de Inicialização');
        showLoading(false);
    }
}

/**
 * Carrega os vínculos entre entidades
 */
async function loadLinks(page = 1, limit = 20) {
    try {
        console.log("Carregando vínculos...");

        // Carregar todos os tipos de vínculos em paralelo
        const [providerBranchResponse, technicianBranchResponse, providerTechnicianResponse] =
            await Promise.all([
                fetch(`/api/links/provider-branch?page=${page}&limit=${limit}`),
                fetch(`/api/links/technician-branch?page=${page}&limit=${limit}`),
                fetch(`/api/links/provider-technician?page=${page}&limit=${limit}`)
            ]);

        // Processar vínculos entre prestadoras e filiais
        if (providerBranchResponse.ok) {
            const data = await providerBranchResponse.json();
            if (data && data.links) {
                providerBranchLinks = data.links;
                providerBranchPagination = data.pagination || null;
                console.log(`${providerBranchLinks.length} vínculos de prestadoras e filiais carregados`);
            } else {
                console.warn('Formato de resposta inesperado para vínculos de prestadoras e filiais:', data);
                providerBranchLinks = [];
            }
        } else {
            console.error('Erro ao carregar vínculos de prestadoras e filiais:', providerBranchResponse.status);
            providerBranchLinks = [];
        }

        // Processar vínculos entre técnicos e filiais
        if (technicianBranchResponse.ok) {
            const data = await technicianBranchResponse.json();
            if (data && data.links) {
                technicianBranchLinks = data.links;
                technicianBranchPagination = data.pagination || null;
                console.log(`${technicianBranchLinks.length} vínculos de técnicos e filiais carregados`);
            } else {
                console.warn('Formato de resposta inesperado para vínculos de técnicos e filiais:', data);
                technicianBranchLinks = [];
            }
        } else {
            console.error('Erro ao carregar vínculos de técnicos e filiais:', technicianBranchResponse.status);
            technicianBranchLinks = [];
        }

        // Processar vínculos entre prestadoras e técnicos
        if (providerTechnicianResponse.ok) {
            const data = await providerTechnicianResponse.json();
            if (data && data.links) {
                providerTechnicianLinks = data.links;
                providerTechnicianPagination = data.pagination || null;
                console.log(`${providerTechnicianLinks.length} vínculos de prestadoras e técnicos carregados`);
            } else {
                console.warn('Formato de resposta inesperado para vínculos de prestadoras e técnicos:', data);
                providerTechnicianLinks = [];
            }
        } else {
            console.error('Erro ao carregar vínculos de prestadoras e técnicos:', providerTechnicianResponse.status);
            providerTechnicianLinks = [];
        }

        // Renderizar tabelas com os dados carregados
        renderProviderBranchTable();
        renderTechnicianBranchTable();
        renderProviderTechnicianTable();

    } catch (error) {
        console.error('Erro ao carregar vínculos:', error);
        showErrorMessage('Erro ao carregar vínculos. Algumas funcionalidades podem estar limitadas.');
    }
}

/**
 * Renderiza a tabela de vínculos entre prestadoras e filiais usando DocumentFragment
 */
function renderProviderBranchTable() {
    const tableBody = document.getElementById('providerBranchTableBody');
    const providerFilter = document.getElementById('providerFilter');
    const branchFilter = document.getElementById('branchFilter');

    if (!tableBody) return;

    // Limpar tabela
    tableBody.innerHTML = '';

    // Aplicar filtros
    const providerId = providerFilter ? providerFilter.value : '';
    const branchId = branchFilter ? branchFilter.value : '';

    // Filtrar links
    let filteredLinks = [...providerBranchLinks];

    if (providerId) {
        filteredLinks = filteredLinks.filter(link => link.service_provider_id.toString() === providerId);
    }

    if (branchId) {
        filteredLinks = filteredLinks.filter(link => link.branch_id.toString() === branchId);
    }

    // Verificar se há links para exibir
    if (filteredLinks.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-link-slash text-shell-yellow"></i>
                        <h4 class="text-shell-yellow">Nenhum vínculo encontrado</h4>
                        <p>Não há vínculos entre prestadoras e filiais com os filtros selecionados.</p>
                        <div class="mt-3">
                            <button class="btn-shell-yellow btn-sm" id="addFirstProviderBranchLinkBtn">
                                <i class="fas fa-plus me-1"></i> Adicionar Primeiro Vínculo
                            </button>
                        </div>
                    </div>
                </td>
            </tr>
        `;

        // Adicionar evento ao botão
        const addFirstLinkBtn = document.getElementById('addFirstProviderBranchLinkBtn');
        if (addFirstLinkBtn) {
            addFirstLinkBtn.addEventListener('click', showAddProviderBranchModal);
        }

        return;
    }

    // Usar DocumentFragment para melhor performance
    const fragment = document.createDocumentFragment();

    // Preencher tabela com links filtrados
    filteredLinks.forEach(link => {
        const row = document.createElement('tr');

        // Criar células da linha
        row.innerHTML = `
            <td>${link.provider_name || 'Desconhecida'}</td>
            <td>${link.branch_name || 'Desconhecida'}</td>
            <td>${new Date(link.created_at).toLocaleDateString('pt-BR')}</td>
            <td>
                <button class="btn btn-sm btn-danger unlink-provider-branch"
                        data-provider-id="${link.service_provider_id}"
                        data-branch-id="${link.branch_id}">
                    <i class="fas fa-unlink"></i> Desvincular
                </button>
            </td>
        `;

        fragment.appendChild(row);
    });

    // Adicionar todas as linhas de uma vez
    tableBody.appendChild(fragment);

    // Adicionar eventos aos botões de desvincular
    document.querySelectorAll('.unlink-provider-branch').forEach(button => {
        button.addEventListener('click', function() {
            const providerId = this.getAttribute('data-provider-id');
            const branchId = this.getAttribute('data-branch-id');
            unlinkProviderBranch(providerId, branchId);
        });
    });

    // Renderizar paginação se disponível
    renderPagination('providerBranchPagination', providerBranchPagination, loadProviderBranchPage);
}

/**
 * Renderiza a tabela de vínculos entre técnicos e filiais usando DocumentFragment
 */
function renderTechnicianBranchTable() {
    const tableBody = document.getElementById('technicianBranchTableBody');
    const technicianFilter = document.getElementById('technicianFilter');
    const branchFilter = document.getElementById('branchTechFilter');
    const specialtyFilter = document.getElementById('specialtyFilter');

    if (!tableBody) return;

    // Limpar tabela
    tableBody.innerHTML = '';

    // Aplicar filtros
    const technicianId = technicianFilter ? technicianFilter.value : '';
    const branchId = branchFilter ? branchFilter.value : '';
    const specialtyId = specialtyFilter ? specialtyFilter.value : '';

    // Filtrar links
    let filteredLinks = [...technicianBranchLinks];

    if (technicianId) {
        filteredLinks = filteredLinks.filter(link => link.technician_id.toString() === technicianId);
    }

    if (branchId) {
        filteredLinks = filteredLinks.filter(link => link.branch_id.toString() === branchId);
    }

    if (specialtyId) {
        filteredLinks = filteredLinks.filter(link => link.specialty_id.toString() === specialtyId);
    }

    // Verificar se há links para exibir
    if (filteredLinks.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-link-slash text-shell-yellow"></i>
                        <h4 class="text-shell-yellow">Nenhum vínculo encontrado</h4>
                        <p>Não há vínculos entre técnicos e filiais com os filtros selecionados.</p>
                        <div class="mt-3">
                            <button class="btn-shell-yellow btn-sm" id="addFirstTechBranchLinkBtn">
                                <i class="fas fa-plus me-1"></i> Adicionar Primeiro Vínculo
                            </button>
                        </div>
                    </div>
                </td>
            </tr>
        `;

        // Adicionar evento ao botão
        const addFirstLinkBtn = document.getElementById('addFirstTechBranchLinkBtn');
        if (addFirstLinkBtn) {
            addFirstLinkBtn.addEventListener('click', showAddTechnicianBranchModal);
        }

        return;
    }

    // Usar DocumentFragment para melhor performance
    const fragment = document.createDocumentFragment();

    // Preencher tabela com links filtrados
    filteredLinks.forEach(link => {
        const row = document.createElement('tr');

        // Criar células da linha
        row.innerHTML = `
            <td>${link.technician_name || 'Desconhecido'}</td>
            <td>${link.branch_name || 'Desconhecida'}</td>
            <td>${link.specialty_name || 'Geral'}</td>
            <td>${new Date(link.created_at).toLocaleDateString('pt-BR')}</td>
            <td>
                <button class="btn btn-sm btn-danger unlink-technician-branch"
                        data-technician-id="${link.technician_id}"
                        data-branch-id="${link.branch_id}">
                    <i class="fas fa-unlink"></i> Desvincular
                </button>
            </td>
        `;

        fragment.appendChild(row);
    });

    // Adicionar todas as linhas de uma vez
    tableBody.appendChild(fragment);

    // Adicionar eventos aos botões de desvincular
    document.querySelectorAll('.unlink-technician-branch').forEach(button => {
        button.addEventListener('click', function() {
            const technicianId = this.getAttribute('data-technician-id');
            const branchId = this.getAttribute('data-branch-id');
            unlinkTechnicianBranch(technicianId, branchId);
        });
    });

    // Renderizar paginação se disponível
    renderPagination('technicianBranchPagination', technicianBranchPagination, loadTechnicianBranchPage);
}

/**
 * Renderiza a tabela de vínculos entre prestadoras e técnicos usando DocumentFragment
 */
function renderProviderTechnicianTable() {
    const tableBody = document.getElementById('providerTechnicianTableBody');
    const providerFilter = document.getElementById('providerTechFilter');
    const technicianFilter = document.getElementById('technicianProviderFilter');

    if (!tableBody) return;

    // Limpar tabela
    tableBody.innerHTML = '';

    // Aplicar filtros
    const providerId = providerFilter ? providerFilter.value : '';
    const technicianId = technicianFilter ? technicianFilter.value : '';

    // Filtrar links
    let filteredLinks = [...providerTechnicianLinks];

    if (providerId && providerId !== 'no_technicians') {
        filteredLinks = filteredLinks.filter(link => link.provider_id.toString() === providerId);
    }

    if (technicianId && technicianId !== 'no_provider') {
        filteredLinks = filteredLinks.filter(link => link.technician_id.toString() === technicianId);
    }

    // Casos especiais de filtro
    if (providerId === 'no_technicians') {
        // Lógica para prestadoras sem técnicos
        const providersWithTechs = new Set(providerTechnicianLinks.map(link => link.provider_id));
        const providersWithoutTechs = allProviders.filter(p => !providersWithTechs.has(p.id));

        if (providersWithoutTechs.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center">
                        <div class="empty-state">
                            <i class="fas fa-info-circle text-shell-yellow"></i>
                            <h4 class="text-shell-yellow">Todas as prestadoras possuem técnicos</h4>
                            <p>Não há prestadoras sem técnicos vinculados.</p>
                        </div>
                    </td>
                </tr>
            `;
        } else {
            const fragment = document.createDocumentFragment();

            providersWithoutTechs.forEach(provider => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td colspan="3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-building me-2 text-shell-yellow"></i>
                            <strong>${provider.name}</strong>
                            <span class="badge bg-warning text-dark ms-2">Sem técnicos</span>
                        </div>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-shell-yellow add-provider-technician"
                                data-provider-id="${provider.id}">
                            <i class="fas fa-user-plus"></i> Adicionar Técnico
                        </button>
                    </td>
                `;
                fragment.appendChild(row);
            });

            tableBody.appendChild(fragment);

            // Adicionar eventos aos botões
            document.querySelectorAll('.add-provider-technician').forEach(button => {
                button.addEventListener('click', function() {
                    const providerId = this.getAttribute('data-provider-id');
                    showAddProviderTechnicianModal(providerId);
                });
            });
        }

        return;
    }

    if (technicianId === 'no_provider') {
        // Lógica para técnicos sem prestadora
        const techniciansWithProviders = new Set(providerTechnicianLinks.map(link => link.technician_id));
        const techniciansWithoutProviders = allTechnicians.filter(t => !techniciansWithProviders.has(t.id));

        if (techniciansWithoutProviders.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center">
                        <div class="empty-state">
                            <i class="fas fa-info-circle text-shell-yellow"></i>
                            <h4 class="text-shell-yellow">Todos os técnicos possuem prestadoras</h4>
                            <p>Não há técnicos sem prestadoras vinculadas.</p>
                        </div>
                    </td>
                </tr>
            `;
        } else {
            const fragment = document.createDocumentFragment();

            techniciansWithoutProviders.forEach(technician => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-user-cog me-2 text-shell-yellow"></i>
                            <strong>${technician.name}</strong>
                            <span class="badge bg-warning text-dark ms-2">Sem prestadora</span>
                        </div>
                    </td>
                    <td>${technician.email || 'N/A'}</td>
                    <td>${technician.phone || 'N/A'}</td>
                    <td>
                        <button class="btn btn-sm btn-shell-yellow add-technician-provider"
                                data-technician-id="${technician.id}">
                            <i class="fas fa-building-user"></i> Vincular a Prestadora
                        </button>
                    </td>
                `;
                fragment.appendChild(row);
            });

            tableBody.appendChild(fragment);

            // Adicionar eventos aos botões
            document.querySelectorAll('.add-technician-provider').forEach(button => {
                button.addEventListener('click', function() {
                    const technicianId = this.getAttribute('data-technician-id');
                    showAddProviderTechnicianModal(null, technicianId);
                });
            });
        }

        return;
    }

    // Verificar se há links para exibir
    if (filteredLinks.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-link-slash text-shell-yellow"></i>
                        <h4 class="text-shell-yellow">Nenhum vínculo encontrado</h4>
                        <p>Não há vínculos entre prestadoras e técnicos com os filtros selecionados.</p>
                        <div class="mt-3">
                            <button class="btn-shell-yellow btn-sm" id="addFirstProviderTechnicianLinkBtn">
                                <i class="fas fa-plus me-1"></i> Adicionar Primeiro Vínculo
                            </button>
                        </div>
                    </div>
                </td>
            </tr>
        `;

        // Adicionar evento ao botão
        const addFirstLinkBtn = document.getElementById('addFirstProviderTechnicianLinkBtn');
        if (addFirstLinkBtn) {
            addFirstLinkBtn.addEventListener('click', showAddProviderTechnicianModal);
        }

        return;
    }

    // Usar DocumentFragment para melhor performance
    const fragment = document.createDocumentFragment();

    // Preencher tabela com links filtrados
    filteredLinks.forEach(link => {
        const row = document.createElement('tr');

        // Criar células da linha
        row.innerHTML = `
            <td>${link.provider_name || 'Desconhecida'}</td>
            <td>${link.technician_name || 'Desconhecido'}</td>
            <td>${link.technician_email || 'N/A'}</td>
            <td>
                <button class="btn btn-sm btn-danger unlink-provider-technician"
                        data-provider-id="${link.provider_id}"
                        data-technician-id="${link.technician_id}">
                    <i class="fas fa-unlink"></i> Desvincular
                </button>
                <button class="btn btn-sm btn-info inherit-branch-links"
                        data-provider-id="${link.provider_id}"
                        data-technician-id="${link.technician_id}">
                    <i class="fas fa-sitemap"></i> Herdar Filiais
                </button>
            </td>
        `;

        fragment.appendChild(row);
    });

    // Adicionar todas as linhas de uma vez
    tableBody.appendChild(fragment);

    // Adicionar eventos aos botões de desvincular
    document.querySelectorAll('.unlink-provider-technician').forEach(button => {
        button.addEventListener('click', function() {
            const providerId = this.getAttribute('data-provider-id');
            const technicianId = this.getAttribute('data-technician-id');
            unlinkProviderTechnician(providerId, technicianId);
        });
    });

    // Adicionar eventos aos botões de herdar filiais
    document.querySelectorAll('.inherit-branch-links').forEach(button => {
        button.addEventListener('click', function() {
            const providerId = this.getAttribute('data-provider-id');
            const technicianId = this.getAttribute('data-technician-id');
            inheritBranchLinks(providerId, technicianId);
        });
    });

    // Renderizar paginação se disponível
    renderPagination('providerTechnicianPagination', providerTechnicianPagination, loadProviderTechnicianPage);
}

/**
 * Renderiza a paginação para uma tabela
 * @param {string} containerId - ID do elemento que conterá a paginação
 * @param {object} pagination - Objeto com informações de paginação
 * @param {function} loadPageFunction - Função para carregar uma página específica
 */
function renderPagination(containerId, pagination, loadPageFunction) {
    const container = document.getElementById(containerId);
    if (!container || !pagination) return;

    // Limpar container
    container.innerHTML = '';

    // Verificar se há mais de uma página
    if (pagination.total_pages <= 1) return;

    // Criar elemento de paginação
    const nav = document.createElement('nav');
    nav.setAttribute('aria-label', 'Navegação de páginas');

    const ul = document.createElement('ul');
    ul.className = 'pagination justify-content-center';

    // Botão "Anterior"
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${pagination.current_page === 1 ? 'disabled' : ''}`;

    const prevLink = document.createElement('a');
    prevLink.className = 'page-link';
    prevLink.href = '#';
    prevLink.setAttribute('aria-label', 'Anterior');
    prevLink.innerHTML = '<span aria-hidden="true">&laquo;</span>';

    if (pagination.current_page > 1) {
        prevLink.addEventListener('click', (e) => {
            e.preventDefault();
            loadPageFunction(pagination.current_page - 1);
        });
    }

    prevLi.appendChild(prevLink);
    ul.appendChild(prevLi);

    // Páginas
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const pageLi = document.createElement('li');
        pageLi.className = `page-item ${i === pagination.current_page ? 'active' : ''}`;

        const pageLink = document.createElement('a');
        pageLink.className = 'page-link';
        pageLink.href = '#';
        pageLink.textContent = i;

        if (i !== pagination.current_page) {
            pageLink.addEventListener('click', (e) => {
                e.preventDefault();
                loadPageFunction(i);
            });
        }

        pageLi.appendChild(pageLink);
        ul.appendChild(pageLi);
    }

    // Botão "Próximo"
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${pagination.current_page === pagination.total_pages ? 'disabled' : ''}`;

    const nextLink = document.createElement('a');
    nextLink.className = 'page-link';
    nextLink.href = '#';
    nextLink.setAttribute('aria-label', 'Próximo');
    nextLink.innerHTML = '<span aria-hidden="true">&raquo;</span>';

    if (pagination.current_page < pagination.total_pages) {
        nextLink.addEventListener('click', (e) => {
            e.preventDefault();
            loadPageFunction(pagination.current_page + 1);
        });
    }

    nextLi.appendChild(nextLink);
    ul.appendChild(nextLi);

    nav.appendChild(ul);
    container.appendChild(nav);
}

/**
 * Carrega uma página específica de vínculos entre prestadoras e filiais
 * @param {number} page - Número da página a ser carregada
 */
function loadProviderBranchPage(page) {
    loadLinks(page, providerBranchPagination?.limit || 20);
}

/**
 * Carrega uma página específica de vínculos entre técnicos e filiais
 * @param {number} page - Número da página a ser carregada
 */
function loadTechnicianBranchPage(page) {
    loadLinks(page, technicianBranchPagination?.limit || 20);
}

/**
 * Carrega uma página específica de vínculos entre prestadoras e técnicos
 * @param {number} page - Número da página a ser carregada
 */
function loadProviderTechnicianPage(page) {
    loadLinks(page, providerTechnicianPagination?.limit || 20);
}

/**
 * Configura os listeners de eventos para a página
 */
function setupEventListeners() {
    // Configurar filtros
    setupFilterListeners();

    // Configurar botões de adicionar vínculos
    setupAddLinkButtons();

    // Configurar botões de limpar filtros
    setupClearFilterButtons();
}

/**
 * Configura os listeners para os filtros
 */
function setupFilterListeners() {
    // Filtros de prestadoras e filiais
    const providerFilter = document.getElementById('providerFilter');
    const branchFilter = document.getElementById('branchFilter');

    if (providerFilter) {
        providerFilter.addEventListener('change', renderProviderBranchTable);
    }

    if (branchFilter) {
        branchFilter.addEventListener('change', renderProviderBranchTable);
    }

    // Filtros de técnicos e filiais
    const technicianFilter = document.getElementById('technicianFilter');
    const branchTechFilter = document.getElementById('branchTechFilter');
    const specialtyFilter = document.getElementById('specialtyFilter');

    if (technicianFilter) {
        technicianFilter.addEventListener('change', renderTechnicianBranchTable);
    }

    if (branchTechFilter) {
        branchTechFilter.addEventListener('change', renderTechnicianBranchTable);
    }

    if (specialtyFilter) {
        specialtyFilter.addEventListener('change', renderTechnicianBranchTable);
    }

    // Filtros de prestadoras e técnicos
    const providerTechFilter = document.getElementById('providerTechFilter');
    const technicianProviderFilter = document.getElementById('technicianProviderFilter');

    if (providerTechFilter) {
        providerTechFilter.addEventListener('change', renderProviderTechnicianTable);
    }

    if (technicianProviderFilter) {
        technicianProviderFilter.addEventListener('change', renderProviderTechnicianTable);
    }
}

/**
 * Configura os botões de adicionar vínculos
 */
function setupAddLinkButtons() {
    // Botão de adicionar vínculo entre prestadora e filial
    const addProviderBranchBtn = document.getElementById('addProviderBranchBtn');
    if (addProviderBranchBtn) {
        addProviderBranchBtn.addEventListener('click', showAddProviderBranchModal);
    }

    // Botão de adicionar vínculo entre técnico e filial
    const addTechnicianBranchBtn = document.getElementById('addTechnicianBranchBtn');
    if (addTechnicianBranchBtn) {
        addTechnicianBranchBtn.addEventListener('click', showAddTechnicianBranchModal);
    }

    // Botão de adicionar vínculo entre prestadora e técnico
    const addProviderTechnicianBtn = document.getElementById('addProviderTechnicianBtn');
    if (addProviderTechnicianBtn) {
        addProviderTechnicianBtn.addEventListener('click', showAddProviderTechnicianModal);
    }
}

/**
 * Configura os botões de limpar filtros
 */
function setupClearFilterButtons() {
    // Botão de limpar filtros de prestadoras e filiais
    const clearProviderBranchFiltersBtn = document.getElementById('clearProviderBranchFiltersBtn');
    if (clearProviderBranchFiltersBtn) {
        clearProviderBranchFiltersBtn.addEventListener('click', () => {
            const providerFilter = document.getElementById('providerFilter');
            const branchFilter = document.getElementById('branchFilter');

            if (providerFilter) providerFilter.value = '';
            if (branchFilter) branchFilter.value = '';

            renderProviderBranchTable();
        });
    }

    // Botão de limpar filtros de técnicos e filiais
    const clearTechnicianBranchFiltersBtn = document.getElementById('clearTechnicianBranchFiltersBtn');
    if (clearTechnicianBranchFiltersBtn) {
        clearTechnicianBranchFiltersBtn.addEventListener('click', () => {
            const technicianFilter = document.getElementById('technicianFilter');
            const branchTechFilter = document.getElementById('branchTechFilter');
            const specialtyFilter = document.getElementById('specialtyFilter');

            if (technicianFilter) technicianFilter.value = '';
            if (branchTechFilter) branchTechFilter.value = '';
            if (specialtyFilter) specialtyFilter.value = '';

            renderTechnicianBranchTable();
        });
    }

    // Botão de limpar filtros de prestadoras e técnicos
    const clearProviderTechnicianFiltersBtn = document.getElementById('clearProviderTechnicianFiltersBtn');
    if (clearProviderTechnicianFiltersBtn) {
        clearProviderTechnicianFiltersBtn.addEventListener('click', () => {
            const providerTechFilter = document.getElementById('providerTechFilter');
            const technicianProviderFilter = document.getElementById('technicianProviderFilter');

            if (providerTechFilter) providerTechFilter.value = '';
            if (technicianProviderFilter) technicianProviderFilter.value = '';

            renderProviderTechnicianTable();
        });
    }
}

/**
 * Preenche os dropdowns de filtros com os dados disponíveis
 */
function populateFilterDropdowns() {
    // Preencher dropdown de prestadoras
    populateDropdown('providerFilter', allProviders, 'Todas as Prestadoras');
    populateDropdown('providerTechFilter', allProviders, 'Todas as Prestadoras', true);

    // Preencher dropdown de filiais
    populateDropdown('branchFilter', allBranches, 'Todas as Filiais');
    populateDropdown('branchTechFilter', allBranches, 'Todas as Filiais');

    // Preencher dropdown de técnicos
    populateDropdown('technicianFilter', allTechnicians, 'Todos os Técnicos');
    populateDropdown('technicianProviderFilter', allTechnicians, 'Todos os Técnicos', true);

    // Preencher dropdown de especialidades
    populateDropdown('specialtyFilter', allSpecialties, 'Todas as Especialidades');
}

/**
 * Preenche um dropdown com os dados fornecidos
 * @param {string} dropdownId - ID do elemento select
 * @param {Array} items - Array de itens para preencher o dropdown
 * @param {string} defaultLabel - Label para a opção padrão (vazia)
 * @param {boolean} addSpecialOptions - Se deve adicionar opções especiais
 */
function populateDropdown(dropdownId, items, defaultLabel, addSpecialOptions = false) {
    const dropdown = document.getElementById(dropdownId);
    if (!dropdown) return;

    // Limpar dropdown
    dropdown.innerHTML = '';

    // Adicionar opção padrão
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = defaultLabel;
    dropdown.appendChild(defaultOption);

    // Adicionar opções especiais se necessário
    if (addSpecialOptions) {
        if (dropdownId === 'providerTechFilter') {
            const noTechniciansOption = document.createElement('option');
            noTechniciansOption.value = 'no_technicians';
            noTechniciansOption.textContent = 'Prestadoras sem Técnicos';
            dropdown.appendChild(noTechniciansOption);
        } else if (dropdownId === 'technicianProviderFilter') {
            const noProviderOption = document.createElement('option');
            noProviderOption.value = 'no_provider';
            noProviderOption.textContent = 'Técnicos sem Prestadora';
            dropdown.appendChild(noProviderOption);
        }
    }

    // Adicionar itens
    items.forEach(item => {
        const option = document.createElement('option');
        option.value = item.id;
        option.textContent = item.name;
        dropdown.appendChild(option);
    });
}

/**
 * Exibe um indicador de carregamento
 * @param {boolean} show - Se deve mostrar ou ocultar o indicador
 */
function showLoading(show) {
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = show ? 'flex' : 'none';
    }
}

/**
 * Exibe uma mensagem de erro
 * @param {string} message - Mensagem de erro
 * @param {string} title - Título da mensagem (opcional)
 */
function showErrorMessage(message, title = 'Erro') {
    // Verificar se o Bootstrap está disponível
    if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
        const toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) return;

        // Criar elemento de toast
        const toastElement = document.createElement('div');
        toastElement.className = 'toast align-items-center text-white bg-danger border-0';
        toastElement.setAttribute('role', 'alert');
        toastElement.setAttribute('aria-live', 'assertive');
        toastElement.setAttribute('aria-atomic', 'true');

        toastElement.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <strong>${title}</strong>: ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Fechar"></button>
            </div>
        `;

        toastContainer.appendChild(toastElement);

        // Inicializar e mostrar o toast
        const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 5000 });
        toast.show();

        // Remover o toast após ser ocultado
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    } else {
        // Fallback para alert se o Bootstrap não estiver disponível
        alert(`${title}: ${message}`);
    }
}

/**
 * Exibe uma mensagem de sucesso
 * @param {string} message - Mensagem de sucesso
 */
function showSuccessMessage(message) {
    // Verificar se o Bootstrap está disponível
    if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
        const toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) return;

        // Criar elemento de toast
        const toastElement = document.createElement('div');
        toastElement.className = 'toast align-items-center text-white bg-success border-0';
        toastElement.setAttribute('role', 'alert');
        toastElement.setAttribute('aria-live', 'assertive');
        toastElement.setAttribute('aria-atomic', 'true');

        toastElement.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check-circle me-2"></i> ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Fechar"></button>
            </div>
        `;

        toastContainer.appendChild(toastElement);

        // Inicializar e mostrar o toast
        const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });
        toast.show();

        // Remover o toast após ser ocultado
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    } else {
        // Fallback para alert se o Bootstrap não estiver disponível
        alert(`Sucesso: ${message}`);
    }
}