/**
 * Alterar Senha - Sistema de Manutenção Shell
 * Script para gerenciar a página de alteração de senha do usuário
 */
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar formulário
    initializeForm();

    // Inicializar validação de senha
    initializePasswordValidation();
});

/**
 * Inicializa o formulário de alteração de senha
 */
function initializeForm() {
    const passwordForm = document.getElementById('passwordForm');

    if (passwordForm) {
        passwordForm.addEventListener('submit', function(event) {
            event.preventDefault();

            // Validar formulário
            if (!validateForm()) {
                return;
            }

            // Obter dados do formulário
            const formData = {
                "current_password": document.getElementById('currentPassword').value,
                "new_password": document.getElementById('newPassword').value,
                "confirm_password": document.getElementById('confirmPassword').value
            };

            // Enviar dados para a API
            changePassword(formData);
        });
    }
}

/**
 * Inicializa a validação de senha
 */
function initializePasswordValidation() {
    const newPassword = document.getElementById('newPassword');
    const confirmPassword = document.getElementById('confirmPassword');

    if (newPassword) {
        newPassword.addEventListener('input', function() {
            validatePassword(this.value);
            checkPasswordMatch();
        });
    }

    if (confirmPassword) {
        confirmPassword.addEventListener('input', function() {
            checkPasswordMatch();
        });
    }
}

/**
 * Valida o formulário de alteração de senha
 * @returns {boolean} - Retorna true se o formulário for válido
 */
function validateForm() {
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    // Validar senha atual
    if (!currentPassword || currentPassword.trim() === '') {
        showError('A senha atual é obrigatória.');
        return false;
    }

    // Validar nova senha
    if (!newPassword || newPassword.trim() === '') {
        showError('A nova senha é obrigatória.');
        return false;
    }

    // Validar confirmação de senha
    if (!confirmPassword || confirmPassword.trim() === '') {
        showError('A confirmação da nova senha é obrigatória.');
        return false;
    }

    // Verificar se as senhas coincidem
    if (newPassword !== confirmPassword) {
        showError('A nova senha e a confirmação não coincidem.');
        return false;
    }

    // Verificar se a nova senha atende aos requisitos
    if (!isPasswordValid(newPassword)) {
        showError('A nova senha não atende aos requisitos de segurança.');
        return false;
    }

    return true;
}

/**
 * Verifica se a senha atende aos requisitos
 * @param {string} password - Senha a ser verificada
 * @returns {boolean} - Retorna true se a senha for válida
 */
function isPasswordValid(password) {
    // Verificar comprimento mínimo
    if (password.length < 8) {
        return false;
    }

    // Verificar letra maiúscula
    if (!/[A-Z]/.test(password)) {
        return false;
    }

    // Verificar letra minúscula
    if (!/[a-z]/.test(password)) {
        return false;
    }

    // Verificar número
    if (!/[0-9]/.test(password)) {
        return false;
    }

    // Verificar caractere especial
    if (!/[^A-Za-z0-9]/.test(password)) {
        return false;
    }

    return true;
}

/**
 * Valida a senha e atualiza a interface
 * @param {string} password - Senha a ser validada
 */
function validatePassword(password) {
    // Verificar requisitos
    const reqLength = document.getElementById('req-length');
    const reqUppercase = document.getElementById('req-uppercase');
    const reqLowercase = document.getElementById('req-lowercase');
    const reqNumber = document.getElementById('req-number');
    const reqSpecial = document.getElementById('req-special');

    // Verificar comprimento mínimo
    if (password.length >= 8) {
        reqLength.classList.add('valid');
        reqLength.innerHTML = '<i class="fas fa-check-circle"></i> Mínimo de 8 caracteres';
    } else {
        reqLength.classList.remove('valid');
        reqLength.innerHTML = '<i class="fas fa-circle"></i> Mínimo de 8 caracteres';
    }

    // Verificar letra maiúscula
    if (/[A-Z]/.test(password)) {
        reqUppercase.classList.add('valid');
        reqUppercase.innerHTML = '<i class="fas fa-check-circle"></i> Pelo menos uma letra maiúscula';
    } else {
        reqUppercase.classList.remove('valid');
        reqUppercase.innerHTML = '<i class="fas fa-circle"></i> Pelo menos uma letra maiúscula';
    }

    // Verificar letra minúscula
    if (/[a-z]/.test(password)) {
        reqLowercase.classList.add('valid');
        reqLowercase.innerHTML = '<i class="fas fa-check-circle"></i> Pelo menos uma letra minúscula';
    } else {
        reqLowercase.classList.remove('valid');
        reqLowercase.innerHTML = '<i class="fas fa-circle"></i> Pelo menos uma letra minúscula';
    }

    // Verificar número
    if (/[0-9]/.test(password)) {
        reqNumber.classList.add('valid');
        reqNumber.innerHTML = '<i class="fas fa-check-circle"></i> Pelo menos um número';
    } else {
        reqNumber.classList.remove('valid');
        reqNumber.innerHTML = '<i class="fas fa-circle"></i> Pelo menos um número';
    }

    // Verificar caractere especial
    if (/[^A-Za-z0-9]/.test(password)) {
        reqSpecial.classList.add('valid');
        reqSpecial.innerHTML = '<i class="fas fa-check-circle"></i> Pelo menos um caractere especial';
    } else {
        reqSpecial.classList.remove('valid');
        reqSpecial.innerHTML = '<i class="fas fa-circle"></i> Pelo menos um caractere especial';
    }

    // Atualizar medidor de força
    updateStrengthMeter(password);
}

/**
 * Atualiza o medidor de força da senha
 * @param {string} password - Senha a ser avaliada
 */
function updateStrengthMeter(password) {
    const strengthMeter = document.getElementById('strengthMeter');
    const strengthText = document.getElementById('strengthText');

    // Calcular pontuação
    let score = 0;

    // Comprimento
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;

    // Complexidade
    if (/[A-Z]/.test(password)) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;

    // Atualizar medidor
    strengthMeter.className = 'strength-meter-fill';

    if (password.length === 0) {
        strengthMeter.style.width = '0';
        strengthText.textContent = 'Força da senha';
    } else if (score < 3) {
        strengthMeter.classList.add('weak');
        strengthText.textContent = 'Fraca';
    } else if (score < 5) {
        strengthMeter.classList.add('medium');
        strengthText.textContent = 'Média';
    } else if (score < 7) {
        strengthMeter.classList.add('strong');
        strengthText.textContent = 'Forte';
    } else {
        strengthMeter.classList.add('very-strong');
        strengthText.textContent = 'Muito forte';
    }
}

/**
 * Verifica se as senhas coincidem
 */
function checkPasswordMatch() {
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    if (confirmPassword && newPassword !== confirmPassword) {
        document.getElementById('confirmPassword').classList.add('is-invalid');
    } else {
        document.getElementById('confirmPassword').classList.remove('is-invalid');
    }
}

/**
 * Alterna a visibilidade da senha
 * @param {string} inputId - ID do campo de senha
 */
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;

    if (input.type === 'password') {
        input.type = 'text';
        button.innerHTML = '<i class="fas fa-eye-slash"></i>';
    } else {
        input.type = 'password';
        button.innerHTML = '<i class="fas fa-eye"></i>';
    }
}

/**
 * Altera a senha do usuário
 * @param {Object} passwordData - Dados da senha
 */
function changePassword(passwordData) {
    // Exibir indicador de carregamento
    const submitButton = document.querySelector('button[type="submit"]');
    const originalButtonText = submitButton.innerHTML;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Alterando...';
    submitButton.disabled = true;

    console.log('[ALTERAR SENHA] Iniciando processo de alteração de senha');
    console.log('[ALTERAR SENHA] Dados enviados:', JSON.stringify(passwordData));

    // Obter token CSRF
    const csrfToken = getCsrfToken();
    console.log('[ALTERAR SENHA] Token CSRF obtido:', csrfToken ? 'Sim' : 'Não');

    // Fazer requisição para a API
    fetch('/api/user/change-password', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': csrfToken // Adicionar token CSRF se necessário
        },
        body: JSON.stringify(passwordData),
        credentials: 'same-origin' // Incluir cookies na requisição
    })
    .then(response => {
        console.log('[ALTERAR SENHA] Status da resposta:', response.status);
        console.log('[ALTERAR SENHA] Headers da resposta:', [...response.headers.entries()]);

        // Sempre tentar ler a resposta, independente do status
        return response.text().then(text => {
            console.log('[ALTERAR SENHA] Texto da resposta:', text);
            // Tentar converter para JSON se possível
            try {
                if (text) {
                    return { json: JSON.parse(text), status: response.status, ok: response.ok };
                }
                return { json: {}, status: response.status, ok: response.ok };
            } catch (e) {
                console.error('[ALTERAR SENHA] Erro ao parsear JSON:', e);
                return { text: text, status: response.status, ok: response.ok };
            }
        });
    })
    .then(result => {
        console.log('[ALTERAR SENHA] Resultado processado:', result);

        if (!result.ok) {
            // Tratar erro
            if (result.json && result.json.error) {
                throw new Error(result.json.error);
            } else if (result.text) {
                throw new Error('Resposta do servidor: ' + result.text);
            } else {
                throw new Error('Falha ao alterar senha: ' + result.status);
            }
        }

        // Sucesso
        return result.json;
    })
    .then(data => {
        console.log('[ALTERAR SENHA] Dados de sucesso:', data);
        // Exibir mensagem de sucesso
        showSuccess();

        // Restaurar botão
        submitButton.innerHTML = originalButtonText;
        submitButton.disabled = false;
    })
    .catch(error => {
        console.error('[ALTERAR SENHA] Erro ao alterar senha:', error);

        // Exibir mensagem de erro
        showError('Erro ao alterar senha: ' + error.message);

        // Restaurar botão
        submitButton.innerHTML = originalButtonText;
        submitButton.disabled = false;
    });
}

/**
 * Exibe mensagem de sucesso
 */
function showSuccess() {
    const successModal = new bootstrap.Modal(document.getElementById('successModal'));
    successModal.show();
}

/**
 * Exibe mensagem de erro
 * @param {string} message - Mensagem de erro
 */
function showError(message) {
    const errorModal = document.getElementById('errorModal');
    const errorMessage = document.getElementById('errorMessage');

    errorMessage.textContent = message;

    const modal = new bootstrap.Modal(errorModal);
    modal.show();
}

/**
 * Obtém o token CSRF da página
 * @returns {string} - Token CSRF ou string vazia se não encontrado
 */
function getCsrfToken() {
    const metaTag = document.querySelector('meta[name="csrf-token"]');
    return metaTag ? metaTag.getAttribute('content') : '';
}
