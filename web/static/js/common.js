/**
 * Funções comuns utilizadas em várias páginas do sistema TRADICAO
 */

// Verificar autenticação do usuário
function checkAuthentication() {
    return fetch('/api/user/me')
        .then(response => {
            if (!response.ok) {
                if (response.status === 401) {
                    // Não está autenticado
                    if (!window.location.pathname.startsWith('/login') &&
                        window.location.pathname !== '/') {
                        window.location.href = '/';
                    }
                    return null;
                }
                throw new Error('Erro na autenticação');
            }
            return response.json();
        })
        .then(user => {
            if (user) {
                updateUserInfo(user);
                setupLogoutButtons();
            }
            return user;
        })
        .catch(error => {
            console.error('Erro ao verificar autenticação:', error);
            return null;
        });
}

// Configurar botões de logout
function setupLogoutButtons() {
    const logoutButtons = document.querySelectorAll('.logout-btn, #logout-btn');

    logoutButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Verificar se o botão tem o atributo data-api-logout
            if (button.hasAttribute('data-api-logout')) {
                // Usar a função de logout via API
                logout();
            } else {
                // Redirecionar diretamente para a rota de logout
                window.location.href = '/logout';
            }
        });
    });
}

// Processar logout
function logout() {
    fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (response.ok) {
            showNotification('Logout realizado com sucesso!', 'success');
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        } else {
            throw new Error('Falha no logout');
        }
    })
    .catch(error => {
        console.error('Erro ao fazer logout:', error);
        showNotification('Erro ao fazer logout. Tente novamente.', 'error');
        // Mesmo em caso de erro, tente redirecionar para a página inicial
        setTimeout(() => {
            window.location.href = '/';
        }, 2000);
    });
}

// Atualizar informações do usuário na interface
function updateUserInfo(user) {
    // Atualizar nome do usuário
    const userNameElements = document.querySelectorAll('.user-name, #user-name');
    userNameElements.forEach(element => {
        if (element) {
            element.textContent = user.name || 'Usuário';
        }
    });

    // Atualizar função do usuário
    const userRoleElements = document.querySelectorAll('.user-role, #user-role');
    userRoleElements.forEach(element => {
        if (element) {
            element.textContent = translateRole(user.role) || 'Usuário';
        }
    });

    // Atualizar avatar do usuário
    const userAvatarElements = document.querySelectorAll('.user-avatar img, #user-avatar');
    userAvatarElements.forEach(element => {
        if (element && user.avatar) {
            element.src = user.avatar;
        }
    });

    // Atualizar informações em outros campos específicos
    const userEmailElements = document.querySelectorAll('.user-email');
    userEmailElements.forEach(element => {
        if (element && user.email) {
            element.textContent = user.email;
        }
    });
}

// Inicializar tooltips
function initTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Formatar valor monetário
function formatCurrency(value) {
    return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
    }).format(value);
}

// Formatar data
function formatDate(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    }).format(date);
}

// Formatar data e hora
function formatDateTime(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(date);
}

// Mostrar notificação toast
function showNotification(message, type = 'success') {
    // Verificar se o container de notificações existe
    let notificationContainer = document.getElementById('notification-container');

    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'notification-container';
        notificationContainer.className = 'position-fixed top-0 end-0 p-3';
        notificationContainer.style.zIndex = '1060';
        document.body.appendChild(notificationContainer);
    }

    // Criar elemento de notificação
    const notification = document.createElement('div');
    notification.className = `toast align-items-center text-white bg-${type} border-0`;
    notification.setAttribute('role', 'alert');
    notification.setAttribute('aria-live', 'assertive');
    notification.setAttribute('aria-atomic', 'true');

    // Conteúdo da notificação
    notification.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Fechar"></button>
        </div>
    `;

    // Adicionar ao container
    notificationContainer.appendChild(notification);

    // Inicializar e mostrar toast
    const toast = new bootstrap.Toast(notification, {
        autohide: true,
        delay: 5000
    });
    toast.show();

    // Remover após fechar
    notification.addEventListener('hidden.bs.toast', function() {
        notification.remove();
    });
}

// Traduzir função do usuário
function translateRole(role) {
    const roles = {
        'admin': 'Administrador',
        'manager': 'Gerente',
        'technician': 'Técnico',
        'operator': 'Operador',
        'user': 'Usuário'
    };

    return roles[role] || role;
}

// Traduzir status de manutenção
function translateStatus(status) {
    const statusMap = {
        'pending': 'Pendente',
        'in_progress': 'Em Andamento',
        'completed': 'Concluída',
        'canceled': 'Cancelada',
        'on_hold': 'Em Espera',
        'waiting_parts': 'Aguardando Peças',
        'waiting_approval': 'Aguardando Aprovação'
    };

    return statusMap[status] || status;
}

// Traduzir prioridade
function translatePriority(priority) {
    const priorityMap = {
        'baixa': 'Baixa',
        'media': 'Média',
        'alta': 'Alta',
        'critica': 'Crítica'
    };

    return priorityMap[priority] || priority;
}

// Obter cor do status
function getStatusColor(status) {
    const colorMap = {
        'pending': 'warning',
        'in_progress': 'info',
        'completed': 'success',
        'canceled': 'danger',
        'on_hold': 'secondary',
        'waiting_parts': 'primary',
        'waiting_approval': 'dark'
    };

    return colorMap[status] || 'light';
}

// Obter cor da prioridade
function getPriorityColor(priority) {
    const colorMap = {
        'baixa': 'success',
        'media': 'warning',
        'alta': 'danger',
        'critica': 'dark'
    };

    return colorMap[priority] || 'light';
}

// Função de debounce para limitar o número de chamadas
function debounce(func, wait = 300) {
    let timeout;
    return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}

// Função para obter um elemento do template
function getTemplateContent(id) {
    const template = document.getElementById(id);
    if (!template) return null;

    return template.content.cloneNode(true);
}

// Traduzir tipo de manutenção
function translateMaintenanceType(type) {
    const typeMap = {
        'corretiva': 'Corretiva',
        'preventiva': 'Preventiva',
        'instalacao': 'Instalação',
        'substituicao': 'Substituição'
    };

    return typeMap[type] || type;
}

// Calcular o tempo decorrido desde uma data
function timeAgo(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffSec = Math.round(diffMs / 1000);
    const diffMin = Math.round(diffSec / 60);
    const diffHour = Math.round(diffMin / 60);
    const diffDay = Math.round(diffHour / 24);

    if (diffSec < 60) {
        return `${diffSec} segundo${diffSec !== 1 ? 's' : ''} atrás`;
    } else if (diffMin < 60) {
        return `${diffMin} minuto${diffMin !== 1 ? 's' : ''} atrás`;
    } else if (diffHour < 24) {
        return `${diffHour} hora${diffHour !== 1 ? 's' : ''} atrás`;
    } else if (diffDay < 30) {
        return `${diffDay} dia${diffDay !== 1 ? 's' : ''} atrás`;
    } else {
        return formatDate(dateString);
    }
}

// Validar e-mail
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(String(email).toLowerCase());
}

// Copiar texto para a área de transferência
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text)
            .then(() => {
                showNotification('Texto copiado para a área de transferência!', 'success');
            })
            .catch(err => {
                console.error('Erro ao copiar texto:', err);
                showNotification('Não foi possível copiar o texto.', 'error');
            });
    } else {
        // Fallback para navegadores que não suportam clipboard API
        const textarea = document.createElement('textarea');
        textarea.value = text;
        textarea.style.position = 'fixed';
        document.body.appendChild(textarea);
        textarea.focus();
        textarea.select();

        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showNotification('Texto copiado para a área de transferência!', 'success');
            } else {
                showNotification('Não foi possível copiar o texto.', 'error');
            }
        } catch (err) {
            console.error('Erro ao copiar texto:', err);
            showNotification('Não foi possível copiar o texto.', 'error');
        }

        document.body.removeChild(textarea);
    }
}

// Carregar frases engraçadas para display LED
function loadFunnyQuotes() {
    return [
        "Manutenção preventiva é como abastecer premium: custa um pouco mais mas mantém o motor sempre funcionando!",
        "Equipamentos bem calibrados são como pneus equilibrados: garantem uma viagem tranquila e segura!",
        "Assim como uma troca de óleo no tempo certo, o sistema preventivo evita desgastes maiores!",
        "Postos Shell: abastecendo sonhos e mantendo seu negócio acelerando na direção certa!",
        "Uma bomba bem regulada é como um bom investimento: entrega exatamente o que você precisa!",
        "O combustível move veículos, a manutenção move nosso compromisso com a qualidade!",
        "Como dizia o velho frentista: melhor apertar um parafuso hoje do que empurrar o caminhão amanhã!",
        "Qual é o automóvel que você consegue beber? A Kombi-nação perfeita de Shell com guaraná!",
        "Por que o posto de gasolina foi ao psicólogo? Porque estava tendo problemas de auto-estima! 🤣",
        "Os números não mentem: manutenção em dia gera economia e prolonga a vida útil dos equipamentos!"
    ];
}

// Iniciar configuração na carregamento do documento
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar tooltips
    initTooltips();

    // Verificar autenticação
    checkAuthentication();

    // Configurar botões de logout
    setupLogoutButtons();

    // Destacar item de menu ativo
    highlightActiveMenuItem();

    // Configurar darkmode (se existir)
    setupDarkMode();
});

// Destacar item de menu ativo
function highlightActiveMenuItem() {
    const currentPath = window.location.pathname;
    const menuLinks = document.querySelectorAll('.menu-link, .submenu-link');

    menuLinks.forEach(link => {
        const href = link.getAttribute('href');

        if (href === currentPath ||
            (href !== '/' && currentPath.startsWith(href))) {
            link.classList.add('active');

            // Se for um submenu, abrir o item pai
            const parentItem = link.closest('.menu-item');
            if (parentItem && link.classList.contains('submenu-link')) {
                parentItem.classList.add('open');
            }
        }
    });
}

// Configurar modo escuro (se existir)
function setupDarkMode() {
    const darkModeToggle = document.getElementById('dark-mode-toggle');
    if (!darkModeToggle) return;

    // Verificar preferência salva
    const isDarkMode = localStorage.getItem('dark-mode') === 'true';

    // Aplicar modo escuro se necessário
    if (isDarkMode) {
        document.body.classList.add('dark-mode');
        darkModeToggle.checked = true;
    }

    // Configurar listener para alternar modo
    darkModeToggle.addEventListener('change', function() {
        if (this.checked) {
            document.body.classList.add('dark-mode');
            localStorage.setItem('dark-mode', 'true');
        } else {
            document.body.classList.remove('dark-mode');
            localStorage.setItem('dark-mode', 'false');
        }
    });
}