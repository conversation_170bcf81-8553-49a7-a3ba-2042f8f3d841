/**
 * Calendar Flip - Script para controlar o calendário com efeito de virada
 * Rede Tradição - Sistema de Manutenção Shell
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elementos do calendário flip
    const flipCard = document.getElementById('flip-card');
    const backToCalendarBtn = document.getElementById('back-to-calendar');

    // Elementos do calendário
    const calendarDays = document.getElementById('calendar-days');
    const calendarTitle = document.querySelector('.calendar-title');
    const prevMonthBtn = document.querySelector('.prev-month');
    const nextMonthBtn = document.querySelector('.next-month');

    // Modal de seleção de múltiplas ordens
    let orderSelectionModal;

    // Data atual (usando a data real do sistema)
    let currentDate = new Date();

    // Array para armazenar eventos/ordens reais do banco de dados
    const events = [];

    // Inicialização
    function init() {
        createSelectionModal();
        addCompactServiceBoxStyles();
        renderCalendar();
        setupEventListeners();
        setupServiceCardListeners();
    }

    // Adiciona estilos CSS para os cards de serviço compactos
    function addCompactServiceBoxStyles() {
        if (!document.getElementById('compact-service-styles')) {
            const style = document.createElement('style');
            style.id = 'compact-service-styles';
            style.textContent = `
                .compact-service-box {
                    background-color: rgba(0, 0, 0, 0.3);
                    border-radius: 10px;
                    padding: 15px 10px;
                    height: 100%;
                    transition: all 0.3s;
                    cursor: pointer;
                }
                .compact-service-box:hover {
                    background-color: rgba(30, 30, 30, 0.5);
                    transform: translateY(-3px);
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Configura event listeners para os cards de serviço
    function setupServiceCardListeners() {
        // Seleciona todos os cards de serviço
        const serviceCards = document.querySelectorAll('.service-box');

        // Adiciona event listeners para cada card
        serviceCards.forEach(card => {
            card.addEventListener('click', function() {
                // Obtém o ID da ordem a partir do atributo data-id
                const orderId = this.getAttribute('data-id');

                // Buscar detalhes da ordem via API
                if (orderId) {
                    showOrderDetails(parseInt(orderId));
                }
            });
        });

        // Configura listeners para cards compactos também
        const compactServiceBoxes = document.querySelectorAll('.compact-service-box');
        compactServiceBoxes.forEach(box => {
            box.addEventListener('click', function() {
                const boxId = this.getAttribute('data-id');
                const boxType = this.getAttribute('data-type');

                // Diferentes comportamentos baseados no tipo de card
                if (boxType === 'history') { // Histórico
                    showHistoryInfo(boxId);
                } else if (boxType === 'costs') { // Custos
                    showCostInfo(boxId);
                } else if (boxType === 'schedule') { // Cronograma
                    showTimelineInfo(boxId);
                } else {
                    // Comportamento legado para compatibilidade
                    if (boxId === '101') { // Histórico
                        showHistoryInfo();
                    } else if (boxId === '102') { // Custos
                        showCostInfo();
                    } else if (boxId === '103') { // Cronograma
                        showTimelineInfo();
                    }
                }
            });
        });
    }

    // Função para mostrar informações de histórico
    function showHistoryInfo() {
        const historyModal = document.createElement('div');
        historyModal.className = 'custom-modal';
        historyModal.innerHTML = `
            <div class="custom-modal-content">
                <div class="custom-modal-header">
                    <h4>Histórico da Ordem</h4>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="custom-modal-body">
                    <ul class="timeline">
                        <li class="timeline-item">
                            <div class="timeline-badge bg-success"><i class="fas fa-check"></i></div>
                            <div class="timeline-panel">
                                <div class="timeline-heading">
                                    <h5>Criação da Ordem</h5>
                                    <p><small class="text-muted"><i class="fas fa-clock"></i> 01/04/2025 - 08:30</small></p>
                                </div>
                                <div class="timeline-body">
                                    <p>Ordem de serviço criada por João Silva</p>
                                </div>
                            </div>
                        </li>
                        <li class="timeline-item">
                            <div class="timeline-badge bg-primary"><i class="fas fa-check"></i></div>
                            <div class="timeline-panel">
                                <div class="timeline-heading">
                                    <h5>Aprovação Técnica</h5>
                                    <p><small class="text-muted"><i class="fas fa-clock"></i> 02/04/2025 - 10:15</small></p>
                                </div>
                                <div class="timeline-body">
                                    <p>Aprovado por Carlos Mendes (Supervisor)</p>
                                </div>
                            </div>
                        </li>
                        <li class="timeline-item">
                            <div class="timeline-badge bg-warning"><i class="fas fa-cog"></i></div>
                            <div class="timeline-panel">
                                <div class="timeline-heading">
                                    <h5>Em Andamento</h5>
                                    <p><small class="text-muted"><i class="fas fa-clock"></i> 03/04/2025 - 09:45</small></p>
                                </div>
                                <div class="timeline-body">
                                    <p>Equipe técnica iniciou o serviço</p>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        `;

        document.body.appendChild(historyModal);

        // Estilo para o modal e o timeline
        const style = document.createElement('style');
        style.textContent = `
            .custom-modal {
                position: fixed;
                z-index: 1000;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0,0,0,0.7);
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .custom-modal-content {
                background-color: #222;
                color: #fff;
                width: 80%;
                max-width: 600px;
                border-radius: 8px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.5);
            }
            .custom-modal-header {
                padding: 15px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1px solid #444;
            }
            .close-btn {
                background: none;
                border: none;
                font-size: 24px;
                color: #fff;
                cursor: pointer;
            }
            .custom-modal-body {
                padding: 20px;
                max-height: 70vh;
                overflow-y: auto;
            }
            .timeline {
                position: relative;
                padding: 20px 0;
                list-style: none;
                max-width: 1200px;
                margin: 0 auto;
            }
            .timeline:before {
                content: " ";
                position: absolute;
                top: 0;
                bottom: 0;
                left: 50px;
                width: 3px;
                background-color: #333;
            }
            .timeline-item {
                margin-bottom: 20px;
                position: relative;
            }
            .timeline-badge {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                text-align: center;
                position: absolute;
                left: 32px;
                margin-left: -25px;
                z-index: 100;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .timeline-panel {
                width: calc(100% - 90px);
                float: right;
                border: 1px solid #333;
                border-radius: 8px;
                padding: 15px;
                position: relative;
                background: #222;
            }
            .timeline-panel:before {
                position: absolute;
                top: 10px;
                left: -15px;
                display: inline-block;
                border-top: 15px solid transparent;
                border-right: 15px solid #333;
                border-bottom: 15px solid transparent;
                content: " ";
            }
        `;
        document.head.appendChild(style);

        // Adiciona evento para fechar o modal
        const closeBtn = historyModal.querySelector('.close-btn');
        closeBtn.addEventListener('click', function() {
            document.body.removeChild(historyModal);
        });

        // Fecha o modal se clicar fora do conteúdo
        historyModal.addEventListener('click', function(event) {
            if (event.target === historyModal) {
                document.body.removeChild(historyModal);
            }
        });
    }

    // Função para mostrar informações de custos
    function showCostInfo() {
        const costModal = document.createElement('div');
        costModal.className = 'custom-modal';
        costModal.innerHTML = `
            <div class="custom-modal-content">
                <div class="custom-modal-header">
                    <h4>Detalhamento de Custos</h4>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="custom-modal-body">
                    <div class="cost-summary mb-4">
                        <h5 class="text-warning">Resumo de Gastos</h5>
                        <div class="row mb-3">
                            <div class="col-8">Valor Total:</div>
                            <div class="col-4 text-end text-warning fw-bold">R$ 1.042,50</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-8">Status de Pagamento:</div>
                            <div class="col-4 text-end"><span class="badge bg-warning text-dark">Pendente</span></div>
                        </div>
                    </div>

                    <div class="cost-details">
                        <h5 class="text-warning">Itens</h5>
                        <table class="table table-dark table-hover">
                            <thead>
                                <tr>
                                    <th>Descrição</th>
                                    <th>Qtd</th>
                                    <th class="text-end">Valor</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Mão de obra técnica</td>
                                    <td>5h</td>
                                    <td class="text-end">R$ 375,00</td>
                                </tr>
                                <tr>
                                    <td>Peças de reposição</td>
                                    <td>3</td>
                                    <td class="text-end">R$ 487,50</td>
                                </tr>
                                <tr>
                                    <td>Deslocamento</td>
                                    <td>1</td>
                                    <td class="text-end">R$ 180,00</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4 d-flex justify-content-end">
                        <button class="shell-btn me-2"><i class="fas fa-file-invoice"></i> Gerar Fatura</button>
                        <button class="shell-btn shell-btn-success"><i class="fas fa-check-circle"></i> Aprovar Custos</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(costModal);

        // Adiciona evento para fechar o modal
        const closeBtn = costModal.querySelector('.close-btn');
        closeBtn.addEventListener('click', function() {
            document.body.removeChild(costModal);
        });

        // Fecha o modal se clicar fora do conteúdo
        costModal.addEventListener('click', function(event) {
            if (event.target === costModal) {
                document.body.removeChild(costModal);
            }
        });
    }

    // Função para mostrar informações de cronograma
    function showTimelineInfo() {
        const timelineModal = document.createElement('div');
        timelineModal.className = 'custom-modal';
        timelineModal.innerHTML = `
            <div class="custom-modal-content">
                <div class="custom-modal-header">
                    <h4>Cronograma da Ordem</h4>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="custom-modal-body">
                    <div class="timeline-info mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h5 class="text-warning mb-0">Manutenção Preventiva</h5>
                                <p class="text-muted small">Cronograma de Execução</p>
                            </div>
                            <span class="badge bg-success">Em andamento</span>
                        </div>
                    </div>

                    <div class="schedule-item">
                        <div class="d-flex p-3 mb-3" style="background-color: rgba(0,0,0,0.2); border-radius: 8px;">
                            <div class="me-3">
                                <div class="schedule-time">
                                    <h5 class="text-success mb-0">14:00h</h5>
                                    <p class="text-muted small mb-0">Início</p>
                                </div>
                            </div>
                            <div>
                                <h6>Início dos Trabalhos</h6>
                                <p class="mb-0">Chegada da equipe técnica e isolamento da área de trabalho</p>
                            </div>
                        </div>

                        <div class="schedule-connector">
                            <div class="connector-line"></div>
                        </div>

                        <div class="d-flex p-3 mb-3" style="background-color: rgba(0,0,0,0.2); border-radius: 8px;">
                            <div class="me-3">
                                <div class="schedule-time">
                                    <h5 class="text-primary mb-0">15:30h</h5>
                                    <p class="text-muted small mb-0">Em progresso</p>
                                </div>
                            </div>
                            <div>
                                <h6>Manutenção das Bombas</h6>
                                <p class="mb-0">Substituição de componentes e calibração dos sistemas</p>
                            </div>
                        </div>

                        <div class="schedule-connector">
                            <div class="connector-line"></div>
                        </div>

                        <div class="d-flex p-3 mb-3" style="background-color: rgba(0,0,0,0.2); border-radius: 8px;">
                            <div class="me-3">
                                <div class="schedule-time">
                                    <h5 class="text-danger mb-0">16:00h</h5>
                                    <p class="text-muted small mb-0">Pendente</p>
                                </div>
                            </div>
                            <div>
                                <h6>Testes e Finalização</h6>
                                <p class="mb-0">Testes operacionais e liberação dos equipamentos</p>
                            </div>
                        </div>
                    </div>

                    <style>
                        .schedule-connector {
                            position: relative;
                            height: 30px;
                            margin-left: 25px;
                        }
                        .connector-line {
                            position: absolute;
                            top: 0;
                            bottom: 0;
                            left: 0;
                            width: 2px;
                            background-color: #444;
                        }
                    </style>
                </div>
            </div>
        `;

        document.body.appendChild(timelineModal);

        // Adiciona evento para fechar o modal
        const closeBtn = timelineModal.querySelector('.close-btn');
        closeBtn.addEventListener('click', function() {
            document.body.removeChild(timelineModal);
        });

        // Fecha o modal se clicar fora do conteúdo
        timelineModal.addEventListener('click', function(event) {
            if (event.target === timelineModal) {
                document.body.removeChild(timelineModal);
            }
        });
    }

    // Configurar ouvintes de eventos
    function setupEventListeners() {
        // Botões de navegação do calendário
        prevMonthBtn.addEventListener('click', () => {
            currentDate.setMonth(currentDate.getMonth() - 1);
            renderCalendar();
        });

        nextMonthBtn.addEventListener('click', () => {
            currentDate.setMonth(currentDate.getMonth() + 1);
            renderCalendar();
        });

        // Botão para voltar ao calendário (face frontal)
        if (backToCalendarBtn) {
            backToCalendarBtn.addEventListener('click', function() {
                flipCard.classList.remove('flipped');
            });
        }
    }

    // Renderizar o calendário
    function renderCalendar() {
        const year = currentDate.getFullYear();
        const month = currentDate.getMonth();

        // Atualiza o título do calendário
        const monthName = getMonthName(month);
        calendarTitle.textContent = `${monthName} ${year}`;

        // Limpa os dias existentes
        calendarDays.innerHTML = '';

        // Determina o primeiro dia do mês
        const firstDay = new Date(year, month, 1);
        const startingDay = firstDay.getDay(); // 0 = Domingo, 1 = Segunda, etc.

        // Determina o último dia do mês
        const lastDay = new Date(year, month + 1, 0);
        const totalDays = lastDay.getDate();

        // Obtém a data de hoje
        const today = new Date();

        // Adiciona os dias vazios do início do mês
        for (let i = 0; i < startingDay; i++) {
            const emptyDay = document.createElement('div');
            emptyDay.className = 'calendar-day empty';
            calendarDays.appendChild(emptyDay);
        }

        // Adiciona os dias do mês
        for (let day = 1; day <= totalDays; day++) {
            const dayElement = document.createElement('div');
            dayElement.className = 'calendar-day';
            dayElement.textContent = day;

            // Verifica se é o dia atual
            if (today.getDate() === day && today.getMonth() === month && today.getFullYear() === year) {
                dayElement.classList.add('today');
            }

            // Adicionamos evento de clique para todos os dias
            dayElement.addEventListener('click', function() {
                // Buscar ordens para esta data específica
                const clickedDate = new Date(year, month, day);
                const formattedDate = `${year}-${(month + 1).toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;

                // Buscar ordens para esta data via API
                fetch(`/api/ordens/tecnico?date=${formattedDate}`)
                    .then(response => response.json())
                    .then(response => {
                        if (response.success && response.data && response.data.length > 0) {
                            // Se houver apenas uma ordem, mostrar detalhes
                            if (response.data.length === 1) {
                                const order = response.data[0];
                                showOrderDetails(order);
                            } else {
                                // Se houver múltiplas ordens, mostrar modal de seleção
                                showOrderSelectionModal(response.data, day, monthName);
                            }
                        } else {
                            // Se não houver ordens, mostrar notificação
                            alert(`Não há ordens para ${day}/${month + 1}/${year}`);
                        }
                    })
                    .catch(error => {
                        console.error('Erro ao buscar ordens:', error);
                        alert('Erro ao buscar ordens: ' + error.message);
                    });
            });

            calendarDays.appendChild(dayElement);
        }

        // Após renderizar o calendário, configura os listeners para todos os cards de serviço
        setTimeout(setupServiceCardListeners, 100);
    }

    // Criar o modal de seleção de ordens
    function createSelectionModal() {
        // Verifica se o modal já existe
        if (document.getElementById('order-selection-modal')) {
            return;
        }

        // Cria o modal
        const modal = document.createElement('div');
        modal.id = 'order-selection-modal';
        modal.className = 'order-selection-modal';

        modal.innerHTML = `
            <div class="order-selection-content">
                <div class="order-selection-header">
                    <h3>Selecione uma Ordem</h3>
                    <button class="close-modal-btn">&times;</button>
                </div>
                <div class="order-selection-list" id="order-selection-list">
                    <!-- Lista de ordens será preenchida dinamicamente -->
                </div>
            </div>
        `;

        // Adiciona o modal ao corpo do documento
        document.body.appendChild(modal);

        // Adiciona evento para fechar o modal
        const closeBtn = modal.querySelector('.close-modal-btn');
        closeBtn.addEventListener('click', function() {
            modal.classList.remove('active');
        });

        // Fecha o modal se clicar fora do conteúdo
        modal.addEventListener('click', function(event) {
            if (event.target === modal) {
                modal.classList.remove('active');
            }
        });

        // Salva a referência do modal
        orderSelectionModal = modal;
    }

    // Mostrar modal de seleção de ordens
    function showOrderSelectionModal(orders, day, monthName) {
        const listContainer = document.getElementById('order-selection-list');
        listContainer.innerHTML = '';

        document.querySelector('#order-selection-modal .order-selection-header h3')
            .textContent = `Ordens de Serviço - ${day} de ${monthName}`;

        // Adiciona cada ordem à lista
        orders.forEach(order => {
            const orderItem = document.createElement('div');
            orderItem.className = 'order-selection-item';

            // Determinar a classe e texto da prioridade com base no valor da ordem
            let priorityClass = 'bg-info';
            let priorityText = 'Normal';

            if (order.priority === 'high' || order.priority === 'urgent' || order.priority === 'alta') {
                priorityClass = 'bg-danger';
                priorityText = 'Urgente';
            } else if (order.priority === 'medium' || order.priority === 'media') {
                priorityClass = 'bg-warning text-dark';
                priorityText = 'Média';
            }

            const priorityBadge = `<span class="badge ${priorityClass}">${order.priority_display || priorityText}</span>`;
            const title = order.title || order.problem || 'Ordem de Serviço';
            const location = order.branch_name || order.filial || 'Local não especificado';
            const id = order.id || order.number || 'N/A';

            orderItem.innerHTML = `
                <h4>${title} ${priorityBadge}</h4>
                <p><i class="fas fa-map-marker-alt"></i> ${location}</p>
                <p><i class="fas fa-hashtag"></i> Ordem #${id}</p>
            `;

            // Adiciona evento de clique para selecionar a ordem
            orderItem.addEventListener('click', function() {
                orderSelectionModal.classList.remove('active');
                showOrderDetails(order);
            });

            listContainer.appendChild(orderItem);
        });

        // Exibe o modal
        orderSelectionModal.classList.add('active');

        // Atualiza event listeners para todos os cards de serviço
        setupServiceCardListeners();
    }

    // Mostrar detalhes da ordem selecionada
    function showOrderDetails(order) {
        // Verificar se a ordem é a ordem #18 (hardcoded) e bloqueá-la
        if ((typeof order === 'number' && order === 18) ||
            (typeof order === 'object' && order.id && (order.id === 18 || order.id === "18"))) {
            console.error('Tentativa de mostrar a ordem #18 que é inválida/hardcoded');
            alert('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
            return;
        }

        // Se recebemos um ID em vez de um objeto completo, buscar os detalhes da ordem
        if (typeof order === 'number' || (typeof order === 'object' && !order.title && order.id)) {
            const orderId = typeof order === 'number' ? order : order.id;

            // Verificar novamente se o ID é 18 e bloqueá-lo
            if (orderId === 18 || orderId === "18") {
                console.error('Tentativa de buscar a ordem #18 que é inválida/hardcoded');
                alert('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
                return;
            }

            // Buscar detalhes da ordem via API
            fetch(`/api/ordens/${orderId}`)
                .then(response => response.json())
                .then(response => {
                    if (response.success && response.data) {
                        // Verificar se a ordem retornada é a ordem #18
                        if (response.data.id === 18 || response.data.id === "18") {
                            console.error('API retornou a ordem #18 que é inválida/hardcoded');
                            alert('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
                            return;
                        }
                        showOrderDetails(response.data);
                    } else {
                        alert('Erro ao buscar detalhes da ordem: ' + (response.message || 'Erro desconhecido'));
                    }
                })
                .catch(error => {
                    console.error('Erro ao buscar detalhes da ordem:', error);
                    alert('Erro ao buscar detalhes da ordem: ' + error.message);
                });
            return;
        }

        // Atualiza o título da ordem
        const orderTitle = document.getElementById('order-title');
        if (orderTitle) {
            orderTitle.textContent = order.title || order.problem || 'Ordem de Serviço';
        }

        // Detalhes da ordem
        const orderDetailsContent = document.getElementById('order-details-content');
        if (orderDetailsContent) {
            // Formata os detalhes de acordo com a prioridade da ordem
            let priorityClass = 'bg-info';
            let priorityText = 'Normal';

            // Determinar a classe e texto da prioridade com base no valor da ordem
            if (order.priority === 'high' || order.priority === 'urgent' || order.priority === 'alta') {
                priorityClass = 'bg-danger';
                priorityText = 'Urgente';
            } else if (order.priority === 'medium' || order.priority === 'media') {
                priorityClass = 'bg-warning text-dark';
                priorityText = 'Média Prioridade';
            }

            // Formatar a data
            let formattedDate = 'Data não disponível';
            if (order.due_date) {
                const dueDate = new Date(order.due_date);
                formattedDate = `${dueDate.getDate()}/${dueDate.getMonth() + 1}/${dueDate.getFullYear()}`;
            } else if (order.data) {
                const dueDate = new Date(order.data);
                formattedDate = `${dueDate.getDate()}/${dueDate.getMonth() + 1}/${dueDate.getFullYear()}`;
            }

            orderDetailsContent.innerHTML = `
                <div class="text-center mb-3">
                    <h4 class="text-warning mb-2">${order.title || order.problem || 'Ordem de Serviço'}</h4>
                    <p class="text-muted small">Detalhes da Manutenção</p>
                </div>

                <div class="d-flex flex-column align-items-center">
                    <div class="d-flex flex-row justify-content-center mb-3">
                        <span class="badge ${priorityClass} mx-1">${order.priority_display || priorityText}</span>
                        <span class="badge bg-secondary mx-1">Ordem #${order.id || order.number || 'N/A'}</span>
                    </div>

                    <div class="row w-100 justify-content-center mb-3">
                        <div class="col-sm-5 mb-2">
                            <div class="text-center">
                                <p class="mb-1"><i class="fas fa-map-marker-alt text-danger"></i> Local</p>
                                <p class="text-white">${order.branch_name || order.filial || 'Local não especificado'}</p>
                            </div>
                        </div>
                        <div class="col-sm-5 mb-2">
                            <div class="text-center">
                                <p class="mb-1"><i class="fas fa-calendar-alt text-primary"></i> Data</p>
                                <p class="text-white">${formattedDate}</p>
                            </div>
                        </div>
                    </div>

                    <div class="row w-100 justify-content-center mb-3">
                        <div class="col-sm-5 mb-2">
                            <div class="text-center">
                                <p class="mb-1"><i class="fas fa-tools text-warning"></i> Equipamento</p>
                                <p class="text-white">${order.equipment_name || order.equipamento || 'Equipamento não especificado'}</p>
                            </div>
                        </div>
                        <div class="col-sm-5 mb-2">
                            <div class="text-center">
                                <p class="mb-1"><i class="fas fa-user text-info"></i> Responsável</p>
                                <p class="text-white">${order.technician_name || order.responsavel || 'Não atribuído'}</p>
                            </div>
                        </div>
                    </div>

                    <div class="row w-100 justify-content-center mb-3">
                        <div class="col-sm-5 mb-2">
                            <div class="text-center">
                                <p class="mb-1"><i class="fas fa-clock text-success"></i> Tempo Estimado</p>
                                <p class="text-white">${order.estimated_time || order.tempo || '2 horas'}</p>
                            </div>
                        </div>
                        <div class="col-sm-5 mb-2">
                            <div class="text-center">
                                <p class="mb-1"><i class="fas fa-calendar-check text-warning"></i> Status</p>
                                <p class="text-white">${order.status_display || order.status || 'Pendente'}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-center mt-2 mb-3">
                    <button class="shell-btn mx-1" onclick="printOrderDetails()"><i class="fas fa-print"></i> Imprimir</button>
                    <button class="shell-btn shell-btn-success mx-1" onclick="approveOrder()"><i class="fas fa-check-circle"></i> Aprovar</button>
                    <button class="shell-btn shell-btn-danger mx-1" onclick="rejectOrder()"><i class="fas fa-times-circle"></i> Reprovar</button>
                </div>

                <div class="row mt-3 justify-content-center">
                    <div class="col-sm-3 mb-2">
                        <div class="compact-service-box text-center" data-id="${order.id}" data-type="history">
                            <i class="fas fa-history text-warning fa-2x mb-2"></i>
                            <h6 class="mb-1">Histórico</h6>
                            <small class="text-muted">Clique para ver</small>
                        </div>
                    </div>
                    <div class="col-sm-3 mb-2">
                        <div class="compact-service-box text-center" data-id="${order.id}" data-type="costs">
                            <i class="fas fa-file-invoice-dollar text-warning fa-2x mb-2"></i>
                            <h6 class="mb-1">Custos</h6>
                            <div class="text-warning fw-bold">${order.custos && order.custos.total ? 'R$ ' + order.custos.total.toFixed(2) : 'N/A'}</div>
                        </div>
                    </div>
                    <div class="col-sm-3 mb-2">
                        <div class="compact-service-box text-center" data-id="${order.id}" data-type="schedule">
                            <i class="fas fa-calendar-check text-warning fa-2x mb-2"></i>
                            <h6 class="mb-1">Cronograma</h6>
                            <small class="text-success">${order.cronograma && order.cronograma.data_inicio ? order.cronograma.data_inicio + ' - ' + order.cronograma.hora_inicio : 'Não definido'}</small>
                            <small class="text-danger d-block">${order.cronograma && order.cronograma.data_fim ? order.cronograma.data_fim + ' - ' + order.cronograma.hora_fim : 'Não definido'}</small>
                        </div>
                    </div>
                </div>
            `;
        }

        // Faz o flip do cartão para mostrar os detalhes, sem modificar a orientação
        flipCard.classList.add('flipped');

        // Garante que todos os cards terão event listeners
        setTimeout(setupServiceCardListeners, 300); // Pequeno delay para garantir que o DOM foi atualizado
    }

    // Função auxiliar para obter o nome do mês
    function getMonthName(month) {
        const months = [
            'Janeiro', 'Fevereiro', 'Março', 'Abril',
            'Maio', 'Junho', 'Julho', 'Agosto',
            'Setembro', 'Outubro', 'Novembro', 'Dezembro'
        ];
        return months[month];
    }

    // Função para imprimir detalhes da ordem
    window.printOrderDetails = function() {
        const orderDetails = document.getElementById('order-details-content').innerHTML;
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
            <head>
                <title>Detalhes da Ordem de Serviço</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                <link href="/static/css/styles.css" rel="stylesheet">
                <style>
                    body { padding: 20px; font-family: Arial, sans-serif; }
                    .print-header { text-align: center; margin-bottom: 20px; }
                    .print-footer { text-align: center; margin-top: 50px; font-size: 12px; color: #777; }
                    @media print {
                        .no-print { display: none; }
                    }
                </style>
            </head>
            <body>
                <div class="print-header">
                    <img src="/static/img/logo.png" alt="Logo Shell Tradição" height="60">
                    <h2 class="mt-3">Ordem de Serviço</h2>
                </div>
                <div class="print-content">
                    ${orderDetails}
                </div>
                <div class="print-footer">
                    <p>Sistema de Manutenção - Shell Rede Tradição</p>
                    <p>Impresso em ${new Date().toLocaleString()}</p>
                </div>
                <div class="no-print mt-4 text-center">
                    <button class="btn btn-primary" onclick="window.print()">Imprimir</button>
                    <button class="btn btn-secondary" onclick="window.close()">Fechar</button>
                </div>
            </body>
            </html>
        `);
        printWindow.document.close();
        setTimeout(() => {
            printWindow.focus();
        }, 500);
    };

    // Função para aprovar ordem
    window.approveOrder = function() {
        // Verificar se a ordem atual é a #18
        const orderIdElement = document.querySelector('.badge.bg-secondary');
        if (orderIdElement && orderIdElement.textContent) {
            const orderIdText = orderIdElement.textContent;
            const orderId = orderIdText.replace(/[^\d]/g, ''); // Extrai apenas os números

            if (orderId === "18") {
                console.error('Tentativa de aprovar a ordem #18 que é inválida/hardcoded');
                alert('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
                return;
            }
        }

        if (confirm('Confirma a aprovação desta ordem de serviço?')) {
            alert('Ordem de serviço aprovada com sucesso!');
            // Aqui você pode adicionar a lógica para enviar a aprovação para o servidor
        }
    };

    // Função para reprovar ordem
    window.rejectOrder = function() {
        // Verificar se a ordem atual é a #18
        const orderIdElement = document.querySelector('.badge.bg-secondary');
        if (orderIdElement && orderIdElement.textContent) {
            const orderIdText = orderIdElement.textContent;
            const orderId = orderIdText.replace(/[^\d]/g, ''); // Extrai apenas os números

            if (orderId === "18") {
                console.error('Tentativa de reprovar a ordem #18 que é inválida/hardcoded');
                alert('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
                return;
            }
        }

        const reason = prompt('Por favor, informe o motivo da reprovação:');
        if (reason) {
            alert('Ordem de serviço reprovada. Motivo: ' + reason);
            // Aqui você pode adicionar a lógica para enviar a reprovação para o servidor
        }
    };

    // Inicializa o componente
    init();
});