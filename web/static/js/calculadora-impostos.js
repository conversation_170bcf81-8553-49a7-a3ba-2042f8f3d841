/**
 * Script para gerenciar a interface da calculadora de impostos
 */
document.addEventListener('DOMContentLoaded', function() {
    // Elementos do DOM
    const form = document.getElementById('calculadora-impostos-form');
    const estadoSelect = document.getElementById('estado');
    const combustivelSelect = document.getElementById('combustivel');
    const valorBaseInput = document.getElementById('valor-base');
    const mensagemErro = document.getElementById('mensagem-erro');
    const resultadosContainer = document.getElementById('resultados');
    const tabelaResultados = document.getElementById('tabela-resultados');
    const totalImpostos = document.getElementById('total-impostos');
    const valorFinal = document.getElementById('valor-final');
    
    // Inicializar calculadora
    const calculadora = new CalculadoraImpostos();
    
    // Inicializar interface
    inicializarInterface();
    
    /**
     * Inicializa a interface do usuário
     */
    function inicializarInterface() {
        // Preencher seletores
        preencherEstados();
        preencherCombustiveis();
        
        // Adicionar listeners de eventos
        form.addEventListener('submit', calcularImpostos);
        valorBaseInput.addEventListener('input', formatarValorBase);
    }
    
    /**
     * Preenche o seletor de estados
     */
    function preencherEstados() {
        const estados = calculadora.getEstadosDisponiveis();
        
        estados.forEach(estado => {
            const option = document.createElement('option');
            option.value = estado;
            option.textContent = estado;
            estadoSelect.appendChild(option);
        });
    }
    
    /**
     * Preenche o seletor de combustíveis
     */
    function preencherCombustiveis() {
        const combustiveis = calculadora.getTiposCombustiveisDisponiveis();
        
        combustiveis.forEach(combustivel => {
            const option = document.createElement('option');
            option.value = combustivel.id;
            option.textContent = combustivel.nome;
            combustivelSelect.appendChild(option);
        });
    }
    
    /**
     * Formata o valor base como moeda durante a digitação
     */
    function formatarValorBase() {
        const valor = valorBaseInput.value.replace(/\D/g, '');
        
        if (valor === '') {
            valorBaseInput.value = '';
            return;
        }
        
        const valorNumerico = parseFloat(valor) / 100;
        valorBaseInput.value = formatarMoeda(valorNumerico);
    }
    
    /**
     * Formata um valor como moeda brasileira
     * @param {number} valor - Valor a ser formatado
     * @returns {string} - Valor formatado como moeda
     */
    function formatarMoeda(valor) {
        return valor.toLocaleString('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).replace(/^R\$\s+/, 'R$ ');
    }
    
    /**
     * Formata um valor como percentual
     * @param {number} valor - Valor a ser formatado
     * @returns {string} - Valor formatado como percentual
     */
    function formatarPercentual(valor) {
        return valor.toLocaleString('pt-BR', {
            style: 'percent',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }
    
    /**
     * Calcula os impostos com base nos valores do formulário
     * @param {Event} event - Evento de submit do formulário
     */
    function calcularImpostos(event) {
        event.preventDefault();
        
        // Validar campos
        if (!validarFormulario()) {
            return;
        }
        
        // Obter valores do formulário
        const estado = estadoSelect.value;
        const combustivel = combustivelSelect.value;
        const valorBaseStr = valorBaseInput.value.replace(/\D/g, '');
        const valorBase = parseFloat(valorBaseStr) / 100;
        
        try {
            // Calcular impostos
            const resultadoCalculo = calculadora.calcularImpostos(combustivel, estado, valorBase);
            
            // Exibir resultados
            exibirResultados(resultadoCalculo, valorBase);
        } catch (error) {
            mostrarErro(error.message);
        }
    }
    
    /**
     * Valida o formulário antes do cálculo
     * @returns {boolean} - Indica se o formulário está válido
     */
    function validarFormulario() {
        ocultarErro();
        
        if (!estadoSelect.value) {
            mostrarErro('Por favor, selecione um estado.');
            return false;
        }
        
        if (!combustivelSelect.value) {
            mostrarErro('Por favor, selecione um tipo de combustível.');
            return false;
        }
        
        if (!valorBaseInput.value) {
            mostrarErro('Por favor, informe o valor base para cálculo.');
            return false;
        }
        
        return true;
    }
    
    /**
     * Exibe uma mensagem de erro
     * @param {string} mensagem - Mensagem de erro a ser exibida
     */
    function mostrarErro(mensagem) {
        mensagemErro.textContent = mensagem;
        mensagemErro.classList.remove('d-none');
    }
    
    /**
     * Oculta a mensagem de erro
     */
    function ocultarErro() {
        mensagemErro.textContent = '';
        mensagemErro.classList.add('d-none');
    }
    
    /**
     * Exibe os resultados do cálculo de impostos
     * @param {Object} resultados - Resultados do cálculo de impostos
     * @param {number} valorBase - Valor base utilizado no cálculo
     */
    function exibirResultados(resultados, valorBase) {
        // Limpar tabela de resultados
        tabelaResultados.innerHTML = '';
        
        // Total de impostos
        let somaImpostos = 0;
        
        // Adicionar cada imposto à tabela
        Object.entries(resultados).forEach(([imposto, dados]) => {
            if (imposto !== 'total') {
                // Criar linha da tabela
                const tr = document.createElement('tr');
                
                // Coluna: Nome do imposto
                const tdImposto = document.createElement('td');
                tdImposto.textContent = formatarNomeImposto(imposto);
                tr.appendChild(tdImposto);
                
                // Coluna: Alíquota
                const tdAliquota = document.createElement('td');
                tdAliquota.textContent = formatarPercentual(dados.percentual / 100);
                tr.appendChild(tdAliquota);
                
                // Coluna: Valor
                const tdValor = document.createElement('td');
                tdValor.textContent = formatarMoeda(dados.valor);
                tr.appendChild(tdValor);
                
                // Adicionar linha à tabela
                tabelaResultados.appendChild(tr);
                
                // Somar ao total de impostos
                somaImpostos += dados.valor;
            }
        });
        
        // Atualizar total de impostos e valor final
        totalImpostos.textContent = formatarMoeda(somaImpostos);
        valorFinal.textContent = formatarMoeda(valorBase + somaImpostos);
        
        // Exibir container de resultados
        resultadosContainer.classList.remove('d-none');
    }
    
    /**
     * Formata o nome do imposto para exibição
     * @param {string} imposto - Chave do imposto
     * @returns {string} - Nome formatado para exibição
     */
    function formatarNomeImposto(imposto) {
        const nomesImpostos = {
            'cide': 'CIDE',
            'pisCofinsProduto': 'PIS/COFINS (Produto)',
            'pisCofinsAeac': 'PIS/COFINS (AEAC)',
            'pisCofinsProd': 'PIS/COFINS (Produto)',
            'pisCofinsDistr': 'PIS/COFINS (Distribuição)',
            'pisCofinsEtanol': 'PIS/COFINS (Etanol)',
            'pisCofinsDiesel': 'PIS/COFINS (Diesel)',
            'pisCofinsBio': 'PIS/COFINS (Biodiesel)',
            'mistura': 'Mistura',
            'cidePisCof': 'CIDE + PIS/COFINS',
            'icms': 'ICMS',
            'pmpf': 'PMPF',
            'icmsTotal': 'ICMS Total'
        };
        
        return nomesImpostos[imposto] || imposto.charAt(0).toUpperCase() + imposto.slice(1).replace(/([A-Z])/g, ' $1');
    }
}); 