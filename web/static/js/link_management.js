/**
 * Script para a página de Gerenciamento de Relações
 * Este arquivo contém todas as funcionalidades JavaScript para a página de gerenciamento
 * de relações entre filiais, prestadoras e técnicos.
 */

// Variáveis globais para armazenar dados
let allBranches = [];
let allProviders = [];
let allTechnicians = [];
let allSpecialties = [];
let providerBranchLinks = [];
let technicianBranchLinks = [];

// Inicialização quando o documento estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar componentes
    initTabs();

    // Carregar dados iniciais
    loadInitialData();

    // Configurar listeners de eventos
    setupEventListeners();
});

/**
 * Inicializa as abas da página
 */
function initTabs() {
    // Usar o Bootstrap Tabs API que já está incluído
    // Não precisamos implementar manualmente, pois o Bootstrap já faz isso
    // Apenas garantimos que a primeira aba esteja ativa ao carregar a página

    // Verificar se já existe uma instância de Tab do Bootstrap
    const firstTab = document.querySelector('#provider-branch-tab');
    if (firstTab) {
        // Garantir que a primeira aba esteja ativa
        const tab = new bootstrap.Tab(firstTab);
        tab.show();
    }

    // Adicionar evento para carregar o conteúdo correto quando a aba for alterada
    const tabs = document.querySelectorAll('button[data-bs-toggle="tab"]');
    tabs.forEach(tab => {
        tab.addEventListener('shown.bs.tab', function (event) {
            const targetId = event.target.getAttribute('data-bs-target').replace('#', '');

            // Renderizar o conteúdo apropriado com base na aba selecionada
            if (targetId === 'provider-branch') {
                renderProviderBranchTable();
            } else if (targetId === 'technician-branch') {
                renderTechnicianBranchTable();
            } else if (targetId === 'provider-technician') {
                renderProviderTechnicianTable();
            }
        });
    });
}

/**
 * Carrega os dados iniciais necessários para a página
 */
async function loadInitialData() {
    try {
        // Mostrar indicador de carregamento
        showLoading(true);
        console.log("Iniciando carregamento de dados...");

        // Carregar filiais
        try {
            console.log("Carregando filiais...");
            const branchesResponse = await fetch('/api/branches');
            console.log("Resposta de filiais:", branchesResponse.status);

            if (branchesResponse.ok) {
                allBranches = await branchesResponse.json();
                console.log(`${allBranches.length} filiais carregadas com sucesso`);
            } else {
                console.error('Erro ao carregar filiais:', branchesResponse.status);
                const errorText = await branchesResponse.text();
                console.error('Detalhes do erro:', errorText);
                allBranches = [];
                showErrorMessage('Erro ao carregar filiais. Algumas funcionalidades podem estar limitadas.');
            }
        } catch (error) {
            console.error('Exceção ao carregar filiais:', error);
            allBranches = [];
            showErrorMessage('Erro ao carregar filiais. Algumas funcionalidades podem estar limitadas.');
        }

        // Carregar prestadoras
        try {
            console.log("Carregando prestadoras...");
            const providersResponse = await fetch('/api/providers');
            console.log("Resposta de prestadoras:", providersResponse.status);

            if (providersResponse.ok) {
                allProviders = await providersResponse.json();
                console.log(`${allProviders.length} prestadoras carregadas com sucesso`);

                // Verificar se há prestadoras disponíveis
                if (allProviders.length === 0) {
                    console.warn('Nenhuma prestadora encontrada no sistema');
                } else {
                    console.log('Primeira prestadora:', allProviders[0]);
                }
            } else {
                console.error('Erro ao carregar prestadoras:', providersResponse.status);
                const errorText = await providersResponse.text();
                console.error('Detalhes do erro:', errorText);
                allProviders = [];
                showErrorMessage('Erro ao carregar prestadoras. Algumas funcionalidades podem estar limitadas.');
            }
        } catch (error) {
            console.error('Exceção ao carregar prestadoras:', error);
            allProviders = [];
            showErrorMessage('Erro ao carregar prestadoras. Algumas funcionalidades podem estar limitadas.');
        }

        // Carregar técnicos
        try {
            console.log("Carregando técnicos...");
            const techniciansResponse = await fetch('/api/technicians');
            console.log("Resposta de técnicos:", techniciansResponse.status);

            if (techniciansResponse.ok) {
                allTechnicians = await techniciansResponse.json();
                console.log(`${allTechnicians.length} técnicos carregados com sucesso`);

                // Verificar se há técnicos disponíveis
                if (allTechnicians.length === 0) {
                    console.warn('Nenhum técnico encontrado no sistema');
                } else {
                    console.log('Primeiro técnico:', allTechnicians[0]);
                }
            } else {
                console.error('Erro ao carregar técnicos:', techniciansResponse.status);
                const errorText = await techniciansResponse.text();
                console.error('Detalhes do erro:', errorText);
                allTechnicians = [];
                showErrorMessage('Erro ao carregar técnicos. Algumas funcionalidades podem estar limitadas.');
            }
        } catch (error) {
            console.error('Exceção ao carregar técnicos:', error);
            allTechnicians = [];
            showErrorMessage('Erro ao carregar técnicos. Algumas funcionalidades podem estar limitadas.');
        }

        // Carregar especialidades
        try {
            console.log("Carregando especialidades...");
            const specialtiesResponse = await fetch('/api/technicians/specialties');
            console.log("Resposta de especialidades:", specialtiesResponse.status);

            if (specialtiesResponse.ok) {
                allSpecialties = await specialtiesResponse.json();
                console.log(`${allSpecialties.length} especialidades carregadas com sucesso`);
            } else {
                console.error('Erro ao carregar especialidades:', specialtiesResponse.status);
                const errorText = await specialtiesResponse.text();
                console.error('Detalhes do erro:', errorText);
                allSpecialties = [];
                showErrorMessage('Erro ao carregar especialidades. Algumas funcionalidades podem estar limitadas.');
            }
        } catch (error) {
            console.error('Exceção ao carregar especialidades:', error);
            allSpecialties = [];
            showErrorMessage('Erro ao carregar especialidades. Algumas funcionalidades podem estar limitadas.');
        }

        // Carregar dados de vínculos (se disponíveis)
        try {
            console.log("Carregando vínculos entre prestadoras e filiais...");
            const providerBranchResponse = await fetch('/api/links/provider-branch');
            console.log("Resposta de vínculos prestadoras-filiais:", providerBranchResponse.status);

            if (providerBranchResponse.ok) {
                const data = await providerBranchResponse.json();
                console.log("Dados de vínculos prestadoras-filiais:", data);

                // Verificar se a resposta tem o formato esperado (objeto com propriedade 'links')
                if (data && data.links) {
                    providerBranchLinks = data.links;
                    console.log(`${providerBranchLinks.length} vínculos de prestadoras e filiais carregados`);
                } else {
                    console.warn('Formato de resposta inesperado para vínculos de prestadoras e filiais:', data);
                    providerBranchLinks = [];
                }
            } else {
                console.error('Erro ao carregar vínculos de prestadoras e filiais:', providerBranchResponse.status);
                const errorText = await providerBranchResponse.text();
                console.error('Detalhes do erro:', errorText);
                providerBranchLinks = [];
            }

            console.log("Carregando vínculos entre técnicos e filiais...");
            const technicianBranchResponse = await fetch('/api/links/technician-branch');
            console.log("Resposta de vínculos técnicos-filiais:", technicianBranchResponse.status);

            if (technicianBranchResponse.ok) {
                const data = await technicianBranchResponse.json();
                console.log("Dados de vínculos técnicos-filiais:", data);

                // Verificar se a resposta tem o formato esperado
                if (Array.isArray(data)) {
                    technicianBranchLinks = data;
                    console.log(`${technicianBranchLinks.length} vínculos de técnicos e filiais carregados (array)`);
                } else if (data && data.links) {
                    technicianBranchLinks = data.links;
                    console.log(`${technicianBranchLinks.length} vínculos de técnicos e filiais carregados (objeto)`);
                } else {
                    console.warn('Formato de resposta inesperado para vínculos de técnicos e filiais:', data);
                    technicianBranchLinks = [];
                }
            } else {
                console.error('Erro ao carregar vínculos de técnicos e filiais:', technicianBranchResponse.status);
                const errorText = await technicianBranchResponse.text();
                console.error('Detalhes do erro:', errorText);
                technicianBranchLinks = [];
            }

            // Verificar vínculos entre prestadoras e técnicos
            try {
                console.log("Carregando vínculos entre prestadoras e técnicos...");
                const providerTechnicianResponse = await fetch('/api/links/provider-technician');
                console.log("Resposta de vínculos prestadoras-técnicos:", providerTechnicianResponse.status);

                if (providerTechnicianResponse.ok) {
                    const data = await providerTechnicianResponse.json();
                    console.log("Dados de vínculos prestadoras-técnicos:", data);
                } else {
                    console.error('Erro ao carregar vínculos de prestadoras e técnicos:', providerTechnicianResponse.status);
                    const errorText = await providerTechnicianResponse.text();
                    console.error('Detalhes do erro:', errorText);
                }
            } catch (error) {
                console.error('Exceção ao carregar vínculos de prestadoras e técnicos:', error);
            }
        } catch (linkError) {
            console.error('Não foi possível carregar dados de vínculos:', linkError);
            providerBranchLinks = [];
            technicianBranchLinks = [];
            showErrorMessage('Erro ao carregar vínculos. Algumas funcionalidades podem estar limitadas.');
        }

        console.log("Preenchendo dropdowns de filtros...");
        // Preencher dropdowns de filtros
        populateFilterDropdowns();

        console.log("Renderizando tabelas...");
        // Renderizar todas as tabelas iniciais
        renderProviderBranchTable();
        renderTechnicianBranchTable();
        renderProviderTechnicianTable();

        console.log("Carregamento de dados concluído com sucesso");
        // Ocultar indicador de carregamento
        showLoading(false);
    } catch (error) {
        console.error('Erro geral ao carregar dados iniciais:', error);
        showErrorMessage('Erro ao carregar dados. Por favor, recarregue a página.', 'Erro de Inicialização');
        showLoading(false);
    }
}

/**
 * Renderiza a tabela de vínculos entre técnicos e filiais
 */
function renderTechnicianBranchTable() {
    const tableBody = document.getElementById('technicianBranchTableBody');
    const technicianFilter = document.getElementById('technicianFilter');
    const branchFilter = document.getElementById('branchTechFilter');
    const specialtyFilter = document.getElementById('specialtyFilter');

    if (!tableBody) return;

    // Limpar tabela
    tableBody.innerHTML = '';

    // Aplicar filtros
    const technicianId = technicianFilter ? technicianFilter.value : '';
    const branchId = branchFilter ? branchFilter.value : '';
    const specialtyId = specialtyFilter ? specialtyFilter.value : '';

    // Filtrar links
    let filteredLinks = [...technicianBranchLinks];

    if (technicianId) {
        filteredLinks = filteredLinks.filter(link => link.technician_id.toString() === technicianId);
    }

    if (branchId) {
        filteredLinks = filteredLinks.filter(link => link.branch_id.toString() === branchId);
    }

    if (specialtyId) {
        filteredLinks = filteredLinks.filter(link => link.specialty_id.toString() === specialtyId);
    }

    // Verificar se há links para exibir
    if (filteredLinks.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-link-slash text-shell-yellow"></i>
                        <h4 class="text-shell-yellow">Nenhum vínculo encontrado</h4>
                        <p>Não há vínculos entre técnicos e filiais com os filtros selecionados.</p>
                        <div class="mt-3">
                            <button class="btn-shell-yellow btn-sm" id="addFirstTechBranchLinkBtn">
                                <i class="fas fa-plus me-1"></i> Adicionar Primeiro Vínculo
                            </button>
                        </div>
                    </div>
                </td>
            </tr>
        `;

        // Adicionar evento ao botão
        const addFirstLinkBtn = document.getElementById('addFirstTechBranchLinkBtn');
        if (addFirstLinkBtn) {
            addFirstLinkBtn.addEventListener('click', showAddTechnicianBranchModal);
        }

        return;
    }

    // Preencher tabela com links filtrados
    filteredLinks.forEach(link => {
        const technician = allTechnicians.find(t => t.id === link.technician_id) || { name: 'Desconhecido' };
        const branch = allBranches.find(b => b.id === link.branch_id) || { name: 'Desconhecida' };
        const specialty = allSpecialties.find(s => s.id === link.specialty_id) || { name: 'Geral' };

        tableBody.innerHTML += `
            <tr>
                <td>${technician.name}</td>
                <td>${branch.name}</td>
                <td>${specialty.name}</td>
                <td>${new Date(link.created_at).toLocaleDateString('pt-BR')}</td>
                <td>
                    <button class="btn btn-sm btn-danger" onclick="unlinkTechnicianBranch(${link.technician_id}, ${link.branch_id})">
                        <i class="fas fa-unlink"></i> Desvincular
                    </button>
                </td>
            </tr>
        `;
    });
}

/**
 * Remove o vínculo entre um técnico e uma filial
 * @param {number} technicianId - ID do técnico
 * @param {number} branchId - ID da filial
 */
function unlinkTechnicianBranch(technicianId, branchId) {
    console.log(`Solicitação para desvincular técnico ${technicianId} da filial ${branchId}`);

    // Configurar o modal de confirmação
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (!confirmDeleteBtn) return;

    // Remover qualquer listener anterior
    const newConfirmBtn = confirmDeleteBtn.cloneNode(true);
    confirmDeleteBtn.parentNode.replaceChild(newConfirmBtn, confirmDeleteBtn);

    // Adicionar novo listener
    newConfirmBtn.addEventListener('click', function() {
        // Mostrar indicador de carregamento
        showLoading(true);

        // Enviar requisição para remover vínculo
        fetch(`/api/links/technician-branch/${technicianId}/${branchId}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || 'Erro ao remover vínculo');
                });
            }
            return response.json();
        })
        .then(data => {
            // Exibir mensagem de sucesso
            showSuccessMessage('Vínculo removido com sucesso!');

            // Recarregar os vínculos
            fetch('/api/links/technician-branch')
                .then(response => response.json())
                .then(data => {
                    if (data && data.links) {
                        technicianBranchLinks = data.links;
                    } else if (Array.isArray(data)) {
                        technicianBranchLinks = data;
                    }
                    renderTechnicianBranchTable();
                })
                .catch(error => {
                    console.error('Erro ao recarregar vínculos:', error);
                });

            // Fechar o modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('confirmDeleteModal'));
            if (modal) {
                modal.hide();
            }
        })
        .catch(error => {
            console.error('Erro ao remover vínculo:', error);
            showErrorMessage(`Erro ao remover vínculo: ${error.message}`);
        })
        .finally(() => {
            showLoading(false);
        });
    });

    // Exibir o modal de confirmação
    const modal = new bootstrap.Modal(document.getElementById('confirmDeleteModal'));
    modal.show();
}

/**
 * Renderiza a tabela de vínculos entre prestadoras e técnicos
 */
function renderProviderTechnicianTable() {
    const tableBody = document.getElementById('providerTechnicianTableBody');
    const providerFilter = document.getElementById('providerTechFilter');
    const technicianFilter = document.getElementById('technicianProviderFilter');
    const relationshipFilter = document.getElementById('relationshipFilter');

    if (!tableBody) return;

    // Limpar tabela
    tableBody.innerHTML = '';

    // Obter valores dos filtros
    const providerId = providerFilter ? providerFilter.value : '';
    const technicianId = technicianFilter ? technicianFilter.value : '';
    const relationshipType = relationshipFilter ? relationshipFilter.value : '';

    // Verificar se temos dados para exibir
    if (allProviders.length === 0 || allTechnicians.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-database text-shell-yellow"></i>
                        <h4 class="text-shell-yellow">Sem Dados Disponíveis</h4>
                        <p class="text-white">Não há dados de prestadoras ou técnicos para exibir.</p>
                        <div class="mt-3">
                            <button class="btn-shell-yellow" onclick="showAddProviderTechnicianModal()">
                                <i class="fas fa-plus me-1"></i> Adicionar Vínculo
                            </button>
                        </div>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // Buscar vínculos entre prestadoras e técnicos
    fetch('/api/links/provider-technician')
        .then(response => response.json())
        .then(data => {
            const links = data.links || [];

            // Filtrar links conforme os filtros selecionados
            let filteredLinks = [...links];

            if (providerId) {
                if (providerId === 'no_technicians') {
                    // Lógica para prestadoras sem técnicos
                    const providersWithTechs = new Set(links.map(link => link.provider_id));
                    const providersWithoutTechs = allProviders.filter(p => !providersWithTechs.has(p.id));

                    tableBody.innerHTML = '';

                    if (providersWithoutTechs.length === 0) {
                        tableBody.innerHTML = `
                            <tr>
                                <td colspan="4" class="text-center">
                                    <div class="empty-state">
                                        <i class="fas fa-info-circle text-shell-yellow"></i>
                                        <h4 class="text-shell-yellow">Todas as prestadoras possuem técnicos</h4>
                                        <p>Não há prestadoras sem técnicos vinculados.</p>
                                    </div>
                                </td>
                            </tr>
                        `;
                    } else {
                        providersWithoutTechs.forEach(provider => {
                            tableBody.innerHTML += `
                                <tr>
                                    <td colspan="3">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-building me-2 text-shell-yellow"></i>
                                            <strong>${provider.name}</strong>
                                            <span class="badge bg-warning text-dark ms-2">Sem técnicos</span>
                                        </div>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-shell-yellow" onclick="showAddProviderTechnicianModal()">
                                            <i class="fas fa-user-plus"></i> Adicionar Técnico
                                        </button>
                                    </td>
                                </tr>
                            `;
                        });
                    }
                    return;
                } else {
                    filteredLinks = filteredLinks.filter(link => link.provider_id.toString() === providerId);
                }
            }

            if (technicianId) {
                if (technicianId === 'no_provider') {
                    // Lógica para técnicos sem prestadora
                    const techniciansWithProviders = new Set(links.map(link => link.technician_id));
                    const techniciansWithoutProviders = allTechnicians.filter(t => !techniciansWithProviders.has(t.id));

                    tableBody.innerHTML = '';

                    if (techniciansWithoutProviders.length === 0) {
                        tableBody.innerHTML = `
                            <tr>
                                <td colspan="4" class="text-center">
                                    <div class="empty-state">
                                        <i class="fas fa-info-circle text-shell-yellow"></i>
                                        <h4 class="text-shell-yellow">Todos os técnicos possuem prestadoras</h4>
                                        <p>Não há técnicos sem prestadoras vinculadas.</p>
                                    </div>
                                </td>
                            </tr>
                        `;
                    } else {
                        techniciansWithoutProviders.forEach(technician => {
                            tableBody.innerHTML += `
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-user-cog me-2 text-shell-yellow"></i>
                                            <strong>${technician.name}</strong>
                                            <span class="badge bg-warning text-dark ms-2">Sem prestadora</span>
                                        </div>
                                    </td>
                                    <td>${technician.email || 'N/A'}</td>
                                    <td>${technician.phone || 'N/A'}</td>
                                    <td>
                                        <button class="btn btn-sm btn-shell-yellow" onclick="showAddProviderTechnicianModal()">
                                            <i class="fas fa-building-user"></i> Vincular a Prestadora
                                        </button>
                                    </td>
                                </tr>
                            `;
                        });
                    }
                    return;
                } else {
                    filteredLinks = filteredLinks.filter(link => link.technician_id.toString() === technicianId);
                }
            }

            // Verificar se há links para exibir após a filtragem
            if (filteredLinks.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="4" class="text-center">
                            <div class="empty-state">
                                <i class="fas fa-link-slash text-shell-yellow"></i>
                                <h4 class="text-shell-yellow">Nenhum vínculo encontrado</h4>
                                <p>Não há vínculos entre prestadoras e técnicos com os filtros selecionados.</p>
                                <div class="mt-3">
                                    <button class="btn-shell-yellow btn-sm" onclick="showAddProviderTechnicianModal()">
                                        <i class="fas fa-plus me-1"></i> Adicionar Vínculo
                                    </button>
                                </div>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            // Agrupar por prestadora para melhor visualização
            const groupedByProvider = {};

            filteredLinks.forEach(link => {
                if (!groupedByProvider[link.provider_id]) {
                    groupedByProvider[link.provider_id] = [];
                }
                groupedByProvider[link.provider_id].push(link);
            });

            // Renderizar os grupos
            Object.keys(groupedByProvider).forEach(providerId => {
                const provider = allProviders.find(p => p.id.toString() === providerId) || { name: 'Desconhecida' };
                const technicians = groupedByProvider[providerId];

                // Cabeçalho da prestadora
                tableBody.innerHTML += `
                    <tr class="provider-header">
                        <td colspan="4" class="bg-dark">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-building me-2 text-shell-yellow"></i>
                                    <strong class="text-shell-yellow">${provider.name}</strong>
                                    <span class="badge bg-shell-yellow text-dark ms-2">${technicians.length} técnico(s)</span>
                                </div>
                                <button class="btn btn-sm btn-shell-yellow" onclick="showAddProviderTechnicianModal()">
                                    <i class="fas fa-user-plus"></i> Adicionar Técnico
                                </button>
                            </div>
                        </td>
                    </tr>
                `;

                // Listar técnicos da prestadora
                technicians.forEach(link => {
                    const technician = allTechnicians.find(t => t.id === link.technician_id) || { name: 'Desconhecido' };

                    // Buscar filiais vinculadas ao técnico
                    const techBranches = technicianBranchLinks.filter(b => b.technician_id === technician.id);
                    const branchCount = techBranches.length;

                    tableBody.innerHTML += `
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-user-cog me-2"></i>
                                    ${technician.name}
                                </div>
                            </td>
                            <td>${technician.email || 'N/A'}</td>
                            <td>${technician.phone || 'N/A'}</td>
                            <td>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge ${branchCount > 0 ? 'bg-success' : 'bg-warning text-dark'}">
                                        ${branchCount} filial(is)
                                    </span>
                                    <div class="btn-group">
                                        <button class="btn btn-sm btn-shell-yellow" onclick="showTechnicianBranches(${technician.id})">
                                            <i class="fas fa-eye"></i> Ver Filiais
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="unlinkProviderTechnician(${provider.id}, ${technician.id})">
                                            <i class="fas fa-unlink"></i> Desvincular
                                        </button>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    `;
                });
            });
        })
        .catch(error => {
            console.error('Erro ao carregar vínculos:', error);
            tableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center">
                        <div class="empty-state">
                            <i class="fas fa-exclamation-triangle text-warning"></i>
                            <h4 class="text-warning">Erro ao carregar dados</h4>
                            <p>Não foi possível carregar os vínculos entre prestadoras e técnicos.</p>
                            <div class="mt-3">
                                <button class="btn-shell-yellow" onclick="renderProviderTechnicianTable()">
                                    <i class="fas fa-sync-alt me-1"></i> Tentar Novamente
                                </button>
                            </div>
                        </div>
                    </td>
                </tr>
            `;
        });
}

/**
 * Exibe o modal para adicionar um novo vínculo entre prestadora e filial
 */
function showAddProviderBranchModal() {
    const modal = new bootstrap.Modal(document.getElementById('addProviderBranchModal'));
    modal.show();
}

/**
 * Exibe o modal para adicionar um novo vínculo entre técnico e filial
 */
function showAddTechnicianBranchModal() {
    const modal = new bootstrap.Modal(document.getElementById('addTechnicianBranchModal'));
    modal.show();
}

/**
 * Exibe o modal para adicionar um novo vínculo entre prestadora e técnico
 */
function showAddProviderTechnicianModal() {
    // Limpar o formulário antes de exibir o modal
    const form = document.getElementById('providerTechnicianForm');
    if (form) form.reset();

    // Exibir o modal
    const modal = new bootstrap.Modal(document.getElementById('addProviderTechnicianModal'));
    modal.show();
}

/**
 * Manipula o envio do formulário de vínculo entre prestadora e filial
 */
function handleProviderBranchSubmit(event) {
    event.preventDefault();

    // Obter valores do formulário
    const providerId = document.getElementById('modalProviderSelect').value;
    const branchId = document.getElementById('modalBranchSelect').value;

    // Validação
    if (!providerId || !branchId) {
        showErrorMessage('Por favor, selecione uma prestadora e uma filial.');
        return;
    }

    // Mostrar indicador de carregamento
    showLoading(true);

    // Enviar requisição para criar vínculo
    fetch('/api/links/provider-branch', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            provider_id: parseInt(providerId),
            branch_id: parseInt(branchId)
        })
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.error || 'Erro ao criar vínculo');
            });
        }
        return response.json();
    })
    .then(data => {
        // Exibir mensagem de sucesso
        showSuccessMessage('Vínculo criado com sucesso!');

        // Recarregar os vínculos
        fetch('/api/links/provider-branch')
            .then(response => response.json())
            .then(data => {
                if (data && data.links) {
                    providerBranchLinks = data.links;
                    renderProviderBranchTable();
                }
            })
            .catch(error => {
                console.error('Erro ao recarregar vínculos:', error);
            });

        // Fechar o modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('addProviderBranchModal'));
        if (modal) {
            modal.hide();
        }
    })
    .catch(error => {
        console.error('Erro ao criar vínculo:', error);
        showErrorMessage(`Erro ao criar vínculo: ${error.message}`);
    })
    .finally(() => {
        showLoading(false);
    });
}

/**
 * Manipula o envio do formulário de vínculo entre técnico e filial
 */
function handleTechnicianBranchSubmit(event) {
    event.preventDefault();

    // Obter valores do formulário
    const technicianId = document.getElementById('modalTechnicianSelect').value;
    const branchId = document.getElementById('modalBranchTechSelect').value;
    const specialtyId = document.getElementById('modalSpecialtySelect').value;

    // Validação
    if (!technicianId || !branchId || !specialtyId) {
        showErrorMessage('Por favor, preencha todos os campos obrigatórios.');
        return;
    }

    // Mostrar indicador de carregamento
    showLoading(true);

    // Enviar requisição para criar vínculo
    fetch('/api/links/technician-branch', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            technician_id: parseInt(technicianId),
            branch_id: parseInt(branchId),
            specialty_id: parseInt(specialtyId)
        })
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.error || 'Erro ao criar vínculo');
            });
        }
        return response.json();
    })
    .then(data => {
        // Exibir mensagem de sucesso
        showSuccessMessage('Vínculo criado com sucesso!');

        // Recarregar os vínculos
        fetch('/api/links/technician-branch')
            .then(response => response.json())
            .then(data => {
                if (data && data.links) {
                    technicianBranchLinks = data.links;
                } else if (Array.isArray(data)) {
                    technicianBranchLinks = data;
                }
                renderTechnicianBranchTable();
            })
            .catch(error => {
                console.error('Erro ao recarregar vínculos:', error);
            });

        // Fechar o modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('addTechnicianBranchModal'));
        if (modal) {
            modal.hide();
        }
    })
    .catch(error => {
        console.error('Erro ao criar vínculo:', error);
        showErrorMessage(`Erro ao criar vínculo: ${error.message}`);
    })
    .finally(() => {
        showLoading(false);
    });
}

/**
 * Manipula o envio do formulário de vínculo entre prestadora e técnico
 */
function handleProviderTechnicianSubmit(event) {
    event.preventDefault();

    // Obter valores do formulário
    const providerId = document.getElementById('modalProviderTechSelect').value;
    const technicianId = document.getElementById('modalTechnicianProviderSelect').value;
    const inheritBranchLinks = document.getElementById('inheritBranchLinks').checked;

    // Log dos valores selecionados para depuração
    console.log("Valores selecionados:", {
        providerId: providerId,
        technicianId: technicianId,
        inheritBranchLinks: inheritBranchLinks
    });

    // Verificar se os valores são numéricos
    const providerIdNum = parseInt(providerId, 10);
    const technicianIdNum = parseInt(technicianId, 10);

    // Validação mais detalhada
    if (!providerId || !technicianId) {
        showErrorMessage('Por favor, selecione uma prestadora e um técnico.');
        return;
    }

    if (isNaN(providerIdNum) || providerIdNum <= 0) {
        showErrorMessage('ID da prestadora inválido. Por favor, selecione uma prestadora válida.');
        return;
    }

    if (isNaN(technicianIdNum) || technicianIdNum <= 0) {
        showErrorMessage('ID do técnico inválido. Por favor, selecione um técnico válido.');
        return;
    }

    // Mostrar indicador de carregamento
    showLoading(true);

    // Processar a vinculação com verificações adicionais
    (async () => {
        try {
            // Verificar se a prestadora existe no banco de dados
            const providerExists = await verifyProviderExists(providerIdNum);
            if (!providerExists) {
                throw new Error(`A prestadora com ID ${providerIdNum} não existe no banco de dados.`);
            }

            // Verificar se o técnico existe no banco de dados
            const technicianExists = await verifyTechnicianExists(technicianIdNum);
            if (!technicianExists) {
                throw new Error(`O técnico com ID ${technicianIdNum} não existe no banco de dados.`);
            }

            // Verificar se o vínculo já existe
            const linkExists = await verifyProviderTechnicianLinkExists(providerIdNum, technicianIdNum);
            if (linkExists) {
                throw new Error(`Já existe um vínculo entre esta prestadora e este técnico.`);
            }

            // Preparar dados para envio
            const requestData = {
                provider_id: providerIdNum,
                technician_id: technicianIdNum
            };

            console.log("Enviando requisição para criar vínculo:", requestData);

            // Vincular técnico à prestadora
            const response = await fetch('/api/links/provider-technician', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            // Log da resposta para depuração
            console.log("Status da resposta:", response.status);

            let responseData;
            try {
                // Tentar obter dados da resposta como JSON
                responseData = await response.json();
                console.log("Dados da resposta:", responseData);
            } catch (jsonError) {
                // Se não for JSON, tentar obter como texto
                const textResponse = await response.text();
                console.log("Resposta em texto:", textResponse);
                responseData = { error: "Formato de resposta inválido", details: textResponse };
            }

            if (!response.ok) {
                // Exibir detalhes do erro
                const errorMessage = responseData.error || 'Erro desconhecido';
                const errorDetails = responseData.details || '';
                console.error("Erro detalhado:", {
                    status: response.status,
                    message: errorMessage,
                    details: errorDetails
                });

                throw new Error(`${errorMessage}${errorDetails ? ': ' + errorDetails : ''}`);
            }

            // Se a opção de herdar vínculos estiver marcada
            if (inheritBranchLinks) {
                console.log(`Herdando vínculos de filiais da prestadora ${providerId} para o técnico ${technicianId}`);

                // Chamar a API para herdar vínculos
                const inheritResponse = await fetch('/api/links/provider-technician/inherit-branches', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                let inheritResponseData;
                try {
                    inheritResponseData = await inheritResponse.json();
                    console.log("Resposta da herança de vínculos:", inheritResponseData);
                } catch (jsonError) {
                    const textResponse = await inheritResponse.text();
                    console.log("Resposta em texto da herança:", textResponse);
                    inheritResponseData = { error: "Formato de resposta inválido", count: 0 };
                }

                if (!inheritResponse.ok) {
                    console.warn('Erro ao herdar vínculos:', inheritResponseData.error);
                    // Não interromper o fluxo se a herança falhar
                } else {
                    console.log(`${inheritResponseData.count || 0} vínculos de filiais herdados com sucesso`);
                }
            }

            // Exibir mensagem de sucesso
            showSuccessMessage('Vínculo criado com sucesso!');

            // Atualizar a tabela
            renderProviderTechnicianTable();

            // Fechar o modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addProviderTechnicianModal'));
            if (modal) {
                modal.hide();
            }
        } catch (error) {
            console.error('Erro ao criar vínculo:', error);
            showErrorMessage(`Erro ao criar vínculo: ${error.message}`);
        } finally {
            showLoading(false);
        }
    })();
}

/**
 * Remove o vínculo entre uma prestadora e uma filial
 * @param {number} providerId - ID da prestadora
 * @param {number} branchId - ID da filial
 */
function unlinkProviderBranch(providerId, branchId) {
    console.log(`Solicitação para desvincular prestadora ${providerId} da filial ${branchId}`);

    // Configurar o modal de confirmação
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (!confirmDeleteBtn) return;

    // Remover qualquer listener anterior
    const newConfirmBtn = confirmDeleteBtn.cloneNode(true);
    confirmDeleteBtn.parentNode.replaceChild(newConfirmBtn, confirmDeleteBtn);

    // Adicionar novo listener
    newConfirmBtn.addEventListener('click', function() {
        // Mostrar indicador de carregamento
        showLoading(true);

        // Enviar requisição para remover vínculo
        fetch(`/api/links/provider-branch/${providerId}/${branchId}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || 'Erro ao remover vínculo');
                });
            }
            return response.json();
        })
        .then(data => {
            // Exibir mensagem de sucesso
            showSuccessMessage('Vínculo removido com sucesso!');

            // Recarregar os vínculos
            fetch('/api/links/provider-branch')
                .then(response => response.json())
                .then(data => {
                    if (data && data.links) {
                        providerBranchLinks = data.links;
                        renderProviderBranchTable();
                    }
                })
                .catch(error => {
                    console.error('Erro ao recarregar vínculos:', error);
                });

            // Fechar o modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('confirmDeleteModal'));
            if (modal) {
                modal.hide();
            }
        })
        .catch(error => {
            console.error('Erro ao remover vínculo:', error);
            showErrorMessage(`Erro ao remover vínculo: ${error.message}`);
        })
        .finally(() => {
            showLoading(false);
        });
    });

    // Exibir o modal de confirmação
    const modal = new bootstrap.Modal(document.getElementById('confirmDeleteModal'));
    modal.show();
}

/**
 * Configura os listeners de eventos para a página
 */
function setupEventListeners() {
    // Filtros da aba Prestadoras e Filiais
    const providerFilter = document.getElementById('providerFilter');
    const branchFilter = document.getElementById('branchFilter');
    const clearProviderBranchFilters = document.getElementById('clearProviderBranchFilters');

    if (providerFilter) {
        providerFilter.addEventListener('change', renderProviderBranchTable);
    }

    if (branchFilter) {
        branchFilter.addEventListener('change', renderProviderBranchTable);
    }

    if (clearProviderBranchFilters) {
        clearProviderBranchFilters.addEventListener('click', () => {
            if (providerFilter) providerFilter.value = '';
            if (branchFilter) branchFilter.value = '';
            renderProviderBranchTable();
        });
    }

    // Filtros da aba Técnicos e Filiais
    const technicianFilter = document.getElementById('technicianFilter');
    const branchTechFilter = document.getElementById('branchTechFilter');
    const specialtyFilter = document.getElementById('specialtyFilter');
    const clearTechnicianBranchFilters = document.getElementById('clearTechnicianBranchFilters');

    if (technicianFilter) {
        technicianFilter.addEventListener('change', renderTechnicianBranchTable);
    }

    if (branchTechFilter) {
        branchTechFilter.addEventListener('change', renderTechnicianBranchTable);
    }

    if (specialtyFilter) {
        specialtyFilter.addEventListener('change', renderTechnicianBranchTable);
    }

    if (clearTechnicianBranchFilters) {
        clearTechnicianBranchFilters.addEventListener('click', () => {
            if (technicianFilter) technicianFilter.value = '';
            if (branchTechFilter) branchTechFilter.value = '';
            if (specialtyFilter) specialtyFilter.value = '';
            renderTechnicianBranchTable();
        });
    }

    // Filtros da aba Prestadoras e Técnicos
    const providerTechFilter = document.getElementById('providerTechFilter');
    const technicianProviderFilter = document.getElementById('technicianProviderFilter');
    const relationshipFilter = document.getElementById('relationshipFilter');
    const clearProviderTechFilter = document.getElementById('clearProviderTechFilter');

    if (providerTechFilter) {
        providerTechFilter.addEventListener('change', renderProviderTechnicianTable);
    }

    if (technicianProviderFilter) {
        technicianProviderFilter.addEventListener('change', renderProviderTechnicianTable);
    }

    if (relationshipFilter) {
        relationshipFilter.addEventListener('change', renderProviderTechnicianTable);
    }

    if (clearProviderTechFilter) {
        clearProviderTechFilter.addEventListener('click', () => {
            if (providerTechFilter) providerTechFilter.value = '';
            if (technicianProviderFilter) technicianProviderFilter.value = '';
            if (relationshipFilter) relationshipFilter.value = '';
            renderProviderTechnicianTable();
        });
    }

    // Botões para adicionar novos vínculos
    const addProviderBranchBtn = document.getElementById('addProviderBranchBtn');
    const addTechnicianBranchBtn = document.getElementById('addTechnicianBranchBtn');
    const addProviderTechnicianBtn = document.getElementById('addProviderTechnicianBtn');

    if (addProviderBranchBtn) {
        addProviderBranchBtn.addEventListener('click', showAddProviderBranchModal);
    }

    if (addTechnicianBranchBtn) {
        addTechnicianBranchBtn.addEventListener('click', showAddTechnicianBranchModal);
    }

    if (addProviderTechnicianBtn) {
        addProviderTechnicianBtn.addEventListener('click', showAddProviderTechnicianModal);
    }

    // Formulários de modais
    const providerBranchForm = document.getElementById('providerBranchForm');
    const technicianBranchForm = document.getElementById('technicianBranchForm');
    const providerTechnicianForm = document.getElementById('providerTechnicianForm');

    if (providerBranchForm) {
        providerBranchForm.addEventListener('submit', handleProviderBranchSubmit);
    }

    if (technicianBranchForm) {
        technicianBranchForm.addEventListener('submit', handleTechnicianBranchSubmit);
    }

    if (providerTechnicianForm) {
        providerTechnicianForm.addEventListener('submit', handleProviderTechnicianSubmit);
    }

    // Garantir que os modais sejam resetados ao fechar
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modalElement => {
        modalElement.addEventListener('hidden.bs.modal', function() {
            // Resetar formulários quando o modal for fechado
            const forms = this.querySelectorAll('form');
            forms.forEach(form => form.reset());
        });
    });
}

/**
 * Preenche os dropdowns de filtros com os dados carregados
 */
function populateFilterDropdowns() {
    // Preencher dropdowns de filiais
    const branchDropdowns = document.querySelectorAll('.branch-dropdown');
    branchDropdowns.forEach(dropdown => {
        dropdown.innerHTML = '<option value="">Todas as Filiais</option>';
        allBranches.forEach(branch => {
            dropdown.innerHTML += `<option value="${branch.id}">${branch.name}</option>`;
        });
    });

    // Preencher dropdowns de prestadoras
    const providerDropdowns = document.querySelectorAll('.provider-dropdown');
    providerDropdowns.forEach(dropdown => {
        dropdown.innerHTML = '<option value="">Todas as Prestadoras</option>';
        allProviders.forEach(provider => {
            dropdown.innerHTML += `<option value="${provider.id}">${provider.name}</option>`;
        });
    });

    // Preencher dropdowns de técnicos
    const technicianDropdowns = document.querySelectorAll('.technician-dropdown');
    technicianDropdowns.forEach(dropdown => {
        dropdown.innerHTML = '<option value="">Todos os Técnicos</option>';
        allTechnicians.forEach(technician => {
            dropdown.innerHTML += `<option value="${technician.id}">${technician.name}</option>`;
        });
    });

    // Preencher dropdowns de especialidades
    const specialtyDropdowns = document.querySelectorAll('.specialty-dropdown');
    specialtyDropdowns.forEach(dropdown => {
        dropdown.innerHTML = '<option value="">Todas as Especialidades</option>';
        allSpecialties.forEach(specialty => {
            dropdown.innerHTML += `<option value="${specialty.id}">${specialty.name}</option>`;
        });
    });
}

/**
 * Renderiza a tabela de vínculos entre prestadoras e filiais
 */
function renderProviderBranchTable() {
    const tableBody = document.getElementById('providerBranchTableBody');
    const providerFilter = document.getElementById('providerFilter');
    const branchFilter = document.getElementById('branchFilter');

    if (!tableBody) return;

    // Aplicar filtros
    const providerId = providerFilter ? providerFilter.value : '';
    const branchId = branchFilter ? branchFilter.value : '';

    // Filtrar links
    let filteredLinks = [...providerBranchLinks];

    if (providerId) {
        filteredLinks = filteredLinks.filter(link => link.service_provider_id.toString() === providerId);
    }

    if (branchId) {
        filteredLinks = filteredLinks.filter(link => link.branch_id.toString() === branchId);
    }

    // Limpar tabela
    tableBody.innerHTML = '';

    // Verificar se há links para exibir
    if (filteredLinks.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-link-slash text-shell-yellow"></i>
                        <h4 class="text-shell-yellow">Nenhum vínculo encontrado</h4>
                        <p>Não há vínculos entre prestadoras e filiais com os filtros selecionados.</p>
                        <div class="mt-3">
                            <button class="btn-shell-yellow btn-sm" id="addFirstLinkBtn">
                                <i class="fas fa-plus me-1"></i> Adicionar Primeiro Vínculo
                            </button>
                        </div>
                    </div>
                </td>
            </tr>
        `;

        // Adicionar evento ao botão
        const addFirstLinkBtn = document.getElementById('addFirstLinkBtn');
        if (addFirstLinkBtn) {
            addFirstLinkBtn.addEventListener('click', showAddProviderBranchModal);
        }

        return;
    }

    // Preencher tabela com links filtrados
    filteredLinks.forEach(link => {
        const provider = allProviders.find(p => p.id === link.service_provider_id) || { name: 'Desconhecido' };
        const branch = allBranches.find(b => b.id === link.branch_id) || { name: 'Desconhecida' };

        tableBody.innerHTML += `
            <tr>
                <td>${provider.name}</td>
                <td>${branch.name}</td>
                <td>${new Date(link.created_at).toLocaleDateString('pt-BR')}</td>
                <td>
                    <button class="btn btn-sm btn-danger" onclick="unlinkProviderBranch(${link.service_provider_id}, ${link.branch_id})">
                        <i class="fas fa-unlink"></i> Desvincular
                    </button>
                </td>
            </tr>
        `;
    });
}

// Funções adicionais para diagnóstico e validação

/**
 * Verifica se uma prestadora existe no banco de dados
 * @param {number} providerId - ID da prestadora a verificar
 * @returns {Promise<boolean>} - Verdadeiro se a prestadora existir
 */
async function verifyProviderExists(providerId) {
    try {
        console.log(`Verificando existência da prestadora ID ${providerId}...`);
        const response = await fetch(`/api/providers/${providerId}`);

        if (response.ok) {
            const provider = await response.json();
            console.log(`Prestadora encontrada:`, provider);
            return true;
        } else {
            console.error(`Prestadora ID ${providerId} não encontrada. Status: ${response.status}`);
            return false;
        }
    } catch (error) {
        console.error(`Erro ao verificar prestadora ID ${providerId}:`, error);
        return false;
    }
}

/**
 * Verifica se um técnico existe no banco de dados
 * @param {number} technicianId - ID do técnico a verificar
 * @returns {Promise<boolean>} - Verdadeiro se o técnico existir
 */
async function verifyTechnicianExists(technicianId) {
    try {
        console.log(`Verificando existência do técnico ID ${technicianId}...`);
        const response = await fetch(`/api/technicians/${technicianId}`);

        if (response.ok) {
            const technician = await response.json();
            console.log(`Técnico encontrado:`, technician);
            return true;
        } else {
            console.error(`Técnico ID ${technicianId} não encontrado. Status: ${response.status}`);
            return false;
        }
    } catch (error) {
        console.error(`Erro ao verificar técnico ID ${technicianId}:`, error);
        return false;
    }
}

/**
 * Remove o vínculo entre uma prestadora e um técnico
 * @param {number} providerId - ID da prestadora
 * @param {number} technicianId - ID do técnico
 */
function unlinkProviderTechnician(providerId, technicianId) {
    console.log(`Solicitação para desvincular técnico ${technicianId} da prestadora ${providerId}`);

    // Configurar o modal de confirmação
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (!confirmDeleteBtn) return;

    // Remover qualquer listener anterior
    const newConfirmBtn = confirmDeleteBtn.cloneNode(true);
    confirmDeleteBtn.parentNode.replaceChild(newConfirmBtn, confirmDeleteBtn);

    // Adicionar novo listener
    newConfirmBtn.addEventListener('click', function() {
        // Mostrar indicador de carregamento
        showLoading(true);

        // Enviar requisição para remover vínculo
        fetch(`/api/links/provider-technician/${providerId}/${technicianId}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || 'Erro ao remover vínculo');
                });
            }
            return response.json();
        })
        .then(data => {
            // Exibir mensagem de sucesso
            showSuccessMessage('Vínculo removido com sucesso!');

            // Recarregar os vínculos
            renderProviderTechnicianTable();

            // Fechar o modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('confirmDeleteModal'));
            if (modal) {
                modal.hide();
            }
        })
        .catch(error => {
            console.error('Erro ao remover vínculo:', error);
            showErrorMessage(`Erro ao remover vínculo: ${error.message}`);
        })
        .finally(() => {
            showLoading(false);
        });
    });

    // Exibir o modal de confirmação
    const modal = new bootstrap.Modal(document.getElementById('confirmDeleteModal'));
    modal.show();
}

/**
 * Exibe um modal com as filiais vinculadas a um técnico
 * @param {number} technicianId - ID do técnico
 */
function showTechnicianBranches(technicianId) {
    console.log(`Exibindo filiais vinculadas ao técnico ${technicianId}`);

    // Verificar se já existe um modal para isso
    let techBranchesModal = document.getElementById('technicianBranchesModal');

    // Se não existir, criar um
    if (!techBranchesModal) {
        const modalHtml = `
            <div class="modal fade" id="technicianBranchesModal" tabindex="-1" aria-labelledby="technicianBranchesModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="technicianBranchesModalLabel">Filiais Vinculadas ao Técnico</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                        </div>
                        <div class="modal-body">
                            <div id="technicianBranchesContent">
                                <div class="text-center">
                                    <div class="spinner-border text-warning" role="status">
                                        <span class="visually-hidden">Carregando...</span>
                                    </div>
                                    <p class="mt-2 text-warning">Carregando filiais, por favor aguarde...</p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn-shell-dark text-white" data-bs-dismiss="modal">Fechar</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Adicionar o modal ao corpo do documento
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        techBranchesModal = document.getElementById('technicianBranchesModal');
    }

    // Exibir o modal
    const modal = new bootstrap.Modal(techBranchesModal);
    modal.show();

    // Buscar as filiais vinculadas ao técnico
    const contentDiv = document.getElementById('technicianBranchesContent');
    if (!contentDiv) return;

    // Mostrar indicador de carregamento
    contentDiv.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-warning" role="status">
                <span class="visually-hidden">Carregando...</span>
            </div>
            <p class="mt-2 text-warning">Carregando filiais, por favor aguarde...</p>
        </div>
    `;

    // Buscar o técnico
    const technician = allTechnicians.find(t => t.id === technicianId) || { name: 'Técnico Desconhecido' };

    // Atualizar o título do modal
    const modalTitle = document.getElementById('technicianBranchesModalLabel');
    if (modalTitle) {
        modalTitle.textContent = `Filiais Vinculadas ao Técnico: ${technician.name}`;
    }

    // Filtrar as filiais vinculadas ao técnico
    const techBranches = technicianBranchLinks.filter(link => link.technician_id === technicianId);

    // Se não houver filiais vinculadas
    if (techBranches.length === 0) {
        contentDiv.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-building-circle-xmark text-shell-yellow"></i>
                <h4 class="text-shell-yellow">Nenhuma Filial Vinculada</h4>
                <p>Este técnico não está vinculado a nenhuma filial.</p>
                <div class="mt-3">
                    <button class="btn-shell-yellow" onclick="showAddTechnicianBranchModal()">
                        <i class="fas fa-plus me-1"></i> Adicionar Vínculo com Filial
                    </button>
                </div>
            </div>
        `;
        return;
    }

    // Preparar o conteúdo com as filiais
    let content = `
        <div class="mb-3">
            <p class="text-white">O técnico <strong>${technician.name}</strong> está vinculado a <strong>${techBranches.length}</strong> filial(is):</p>
        </div>
        <div class="table-responsive">
            <table class="table-shell">
                <thead>
                    <tr>
                        <th class="text-white">Filial</th>
                        <th class="text-white">Especialidade</th>
                        <th class="text-white">Data de Vínculo</th>
                        <th class="text-white">Ações</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // Adicionar cada filial à tabela
    techBranches.forEach(link => {
        const branch = allBranches.find(b => b.id === link.branch_id) || { name: 'Filial Desconhecida' };
        const specialty = allSpecialties.find(s => s.id === link.specialty_id) || { name: 'Especialidade Desconhecida' };

        content += `
            <tr>
                <td>${branch.name}</td>
                <td>${specialty.name}</td>
                <td>${new Date(link.created_at).toLocaleDateString('pt-BR')}</td>
                <td>
                    <button class="btn btn-sm btn-danger" onclick="unlinkTechnicianBranch(${technicianId}, ${branch.id})">
                        <i class="fas fa-unlink"></i> Desvincular
                    </button>
                </td>
            </tr>
        `;
    });

    content += `
                </tbody>
            </table>
        </div>
        <div class="mt-3 text-end">
            <button class="btn-shell-yellow" onclick="showAddTechnicianBranchModal()">
                <i class="fas fa-plus me-1"></i> Adicionar Nova Filial
            </button>
        </div>
    `;

    // Atualizar o conteúdo do modal
    contentDiv.innerHTML = content;
}

/**
 * Verifica se já existe um vínculo entre prestadora e técnico
 * @param {number} providerId - ID da prestadora
 * @param {number} technicianId - ID do técnico
 * @returns {Promise<boolean>} - Verdadeiro se o vínculo já existir
 */
async function verifyProviderTechnicianLinkExists(providerId, technicianId) {
    try {
        console.log(`Verificando existência de vínculo entre prestadora ${providerId} e técnico ${technicianId}...`);
        const response = await fetch('/api/links/provider-technician');

        if (response.ok) {
            const data = await response.json();
            const links = data.links || [];

            const linkExists = links.some(link =>
                link.provider_id === providerId &&
                link.technician_id === technicianId
            );

            console.log(`Vínculo ${linkExists ? 'encontrado' : 'não encontrado'}`);
            return linkExists;
        } else {
            console.error(`Erro ao verificar vínculos. Status: ${response.status}`);
            return false;
        }
    } catch (error) {
        console.error(`Erro ao verificar vínculo:`, error);
        return false;
    }
}

// Esta é uma versão melhorada do arquivo JavaScript

/**
 * Exibe ou oculta o indicador de carregamento
 */
function showLoading(show) {
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = show ? 'flex' : 'none';
    }
}

/**
 * Exibe uma mensagem de erro
 * @param {string} message - Mensagem de erro a ser exibida
 * @param {string} [title="Erro"] - Título da mensagem de erro
 */
function showErrorMessage(message, title = "Erro") {
    // Verificar se existe um elemento de toast para erros
    let errorToastContainer = document.getElementById('errorToastContainer');

    // Se não existir, criar um
    if (!errorToastContainer) {
        errorToastContainer = document.createElement('div');
        errorToastContainer.id = 'errorToastContainer';
        errorToastContainer.className = 'position-fixed bottom-0 end-0 p-3';
        errorToastContainer.style.zIndex = '11';
        document.body.appendChild(errorToastContainer);
    }

    // Criar ID único para o toast
    const toastId = 'errorToast-' + Date.now();

    // Criar HTML do toast
    const toastHtml = `
        <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="true" data-bs-delay="5000">
            <div class="toast-header bg-danger text-white">
                <i class="fas fa-exclamation-circle me-2"></i>
                <strong class="me-auto">${title}</strong>
                <small>Agora</small>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Fechar"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;

    // Adicionar o toast ao container
    errorToastContainer.innerHTML += toastHtml;

    // Inicializar e mostrar o toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement);
    toast.show();

    // Também logar o erro no console
    console.error(message);
}

/**
 * Exibe uma mensagem de sucesso
 * @param {string} message - Mensagem de sucesso a ser exibida
 * @param {string} [title="Sucesso"] - Título da mensagem de sucesso
 */
function showSuccessMessage(message, title = "Sucesso") {
    // Verificar se existe um elemento de toast para sucessos
    let successToastContainer = document.getElementById('successToastContainer');

    // Se não existir, criar um
    if (!successToastContainer) {
        successToastContainer = document.createElement('div');
        successToastContainer.id = 'successToastContainer';
        successToastContainer.className = 'position-fixed bottom-0 end-0 p-3';
        successToastContainer.style.zIndex = '11';
        document.body.appendChild(successToastContainer);
    }

    // Criar ID único para o toast
    const toastId = 'successToast-' + Date.now();

    // Criar HTML do toast
    const toastHtml = `
        <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="true" data-bs-delay="3000">
            <div class="toast-header bg-success text-white">
                <i class="fas fa-check-circle me-2"></i>
                <strong class="me-auto">${title}</strong>
                <small>Agora</small>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Fechar"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;

    // Adicionar o toast ao container
    successToastContainer.innerHTML += toastHtml;

    // Inicializar e mostrar o toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement);
    toast.show();

    // Também logar a mensagem no console
    console.log(message);
}
