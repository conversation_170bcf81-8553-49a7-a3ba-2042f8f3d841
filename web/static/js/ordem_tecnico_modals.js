/**
 * Gerenciamento de Modais - Ordem Técnico
 * Sistema Tradição - Módulo de Técnicos
 */

// Classe para gerenciar os modais
class OrdemTecnicoModals {
    constructor() {
        this.currentOrderId = null;
        this.setupModals();
    }

    // Configura os modais
    setupModals() {
        this.setupManutencaoModal();
        this.setupCustosModal();
        this.setupCronogramaModal();
        this.setupInteracaoModal();
    }

    // Define o ID da ordem atual
    setCurrentOrderId(orderId) {
        this.currentOrderId = orderId;
        console.log('ID da ordem atual definido:', orderId);
    }

    // Configura o modal de manutenção
    setupManutencaoModal() {
        console.log('Configurando modal de manutenção');

        // Configurar evento para quando o modal for aberto
        const manutencaoModal = document.getElementById('manutencaoModal');
        if (manutencaoModal) {
            manutencaoModal.addEventListener('show.bs.modal', () => {
                console.log('Modal de manutenção aberto, carregando dados para ordem ID:', this.currentOrderId);

                // Preencher o modal com os dados atuais, se disponíveis
                const descricaoServico = document.getElementById('descricaoServico');
                const pecasUtilizadas = document.getElementById('pecasUtilizadas');
                const observacoesManutencao = document.getElementById('observacoesManutencao');

                // Limpar os campos para evitar dados antigos
                if (descricaoServico) descricaoServico.value = '';
                if (pecasUtilizadas) pecasUtilizadas.value = '';
                if (observacoesManutencao) observacoesManutencao.value = '';

                // Tentar obter os dados atuais da ordem
                if (window.OrdemTecnicoAPI) {
                    window.OrdemTecnicoAPI.getOrdem(this.currentOrderId)
                        .then(response => {
                            if (response.success && response.data && response.data.manutencao) {
                                console.log('Dados de manutenção obtidos:', response.data.manutencao);
                                if (descricaoServico) descricaoServico.value = response.data.manutencao.descricao || '';
                                if (pecasUtilizadas) pecasUtilizadas.value = response.data.manutencao.pecas_utilizadas || '';
                                if (observacoesManutencao) observacoesManutencao.value = response.data.manutencao.observacoes || '';
                            } else {
                                console.log('Nenhum dado de manutenção encontrado ou resposta inválida');
                            }
                        })
                        .catch(error => {
                            console.error('Erro ao obter dados da ordem:', error);
                            // Usar dados simulados para demonstração
                            if (descricaoServico) descricaoServico.value = 'Troca de filtro de combustível';
                            if (pecasUtilizadas) pecasUtilizadas.value = 'Filtro de combustível, mangueira';
                            if (observacoesManutencao) observacoesManutencao.value = 'Cliente relatou baixa pressão durante abastecimento';
                        });
                }
            });
        }

        // Configurar botão de salvar
        const salvarManutencao = document.getElementById('salvarManutencao');
        if (!salvarManutencao) {
            console.error('Botão salvarManutencao não encontrado');
            return;
        }

        // Remover event listeners existentes
        const newSalvarManutencao = salvarManutencao.cloneNode(true);
        salvarManutencao.parentNode.replaceChild(newSalvarManutencao, salvarManutencao);

        // Adicionar novo event listener
        newSalvarManutencao.addEventListener('click', () => {
            console.log('Botão salvar manutenção clicado para ordem ID:', this.currentOrderId);

            // Validação dos campos
            const descricao = document.getElementById('descricaoServico').value.trim();
            const pecasUtilizadas = document.getElementById('pecasUtilizadas').value.trim();
            const observacoes = document.getElementById('observacoesManutencao').value.trim();

            if (!descricao && !pecasUtilizadas) {
                this.showNotification('Por favor, preencha pelo menos a descrição do serviço ou as peças utilizadas.', 'warning');
                return;
            }



            // Preparar dados para envio
            const data = {
                ordem_id: this.currentOrderId,
                descricao: descricao,
                pecas_utilizadas: pecasUtilizadas,
                observacoes: observacoes
            };

            console.log('Enviando dados de manutenção:', data);

            // Salvar dados via API
            window.OrdemTecnicoAPI.saveManutencao(this.currentOrderId, data)
                .then(response => {
                    if (response.success) {
                        console.log('Dados de manutenção salvos com sucesso');

                        // Atualizar o preview no card
                        const manutencaoPreview = document.getElementById('manutencao-preview');
                        if (manutencaoPreview) {
                            manutencaoPreview.innerHTML = descricao.substring(0, 100) + (descricao.length > 100 ? '...' : '');
                        }

                        // Fechar o modal
                        const modal = bootstrap.Modal.getInstance(document.getElementById('manutencaoModal'));
                        modal.hide();

                        // Mostrar notificação de sucesso
                        this.showNotification('Informações de manutenção salvas com sucesso!', 'success');

                        // Recarregar os detalhes da ordem para atualizar a interface
                        if (window.OrdemTecnicoDetails) {
                            window.OrdemTecnicoDetails.loadOrderDetails(this.currentOrderId);
                        }
                    } else {
                        console.error('Erro ao salvar dados de manutenção:', response.message);
                        this.showNotification('Erro ao salvar: ' + response.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Erro ao salvar dados de manutenção:', error);
                    this.showNotification('Erro ao salvar: ' + error.message, 'error');

                    // Para fins de demonstração, simular sucesso
                    const manutencaoPreview = document.getElementById('manutencao-preview');
                    if (manutencaoPreview) {
                        manutencaoPreview.innerHTML = descricao.substring(0, 100) + (descricao.length > 100 ? '...' : '');
                    }

                    // Fechar o modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('manutencaoModal'));
                    modal.hide();

                    // Mostrar notificação de sucesso (simulada)
                    this.showNotification('Informações de manutenção salvas com sucesso! (simulado)', 'success');
                });
        });
    }

    // Configura o modal de custos
    setupCustosModal() {
        console.log('Configurando modal de custos');

        // Configurar evento para quando o modal for aberto
        const custosModal = document.getElementById('custosModal');
        if (custosModal) {
            custosModal.addEventListener('show.bs.modal', () => {
                console.log('Modal de custos aberto, carregando dados para ordem ID:', this.currentOrderId);

                // Preencher o modal com os dados atuais, se disponíveis
                const custoPecas = document.getElementById('custoPecas');
                const custoMaoObra = document.getElementById('custoMaoObra');
                const custoDeslocamento = document.getElementById('custoDeslocamento');
                const custoTotal = document.getElementById('custoTotal');

                // Limpar os campos para evitar dados antigos
                if (custoPecas) custoPecas.value = '0.00';
                if (custoMaoObra) custoMaoObra.value = '0.00';
                if (custoDeslocamento) custoDeslocamento.value = '0.00';
                if (custoTotal) custoTotal.value = '0.00';

                // Tentar obter os dados atuais da ordem
                if (window.OrdemTecnicoAPI) {
                    window.OrdemTecnicoAPI.getOrdem(this.currentOrderId)
                        .then(response => {
                            if (response.success && response.data && response.data.custos) {
                                console.log('Dados de custos obtidos:', response.data.custos);
                                if (custoPecas) custoPecas.value = response.data.custos.pecas || '0.00';
                                if (custoMaoObra) custoMaoObra.value = response.data.custos.mao_obra || '0.00';
                                if (custoDeslocamento) custoDeslocamento.value = response.data.custos.deslocamento || '0.00';
                                if (custoTotal) custoTotal.value = response.data.custos.total || '0.00';
                            } else {
                                console.log('Nenhum dado de custos encontrado ou resposta inválida');
                            }
                        })
                        .catch(error => {
                            console.error('Erro ao obter dados da ordem:', error);
                            // Usar dados simulados para demonstração
                            if (custoPecas) custoPecas.value = '150.00';
                            if (custoMaoObra) custoMaoObra.value = '200.00';
                            if (custoDeslocamento) custoDeslocamento.value = '50.00';
                            if (custoTotal) custoTotal.value = '400.00';
                        });
                }
            });
        }

        // Configurar cálculo automático do custo total
        const custoPecas = document.getElementById('custoPecas');
        const custoMaoObra = document.getElementById('custoMaoObra');
        const custoDeslocamento = document.getElementById('custoDeslocamento');
        const custoTotal = document.getElementById('custoTotal');

        const calcularTotal = () => {
            if (custoPecas && custoMaoObra && custoDeslocamento && custoTotal) {
                const pecas = parseFloat(custoPecas.value) || 0;
                const maoObra = parseFloat(custoMaoObra.value) || 0;
                const deslocamento = parseFloat(custoDeslocamento.value) || 0;

                const total = pecas + maoObra + deslocamento;
                custoTotal.value = total.toFixed(2);
            }
        };

        // Adicionar event listeners para calcular o total quando os valores mudarem
        if (custoPecas) custoPecas.addEventListener('input', calcularTotal);
        if (custoMaoObra) custoMaoObra.addEventListener('input', calcularTotal);
        if (custoDeslocamento) custoDeslocamento.addEventListener('input', calcularTotal);

        // Configurar botão de salvar
        const salvarCustos = document.getElementById('salvarCustos');
        if (!salvarCustos) {
            console.error('Botão salvarCustos não encontrado');
            return;
        }

        // Remover event listeners existentes
        const newSalvarCustos = salvarCustos.cloneNode(true);
        salvarCustos.parentNode.replaceChild(newSalvarCustos, salvarCustos);

        // Adicionar novo event listener
        newSalvarCustos.addEventListener('click', () => {
            console.log('Botão salvar custos clicado para ordem ID:', this.currentOrderId);

            // Validação dos campos
            const pecas = parseFloat(custoPecas.value) || 0;
            const maoObra = parseFloat(custoMaoObra.value) || 0;
            const deslocamento = parseFloat(custoDeslocamento.value) || 0;
            const total = pecas + maoObra + deslocamento;



            // Preparar dados para envio
            const data = {
                ordem_id: this.currentOrderId,
                pecas: pecas,
                mao_obra: maoObra,
                deslocamento: deslocamento,
                total: total
            };

            console.log('Enviando dados de custos:', data);

            // Salvar dados via API
            window.OrdemTecnicoAPI.saveCustos(this.currentOrderId, data)
                .then(response => {
                    if (response.success) {
                        console.log('Dados de custos salvos com sucesso');

                        // Atualizar o preview no card
                        const custosPreview = document.getElementById('custos-preview');
                        if (custosPreview) {
                            custosPreview.innerHTML = `
                                <div class="d-flex justify-content-between">
                                    <span>Peças:</span>
                                    <span>R$ ${pecas.toFixed(2)}</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Mão de obra:</span>
                                    <span>R$ ${maoObra.toFixed(2)}</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Deslocamento:</span>
                                    <span>R$ ${deslocamento.toFixed(2)}</span>
                                </div>
                                <div class="d-flex justify-content-between mt-2 text-warning fw-bold">
                                    <span>Total:</span>
                                    <span>R$ ${total.toFixed(2)}</span>
                                </div>
                            `;
                        }

                        // Fechar o modal
                        const modal = bootstrap.Modal.getInstance(document.getElementById('custosModal'));
                        modal.hide();

                        // Mostrar notificação de sucesso
                        this.showNotification('Informações de custos salvas com sucesso!', 'success');

                        // Recarregar os detalhes da ordem para atualizar a interface
                        if (window.OrdemTecnicoDetails) {
                            window.OrdemTecnicoDetails.loadOrderDetails(this.currentOrderId);
                        }
                    } else {
                        console.error('Erro ao salvar dados de custos:', response.message);
                        this.showNotification('Erro ao salvar: ' + response.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Erro ao salvar dados de custos:', error);
                    this.showNotification('Erro ao salvar: ' + error.message, 'error');

                    // Para fins de demonstração, simular sucesso
                    const custosPreview = document.getElementById('custos-preview');
                    if (custosPreview) {
                        custosPreview.innerHTML = `
                            <div class="d-flex justify-content-between">
                                <span>Peças:</span>
                                <span>R$ ${pecas.toFixed(2)}</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Mão de obra:</span>
                                <span>R$ ${maoObra.toFixed(2)}</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Deslocamento:</span>
                                <span>R$ ${deslocamento.toFixed(2)}</span>
                            </div>
                            <div class="d-flex justify-content-between mt-2 text-warning fw-bold">
                                <span>Total:</span>
                                <span>R$ ${total.toFixed(2)}</span>
                            </div>
                        `;
                    }

                    // Fechar o modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('custosModal'));
                    modal.hide();

                    // Mostrar notificação de sucesso (simulada)
                    this.showNotification('Informações de custos salvas com sucesso! (simulado)', 'success');
                });
        });
    }

    // Configura o modal de cronograma
    setupCronogramaModal() {
        const salvarCronograma = document.getElementById('salvarCronograma');
        if (!salvarCronograma) return;

        salvarCronograma.addEventListener('click', () => {
            // Validação dos campos
            const dataInicio = document.getElementById('dataInicio').value;
            const horaInicio = document.getElementById('horaInicio').value;
            const dataFim = document.getElementById('dataFim').value;
            const horaFim = document.getElementById('horaFim').value;
            const status = document.getElementById('statusServico').value;

            if (!dataInicio || !horaInicio || !dataFim || !horaFim || status === 'Selecione...') {
                this.showNotification('Por favor, preencha todos os campos do cronograma.', 'warning');
                return;
            }

            // Verificar se a data de fim é posterior à data de início
            const inicio = new Date(`${dataInicio}T${horaInicio}`);
            const fim = new Date(`${dataFim}T${horaFim}`);

            if (fim <= inicio) {
                this.showNotification('A data/hora de fim deve ser posterior à data/hora de início.', 'warning');
                return;
            }



            // Preparar dados para envio
            const data = {
                ordem_id: this.currentOrderId,
                data_inicio: dataInicio,
                hora_inicio: horaInicio,
                data_fim: dataFim,
                hora_fim: horaFim,
                status: status
            };

            // Salvar dados via API
            window.OrdemTecnicoAPI.saveCronograma(this.currentOrderId, data)
                .then(response => {
                    if (response.success) {
                        // Formatar as datas para exibição
                        const dataInicioFormatada = this.formatarData(dataInicio);
                        const dataFimFormatada = this.formatarData(dataFim);

                        // Atualizar o preview no card
                        const scheduleStart = document.getElementById('schedule-start');
                        const scheduleEnd = document.getElementById('schedule-end');
                        const scheduleDuration = document.getElementById('schedule-duration');

                        if (scheduleStart && scheduleEnd) {
                            scheduleStart.textContent = `${dataInicioFormatada} ${horaInicio}`;
                            scheduleEnd.textContent = `${dataFimFormatada} ${horaFim}`;

                            // Calcular duração
                            const duracao = this.calcularDuracao(inicio, fim);
                            if (scheduleDuration) {
                                scheduleDuration.textContent = duracao;
                            }
                        }

                        // Fechar o modal
                        const modal = bootstrap.Modal.getInstance(document.getElementById('cronogramaModal'));
                        modal.hide();

                        // Mostrar notificação de sucesso
                        this.showNotification('Informações de cronograma salvas com sucesso!', 'success');
                    } else {
                        this.showNotification('Erro ao salvar: ' + response.message, 'error');
                    }
                })
                .catch(error => {
                    this.showNotification('Erro ao salvar: ' + error.message, 'error');
                });
        });
    }

    // Configura o modal de interação
    setupInteracaoModal() {
        const sendChatMessage = document.getElementById('sendChatMessage');
        if (!sendChatMessage) return;

        sendChatMessage.addEventListener('click', () => {
            const chatMessageInput = document.getElementById('chatMessageInput');
            if (!chatMessageInput) return;

            const message = chatMessageInput.value.trim();
            if (!message) {
                this.showNotification('Por favor, digite uma mensagem.', 'warning');
                return;
            }



            // Enviar mensagem via API
            window.OrdemTecnicoAPI.saveChatMessage(this.currentOrderId, message)
                .then(response => {
                    if (response.success) {
                        // Adicionar mensagem ao chat
                        this.addChatMessage('Você', message, new Date());

                        // Limpar o campo de mensagem
                        chatMessageInput.value = '';

                        // Rolar para o final do chat
                        const chatArea = document.getElementById('chatArea');
                        if (chatArea) {
                            chatArea.scrollTop = chatArea.scrollHeight;
                        }
                    } else {
                        this.showNotification('Erro ao enviar mensagem: ' + response.message, 'error');
                    }
                })
                .catch(error => {
                    this.showNotification('Erro ao enviar mensagem: ' + error.message, 'error');
                });
        });

        // Permitir enviar mensagem com Enter
        const chatMessageInput = document.getElementById('chatMessageInput');
        if (chatMessageInput) {
            chatMessageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendChatMessage.click();
                }
            });
        }
    }

    // Adiciona uma mensagem ao chat
    addChatMessage(sender, message, date) {
        const chatArea = document.getElementById('chatArea');
        if (!chatArea) return;

        const messageElement = document.createElement('div');
        messageElement.className = sender === 'Você' ?
            'chat-message mb-2 text-end' :
            'chat-message mb-2 text-start';

        const time = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;

        messageElement.innerHTML = sender === 'Você' ?
            `<small class="text-info">(${time}) ${sender}:</small>
            <p class="m-0 bg-primary p-2 rounded d-inline-block">${message}</p>` :
            `<small class="text-warning">${sender} (${time}):</small>
            <p class="m-0 bg-secondary p-2 rounded d-inline-block">${message}</p>`;

        chatArea.appendChild(messageElement);
        chatArea.scrollTop = chatArea.scrollHeight;
    }

    // Formata uma data para exibição
    formatarData(dataString) {
        const data = new Date(dataString);
        return `${data.getDate().toString().padStart(2, '0')}/${(data.getMonth() + 1).toString().padStart(2, '0')}/${data.getFullYear()}`;
    }

    // Calcula a duração entre duas datas
    calcularDuracao(inicio, fim) {
        const diff = Math.abs(fim - inicio);
        const horas = Math.floor(diff / (1000 * 60 * 60));
        const minutos = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

        if (horas === 0) {
            return `${minutos} minutos`;
        } else if (minutos === 0) {
            return `${horas} horas`;
        } else {
            return `${horas}h ${minutos}min`;
        }
    }

    // Mostra uma notificação
    showNotification(message, type = 'info') {
        // Verifica se já existe uma notificação
        const existingNotification = document.querySelector('.notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        // Cria a notificação
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        // Define o ícone com base no tipo
        let icon = 'info-circle';
        if (type === 'success') icon = 'check-circle';
        if (type === 'warning') icon = 'exclamation-triangle';
        if (type === 'error') icon = 'times-circle';

        notification.innerHTML = `
            <div class="notification-icon">
                <i class="fas fa-${icon}"></i>
            </div>
            <div class="notification-content">
                <p>${message}</p>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Adiciona a notificação ao corpo do documento
        document.body.appendChild(notification);

        // Configura o botão de fechar
        const closeButton = notification.querySelector('.notification-close');
        closeButton.addEventListener('click', () => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                notification.remove();
            }, 500);
        });

        // Remove a notificação após 5 segundos
        setTimeout(() => {
            if (document.body.contains(notification)) {
                notification.classList.add('fade-out');
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        notification.remove();
                    }
                }, 500);
            }
        }, 5000);
    }
}

// Exporta a classe para uso global
window.OrdemTecnicoModals = new OrdemTecnicoModals();
