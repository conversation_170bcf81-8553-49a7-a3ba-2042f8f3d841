/**
 * Módulo para gerenciar transferências de equipamentos
 */
const EquipmentTransfer = (function() {
    // Elementos do DOM
    const transferCardElement = document.getElementById('transferCard');
    const transferButtonElement = document.getElementById('btnTransferirEquipamento');
    const transferModalElement = document.getElementById('modalTransferirEquipamento');
    const transferFormElement = document.getElementById('formTransferirEquipamento');
    const equipmentSelectElement = document.getElementById('equipmentSelect');
    const destinationBranchSelectElement = document.getElementById('destinationBranchSelect');
    const justificationElement = document.getElementById('justification');
    const authorizedByElement = document.getElementById('authorizedBy');
    const pendingTransfersContainerElement = document.getElementById('pendingTransfersContainer');
    const incomingTransfersContainerElement = document.getElementById('incomingTransfersContainer');

    // Variáveis de estado
    let currentBranchId = null;
    let equipments = [];
    let branches = [];
    let pendingTransfers = [];
    let incomingTransfers = [];

    /**
     * Inicializa o módulo de transferência de equipamentos
     * @param {number} branchId - ID da filial atual
     */
    function init(branchId) {
        currentBranchId = branchId;
        
        // Verificar se os elementos existem antes de adicionar event listeners
        if (transferButtonElement) {
            transferButtonElement.addEventListener('click', showTransferModal);
        }
        
        if (transferFormElement) {
            transferFormElement.addEventListener('submit', handleTransferSubmit);
        }
        
        // Carregar dados iniciais
        loadEquipments();
        loadBranches();
        loadPendingTransfers();
        loadIncomingTransfers();
        
        // Verificar se há um parâmetro de transferência na URL
        const urlParams = new URLSearchParams(window.location.search);
        const transferId = urlParams.get('transfer');
        if (transferId) {
            loadTransferDetails(transferId);
        }
    }

    /**
     * Carrega os equipamentos da filial atual
     */
    function loadEquipments() {
        fetch(`/api/equipments?branch_id=${currentBranchId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erro ao carregar equipamentos');
                }
                return response.json();
            })
            .then(data => {
                equipments = data;
                populateEquipmentSelect();
            })
            .catch(error => {
                console.error('Erro ao carregar equipamentos:', error);
                displayErrorMessage(equipmentSelectElement, 'Erro ao carregar equipamentos. Por favor, tente novamente.');
                showToast('Erro ao carregar equipamentos', 'error');
            });
    }

    /**
     * Carrega as filiais disponíveis
     */
    function loadBranches() {
        fetch('/api/branches')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erro ao carregar filiais');
                }
                return response.json();
            })
            .then(data => {
                // Filtrar a filial atual
                branches = data.filter(branch => branch.id !== currentBranchId);
                populateBranchSelect();
            })
            .catch(error => {
                console.error('Erro ao carregar filiais:', error);
                displayErrorMessage(destinationBranchSelectElement, 'Erro ao carregar filiais. Por favor, tente novamente.');
                showToast('Erro ao carregar filiais', 'error');
            });
    }

    /**
     * Exibe mensagem de erro visível em um elemento select
     * @param {HTMLElement} selectElement - Elemento select para exibir a mensagem
     * @param {string} message - Mensagem de erro a ser exibida
     */
    function displayErrorMessage(selectElement, message) {
        if (!selectElement) return;
        selectElement.innerHTML = '';
        const option = document.createElement('option');
        option.value = '';
        option.textContent = message;
        option.disabled = true;
        option.selected = true;
        selectElement.appendChild(option);
    }

    /**
     * Carrega as transferências pendentes da filial atual
     */
    function loadPendingTransfers() {
        fetch(`/api/equipment-transfers/branch/${currentBranchId}?direction=source`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erro ao carregar transferências pendentes');
                }
                return response.json();
            })
            .then(data => {
                pendingTransfers = data;
                renderPendingTransfers();
            })
            .catch(error => {
                console.error('Erro ao carregar transferências pendentes:', error);
            });
    }

    /**
     * Carrega as transferências pendentes para a filial atual
     */
    function loadIncomingTransfers() {
        fetch(`/api/equipment-transfers/branch/${currentBranchId}?direction=destination`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erro ao carregar transferências recebidas');
                }
                return response.json();
            })
            .then(data => {
                incomingTransfers = data;
                renderIncomingTransfers();
            })
            .catch(error => {
                console.error('Erro ao carregar transferências recebidas:', error);
            });
    }

    /**
     * Carrega os detalhes de uma transferência específica
     * @param {number} transferId - ID da transferência
     */
    function loadTransferDetails(transferId) {
        fetch(`/api/equipment-transfers/${transferId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erro ao carregar detalhes da transferência');
                }
                return response.json();
            })
            .then(data => {
                showTransferDetailsModal(data);
            })
            .catch(error => {
                console.error('Erro ao carregar detalhes da transferência:', error);
                showToast('Erro ao carregar detalhes da transferência', 'error');
            });
    }

    /**
     * Preenche o select de equipamentos
     */
    function populateEquipmentSelect() {
        if (!equipmentSelectElement) return;
        
        equipmentSelectElement.innerHTML = '<option value="">Selecione um equipamento</option>';
        
        equipments.forEach(equipment => {
            const option = document.createElement('option');
            option.value = equipment.id;
            option.textContent = `${equipment.name} (${equipment.serial_number})`;
            equipmentSelectElement.appendChild(option);
        });
    }

    /**
     * Preenche o select de filiais de destino
     */
    function populateBranchSelect() {
        if (!destinationBranchSelectElement) return;
        
        destinationBranchSelectElement.innerHTML = '<option value="">Selecione uma filial</option>';
        
        branches.forEach(branch => {
            const option = document.createElement('option');
            option.value = branch.id;
            option.textContent = branch.name;
            destinationBranchSelectElement.appendChild(option);
        });
    }

    /**
     * Renderiza as transferências pendentes
     */
    function renderPendingTransfers() {
        if (!pendingTransfersContainerElement) return;
        
        if (pendingTransfers.length === 0) {
            pendingTransfersContainerElement.innerHTML = '<p class="text-center">Não há transferências pendentes.</p>';
            return;
        }
        
        let html = '<div class="list-group">';
        
        pendingTransfers.forEach(transfer => {
            const equipment = transfer.equipment || { name: 'Equipamento não encontrado' };
            const destination = transfer.destination_branch || { name: 'Filial não encontrada' };
            
            html += `
                <div class="list-group-item list-group-item-action">
                    <div class="d-flex w-100 justify-content-between">
                        <h5 class="mb-1">${equipment.name}</h5>
                        <small class="text-muted">Solicitado em ${new Date(transfer.requested_at).toLocaleDateString()}</small>
                    </div>
                    <p class="mb-1">Destino: ${destination.name}</p>
                    <p class="mb-1">Status: <span class="badge bg-warning">${transfer.status}</span></p>
                    <div class="d-flex justify-content-end">
                        <button class="btn btn-sm btn-outline-danger me-2" onclick="EquipmentTransfer.cancelTransfer(${transfer.id})">Cancelar</button>
                        <button class="btn btn-sm btn-outline-primary" onclick="EquipmentTransfer.viewTransferDetails(${transfer.id})">Detalhes</button>
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        pendingTransfersContainerElement.innerHTML = html;
    }

    /**
     * Renderiza as transferências recebidas
     */
    function renderIncomingTransfers() {
        if (!incomingTransfersContainerElement) return;
        
        if (incomingTransfers.length === 0) {
            incomingTransfersContainerElement.innerHTML = '<p class="text-center">Não há transferências pendentes para aprovação.</p>';
            return;
        }
        
        let html = '<div class="list-group">';
        
        incomingTransfers.forEach(transfer => {
            const equipment = transfer.equipment || { name: 'Equipamento não encontrado' };
            const source = transfer.source_branch || { name: 'Filial não encontrada' };
            
            html += `
                <div class="list-group-item list-group-item-action">
                    <div class="d-flex w-100 justify-content-between">
                        <h5 class="mb-1">${equipment.name}</h5>
                        <small class="text-muted">Solicitado em ${new Date(transfer.requested_at).toLocaleDateString()}</small>
                    </div>
                    <p class="mb-1">Origem: ${source.name}</p>
                    <p class="mb-1">Status: <span class="badge bg-warning">${transfer.status}</span></p>
                    <div class="d-flex justify-content-end">
                        <button class="btn btn-sm btn-outline-danger me-2" onclick="EquipmentTransfer.rejectTransfer(${transfer.id})">Rejeitar</button>
                        <button class="btn btn-sm btn-success" onclick="EquipmentTransfer.approveTransfer(${transfer.id})">Aprovar</button>
                        <button class="btn btn-sm btn-outline-primary ms-2" onclick="EquipmentTransfer.viewTransferDetails(${transfer.id})">Detalhes</button>
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        incomingTransfersContainerElement.innerHTML = html;
    }

    /**
     * Exibe o modal de transferência de equipamento
     */
    function showTransferModal() {
        if (!transferModalElement) return;
        
        // Limpar o formulário
        if (transferFormElement) {
            transferFormElement.reset();
        }

        // Limpar mensagens de erro anteriores
        displayErrorMessage(equipmentSelectElement, 'Selecione um equipamento');
        displayErrorMessage(destinationBranchSelectElement, 'Selecione uma filial');
        
        // Exibir o modal
        const modal = new bootstrap.Modal(transferModalElement);
        modal.show();
    }

    /**
     * Exibe o modal de detalhes da transferência
     * @param {Object} transfer - Dados da transferência
     */
    function showTransferDetailsModal(transfer) {
        // Verificar se o modal existe
        const modalElement = document.getElementById('modalDetalhesTransferencia');
        if (!modalElement) return;
        
        // Preencher os dados do modal
        const equipment = transfer.equipment || { name: 'Equipamento não encontrado' };
        const source = transfer.source_branch || { name: 'Filial não encontrada' };
        const destination = transfer.destination_branch || { name: 'Filial não encontrada' };
        const requestedBy = transfer.requested_by_user || { name: 'Usuário não encontrado' };
        
        document.getElementById('detailsEquipmentName').textContent = equipment.name;
        document.getElementById('detailsSourceBranch').textContent = source.name;
        document.getElementById('detailsDestinationBranch').textContent = destination.name;
        document.getElementById('detailsRequestedBy').textContent = requestedBy.name;
        document.getElementById('detailsRequestedAt').textContent = new Date(transfer.requested_at).toLocaleString();
        document.getElementById('detailsStatus').textContent = transfer.status;
        document.getElementById('detailsJustification').textContent = transfer.justification;
        document.getElementById('detailsAuthorizedBy').textContent = transfer.authorized_by;
        
        if (transfer.notes) {
            document.getElementById('detailsNotes').textContent = transfer.notes;
            document.getElementById('detailsNotesContainer').classList.remove('d-none');
        } else {
            document.getElementById('detailsNotesContainer').classList.add('d-none');
        }
        
        // Exibir o modal
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    }

    /**
     * Manipula o envio do formulário de transferência
     * @param {Event} event - Evento de submit
     */
    function handleTransferSubmit(event) {
        event.preventDefault();
        
        const equipmentId = equipmentSelectElement.value;
        const destinationBranchId = destinationBranchSelectElement.value;
        const justification = justificationElement.value;
        const authorizedBy = authorizedByElement.value;
        
        // Validar campos
        if (!equipmentId || !destinationBranchId || !justification || !authorizedBy) {
            showToast('Preencha todos os campos obrigatórios', 'error');
            return;
        }
        
        // Dados da transferência
        const transferData = {
            equipment_id: parseInt(equipmentId),
            destination_branch_id: parseInt(destinationBranchId),
            justification: justification,
            authorized_by: authorizedBy
        };
        
        // Enviar solicitação
        fetch('/api/equipment-transfers', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(transferData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Erro ao solicitar transferência');
            }
            return response.json();
        })
        .then(data => {
            // Fechar o modal
            const modal = bootstrap.Modal.getInstance(transferModalElement);
            modal.hide();
            
            // Atualizar a lista de transferências
            loadPendingTransfers();
            
            // Exibir mensagem de sucesso
            showToast('Transferência solicitada com sucesso', 'success');
        })
        .catch(error => {
            console.error('Erro ao solicitar transferência:', error);
            showToast('Erro ao solicitar transferência', 'error');
        });
    }

    /**
     * Aprova uma transferência
     * @param {number} transferId - ID da transferência
     */
    function approveTransfer(transferId) {
        // Solicitar notas para aprovação
        const notes = prompt('Informe observações para a aprovação (opcional):');
        
        fetch(`/api/equipment-transfers/${transferId}/approve`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ notes: notes || '' })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Erro ao aprovar transferência');
            }
            return response.json();
        })
        .then(data => {
            // Atualizar as listas de transferências
            loadIncomingTransfers();
            
            // Exibir mensagem de sucesso
            showToast('Transferência aprovada com sucesso', 'success');
        })
        .catch(error => {
            console.error('Erro ao aprovar transferência:', error);
            showToast('Erro ao aprovar transferência', 'error');
        });
    }

    /**
     * Rejeita uma transferência
     * @param {number} transferId - ID da transferência
     */
    function rejectTransfer(transferId) {
        // Solicitar motivo da rejeição
        const notes = prompt('Informe o motivo da rejeição:');
        if (!notes) {
            showToast('É necessário informar o motivo da rejeição', 'error');
            return;
        }
        
        fetch(`/api/equipment-transfers/${transferId}/reject`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ notes: notes })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Erro ao rejeitar transferência');
            }
            return response.json();
        })
        .then(data => {
            // Atualizar as listas de transferências
            loadIncomingTransfers();
            
            // Exibir mensagem de sucesso
            showToast('Transferência rejeitada com sucesso', 'success');
        })
        .catch(error => {
            console.error('Erro ao rejeitar transferência:', error);
            showToast('Erro ao rejeitar transferência', 'error');
        });
    }

    /**
     * Cancela uma transferência
     * @param {number} transferId - ID da transferência
     */
    function cancelTransfer(transferId) {
        // Solicitar motivo do cancelamento
        const notes = prompt('Informe o motivo do cancelamento:');
        if (!notes) {
            showToast('É necessário informar o motivo do cancelamento', 'error');
            return;
        }
        
        fetch(`/api/equipment-transfers/${transferId}/cancel`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ notes: notes })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Erro ao cancelar transferência');
            }
            return response.json();
        })
        .then(data => {
            // Atualizar as listas de transferências
            loadPendingTransfers();
            
            // Exibir mensagem de sucesso
            showToast('Transferência cancelada com sucesso', 'success');
        })
        .catch(error => {
            console.error('Erro ao cancelar transferência:', error);
            showToast('Erro ao cancelar transferência', 'error');
        });
    }

    /**
     * Exibe os detalhes de uma transferência
     * @param {number} transferId - ID da transferência
     */
    function viewTransferDetails(transferId) {
        loadTransferDetails(transferId);
    }

    /**
     * Exibe uma mensagem toast
     * @param {string} message - Mensagem a ser exibida
     * @param {string} type - Tipo da mensagem (success, error, warning, info)
     */
    function showToast(message, type = 'info') {
        // Verificar se a função toast existe no escopo global
        if (typeof toast === 'function') {
            toast(message, type);
        } else {
            // Fallback para alert
            alert(message);
        }
    }

    // API pública do módulo
    return {
        init: init,
        approveTransfer: approveTransfer,
        rejectTransfer: rejectTransfer,
        cancelTransfer: cancelTransfer,
        viewTransferDetails: viewTransferDetails
    };
})();

// Inicializar o módulo quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    // Verificar se estamos na página de minha conta
    const branchIdElement = document.getElementById('currentBranchId');
    if (branchIdElement) {
        const branchId = parseInt(branchIdElement.value);
        if (branchId) {
            EquipmentTransfer.init(branchId);
        }
    }
});
