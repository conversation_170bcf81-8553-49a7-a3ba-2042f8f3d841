/**
 * Correção para o botão de recolher/expandir do sidebar
 * Este script corrige a funcionalidade sem modificar o visual/layout
 */
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.querySelector('.tradicio-sidebar');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const content = document.querySelector('.content-with-sidebar');
    
    if (sidebar && sidebarToggle) {
        // Adicionar evento de clique ao botão
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            if (content) content.classList.toggle('collapsed');
            
            // Salvar estado no localStorage
            localStorage.setItem('sidebar-collapsed', sidebar.classList.contains('collapsed'));
        });
        
        // Carregar estado do sidebar do localStorage
        const sidebarCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
        if (sidebarCollapsed) {
            sidebar.classList.add('collapsed');
            if (content) content.classList.add('collapsed');
        }
    }
});
