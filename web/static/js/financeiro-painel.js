/**
 * Cria o conteúdo da ferramenta de Impostos
 * @param {HTMLElement} container - Container onde o conteúdo será inserido
 */
function createImpostosContent(container) {
    container.innerHTML = `
        <div class="tool-section">
            <h3 class="tool-section-title">
                <i class="fas fa-file-invoice-dollar"></i> Consulta de Impostos
            </h3>
            <div class="tool-form">
                <div class="form-group">
                    <label>Estado</label>
                    <select id="estadoSelect">
                        <option value="">Selecione um estado</option>
                        <option value="AC">Acre (AC)</option>
                        <option value="AL">Alagoas (AL)</option>
                        <option value="AP">Amapá (AP)</option>
                        <option value="AM">Amazonas (AM)</option>
                        <option value="BA">Bahia (BA)</option>
                        <option value="CE">Ceará (CE)</option>
                        <option value="DF">Distrito Federal (DF)</option>
                        <option value="ES">Espírito Santo (ES)</option>
                        <option value="GO">Goiás (GO)</option>
                        <option value="MA">Maranhão (MA)</option>
                        <option value="MT">Mato Grosso (MT)</option>
                        <option value="MS">Mato Grosso do Sul (MS)</option>
                        <option value="MG">Minas Gerais (MG)</option>
                        <option value="PA">Pará (PA)</option>
                        <option value="PB">Paraíba (PB)</option>
                        <option value="PR">Paraná (PR)</option>
                        <option value="PE">Pernambuco (PE)</option>
                        <option value="PI">Piauí (PI)</option>
                        <option value="RJ">Rio de Janeiro (RJ)</option>
                        <option value="RN">Rio Grande do Norte (RN)</option>
                        <option value="RS">Rio Grande do Sul (RS)</option>
                        <option value="RO">Rondônia (RO)</option>
                        <option value="RR">Roraima (RR)</option>
                        <option value="SC">Santa Catarina (SC)</option>
                        <option value="SP">São Paulo (SP)</option>
                        <option value="SE">Sergipe (SE)</option>
                        <option value="TO">Tocantins (TO)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Tipo de Combustível</label>
                    <select id="combustivelSelect">
                        <option value="">Selecione um combustível</option>
                        <option value="gasolina">Gasolina Comum</option>
                        <option value="gasolina_aditivada">Gasolina Aditivada</option>
                        <option value="etanol">Etanol</option>
                        <option value="diesel">Diesel Comum</option>
                        <option value="diesel_s10">Diesel S10</option>
                        <option value="gnv">GNV</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button class="btn-shell-yellow" id="consultarBtn">Consultar</button>
                </div>
            </div>
        </div>

        <div class="tool-section" id="resultadosImpostos" style="display: none;">
            <h3 class="tool-section-title">
                <i class="fas fa-percentage"></i> Alíquotas de Impostos
            </h3>
            <div class="impostos-cards">
                <div class="imposto-card">
                    <div class="imposto-title">ICMS</div>
                    <div class="imposto-value" id="valorICMS">0%</div>
                    <div class="imposto-desc">Imposto sobre Circulação de Mercadorias e Serviços</div>
                </div>
                <div class="imposto-card">
                    <div class="imposto-title">PIS</div>
                    <div class="imposto-value" id="valorPIS">0%</div>
                    <div class="imposto-desc">Programa de Integração Social</div>
                </div>
                <div class="imposto-card">
                    <div class="imposto-title">COFINS</div>
                    <div class="imposto-value" id="valorCOFINS">0%</div>
                    <div class="imposto-desc">Contribuição para o Financiamento da Seguridade Social</div>
                </div>
                <div class="imposto-card">
                    <div class="imposto-title">CIDE</div>
                    <div class="imposto-value" id="valorCIDE">0%</div>
                    <div class="imposto-desc">Contribuição de Intervenção no Domínio Econômico</div>
                </div>
            </div>

            <div class="chart-container">
                <canvas id="impostosChart" width="100%" height="250"></canvas>
            </div>

            <div class="impostos-total">
                <div class="total-title">Carga Tributária Total</div>
                <div class="total-value" id="totalImpostos">0%</div>
                <div class="total-desc">Percentual total de impostos sobre o preço final</div>
            </div>
        </div>
    `;

    // Dados simulados de impostos por estado e combustível
    const dadosImpostos = {
        SP: {
            gasolina: { ICMS: 25, PIS: 1.65, COFINS: 7.6, CIDE: 1.5 },
            gasolina_aditivada: { ICMS: 25, PIS: 1.65, COFINS: 7.6, CIDE: 1.5 },
            etanol: { ICMS: 12, PIS: 1.5, COFINS: 7.0, CIDE: 0 },
            diesel: { ICMS: 13.3, PIS: 1.65, COFINS: 7.6, CIDE: 0.9 },
            diesel_s10: { ICMS: 13.3, PIS: 1.65, COFINS: 7.6, CIDE: 0.9 },
            gnv: { ICMS: 12, PIS: 1.65, COFINS: 7.6, CIDE: 0 }
        },
        RJ: {
            gasolina: { ICMS: 34, PIS: 1.65, COFINS: 7.6, CIDE: 1.5 },
            gasolina_aditivada: { ICMS: 34, PIS: 1.65, COFINS: 7.6, CIDE: 1.5 },
            etanol: { ICMS: 18, PIS: 1.5, COFINS: 7.0, CIDE: 0 },
            diesel: { ICMS: 16, PIS: 1.65, COFINS: 7.6, CIDE: 0.9 },
            diesel_s10: { ICMS: 16, PIS: 1.65, COFINS: 7.6, CIDE: 0.9 },
            gnv: { ICMS: 12, PIS: 1.65, COFINS: 7.6, CIDE: 0 }
        },
        MG: {
            gasolina: { ICMS: 31, PIS: 1.65, COFINS: 7.6, CIDE: 1.5 },
            gasolina_aditivada: { ICMS: 31, PIS: 1.65, COFINS: 7.6, CIDE: 1.5 },
            etanol: { ICMS: 16, PIS: 1.5, COFINS: 7.0, CIDE: 0 },
            diesel: { ICMS: 15, PIS: 1.65, COFINS: 7.6, CIDE: 0.9 },
            diesel_s10: { ICMS: 15, PIS: 1.65, COFINS: 7.6, CIDE: 0.9 },
            gnv: { ICMS: 13, PIS: 1.65, COFINS: 7.6, CIDE: 0 }
        },
        RS: {
            gasolina: { ICMS: 30, PIS: 1.65, COFINS: 7.6, CIDE: 1.5 },
            gasolina_aditivada: { ICMS: 30, PIS: 1.65, COFINS: 7.6, CIDE: 1.5 },
            etanol: { ICMS: 13, PIS: 1.5, COFINS: 7.0, CIDE: 0 },
            diesel: { ICMS: 12, PIS: 1.65, COFINS: 7.6, CIDE: 0.9 },
            diesel_s10: { ICMS: 12, PIS: 1.65, COFINS: 7.6, CIDE: 0.9 },
            gnv: { ICMS: 12, PIS: 1.65, COFINS: 7.6, CIDE: 0 }
        },
        PR: {
            gasolina: { ICMS: 29, PIS: 1.65, COFINS: 7.6, CIDE: 1.5 },
            gasolina_aditivada: { ICMS: 29, PIS: 1.65, COFINS: 7.6, CIDE: 1.5 },
            etanol: { ICMS: 18, PIS: 1.5, COFINS: 7.0, CIDE: 0 },
            diesel: { ICMS: 12, PIS: 1.65, COFINS: 7.6, CIDE: 0.9 },
            diesel_s10: { ICMS: 12, PIS: 1.65, COFINS: 7.6, CIDE: 0.9 },
            gnv: { ICMS: 12, PIS: 1.65, COFINS: 7.6, CIDE: 0 }
        },
        BA: {
            gasolina: { ICMS: 28, PIS: 1.65, COFINS: 7.6, CIDE: 1.5 },
            gasolina_aditivada: { ICMS: 28, PIS: 1.65, COFINS: 7.6, CIDE: 1.5 },
            etanol: { ICMS: 19, PIS: 1.5, COFINS: 7.0, CIDE: 0 },
            diesel: { ICMS: 18, PIS: 1.65, COFINS: 7.6, CIDE: 0.9 },
            diesel_s10: { ICMS: 18, PIS: 1.65, COFINS: 7.6, CIDE: 0.9 },
            gnv: { ICMS: 12, PIS: 1.65, COFINS: 7.6, CIDE: 0 }
        }
    };

    // Para outros estados, usar valores padrão
    const estadosPadrao = {
        gasolina: { ICMS: 27, PIS: 1.65, COFINS: 7.6, CIDE: 1.5 },
        gasolina_aditivada: { ICMS: 27, PIS: 1.65, COFINS: 7.6, CIDE: 1.5 },
        etanol: { ICMS: 16, PIS: 1.5, COFINS: 7.0, CIDE: 0 },
        diesel: { ICMS: 14, PIS: 1.65, COFINS: 7.6, CIDE: 0.9 },
        diesel_s10: { ICMS: 14, PIS: 1.65, COFINS: 7.6, CIDE: 0.9 },
        gnv: { ICMS: 12, PIS: 1.65, COFINS: 7.6, CIDE: 0 }
    };

    // Adicionar evento ao botão de consulta
    const consultarBtn = container.querySelector('#consultarBtn');
    consultarBtn.addEventListener('click', function() {
        const estado = container.querySelector('#estadoSelect').value;
        const combustivel = container.querySelector('#combustivelSelect').value;

        if (!estado || !combustivel) {
            alert('Por favor, selecione um estado e um tipo de combustível.');
            return;
        }

        // Obter dados de impostos
        const impostos = dadosImpostos[estado] ?
            dadosImpostos[estado][combustivel] :
            estadosPadrao[combustivel];

        // Atualizar valores na interface
        container.querySelector('#valorICMS').textContent = impostos.ICMS + '%';
        container.querySelector('#valorPIS').textContent = impostos.PIS + '%';
        container.querySelector('#valorCOFINS').textContent = impostos.COFINS + '%';
        container.querySelector('#valorCIDE').textContent = impostos.CIDE + '%';

        // Calcular total
        const totalImpostos = impostos.ICMS + impostos.PIS + impostos.COFINS + impostos.CIDE;
        container.querySelector('#totalImpostos').textContent = totalImpostos.toFixed(2) + '%';

        // Mostrar resultados
        container.querySelector('#resultadosImpostos').style.display = 'block';

        // Criar gráfico
        createImpostosChart(impostos);
    });

    // Função para criar o gráfico de impostos
    function createImpostosChart(impostos) {
        const ctx = document.getElementById('impostosChart');
        if (!ctx) return;

        // Destruir gráfico existente se houver
        if (window.impostosChart) {
            window.impostosChart.destroy();
        }

        // Criar novo gráfico
        window.impostosChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['ICMS', 'PIS', 'COFINS', 'CIDE'],
                datasets: [{
                    data: [impostos.ICMS, impostos.PIS, impostos.COFINS, impostos.CIDE],
                    backgroundColor: ['#ED1C24', '#FDB813', '#0275d8', '#28a745'],
                    borderColor: '#333',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            color: '#ccc',
                            font: {
                                family: "'Rajdhani', sans-serif",
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(2);
                                return `${label}: ${value}% (${percentage}% do total de impostos)`;
                            }
                        }
                    }
                }
            }
        });
    }
}

/**
 * Cria o conteúdo da ferramenta de Calculadora de Margem
 * @param {HTMLElement} container - Container onde o conteúdo será inserido
 */
function createCalculadoraContent(container) {
    // Estrutura HTML da calculadora
    container.innerHTML = `
        <div class="tool-section">
            <h3 class="tool-section-title">
                <i class="fas fa-calculator"></i> Calculadora de Margem de Lucro
            </h3>
            <div class="tool-form">
                <!-- Seção de Custos Fixos -->
                <div class="form-group">
                    <label>Preço de Compra (R$/litro)</label>
                    <input type="number" id="precoCusto" step="0.01" value="4.25">
                </div>
                <div class="form-group">
                    <label>Preço de Venda (R$/litro)</label>
                    <input type="number" id="precoVenda" step="0.01" value="6.79">
                </div>

                <!-- Seção de Custos Variáveis -->
                <div class="form-section">
                    <h4>Custos Variáveis</h4>
                    <div id="custosVariaveis">
                        <div class="custo-item">
                            <div class="form-group">
                                <label>Transporte</label>
                                <input type="number" class="custo-valor" step="0.01" value="0.15">
                            </div>
                        </div>
                        <div class="custo-item">
                            <div class="form-group">
                                <label>Impostos</label>
                                <input type="number" class="custo-valor" step="0.01" value="1.25">
                            </div>
                        </div>
                    </div>
                    <button class="btn-outline-shell" id="addCustoBtn">
                        <i class="fas fa-plus"></i> Adicionar Custo
                    </button>
                </div>

                <div class="form-actions">
                    <button class="btn-shell-yellow" id="calcularBtn">Calcular</button>
                    <button class="btn-outline-shell" id="resetBtn">Limpar</button>
                </div>
            </div>
        </div>

        <!-- Resultados -->
        <div class="tool-section" id="resultadosSection" style="display: none;">
            <h3 class="tool-section-title">
                <i class="fas fa-chart-pie"></i> Resultados
            </h3>
            <div class="results-grid">
                <div class="result-card">
                    <div class="result-title">Margem Bruta</div>
                    <div class="result-value" id="margemBruta">R$ 0,00</div>
                    <div class="result-percent" id="margemBrutaPercent">0%</div>
                </div>
                <div class="result-card">
                    <div class="result-title">Margem Líquida</div>
                    <div class="result-value" id="margemLiquida">R$ 0,00</div>
                    <div class="result-percent" id="margemLiquidaPercent">0%</div>
                </div>
                <div class="result-card">
                    <div class="result-title">Total de Custos</div>
                    <div class="result-value" id="totalCustos">R$ 0,00</div>
                    <div class="result-percent" id="totalCustosPercent">0%</div>
                </div>
            </div>

            <div class="chart-container">
                <canvas id="margemChart" width="100%" height="250"></canvas>
            </div>
        </div>
    `;

    // Adicionar eventos
    const addCustoBtn = container.querySelector('#addCustoBtn');
    const calcularBtn = container.querySelector('#calcularBtn');
    const resetBtn = container.querySelector('#resetBtn');
    const custosVariaveis = container.querySelector('#custosVariaveis');

    // Evento para adicionar novo custo
    addCustoBtn.addEventListener('click', function() {
        const novoCusto = document.createElement('div');
        novoCusto.className = 'custo-item';
        novoCusto.innerHTML = `
            <div class="form-group">
                <label>Novo Custo</label>
                <div class="input-group">
                    <input type="text" class="custo-nome" placeholder="Nome do custo">
                    <input type="number" class="custo-valor" step="0.01" value="0.00">
                    <button class="btn-remove-custo"><i class="fas fa-times"></i></button>
                </div>
            </div>
        `;
        custosVariaveis.appendChild(novoCusto);

        // Adicionar evento para remover custo
        novoCusto.querySelector('.btn-remove-custo').addEventListener('click', function() {
            custosVariaveis.removeChild(novoCusto);
        });
    });

    // Evento para calcular margem
    calcularBtn.addEventListener('click', function() {
        const precoCusto = parseFloat(container.querySelector('#precoCusto').value) || 0;
        const precoVenda = parseFloat(container.querySelector('#precoVenda').value) || 0;

        // Calcular total de custos variáveis
        let totalCustosVariaveis = 0;
        const custoInputs = container.querySelectorAll('.custo-valor');
        custoInputs.forEach(input => {
            totalCustosVariaveis += parseFloat(input.value) || 0;
        });

        // Calcular margens
        const margemBruta = precoVenda - precoCusto;
        const margemLiquida = margemBruta - totalCustosVariaveis;
        const totalCustos = precoCusto + totalCustosVariaveis;

        // Calcular percentuais
        const margemBrutaPercent = (margemBruta / precoVenda) * 100;
        const margemLiquidaPercent = (margemLiquida / precoVenda) * 100;
        const totalCustosPercent = (totalCustos / precoVenda) * 100;

        // Atualizar resultados
        container.querySelector('#margemBruta').textContent = `R$ ${margemBruta.toFixed(2)}`;
        container.querySelector('#margemBrutaPercent').textContent = `${margemBrutaPercent.toFixed(2)}%`;

        container.querySelector('#margemLiquida').textContent = `R$ ${margemLiquida.toFixed(2)}`;
        container.querySelector('#margemLiquidaPercent').textContent = `${margemLiquidaPercent.toFixed(2)}%`;

        container.querySelector('#totalCustos').textContent = `R$ ${totalCustos.toFixed(2)}`;
        container.querySelector('#totalCustosPercent').textContent = `${totalCustosPercent.toFixed(2)}%`;

        // Mostrar seção de resultados
        container.querySelector('#resultadosSection').style.display = 'block';

        // Criar gráfico
        createMargemChart(precoCusto, totalCustosVariaveis, margemLiquida);
    });

    // Evento para resetar calculadora
    resetBtn.addEventListener('click', function() {
        container.querySelector('#precoCusto').value = '4.25';
        container.querySelector('#precoVenda').value = '6.79';

        // Remover custos adicionais
        const custosAdicionais = container.querySelectorAll('.custo-item:nth-child(n+3)');
        custosAdicionais.forEach(custo => {
            custosVariaveis.removeChild(custo);
        });

        // Resetar valores dos custos padrão
        container.querySelectorAll('.custo-item:nth-child(-n+2) .custo-valor').forEach((input, index) => {
            input.value = index === 0 ? '0.15' : '1.25';
        });

        // Esconder resultados
        container.querySelector('#resultadosSection').style.display = 'none';
    });
}

/**
 * Cria o gráfico da calculadora de margem
 * @param {number} precoCusto - Preço de custo
 * @param {number} custosVariaveis - Total de custos variáveis
 * @param {number} margemLiquida - Margem líquida
 */
function createMargemChart(precoCusto, custosVariaveis, margemLiquida) {
    const ctx = document.getElementById('margemChart');
    if (!ctx) return;

    // Destruir gráfico existente se houver
    if (window.margemChart) {
        window.margemChart.destroy();
    }

    // Criar novo gráfico
    window.margemChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Custo de Aquisição', 'Custos Variáveis', 'Margem Líquida'],
            datasets: [{
                data: [precoCusto, custosVariaveis, margemLiquida],
                backgroundColor: ['#ED1C24', '#FDB813', '#28a745'],
                borderColor: '#333',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    labels: {
                        color: '#ccc',
                        font: {
                            family: "'Rajdhani', sans-serif",
                            size: 12
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(2);
                            return `${label}: R$ ${value.toFixed(2)} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

/**
 * Exibe o modal da ferramenta selecionada ou usa o efeito flip para o histórico
 * @param {string} tool - Nome da ferramenta
 */
function showToolModal(tool) {
    console.log('Mostrando ferramenta:', tool);



    // Para as outras ferramentas, continua usando o modal
    // Cria o modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = `modal-${tool}`;
    modal.tabIndex = -1;
    modal.setAttribute('aria-labelledby', `toolModalLabel-${tool}`);
    modal.setAttribute('aria-hidden', 'true');

    // Prepara o conteúdo baseado na ferramenta
    let modalContent = '';

    switch (tool) {
        case 'cotacoes':
            modalContent = createCotacoesContent();
            break;
        case 'calculadora':
            modalContent = createCalculadoraContent();
            break;
        case 'impostos':
            modalContent = createImpostosContent();
            break;
        case 'noticias':
            modalContent = createNoticiasContent();
            break;
        case 'relatorios':
            modalContent = createRelatoriosContent();
            break;
    }

    // Estrutura do modal
    modal.innerHTML = `
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="toolModalLabel-${tool}">
                        ${getToolTitle(tool)}
                    </h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Fechar">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    ${modalContent}
                </div>
            </div>
        </div>
    `;

    // Adiciona o modal ao body
    document.body.appendChild(modal);

    // Inicializa o modal do Bootstrap
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();

    // Inicializa comportamentos específicos de cada ferramenta após o modal ser mostrado
    modal.addEventListener('shown.bs.modal', function() {
        const modalBody = modal.querySelector('.modal-body');

        switch (tool) {
            case 'cotacoes':
                // Aqui seria inicializado o gráfico de cotações, se necessário
                break;
            case 'calculadora':
                // Comportamentos específicos da calculadora
                break;
            // Outros casos conforme necessário
        }
    });

    // Remove o modal quando fechado
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

/**
 * Retorna o título da ferramenta
 * @param {string} tool - Nome da ferramenta
 * @returns {string} - Título da ferramenta
 */
function getToolTitle(tool) {
    const titles = {
        'cotacoes': 'Cotações',
        'calculadora': 'Calculadora de Margem',
        'impostos': 'Impostos',

        'noticias': 'Notícias do Mercado',
        'relatorios': 'Relatórios'
    };
    return titles[tool] || tool;
}

/**
 * Cria o conteúdo da ferramenta de Cotações
 * @returns {string} HTML do conteúdo
 */
function createCotacoesContent() {
    return `
        <div class="tool-section">
            <h3 class="tool-section-title">
                <i class="fas fa-dollar-sign"></i> Cotações em Tempo Real
            </h3>
            <div class="cotacoes-grid">
                <div class="cotacao-card">
                    <div class="cotacao-header">
                        <i class="fas fa-gas-pump"></i>
                        <span>Petróleo Brent</span>
                    </div>
                    <div class="cotacao-value">US$ 85.42</div>
                    <div class="cotacao-variation positive">+1.25%</div>
                </div>
                <div class="cotacao-card">
                    <div class="cotacao-header">
                        <i class="fas fa-dollar-sign"></i>
                        <span>Dólar</span>
                    </div>
                    <div class="cotacao-value">R$ 4.92</div>
                    <div class="cotacao-variation negative">-0.35%</div>
                </div>
                <div class="cotacao-card">
                    <div class="cotacao-header">
                        <i class="fas fa-euro-sign"></i>
                        <span>Euro</span>
                    </div>
                    <div class="cotacao-value">R$ 5.38</div>
                    <div class="cotacao-variation positive">+0.42%</div>
                </div>
            </div>
            <div class="chart-container mt-4">
                <canvas id="cotacoesChart" width="100%" height="300"></canvas>
            </div>
        </div>
    `;
}



/**
 * Cria o conteúdo da ferramenta de Notícias
 * @returns {string} HTML do conteúdo
 */
function createNoticiasContent() {
    return `
        <div class="tool-section">
            <h3 class="tool-section-title">
                <i class="fas fa-newspaper"></i> Notícias do Mercado
            </h3>
            <div class="noticias-list">
                <div class="noticia-item">
                    <div class="noticia-data">Hoje, 10:45</div>
                    <h4 class="noticia-titulo">Preço do petróleo sobe com tensões no Oriente Médio</h4>
                    <p class="noticia-resumo">O preço do petróleo Brent subiu mais de 2% nesta quinta-feira...</p>
                    <div class="noticia-fonte">Fonte: Reuters</div>
                </div>
                <div class="noticia-item">
                    <div class="noticia-data">Hoje, 09:30</div>
                    <h4 class="noticia-titulo">Governo anuncia nova política de preços para combustíveis</h4>
                    <p class="noticia-resumo">O Ministério de Minas e Energia anunciou hoje...</p>
                    <div class="noticia-fonte">Fonte: Agência Brasil</div>
                </div>
            </div>
            <div class="form-actions mt-4">
                <button class="btn-shell-yellow">Carregar Mais Notícias</button>
            </div>
        </div>
    `;
}

/**
 * Cria o conteúdo da ferramenta de Relatórios
 * @returns {string} HTML do conteúdo
 */
function createRelatoriosContent() {
    return `
        <div class="tool-section">
            <h3 class="tool-section-title">
                <i class="fas fa-file-alt"></i> Relatórios
            </h3>
            <div class="tool-form">
                <div class="form-group">
                    <label>Tipo de Relatório</label>
                    <select class="form-control">
                        <option>Relatório Financeiro Completo</option>
                        <option>Análise de Vendas</option>
                        <option>Performance por Filial</option>
                        <option>Margem de Lucro</option>
                        <option>Fluxo de Caixa</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Período</label>
                    <input type="text" class="form-control" id="relatorioPeriodo">
                </div>
                <div class="form-group">
                    <label>Formato</label>
                    <select class="form-control">
                        <option>PDF</option>
                        <option>Excel</option>
                        <option>CSV</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button class="btn-shell-yellow">Gerar Relatório</button>
                </div>
            </div>
        </div>
    `;
}

/**
 * Cria o conteúdo da ferramenta de Calculadora de Margem
 * @returns {string} HTML do conteúdo
 */
function createCalculadoraContent() {
    return `
        <div class="tool-section">
            <h3 class="tool-section-title">
                <i class="fas fa-calculator"></i> Calculadora de Margem
            </h3>
            <div class="tool-form">
                <div class="form-group">
                    <label>Tipo de Combustível</label>
                    <select class="form-control" id="calcCombustivel">
                        <option>Gasolina Comum</option>
                        <option>Gasolina Aditivada</option>
                        <option>Etanol</option>
                        <option>Diesel S10</option>
                        <option>Diesel Comum</option>
                    </select>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-6">
                        <label>Preço de Custo (R$)</label>
                        <input type="number" class="form-control" id="calcCusto" step="0.01">
                    </div>
                    <div class="form-group col-md-6">
                        <label>Preço de Venda (R$)</label>
                        <input type="number" class="form-control" id="calcVenda" step="0.01">
                    </div>
                </div>
                <div class="form-group">
                    <label>Volume Mensal (L)</label>
                    <input type="number" class="form-control" id="calcVolume">
                </div>
                <div class="form-actions">
                    <button class="btn-shell-yellow" onclick="calcularMargem()">Calcular</button>
                </div>
            </div>
            <div class="resultado-calculo mt-4" style="display: none;">
                <h4>Resultados</h4>
                <div class="resultados-grid">
                    <div class="resultado-item">
                        <span class="resultado-label">Margem Bruta</span>
                        <span class="resultado-value" id="margemBruta">0.00%</span>
                    </div>
                    <div class="resultado-item">
                        <span class="resultado-label">Lucro por Litro</span>
                        <span class="resultado-value" id="lucroPorLitro">R$ 0.00</span>
                    </div>
                    <div class="resultado-item">
                        <span class="resultado-label">Lucro Mensal</span>
                        <span class="resultado-value" id="lucroMensal">R$ 0.00</span>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * Cria o conteúdo da ferramenta de Impostos
 * @returns {string} HTML do conteúdo
 */
function createImpostosContent() {
    return `
        <div class="tool-section">
            <h3 class="tool-section-title">
                <i class="fas fa-file-invoice-dollar"></i> Consulta de Impostos
            </h3>
            <div class="tool-form">
                <div class="form-group">
                    <label>Estado</label>
                    <select class="form-control" id="impostoEstado">
                        <option value="">Selecione um estado</option>
                        <option value="SP">São Paulo</option>
                        <option value="RJ">Rio de Janeiro</option>
                        <option value="MG">Minas Gerais</option>
                        <option value="ES">Espírito Santo</option>
                        <option value="PR">Paraná</option>
                        <option value="SC">Santa Catarina</option>
                        <option value="RS">Rio Grande do Sul</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Tipo de Combustível</label>
                    <select class="form-control" id="impostoCombustivel">
                        <option value="">Selecione um combustível</option>
                        <option value="gasolina">Gasolina Comum</option>
                        <option value="gasolina_ad">Gasolina Aditivada</option>
                        <option value="etanol">Etanol</option>
                        <option value="diesel">Diesel Comum</option>
                        <option value="diesel_s10">Diesel S10</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button class="btn-shell-yellow" onclick="consultarImpostos()">Consultar</button>
                </div>
            </div>
            <div class="resultado-impostos mt-4" style="display: none;">
                <h4>Alíquotas de Impostos</h4>
                <div class="impostos-grid">
                    <div class="imposto-item">
                        <span class="imposto-label">ICMS</span>
                        <span class="imposto-value" id="aliquotaICMS">0%</span>
                    </div>
                    <div class="imposto-item">
                        <span class="imposto-label">PIS</span>
                        <span class="imposto-value" id="aliquotaPIS">0%</span>
                    </div>
                    <div class="imposto-item">
                        <span class="imposto-label">COFINS</span>
                        <span class="imposto-value" id="aliquotaCOFINS">0%</span>
                    </div>
                </div>
                <div class="chart-container mt-4">
                    <canvas id="impostosChart" width="100%" height="200"></canvas>
                </div>
            </div>
        </div>
    `;
}

/**
 * Calcula a margem de lucro
 */
function calcularMargem() {
    const custo = parseFloat(document.getElementById('calcCusto').value);
    const venda = parseFloat(document.getElementById('calcVenda').value);
    const volume = parseFloat(document.getElementById('calcVolume').value);

    if (!custo || !venda || !volume) {
        showToast('Preencha todos os campos para calcular a margem', 'error');
        return;
    }

    const margemBruta = ((venda - custo) / venda) * 100;
    const lucroPorLitro = venda - custo;
    const lucroMensal = lucroPorLitro * volume;

    document.getElementById('margemBruta').textContent = margemBruta.toFixed(2) + '%';
    document.getElementById('lucroPorLitro').textContent = 'R$ ' + lucroPorLitro.toFixed(2);
    document.getElementById('lucroMensal').textContent = 'R$ ' + lucroMensal.toFixed(2);

    document.querySelector('.resultado-calculo').style.display = 'block';
}

/**
 * Consulta os impostos por estado e combustível
 */
function consultarImpostos() {
    const estado = document.getElementById('impostoEstado').value;
    const combustivel = document.getElementById('impostoCombustivel').value;

    if (!estado || !combustivel) {
        showToast('Selecione o estado e o combustível', 'error');
        return;
    }

    // Dados simulados de impostos
    const impostos = {
        SP: {
            gasolina: { icms: 25, pis: 1.65, cofins: 7.6 },
            gasolina_ad: { icms: 25, pis: 1.65, cofins: 7.6 },
            etanol: { icms: 12, pis: 1.65, cofins: 7.6 },
            diesel: { icms: 12, pis: 1.65, cofins: 7.6 },
            diesel_s10: { icms: 12, pis: 1.65, cofins: 7.6 }
        },
        RJ: {
            gasolina: { icms: 34, pis: 1.65, cofins: 7.6 },
            gasolina_ad: { icms: 34, pis: 1.65, cofins: 7.6 },
            etanol: { icms: 14, pis: 1.65, cofins: 7.6 },
            diesel: { icms: 14, pis: 1.65, cofins: 7.6 },
            diesel_s10: { icms: 14, pis: 1.65, cofins: 7.6 }
        }
        // Adicionar mais estados conforme necessário
    };

    const aliquotas = impostos[estado][combustivel];
    document.getElementById('aliquotaICMS').textContent = aliquotas.icms + '%';
    document.getElementById('aliquotaPIS').textContent = aliquotas.pis + '%';
    document.getElementById('aliquotaCOFINS').textContent = aliquotas.cofins + '%';

    document.querySelector('.resultado-impostos').style.display = 'block';

    // Criar gráfico de pizza com os impostos
    const ctx = document.getElementById('impostosChart').getContext('2d');
    new Chart(ctx, {
        type: 'pie',
        data: {
            labels: ['ICMS', 'PIS', 'COFINS', 'Valor Líquido'],
            datasets: [{
                data: [
                    aliquotas.icms,
                    aliquotas.pis,
                    aliquotas.cofins,
                    100 - (aliquotas.icms + aliquotas.pis + aliquotas.cofins)
                ],
                backgroundColor: [
                    '#ED1C24',
                    '#FDB813',
                    '#333333',
                    '#808080'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Variáveis globais
let loadingOverlay;
let toastTimeout;

// Inicialização quando o documento estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    initializeUI();
    setupEventListeners();
    initializeDateRangePicker();
});

// Inicialização da UI
function initializeUI() {
    // Criar overlay de loading
    loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'loading-overlay d-none';
    loadingOverlay.innerHTML = `
        <div class="spinner-border text-light" role="status">
            <span class="sr-only">Carregando...</span>
        </div>
    `;
    document.body.appendChild(loadingOverlay);
}

// Configuração dos event listeners
function setupEventListeners() {
    // Listener para o formulário de filtros
    const filterForm = document.getElementById('filterForm');
    if (filterForm) {
        filterForm.addEventListener('submit', handleFilterSubmit);
    }

    // Listeners para botões de ação
    document.querySelectorAll('[data-action]').forEach(button => {
        button.addEventListener('click', handleActionButton);
    });
}

// Inicialização do DateRangePicker
function initializeDateRangePicker() {
    $('input[name="daterange"]').daterangepicker({
        opens: 'left',
        locale: {
            format: 'DD/MM/YYYY',
            separator: ' - ',
            applyLabel: 'Aplicar',
            cancelLabel: 'Cancelar',
            fromLabel: 'De',
            toLabel: 'Até',
            customRangeLabel: 'Personalizado',
            weekLabel: 'S',
            daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'],
            monthNames: [
                'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
                'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
            ],
            firstDay: 1
        },
        ranges: {
            'Hoje': [moment(), moment()],
            'Ontem': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Últimos 7 Dias': [moment().subtract(6, 'days'), moment()],
            'Últimos 30 Dias': [moment().subtract(29, 'days'), moment()],
            'Este Mês': [moment().startOf('month'), moment().endOf('month')],
            'Mês Passado': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        }
    });
}

// Handler para submissão do formulário de filtros
async function handleFilterSubmit(event) {
    event.preventDefault();
    showLoading();

    try {
        const formData = new FormData(event.target);
        const response = await fetch('/api/financeiro/filtrar', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) throw new Error('Erro ao filtrar dados');

        const data = await response.json();
        updateDashboard(data);
        showToast('Filtros aplicados com sucesso', 'success');
    } catch (error) {
        console.error('Erro:', error);
        showToast('Erro ao aplicar filtros', 'error');
    } finally {
        hideLoading();
    }
}

// Handler para botões de ação
async function handleActionButton(event) {
    const button = event.currentTarget;
    const action = button.dataset.action;
    const id = button.dataset.id;

    showLoading();

    try {
        const response = await fetch(`/api/financeiro/${action}/${id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) throw new Error(`Erro ao executar ação ${action}`);

        const data = await response.json();
        showToast(data.message || 'Ação executada com sucesso', 'success');

        // Atualizar UI conforme necessário
        if (data.requiresRefresh) {
            location.reload();
        }
    } catch (error) {
        console.error('Erro:', error);
        showToast(`Erro ao executar ação: ${error.message}`, 'error');
    } finally {
        hideLoading();
    }
}

// Função para atualizar o dashboard com novos dados
function updateDashboard(data) {
    // Atualizar indicadores
    updateIndicators(data.indicators);

    // Atualizar tabela
    updateTable(data.transactions);

    // Atualizar gráficos
    updateCharts(data.charts);
}

// Funções auxiliares para atualização de componentes
function updateIndicators(indicators) {
    Object.entries(indicators).forEach(([key, value]) => {
        const element = document.getElementById(`indicator-${key}`);
        if (element) {
            const valueElement = element.querySelector('.indicator-value');
            if (valueElement) {
                valueElement.textContent = formatCurrency(value);
                valueElement.className = `indicator-value ${value < 0 ? 'negative' : 'positive'}`;
            }
        }
    });
}

function updateTable(transactions) {
    const tableBody = document.querySelector('#transactionsTable tbody');
    if (!tableBody) return;

    tableBody.innerHTML = transactions.map(transaction => `
        <tr>
            <td>${transaction.date}</td>
            <td>${transaction.description}</td>
            <td class="${transaction.value < 0 ? 'text-danger' : 'text-success'}">
                ${formatCurrency(transaction.value)}
            </td>
            <td>${transaction.category}</td>
            <td>
                <button class="btn btn-sm btn-shell-yellow" data-action="edit" data-id="${transaction.id}">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-shell-red" data-action="delete" data-id="${transaction.id}">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');

    // Reattach event listeners
    setupEventListeners();
}

function updateCharts(chartData) {
    // Atualizar gráficos usando Chart.js
    Object.entries(chartData).forEach(([chartId, data]) => {
        const chart = Chart.getChart(chartId);
        if (chart) {
            chart.data = data;
            chart.update();
        }
    });
}

// Funções utilitárias
function showLoading() {
    loadingOverlay.classList.remove('d-none');
}

function hideLoading() {
    loadingOverlay.classList.add('d-none');
}

function showToast(message, type = 'success') {
    // Remover toast anterior se existir
    const existingToast = document.querySelector('.toast-message');
    if (existingToast) {
        existingToast.remove();
    }

    // Limpar timeout anterior
    if (toastTimeout) {
        clearTimeout(toastTimeout);
    }

    // Criar novo toast
    const toast = document.createElement('div');
    toast.className = `toast-message ${type}`;
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
        ${message}
    `;

    document.body.appendChild(toast);

    // Mostrar toast com animação
    requestAnimationFrame(() => {
        toast.classList.add('show');
    });

    // Configurar timeout para remover o toast
    toastTimeout = setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}

function formatCurrency(value) {
    return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
    }).format(value);
}

// Exportar funções para uso global se necessário
window.showLoading = showLoading;
window.hideLoading = hideLoading;
window.showToast = showToast;
roundColor: chartType === 'line' ? 'rgba(153, 0, 0, 0.1)' : '#990000',
                    borderWidth: 2,
                    pointBackgroundColor: '#990000',
                    pointBorderColor: '#fff',
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    tension: 0.4,
                    fill: chartType === 'line' ? false : undefined
                });
            }

            if (selectedFuels.includes('etanol')) {
                historicoChart.data.datasets.push({
                    label: 'Etanol',
                    data: dadosEtanol,
                    borderColor: '#28a745',
                    backgroundColor: chartType === 'line' ? 'rgba(40, 167, 69, 0.1)' : '#28a745',
                    borderWidth: 2,
                    pointBackgroundColor: '#28a745',
                    pointBorderColor: '#fff',
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    tension: 0.4,
                    fill: chartType === 'line' ? false : undefined
                });
            }

            if (selectedFuels.includes('diesel')) {
                historicoChart.data.datasets.push({
                    label: 'Diesel S10',
                    data: dadosDiesel,
                    borderColor: '#FDB813',
                    backgroundColor: chartType === 'line' ? 'rgba(253, 184, 19, 0.1)' : '#FDB813',
                    borderWidth: 2,
                    pointBackgroundColor: '#FDB813',
                    pointBorderColor: '#fff',
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    tension: 0.4,
                    fill: chartType === 'line' ? false : undefined
                });
            }

            // Atualiza tipo do gráfico
            historicoChart.config.type = chartType;

            // Atualiza o gráfico
            historicoChart.update();

            // Atualiza a legenda
            atualizarLegenda();

            if (chartLoading) {
                chartLoading.style.display = 'none';
            }
        }

        // Configura eventos para os controles
        if (viewButtons.length > 0) {
            viewButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const view = this.dataset.view;
                    viewButtons.forEach(button => button.classList.remove('active'));
                    this.classList.add('active');
                    updateChart(view);
                });
            });
        }

        if (fuelCheckboxes.length > 0) {
            fuelCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateChart(historicoChart.config.type);
                });
            });
        }

        if (periodButtons.length > 0) {
            periodButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    periodButtons.forEach(button => button.classList.remove('active'));
                    this.classList.add('active');
                    updateChart(historicoChart.config.type);
                });
            });
        }

        if (consultarBtn) {
            consultarBtn.addEventListener('click', function() {
                updateChart(historicoChart.config.type);
            });
        }

        if (exportarBtn) {
            exportarBtn.addEventListener('click', function() {
                showToast('Exportando dados do histórico...', 'success');
            });
        }

        // Inicializa a legenda e o gráfico
        atualizarLegenda();
        updateChart();

        return historicoChart;
    } catch (error) {
        console.error('Erro ao inicializar o gráfico de histórico:', error);
        // Exibir mensagem de erro no container
        if (container) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'alert alert-danger mt-3';
            errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> Erro ao carregar o gráfico: ${error.message}`;
            container.querySelector('.chart-container')?.appendChild(errorDiv);
        }
        return null;
    }
}

// Exportar funções para uso global
window.financeiroPainel = window.financeiroPainel || {};
