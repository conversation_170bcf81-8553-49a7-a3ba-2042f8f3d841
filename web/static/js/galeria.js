/**
 * Galeria de Equipamentos - Rede Tradição
 * Script para gerenciar a página de galeria de equipamentos
 */

// Variáveis globais
let equipments = [];
let filteredEquipments = [];
let branches = [];
let currentBranchId = null;

// Inicialização quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar tooltips do Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Inicializar Lightbox
    lightbox.option({
        'resizeDuration': 200,
        'wrapAround': true,
        'albumLabel': "Imagem %1 de %2",
        'fadeDuration': 300
    });

    // Carregar filiais
    loadBranches();

    // Carregar equipamentos (inicialmente todos ou da filial do usuário)
    if (User && User.BranchID && User.BranchID > 0) {
        // Se o usuário for de uma filial, carregar apenas os equipamentos dessa filial
        document.getElementById('filialSelect').value = User.BranchID;
        loadFilialInfo(User.BranchID);
        loadEquipments(User.BranchID);
    } else {
        // Se for admin ou outro tipo de usuário, carregar todos os equipamentos
        loadEquipments();
    }

    // Event listeners
    setupEventListeners();
});

/**
 * Configura os event listeners da página
 */
function setupEventListeners() {
    // Alternar entre visualização em grade e lista
    document.getElementById('gridViewBtn').addEventListener('click', function() {
        document.getElementById('gridView').classList.remove('d-none');
        document.getElementById('listView').classList.add('d-none');
        this.classList.add('active');
        document.getElementById('listViewBtn').classList.remove('active');
    });

    document.getElementById('listViewBtn').addEventListener('click', function() {
        document.getElementById('listView').classList.remove('d-none');
        document.getElementById('gridView').classList.add('d-none');
        this.classList.add('active');
        document.getElementById('gridViewBtn').classList.remove('active');
    });

    // Filtro de filial
    document.getElementById('filialSelect').addEventListener('change', function() {
        const filialId = this.value;
        currentBranchId = filialId ? parseInt(filialId) : null;

        if (filialId) {
            loadFilialInfo(filialId);
        } else {
            document.getElementById('filialInfoSection').classList.add('d-none');
        }

        applyFilters();
    });

    // Filtro de tipo de equipamento
    document.getElementById('equipmentTypeSelect').addEventListener('change', function() {
        applyFilters();
    });

    // Filtro de status
    document.getElementById('statusSelect').addEventListener('change', function() {
        applyFilters();
    });

    // Busca
    document.getElementById('searchBtn').addEventListener('click', function() {
        applyFilters();
    });

    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            applyFilters();
        }
    });

    // Botão de atualizar
    document.getElementById('refreshBtn').addEventListener('click', function() {
        if (currentBranchId) {
            // Usar a nova rota para carregar equipamentos por filial
            fetch(`/api/equipments/filial/${currentBranchId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Falha ao carregar equipamentos da filial');
                    }
                    return response.json();
                })
                .then(data => {
                    // Armazenar os equipamentos
                    equipments = data;
                    filteredEquipments = [...data];

                    // Ocultar indicador de carregamento
                    document.getElementById('loadingIndicator').classList.add('d-none');

                    // Atualizar o contador de resultados
                    document.getElementById('resultsCount').textContent = data.length;

                    if (data.length === 0) {
                        // Exibir mensagem de "nenhum equipamento"
                        document.getElementById('noEquipmentsMessage').classList.remove('d-none');
                    } else {
                        // Renderizar os equipamentos
                        renderEquipments(data);
                    }
                })
                .catch(error => {
                    console.error('Erro ao carregar equipamentos da filial:', error);
                    document.getElementById('loadingIndicator').classList.add('d-none');
                    document.getElementById('noEquipmentsMessage').classList.remove('d-none');
                    document.getElementById('noEquipmentsMessage').querySelector('p').textContent =
                        'Erro ao carregar equipamentos: ' + error.message;
                    showToast('Erro ao carregar equipamentos: ' + error.message, 'error');
                });

            // Atualizar informações da filial
            loadFilialInfo(currentBranchId);
        } else {
            loadEquipments();
        }
    });
}

/**
 * Carrega a lista de filiais
 */
function loadBranches() {
    // Usar a API filtrada de filiais que respeita as permissões do usuário
    fetch('/api/filiais')
        .then(response => {
            if (!response.ok) {
                throw new Error('Falha ao carregar filiais');
            }
            return response.json();
        })
        .then(data => {
            branches = data;

            // Se o usuário não for de uma filial específica, atualizar o select de filiais
            if (!User.BranchID || User.BranchID === 0) {
                const branchSelect = document.getElementById('filialSelect');

                // Limpar opções existentes, mantendo a opção "Todas as Filiais"
                while (branchSelect.options.length > 1) {
                    branchSelect.remove(1);
                }

                // Adicionar as filiais ao select
                data.forEach(branch => {
                    const option = document.createElement('option');
                    option.value = branch.ID || branch.id; // Aceita tanto ID quanto id
                    option.textContent = branch.Name || branch.name; // Aceita tanto Name quanto name
                    branchSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Erro ao carregar filiais:', error);
            showToast('Erro ao carregar filiais: ' + error.message, 'error');
        });
}

/**
 * Carrega informações detalhadas de uma filial
 * @param {number} filialId - ID da filial
 */
function loadFilialInfo(filialId) {
    // Usar a API filtrada de filiais que respeita as permissões do usuário
    fetch(`/api/filiais/${filialId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Falha ao carregar informações da filial');
            }
            return response.json();
        })
        .then(data => {
            // Exibir a seção de informações da filial com animação
            showBranchInfo();

            // Preencher os dados da filial (aceita tanto campos em maiúsculas quanto minúsculas)
            document.getElementById('filialName').textContent = data.Name || data.name || 'Não informado';
            document.getElementById('filialAddress').textContent = data.Address || data.address || 'Não informado';
            document.getElementById('filialPhone').textContent = data.Phone || data.phone || 'Não informado';
            document.getElementById('filialEmail').textContent = data.Email || data.email || 'Não informado';

            // Exibir informações no console para debug
            console.log('Dados da filial recebidos:', data);

            // Atualizar estatísticas de equipamentos
            updateFilialStats(filialId);
        })
        .catch(error => {
            console.error('Erro ao carregar informações da filial:', error);
            document.getElementById('filialInfoSection').classList.add('d-none');
            showToast('Erro ao carregar informações da filial: ' + error.message, 'error');
        });
}

/**
 * Atualiza as estatísticas de equipamentos da filial
 * @param {number} filialId - ID da filial
 */
function updateFilialStats(filialId) {
    // Filtrar equipamentos da filial
    const filialEquipments = equipments.filter(eq => eq.filial_id || eq.station_id === parseInt(filialId));

    // Contar equipamentos por status
    const totalEquipments = filialEquipments.length;
    const activeEquipments = filialEquipments.filter(eq => eq.status === 'ativo').length;
    const maintenanceEquipments = filialEquipments.filter(eq => eq.status === 'manutencao').length;

    // Atualizar os contadores com animação
    document.getElementById('totalEquipments').textContent = '0';
    document.getElementById('activeEquipments').textContent = '0';
    document.getElementById('maintenanceEquipments').textContent = '0';

    // Animar contadores após um pequeno delay
    setTimeout(() => {
        animateCounter(document.getElementById('totalEquipments'), totalEquipments, 1500);
        animateCounter(document.getElementById('activeEquipments'), activeEquipments, 1500);
        animateCounter(document.getElementById('maintenanceEquipments'), maintenanceEquipments, 1500);
    }, 300);
}

/**
 * Carrega a lista de equipamentos
 * @param {number} branchId - ID da filial (opcional)
 */
function loadEquipments(branchId = null) {
    // Exibir indicador de carregamento
    document.getElementById('loadingIndicator').classList.remove('d-none');
    document.getElementById('noEquipmentsMessage').classList.add('d-none');

    // Limpar a grade de equipamentos
    const gridView = document.getElementById('gridView');
    const tableBody = document.getElementById('equipmentTableBody');

    // Manter apenas o indicador de carregamento e a mensagem de "nenhum equipamento"
    while (gridView.children.length > 2) {
        gridView.removeChild(gridView.lastChild);
    }

    // Limpar a tabela
    tableBody.innerHTML = '';

    // URL da API
    let url = '/api/equipments';
    if (branchId) {
        url = `/api/equipments/filial/${branchId}`;
    }

    console.log(`Carregando equipamentos da URL: ${url}`);

    // Fazer requisição para a API
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error('Falha ao carregar equipamentos');
            }
            return response.json();
        })
        .then(data => {
            // Armazenar os equipamentos
            equipments = data;
            filteredEquipments = [...data];

            // Ocultar indicador de carregamento
            document.getElementById('loadingIndicator').classList.add('d-none');

            // Atualizar o contador de resultados
            document.getElementById('resultsCount').textContent = data.length;

            if (data.length === 0) {
                // Exibir mensagem de "nenhum equipamento"
                document.getElementById('noEquipmentsMessage').classList.remove('d-none');
            } else {
                // Renderizar os equipamentos
                renderEquipments(data);
            }
        })
        .catch(error => {
            console.error('Erro ao carregar equipamentos:', error);
            document.getElementById('loadingIndicator').classList.add('d-none');
            document.getElementById('noEquipmentsMessage').classList.remove('d-none');
            document.getElementById('noEquipmentsMessage').querySelector('p').textContent =
                'Erro ao carregar equipamentos: ' + error.message;
            showToast('Erro ao carregar equipamentos: ' + error.message, 'error');
        });
}

/**
 * Renderiza os equipamentos na interface
 * @param {Array} equipmentList - Lista de equipamentos
 */
function renderEquipments(equipmentList) {
    const gridView = document.getElementById('gridView');
    const tableBody = document.getElementById('equipmentTableBody');

    // Limpar a grade de equipamentos (mantendo o indicador de carregamento e a mensagem)
    while (gridView.children.length > 2) {
        gridView.removeChild(gridView.lastChild);
    }

    // Limpar a tabela
    tableBody.innerHTML = '';

    if (equipmentList.length === 0) {
        // Exibir mensagem de "nenhum equipamento"
        document.getElementById('noEquipmentsMessage').classList.remove('d-none');
        return;
    }

    // Ocultar mensagem de "nenhum equipamento"
    document.getElementById('noEquipmentsMessage').classList.add('d-none');

    // Renderizar cada equipamento
    equipmentList.forEach(equipment => {
        // Adicionar à visualização em grade
        const cardCol = document.createElement('div');
        cardCol.className = 'equipment-item';

        // Determinar a classe de status
        let statusClass = 'badge-shell-green';
        let statusText = 'Ativo';

        if (equipment.status === 'inativo') {
            statusClass = 'badge-shell-red';
            statusText = 'Inativo';
        } else if (equipment.status === 'manutencao') {
            statusClass = 'badge-shell-yellow';
            statusText = 'Em Manutenção';
        }

        // Encontrar a filial do equipamento
        let branchName = 'Não informado';
        // Usar filial_id se disponível, caso contrário usar station_id para compatibilidade
        const filialId = equipment.filial_id || equipment.station_id;
        if (filialId) {
            const branch = branches.find(b => (b.ID || b.id) === filialId);
            if (branch) {
                branchName = branch.Name || branch.name;
            }
        }

        // Contar mídias
        const mediaCount = equipment.media ? equipment.media.length : 0;

        // Criar o card do equipamento
        cardCol.innerHTML = `
            <div class="equipment-card">
                <div class="card-img-top">
                    ${mediaCount > 0 ?
                        `<img src="/uploads/equipment/${equipment.media[0].file_path}" alt="${equipment.name}">` :
                        `<div class="no-image"><i class="fas fa-tools"></i></div>`
                    }
                </div>
                <div class="card-body">
                    <h5 class="card-title" title="${equipment.name}">${equipment.name}</h5>
                    <p class="card-text" title="${formatEquipmentType(equipment.type)}"><i class="fas fa-tag me-2"></i>${formatEquipmentType(equipment.type)}</p>
                    <p class="card-text" title="${branchName}"><i class="fas fa-building me-2"></i>${branchName}</p>
                    <p class="card-text" title="${equipment.serial_number || 'N/A'}"><i class="fas fa-barcode me-2"></i>${equipment.serial_number || 'N/A'}</p>
                    <p class="card-text" title="${mediaCount} mídia${mediaCount !== 1 ? 's' : ''}">
                        <i class="fas fa-photo-video me-2"></i>
                        ${mediaCount} mídia${mediaCount !== 1 ? 's' : ''}
                    </p>
                </div>
                <div class="card-footer">
                    <span class="${statusClass}">${statusText}</span>
                    <div class="card-actions">
                        <button class="btn-icon" onclick="viewEquipmentDetails(${equipment.id})" data-bs-toggle="tooltip" title="Ver Detalhes">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;

        gridView.appendChild(cardCol);

        // Adicionar à visualização em lista
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${equipment.name}</td>
            <td>${formatEquipmentType(equipment.type)}</td>
            <td>${equipment.model || '-'}</td>
            <td>${equipment.serial_number || '-'}</td>
            <td><span class="${statusClass}">${statusText}</span></td>
            <td>${branchName}</td>
            <td><span class="media-count">${mediaCount}</span></td>
            <td>
                <button class="btn-icon" onclick="viewEquipmentDetails(${equipment.id})" data-bs-toggle="tooltip" title="Ver Detalhes">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
        `;

        tableBody.appendChild(row);
    });

    // Reinicializar tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Aplica os filtros selecionados aos equipamentos
 */
function applyFilters() {
    // Obter valores dos filtros
    const branchId = document.getElementById('filialSelect').value;
    const equipmentType = document.getElementById('equipmentTypeSelect').value;
    const status = document.getElementById('statusSelect').value;
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();

    // Filtrar equipamentos
    let filtered = [...equipments];

    // Filtrar por filial
    if (branchId) {
        filtered = filtered.filter(eq => (eq.filial_id || eq.station_id) === parseInt(branchId));
    }

    // Filtrar por tipo
    if (equipmentType) {
        filtered = filtered.filter(eq => eq.type === equipmentType);
    }

    // Filtrar por status
    if (status) {
        filtered = filtered.filter(eq => eq.status === status);
    }

    // Filtrar por termo de busca
    if (searchTerm) {
        filtered = filtered.filter(eq =>
            (eq.name && eq.name.toLowerCase().includes(searchTerm)) ||
            (eq.model && eq.model.toLowerCase().includes(searchTerm)) ||
            (eq.serial_number && eq.serial_number.toLowerCase().includes(searchTerm)) ||
            (eq.brand && eq.brand.toLowerCase().includes(searchTerm))
        );
    }

    // Atualizar a lista filtrada
    filteredEquipments = filtered;

    // Atualizar o contador de resultados
    document.getElementById('resultsCount').textContent = filtered.length;

    // Renderizar os equipamentos filtrados
    renderEquipments(filtered);
}

/**
 * Exibe os detalhes de um equipamento
 * @param {number} equipmentId - ID do equipamento
 */
function viewEquipmentDetails(equipmentId) {
    // Encontrar o equipamento na lista
    const equipment = equipments.find(eq => eq.id === equipmentId);

    if (!equipment) {
        showToast('Equipamento não encontrado', 'error');
        return;
    }

    // Preencher os dados do equipamento no modal
    document.getElementById('equipmentName').textContent = equipment.name;
    document.getElementById('equipmentType').textContent = formatEquipmentType(equipment.type);
    document.getElementById('equipmentModel').textContent = equipment.model || 'Não informado';
    document.getElementById('equipmentBrand').textContent = equipment.brand || 'Não informado';
    document.getElementById('equipmentSerialNumber').textContent = equipment.serial_number || 'Não informado';

    // Status
    const statusElement = document.getElementById('equipmentStatus');
    statusElement.textContent = formatStatus(equipment.status);
    statusElement.className = getStatusClass(equipment.status);

    // Datas
    document.getElementById('equipmentInstallationDate').textContent =
        equipment.installation_date ? formatDate(equipment.installation_date) : 'Não informado';
    document.getElementById('equipmentLastMaintenance').textContent =
        equipment.last_maintenance ? formatDate(equipment.last_maintenance) : 'Não informado';
    document.getElementById('equipmentNextMaintenance').textContent =
        equipment.next_maintenance ? formatDate(equipment.next_maintenance) : 'Não informado';

    // Observações
    document.getElementById('equipmentNotes').textContent = equipment.notes || 'Nenhuma observação';

    // Mídias
    const mediaGallery = document.getElementById('mediaGallery');
    const mediaLoadingIndicator = document.getElementById('mediaLoadingIndicator');
    const noMediaMessage = document.getElementById('noMediaMessage');

    // Limpar a galeria de mídias (mantendo o indicador de carregamento e a mensagem)
    while (mediaGallery.children.length > 2) {
        mediaGallery.removeChild(mediaGallery.lastChild);
    }

    // Exibir indicador de carregamento
    mediaLoadingIndicator.classList.remove('d-none');
    noMediaMessage.classList.add('d-none');

    // Verificar se o equipamento tem mídias
    if (!equipment.media || equipment.media.length === 0) {
        mediaLoadingIndicator.classList.add('d-none');
        noMediaMessage.classList.remove('d-none');
    } else {
        // Ocultar indicador de carregamento
        mediaLoadingIndicator.classList.add('d-none');

        // Renderizar as mídias
        equipment.media.forEach(media => {
            const mediaItem = document.createElement('div');
            mediaItem.className = 'media-item';

            // Determinar o tipo de mídia
            const fileType = media.file_type || '';
            let mediaTypeIcon = '';

            if (fileType.startsWith('image/')) {
                mediaTypeIcon = '<i class="fas fa-image"></i>';
                mediaItem.innerHTML = `
                    <a href="/uploads/equipment/${media.file_path}" data-lightbox="equipment-${equipment.id}" data-title="${media.description || media.file_name}">
                        <img src="/uploads/equipment/${media.file_path}" alt="${media.description || media.file_name}">
                    </a>
                    <div class="media-type-icon">${mediaTypeIcon}</div>
                    <div class="media-description">${media.description || media.file_name}</div>
                `;
            } else if (fileType.startsWith('video/')) {
                mediaTypeIcon = '<i class="fas fa-video"></i>';
                mediaItem.innerHTML = `
                    <video src="/uploads/equipment/${media.file_path}" controls></video>
                    <div class="media-type-icon">${mediaTypeIcon}</div>
                    <div class="media-description">${media.description || media.file_name}</div>
                `;
            } else if (fileType.includes('pdf')) {
                mediaTypeIcon = '<i class="fas fa-file-pdf"></i>';
                mediaItem.innerHTML = `
                    <a href="/uploads/equipment/${media.file_path}" target="_blank">
                        <div class="d-flex align-items-center justify-content-center h-100">
                            <i class="fas fa-file-pdf fa-3x text-danger"></i>
                        </div>
                    </a>
                    <div class="media-type-icon">${mediaTypeIcon}</div>
                    <div class="media-description">${media.description || media.file_name}</div>
                `;
            } else {
                mediaTypeIcon = '<i class="fas fa-file"></i>';
                mediaItem.innerHTML = `
                    <a href="/uploads/equipment/${media.file_path}" target="_blank">
                        <div class="d-flex align-items-center justify-content-center h-100">
                            <i class="fas fa-file fa-3x text-primary"></i>
                        </div>
                    </a>
                    <div class="media-type-icon">${mediaTypeIcon}</div>
                    <div class="media-description">${media.description || media.file_name}</div>
                `;
            }

            mediaGallery.appendChild(mediaItem);
        });
    }

    // Abrir o modal
    const modal = new bootstrap.Modal(document.getElementById('equipmentDetailModal'));
    modal.show();
}

/**
 * Formata o tipo de equipamento para exibição
 * @param {string} type - Tipo do equipamento
 * @returns {string} - Tipo formatado
 */
function formatEquipmentType(type) {
    const types = {
        'bomba': 'Bomba de Combustível',
        'tanque': 'Tanque de Armazenamento',
        'compressor': 'Compressor de Ar',
        'gerador': 'Gerador de Energia',
        'sistema_pagamento': 'Sistema de Pagamento',
        'outro': 'Outro'
    };

    return types[type] || type;
}

/**
 * Formata o status do equipamento para exibição
 * @param {string} status - Status do equipamento
 * @returns {string} - Status formatado
 */
function formatStatus(status) {
    const statuses = {
        'ativo': 'Ativo',
        'manutencao': 'Em Manutenção',
        'inativo': 'Inativo'
    };

    return statuses[status] || status;
}

/**
 * Retorna a classe CSS para o status do equipamento
 * @param {string} status - Status do equipamento
 * @returns {string} - Classe CSS
 */
function getStatusClass(status) {
    const classes = {
        'ativo': 'badge-shell-green',
        'manutencao': 'badge-shell-yellow',
        'inativo': 'badge-shell-red'
    };

    return classes[status] || 'badge-shell-gray';
}

/**
 * Formata uma data para exibição
 * @param {string} dateString - Data em formato ISO
 * @returns {string} - Data formatada
 */
function formatDate(dateString) {
    if (!dateString) return 'Não informado';

    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
}

/**
 * Exibe uma mensagem toast
 * @param {string} message - Mensagem a ser exibida
 * @param {string} type - Tipo da mensagem (success, error, warning, info)
 */
function showToast(message, type = 'info') {
    // Verificar se o container de toasts existe
    let toastContainer = document.querySelector('.toast-container');

    // Se não existir, criar
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    // Determinar a classe de cor com base no tipo
    let bgClass = 'bg-info';
    let icon = '<i class="fas fa-info-circle me-2"></i>';

    switch (type) {
        case 'success':
            bgClass = 'bg-success';
            icon = '<i class="fas fa-check-circle me-2"></i>';
            break;
        case 'error':
            bgClass = 'bg-danger';
            icon = '<i class="fas fa-exclamation-circle me-2"></i>';
            break;
        case 'warning':
            bgClass = 'bg-warning';
            icon = '<i class="fas fa-exclamation-triangle me-2"></i>';
            break;
    }

    // Criar o toast
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    ${icon} ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Fechar"></button>
            </div>
        </div>
    `;

    // Adicionar o toast ao container
    toastContainer.innerHTML += toastHtml;

    // Inicializar e exibir o toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: 5000
    });

    toast.show();

    // Remover o toast do DOM após ser ocultado
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}
