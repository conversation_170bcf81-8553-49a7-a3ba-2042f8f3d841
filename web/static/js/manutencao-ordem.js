/**
 * Script específico para a página ManutencaoOrdem
 * Rede Tradição - Sistema de Manutenção
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log("Inicializando script da página ManutencaoOrdem");
    
    // Configurar os cards de manutenção
    setupMaintenanceCards();
    
    // Configurar os modais
    setupModals();
    
    // Função para configurar os cards de manutenção
    function setupMaintenanceCards() {
        // Adicionar eventos de clique aos cards
        const manutencaoCard = document.getElementById('manutencao-card');
        const custosCard = document.getElementById('custos-card');
        const interacaoCard = document.getElementById('interacao-card');
        const cronogramaCard = document.getElementById('cronograma-card');

        // Botões de edição
        const btnEditarManutencao = document.getElementById('btn-editar-manutencao');
        const btnEditarCustos = document.getElementById('btn-editar-custos');

        // Função para abrir o modal de manutenção
        function abrirModalManutencao() {
            const modal = new bootstrap.Modal(document.getElementById('manutencaoModal'));
            modal.show();
        }

        // Função para abrir o modal de custos
        function abrirModalCustos() {
            const modal = new bootstrap.Modal(document.getElementById('custosModal'));
            modal.show();
        }

        // Função para abrir o modal de interação
        function abrirModalInteracao() {
            const modal = new bootstrap.Modal(document.getElementById('interacaoModal'));
            modal.show();
        }

        // Função para abrir o modal de cronograma
        function abrirModalCronograma() {
            const modal = new bootstrap.Modal(document.getElementById('cronogramaModal'));
            modal.show();
        }

        // Configurar eventos para o card de manutenção
        if (manutencaoCard) {
            manutencaoCard.addEventListener('click', abrirModalManutencao);
            console.log('Evento de clique adicionado ao card de manutenção');
        } else {
            console.log('Card de manutenção não encontrado');
        }

        // Configurar evento para o botão de editar manutenção
        if (btnEditarManutencao) {
            btnEditarManutencao.addEventListener('click', function(e) {
                e.stopPropagation(); // Evita que o clique propague para o card
                abrirModalManutencao();
            });
            console.log('Evento de clique adicionado ao botão de editar manutenção');
        } else {
            console.log('Botão de editar manutenção não encontrado');
        }

        // Configurar evento para o botão de editar custos
        if (btnEditarCustos) {
            btnEditarCustos.addEventListener('click', function(e) {
                e.stopPropagation(); // Evita que o clique propague para o card
                abrirModalCustos();
            });
            console.log('Evento de clique adicionado ao botão de editar custos');
        } else {
            console.log('Botão de editar custos não encontrado');
        }

        // Configurar eventos para os outros cards
        if (custosCard) {
            custosCard.addEventListener('click', abrirModalCustos);
        }

        if (interacaoCard) {
            interacaoCard.addEventListener('click', abrirModalInteracao);
        }

        if (cronogramaCard) {
            cronogramaCard.addEventListener('click', abrirModalCronograma);
        }

        // Adicionar logs para depuração
        console.log('Cards configurados:', {
            manutencaoCard: !!manutencaoCard,
            custosCard: !!custosCard,
            interacaoCard: !!interacaoCard,
            cronogramaCard: !!cronogramaCard,
            btnEditarManutencao: !!btnEditarManutencao,
            btnEditarCustos: !!btnEditarCustos
        });
    }

    // Função para configurar os modais
    function setupModals() {
        // Configurar cálculo automático do custo total
        const custoPecas = document.getElementById('custoPecas');
        const custoMaoObra = document.getElementById('custoMaoObra');
        const custoDeslocamento = document.getElementById('custoDeslocamento');
        const custoTotal = document.getElementById('custoTotal');

        const calcularTotal = () => {
            if (custoPecas && custoMaoObra && custoDeslocamento && custoTotal) {
                const pecas = parseFloat(custoPecas.value) || 0;
                const maoObra = parseFloat(custoMaoObra.value) || 0;
                const deslocamento = parseFloat(custoDeslocamento.value) || 0;

                const total = pecas + maoObra + deslocamento;
                custoTotal.value = total.toFixed(2);
            }
        };

        if (custoPecas) custoPecas.addEventListener('input', calcularTotal);
        if (custoMaoObra) custoMaoObra.addEventListener('input', calcularTotal);
        if (custoDeslocamento) custoDeslocamento.addEventListener('input', calcularTotal);

        // Configurar envio de mensagens no chat
        const chatMessageInput = document.getElementById('chatMessageInput');
        const sendChatMessageBtn = document.getElementById('sendChatMessage');

        if (sendChatMessageBtn && chatMessageInput) {
            sendChatMessageBtn.addEventListener('click', function() {
                enviarMensagemChat();
            });

            chatMessageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    enviarMensagemChat();
                }
            });
        }

        // Preencher datas do cronograma com valores padrão
        const dataInicio = document.getElementById('dataInicio');
        const horaInicio = document.getElementById('horaInicio');
        const dataFim = document.getElementById('dataFim');
        const horaFim = document.getElementById('horaFim');

        if (dataInicio && horaInicio && dataFim && horaFim) {
            const hoje = new Date();
            const dataFormatada = hoje.toISOString().split('T')[0];
            const horaFormatada = '14:00';

            dataInicio.value = dataFormatada;
            horaInicio.value = horaFormatada;

            const amanha = new Date();
            amanha.setDate(hoje.getDate() + 1);
            dataFim.value = amanha.toISOString().split('T')[0];
            horaFim.value = '16:00';
        }

        // Configurar botões de salvar
        const salvarManutencao = document.getElementById('salvarManutencao');
        if (salvarManutencao) {
            salvarManutencao.addEventListener('click', function() {
                // Obter valores do formulário
                const descricao = document.getElementById('descricaoServico').value;
                const pecas = document.getElementById('pecasUtilizadas').value;
                const observacoes = document.getElementById('observacoesManutencao').value;

                // Atualizar o preview no card
                const manutencaoPreview = document.getElementById('manutencao-preview');
                if (manutencaoPreview) {
                    manutencaoPreview.innerHTML = descricao.substring(0, 100) + (descricao.length > 100 ? '...' : '');
                }

                // Fechar o modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('manutencaoModal'));
                modal.hide();

                // Mostrar notificação
                showNotification('Informações de manutenção salvas com sucesso!', 'success');
            });
        }

        const salvarCustos = document.getElementById('salvarCustos');
        if (salvarCustos) {
            salvarCustos.addEventListener('click', function() {
                // Obter valores do formulário
                const pecas = document.getElementById('custoPecas').value;
                const maoObra = document.getElementById('custoMaoObra').value;
                const deslocamento = document.getElementById('custoDeslocamento').value;
                const total = document.getElementById('custoTotal').value;

                // Atualizar o preview no card
                const custosPreview = document.getElementById('custos-preview');
                if (custosPreview) {
                    custosPreview.innerHTML = `
                        <div class="d-flex justify-content-between">
                            <span>Peças:</span>
                            <span>R$ ${parseFloat(pecas).toFixed(2)}</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Mão de obra:</span>
                            <span>R$ ${parseFloat(maoObra).toFixed(2)}</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Deslocamento:</span>
                            <span>R$ ${parseFloat(deslocamento).toFixed(2)}</span>
                        </div>
                        <div class="d-flex justify-content-between mt-2 text-warning fw-bold">
                            <span>Total:</span>
                            <span>R$ ${parseFloat(total).toFixed(2)}</span>
                        </div>
                    `;
                }

                // Fechar o modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('custosModal'));
                modal.hide();

                // Mostrar notificação
                showNotification('Informações de custos salvas com sucesso!', 'success');
            });
        }

        const salvarCronograma = document.getElementById('salvarCronograma');
        if (salvarCronograma) {
            salvarCronograma.addEventListener('click', function() {
                // Obter valores do formulário
                const dataInicio = document.getElementById('dataInicio').value;
                const horaInicio = document.getElementById('horaInicio').value;
                const dataFim = document.getElementById('dataFim').value;
                const horaFim = document.getElementById('horaFim').value;
                const status = document.getElementById('statusServico').value;

                // Formatar as datas
                const dataInicioFormatada = formatarData(dataInicio);
                const dataFimFormatada = formatarData(dataFim);

                // Atualizar o preview no card
                const scheduleStart = document.getElementById('schedule-start');
                const scheduleEnd = document.getElementById('schedule-end');
                if (scheduleStart && scheduleEnd) {
                    scheduleStart.textContent = `${dataInicioFormatada} ${horaInicio}`;
                    scheduleEnd.textContent = `${dataFimFormatada} ${horaFim}`;
                }

                // Fechar o modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('cronogramaModal'));
                modal.hide();

                // Mostrar notificação
                showNotification('Informações de cronograma salvas com sucesso!', 'success');
            });
        }
    }

    // Função para enviar mensagem no chat
    function enviarMensagemChat() {
        const chatMessageInput = document.getElementById('chatMessageInput');
        const chatArea = document.getElementById('chatArea');

        if (chatMessageInput && chatArea && chatMessageInput.value.trim() !== '') {
            const mensagem = chatMessageInput.value.trim();
            const agora = new Date();
            const hora = agora.getHours().toString().padStart(2, '0');
            const minutos = agora.getMinutes().toString().padStart(2, '0');

            const novaMensagem = document.createElement('div');
            novaMensagem.className = 'chat-message mb-2 text-end';
            novaMensagem.innerHTML = `
                <small class="text-info">(${hora}:${minutos}) Você:</small>
                <p class="m-0 bg-primary p-2 rounded d-inline-block">${mensagem}</p>
            `;

            chatArea.appendChild(novaMensagem);
            chatArea.scrollTop = chatArea.scrollHeight;
            chatMessageInput.value = '';

            // Simular resposta após 1-2 segundos
            setTimeout(() => {
                simularRespostaChat();
            }, Math.random() * 1000 + 1000);
        }
    }

    // Função para simular resposta no chat
    function simularRespostaChat() {
        const chatArea = document.getElementById('chatArea');

        if (chatArea) {
            const respostas = [
                { remetente: 'Gerente', mensagem: 'Entendido. Vou verificar a disponibilidade.' },
                { remetente: 'Financeiro', mensagem: 'Aprovado. Pode prosseguir com a compra.' },
                { remetente: 'Gerente', mensagem: 'Precisa de mais alguma informação?' },
                { remetente: 'Financeiro', mensagem: 'Por favor, envie a nota fiscal quando concluir.' }
            ];

            const resposta = respostas[Math.floor(Math.random() * respostas.length)];
            const agora = new Date();
            const hora = agora.getHours().toString().padStart(2, '0');
            const minutos = agora.getMinutes().toString().padStart(2, '0');

            const novaMensagem = document.createElement('div');
            novaMensagem.className = 'chat-message mb-2 text-start';
            novaMensagem.innerHTML = `
                <small class="text-warning">${resposta.remetente} (${hora}:${minutos}):</small>
                <p class="m-0 bg-secondary p-2 rounded d-inline-block">${resposta.mensagem}</p>
            `;

            chatArea.appendChild(novaMensagem);
            chatArea.scrollTop = chatArea.scrollHeight;
        }
    }

    // Função para formatar data (YYYY-MM-DD para DD/MM/YYYY)
    function formatarData(dataString) {
        if (!dataString) return '';
        const partes = dataString.split('-');
        if (partes.length !== 3) return dataString;
        return `${partes[2]}/${partes[1]}/${partes[0]}`;
    }

    // Função para mostrar notificações
    function showNotification(message, type = 'info') {
        console.log(`Notificação (${type}): ${message}`);

        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-icon">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
            </div>
            <div class="notification-content">
                <p>${message}</p>
            </div>
            <button class="notification-close"><i class="fas fa-times"></i></button>
        `;

        document.body.appendChild(notification);

        // Adiciona evento para fechar a notificação
        notification.querySelector('.notification-close').addEventListener('click', function() {
            notification.remove();
        });

        // Remove a notificação após 5 segundos
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                notification.remove();
            }, 500);
        }, 5000);
    }
});
