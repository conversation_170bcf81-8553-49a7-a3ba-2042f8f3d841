/**
 * ManutencaoOrdem.js - Script para a página de manutenção de ordens
 * Sistema Tradição - Módulo de Técnicos
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elementos do DOM
    const flipCard = document.getElementById('flipCard');
    const calendarFront = document.getElementById('calendarFront');
    const calendarBack = document.getElementById('calendarBack');
    const calendarDays = document.getElementById('calendarDays');
    const monthYearText = document.getElementById('monthYearText');
    const prevMonthBtn = document.getElementById('prevMonthBtn');
    const nextMonthBtn = document.getElementById('nextMonthBtn');
    const backToCalendarBtn = document.getElementById('backToCalendarBtn');

    // Data atual
    let currentDate = new Date();

    // Dados de exemplo para ordens de manutenção
    const ordens = [
        { id: 1, dia: 5, titulo: "Manutenção Preventiva", equipamento: "Bomba de Combustível #3", status: "pendente" },
        { id: 2, dia: 12, titulo: "Troca de Filtro", equipamento: "Filtro de Combustível #2", status: "em_andamento" },
        { id: 3, dia: 15, titulo: "Calibração", equipamento: "Bomba de Combustível #1", status: "concluida" },
        { id: 4, dia: 20, titulo: "Reparo Emergencial", equipamento: "Tanque de Armazenamento #2", status: "em_andamento" },
        { id: 5, dia: 25, titulo: "Inspeção de Segurança", equipamento: "Sistema Elétrico", status: "pendente" }
    ];

    // Inicialização
    init();

    /**
     * Inicializa a página
     */
    function init() {
        renderCalendar();
        setupEventListeners();

        // Adiciona mensagens de exemplo ao chat
        addSampleChatMessages();
    }

    /**
     * Configura os listeners de eventos
     */
    function setupEventListeners() {
        // Navegação do calendário
        if (prevMonthBtn) {
            prevMonthBtn.addEventListener('click', function() {
                currentDate.setMonth(currentDate.getMonth() - 1);
                renderCalendar();
            });
        }

        if (nextMonthBtn) {
            nextMonthBtn.addEventListener('click', function() {
                currentDate.setMonth(currentDate.getMonth() + 1);
                renderCalendar();
            });
        }

        // Botão para voltar ao calendário
        if (backToCalendarBtn) {
            backToCalendarBtn.addEventListener('click', function() {
                flipCard.classList.remove('flipped');
                setTimeout(() => {
                    calendarBack.style.display = 'none';
                    calendarFront.style.display = 'block';
                }, 300);
            });
        }

        // Botões de salvar nos cards
        document.querySelectorAll('.card button').forEach(button => {
            button.addEventListener('click', function() {
                const cardId = this.closest('.card').id;
                saveCardData(cardId);
            });
        });

        // Botão de enviar mensagem no chat
        const chatSendBtn = document.querySelector('#cardInteracao .chat-input button');
        if (chatSendBtn) {
            chatSendBtn.addEventListener('click', sendChatMessage);
        }
    }

    /**
     * Renderiza o calendário com base na data atual
     */
    function renderCalendar() {
        if (!calendarDays || !monthYearText) return;

        const year = currentDate.getFullYear();
        const month = currentDate.getMonth();

        // Atualiza o texto do mês e ano
        const monthNames = ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
                           'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'];
        monthYearText.textContent = `${monthNames[month]} ${year}`;

        // Limpa o calendário
        calendarDays.innerHTML = '';

        // Obtém o primeiro dia do mês
        const firstDay = new Date(year, month, 1);
        // Obtém o último dia do mês
        const lastDay = new Date(year, month + 1, 0);

        // Número de dias no mês
        const daysInMonth = lastDay.getDate();

        // Dia da semana do primeiro dia (0 = Domingo, 6 = Sábado)
        const firstDayIndex = firstDay.getDay();

        // Adiciona os dias da semana
        const weekdays = document.createElement('div');
        weekdays.className = 'calendar-weekdays';

        ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'].forEach(day => {
            const dayElement = document.createElement('div');
            dayElement.textContent = day;
            weekdays.appendChild(dayElement);
        });

        calendarDays.appendChild(weekdays);

        // Cria o grid de dias
        const daysGrid = document.createElement('div');
        daysGrid.className = 'calendar-grid';

        // Adiciona os dias vazios do início do mês
        for (let i = 0; i < firstDayIndex; i++) {
            const emptyDay = document.createElement('div');
            emptyDay.className = 'calendar-day empty';
            daysGrid.appendChild(emptyDay);
        }

        // Adiciona os dias do mês
        for (let day = 1; day <= daysInMonth; day++) {
            const dayElement = document.createElement('div');
            dayElement.className = 'calendar-day';
            dayElement.textContent = day;

            // Verifica se é hoje
            const today = new Date();
            if (day === today.getDate() && month === today.getMonth() && year === today.getFullYear()) {
                dayElement.classList.add('today');
            }

            // Verifica se tem ordens para este dia
            const ordensNoDia = ordens.filter(ordem => ordem.dia === day);
            if (ordensNoDia.length > 0) {
                dayElement.classList.add('has-event');

                // Adiciona indicador de evento
                const indicator = document.createElement('div');
                indicator.className = 'event-indicator';
                dayElement.appendChild(indicator);

                // Adiciona as ordens como itens clicáveis
                ordensNoDia.forEach(ordem => {
                    const orderItem = document.createElement('div');
                    orderItem.className = 'order-item';
                    orderItem.textContent = ordem.titulo;
                    orderItem.dataset.id = ordem.id;
                    orderItem.addEventListener('click', function(e) {
                        e.stopPropagation(); // Evita que o clique propague para o dia
                        showOrderDetails(ordem);
                    });
                    dayElement.appendChild(orderItem);
                });
            }

            // Adiciona evento de clique no dia
            dayElement.addEventListener('click', function() {
                if (ordensNoDia.length === 1) {
                    showOrderDetails(ordensNoDia[0]);
                } else if (ordensNoDia.length > 1) {
                    // Aqui poderia mostrar um modal para escolher qual ordem visualizar
                    showOrderDetails(ordensNoDia[0]); // Por enquanto, mostra a primeira
                }
            });

            daysGrid.appendChild(dayElement);
        }

        calendarDays.appendChild(daysGrid);
    }

    /**
     * Mostra os detalhes de uma ordem específica
     * @param {Object} ordem - Objeto com os dados da ordem
     */
    function showOrderDetails(ordem) {
        // Verificar se a ordem é a ordem #18 (hardcoded) e bloqueá-la
        if (ordem.id === 18 || ordem.id === "18") {
            console.error('Tentativa de mostrar a ordem #18 que é inválida/hardcoded');
            alert('Ordem #18 não está disponível. Por favor, selecione outra ordem.');
            return;
        }

        // Atualiza os campos do verso do cartão com os detalhes da ordem
        document.querySelector('#cardManutencao h2').textContent = `Manutenção - ${ordem.titulo}`;
        document.querySelector('#cardManutencao textarea').placeholder = `Insira informações sobre a manutenção do ${ordem.equipamento}...`;

        document.querySelector('#cardPecas h2').textContent = `Peças - ${ordem.equipamento}`;
        document.querySelector('#cardPecas p').textContent = `Peças a serem trocadas, encomendadas ou compradas para ${ordem.equipamento}.`;

        document.querySelector('#cardCustos h2').textContent = `Custos - ${ordem.titulo}`;

        document.querySelector('#cardInteracao h2').textContent = `Interação - Ordem #${ordem.id}`;

        // Vira o cartão
        flipCard.classList.add('flipped');
        calendarFront.style.display = 'none';
        calendarBack.style.display = 'block';
    }

    /**
     * Salva os dados de um card específico
     * @param {string} cardId - ID do card a ser salvo
     */
    function saveCardData(cardId) {
        // Aqui seria implementada a lógica para salvar os dados no backend
        // Por enquanto, apenas mostra um alerta

        let message = '';

        switch (cardId) {
            case 'cardManutencao':
                const descricao = document.querySelector('#cardManutencao textarea').value;
                message = `Informações de manutenção salvas: ${descricao}`;
                break;

            case 'cardPecas':
                message = 'Informações de peças salvas';
                break;

            case 'cardCustos':
                const maoDeObra = document.querySelector('#cardCustos input:nth-of-type(1)').value;
                const pecas = document.querySelector('#cardCustos input:nth-of-type(2)').value;
                const frete = document.querySelector('#cardCustos input:nth-of-type(3)').value;

                const total = (parseFloat(maoDeObra || 0) + parseFloat(pecas || 0) + parseFloat(frete || 0)).toFixed(2);

                message = `Custos salvos. Total: R$ ${total}`;
                break;

            case 'cardInteracao':
                message = 'Mensagem enviada';
                break;
        }

        alert(message);
    }

    /**
     * Envia uma mensagem no chat
     */
    function sendChatMessage() {
        const chatInput = document.querySelector('#cardInteracao .chat-input textarea');
        const chatMessages = document.getElementById('chatMessages');

        if (!chatInput || !chatMessages) return;

        const message = chatInput.value.trim();
        if (!message) return;

        // Cria o elemento da mensagem
        const messageElement = document.createElement('div');
        messageElement.className = 'chat-message';

        const now = new Date();
        const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

        messageElement.innerHTML = `
            <div class="chat-message-header">
                <span class="chat-message-sender">Técnico</span>
                <span class="chat-message-time">${timeStr}</span>
            </div>
            <div class="chat-message-content">${message}</div>
        `;

        // Adiciona a mensagem ao chat
        chatMessages.appendChild(messageElement);

        // Limpa o input
        chatInput.value = '';

        // Rola para o final do chat
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    /**
     * Adiciona mensagens de exemplo ao chat
     */
    function addSampleChatMessages() {
        const chatMessages = document.getElementById('chatMessages');
        if (!chatMessages) return;

        const messages = [
            { sender: 'Sistema', content: 'Ordem de manutenção criada', time: '09:30' },
            { sender: 'Filial', content: 'Precisamos de manutenção urgente na bomba de combustível #3', time: '09:35' },
            { sender: 'Técnico', content: 'Recebido. Estarei no local amanhã às 10h', time: '10:15' }
        ];

        messages.forEach(msg => {
            const messageElement = document.createElement('div');
            messageElement.className = 'chat-message';

            messageElement.innerHTML = `
                <div class="chat-message-header">
                    <span class="chat-message-sender">${msg.sender}</span>
                    <span class="chat-message-time">${msg.time}</span>
                </div>
                <div class="chat-message-content">${msg.content}</div>
            `;

            chatMessages.appendChild(messageElement);
        });
    }
});
