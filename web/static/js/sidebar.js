/**
 * Sidebar Manager - Sistema de Manutenção Shell
 * Gerencia o comportamento do menu lateral em todas as páginas
 */
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content') || document.getElementById('content');
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const mobileToggle = document.getElementById('mobile-toggle');
    const logoutBtn = document.getElementById('logout-btn');

    // Carregar estado do sidebar do localStorage
    const sidebarCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';

    if (sidebarCollapsed) {
        sidebar.classList.add('collapsed');
        if (mainContent) mainContent.classList.add('expanded');
        if (sidebarToggle) sidebarToggle.innerHTML = '<i class="fas fa-chevron-right"></i>';
    }

    // Toggle do sidebar desktop
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            if (mainContent) mainContent.classList.toggle('expanded');

            const isCollapsed = sidebar.classList.contains('collapsed');

            // Atualizar ícone
            sidebarToggle.innerHTML = isCollapsed ?
                '<i class="fas fa-chevron-right"></i>' :
                '<i class="fas fa-chevron-left"></i>';

            // Salvar estado no localStorage
            localStorage.setItem('sidebar-collapsed', isCollapsed);

            // Disparar um evento para notificar outras partes da aplicação
            document.dispatchEvent(new CustomEvent('sidebar-toggle', {
                detail: { collapsed: isCollapsed }
            }));
        });
    }

    // Toggle do sidebar mobile
    if (mobileToggle) {
        mobileToggle.addEventListener('click', function() {
            sidebar.classList.toggle('mobile-visible');
        });

        // Fechar sidebar móvel ao clicar fora
        document.addEventListener('click', function(event) {
            if (!sidebar.contains(event.target) && !mobileToggle.contains(event.target) &&
                sidebar.classList.contains('mobile-visible')) {
                sidebar.classList.remove('mobile-visible');
            }
        });
    }

    // Manipulador de logout
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // Verificar se a função global de logout existe
            if (typeof logout === 'function') {
                // Usar a função global de logout
                logout();
            } else {
                // Fallback: fazer logout através da API diretamente
                fetch('/api/auth/logout', {
                    method: 'POST',
                    credentials: 'same-origin'
                })
                .then(response => {
                    if (response.ok) {
                        window.location.href = '/login';
                    } else {
                        throw new Error('Falha ao fazer logout');
                    }
                })
                .catch(error => {
                    console.error('Erro de logout:', error);
                    // Fallback para redirecionamento direto em caso de erro
                    window.location.href = '/login';
                });
            }
        });
    }

    // Inicializar carregador de frases
    loadFunnyQuotes();

    // Função para carregar frases aleatórias
    function loadFunnyQuotes() {
        const quotes = [
            "Um carro bem cuidado é como um tanque cheio: ele te leva mais longe! 🚗",
            "Bomba calibrada, cliente satisfeito! 💧 Seu posto Shell, sua segurança!",
            "A energia que move a sua frota é a mesma que move o nosso compromisso! ⚡",
            "Uma manutenção em dia garante a segurança de todos! 🔧",
            "Entre para abastecer, saia para impressionar! 🚀",
            "Shell: Porque seu carro merece o melhor combustível e o melhor cuidado! ✨",
            "O caminho para um motor feliz passa por um posto Shell! 😊",
            "Combustível de qualidade e manutenção em dia: a dupla perfeita! 🤝",
            "Abasteça aqui, vá mais longe! 🛣️",
            "O sucesso da sua empresa está diretamente ligado à manutenção da sua frota! 📈"
        ];

        const quoteElement = document.getElementById('quote-text');
        if (!quoteElement) return;

        // Iniciar com uma frase aleatória
        let currentIndex = Math.floor(Math.random() * quotes.length);
        quoteElement.textContent = quotes[currentIndex];

        // Atualizar a frase a cada 30 segundos
        setInterval(() => {
            currentIndex = (currentIndex + 1) % quotes.length;
            quoteElement.textContent = quotes[currentIndex];
        }, 30000);
    }
});