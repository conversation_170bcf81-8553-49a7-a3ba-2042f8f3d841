// Dados dos impostos por combustível e estado
const IMPOSTOS_COMBUSTIVEIS = {
    "Gasolina C (comum ou aditivada)": {
        "AC": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "AL": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.25,
            "total": 0.5193
        },
        "AM": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "AP": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "BA": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "CE": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "DF": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "ES": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "GO": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "MA": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "MG": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "MS": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "MT": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PA": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PB": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PE": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PI": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PR": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RJ": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RN": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RO": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RR": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RS": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "SC": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "SE": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "SP": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "TO": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        }
    },
    "Etanol Hidratado": {
        "AC": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.17,
            "pmpf": 0.02,
            "icmsTotal": 0.19,
            "total": 0.3593
        },
        "AL": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.25,
            "pmpf": 0.02,
            "icmsTotal": 0.27,
            "total": 0.4393
        },
        "AM": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.20,
            "pmpf": 0.02,
            "icmsTotal": 0.22,
            "total": 0.3893
        },
        "AP": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.18,
            "pmpf": 0.02,
            "icmsTotal": 0.20,
            "total": 0.3493
        },
        "BA": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.1286,
            "pmpf": 0.02,
            "icmsTotal": 0.15,
            "total": 0.2883
        },
        "CE": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.20,
            "pmpf": 0.02,
            "icmsTotal": 0.22,
            "total": 0.3893
        },
        "DF": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.13,
            "pmpf": 0.02,
            "icmsTotal": 0.15,
            "total": 0.2893
        },
        "ES": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.17,
            "pmpf": 0.02,
            "icmsTotal": 0.19,
            "total": 0.3493
        },
        "GO": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.1417,
            "pmpf": 0.02,
            "icmsTotal": 0.16,
            "total": 0.3093
        },
        "MA": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.22,
            "pmpf": 0.02,
            "icmsTotal": 0.24,
            "total": 0.3893
        },
        "MG": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.1308,
            "pmpf": 0.02,
            "icmsTotal": 0.15,
            "total": 0.2893
        },
        "MS": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.1133,
            "pmpf": 0.02,
            "icmsTotal": 0.13,
            "total": 0.2593
        },
        "MT": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.17,
            "pmpf": 0.02,
            "icmsTotal": 0.19,
            "total": 0.3493
        },
        "PA": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.1696,
            "pmpf": 0.02,
            "icmsTotal": 0.19,
            "total": 0.3493
        },
        "PB": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.1533,
            "pmpf": 0.02,
            "icmsTotal": 0.17,
            "total": 0.3293
        },
        "PE": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.1552,
            "pmpf": 0.02,
            "icmsTotal": 0.17,
            "total": 0.3293
        },
        "PI": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.1490,
            "pmpf": 0.02,
            "icmsTotal": 0.17,
            "total": 0.3293
        },
        "PR": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.12,
            "pmpf": 0.02,
            "icmsTotal": 0.14,
            "total": 0.2693
        },
        "RJ": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.1687,
            "pmpf": 0.02,
            "icmsTotal": 0.19,
            "total": 0.3493
        },
        "RN": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.1533,
            "pmpf": 0.02,
            "icmsTotal": 0.17,
            "total": 0.3293
        },
        "RO": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.1750,
            "pmpf": 0.02,
            "icmsTotal": 0.19,
            "total": 0.3493
        },
        "RR": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.20,
            "pmpf": 0.02,
            "icmsTotal": 0.22,
            "total": 0.3893
        },
        "RS": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.17,
            "pmpf": 0.02,
            "icmsTotal": 0.19,
            "total": 0.3493
        },
        "SC": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.17,
            "pmpf": 0.02,
            "icmsTotal": 0.19,
            "total": 0.3493
        },
        "SE": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.19,
            "pmpf": 0.02,
            "icmsTotal": 0.21,
            "total": 0.3893
        },
        "SP": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.12,
            "pmpf": 0.02,
            "icmsTotal": 0.14,
            "total": 0.2693
        },
        "TO": {
            "pisCofinsProd": 0.0768,
            "pisCofinsDistr": 0.0925,
            "pisCofinsEtanol": 0.1693,
            "icms": 0.20,
            "pmpf": 0.02,
            "icmsTotal": 0.22,
            "total": 0.3893
        }
    },
    "Diesel S-500 (comum ou aditivado)": {
        "AC": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "AL": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.25,
            "total": 0.5193
        },
        "AM": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "AP": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "BA": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "CE": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "DF": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "ES": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "GO": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "MA": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "MG": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "MS": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "MT": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PA": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PB": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PE": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PI": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PR": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RJ": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RN": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RO": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RR": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RS": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "SC": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "SE": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "SP": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "TO": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        }
    },
    "Diesel S-10 (comum ou aditivado)": {
        "AC": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "AL": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.25,
            "total": 0.5193
        },
        "AM": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "AP": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "BA": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "CE": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "DF": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "ES": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "GO": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "MA": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "MG": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "MS": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "MT": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PA": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PB": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PE": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PI": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PR": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RJ": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RN": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RO": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RR": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RS": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "SC": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "SE": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "SP": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "TO": {
            "cide": 0.10,
            "pisCofinsDiesel": 0.0768,
            "pisCofinsBio": 0.0925,
            "mistura": 0.10,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        }
    },
    "Gasolina Premium": {
        "AC": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "AL": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.25,
            "total": 0.5193
        },
        "AM": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "AP": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "BA": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "CE": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "DF": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "ES": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "GO": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "MA": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "MG": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "MS": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "MT": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PA": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PB": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PE": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PI": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "PR": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RJ": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RN": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RO": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RR": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "RS": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "SC": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "SE": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "SP": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        },
        "TO": {
            "cide": 0.10,
            "pisCofinsProduto": 0.0768,
            "pisCofinsAeac": 0.0925,
            "mistura": 0.27,
            "cidePisCof": 0.2693,
            "icms": 0.17,
            "total": 0.4393
        }
    }
};

// Função utilitária para buscar os impostos de um combustível específico
function getImpostosCombustivel(tipoCombustivel, estado) {
    if (!IMPOSTOS_COMBUSTIVEIS[tipoCombustivel]) {
        throw new Error(`Tipo de combustível inválido: ${tipoCombustivel}`);
    }

    const impostosEstado = IMPOSTOS_COMBUSTIVEIS[tipoCombustivel][estado];
    if (!impostosEstado) {
        throw new Error(`Estado inválido: ${estado}`);
    }

    return {
        nome: tipoCombustivel,
        impostos: impostosEstado
    };
}

// Exporta as constantes e funções
export { IMPOSTOS_COMBUSTIVEIS, getImpostosCombustivel }; 