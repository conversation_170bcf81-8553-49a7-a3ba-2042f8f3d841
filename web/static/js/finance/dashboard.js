// Configurações Globais
const CONFIG = {
    refreshInterval: 300000, // 5 minutos
    colors: {
        primary: '#2c3e50',
        secondary: '#34495e',
        accent: '#3498db',
        success: '#2ecc71',
        warning: '#f1c40f',
        danger: '#e74c3c'
    }
};

// Inicialização
document.addEventListener('DOMContentLoaded', () => {
    initializeCharts();
    setupEventListeners();
    startAutoRefresh();
    loadInitialData();
});

// Gráficos
function initializeCharts() {
    // Gráfico de Despesas
    const expensesCtx = document.getElementById('expensesChart').getContext('2d');
    window.expensesChart = new Chart(expensesCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Despesas',
                data: [],
                borderColor: CONFIG.colors.danger,
                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: value => formatCurrency(value)
                    }
                }
            }
        }
    });

    // Gráfico de Distribuição de Custos
    const costsCtx = document.getElementById('costsChart').getContext('2d');
    window.costsChart = new Chart(costsCtx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    CONFIG.colors.accent,
                    CONFIG.colors.success,
                    CONFIG.colors.warning,
                    CONFIG.colors.danger
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                }
            }
        }
    });
}

// Event Listeners
function setupEventListeners() {
    // Filtro de Data
    const dateFilter = document.getElementById('dateFilter');
    if (dateFilter) {
        dateFilter.addEventListener('change', () => {
            updateDashboardData();
        });
    }

    // Botões de Exportação
    const exportButtons = document.querySelectorAll('[data-export]');
    exportButtons.forEach(button => {
        button.addEventListener('click', () => {
            const format = button.dataset.export;
            exportReport(format);
        });
    });

    // Atualização de Cotações
    const refreshQuotesBtn = document.getElementById('refreshQuotes');
    if (refreshQuotesBtn) {
        refreshQuotesBtn.addEventListener('click', () => {
            updateQuotes();
        });
    }
}

// Atualização Automática
function startAutoRefresh() {
    setInterval(() => {
        updateDashboardData();
    }, CONFIG.refreshInterval);
}

// Carregamento Inicial
async function loadInitialData() {
    try {
        await updateDashboardData();
        await updateQuotes();
        await loadPayments();
        await loadNews();
    } catch (error) {
        console.error('Erro ao carregar dados iniciais:', error);
        showError('Erro ao carregar dados. Tente novamente mais tarde.');
    }
}

// Atualização de Dados
async function updateDashboardData() {
    try {
        const dateRange = document.getElementById('dateFilter').value;
        const response = await fetch(`/api/finance/dashboard?period=${dateRange}`);
        
        if (!response.ok) {
            throw new Error('Erro ao buscar dados');
        }

        const data = await response.json();
        updateMetrics(data.metrics);
        updateCharts(data.charts);
    } catch (error) {
        console.error('Erro ao atualizar dados:', error);
        showError('Erro ao atualizar dados. Tente novamente mais tarde.');
    }
}

// Atualização de Métricas
function updateMetrics(metrics) {
    const elements = {
        revenue: document.getElementById('totalRevenue'),
        expenses: document.getElementById('totalExpenses'),
        profit: document.getElementById('totalProfit'),
        balance: document.getElementById('totalBalance')
    };

    for (const [key, element] of Object.entries(elements)) {
        if (element && metrics[key] !== undefined) {
            element.textContent = formatCurrency(metrics[key]);
        }
    }
}

// Atualização de Gráficos
function updateCharts(chartData) {
    // Atualizar gráfico de despesas
    if (window.expensesChart && chartData.expenses) {
        window.expensesChart.data.labels = chartData.expenses.labels;
        window.expensesChart.data.datasets[0].data = chartData.expenses.data;
        window.expensesChart.update();
    }

    // Atualizar gráfico de distribuição de custos
    if (window.costsChart && chartData.costs) {
        window.costsChart.data.labels = chartData.costs.labels;
        window.costsChart.data.datasets[0].data = chartData.costs.data;
        window.costsChart.update();
    }
}

// Atualização de Cotações
async function updateQuotes() {
    try {
        const response = await fetch('/api/finance/quotes');
        
        if (!response.ok) {
            throw new Error('Erro ao buscar cotações');
        }

        const quotes = await response.json();
        const quotesContainer = document.getElementById('quotesGrid');
        
        if (quotesContainer) {
            quotesContainer.innerHTML = quotes.map(quote => `
                <div class="quote-item">
                    <h4>${quote.symbol}</h4>
                    <div class="value">${formatCurrency(quote.value)}</div>
                    <div class="change ${quote.change >= 0 ? 'positive' : 'negative'}">
                        ${quote.change >= 0 ? '+' : ''}${quote.change}%
                    </div>
                </div>
            `).join('');
        }
    } catch (error) {
        console.error('Erro ao atualizar cotações:', error);
        showError('Erro ao atualizar cotações. Tente novamente mais tarde.');
    }
}

// Carregamento de Pagamentos
async function loadPayments() {
    try {
        const response = await fetch('/api/finance/payments');
        
        if (!response.ok) {
            throw new Error('Erro ao buscar pagamentos');
        }

        const payments = await response.json();
        const paymentsContainer = document.getElementById('paymentsList');
        
        if (paymentsContainer) {
            paymentsContainer.innerHTML = payments.map(payment => `
                <div class="payment-item">
                    <div class="payment-info">
                        <h4>${payment.description}</h4>
                        <p>${formatDate(payment.dueDate)}</p>
                    </div>
                    <div class="payment-amount">
                        <div class="amount">${formatCurrency(payment.amount)}</div>
                        <span class="status ${payment.status.toLowerCase()}">${payment.status}</span>
                    </div>
                </div>
            `).join('');
        }
    } catch (error) {
        console.error('Erro ao carregar pagamentos:', error);
        showError('Erro ao carregar pagamentos. Tente novamente mais tarde.');
    }
}

// Carregamento de Notícias
async function loadNews() {
    try {
        const response = await fetch('/api/finance/news');
        
        if (!response.ok) {
            throw new Error('Erro ao buscar notícias');
        }

        const news = await response.json();
        const newsContainer = document.getElementById('newsList');
        
        if (newsContainer) {
            newsContainer.innerHTML = news.map(item => `
                <div class="news-item">
                    <div class="news-content">
                        <h4>${item.title}</h4>
                        <p>${item.summary}</p>
                        <div class="news-meta">
                            <span>${formatDate(item.date)}</span>
                            <span>${item.source}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }
    } catch (error) {
        console.error('Erro ao carregar notícias:', error);
        showError('Erro ao carregar notícias. Tente novamente mais tarde.');
    }
}

// Exportação de Relatórios
async function exportReport(format) {
    try {
        const dateRange = document.getElementById('dateFilter').value;
        const response = await fetch(`/api/finance/export?format=${format}&period=${dateRange}`);
        
        if (!response.ok) {
            throw new Error('Erro ao exportar relatório');
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `relatorio-financeiro-${formatDate(new Date())}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    } catch (error) {
        console.error('Erro ao exportar relatório:', error);
        showError('Erro ao exportar relatório. Tente novamente mais tarde.');
    }
}

// Utilitários
function formatCurrency(value) {
    return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
    }).format(value);
}

function formatDate(date) {
    return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    }).format(new Date(date));
}

function showError(message) {
    // Implementar lógica de exibição de erro (toast, alert, etc.)
    console.error(message);
}

// API
const API = {
    async get(endpoint) {
        const response = await fetch(endpoint);
        if (!response.ok) {
            throw new Error(`Erro na requisição: ${response.statusText}`);
        }
        return response.json();
    },

    async post(endpoint, data) {
        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        if (!response.ok) {
            throw new Error(`Erro na requisição: ${response.statusText}`);
        }
        return response.json();
    }
}; 