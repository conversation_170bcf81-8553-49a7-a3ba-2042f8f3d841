document.addEventListener('DOMContentLoaded', function() {
    // Inicializar máscaras para campos monetários
    $('.money').maskMoney({
        prefix: 'R$ ',
        thousands: '.',
        decimal: ',',
        allowZero: true
    });
    
    // Handler para o formulário de cálculo
    document.getElementById('form-margem').addEventListener('submit', function(e) {
        e.preventDefault();
        calcularMargem();
    });
    
    // Handler para o botão de resetar
    document.getElementById('btn-resetar').addEventListener('click', function() {
        resetarFormulario();
    });
    
    /**
     * Calcula as margens de lucro com base nos valores informados
     */
    function calcularMargem() {
        // Obter valores dos inputs
        const custoInput = document.getElementById('custo');
        const precoVendaInput = document.getElementById('preco-venda');
        
        // Remover máscaras para obter apenas os números
        const custoValor = parseFloat(custoInput.value.replace(/[^\d,]/g, '').replace(',', '.'));
        const precoVendaValor = parseFloat(precoVendaInput.value.replace(/[^\d,]/g, '').replace(',', '.'));
        
        // Validar os valores
        if (isNaN(custoValor) || isNaN(precoVendaValor)) {
            exibirErro('Por favor, informe valores válidos para custo e preço de venda.');
            return;
        }
        
        if (custoValor <= 0 || precoVendaValor <= 0) {
            exibirErro('Os valores de custo e preço de venda devem ser maiores que zero.');
            return;
        }
        
        if (precoVendaValor < custoValor) {
            exibirErro('O preço de venda deve ser maior que o custo do produto.');
            return;
        }
        
        // Calcular margens
        const margemBruta = precoVendaValor - custoValor;
        const margemBrutaPercentual = (margemBruta / precoVendaValor) * 100;
        
        const custoPercentual = (custoValor / precoVendaValor) * 100;
        
        // Exibir resultados
        exibirResultados(custoValor, precoVendaValor, margemBruta, margemBrutaPercentual, custoPercentual);
    }
    
    /**
     * Exibe os resultados do cálculo de margem
     */
    function exibirResultados(custo, precoVenda, margemBruta, margemBrutaPercentual, custoPercentual) {
        // Formatar valores monetários
        const formatarMoeda = (valor) => {
            return valor.toLocaleString('pt-BR', {
                style: 'currency',
                currency: 'BRL'
            });
        };
        
        // Atualizar valores no card de resultados
        document.getElementById('resultado-custo').textContent = formatarMoeda(custo);
        document.getElementById('resultado-preco-venda').textContent = formatarMoeda(precoVenda);
        document.getElementById('resultado-margem-bruta').textContent = formatarMoeda(margemBruta);
        document.getElementById('resultado-margem-percentual').textContent = margemBrutaPercentual.toFixed(2) + '%';
        
        // Atualizar barras de progresso
        const barraMargemBruta = document.getElementById('barra-margem-bruta');
        const barraCusto = document.getElementById('barra-custo');
        
        barraMargemBruta.style.width = margemBrutaPercentual + '%';
        barraCusto.style.width = custoPercentual + '%';
        
        // Analisar os percentuais e dar feedback
        const feedbackMargemBruta = analisarMargemBruta(margemBrutaPercentual);
        document.getElementById('feedback-margem').textContent = feedbackMargemBruta.mensagem;
        document.getElementById('feedback-margem').className = 'feedback ' + feedbackMargemBruta.classe;
        
        // Mostrar o card de resultados
        document.getElementById('card-resultados').classList.remove('d-none');
    }
    
    /**
     * Analisa o percentual de margem bruta e retorna um feedback
     */
    function analisarMargemBruta(percentual) {
        if (percentual < 15) {
            return {
                mensagem: 'Margem baixa. Considere aumentar o preço ou reduzir custos.',
                classe: 'feedback-ruim'
            };
        } else if (percentual < 30) {
            return {
                mensagem: 'Margem razoável, mas há espaço para melhorias.',
                classe: 'feedback-medio'
            };
        } else if (percentual < 50) {
            return {
                mensagem: 'Boa margem de lucro!',
                classe: 'feedback-bom'
            };
        } else {
            return {
                mensagem: 'Excelente margem de lucro!',
                classe: 'feedback-excelente'
            };
        }
    }
    
    /**
     * Exibe uma mensagem de erro
     */
    function exibirErro(mensagem) {
        const alertaErro = document.getElementById('alerta-erro');
        alertaErro.textContent = mensagem;
        alertaErro.classList.remove('d-none');
        
        // Esconder o alerta após 5 segundos
        setTimeout(() => {
            alertaErro.classList.add('d-none');
        }, 5000);
        
        // Esconder o card de resultados se estiver visível
        document.getElementById('card-resultados').classList.add('d-none');
    }
    
    /**
     * Reseta o formulário e esconde os resultados
     */
    function resetarFormulario() {
        document.getElementById('form-margem').reset();
        document.getElementById('card-resultados').classList.add('d-none');
        document.getElementById('alerta-erro').classList.add('d-none');
    }
}); 