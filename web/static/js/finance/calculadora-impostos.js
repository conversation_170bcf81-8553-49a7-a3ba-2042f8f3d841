import { IMPOSTOS_COMBUSTIVEIS, getImpostosCombustivel } from './impostos.js';

/**
 * Classe para cálculo de impostos sobre combustíveis
 */
class CalculadoraImpostos {
    /**
     * Inicializa a calculadora de impostos
     */
    constructor() {
        this.impostos = IMPOSTOS_COMBUSTIVEIS;
    }

    /**
     * Calcula os impostos para um determinado tipo de combustível em um estado específico
     * @param {string} tipoCombustivel - O tipo de combustível para cálculo
     * @param {string} estado - Sigla do estado para cálculo
     * @param {number} valorBase - Valor base para cálculo
     * @returns {Object} Objeto com os impostos calculados
     */
    calcularImpostos(tipoCombustivel, estado, valorBase) {
        // Validar parâmetros
        if (!tipoCombustivel || !estado || valorBase === undefined || valorBase <= 0) {
            throw new Error('Parâmetros inválidos para cálculo de impostos');
        }
        
        // Buscar impostos para o tipo de combustível e estado especificados
        const impostosCombustivel = getImpostosCombustivel(tipoCombustivel, estado);
        if (!impostosCombustivel) {
            throw new Error(`Não foram encontrados impostos para ${tipoCombustivel} no estado ${estado}`);
        }
        
        // Calcular valor para cada imposto
        const resultado = {};
        
        for (const [imposto, valor] of Object.entries(impostosCombustivel)) {
            if (imposto !== 'total') {
                resultado[imposto] = {
                    percentual: valor * 100,
                    valor: valorBase * valor
                };
            }
        }
        
        // Adicionar total
        resultado.total = {
            percentual: impostosCombustivel.total * 100,
            valor: valorBase * impostosCombustivel.total
        };
        
        return resultado;
    }

    /**
     * Retorna a lista de estados disponíveis
     * @returns {Array} Array com siglas dos estados disponíveis
     */
    getEstadosDisponiveis() {
        // Obter o primeiro combustível para extrair os estados
        const primeiroCombustivel = Object.values(this.impostos)[0];
        
        if (primeiroCombustivel) {
            return Object.keys(primeiroCombustivel).sort();
        }
        
        return [];
    }

    /**
     * Retorna a lista de tipos de combustíveis disponíveis
     * @returns {Array} Array com objetos de tipos de combustível
     */
    getTiposCombustiveisDisponiveis() {
        return Object.keys(this.impostos).map(combustivel => {
            return {
                id: combustivel,
                nome: combustivel
            };
        });
    }
}

// Exportar a classe
export default CalculadoraImpostos; 