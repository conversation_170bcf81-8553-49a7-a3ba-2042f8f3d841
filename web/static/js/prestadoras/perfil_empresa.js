// JavaScript para a página Perfil da Empresa

document.addEventListener('DOMContentLoaded', function() {
    // Carregar dados da empresa
    carregarDadosEmpresa();

    // Configurar eventos
    configurarEventos();
});

// Função para carregar dados da empresa
function carregarDadosEmpresa() {
    // Configurar logo padrão para evitar erro 404
    const logoPreview = document.getElementById('logoPreview');
    if (logoPreview) {
        logoPreview.onerror = function() {
            this.src = '/static/img/default-company-logo.png';
            this.onerror = null; // Evita loop infinito
        };

        // Definir logo padrão inicialmente
        logoPreview.src = '/static/img/default-company-logo.png';
    }

    // Fazer requisição para a API
    fetch('/api/my-provider')
        .then(response => {
            if (!response.ok) {
                // Se for 404, não é um erro crítico - pode ser primeiro acesso
                if (response.status === 404) {
                    console.log('Empresa ainda não cadastrada. Usando formulário vazio.');
                    return {}; // Retorna objeto vazio para preencher formulário com valores padrão
                }
                throw new Error('Erro ao carregar dados da empresa');
            }
            return response.json();
        })
        .then(data => {
            // Se não houver dados, não tenta preencher
            if (Object.keys(data).length === 0) {
                // Preencher estatísticas com valores padrão
                preencherEstatisticasPadrao();
                return;
            }

            // Preencher formulário
            preencherFormulario(data);

            // Preencher logomarca
            if (data.logo_url) {
                logoPreview.src = data.logo_url;
            }

            // Preencher estatísticas
            preencherEstatisticas(data);

            // Formatar data de cadastro
            if (data.created_at) {
                const data_cadastro = new Date(data.created_at);
                document.getElementById('dataCadastro').textContent = data_cadastro.toLocaleDateString('pt-BR');
            }

            // Status
            document.getElementById('statusEmpresa').checked = data.status === 'active';
        })
        .catch(error => {
            console.error('Erro:', error);
            // Usar valores padrão em vez de mostrar alerta
            preencherEstatisticasPadrao();

            // Mostrar mensagem de erro no console, mas não para o usuário
            console.error('Erro ao carregar dados da empresa:', error.message);
        });
}

// Função para preencher formulário
function preencherFormulario(data) {
    // Dados básicos
    document.getElementById('nomeFantasia').value = data.name || '';
    document.getElementById('razaoSocial').value = data.company_name || '';
    document.getElementById('cnpj').value = data.cnpj || '';
    document.getElementById('areaAtuacao').value = data.area_of_expertise || '';

    // Endereço
    document.getElementById('endereco').value = data.address || '';
    document.getElementById('cidade').value = data.city || '';
    document.getElementById('estado').value = data.state || '';
    document.getElementById('cep').value = data.zip_code || '';

    // Contato
    document.getElementById('nomeContato').value = data.contact_name || '';
    document.getElementById('emailContato').value = data.contact_email || '';
    document.getElementById('telefoneContato').value = data.contact_phone || '';

    // Especialidades
    document.querySelectorAll('input[name="specialties"]').forEach(checkbox => {
        checkbox.checked = false;
    });

    if (data.specialties) {
        // Verificar se é string ou array
        let especialidades = data.specialties;
        if (typeof especialidades === 'string') {
            try {
                especialidades = JSON.parse(especialidades);
            } catch (e) {
                // Se não for JSON válido, tratar como string separada por vírgulas
                especialidades = especialidades.split(',').map(item => item.trim());
            }
        }

        // Marcar checkboxes
        if (Array.isArray(especialidades)) {
            especialidades.forEach(especialidade => {
                const checkbox = document.querySelector(`input[name="specialties"][value="${especialidade}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });
        }
    }
}

// Função para preencher estatísticas padrão
function preencherEstatisticasPadrao() {
    document.getElementById('estatTecnicos').textContent = '0';
    document.getElementById('estatOrdens').textContent = '0';
    document.getElementById('estatTempo').textContent = '0h';
    document.getElementById('estatTaxa').textContent = '0%';
}

// Função para preencher estatísticas
function preencherEstatisticas(data) {
    // Obter estatísticas da API
    fetch('/api/my-provider/statistics')
        .then(response => {
            if (!response.ok) {
                // Se for 404, não é um erro crítico
                if (response.status === 404) {
                    console.log('Estatísticas não encontradas. Usando valores padrão.');
                    preencherEstatisticasPadrao();
                    return;
                }
                throw new Error('Erro ao carregar estatísticas');
            }
            return response.json();
        })
        .then(stats => {
            // Se não houver dados, não tenta preencher
            if (!stats || Object.keys(stats).length === 0) {
                preencherEstatisticasPadrao();
                return;
            }

            // Preencher estatísticas
            document.getElementById('estatTecnicos').textContent = stats.technicians_count || 0;
            document.getElementById('estatOrdens').textContent = stats.orders_count || 0;
            document.getElementById('estatTempo').textContent = stats.average_time || '0h';
            document.getElementById('estatTaxa').textContent = stats.completion_rate || '0%';
        })
        .catch(error => {
            console.error('Erro:', error);
            // Usar valores padrão
            preencherEstatisticasPadrao();
        });
}

// Função para configurar eventos
function configurarEventos() {
    // Botão Salvar Perfil
    document.getElementById('btnSalvarPerfil').addEventListener('click', salvarPerfil);

    // Status da empresa
    document.getElementById('statusEmpresa').addEventListener('change', function() {
        const label = this.nextElementSibling;
        if (this.checked) {
            label.textContent = 'Ativo';
        } else {
            label.textContent = 'Inativo';
        }
    });

    // Máscara para CNPJ
    const cnpjInput = document.getElementById('cnpj');
    cnpjInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 14) {
            value = value.substring(0, 14);
        }

        // Aplicar máscara
        if (value.length > 12) {
            value = value.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/, '$1.$2.$3/$4-$5');
        } else if (value.length > 8) {
            value = value.replace(/^(\d{2})(\d{3})(\d{3})(\d+)$/, '$1.$2.$3/$4');
        } else if (value.length > 5) {
            value = value.replace(/^(\d{2})(\d{3})(\d+)$/, '$1.$2.$3');
        } else if (value.length > 2) {
            value = value.replace(/^(\d{2})(\d+)$/, '$1.$2');
        }

        e.target.value = value;
    });

    // Máscara para CEP
    const cepInput = document.getElementById('cep');
    cepInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 8) {
            value = value.substring(0, 8);
        }

        // Aplicar máscara
        if (value.length > 5) {
            value = value.replace(/^(\d{5})(\d+)$/, '$1-$2');
        }

        e.target.value = value;
    });

    // Máscara para telefone
    const telefoneInput = document.getElementById('telefoneContato');
    telefoneInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 11) {
            value = value.substring(0, 11);
        }

        // Aplicar máscara
        if (value.length > 10) {
            value = value.replace(/^(\d{2})(\d{5})(\d+)$/, '($1) $2-$3');
        } else if (value.length > 6) {
            value = value.replace(/^(\d{2})(\d{4})(\d+)$/, '($1) $2-$3');
        } else if (value.length > 2) {
            value = value.replace(/^(\d{2})(\d+)$/, '($1) $2');
        }

        e.target.value = value;
    });
}

// Função para salvar perfil
function salvarPerfil() {
    // Obter formulário
    const form = document.getElementById('formPerfilEmpresa');

    // Validar formulário
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // Obter dados do formulário
    const formData = new FormData(form);

    // Converter especialidades para array
    const especialidades = [];
    document.querySelectorAll('input[name="specialties"]:checked').forEach(checkbox => {
        especialidades.push(checkbox.value);
    });

    // Obter status
    const status = document.getElementById('statusEmpresa').checked ? 'active' : 'inactive';

    // Criar objeto de dados
    const data = {
        name: formData.get('name'),
        company_name: formData.get('company_name'),
        cnpj: formData.get('cnpj'),
        address: formData.get('address'),
        city: formData.get('city'),
        state: formData.get('state'),
        zip_code: formData.get('zip_code'),
        contact_name: formData.get('contact_name'),
        contact_email: formData.get('contact_email'),
        contact_phone: formData.get('contact_phone'),
        area_of_expertise: formData.get('area_of_expertise'),
        specialties: especialidades,
        status: status
    };

    // Enviar dados para a API
    fetch('/api/my-provider', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Erro ao atualizar perfil');
        }
        return response.json();
    })
    .then(data => {
        // Mostrar modal de sucesso
        const modal = new bootstrap.Modal(document.getElementById('modalSucesso'));
        modal.show();

        // Recarregar dados
        carregarDadosEmpresa();
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao atualizar perfil: ' + error.message);
    });
}
