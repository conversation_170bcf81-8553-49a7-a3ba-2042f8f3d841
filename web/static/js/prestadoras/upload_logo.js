// JavaScript para upload de logomarca

document.addEventListener('DOMContentLoaded', function() {
    // Configurar preview de imagem
    configurarPreviewImagem();
    
    // Configurar formulário de upload
    configurarFormularioUpload();
});

// Função para configurar preview de imagem
function configurarPreviewImagem() {
    // Preview de imagem ao selecionar arquivo
    const logoInput = document.getElementById('logoInput');
    if (logoInput) {
        logoInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Verificar tipo de arquivo
                if (!file.type.match('image.*')) {
                    alert('Por favor, selecione uma imagem válida (JPG, PNG, GIF).');
                    logoInput.value = '';
                    return;
                }
                
                // Verificar tamanho do arquivo (máximo 2MB)
                if (file.size > 2 * 1024 * 1024) {
                    alert('O tamanho máximo permitido é 2MB. Por favor, selecione uma imagem menor.');
                    logoInput.value = '';
                    return;
                }
                
                // Criar URL para preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Mostrar preview no modal
                    document.getElementById('modalLogoImage').src = e.target.result;
                    
                    // Abrir modal de preview
                    const modal = new bootstrap.Modal(document.getElementById('modalLogoPreview'));
                    modal.show();
                }
                reader.readAsDataURL(file);
            }
        });
    }
    
    // Botão de confirmação no modal
    const btnConfirmLogo = document.getElementById('btnConfirmLogo');
    if (btnConfirmLogo) {
        btnConfirmLogo.addEventListener('click', function() {
            // Atualizar preview principal
            document.getElementById('logoPreview').src = document.getElementById('modalLogoImage').src;
            
            // Fechar modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('modalLogoPreview'));
            modal.hide();
            
            // Enviar formulário
            document.getElementById('formUploadLogo').dispatchEvent(new Event('submit'));
        });
    }
}

// Função para configurar formulário de upload
function configurarFormularioUpload() {
    const formUploadLogo = document.getElementById('formUploadLogo');
    if (formUploadLogo) {
        formUploadLogo.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Obter arquivo
            const logoInput = document.getElementById('logoInput');
            const file = logoInput.files[0];
            
            if (!file) {
                alert('Por favor, selecione uma imagem.');
                return;
            }
            
            // Criar FormData
            const formData = new FormData();
            formData.append('logo', file);
            
            // Desabilitar botão de envio
            const btnUploadLogo = document.getElementById('btnUploadLogo');
            const btnText = btnUploadLogo.innerHTML;
            btnUploadLogo.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enviando...';
            btnUploadLogo.disabled = true;
            
            // Enviar para a API
            fetch('/api/my-provider/logo', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erro ao fazer upload da logomarca');
                }
                return response.json();
            })
            .then(data => {
                // Atualizar preview com URL do servidor
                document.getElementById('logoPreview').src = data.logo_url;
                
                // Limpar input
                logoInput.value = '';
                
                // Restaurar botão
                btnUploadLogo.innerHTML = btnText;
                btnUploadLogo.disabled = false;
                
                // Mostrar mensagem de sucesso
                alert('Logomarca atualizada com sucesso!');
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('Erro ao fazer upload da logomarca: ' + error.message);
                
                // Restaurar botão
                btnUploadLogo.innerHTML = btnText;
                btnUploadLogo.disabled = false;
            });
        });
    }
}
