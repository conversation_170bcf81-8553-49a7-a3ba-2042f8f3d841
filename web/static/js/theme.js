/**
 * <PERSON><PERSON><PERSON><PERSON>
 * Gerencia aspectos visuais e personalizáveis do tema da aplicação
 */
const themeModule = (function() {
  let app = null;
  
  // Configurações padrão
  const defaultSettings = {
    theme: 'light', // 'light' ou 'dark'
    sidebarCollapsed: false,
    fontSize: 'medium' // 'small', 'medium', 'large'
  };
  
  // Configurações atuais
  let settings = { ...defaultSettings };
  
  /**
   * Inicializa o módulo
   * @param {Object} appRef - Referência para o app principal
   */
  function init(appRef) {
    app = appRef;
    console.log('Módulo de tema inicializado');
    
    // Carregar configurações do localStorage
    loadSettings();
    
    // Aplicar configurações
    applySettings();
    
    // Configurar controles
    setupControls();
  }
  
  /**
   * Carrega configurações salvas do localStorage
   */
  function loadSettings() {
    try {
      const savedSettings = localStorage.getItem('shell_theme_settings');
      if (savedSettings) {
        settings = { ...defaultSettings, ...JSON.parse(savedSettings) };
      }
    } catch (error) {
      console.error('Erro ao carregar configurações de tema:', error);
      settings = { ...defaultSettings };
    }
  }
  
  /**
   * Salva configurações no localStorage
   */
  function saveSettings() {
    try {
      localStorage.setItem('shell_theme_settings', JSON.stringify(settings));
    } catch (error) {
      console.error('Erro ao salvar configurações de tema:', error);
    }
  }
  
  /**
   * Aplica as configurações do tema
   */
  function applySettings() {
    // Aplicar tema claro/escuro
    setTheme(settings.theme);
    
    // Aplicar tamanho da fonte
    setFontSize(settings.fontSize);
    
    // Aplicar estado da sidebar
    if (settings.sidebarCollapsed) {
      toggleSidebar();
    }
  }
  
  /**
   * Configura controles de tema na interface
   */
  function setupControls() {
    // Toggle da sidebar
    const sidebarToggle = document.getElementById('sidebar-toggle');
    if (sidebarToggle) {
      sidebarToggle.addEventListener('click', toggleSidebar);
    }
    
    // Botão de alternar tema
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
      themeToggle.addEventListener('click', toggleTheme);
    }
    
    // Botões de tamanho de fonte
    const increaseFontBtn = document.getElementById('increase-font');
    const decreaseFontBtn = document.getElementById('decrease-font');
    
    if (increaseFontBtn) {
      increaseFontBtn.addEventListener('click', () => changeFontSize(1));
    }
    
    if (decreaseFontBtn) {
      decreaseFontBtn.addEventListener('click', () => changeFontSize(-1));
    }
  }
  
  /**
   * Alterna a visibilidade da sidebar
   */
  function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const content = document.querySelector('.content-with-sidebar');
    
    if (sidebar && content) {
      sidebar.classList.toggle('show');
      settings.sidebarCollapsed = !sidebar.classList.contains('show');
      saveSettings();
    }
  }
  
  /**
   * Alterna entre tema claro e escuro
   */
  function toggleTheme() {
    const newTheme = settings.theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
  }
  
  /**
   * Altera o tamanho da fonte
   * @param {number} direction - Direção (1 para aumentar, -1 para diminuir)
   */
  function changeFontSize(direction) {
    const sizes = ['small', 'medium', 'large'];
    let currentIndex = sizes.indexOf(settings.fontSize);
    
    if (currentIndex === -1) currentIndex = 1; // Padrão: medium
    
    const newIndex = Math.max(0, Math.min(sizes.length - 1, currentIndex + direction));
    const newSize = sizes[newIndex];
    
    setFontSize(newSize);
  }
  
  /**
   * Define o tema
   * @param {string} theme - Tema ('light' ou 'dark')
   */
  function setTheme(theme) {
    const body = document.body;
    
    if (theme === 'dark') {
      body.classList.add('dark-theme');
    } else {
      body.classList.remove('dark-theme');
    }
    
    settings.theme = theme;
    saveSettings();
    
    // Atualizar ícone ou indicador visual se existir
    const themeIcon = document.getElementById('theme-icon');
    if (themeIcon) {
      if (theme === 'dark') {
        themeIcon.classList.remove('fa-sun');
        themeIcon.classList.add('fa-moon');
      } else {
        themeIcon.classList.remove('fa-moon');
        themeIcon.classList.add('fa-sun');
      }
    }
  }
  
  /**
   * Define o tamanho da fonte
   * @param {string} size - Tamanho ('small', 'medium', 'large')
   */
  function setFontSize(size) {
    const body = document.body;
    
    // Remover classes existentes
    body.classList.remove('font-small', 'font-medium', 'font-large');
    
    // Adicionar nova classe
    body.classList.add(`font-${size}`);
    
    settings.fontSize = size;
    saveSettings();
  }
  
  // Retornar API pública
  return {
    init: init,
    toggleTheme: toggleTheme,
    toggleSidebar: toggleSidebar,
    changeFontSize: changeFontSize
  };
})();