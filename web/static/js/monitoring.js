/**
 * Sistema de monitoramento e telemetria
 */

class Monitoring {
    constructor() {
        this.metrics = {
            pageLoads: {},
            errors: [],
            performance: {}
        };
    }

    logPageView(pageName) {
        const loadTime = window.performance.timing.loadEventEnd - window.performance.timing.navigationStart;
        
        this.metrics.pageLoads[pageName] = {
            timestamp: new Date().toISOString(),
            loadTime: loadTime,
            url: window.location.pathname
        };

        // Enviar para o backend
        this.sendMetrics();
    }

    logError(error, context) {
        this.metrics.errors.push({
            timestamp: new Date().toISOString(),
            error: error.message,
            stack: error.stack,
            context: context,
            url: window.location.pathname
        });

        // Enviar erro imediatamente
        this.sendMetrics();
    }

    measurePerformance() {
        if (window.performance && window.performance.memory) {
            this.metrics.performance = {
                timestamp: new Date().toISOString(),
                memory: window.performance.memory.usedJSHeapSize,
                totalMemory: window.performance.memory.totalJSHeapSize,
                url: window.location.pathname
            };
        }
    }

    sendMetrics() {
        fetch('/api/monitoring/metrics', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(this.metrics)
        }).catch(console.error);
    }
}

// Instância global
const monitoring = new Monitoring();

// Interceptar erros não tratados
window.onerror = function(msg, url, line, col, error) {
    monitoring.logError(error, {line, col});
};

// Medir performance periodicamente
setInterval(() => monitoring.measurePerformance(), 60000);

// Exportar para uso global
window.monitoring = monitoring;