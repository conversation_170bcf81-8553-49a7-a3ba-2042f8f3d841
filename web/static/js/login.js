document.addEventListener("DOMContentLoaded", function () {
    const loginForm = document.getElementById("login-form");
    const errorMessage = document.getElementById("error-message");
    const errorText = document.getElementById("error-text");

    loginForm.addEventListener("submit", function (e) {
        e.preventDefault();

        const email = document.getElementById("email").value;
        const password = document.getElementById("password").value;

        // Fazer a chamada AJAX para autenticar
        const loginData = {
            email: email,
            password: password
        };

        fetch("/api/auth/login", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Accept": "application/json"
            },
            body: JSON.stringify(loginData)
        })
        .then((response) => {
            // Primeiro verificamos se a resposta está OK
            if (!response.ok) {
                // Se não estiver OK, tentamos analisar o JSON de erro
                return response.text().then(text => {
                    try {
                        // Tenta analisar como JSON
                        const data = JSON.parse(text);
                        throw new Error(data.error || "Erro de autenticação");
                    } catch (e) {
                        // Se não for JSON válido, usa o texto bruto ou mensagem padrão
                        throw new Error(text || "Erro de autenticação");
                    }
                });
            }
            // Se estiver OK, retorna o JSON
            return response.json();
        })
        .then((data) => {
            // Login bem-sucedido, aplicar efeito de rotação e redirecionar
            const loginCard =
                document.querySelector(".login-card");
            loginCard.classList.add("rotate-card");

            // Definir redirecionamento com base no perfil do usuário
            let redirectUrl = data.redirect || '/dashboard';
            console.log("Resposta do login:", data);
            console.log("Redirecionando para:", redirectUrl);

            // Aguardar a animação terminar antes de redirecionar
            setTimeout(() => {
                window.location.href = redirectUrl;
            }, 1000);
        })
        .catch((error) => {
            // Exibir mensagem de erro
            errorText.textContent = error.message;
            errorMessage.classList.add("show");

            // Animar o efeito de shake no formulário
            loginForm.classList.add("shake");
            setTimeout(() => {
                loginForm.classList.remove("shake");
            }, 500);
        });
    });

    // Ocultar a mensagem de erro quando o usuário começa a digitar novamente
    document.querySelectorAll("input").forEach((input) => {
        input.addEventListener("input", () => {
            errorMessage.classList.remove("show");
        });
    });

    // Configurar o botão de login automático
    const autoLoginBtn = document.getElementById('auto-login-btn');
    if (autoLoginBtn) {
        autoLoginBtn.addEventListener('click', function() {
            // Preencher com as credenciais de administrador
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'senha123';

            // Submeter o formulário automaticamente
            loginForm.dispatchEvent(new Event('submit'));
        });
    }
});
