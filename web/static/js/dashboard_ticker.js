/**
 * Gerenciador do letreiro animado do dashboard
 * Atualiza frases a cada 30 minutos
 */

// Array de frases motivacionais e curiosidades
const phrases = [
    "Abasteça seu dia com energia positiva! 🌟",
    "Qualidade Shell: O combustível da sua jornada! ⛽",
    "Você sabia? O primeiro posto de gasolina do mundo foi inaugurado em 1905! 🚗",
    "Segurança em primeiro lugar: Mantenha o motor desligado durante o abastecimento! 🔒",
    "Curiosidade: A Shell foi fundada em 1907 como Royal Dutch Shell! 🐚",
    "Dica: Manter a calibragem correta dos pneus economiza combustível! 💡",
    "Juntos somos mais fortes! Equipe Rede Tradição Shell 💪",
    "Você sabia? O Brasil tem mais de 41 mil postos de combustível! 🇧🇷",
    "Qualidade e confiança: Nossa marca registrada desde sempre! ✨",
    "Dica: Faça a manutenção preventiva regularmente! 🔧",
    "Atendimento de excelência: Nossa prioridade é você! 🤝",
    "Curiosidade: A gasolina foi inventada por acidente em 1859! 📚",
    "Inovação e tradição: O combustível do nosso sucesso! 🚀",
    "Você sabia? O primeiro carro flex foi lançado no Brasil em 2003! 🚙",
    "Compromisso com o meio ambiente: Pensando no futuro! 🌱"
];

// Função para embaralhar o array de frases
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

// Função para atualizar o conteúdo do letreiro
function updateTickerContent() {
    const tickerContent = document.getElementById('tickerContent');
    if (!tickerContent) return;

    // Embaralha as frases e cria o conteúdo
    const shuffledPhrases = shuffleArray([...phrases]);
    const content = shuffledPhrases.map(phrase => `<span>${phrase}</span>`).join('');
    
    // Atualiza o conteúdo
    tickerContent.innerHTML = content;
}

// Inicializa o letreiro
document.addEventListener('DOMContentLoaded', () => {
    updateTickerContent();
    
    // Atualiza as frases a cada 30 minutos
    setInterval(updateTickerContent, 30 * 60 * 1000);
}); 