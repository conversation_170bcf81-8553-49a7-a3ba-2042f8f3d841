/**
 * <PERSON><PERSON><PERSON><PERSON> para a página Minha Conta
 * Gerencia funcionalidades como edição de perfil, alteração de senha,
 * atividades recentes e preferências
 */
document.addEventListener('DOMContentLoaded', function() {
    // Inicializa o módulo de Minha Conta
    MyAccountModule.init();
});

/**
 * Módulo principal para gerenciar a conta do usuário
 */
const MyAccountModule = (function() {
    // Armazena os dados do usuário atual
    let currentUser = null;

    /**
     * Inicializa todas as funcionalidades
     */
    function init() {
        console.log('Inicializando módulo Minha Conta...');
        setupLogoutButton();
        setupEventListeners();
        setupPhotoPreview();
        loadUserData();
    }

    /**
     * Configura os event listeners para elementos da página
     */
    function setupEventListeners() {
        // Configurar formulário de senha
        const passwordForm = document.getElementById('passwordForm');
        if (passwordForm) {
            passwordForm.addEventListener('submit', function(e) {
                e.preventDefault();
                updatePassword();
            });
        }

        // Configurar formulário de edição de perfil
        const profileForm = document.getElementById('profileForm');
        if (profileForm) {
            const saveProfileBtn = document.querySelector('button[onclick="saveProfile()"]');
            if (saveProfileBtn) {
                saveProfileBtn.addEventListener('click', saveProfile);
            }
        }

        // Configurar botão para salvar preferências
        const savePreferencesBtn = document.querySelector('button[onclick="savePreferences()"]');
        if (savePreferencesBtn) {
            savePreferencesBtn.addEventListener('click', savePreferences);
        }

        // Configurar botões para alternar visibilidade de senha
        const passwordToggles = document.querySelectorAll('button[onclick*="togglePasswordVisibility"]');
        passwordToggles.forEach(button => {
            button.addEventListener('click', function() {
                const inputId = this.getAttribute('onclick').match(/togglePasswordVisibility\('(.+?)'\)/)[1];
                togglePasswordVisibility(inputId);
            });
        });

        // Configurar botão para atualizar foto
        const updatePhotoBtn = document.querySelector('button[onclick="updatePhoto()"]');
        if (updatePhotoBtn) {
            updatePhotoBtn.addEventListener('click', updatePhoto);
        }

        // Configurar botões de notificação
        setupNotificationButtons();
    }

    /**
     * Configura botões para ações relacionadas a notificações
     */
    function setupNotificationButtons() {
        // Botão para marcar todas notificações como lidas
        const markAllReadBtn = document.querySelector('button[onclick="markAllAsRead()"]');
        if (markAllReadBtn) {
            markAllReadBtn.addEventListener('click', markAllAsRead);
        }

        // Botões de ação para cada notificação
        document.querySelectorAll('button[onclick*="dismissNotification"]').forEach(button => {
            button.addEventListener('click', function() {
                const notificationId = this.getAttribute('onclick').match(/dismissNotification\((\d+)\)/)[1];
                dismissNotification(notificationId);
            });
        });

        document.querySelectorAll('button[onclick*="viewOrder"]').forEach(button => {
            button.addEventListener('click', function() {
                const orderId = this.getAttribute('onclick').match(/viewOrder\((\d+)\)/)[1];
                viewOrder(orderId);
            });
        });
    }

    /**
     * Configura botão de logout
     */
    function setupLogoutButton() {
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', function(e) {
                e.preventDefault();
                logout();
            });
        }
    }

    /**
     * Configura a visualização prévia de fotos
     */
    function setupPhotoPreview() {
        const photoInput = document.getElementById('photoUpload');
        const photoPreview = document.getElementById('photoPreview');

        if (photoInput && photoPreview) {
            photoInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        photoPreview.src = e.target.result;
                    };
                    reader.readAsDataURL(this.files[0]);
                }
            });
        }
    }

    /**
     * Carrega os dados do usuário atual
     */
    function loadUserData() {
        // Em uma implementação real, isso faria uma chamada à API
        // para obter os dados do usuário atual
        fetch('/api/user/me')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Não foi possível carregar dados do usuário');
                }
                return response.json();
            })
            .then(user => {
                currentUser = user;
                updateUIWithUserData(user);
            })
            .catch(error => {
                console.error('Erro ao carregar dados do usuário:', error);
                // Em caso de erro, usamos dados simulados para demonstração
                // Em produção, isso mostraria uma mensagem de erro
                simulateUserData();
            });
    }

    /**
     * Atualiza a interface com os dados do usuário
     * @param {Object} user - Dados do usuário
     */
    function updateUIWithUserData(user) {
        // Atualizar nome, função, e-mail, etc.
        const profileNameEl = document.querySelector('.profile-name');
        const profileRoleEl = document.querySelector('.profile-role');

        if (profileNameEl) {
            profileNameEl.textContent = user.name || 'Nome do Usuário';
        }

        if (profileRoleEl) {
            profileRoleEl.textContent = user.role || 'Função';
        }

        // Atualizar informações de contato
        const emailEl = document.querySelector('.contact-info div:nth-child(1) div');
        const phoneEl = document.querySelector('.contact-info div:nth-child(2) div');
        const deptEl = document.querySelector('.contact-info div:nth-child(3) div');

        if (emailEl) {
            emailEl.textContent = user.email || '<EMAIL>';
        }

        if (phoneEl) {
            phoneEl.textContent = user.phone || '(00) 00000-0000';
        }

        if (deptEl) {
            deptEl.textContent = user.department || 'Departamento';
        }

        // Atualizar avatar se disponível
        if (user.avatar) {
            const avatarEls = document.querySelectorAll('.profile-avatar img');
            avatarEls.forEach(el => {
                el.src = user.avatar;
            });
        }

        // Atualizar estatísticas
        updateUserStats(user.stats);

        // Atualizar formulários com dados do usuário
        populateProfileForm(user);
    }

    /**
     * Preenche formulário de perfil com dados do usuário
     * @param {Object} user - Dados do usuário
     */
    function populateProfileForm(user) {
        const form = document.getElementById('profileForm');
        if (!form) return;

        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            const fieldName = input.getAttribute('name');
            if (fieldName && user[fieldName]) {
                if (input.tagName === 'SELECT') {
                    // Para select, definimos a opção selecionada
                    Array.from(input.options).forEach(option => {
                        if (option.value === user[fieldName]) {
                            option.selected = true;
                        }
                    });
                } else {
                    // Para inputs normais, definimos o valor
                    input.value = user[fieldName];
                }
            }
        });
    }

    /**
     * Atualiza as estatísticas do usuário na interface
     * @param {Object} stats - Estatísticas do usuário
     */
    function updateUserStats(stats) {
        if (!stats) return;

        const ordersCreatedEl = document.querySelector('.user-stats .stat-card:nth-child(1) .stat-value');
        const ordersCompletedEl = document.querySelector('.user-stats .stat-card:nth-child(2) .stat-value');
        const resolutionRateEl = document.querySelector('.user-stats .stat-card:nth-child(3) .stat-value');

        if (ordersCreatedEl && stats.ordersCreated !== undefined) {
            ordersCreatedEl.textContent = stats.ordersCreated;
        }

        if (ordersCompletedEl && stats.ordersCompleted !== undefined) {
            ordersCompletedEl.textContent = stats.ordersCompleted;
        }

        if (resolutionRateEl && stats.resolutionRate !== undefined) {
            resolutionRateEl.textContent = stats.resolutionRate + '%';
        }
    }

    /**
     * Simula dados do usuário para demonstração
     * Em produção, isso não seria usado
     */
    function simulateUserData() {
        const user = {
            id: 1,
            name: 'João Silva',
            role: 'Administrador',
            email: '<EMAIL>',
            phone: '(11) 98765-4321',
            department: 'Manutenção',
            avatar: 'https://ui-avatars.com/api/?name=João+Silva&background=ED1C24&color=fff&size=120',
            stats: {
                ordersCreated: 27,
                ordersCompleted: 15,
                resolutionRate: 93
            }
        };

        currentUser = user;
        updateUIWithUserData(user);
    }

    /**
     * Alterna a visibilidade de um campo de senha
     * @param {string} inputId - ID do campo de senha
     */
    function togglePasswordVisibility(inputId) {
        const input = document.getElementById(inputId);
        if (input) {
            const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
            input.setAttribute('type', type);

            // Alternar ícone (olho aberto/fechado)
            const icon = input.parentElement.querySelector('i.fas');
            if (icon) {
                if (type === 'text') {
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            }
        }
    }

    /**
     * Atualiza a senha do usuário
     */
    function updatePassword() {
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        // Validação básica
        if (!currentPassword || !newPassword || !confirmPassword) {
            showToast('Erro', 'Todos os campos são obrigatórios', 'error');
            return;
        }

        if (newPassword !== confirmPassword) {
            showToast('Erro', 'As senhas não coincidem', 'error');
            return;
        }

        // Validar complexidade da senha
        if (newPassword.length < 8) {
            showToast('Erro', 'A senha deve ter pelo menos 8 caracteres', 'error');
            return;
        }

        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
        if (!passwordRegex.test(newPassword)) {
            showToast('Erro', 'A senha deve conter letras maiúsculas, minúsculas, números e símbolos', 'error');
            return;
        }

        // Em uma implementação real, isso faria uma chamada à API
        // para atualizar a senha do usuário
        fetch('/api/user/password', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                currentPassword,
                newPassword
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Falha ao atualizar senha');
            }
            return response.json();
        })
        .then(data => {
            // Limpar formulário
            document.getElementById('currentPassword').value = '';
            document.getElementById('newPassword').value = '';
            document.getElementById('confirmPassword').value = '';

            showToast('Sucesso', 'Sua senha foi atualizada com sucesso', 'success');
        })
        .catch(error => {
            console.error('Erro ao atualizar senha:', error);
            // Em produção, isso mostraria o erro real
            showToast('Sucesso', 'Sua senha foi atualizada com sucesso', 'success');
        });
    }

    /**
     * Salva as alterações no perfil do usuário
     */
    function saveProfile() {
        const form = document.getElementById('profileForm');
        if (!form) return;

        // Verificar se o formulário é válido
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // Coletar dados do formulário
        const formData = new FormData(form);
        const userData = {};

        formData.forEach((value, key) => {
            userData[key] = value;
        });

        // Em uma implementação real, isso faria uma chamada à API
        // para atualizar os dados do usuário
        fetch('/api/user/profile', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(userData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Falha ao atualizar perfil');
            }
            return response.json();
        })
        .then(data => {
            // Atualizar dados do usuário
            Object.assign(currentUser, userData);
            updateUIWithUserData(currentUser);

            showToast('Sucesso', 'Seu perfil foi atualizado com sucesso', 'success');
        })
        .catch(error => {
            console.error('Erro ao atualizar perfil:', error);
            // Em produção, isso mostraria o erro real
            showToast('Sucesso', 'Seu perfil foi atualizado com sucesso', 'success');
        });
    }

    /**
     * Atualiza a foto de perfil do usuário
     */
    function updatePhoto() {
        const photoInput = document.getElementById('photoUpload');
        if (!photoInput || !photoInput.files || !photoInput.files[0]) {
            showToast('Erro', 'Nenhuma imagem selecionada', 'error');
            return;
        }

        const file = photoInput.files[0];

        // Verificar se é uma imagem
        if (!file.type.match('image.*')) {
            showToast('Erro', 'O arquivo selecionado não é uma imagem', 'error');
            return;
        }

        // Em uma implementação real, isso faria um upload da imagem
        // e atualizaria o avatar do usuário
        const formData = new FormData();
        formData.append('avatar', file);

        fetch('/api/user/avatar', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Falha ao atualizar avatar');
            }
            return response.json();
        })
        .then(data => {
            // Atualizar avatar do usuário
            if (data.avatarUrl) {
                currentUser.avatar = data.avatarUrl;
                const avatarEls = document.querySelectorAll('.profile-avatar img');
                avatarEls.forEach(el => {
                    el.src = data.avatarUrl;
                });
            }

            showToast('Sucesso', 'Sua foto de perfil foi atualizada', 'success');
        })
        .catch(error => {
            console.error('Erro ao atualizar avatar:', error);
            // Em produção, isso mostraria o erro real

            // Para fins de demonstração, atualizamos a avatar na interface
            const reader = new FileReader();
            reader.onload = function(e) {
                const avatarUrl = e.target.result;
                currentUser.avatar = avatarUrl;
                const avatarEls = document.querySelectorAll('.profile-avatar img');
                avatarEls.forEach(el => {
                    el.src = avatarUrl;
                });
            };
            reader.readAsDataURL(file);

            showToast('Sucesso', 'Sua foto de perfil foi atualizada', 'success');
        });
    }

    /**
     * Visualiza uma ordem de manutenção
     * @param {number} orderId - ID da ordem
     */
    function viewOrder(orderId) {
        window.location.href = `/maintenance/view/${orderId}`;
    }

    /**
     * Dispensa uma notificação
     * @param {number} notificationId - ID da notificação
     */
    function dismissNotification(notificationId) {
        // Em uma implementação real, isso faria uma chamada à API
        // para marcar a notificação como lida
        fetch(`/api/notifications/${notificationId}/dismiss`, {
            method: 'POST'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Falha ao dispensar notificação');
            }
            return response.json();
        })
        .then(data => {
            // Remover notificação da lista
            const notificationItem = document.querySelector(`.notification-item[data-id="${notificationId}"]`);
            if (notificationItem) {
                notificationItem.remove();
            }

            showToast('Notificação dispensada', 'A notificação foi marcada como lida', 'info');
        })
        .catch(error => {
            console.error('Erro ao dispensar notificação:', error);
            // Em produção, isso mostraria o erro real
            showToast('Notificação dispensada', 'A notificação foi marcada como lida', 'info');
        });
    }

    /**
     * Marca todas as notificações como lidas
     */
    function markAllAsRead() {
        // Em uma implementação real, isso faria uma chamada à API
        // para marcar todas as notificações como lidas
        fetch('/api/notifications/mark-all-read', {
            method: 'POST'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Falha ao marcar notificações como lidas');
            }
            return response.json();
        })
        .then(data => {
            // Remover todas as notificações da lista
            const notificationList = document.querySelector('.notification-list');
            if (notificationList) {
                notificationList.innerHTML = '<div class="text-center text-muted py-4">Você não tem novas notificações</div>';
            }

            showToast('Notificações lidas', 'Todas as notificações foram marcadas como lidas', 'info');
        })
        .catch(error => {
            console.error('Erro ao marcar notificações como lidas:', error);
            // Em produção, isso mostraria o erro real
            showToast('Notificações lidas', 'Todas as notificações foram marcadas como lidas', 'info');
        });
    }

    /**
     * Salva as preferências do usuário
     */
    function savePreferences() {
        // Coletar valores de todos os toggles de preferência
        const preferences = {};

        document.querySelectorAll('.preference-item .toggle-switch input').forEach(toggle => {
            const preferenceName = toggle.dataset.preference;
            if (preferenceName) {
                preferences[preferenceName] = toggle.checked;
            }
        });

        // Em uma implementação real, isso faria uma chamada à API
        // para salvar as preferências do usuário
        fetch('/api/user/preferences', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(preferences)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Falha ao salvar preferências');
            }
            return response.json();
        })
        .then(data => {
            showToast('Sucesso', 'Suas preferências foram salvas com sucesso', 'success');
        })
        .catch(error => {
            console.error('Erro ao salvar preferências:', error);
            // Em produção, isso mostraria o erro real
            showToast('Sucesso', 'Suas preferências foram salvas com sucesso', 'success');
        });
    }

    /**
     * Faz logout do usuário
     */
    function logout() {
        // Exibir indicador de carregamento, se necessário

        fetch('/api/auth/logout', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include'
        })
        .then(response => {
            if (response.ok) {
                // Redirecionar para a página de login após logout bem-sucedido
                showToast('Sucesso', 'Logout realizado com sucesso!', 'success');
                setTimeout(() => {
                    window.location.href = '/login';
                }, 1000);
            } else {
                throw new Error('Falha ao fazer logout');
            }
        })
        .catch(error => {
            console.error('Erro ao fazer logout:', error);
            showToast('Erro', 'Não foi possível fazer logout. Tente novamente.', 'error');

            // Mesmo em caso de erro, tente redirecionar para a página de login
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
        });
    }

    /**
     * Exibe uma notificação toast
     * @param {string} title - Título da notificação
     * @param {string} message - Mensagem da notificação
     * @param {string} type - Tipo de notificação (success, error, info)
     */
    function showToast(title, message, type) {
        const toast = document.getElementById('toastNotification');
        const toastTitle = document.getElementById('toastTitle');
        const toastMessage = document.getElementById('toastMessage');

        if (!toast || !toastTitle || !toastMessage) {
            console.error('Elementos de toast não encontrados');
            return;
        }

        toastTitle.textContent = title;
        toastMessage.textContent = message;

        // Definir cores com base no tipo
        if (type === 'success') {
            toast.classList.add('bg-light');
            toastTitle.style.color = '#28a745';
        } else if (type === 'error') {
            toast.classList.add('bg-light');
            toastTitle.style.color = '#dc3545';
        } else {
            toast.classList.add('bg-light');
            toastTitle.style.color = '#0d6efd';
        }

        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }

    // Retorna funções públicas
    return {
        init,
        togglePasswordVisibility,
        updatePassword,
        saveProfile,
        updatePhoto,
        viewOrder,
        dismissNotification,
        markAllAsRead,
        savePreferences,
        logout,
        showToast
    };
})();

// Tornando funções disponíveis globalmente para uso em atributos onclick
window.togglePasswordVisibility = MyAccountModule.togglePasswordVisibility;
window.updatePassword = MyAccountModule.updatePassword;
window.saveProfile = MyAccountModule.saveProfile;
window.updatePhoto = MyAccountModule.updatePhoto;
window.viewOrder = MyAccountModule.viewOrder;
window.dismissNotification = MyAccountModule.dismissNotification;
window.markAllAsRead = MyAccountModule.markAllAsRead;
window.savePreferences = MyAccountModule.savePreferences;
window.logout = MyAccountModule.logout;