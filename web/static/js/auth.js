
/**
 * <PERSON>ódulo de autenticação para o frontend
 */

// Gestão de tokens CSRF
let csrfToken = '';

// Inicializa a autenticação
document.addEventListener('DOMContentLoaded', function() {
    // Verifica o token CSRF no cabeçalho
    csrfToken = document.querySelector('meta[name="csrf-token"]')?.content || '';

    // Configura interceptor para requisições AJAX
    setupAjaxInterceptors();

    // Configura formulários de autenticação
    setupAuthForms();

    // Verificar estado de autenticação
    checkAuthState();
});

// Configura interceptores para requisições AJAX
function setupAjaxInterceptors() {
    // Adiciona o token CSRF a todas as requisições
    $(document).ajaxSend(function(e, xhr, options) {
        // Adiciona o token CSRF ao cabeçalho
        xhr.setRequestHeader('X-CSRF-Token', csrfToken);

        // Adiciona o token de autenticação ao cabeçalho se disponível
        const token = localStorage.getItem('auth_token');
        if (token) {
            xhr.setRequestHeader('Authorization', `Bearer ${token}`);
        }
    });

    // Intercepta respostas para tratar erros de autenticação
    $(document).ajaxComplete(function(e, xhr, options) {
        // Verifica se há um novo token CSRF no cabeçalho
        const newCsrfToken = xhr.getResponseHeader('X-CSRF-Token');
        if (newCsrfToken) {
            csrfToken = newCsrfToken;
            updateCsrfTokenMeta(newCsrfToken);
        }

        // Verifica se há um novo token de autenticação
        const newAuthToken = xhr.getResponseHeader('X-Renew-Token');
        if (newAuthToken) {
            localStorage.setItem('auth_token', newAuthToken);
        }

        // Redireciona para login se não autenticado
        if (xhr.status === 401) {
            handleUnauthenticated();
        }
    });
}

// Configura formulários de autenticação
function setupAuthForms() {
    // Formulário de login
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            $.ajax({
                url: '/api/login',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ email, password }),
                success: function(response) {
                    // Armazena o token
                    localStorage.setItem('auth_token', response.token);
                    localStorage.setItem('user_data', JSON.stringify(response.user));

                    // Redireciona para o dashboard
                    const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || '/dashboard';
                    window.location.href = redirectUrl;
                },
                error: function(xhr) {
                    let errorMessage = 'Erro ao fazer login';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        errorMessage = response.error || errorMessage;
                    } catch (e) {
                        console.error('Erro ao processar resposta:', e);
                    }

                    showAuthError(errorMessage);
                }
            });
        });
    }

    // Formulário de registro
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            // Validação básica
            if (password !== confirmPassword) {
                showAuthError('As senhas não coincidem');
                return;
            }

            $.ajax({
                url: '/api/register',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ name, email, password }),
                success: function(response) {
                    // Mostra mensagem de sucesso e redireciona para login
                    showAuthSuccess('Conta criada com sucesso! Faça login para continuar.');
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 2000);
                },
                error: function(xhr) {
                    let errorMessage = 'Erro ao criar conta';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        errorMessage = response.error || errorMessage;
                    } catch (e) {
                        console.error('Erro ao processar resposta:', e);
                    }

                    showAuthError(errorMessage);
                }
            });
        });
    }

    // Formulário de recuperação de senha
    const resetForm = document.getElementById('resetRequestForm');
    if (resetForm) {
        resetForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const email = document.getElementById('email').value;

            $.ajax({
                url: '/api/password/reset/request',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ email }),
                success: function(response) {
                    showAuthSuccess('Se o email estiver cadastrado, você receberá instruções para redefinir sua senha.');
                },
                error: function(xhr) {
                    showAuthError('Erro ao processar solicitação');
                }
            });
        });
    }

    // Botão de logout
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // Verificar se a função global de logout existe
            if (typeof logout === 'function') {
                // Limpar dados de autenticação local
                localStorage.removeItem('auth_token');
                localStorage.removeItem('user_data');

                // Usar a função global de logout
                logout();
            } else {
                // Fallback: fazer logout através da API diretamente
                $.ajax({
                    url: '/api/auth/logout', // Usando a rota padronizada
                    type: 'POST',
                    success: function() {
                        // Limpa dados de autenticação
                        localStorage.removeItem('auth_token');
                        localStorage.removeItem('user_data');

                        // Redireciona para login
                        window.location.href = '/login';
                    },
                    error: function() {
                        // Mesmo com erro, realiza logout local
                        localStorage.removeItem('auth_token');
                        localStorage.removeItem('user_data');
                        window.location.href = '/login';
                    }
                });
            }
        });
    }
}

// Verifica estado de autenticação
function checkAuthState() {
    // Verifica se está em página protegida
    const requiresAuth = document.body.hasAttribute('data-requires-auth');
    if (!requiresAuth) {
        return;
    }

    // Verifica se tem token
    const token = localStorage.getItem('auth_token');
    if (!token) {
        handleUnauthenticated();
        return;
    }

    // Verifica se o token é válido
    $.ajax({
        url: '/api/check-auth',
        type: 'GET',
        success: function(response) {
            // Token válido, atualiza interface
            updateAuthInterface();
        },
        error: function() {
            // Token inválido
            handleUnauthenticated();
        }
    });
}

// Atualiza interface com dados do usuário
function updateAuthInterface() {
    const userData = JSON.parse(localStorage.getItem('user_data') || '{}');

    // Atualiza nome do usuário
    const userNameElement = document.getElementById('userName');
    if (userNameElement && userData.name) {
        userNameElement.textContent = userData.name;
    }

    // Atualiza email do usuário
    const userEmailElement = document.getElementById('userEmail');
    if (userEmailElement && userData.email) {
        userEmailElement.textContent = userData.email;
    }

    // Exibe/oculta elementos baseado na role
    if (userData.role) {
        document.querySelectorAll('[data-role-access]').forEach(element => {
            const allowedRoles = element.getAttribute('data-role-access').split(',');
            if (allowedRoles.includes(userData.role)) {
                element.style.display = '';
            } else {
                element.style.display = 'none';
            }
        });
    }
}

// Trata caso de não autenticado
function handleUnauthenticated() {
    // Limpa dados de autenticação
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');

    // Se estiver em página protegida, redireciona para login
    const requiresAuth = document.body.hasAttribute('data-requires-auth');
    if (requiresAuth) {
        window.location.href = `/login?redirect=${encodeURIComponent(window.location.pathname)}`;
    }
}

// Exibe mensagem de erro
function showAuthError(message) {
    const errorElement = document.getElementById('authError');
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.style.display = 'block';

        // Esconde após 5 segundos
        setTimeout(() => {
            errorElement.style.display = 'none';
        }, 5000);
    }
}

// Exibe mensagem de sucesso
function showAuthSuccess(message) {
    const successElement = document.getElementById('authSuccess');
    if (successElement) {
        successElement.textContent = message;
        successElement.style.display = 'block';

        // Esconde após 5 segundos
        setTimeout(() => {
            successElement.style.display = 'none';
        }, 5000);
    }
}

// Atualiza meta tag do CSRF
function updateCsrfTokenMeta(token) {
    let metaTag = document.querySelector('meta[name="csrf-token"]');
    if (!metaTag) {
        metaTag = document.createElement('meta');
        metaTag.name = 'csrf-token';
        document.head.appendChild(metaTag);
    }
    metaTag.content = token;
}
