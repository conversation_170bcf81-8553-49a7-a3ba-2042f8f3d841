/**
 * Gerenciamento de Detalhes da Ordem - Ordem Técnico
 * Sistema Tradição - Módulo de Técnicos
 */

// Classe para gerenciar os detalhes da ordem
class OrdemTecnicoDetails {
    constructor() {
        this.currentOrderId = null;
    }

    // Define o ID da ordem atual
    setCurrentOrderId(orderId) {
        this.currentOrderId = orderId;
        console.log('ID da ordem atual definido:', orderId);

        // Atualiza o ID da ordem atual no gerenciador de modais
        if (window.OrdemTecnicoModals) {
            window.OrdemTecnicoModals.setCurrentOrderId(orderId);
        }
    }

    // Carrega os detalhes da ordem
    loadOrderDetails(orderId) {
        this.setCurrentOrderId(orderId);
        console.log(`Carregando detalhes da ordem ID: ${orderId}`);

        // Busca os dados da ordem via API
        if (window.OrdemTecnicoAPI) {
            window.OrdemTecnicoAPI.getOrdem(orderId)
                .then(response => {
                    if (response.success) {
                        console.log('Dados recebidos da API:', response.data);
                        // Preencher os dados com os valores reais da API
                        this.updateOrderDetails(response.data);
                    } else {
                        console.error('Erro na resposta da API:', response.message);
                        this.showNotification('Erro ao carregar dados da ordem: ' + response.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Erro ao chamar API:', error);
                    this.showNotification('Erro ao carregar dados da ordem: ' + error.message, 'error');
                });
        } else {
            console.error('API não disponível');
            this.showNotification('API não disponível', 'error');
        }
    }

    // Carrega as ordens do técnico atual
    loadTechnicianOrders() {
        console.log('Carregando ordens do técnico...');

        if (window.OrdemTecnicoAPI) {
            // Limpa os containers de ordens
            const ordersContainer = document.getElementById('service-orders-container');
            if (ordersContainer) {
                ordersContainer.innerHTML = '<div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i> Carregando ordens...</div>';
            }

            // Busca as ordens do técnico
            fetch('/api/ordens/tecnico')
                .then(response => response.json())
                .then(response => {
                    if (response.success && response.data && response.data.length > 0) {
                        this.renderTechnicianOrders(response.data);
                    } else {
                        if (ordersContainer) {
                            ordersContainer.innerHTML = '<div class="alert alert-info">Nenhuma ordem encontrada para este técnico.</div>';
                        }
                        this.showNotification('Nenhuma ordem encontrada para este técnico.', 'info');
                    }
                })
                .catch(error => {
                    console.error('Erro ao carregar ordens do técnico:', error);
                    if (ordersContainer) {
                        ordersContainer.innerHTML = '<div class="alert alert-danger">Erro ao carregar ordens. Tente novamente mais tarde.</div>';
                    }
                    this.showNotification('Erro ao carregar ordens do técnico: ' + error.message, 'error');
                });
        } else {
            this.showNotification('API não disponível', 'error');
        }
    }

    // Renderiza as ordens do técnico na interface
    renderTechnicianOrders(orders) {
        console.log('Renderizando ordens do técnico:', orders);

        const ordersContainer = document.getElementById('service-orders-container');
        if (!ordersContainer) return;

        // Limpa o container
        ordersContainer.innerHTML = '';

        console.log(`Renderizando ${orders.length} ordens`);

        // Renderiza cada ordem
        orders.forEach(order => {
            const orderElement = document.createElement('div');
            orderElement.className = 'service-order-item';
            orderElement.setAttribute('data-status', order.status.toLowerCase());
            orderElement.setAttribute('data-date', this.formatarData(order.data));

            // Define a classe de prioridade
            let priorityClass = 'bg-primary';
            if (order.prioridade.toLowerCase() === 'alta' || order.prioridade.toLowerCase() === 'urgente') {
                priorityClass = 'bg-danger';
            } else if (order.prioridade.toLowerCase() === 'média' || order.prioridade.toLowerCase() === 'media') {
                priorityClass = 'bg-warning';
            }

            orderElement.innerHTML = `
                <div class="order-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <div class="order-details">
                    <h6 class="order-title">Ordem #${order.id} - ${order.titulo}</h6>
                    <div class="order-info">
                        <span class="order-location"><i class="fas fa-map-marker-alt"></i> ${order.filial}</span>
                        <span class="order-equipment"><i class="fas fa-cog"></i> ${order.equipamento}</span>
                    </div>
                    <div class="order-priority">
                        <span class="badge ${priorityClass}">${order.prioridade}</span>
                    </div>
                </div>
            `;

            // Adiciona evento de clique
            orderElement.addEventListener('click', () => {
                this.loadOrderDetails(order.id);

                // Virar o cartão do calendário
                const flipCard = document.getElementById('flip-card');
                if (flipCard) {
                    flipCard.classList.add('flipped');
                }

                // Configurar os cards após o flip
                setTimeout(() => {
                    if (window.setupMaintenanceCards) {
                        window.setupMaintenanceCards();
                    }
                }, 500);
            });

            ordersContainer.appendChild(orderElement);
        });
    }

    // Função removida: loadSimulatedOrderDetails
    // Esta função foi removida para garantir que apenas dados reais do banco de dados sejam exibidos

    // Atualiza os detalhes da ordem na interface
    updateOrderDetails(data) {
        console.log('Atualizando detalhes da ordem com dados:', data);

        // Mapear campos da API para os campos esperados pela interface
        const mappedData = {
            id: data.id,
            titulo: data.titulo || data.title || `Manutenção ${data.id}`,
            equipamento: data.equipamento || data.number || '',
            filial: data.filial || `Filial ${data.branch_id}`,
            prioridade: data.prioridade || data.priority || 'Média',
            status: data.status || 'Pendente',
            data: data.data || (data.due_date ? this.formatarData(data.due_date) : ''),
            responsavel: data.responsavel || (data.technician_id ? `Técnico ${data.technician_id}` : 'Não atribuído'),
            tempo: data.tempo || '2 horas',
            descricao: data.descricao || data.problem || '',
            manutencao: data.manutencao || { descricao: '', pecas_utilizadas: '', observacoes: '' },
            custos: data.custos || { pecas: 0.0, mao_obra: 0.0, deslocamento: 0.0, total: 0.0 },
            cronograma: data.cronograma || { data_inicio: '', hora_inicio: '', data_fim: '', hora_fim: '', status: '' },
            chat: data.chat || []
        };

        console.log('Dados mapeados para a interface:', mappedData);

        // Atualiza o título da ordem
        const orderPanelTitle = document.getElementById('order-panel-title');
        if (orderPanelTitle) {
            orderPanelTitle.textContent = `Ordem #${mappedData.id} - ${mappedData.titulo}`;
        }

        // Atualiza os detalhes principais
        const orderTitle = document.getElementById('order-title');
        if (orderTitle) {
            orderTitle.textContent = mappedData.titulo;
        }

        const orderPriority = document.getElementById('order-priority');
        if (orderPriority) {
            orderPriority.textContent = mappedData.prioridade;

            // Define a classe de acordo com a prioridade
            orderPriority.className = 'badge';
            const prioridadeLower = mappedData.prioridade.toLowerCase();
            if (prioridadeLower === 'alta' || prioridadeLower === 'urgente' || prioridadeLower === 'crítica' || prioridadeLower === 'critica') {
                orderPriority.classList.add('bg-danger');
            } else if (prioridadeLower === 'média' || prioridadeLower === 'media') {
                orderPriority.classList.add('bg-warning');
            } else {
                orderPriority.classList.add('bg-primary');
            }
        }

        const orderDescription = document.getElementById('order-description');
        if (orderDescription) {
            orderDescription.textContent = mappedData.descricao;
        }

        const orderLocation = document.getElementById('order-location');
        if (orderLocation) {
            orderLocation.innerHTML = `<i class="fas fa-map-marker-alt text-danger"></i> ${mappedData.filial}`;
        }

        const orderEquipment = document.getElementById('order-equipment');
        if (orderEquipment) {
            orderEquipment.innerHTML = `<i class="fas fa-tools text-secondary"></i> ${mappedData.equipamento}`;
        }

        const orderResponsible = document.getElementById('order-responsible');
        if (orderResponsible) {
            orderResponsible.innerHTML = `<i class="fas fa-user text-primary"></i> ${mappedData.responsavel}`;
        }

        const orderStatus = document.getElementById('order-status');
        if (orderStatus) {
            const statusLower = mappedData.status.toLowerCase();
            let statusClass = 'bg-info';

            if (statusLower === 'pendente' || statusLower === 'pending') {
                statusClass = 'bg-info';
            } else if (statusLower === 'em_andamento' || statusLower === 'in_progress') {
                statusClass = 'bg-warning';
            } else if (statusLower === 'concluida' || statusLower === 'concluído' || statusLower === 'completed') {
                statusClass = 'bg-success';
            } else if (statusLower === 'cancelada' || statusLower === 'cancelled') {
                statusClass = 'bg-secondary';
            }

            orderStatus.innerHTML = `<span class="badge ${statusClass}">${mappedData.status}</span>`;
        }

        const orderTime = document.getElementById('order-time');
        if (orderTime) {
            orderTime.innerHTML = `<i class="far fa-clock"></i> ${mappedData.tempo}`;
        }

        // Atualiza os previews dos cards
        this.updateCardPreviews(mappedData);

        // Atualiza o chat
        this.updateChat(mappedData.chat);
    }

    // Atualiza os previews dos cards
    updateCardPreviews(data) {
        // Preview de manutenção
        const manutencaoPreview = document.getElementById('manutencao-preview');
        if (manutencaoPreview && data.manutencao) {
            if (data.manutencao.descricao) {
                manutencaoPreview.textContent = data.manutencao.descricao.substring(0, 100) +
                    (data.manutencao.descricao.length > 100 ? '...' : '');
            } else {
                manutencaoPreview.textContent = 'Clique para adicionar detalhes da manutenção';
            }
        }

        // Preview de custos
        const custosPreview = document.getElementById('custos-preview');
        if (custosPreview && data.custos) {
            custosPreview.innerHTML = `
                <div class="d-flex justify-content-between">
                    <span>Peças:</span>
                    <span>R$ ${this.formatarNumero(data.custos.pecas)}</span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>Mão de obra:</span>
                    <span>R$ ${this.formatarNumero(data.custos.mao_obra)}</span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>Deslocamento:</span>
                    <span>R$ ${this.formatarNumero(data.custos.deslocamento)}</span>
                </div>
                <div class="d-flex justify-content-between mt-2 text-warning fw-bold">
                    <span>Total:</span>
                    <span>R$ ${this.formatarNumero(data.custos.total)}</span>
                </div>
            `;
        }

        // Preview de cronograma
        const scheduleStart = document.getElementById('schedule-start');
        const scheduleEnd = document.getElementById('schedule-end');
        const scheduleDuration = document.getElementById('schedule-duration');

        if (scheduleStart && scheduleEnd && scheduleDuration && data.cronograma) {
            if (data.cronograma.data_inicio && data.cronograma.hora_inicio) {
                scheduleStart.textContent = `${this.formatarData(data.cronograma.data_inicio)} ${data.cronograma.hora_inicio}`;
            } else {
                scheduleStart.textContent = 'Não definido';
            }

            if (data.cronograma.data_fim && data.cronograma.hora_fim) {
                scheduleEnd.textContent = `${this.formatarData(data.cronograma.data_fim)} ${data.cronograma.hora_fim}`;
            } else {
                scheduleEnd.textContent = 'Não definido';
            }

            if (data.cronograma.data_inicio && data.cronograma.hora_inicio &&
                data.cronograma.data_fim && data.cronograma.hora_fim) {
                const inicio = new Date(`${data.cronograma.data_inicio}T${data.cronograma.hora_inicio}`);
                const fim = new Date(`${data.cronograma.data_fim}T${data.cronograma.hora_fim}`);
                scheduleDuration.textContent = this.calcularDuracao(inicio, fim);
            } else {
                scheduleDuration.textContent = data.tempo || 'Não definido';
            }
        }
    }

    // Atualiza o chat
    updateChat(chatMessages) {
        const chatPreview = document.querySelector('.chat-preview');
        if (chatPreview && chatMessages && chatMessages.length > 0) {
            chatPreview.innerHTML = '';

            // Adiciona as últimas 2 mensagens ao preview
            const lastMessages = chatMessages.slice(-2);
            lastMessages.forEach(msg => {
                const messageElement = document.createElement('div');
                messageElement.className = 'chat-message-preview';
                messageElement.innerHTML = `
                    <small class="${msg.remetente === 'Técnico' ? 'text-info' : 'text-warning'}">${msg.remetente}:</small>
                    <p class="m-0">${msg.mensagem}</p>
                `;
                chatPreview.appendChild(messageElement);
            });
        }

        // Atualiza o modal de chat
        const chatArea = document.getElementById('chatArea');
        if (chatArea && chatMessages && chatMessages.length > 0) {
            chatArea.innerHTML = '';

            // Adiciona todas as mensagens ao chat
            chatMessages.forEach(msg => {
                const messageElement = document.createElement('div');
                messageElement.className = msg.remetente === 'Técnico' ?
                    'chat-message mb-2 text-end' :
                    'chat-message mb-2 text-start';

                const date = new Date(msg.data_envio);
                const time = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;

                messageElement.innerHTML = msg.remetente === 'Técnico' ?
                    `<small class="text-info">(${time}) ${msg.remetente}:</small>
                    <p class="m-0 bg-primary p-2 rounded d-inline-block">${msg.mensagem}</p>` :
                    `<small class="text-warning">${msg.remetente} (${time}):</small>
                    <p class="m-0 bg-secondary p-2 rounded d-inline-block">${msg.mensagem}</p>`;

                chatArea.appendChild(messageElement);
            });

            // Rola para o final do chat
            chatArea.scrollTop = chatArea.scrollHeight;
        }
    }

    // Formata um número para exibição como moeda
    formatarNumero(valor) {
        return parseFloat(valor).toFixed(2);
    }

    // Formata uma data para exibição
    formatarData(dataString) {
        const data = new Date(dataString);
        return `${data.getDate().toString().padStart(2, '0')}/${(data.getMonth() + 1).toString().padStart(2, '0')}/${data.getFullYear()}`;
    }

    // Calcula a duração entre duas datas
    calcularDuracao(inicio, fim) {
        const diff = Math.abs(fim - inicio);
        const horas = Math.floor(diff / (1000 * 60 * 60));
        const minutos = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

        if (horas === 0) {
            return `${minutos} minutos`;
        } else if (minutos === 0) {
            return `${horas} horas`;
        } else {
            return `${horas}h ${minutos}min`;
        }
    }

    // Mostra uma notificação
    showNotification(message, type = 'info') {
        // Usar a função de notificação da classe OrdemTecnicoModals, se disponível
        if (window.OrdemTecnicoModals) {
            window.OrdemTecnicoModals.showNotification(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }
}

// Exporta a classe para uso global
window.OrdemTecnicoDetails = new OrdemTecnicoDetails();
