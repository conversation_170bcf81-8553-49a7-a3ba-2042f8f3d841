/**
 * Editar Perfil - Sistema de Manutenção Shell
 * Script para gerenciar a página de edição de perfil do usuário
 */
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar formulário
    initializeForm();
    
    // Inicializar upload de avatar
    initializeAvatarUpload();
});

/**
 * Inicializa o formulário de edição de perfil
 */
function initializeForm() {
    const profileForm = document.getElementById('profileForm');
    
    if (profileForm) {
        profileForm.addEventListener('submit', function(event) {
            event.preventDefault();
            
            // Validar formulário
            if (!validateForm()) {
                return;
            }
            
            // Obter dados do formulário
            const formData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                position: document.getElementById('position').value,
                bio: document.getElementById('bio').value,
                preferences: {
                    emailNotifications: document.getElementById('emailNotifications').checked,
                    pushNotifications: document.getElementById('pushNotifications').checked,
                    smsNotifications: document.getElementById('smsNotifications').checked
                }
            };
            
            // Enviar dados para a API
            updateProfile(formData);
        });
    }
}

/**
 * Valida o formulário de edição de perfil
 * @returns {boolean} - Retorna true se o formulário for válido
 */
function validateForm() {
    const name = document.getElementById('name').value;
    const email = document.getElementById('email').value;
    const bio = document.getElementById('bio').value;
    
    // Validar nome
    if (!name || name.trim() === '') {
        showError('O nome é obrigatório.');
        return false;
    }
    
    // Validar e-mail
    if (!email || email.trim() === '') {
        showError('O e-mail é obrigatório.');
        return false;
    }
    
    // Validar formato do e-mail
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        showError('O formato do e-mail é inválido.');
        return false;
    }
    
    // Validar tamanho da biografia
    if (bio && bio.length > 200) {
        showError('A biografia deve ter no máximo 200 caracteres.');
        return false;
    }
    
    return true;
}

/**
 * Atualiza o perfil do usuário
 * @param {Object} profileData - Dados do perfil
 */
function updateProfile(profileData) {
    // Exibir indicador de carregamento
    const submitButton = document.querySelector('button[type="submit"]');
    const originalButtonText = submitButton.innerHTML;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Salvando...';
    submitButton.disabled = true;
    
    // Fazer requisição para a API
    fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(profileData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Falha ao atualizar perfil');
        }
        return response.json();
    })
    .then(data => {
        // Exibir mensagem de sucesso
        showSuccess();
        
        // Restaurar botão
        submitButton.innerHTML = originalButtonText;
        submitButton.disabled = false;
    })
    .catch(error => {
        console.error('Erro ao atualizar perfil:', error);
        
        // Exibir mensagem de erro
        showError('Erro ao atualizar perfil: ' + error.message);
        
        // Restaurar botão
        submitButton.innerHTML = originalButtonText;
        submitButton.disabled = false;
    });
}

/**
 * Inicializa o upload de avatar
 */
function initializeAvatarUpload() {
    const avatarUpload = document.getElementById('avatarUpload');
    
    if (avatarUpload) {
        avatarUpload.addEventListener('change', function(event) {
            const file = event.target.files[0];
            
            if (file) {
                // Verificar tipo de arquivo
                if (!file.type.match('image.*')) {
                    showError('Por favor, selecione uma imagem válida.');
                    return;
                }
                
                // Verificar tamanho do arquivo (máximo 2MB)
                if (file.size > 2 * 1024 * 1024) {
                    showError('A imagem deve ter no máximo 2MB.');
                    return;
                }
                
                // Criar FormData para upload
                const formData = new FormData();
                formData.append('avatar', file);
                
                // Fazer upload do avatar
                uploadAvatar(formData);
            }
        });
    }
}

/**
 * Faz upload do avatar
 * @param {FormData} formData - Dados do formulário
 */
function uploadAvatar(formData) {
    // Exibir indicador de carregamento
    const avatarContainer = document.querySelector('.avatar-container');
    avatarContainer.classList.add('uploading');
    
    // Fazer requisição para a API
    fetch('/api/user/avatar', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Falha ao fazer upload do avatar');
        }
        return response.json();
    })
    .then(data => {
        // Atualizar avatar
        const avatarElement = document.querySelector('.profile-avatar-large');
        if (data.avatarUrl) {
            avatarElement.innerHTML = `<img src="${data.avatarUrl}" alt="Avatar" class="avatar-image">`;
        }
        
        // Remover indicador de carregamento
        avatarContainer.classList.remove('uploading');
        
        // Exibir mensagem de sucesso
        showSuccess('Avatar atualizado com sucesso!');
    })
    .catch(error => {
        console.error('Erro ao fazer upload do avatar:', error);
        
        // Remover indicador de carregamento
        avatarContainer.classList.remove('uploading');
        
        // Exibir mensagem de erro
        showError('Erro ao fazer upload do avatar: ' + error.message);
    });
}

/**
 * Exibe mensagem de sucesso
 * @param {string} message - Mensagem de sucesso
 */
function showSuccess(message = 'Suas informações foram atualizadas com sucesso!') {
    const successModal = new bootstrap.Modal(document.getElementById('successModal'));
    successModal.show();
}

/**
 * Exibe mensagem de erro
 * @param {string} message - Mensagem de erro
 */
function showError(message) {
    const errorModal = document.getElementById('errorModal');
    const errorMessage = document.getElementById('errorMessage');
    
    errorMessage.textContent = message;
    
    const modal = new bootstrap.Modal(errorModal);
    modal.show();
}
