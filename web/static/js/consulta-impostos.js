/**
 * Script para gerenciar a funcionalidade da página de consulta de impostos
 */
document.addEventListener('DOMContentLoaded', function() {
    // Elementos do DOM
    const formConsulta = document.getElementById('formConsultaImpostos');
    const estadoSelect = document.getElementById('estadoSelect');
    const combustivelSelect = document.getElementById('combustivelSelect');
    const valorBaseInput = document.getElementById('valorBaseInput');
    const mensagemErro = document.getElementById('mensagemErro');
    const resultadosContainer = document.getElementById('resultadosContainer');
    const tabelaResultadosBody = document.getElementById('tabelaResultadosBody');
    const totalImpostosElement = document.getElementById('totalImpostos');
    const valorFinalElement = document.getElementById('valorFinal');
    const btnExportarPDF = document.getElementById('btnExportarPDF');
    
    // Inicializar calculadora
    const calculadora = new CalculadoraImpostos();
    
    // Preencher selects ao carregar a página
    preencherSelects();
    
    // Event Listeners
    formConsulta.addEventListener('submit', handleFormSubmit);
    valorBaseInput.addEventListener('input', formatarInputValor);
    if (btnExportarPDF) {
        btnExportarPDF.addEventListener('click', exportarResultadosPDF);
    }
    
    /**
     * Preenche os selects de estado e combustível com as opções disponíveis
     */
    function preencherSelects() {
        // Preencher estados
        const estados = calculadora.getEstadosDisponiveis();
        estados.forEach(estado => {
            const option = document.createElement('option');
            option.value = estado;
            option.textContent = estado;
            estadoSelect.appendChild(option);
        });
        
        // Preencher combustíveis
        const combustiveis = calculadora.getTiposCombustiveisDisponiveis();
        combustiveis.forEach(combustivel => {
            const option = document.createElement('option');
            option.value = combustivel.id;
            option.textContent = combustivel.nome;
            combustivelSelect.appendChild(option);
        });
    }
    
    /**
     * Formata o valor monetário para exibição
     * @param {number} valor - Valor a ser formatado
     * @returns {string} Valor formatado como moeda
     */
    function formatarMoeda(valor) {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).format(valor);
    }
    
    /**
     * Formata percentual para exibição
     * @param {number} percentual - Valor percentual a ser formatado
     * @returns {string} Percentual formatado
     */
    function formatarPercentual(percentual) {
        return new Intl.NumberFormat('pt-BR', {
            style: 'percent',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(percentual / 100);
    }
    
    /**
     * Exibe mensagem de erro
     * @param {string} mensagem - Mensagem de erro a ser exibida
     */
    function mostrarErro(mensagem) {
        mensagemErro.textContent = mensagem;
        mensagemErro.classList.remove('d-none');
    }
    
    /**
     * Remove mensagem de erro
     */
    function limparErro() {
        mensagemErro.textContent = '';
        mensagemErro.classList.add('d-none');
    }
    
    /**
     * Formata o input de valor como moeda
     */
    function formatarInputValor() {
        const valor = valorBaseInput.value.replace(/\D/g, '');
        if (valor) {
            const valorNumerico = parseFloat(valor) / 100;
            valorBaseInput.value = formatarMoeda(valorNumerico);
        }
    }
    
    /**
     * Exibe os resultados do cálculo
     * @param {Object} resultados - Objeto com os resultados dos cálculos
     * @param {number} valorBase - Valor base usado no cálculo
     */
    function exibirResultados(resultados, valorBase) {
        // Limpar resultados anteriores
        tabelaResultadosBody.innerHTML = '';
        
        // Variável para armazenar a soma total de impostos
        let totalImpostos = 0;
        
        // Adicionar cada imposto na tabela
        Object.entries(resultados).forEach(([imposto, dados]) => {
            if (imposto !== 'total') {
                const tr = document.createElement('tr');
                
                const tdImposto = document.createElement('td');
                tdImposto.textContent = obterNomeImposto(imposto);
                tr.appendChild(tdImposto);
                
                const tdAliquota = document.createElement('td');
                tdAliquota.textContent = formatarPercentual(dados.percentual);
                tr.appendChild(tdAliquota);
                
                const tdValor = document.createElement('td');
                tdValor.textContent = formatarMoeda(dados.valor);
                tr.appendChild(tdValor);
                
                tabelaResultadosBody.appendChild(tr);
                
                totalImpostos += dados.valor;
            }
        });
        
        // Atualizar total de impostos e valor final
        totalImpostosElement.textContent = formatarMoeda(totalImpostos);
        const valorFinal = valorBase + totalImpostos;
        valorFinalElement.textContent = formatarMoeda(valorFinal);
        
        // Mostrar resultados
        resultadosContainer.classList.remove('d-none');
        resultadosContainer.scrollIntoView({ behavior: 'smooth' });
    }
    
    /**
     * Obtém o nome amigável do imposto
     * @param {string} codigoImposto - Código do imposto
     * @returns {string} Nome amigável do imposto
     */
    function obterNomeImposto(codigoImposto) {
        const nomesImpostos = {
            'cide': 'CIDE',
            'pisCofinsProduto': 'PIS/COFINS Produto',
            'pisCofinsAeac': 'PIS/COFINS AEAC',
            'mistura': 'Mistura',
            'cidePisCof': 'CIDE + PIS/COFINS',
            'icms': 'ICMS',
            'pisCofinsProd': 'PIS/COFINS Produto',
            'pisCofinsDistr': 'PIS/COFINS Distribuidor',
            'pisCofinsEtanol': 'PIS/COFINS Etanol',
            'pmpf': 'PMPF',
            'icmsTotal': 'ICMS Total',
            'pisCofinsDiesel': 'PIS/COFINS Diesel',
            'pisCofinsBio': 'PIS/COFINS Biodiesel'
        };
        
        return nomesImpostos[codigoImposto] || codigoImposto;
    }
    
    /**
     * Trata o envio do formulário
     * @param {Event} event - Evento de submit do formulário
     */
    function handleFormSubmit(event) {
        event.preventDefault();
        limparErro();
        
        // Obter valores do formulário
        const estado = estadoSelect.value;
        const tipoCombustivel = combustivelSelect.value;
        const valorBaseStr = valorBaseInput.value.replace(/\D/g, '');
        
        // Validações
        if (!estado) {
            mostrarErro('Por favor, selecione um estado.');
            return;
        }
        
        if (!tipoCombustivel) {
            mostrarErro('Por favor, selecione um tipo de combustível.');
            return;
        }
        
        if (!valorBaseStr) {
            mostrarErro('Por favor, informe o valor base para cálculo.');
            return;
        }
        
        // Converter valor base para número
        const valorBase = parseFloat(valorBaseStr) / 100;
        
        try {
            // Calcular impostos
            const resultados = calculadora.calcularImpostos(tipoCombustivel, estado, valorBase);
            
            // Exibir resultados
            exibirResultados(resultados, valorBase);
        } catch (error) {
            mostrarErro(`Erro ao calcular impostos: ${error.message}`);
            console.error('Erro ao calcular impostos:', error);
        }
    }
    
    /**
     * Exporta os resultados para PDF
     */
    function exportarResultadosPDF() {
        if (resultadosContainer.classList.contains('d-none')) {
            mostrarErro('Não há resultados para exportar.');
            return;
        }
        
        const estado = estadoSelect.options[estadoSelect.selectedIndex].text;
        const combustivel = combustivelSelect.options[combustivelSelect.selectedIndex].text;
        const valorBase = valorBaseInput.value;
        
        // Configuração do PDF
        const docDefinition = {
            content: [
                { text: 'Relatório de Cálculo de Impostos de Combustíveis', style: 'header' },
                { text: `Data: ${new Date().toLocaleDateString('pt-BR')}`, style: 'subheader' },
                { text: '\n' },
                {
                    text: [
                        { text: 'Estado: ', bold: true }, estado, '\n',
                        { text: 'Combustível: ', bold: true }, combustivel, '\n',
                        { text: 'Valor Base: ', bold: true }, valorBase, '\n\n'
                    ]
                },
                {
                    table: {
                        headerRows: 1,
                        widths: ['*', 'auto', 'auto'],
                        body: [
                            [
                                { text: 'Imposto', style: 'tableHeader' },
                                { text: 'Alíquota', style: 'tableHeader' },
                                { text: 'Valor', style: 'tableHeader' }
                            ],
                            // Adicionar linhas da tabela de resultados
                            ...Array.from(tabelaResultadosBody.rows).map(row => {
                                return Array.from(row.cells).map(cell => cell.textContent);
                            })
                        ]
                    }
                },
                { text: '\n' },
                {
                    text: [
                        { text: 'Total de Impostos: ', bold: true }, totalImpostosElement.textContent, '\n',
                        { text: 'Valor Final: ', bold: true }, valorFinalElement.textContent
                    ]
                }
            ],
            styles: {
                header: {
                    fontSize: 18,
                    bold: true,
                    alignment: 'center',
                    margin: [0, 0, 0, 10]
                },
                subheader: {
                    fontSize: 14,
                    bold: true,
                    margin: [0, 10, 0, 5]
                },
                tableHeader: {
                    bold: true,
                    fontSize: 12,
                    color: 'black'
                }
            },
            defaultStyle: {
                fontSize: 10
            }
        };
        
        // Gerar e baixar o PDF
        pdfMake.createPdf(docDefinition).download('calculo-impostos-combustiveis.pdf');
    }
});