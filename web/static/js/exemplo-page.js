/**
 * Script para a página de exemplo
 * Compatível com o Design System Shell
 */

// Variáveis globais
let dadosTabela = [];
let filtroAtivo = false;

// Inicialização após o DOM estar pronto
document.addEventListener('DOMContentLoaded', function() {
    console.log('Página de exemplo carregada');
    
    // Inicializar componentes
    inicializarComponentes();
    
    // Configurar listeners de eventos
    configurarEventos();
    
    // Carregar dados (simulado)
    carregarDados();
});

/**
 * Inicializa os componentes da página
 */
function inicializarComponentes() {
    // Adicionar tooltips
    const tooltips = document.querySelectorAll('[data-toggle="tooltip"]');
    tooltips.forEach(el => {
        new bootstrap.Tooltip(el);
    });
    
    // Inicializar componentes de data
    const datePickers = document.querySelectorAll('input[type="date"]');
    datePickers.forEach(el => {
        el.valueAsDate = new Date();
    });
    
    // Aplicar animações em componentes
    const animatedElements = document.querySelectorAll('.stats-card');
    animatedElements.forEach((el, index) => {
        el.style.animationDelay = `${index * 0.1}s`;
    });
}

/**
 * Configura os listeners de eventos
 */
function configurarEventos() {
    // Formulário de filtro
    const formFiltro = document.querySelector('.form-shell');
    if (formFiltro) {
        formFiltro.addEventListener('submit', function(e) {
            e.preventDefault();
            aplicarFiltros();
        });
    }
    
    // Botão de exportação
    const btnExportar = document.querySelector('.btn-shell-yellow');
    if (btnExportar) {
        btnExportar.addEventListener('click', function() {
            exportarDados();
        });
    }
    
    // Botões de edição
    const botoesEdicao = document.querySelectorAll('.btn-sm.btn-shell-red');
    botoesEdicao.forEach(btn => {
        btn.addEventListener('click', function() {
            const row = this.closest('tr');
            const id = row.cells[0].textContent;
            editarItem(id);
        });
    });
}

/**
 * Carrega dados simulados para a página
 */
function carregarDados() {
    // Simulação de chamada AJAX
    console.log('Carregando dados...');
    
    // Em um cenário real, você faria uma requisição AJAX aqui
    // fetch('/api/dados')
    //    .then(response => response.json())
    //    .then(data => processarDados(data))
    //    .catch(error => console.error('Erro ao carregar dados:', error));
    
    // Simula um atraso de carregamento
    setTimeout(() => {
        console.log('Dados carregados com sucesso');
        
        // Atualiza contadores com animação
        animarContadores();
    }, 1000);
}

/**
 * Aplica os filtros do formulário
 */
function aplicarFiltros() {
    const dataInicio = document.getElementById('dataInicio').value;
    const dataFim = document.getElementById('dataFim').value;
    const status = document.getElementById('status').value;
    
    console.log('Aplicando filtros:', { dataInicio, dataFim, status });
    
    // Aqui você filtraria os dados reais
    // Por enquanto apenas exibimos um alerta de sucesso
    
    // Cria um alerta de sucesso
    const alerta = document.createElement('div');
    alerta.className = 'alert-shell alert-shell-success mt-3';
    alerta.textContent = 'Filtros aplicados com sucesso';
    
    // Adiciona ao formulário
    const form = document.querySelector('.form-shell');
    form.appendChild(alerta);
    
    // Remove o alerta após 3 segundos
    setTimeout(() => {
        alerta.remove();
    }, 3000);
    
    filtroAtivo = true;
}

/**
 * Exporta os dados da tabela
 */
function exportarDados() {
    console.log('Exportando dados...');
    
    // Aqui você implementaria a lógica real de exportação
    // Por exemplo, gerando um CSV ou PDF
    
    // Apenas mostra um feedback visual para o usuário
    const btn = document.querySelector('.btn-shell-yellow');
    const textoOriginal = btn.innerHTML;
    
    btn.innerHTML = '<i class="fas fa-check me-2"></i>Exportado!';
    btn.disabled = true;
    
    setTimeout(() => {
        btn.innerHTML = textoOriginal;
        btn.disabled = false;
    }, 2000);
}

/**
 * Edita um item da tabela
 * @param {string} id - ID do item a ser editado
 */
function editarItem(id) {
    console.log(`Editando item com ID: ${id}`);
    
    // Em uma aplicação real, isso abriria um modal ou redirecionaria
    // para uma página de edição
    
    // Por enquanto, apenas exibimos um alerta
    alert(`Você selecionou o item ${id} para edição`);
}

/**
 * Anima os contadores de estatísticas
 */
function animarContadores() {
    const contadores = document.querySelectorAll('.stats-card-value');
    
    contadores.forEach(contador => {
        const valorFinal = contador.textContent;
        let valorAtual = 0;
        
        // Remove vírgulas e pontos para obter apenas números
        const valorNumerico = parseFloat(valorFinal.replace(/,/g, '').replace(/%/g, ''));
        
        const duracaoAnimacao = 1500; // 1.5 segundos
        const passos = 50;
        const incremento = valorNumerico / passos;
        const intervalo = duracaoAnimacao / passos;
        
        // Inicia com zero
        contador.textContent = '0';
        
        // Anima o contador
        const timer = setInterval(() => {
            valorAtual += incremento;
            
            if (valorAtual >= valorNumerico) {
                clearInterval(timer);
                contador.textContent = valorFinal; // Garante o valor final exato
            } else {
                // Formata o número adequadamente
                if (valorFinal.includes('%')) {
                    contador.textContent = valorAtual.toFixed(1) + '%';
                } else if (valorFinal.includes(',')) {
                    contador.textContent = Math.floor(valorAtual).toLocaleString();
                } else {
                    contador.textContent = Math.floor(valorAtual);
                }
            }
        }, intervalo);
    });
} 