/**
 * Script para a página de Acesso Negado
 * Sistema Shell Tradição
 *
 * Funcionalidades:
 * - Contador regressivo de 10 segundos
 * - Redirecionamento automático para o logout
 * - Animações e efeitos visuais
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elementos do DOM
    const countdownElement = document.getElementById('countdown');
    const messageElement = document.getElementById('message');
    const iconElement = document.getElementById('icon');
    const cardElement = document.querySelector('.acesso-negado-card');

    // Tempo inicial em segundos
    let timeLeft = 10;

    // Função para atualizar o contador
    function updateCountdown() {
        // Atualiza o texto do contador
        countdownElement.textContent = timeLeft;

        // Aplica efeito de shake quando o tempo estiver acabando
        if (timeLeft <= 3) {
            iconElement.classList.add('shake');
            countdownElement.style.color = '#ED1C24'; // Vermelho quando o tempo está acabando
        }

        // Decrementa o tempo
        timeLeft--;

        // Verifica se o tempo acabou
        if (timeLeft < 0) {
            // Adiciona classe de fade-out para animar a saída
            cardElement.classList.add('fade-out');

            // Redireciona para o logout após a animação terminar
            setTimeout(() => {
                window.location.href = '/logout';
            }, 800);

            // Limpa o intervalo
            clearInterval(countdownInterval);
        }
    }

    // Inicializa o contador
    updateCountdown();

    // Atualiza o contador a cada segundo
    const countdownInterval = setInterval(updateCountdown, 1000);

    // Adiciona animação de entrada
    cardElement.classList.add('fade-in');

    // Adiciona efeito de rotação aos ícones de engrenagem
    const gears = document.querySelectorAll('.rotating-gear');
    gears.forEach((gear, index) => {
        // Alterna a direção da rotação
        const direction = index % 2 === 0 ? 'normal' : 'reverse';
        // Alterna a velocidade
        const duration = 10 + (index * 2);

        gear.style.animationDirection = direction;
        gear.style.animationDuration = `${duration}s`;
    });

    // Botão para redirecionamento manual
    const redirectButton = document.getElementById('redirect-button');
    if (redirectButton) {
        redirectButton.addEventListener('click', function() {
            // Adiciona classe de fade-out para animar a saída
            cardElement.classList.add('fade-out');

            // Redireciona para o logout após a animação terminar
            setTimeout(() => {
                window.location.href = '/logout';
            }, 800);

            // Limpa o intervalo
            clearInterval(countdownInterval);
        });
    }

    // Adiciona frases engraçadas aleatórias
    const funnyPhrases = [
        "Ops! Parece que você tentou entrar em uma área VIP sem o convite...",
        "Acesso negado! Nem mesmo o MacGyver conseguiria entrar aqui.",
        "Houston, temos um problema. Você não tem permissão para acessar esta página.",
        "Esta área é como o Triângulo das Bermudas para o seu perfil de usuário.",
        "Você acaba de encontrar a Área 51 do nosso sistema. Acesso restrito!",
        "Nem o hacker mais experiente conseguiria entrar aqui (sem as permissões corretas).",
        "Esta página está mais protegida que o cofre de um banco!",
        "Parece que você encontrou a Câmara Secreta, mas não é o herdeiro de Slytherin.",
        "Você precisa de um nível de acesso mais alto para desbloquear esta área."
    ];

    // Seleciona uma frase aleatória
    const randomPhrase = funnyPhrases[Math.floor(Math.random() * funnyPhrases.length)];

    // Atualiza o elemento de mensagem com a frase aleatória
    if (messageElement) {
        messageElement.textContent = randomPhrase;
    }
});
