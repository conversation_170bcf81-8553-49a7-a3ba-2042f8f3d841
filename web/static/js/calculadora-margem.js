/**
 * Calculadora de Margem Personalizada
 * Este script gerencia a funcionalidade da calculadora de margem, permitindo
 * adicionar componentes de custo customizáveis e calcular a margem de lucro.
 */

document.addEventListener('DOMContentLoaded', () => {
    // Elementos da DOM
    const dropZone = document.getElementById('componentes-drop-zone');
    const btnAdicionarComponente = document.getElementById('btn-adicionar-componente');
    const btnCalcular = document.getElementById('btn-calcular');
    const btnSalvarComponente = document.getElementById('btn-salvar-componente');
    const componentes = document.querySelectorAll('.componente-item');
    const modalEditar = new bootstrap.Modal(document.getElementById('modal-editar-componente'));
    
    // Contadores para IDs únicos
    let contadorComponentes = 1;
    let componenteEditando = null;
    
    // Estado dos cálculos
    let estadoCalculadora = {
        precoCompra: 0,
        precoVenda: 0,
        volumeMensal: 0,
        componentes: []
    };
    
    // Inicializar componentes existentes
    inicializarComponentesExistentes();
    
    // Inicializar drag and drop
    inicializarDragAndDrop();
    
    // Event listeners para botões
    btnAdicionarComponente.addEventListener('click', adicionarNovoComponente);
    btnCalcular.addEventListener('click', calcularMargem);
    btnSalvarComponente.addEventListener('click', salvarComponenteEditado);
    
    // Inicializar gráfico
    inicializarGrafico();
    
    // Adicionar listeners para mudança de valores
    document.getElementById('preco-compra')?.addEventListener('input', atualizarCalculos);
    document.getElementById('preco-venda')?.addEventListener('input', atualizarCalculos);
    document.getElementById('volume-mensal')?.addEventListener('input', atualizarCalculos);
    
    // Inicializar cálculos
    setTimeout(() => {
        atualizarCalculos();
    }, 500);
    
    /**
     * Inicializa os componentes que já existem na página
     */
    function inicializarComponentesExistentes() {
        // Adicionar event listeners para botões de editar e remover
        document.querySelectorAll('.componente-container').forEach(componente => {
            const btnEditar = componente.querySelector('.btn-editar');
            const btnRemover = componente.querySelector('.btn-remover');
            
            if (btnEditar) {
                btnEditar.addEventListener('click', () => {
                    abrirModalEditarComponente(componente);
                });
            }
            
            if (btnRemover) {
                btnRemover.addEventListener('click', () => {
                    removerComponente(componente);
                });
            }
            
            // Atualizar contador para evitar conflitos de ID
            const componenteId = componente.getAttribute('data-id');
            if (componenteId) {
                const match = componenteId.match(/\d+$/);
                if (match) {
                    const id = parseInt(match[0]);
                    contadorComponentes = Math.max(contadorComponentes, id + 1);
                }
            }
            
            // Adicionar listener para inputs
            const inputValor = componente.querySelector('input');
            if (inputValor) {
                inputValor.addEventListener('input', atualizarCalculos);
            }
        });
    }
    
    /**
     * Inicializa o sistema de drag and drop para os componentes
     */
    function inicializarDragAndDrop() {
        // Adicionar eventos de drag para componentes disponíveis
        componentes.forEach(componente => {
            componente.addEventListener('dragstart', function(e) {
                e.dataTransfer.setData('text/plain', this.getAttribute('data-tipo'));
                this.classList.add('dragging');
            });
            
            componente.addEventListener('dragend', function() {
                this.classList.remove('dragging');
            });
        });
        
        // Eventos de drop para a zona de soltar
        dropZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('drag-over');
        });
        
        dropZone.addEventListener('dragleave', function() {
            this.classList.remove('drag-over');
        });
        
        dropZone.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('drag-over');
            
            const tipo = e.dataTransfer.getData('text/plain');
            if (tipo) {
                criarComponente(tipo);
            }
        });
    }
    
    /**
     * Cria um novo componente baseado no tipo arrastado
     * @param {string} tipo - Tipo de componente a ser criado
     */
    function criarComponente(tipo) {
        // Obter o template
        const template = document.getElementById('template-componente');
        const clone = document.importNode(template.content, true);
        const componente = clone.querySelector('.componente-container');
        
        // Configurar ID único
        const idComponente = `componente-${tipo}-${contadorComponentes++}`;
        componente.setAttribute('data-id', idComponente);
        componente.setAttribute('data-tipo', tipo);
        
        // Personalizar baseado no tipo
        const icone = componente.querySelector('.componente-titulo i');
        const titulo = componente.querySelector('.componente-titulo span');
        const campoLabel = componente.querySelector('.campo-container label');
        const campoValor = componente.querySelector('.custo-valor');
        
        switch (tipo) {
            case 'custo-fixo':
                icone.className = 'fas fa-dollar-sign';
                titulo.textContent = 'Custo Fixo';
                campoLabel.textContent = 'Valor (R$/L)';
                break;
            case 'custo-variavel':
                icone.className = 'fas fa-percentage';
                titulo.textContent = 'Custo Variável';
                campoLabel.textContent = 'Alíquota (%)';
                break;
            case 'imposto':
                icone.className = 'fas fa-file-invoice-dollar';
                titulo.textContent = 'Imposto';
                campoLabel.textContent = 'Alíquota (%)';
                break;
            case 'transporte':
                icone.className = 'fas fa-truck';
                titulo.textContent = 'Transporte';
                campoLabel.textContent = 'Custo por Litro (R$)';
                break;
            case 'combustivel':
                icone.className = 'fas fa-gas-pump';
                titulo.textContent = 'Combustível';
                campoLabel.textContent = 'Custo por Litro (R$)';
                break;
            case 'formula':
                icone.className = 'fas fa-function';
                titulo.textContent = 'Fórmula Personalizada';
                
                // Substituir campo padrão por campo de fórmula
                const campoContainer = componente.querySelector('.campo-container');
                campoContainer.innerHTML = `
                    <label>Fórmula</label>
                    <input type="text" class="form-control formula-valor" value="preco * 0.05">
                    <small class="form-text text-muted">Variáveis: preco, volume, custo</small>
                `;
                break;
        }
        
        // Adicionar event listeners para botões
        const btnEditar = componente.querySelector('.btn-editar');
        const btnRemover = componente.querySelector('.btn-remover');
        
        btnEditar.addEventListener('click', () => {
            abrirModalEditarComponente(componente);
        });
        
        btnRemover.addEventListener('click', () => {
            removerComponente(componente);
        });
        
        // Adicionar event listener para input
        const input = componente.querySelector('input');
        if (input) {
            input.addEventListener('input', atualizarCalculos);
        }
        
        // Adicionar ao DOM com animação
        componente.classList.add('fade-in');
        dropZone.appendChild(componente);
        
        // Remover classe de animação após ela terminar
        setTimeout(() => {
            componente.classList.remove('fade-in');
        }, 300);
        
        // Atualizar cálculos
        atualizarCalculos();
    }
    
    /**
     * Adiciona um novo componente via botão
     */
    function adicionarNovoComponente() {
        const tiposPadrao = ['custo-fixo', 'imposto', 'transporte'];
        const tipoAleatorio = tiposPadrao[Math.floor(Math.random() * tiposPadrao.length)];
        criarComponente(tipoAleatorio);
    }
    
    /**
     * Abre o modal para editar um componente
     * @param {Element} componente - O componente a ser editado
     */
    function abrirModalEditarComponente(componente) {
        componenteEditando = componente;
        
        const tipo = componente.getAttribute('data-tipo') || 'custo-fixo';
        const titulo = componente.querySelector('.componente-titulo span').textContent;
        
        // Preencher campos do modal
        document.getElementById('edit-nome-componente').value = titulo;
        document.getElementById('edit-tipo-componente').value = tipo;
        
        // Mostrar/esconder campos baseado no tipo
        const formGroupValor = document.getElementById('form-group-valor');
        const formGroupAliquota = document.getElementById('form-group-aliquota');
        const formGroupFormula = document.getElementById('form-group-formula');
        
        // Resetar visibilidade
        formGroupValor.style.display = 'block';
        formGroupAliquota.style.display = 'none';
        formGroupFormula.style.display = 'none';
        
        // Configurar campos baseado no tipo
        if (tipo === 'imposto' || tipo === 'custo-variavel') {
            formGroupValor.style.display = 'none';
            formGroupAliquota.style.display = 'block';
            
            const campoValor = componente.querySelector('.custo-valor');
            if (campoValor) {
                document.getElementById('edit-aliquota-componente').value = campoValor.value;
            }
        } else if (tipo === 'formula') {
            formGroupValor.style.display = 'none';
            formGroupFormula.style.display = 'block';
            
            const campoFormula = componente.querySelector('.formula-valor');
            if (campoFormula) {
                document.getElementById('edit-formula-componente').value = campoFormula.value;
            }
        } else {
            const campoValor = componente.querySelector('.custo-valor');
            if (campoValor) {
                document.getElementById('edit-valor-componente').value = campoValor.value;
            }
        }
        
        // Mostrar modal
        modalEditar.show();
    }
    
    /**
     * Salva as alterações feitas no componente sendo editado
     */
    function salvarComponenteEditado() {
        if (!componenteEditando) return;
        
        const novoNome = document.getElementById('edit-nome-componente').value;
        const novoTipo = document.getElementById('edit-tipo-componente').value;
        
        // Atualizar nome e ícone
        const titulo = componenteEditando.querySelector('.componente-titulo span');
        const icone = componenteEditando.querySelector('.componente-titulo i');
        
        titulo.textContent = novoNome;
        componenteEditando.setAttribute('data-tipo', novoTipo);
        
        // Atualizar ícone baseado no tipo
        switch (novoTipo) {
            case 'custo-fixo':
                icone.className = 'fas fa-dollar-sign';
                break;
            case 'custo-variavel':
                icone.className = 'fas fa-percentage';
                break;
            case 'imposto':
                icone.className = 'fas fa-file-invoice-dollar';
                break;
            case 'transporte':
                icone.className = 'fas fa-truck';
                break;
            case 'combustivel':
                icone.className = 'fas fa-gas-pump';
                break;
            case 'formula':
                icone.className = 'fas fa-function';
                break;
        }
        
        // Atualizar campos baseado no tipo
        const componenteBody = componenteEditando.querySelector('.componente-body');
        
        if (novoTipo === 'formula') {
            const valorFormula = document.getElementById('edit-formula-componente').value;
            componenteBody.innerHTML = `
                <div class="campo-container">
                    <label>Fórmula</label>
                    <input type="text" class="form-control formula-valor" value="${valorFormula}">
                    <small class="form-text text-muted">Variáveis: preco, volume, custo</small>
                </div>
                <div class="campo-info">
                    <span class="info-label">Valor por Litro (R$):</span>
                    <span class="info-valor">R$ 0.00</span>
                </div>
            `;
        } else if (novoTipo === 'imposto' || novoTipo === 'custo-variavel') {
            const valorAliquota = document.getElementById('edit-aliquota-componente').value;
            componenteBody.innerHTML = `
                <div class="campo-container">
                    <label>Alíquota (%)</label>
                    <input type="number" class="form-control custo-valor" step="0.01" value="${valorAliquota}">
                </div>
                <div class="campo-info">
                    <span class="info-label">Valor (R$):</span>
                    <span class="info-valor">R$ 0.00</span>
                </div>
            `;
        } else {
            const valor = document.getElementById('edit-valor-componente').value;
            let labelText = 'Valor (R$/L)';
            
            if (novoTipo === 'transporte') labelText = 'Custo por Litro (R$)';
            if (novoTipo === 'combustivel') labelText = 'Custo por Litro (R$)';
            
            componenteBody.innerHTML = `
                <div class="campo-container">
                    <label>${labelText}</label>
                    <input type="number" class="form-control custo-valor" step="0.01" value="${valor}">
                </div>
                <div class="campo-info">
                    <span class="info-label">Valor Total (R$):</span>
                    <span class="info-valor">R$ 0.00</span>
                </div>
            `;
        }
        
        // Fechar modal
        modalEditar.hide();
        componenteEditando = null;
    }
    
    /**
     * Remove um componente da calculadora
     * @param {Element} componente - O componente a ser removido
     */
    function removerComponente(componente) {
        // Adicionar classe para animação de saída
        componente.classList.add('fade-out');
        
        // Remover após a animação
        setTimeout(() => {
            componente.remove();
        }, 300);
    }
    
    /**
     * Calcula a margem baseada nos componentes e valores
     */
    function calcularMargem() {
        // Obter valores principais
        const precoCompra = parseFloat(document.getElementById('preco-compra').value) || 0;
        const precoVenda = parseFloat(document.getElementById('preco-venda').value) || 0;
        const volumeMensal = parseInt(document.getElementById('volume-mensal').value) || 0;
        
        // Validar valores
        if (precoCompra <= 0 || precoVenda <= 0 || volumeMensal <= 0) {
            alert('Por favor, preencha todos os valores corretamente.');
            return;
        }
        
        // Atualizar estado
        estadoCalculadora.precoCompra = precoCompra;
        estadoCalculadora.precoVenda = precoVenda;
        estadoCalculadora.volumeMensal = volumeMensal;
        
        // Calcular componentes
        let totalCustos = precoCompra;
        let componentes = [];
        
        // Obter todos os componentes
        document.querySelectorAll('.componente-container').forEach(componente => {
            const tipo = componente.getAttribute('data-tipo');
            const nome = componente.querySelector('.componente-titulo span').textContent;
            let valor = 0;
            
            if (tipo === 'formula') {
                // Calcular valor baseado na fórmula
                const formulaInput = componente.querySelector('.formula-valor');
                if (formulaInput) {
                    valor = calcularFormula(
                        formulaInput.value,
                        precoCompra,
                        precoVenda,
                        volumeMensal
                    );
                }
            } else if (tipo === 'imposto' || tipo === 'custo-variavel') {
                // Calcular valor baseado na alíquota
                const aliquotaInput = componente.querySelector('.custo-valor');
                if (aliquotaInput) {
                    const aliquota = parseFloat(aliquotaInput.value) / 100;
                    valor = precoVenda * aliquota;
                }
            } else {
                // Valor direto
                const valorInput = componente.querySelector('.custo-valor');
                if (valorInput) {
                    valor = parseFloat(valorInput.value) || 0;
                }
            }
            
            // Atualizar a exibição do valor
            const infoValor = componente.querySelector('.info-valor');
            if (infoValor) {
                infoValor.textContent = `R$ ${valor.toFixed(2)}`;
            }
            
            // Adicionar ao total de custos
            totalCustos += valor;
            
            // Adicionar ao array de componentes
            componentes.push({
                tipo,
                nome,
                valor
            });
        });
        
        // Atualizar estado
        estadoCalculadora.componentes = componentes;
        
        // Calcular margens
        const margemBruta = precoVenda - precoCompra;
        const margemLiquida = precoVenda - totalCustos;
        const custosTotais = totalCustos - precoCompra; // excluir preço de compra do total
        
        // Calcular percentuais
        const margemBrutaPercent = (margemBruta / precoVenda) * 100;
        const margemLiquidaPercent = (margemLiquida / precoVenda) * 100;
        const custosTotaisPercent = (custosTotais / precoVenda) * 100;
        
        // Atualizar cards de resultado
        document.querySelector('.margem-bruta .resultado-valor').textContent = `R$ ${margemBruta.toFixed(2)}`;
        document.querySelector('.margem-bruta .resultado-percentual').textContent = `${margemBrutaPercent.toFixed(1)}%`;
        
        document.querySelector('.custos-totais .resultado-valor').textContent = `R$ ${custosTotais.toFixed(2)}`;
        document.querySelector('.custos-totais .resultado-percentual').textContent = `${custosTotaisPercent.toFixed(1)}%`;
        
        document.querySelector('.margem-liquida .resultado-valor').textContent = `R$ ${margemLiquida.toFixed(2)}`;
        document.querySelector('.margem-liquida .resultado-percentual').textContent = `${margemLiquidaPercent.toFixed(1)}%`;
        
        // Atualizar tabela detalhada
        atualizarTabelaDetalhes(precoCompra, precoVenda, componentes);
        
        // Atualizar gráfico
        atualizarGrafico(precoCompra, custosTotais, margemLiquida);
        
        // Atualizar projeção mensal
        const receitaTotal = precoVenda * volumeMensal;
        const custoTotal = totalCustos * volumeMensal;
        const lucroLiquido = margemLiquida * volumeMensal;
        
        // Formatar números com separador de milhar
        const formatarNumero = (numero) => {
            return numero.toLocaleString('pt-BR', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        };
        
        document.querySelector('.projecao-item:nth-child(1) .item-valor').textContent = `${formatarNumero(volumeMensal)} L`;
        document.querySelector('.projecao-item:nth-child(2) .item-valor').textContent = `R$ ${formatarNumero(receitaTotal)}`;
        document.querySelector('.projecao-item:nth-child(3) .item-valor').textContent = `R$ ${formatarNumero(custoTotal)}`;
        document.querySelector('.projecao-item:nth-child(4) .item-valor').textContent = `R$ ${formatarNumero(lucroLiquido)}`;
        
        // Mostrar resultados
        document.getElementById('resultados').style.display = 'block';
    }
    
    /**
     * Atualiza a tabela de detalhes com os valores calculados
     */
    function atualizarTabelaDetalhes(precoCompra, precoVenda, componentes) {
        const tabela = document.querySelector('.resultados-detalhes table tbody');
        tabela.innerHTML = '';
        
        // Linha para preço de compra
        const percCompra = (precoCompra / precoVenda) * 100;
        const rowCompra = document.createElement('tr');
        rowCompra.innerHTML = `
            <td>Preço de Compra</td>
            <td>R$ ${precoCompra.toFixed(2)}</td>
            <td>${percCompra.toFixed(1)}%</td>
        `;
        tabela.appendChild(rowCompra);
        
        // Linhas para componentes
        componentes.forEach(componente => {
            const percComponente = (componente.valor / precoVenda) * 100;
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${componente.nome}</td>
                <td>R$ ${componente.valor.toFixed(2)}</td>
                <td>${percComponente.toFixed(1)}%</td>
            `;
            tabela.appendChild(row);
        });
        
        // Atualizar linha de preço de venda no footer
        document.querySelector('.resultados-detalhes table tfoot th:nth-child(2)').textContent = `R$ ${precoVenda.toFixed(2)}`;
    }
    
    /**
     * Inicializa o gráfico de margem
     */
    function inicializarGrafico() {
        const ctx = document.getElementById('grafico-margem');
        if (!ctx) return;
        
        window.chart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Preço de Compra', 'Custos Adicionais', 'Margem Líquida'],
                datasets: [{
                    data: [4.25, 1.45, 1.09],
                    backgroundColor: [
                        '#444444',
                        '#FDB813',
                        '#28a745'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#f8f9fa',
                            font: {
                                family: "'Rajdhani', sans-serif",
                                size: 12
                            },
                            padding: 20
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: R$ ${value.toFixed(2)} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }
    
    /**
     * Atualiza o gráfico com novos valores
     */
    function atualizarGrafico(precoCompra, custosTotais, margemLiquida) {
        if (!window.chart) return;
        
        window.chart.data.datasets[0].data = [precoCompra, custosTotais, margemLiquida];
        window.chart.update();
    }
    
    /**
     * Calcula o valor baseado em uma fórmula personalizada
     * @param {string} formula - A fórmula a ser calculada
     * @param {number} precoCompra - Preço de compra
     * @param {number} precoVenda - Preço de venda
     * @param {number} volumeMensal - Volume mensal
     * @returns {number} - O resultado calculado
     */
    function calcularFormula(formula, precoCompra, precoVenda, volumeMensal) {
        try {
            // Substituir variáveis na fórmula
            const expressao = formula
                .replace(/preco/g, precoCompra)
                .replace(/venda/g, precoVenda)
                .replace(/volume/g, volumeMensal)
                .replace(/custo/g, precoCompra);
            
            // Avaliar a expressão de forma segura
            // Nota: em ambiente de produção, use uma biblioteca de avaliação segura
            const resultado = new Function(`return ${expressao}`)();
            
            return isNaN(resultado) ? 0 : resultado;
        } catch (error) {
            console.error('Erro ao calcular fórmula:', error);
            return 0;
        }
    }
}); 