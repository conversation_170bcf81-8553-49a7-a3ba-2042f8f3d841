/**
 * Cria o conteúdo da ferramenta de Cotações
 * @param {HTMLElement} container - Container onde o conteúdo será inserido
 */
function createCotacoesContent(container) {
    // Cria a estrutura do conteúdo
    container.innerHTML = `
        <!-- Seção de cotações principais -->
        <div class="tool-section">
            <h3 class="tool-section-title">
                <i class="fas fa-dollar-sign"></i> Cotações Principais
                <button class="btn-help" id="cotacoes-help">
                    <i class="fas fa-question-circle"></i>
                </button>
            </h3>
            <div class="tool-grid">
                <!-- Dólar Comercial -->
                <div class="tool-card">
                    <div class="tool-card-header">
                        <h4 class="tool-card-title">Dólar Comercial</h4>
                        <span class="tool-card-badge">USD/BRL</span>
                    </div>
                    <div class="tool-card-value">R$ 5,42</div>
                    <div class="tool-card-footer">
                        <span>Atualizado: 10 min atrás</span>
                        <div class="tool-card-trend negative">
                            <i class="fas fa-arrow-down"></i> 0.75%
                        </div>
                    </div>
                </div>
                
                <!-- Euro -->
                <div class="tool-card">
                    <div class="tool-card-header">
                        <h4 class="tool-card-title">Euro</h4>
                        <span class="tool-card-badge">EUR/BRL</span>
                    </div>
                    <div class="tool-card-value">R$ 5,89</div>
                    <div class="tool-card-footer">
                        <span>Atualizado: 10 min atrás</span>
                        <div class="tool-card-trend negative">
                            <i class="fas fa-arrow-down"></i> 0.42%
                        </div>
                    </div>
                </div>
                
                <!-- Petróleo Brent -->
                <div class="tool-card">
                    <div class="tool-card-header">
                        <h4 class="tool-card-title">Petróleo Brent</h4>
                        <span class="tool-card-badge">USD</span>
                    </div>
                    <div class="tool-card-value">$78.35</div>
                    <div class="tool-card-footer">
                        <span>Atualizado: 10 min atrás</span>
                        <div class="tool-card-trend positive">
                            <i class="fas fa-arrow-up"></i> 1.25%
                        </div>
                    </div>
                </div>
                
                <!-- Petróleo WTI -->
                <div class="tool-card">
                    <div class="tool-card-header">
                        <h4 class="tool-card-title">Petróleo WTI</h4>
                        <span class="tool-card-badge">USD</span>
                    </div>
                    <div class="tool-card-value">$74.82</div>
                    <div class="tool-card-footer">
                        <span>Atualizado: 10 min atrás</span>
                        <div class="tool-card-trend positive">
                            <i class="fas fa-arrow-up"></i> 1.18%
                        </div>
                    </div>
                </div>
                
                <!-- Petrobrás (PETR4) -->
                <div class="tool-card">
                    <div class="tool-card-header">
                        <h4 class="tool-card-title">Petrobrás</h4>
                        <span class="tool-card-badge">PETR4</span>
                    </div>
                    <div class="tool-card-value">R$ 38,25</div>
                    <div class="tool-card-footer">
                        <span>Atualizado: 10 min atrás</span>
                        <div class="tool-card-trend positive">
                            <i class="fas fa-arrow-up"></i> 0.87%
                        </div>
                    </div>
                </div>
                
                <!-- Reservas de Petróleo EUA -->
                <div class="tool-card">
                    <div class="tool-card-header">
                        <h4 class="tool-card-title">Reservas EUA</h4>
                        <span class="tool-card-badge">Milhões barris</span>
                    </div>
                    <div class="tool-card-value">428,5</div>
                    <div class="tool-card-footer">
                        <span>Atualizado: 1 dia atrás</span>
                        <div class="tool-card-trend negative">
                            <i class="fas fa-arrow-down"></i> 1.42%
                        </div>
                    </div>
                </div>
                
                <!-- Petróleo Arábia Saudita -->
                <div class="tool-card">
                    <div class="tool-card-header">
                        <h4 class="tool-card-title">Prod. Arábia Saudita</h4>
                        <span class="tool-card-badge">Milhões barris/dia</span>
                    </div>
                    <div class="tool-card-value">10,2</div>
                    <div class="tool-card-footer">
                        <span>Atualizado: 3 dias atrás</span>
                        <div class="tool-card-trend positive">
                            <i class="fas fa-arrow-up"></i> 0.59%
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Seção de gráfico de cotações -->
        <div class="tool-section">
            <h3 class="tool-section-title">
                <i class="fas fa-chart-line"></i> Histórico de Cotações
            </h3>
            <div class="chart-controls">
                <div class="btn-group">
                    <button class="btn-outline-shell active" data-period="7d">7 dias</button>
                    <button class="btn-outline-shell" data-period="1m">1 mês</button>
                    <button class="btn-outline-shell" data-period="3m">3 meses</button>
                    <button class="btn-outline-shell" data-period="6m">6 meses</button>
                    <button class="btn-outline-shell" data-period="1y">1 ano</button>
                </div>
                <div class="btn-group">
                    <button class="btn-outline-shell active" data-currency="usd">Dólar</button>
                    <button class="btn-outline-shell" data-currency="eur">Euro</button>
                    <button class="btn-outline-shell" data-currency="brent">Brent</button>
                    <button class="btn-outline-shell" data-currency="wti">WTI</button>
                    <button class="btn-outline-shell" data-currency="petr4">Petrobrás</button>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="currencyChart" width="100%" height="300"></canvas>
            </div>
        </div>
        
        <!-- Seção de outras cotações -->
        <div class="tool-section">
            <h3 class="tool-section-title">
                <i class="fas fa-globe"></i> Outras Cotações
            </h3>
            <div class="tool-table-container">
                <table class="tool-table">
                    <thead>
                        <tr>
                            <th>Moeda/Commodity</th>
                            <th>Valor</th>
                            <th>Variação</th>
                            <th>Última Atualização</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Libra Esterlina (GBP/BRL)</td>
                            <td>R$ 6,87</td>
                            <td class="negative">-0.32%</td>
                            <td>10 min atrás</td>
                        </tr>
                        <tr>
                            <td>Iene Japonês (JPY/BRL)</td>
                            <td>R$ 0,036</td>
                            <td class="negative">-0.15%</td>
                            <td>10 min atrás</td>
                        </tr>
                        <tr>
                            <td>Franco Suíço (CHF/BRL)</td>
                            <td>R$ 6,12</td>
                            <td class="negative">-0.28%</td>
                            <td>10 min atrás</td>
                        </tr>
                        <tr>
                            <td>Dólar Australiano (AUD/BRL)</td>
                            <td>R$ 3,58</td>
                            <td class="negative">-0.45%</td>
                            <td>10 min atrás</td>
                        </tr>
                        <tr>
                            <td>Dólar Canadense (CAD/BRL)</td>
                            <td>R$ 3,98</td>
                            <td class="negative">-0.38%</td>
                            <td>10 min atrás</td>
                        </tr>
                        <tr>
                            <td>Ouro (USD/onça)</td>
                            <td>$2.345,60</td>
                            <td class="positive">+0.75%</td>
                            <td>10 min atrás</td>
                        </tr>
                        <tr>
                            <td>Prata (USD/onça)</td>
                            <td>$27,85</td>
                            <td class="positive">+0.62%</td>
                            <td>10 min atrás</td>
                        </tr>
                        <tr>
                            <td>Gás Natural (USD/MMBtu)</td>
                            <td>$2,15</td>
                            <td class="negative">-1.25%</td>
                            <td>10 min atrás</td>
                        </tr>
                        <tr>
                            <td>Etanol (BRL/litro)</td>
                            <td>R$ 3,45</td>
                            <td class="positive">+0.88%</td>
                            <td>1 dia atrás</td>
                        </tr>
                        <tr>
                            <td>Milho (USD/bushel)</td>
                            <td>$4,28</td>
                            <td class="negative">-0.35%</td>
                            <td>1 dia atrás</td>
                        </tr>
                        <tr>
                            <td>Cobre (USD/libra)</td>
                            <td>$4,12</td>
                            <td class="positive">+1.25%</td>
                            <td>1 dia atrás</td>
                        </tr>
                        <tr>
                            <td>Alumínio (USD/tonelada)</td>
                            <td>$2.456,75</td>
                            <td class="positive">+0.65%</td>
                            <td>1 dia atrás</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Modal de Ajuda para Cotações -->
        <div class="modal" id="modal-cotacoes-help">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title">Guia de Uso - Cotações</h4>
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <h5>Como usar a ferramenta de Cotações</h5>
                        <p>Esta ferramenta exibe cotações atualizadas de moedas, commodities e ativos relevantes para o setor financeiro.</p>
                        
                        <h6>Recursos principais:</h6>
                        <ul>
                            <li><strong>Cotações Principais:</strong> Visualize de forma rápida os valores atualizados de moedas e commodities essenciais.</li>
                            <li><strong>Gráfico Histórico:</strong> Analise a evolução dos valores em diferentes períodos (7 dias a 1 ano).</li>
                            <li><strong>Tabela de Cotações:</strong> Acesse uma lista expandida de outros ativos e commodities.</li>
                        </ul>
                        
                        <h6>Commodities recomendadas para monitoramento:</h6>
                        <ul>
                            <li><strong>Petróleo (Brent e WTI):</strong> Impacto direto nos preços de combustíveis.</li>
                            <li><strong>Etanol:</strong> Alternativa à gasolina, afeta precificação de combustíveis.</li>
                            <li><strong>Gás Natural:</strong> Importante para regiões com frotas convertidas.</li>
                            <li><strong>Milho e Soja:</strong> Relacionados à produção de etanol e biodiesel.</li>
                            <li><strong>Metais (Alumínio, Cobre):</strong> Indicadores de atividade industrial.</li>
                        </ul>
                        
                        <p>Todas as cotações são atualizadas automaticamente a cada hora durante o horário comercial.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn-shell-yellow" data-dismiss="modal">Entendi</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Inicializa o gráfico de cotações
    setTimeout(() => {
        initCurrencyChart();
    }, 100);
    
    // Adiciona eventos aos botões de período
    const periodButtons = container.querySelectorAll('[data-period]');
    periodButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove a classe active de todos os botões
            periodButtons.forEach(btn => btn.classList.remove('active'));
            // Adiciona a classe active ao botão clicado
            this.classList.add('active');
            
            // Atualiza o gráfico com o novo período
            updateCurrencyChart(this.getAttribute('data-period'), container.querySelector('[data-currency].active').getAttribute('data-currency'));
        });
    });
    
    // Adiciona eventos aos botões de moeda
    const currencyButtons = container.querySelectorAll('[data-currency]');
    currencyButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove a classe active de todos os botões
            currencyButtons.forEach(btn => btn.classList.remove('active'));
            // Adiciona a classe active ao botão clicado
            this.classList.add('active');
            
            // Atualiza o gráfico com a nova moeda
            updateCurrencyChart(container.querySelector('[data-period].active').getAttribute('data-period'), this.getAttribute('data-currency'));
        });
    });
    
    // Adiciona evento ao botão de ajuda
    const btnCotacoesHelp = container.querySelector('#cotacoes-help');
    btnCotacoesHelp.addEventListener('click', function() {
        // Exibir o modal de ajuda
        const modal = container.querySelector('#modal-cotacoes-help');
        modal.style.display = 'block';
    });
    
    // Fechar o modal quando clicar no botão de fechar
    const closeButtons = container.querySelectorAll('[data-dismiss="modal"]');
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            modal.style.display = 'none';
        });
    });
    
    // Fechar o modal quando clicar fora dele
    window.addEventListener('click', function(event) {
        const modal = container.querySelector('#modal-cotacoes-help');
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
}

/**
 * Inicializa o gráfico de cotações
 */
function initCurrencyChart() {
    const ctx = document.getElementById('currencyChart');
    if (!ctx) return;
    
    // Dados para o gráfico (7 dias de Dólar)
    const labels = ['01/04', '02/04', '03/04', '04/04', '05/04', '06/04', '07/04'];
    const data = [5.35, 5.38, 5.41, 5.45, 5.43, 5.40, 5.42];
    
    // Cria o gráfico
    window.currencyChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Dólar (USD/BRL)',
                data: data,
                backgroundColor: 'rgba(253, 184, 19, 0.2)',
                borderColor: '#FDB813',
                borderWidth: 2,
                tension: 0.4,
                fill: true,
                pointBackgroundColor: '#FDB813',
                pointBorderColor: '#FDB813',
                pointRadius: 4,
                pointHoverRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        color: '#ccc',
                        font: {
                            family: "'Rajdhani', sans-serif",
                            size: 12
                        }
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: '#333',
                    titleColor: '#FDB813',
                    bodyColor: '#fff',
                    borderColor: '#555',
                    borderWidth: 1,
                    titleFont: {
                        family: "'Rajdhani', sans-serif",
                        size: 14,
                        weight: 'bold'
                    },
                    bodyFont: {
                        family: "'Rajdhani', sans-serif",
                        size: 13
                    },
                    padding: 10
                }
            },
            scales: {
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#aaa',
                        font: {
                            family: "'Rajdhani', sans-serif",
                            size: 12
                        }
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#aaa',
                        font: {
                            family: "'Rajdhani', sans-serif",
                            size: 12
                        },
                        callback: function(value) {
                            return 'R$ ' + value.toFixed(2);
                        }
                    }
                }
            }
        }
    });
}

/**
 * Atualiza o gráfico de cotações com novos dados
 * @param {string} period - Período selecionado (7d, 1m, 3m, 6m, 1y)
 * @param {string} currency - Moeda selecionada (usd, eur, brent, wti, petr4)
 */
function updateCurrencyChart(period, currency) {
    // Simulação temporária de dados, a ser substituída por chamada à API real
    // Para integração com API real, substitua esta seção por chamada fetch() às APIs como:
    // - https://www.alphavantage.co/ (para cotações de moedas e ações)
    // - https://www.quandl.com/ (para commodities e petróleo)
    // - https://eia.gov/opendata/ (para dados de reservas de petróleo)
    const data = {
        usd: {
            '7d': {
                labels: ['01/04', '02/04', '03/04', '04/04', '05/04', '06/04', '07/04'],
                data: [5.35, 5.38, 5.41, 5.45, 5.43, 5.40, 5.42],
                label: 'Dólar (USD/BRL)'
            },
            '1m': {
                labels: ['10/03', '15/03', '20/03', '25/03', '30/03', '05/04', '07/04'],
                data: [5.30, 5.32, 5.36, 5.39, 5.41, 5.38, 5.42],
                label: 'Dólar (USD/BRL)'
            },
            '3m': {
                labels: ['Jan', 'Fev', 'Mar', 'Abr'],
                data: [5.20, 5.28, 5.35, 5.42],
                label: 'Dólar (USD/BRL)'
            },
            '6m': {
                labels: ['Out', 'Nov', 'Dez', 'Jan', 'Fev', 'Mar', 'Abr'],
                data: [5.15, 5.18, 5.22, 5.20, 5.28, 5.35, 5.42],
                label: 'Dólar (USD/BRL)'
            },
            '1y': {
                labels: ['Abr/23', 'Jun/23', 'Ago/23', 'Out/23', 'Dez/23', 'Fev/24', 'Abr/24'],
                data: [5.05, 5.10, 5.12, 5.15, 5.22, 5.35, 5.42],
                label: 'Dólar (USD/BRL)'
            }
        },
        eur: {
            '7d': {
                labels: ['01/04', '02/04', '03/04', '04/04', '05/04', '06/04', '07/04'],
                data: [5.82, 5.85, 5.88, 5.92, 5.90, 5.87, 5.89],
                label: 'Euro (EUR/BRL)'
            },
            '1m': {
                labels: ['10/03', '15/03', '20/03', '25/03', '30/03', '05/04', '07/04'],
                data: [5.75, 5.78, 5.82, 5.85, 5.88, 5.86, 5.89],
                label: 'Euro (EUR/BRL)'
            },
            '3m': {
                labels: ['Jan', 'Fev', 'Mar', 'Abr'],
                data: [5.65, 5.72, 5.80, 5.89],
                label: 'Euro (EUR/BRL)'
            },
            '6m': {
                labels: ['Out', 'Nov', 'Dez', 'Jan', 'Fev', 'Mar', 'Abr'],
                data: [5.60, 5.63, 5.68, 5.65, 5.72, 5.80, 5.89],
                label: 'Euro (EUR/BRL)'
            },
            '1y': {
                labels: ['Abr/23', 'Jun/23', 'Ago/23', 'Out/23', 'Dez/23', 'Fev/24', 'Abr/24'],
                data: [5.50, 5.55, 5.58, 5.60, 5.68, 5.80, 5.89],
                label: 'Euro (EUR/BRL)'
            }
        },
        brent: {
            '7d': {
                labels: ['01/04', '02/04', '03/04', '04/04', '05/04', '06/04', '07/04'],
                data: [76.80, 77.25, 77.85, 78.10, 77.90, 78.05, 78.35],
                label: 'Petróleo Brent (USD)'
            },
            '1m': {
                labels: ['10/03', '15/03', '20/03', '25/03', '30/03', '05/04', '07/04'],
                data: [75.50, 76.20, 76.85, 77.30, 77.65, 78.10, 78.35],
                label: 'Petróleo Brent (USD)'
            },
            '3m': {
                labels: ['Jan', 'Fev', 'Mar', 'Abr'],
                data: [72.80, 74.50, 76.90, 78.35],
                label: 'Petróleo Brent (USD)'
            },
            '6m': {
                labels: ['Out', 'Nov', 'Dez', 'Jan', 'Fev', 'Mar', 'Abr'],
                data: [80.20, 78.50, 75.30, 72.80, 74.50, 76.90, 78.35],
                label: 'Petróleo Brent (USD)'
            },
            '1y': {
                labels: ['Abr/23', 'Jun/23', 'Ago/23', 'Out/23', 'Dez/23', 'Fev/24', 'Abr/24'],
                data: [82.50, 84.20, 83.10, 80.20, 75.30, 74.50, 78.35],
                label: 'Petróleo Brent (USD)'
            }
        },
        wti: {
            '7d': {
                labels: ['01/04', '02/04', '03/04', '04/04', '05/04', '06/04', '07/04'],
                data: [73.20, 73.65, 74.25, 74.50, 74.30, 74.45, 74.82],
                label: 'Petróleo WTI (USD)'
            },
            '1m': {
                labels: ['10/03', '15/03', '20/03', '25/03', '30/03', '05/04', '07/04'],
                data: [71.90, 72.60, 73.25, 73.70, 74.05, 74.50, 74.82],
                label: 'Petróleo WTI (USD)'
            },
            '3m': {
                labels: ['Jan', 'Fev', 'Mar', 'Abr'],
                data: [69.20, 70.90, 73.30, 74.82],
                label: 'Petróleo WTI (USD)'
            },
            '6m': {
                labels: ['Out', 'Nov', 'Dez', 'Jan', 'Fev', 'Mar', 'Abr'],
                data: [76.60, 74.90, 71.70, 69.20, 70.90, 73.30, 74.82],
                label: 'Petróleo WTI (USD)'
            },
            '1y': {
                labels: ['Abr/23', 'Jun/23', 'Ago/23', 'Out/23', 'Dez/23', 'Fev/24', 'Abr/24'],
                data: [78.90, 80.60, 79.50, 76.60, 71.70, 70.90, 74.82],
                label: 'Petróleo WTI (USD)'
            }
        },
        petr4: {
            '7d': {
                labels: ['01/04', '02/04', '03/04', '04/04', '05/04', '06/04', '07/04'],
                data: [37.25, 37.60, 37.90, 38.15, 38.05, 38.10, 38.25],
                label: 'Petrobrás (PETR4)'
            },
            '1m': {
                labels: ['10/03', '15/03', '20/03', '25/03', '30/03', '05/04', '07/04'],
                data: [36.40, 36.85, 37.20, 37.55, 37.80, 38.10, 38.25],
                label: 'Petrobrás (PETR4)'
            },
            '3m': {
                labels: ['Jan', 'Fev', 'Mar', 'Abr'],
                data: [35.20, 35.90, 37.40, 38.25],
                label: 'Petrobrás (PETR4)'
            },
            '6m': {
                labels: ['Out', 'Nov', 'Dez', 'Jan', 'Fev', 'Mar', 'Abr'],
                data: [33.80, 34.50, 34.95, 35.20, 35.90, 37.40, 38.25],
                label: 'Petrobrás (PETR4)'
            },
            '1y': {
                labels: ['Abr/23', 'Jun/23', 'Ago/23', 'Out/23', 'Dez/23', 'Fev/24', 'Abr/24'],
                data: [29.50, 31.20, 32.40, 33.80, 34.95, 36.30, 38.25],
                label: 'Petrobrás (PETR4)'
            }
        }
    };
    
    // Obter os dados para o período e moeda selecionados
    const chartData = data[currency][period];
    
    // Atualiza o gráfico
    if (window.currencyChart) {
        window.currencyChart.data.labels = chartData.labels;
        window.currencyChart.data.datasets[0].data = chartData.data;
        window.currencyChart.data.datasets[0].label = chartData.label;
        
        // Define cores diferentes para cada moeda
        if (currency === 'usd') {
            window.currencyChart.data.datasets[0].borderColor = '#FDB813';
            window.currencyChart.data.datasets[0].backgroundColor = 'rgba(253, 184, 19, 0.2)';
            window.currencyChart.data.datasets[0].pointBackgroundColor = '#FDB813';
            window.currencyChart.data.datasets[0].pointBorderColor = '#FDB813';
        } else if (currency === 'eur') {
            window.currencyChart.data.datasets[0].borderColor = '#0275d8';
            window.currencyChart.data.datasets[0].backgroundColor = 'rgba(2, 117, 216, 0.2)';
            window.currencyChart.data.datasets[0].pointBackgroundColor = '#0275d8';
            window.currencyChart.data.datasets[0].pointBorderColor = '#0275d8';
        } else if (currency === 'brent') {
            window.currencyChart.data.datasets[0].borderColor = '#ED1C24';
            window.currencyChart.data.datasets[0].backgroundColor = 'rgba(237, 28, 36, 0.2)';
            window.currencyChart.data.datasets[0].pointBackgroundColor = '#ED1C24';
            window.currencyChart.data.datasets[0].pointBorderColor = '#ED1C24';
        } else if (currency === 'wti') {
            window.currencyChart.data.datasets[0].borderColor = '#28a745';
            window.currencyChart.data.datasets[0].backgroundColor = 'rgba(40, 167, 69, 0.2)';
            window.currencyChart.data.datasets[0].pointBackgroundColor = '#28a745';
            window.currencyChart.data.datasets[0].pointBorderColor = '#28a745';
        } else if (currency === 'petr4') {
            window.currencyChart.data.datasets[0].borderColor = '#ED1C24';
            window.currencyChart.data.datasets[0].backgroundColor = 'rgba(237, 28, 36, 0.2)';
            window.currencyChart.data.datasets[0].pointBackgroundColor = '#ED1C24';
            window.currencyChart.data.datasets[0].pointBorderColor = '#ED1C24';
        }
        
        // Atualiza o formato do eixo Y dependendo da moeda
        if (currency === 'usd' || currency === 'eur') {
            window.currencyChart.options.scales.y.ticks.callback = function(value) {
                return 'R$ ' + value.toFixed(2);
            };
        } else if (currency === 'petr4') {
            window.currencyChart.options.scales.y.ticks.callback = function(value) {
                return 'R$ ' + value.toFixed(2);
            };
        } else {
            window.currencyChart.options.scales.y.ticks.callback = function(value) {
                return '$ ' + value.toFixed(2);
            };
        }
        
        window.currencyChart.update();
    }
}

/**
 * Função para buscar dados de API real (a ser implementada com a chave API)
 * @param {string} endpoint - Endpoint da API
 * @param {Object} params - Parâmetros da requisição
 * @returns {Promise} - Promise com os dados da API
 */
async function fetchAPIData(endpoint, params) {
    // Configuração da API - adicionar aqui sua chave de API quando disponível
    const API_KEY = '';
    
    // Construir URL de requisição
    const queryParams = new URLSearchParams({
        ...params,
        apikey: API_KEY
    });
    
    const url = `${endpoint}?${queryParams}`;
    
    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Erro na requisição: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error('Erro ao buscar dados da API:', error);
        return null;
    }
}
