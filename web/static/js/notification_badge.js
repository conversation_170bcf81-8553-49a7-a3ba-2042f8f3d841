/**
 * <PERSON><PERSON><PERSON>lo para gerenciar o badge de notificações
 */
const NotificationBadge = (function() {
    // Elementos do DOM
    let badgeElement;
    let dropdownElement;
    let dropdownBodyElement;
    let markAllReadButton;

    // Estado
    let notifications = [];
    let unreadCount = 0;
    let isDropdownOpen = false;
    let pollingInterval;

    /**
     * Inicializa o módulo
     */
    function init() {
        // Buscar elementos do DOM
        badgeElement = document.getElementById('notificationBadge');
        dropdownElement = document.getElementById('notificationDropdown');
        dropdownBodyElement = document.getElementById('notificationDropdownBody');
        markAllReadButton = document.getElementById('markAllNotificationsRead');

        if (!badgeElement) return;

        // Configurar eventos
        badgeElement.addEventListener('click', toggleDropdown);

        if (markAllReadButton) {
            markAllReadButton.addEventListener('click', markAllAsRead);
        }

        // Fechar dropdown ao clicar fora
        document.addEventListener('click', function(event) {
            if (isDropdownOpen && !badgeElement.contains(event.target) && !dropdownElement.contains(event.target)) {
                closeDropdown();
            }
        });

        // Carregar notificações iniciais
        loadNotifications();

        // Configurar polling para verificar novas notificações
        startPolling();
    }

    /**
     * Carrega as notificações do usuário
     */
    function loadNotifications() {
        const userID = getUserID();
        if (!userID) return;

        fetch(`/api/notifications/me`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erro ao carregar notificações');
                }
                return response.json();
            })
            .then(data => {
                notifications = data;
                unreadCount = notifications.filter(n => !n.read).length;
                updateBadge();
                if (isDropdownOpen) {
                    renderNotifications();
                }
            })
            .catch(error => {
                console.error('Erro ao carregar notificações:', error);
            });
    }

    /**
     * Obtém o ID do usuário atual
     */
    function getUserID() {
        const userIDElement = document.getElementById('currentUserID');
        return userIDElement ? userIDElement.value : null;
    }

    /**
     * Atualiza o badge com o número de notificações não lidas
     */
    function updateBadge() {
        const badgeCountElement = badgeElement.querySelector('.badge');

        if (unreadCount > 0) {
            badgeCountElement.textContent = unreadCount > 99 ? '99+' : unreadCount;
            badgeCountElement.classList.remove('hidden');
        } else {
            badgeCountElement.classList.add('hidden');
        }
    }

    /**
     * Renderiza as notificações no dropdown
     */
    function renderNotifications() {
        if (!dropdownBodyElement) return;

        // Mostrar indicador de carregamento apenas se o dropdown estiver aberto
        if (isDropdownOpen) {
            dropdownBodyElement.innerHTML = `
                <div class="empty-notifications" style="display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 20px;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 1.5rem; margin-bottom: 10px;"></i>
                    <p style="margin: 0;">Carregando notificações...</p>
                </div>
            `;
        }

        if (notifications.length === 0) {
            dropdownBodyElement.innerHTML = `
                <div class="empty-notifications">
                    <i class="fas fa-bell-slash"></i>
                    <p>Você não possui notificações</p>
                </div>
            `;
            return;
        }

        let html = '';

        // Ordenar notificações por data (mais recentes primeiro)
        const sortedNotifications = [...notifications].sort((a, b) =>
            new Date(b.created_at) - new Date(a.created_at)
        );

        // Limitar a 10 notificações mais recentes
        const recentNotifications = sortedNotifications.slice(0, 10);

        recentNotifications.forEach(notification => {
            const isUnread = !notification.read;
            const icon = getNotificationIcon(notification);
            const timeAgo = formatTimeAgo(new Date(notification.created_at));

            html += `
                <div class="notification-item ${isUnread ? 'unread' : ''}" data-id="${notification.id}" onclick="NotificationBadge.viewNotification(${notification.id})">
                    <div class="notification-item-content">
                        <div class="notification-item-icon">
                            <i class="${icon}"></i>
                        </div>
                        <div class="notification-item-text">
                            <div class="notification-item-title">${notification.title}</div>
                            <div class="notification-item-message">${notification.body}</div>
                            <div class="notification-item-time">${timeAgo}</div>
                        </div>
                    </div>
                </div>
            `;
        });

        dropdownBodyElement.innerHTML = html;
    }

    /**
     * Retorna o ícone apropriado para o tipo de notificação
     */
    function getNotificationIcon(notification) {
        // Verificar se é uma notificação de transferência
        if (notification.url && notification.url.includes('transfer=')) {
            return 'fas fa-exchange-alt';
        }

        // Verificar se é uma notificação de ordem
        if (notification.order_id) {
            return 'fas fa-clipboard-list';
        }

        // Ícone padrão
        return 'fas fa-bell';
    }

    /**
     * Formata o tempo relativo (ex: "há 5 minutos")
     */
    function formatTimeAgo(date) {
        const now = new Date();
        const diffMs = now - date;
        const diffSec = Math.floor(diffMs / 1000);
        const diffMin = Math.floor(diffSec / 60);
        const diffHour = Math.floor(diffMin / 60);
        const diffDay = Math.floor(diffHour / 24);

        if (diffSec < 60) {
            return 'agora mesmo';
        } else if (diffMin < 60) {
            return `há ${diffMin} ${diffMin === 1 ? 'minuto' : 'minutos'}`;
        } else if (diffHour < 24) {
            return `há ${diffHour} ${diffHour === 1 ? 'hora' : 'horas'}`;
        } else if (diffDay < 30) {
            return `há ${diffDay} ${diffDay === 1 ? 'dia' : 'dias'}`;
        } else {
            return date.toLocaleDateString();
        }
    }

    /**
     * Abre/fecha o dropdown de notificações
     */
    function toggleDropdown() {
        if (isDropdownOpen) {
            closeDropdown();
        } else {
            openDropdown();
        }
    }

    /**
     * Abre o dropdown de notificações
     */
    function openDropdown() {
        if (!dropdownElement) return;

        // Primeiro adiciona a classe show para exibir o elemento
        dropdownElement.classList.add('show');
        // Define a visibilidade como visível
        dropdownElement.style.visibility = 'visible';
        // Marca como aberto
        isDropdownOpen = true;
        // Agora renderiza as notificações
        renderNotifications();
    }

    /**
     * Fecha o dropdown de notificações
     */
    function closeDropdown() {
        if (!dropdownElement) return;

        // Remove a classe show
        dropdownElement.classList.remove('show');
        // Define a visibilidade como escondida
        dropdownElement.style.visibility = 'hidden';
        // Limpa o conteúdo para evitar vazamentos visuais
        dropdownBodyElement.innerHTML = '';
        // Marca como fechado
        isDropdownOpen = false;
    }

    /**
     * Marca todas as notificações como lidas
     */
    function markAllAsRead() {
        fetch('/api/notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Erro ao marcar notificações como lidas');
            }
            return response.json();
        })
        .then(() => {
            // Atualizar estado local
            notifications.forEach(notification => {
                notification.read = true;
            });
            unreadCount = 0;
            updateBadge();
            renderNotifications();
        })
        .catch(error => {
            console.error('Erro ao marcar notificações como lidas:', error);
        });
    }

    /**
     * Visualiza uma notificação específica
     */
    function viewNotification(id) {
        // Buscar a notificação
        const notification = notifications.find(n => n.id === id);
        if (!notification) return;

        // Marcar como lida
        fetch(`/api/notifications/${id}/read`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Erro ao marcar notificação como lida');
            }

            // Atualizar estado local
            notification.read = true;
            unreadCount = notifications.filter(n => !n.read).length;
            updateBadge();

            // Redirecionar para a URL da notificação, se houver
            if (notification.url) {
                window.location.href = notification.url;
            }

            // Fechar dropdown
            closeDropdown();
        })
        .catch(error => {
            console.error('Erro ao marcar notificação como lida:', error);
        });
    }

    /**
     * Inicia o polling para verificar novas notificações
     */
    function startPolling() {
        // Verificar novas notificações a cada 30 segundos
        pollingInterval = setInterval(loadNotifications, 30000);
    }

    /**
     * Para o polling de notificações
     */
    function stopPolling() {
        if (pollingInterval) {
            clearInterval(pollingInterval);
        }
    }

    // API pública
    return {
        init,
        loadNotifications,
        viewNotification,
        markAllAsRead
    };
})();

// Inicializar o módulo quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', NotificationBadge.init);
