// Script para registrar o Service Worker e habilitar funcionalidades de PWA
if ('serviceWorker' in navigator) {
  window.addEventListener('load', function() {
    navigator.serviceWorker.register('/static/js/service-worker.js')
      .then(function(registration) {
        console.log('Service Worker registrado com sucesso:', registration.scope);
        
        // Verificar se há atualizações de Service Worker
        registration.onupdatefound = function() {
          const installingWorker = registration.installing;
          installingWorker.onstatechange = function() {
            if (installingWorker.state === 'installed') {
              if (navigator.serviceWorker.controller) {
                console.log('Nova versão disponível, recarregue a página para atualizar.');
                // Pode mostrar uma notificação para o usuário atualizar a aplicação
                showUpdateNotification();
              } else {
                console.log('Conteúdo armazenado para uso offline.');
              }
            }
          };
        };
      })
      .catch(function(error) {
        console.log('Erro no registro do Service Worker:', error);
      });
      
    // Registrar para receber notificações push
    registerForPushNotifications();
  });
}

// Função para solicitar permissão de notificação e registrar no servidor
function registerForPushNotifications() {
  // Verificar se o navegador suporta notificações
  if (!('Notification' in window)) {
    console.log('Este navegador não suporta notificações.');
    return;
  }
  
  // Verificar se já temos permissão
  if (Notification.permission === 'granted') {
    // Já temos permissão, podemos registrar para notificações
    subscribeUserToPush();
  } else if (Notification.permission !== 'denied') {
    // Pedir permissão
    Notification.requestPermission().then(function(permission) {
      if (permission === 'granted') {
        subscribeUserToPush();
      }
    });
  }
}

// Registrar o dispositivo no servidor para receber notificações
function subscribeUserToPush() {
  navigator.serviceWorker.ready.then(function(registration) {
    // Verificar se o navegador suporta push
    if (!('pushManager' in registration)) {
      console.log('Push não é suportado pelo seu navegador.');
      return;
    }
    
    const publicVapidKey = 'BF8KwBQI1S-IbdcC3qZ2fVXw3PPXX0nQQEcTvabPeCNdlB9Ns_XBcTqVUgYmzIKUfnpSbTTjGhKpL5khKTB3i5U';
    
    registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: urlBase64ToUint8Array(publicVapidKey)
    })
    .then(function(subscription) {
      // Enviar a subscription para o servidor
      console.log('Usuário inscrito para receber notificações push');
      // Aqui você deve enviar a subscription para o servidor, exemplo:
      // return fetch('/api/subscriptions', {
      //   method: 'POST',
      //   body: JSON.stringify(subscription),
      //   headers: {
      //     'Content-Type': 'application/json'
      //   }
      // });
    })
    .catch(function(error) {
      console.error('Erro ao se inscrever para notificações push:', error);
    });
  });
}

// Converter a chave VAPID de base64 para Uint8Array
function urlBase64ToUint8Array(base64String) {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/\-/g, '+')
    .replace(/_/g, '/');
  
  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);
  
  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

// Mostrar notificação de atualização
function showUpdateNotification() {
  const notification = document.createElement('div');
  notification.className = 'update-notification';
  notification.innerHTML = `
    <div class="update-content">
      <p>Nova versão disponível!</p>
      <button class="update-button">Atualizar agora</button>
    </div>
  `;
  
  document.body.appendChild(notification);
  
  // Estilizar a notificação
  const style = document.createElement('style');
  style.textContent = `
    .update-notification {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background-color: #FDB813;
      color: #5d2906;
      border-radius: 4px;
      padding: 10px 15px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      z-index: 1000;
      font-family: 'Arial', sans-serif;
    }
    
    .update-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    
    .update-content p {
      margin: 0 15px 0 0;
      font-weight: bold;
    }
    
    .update-button {
      background-color: #5d2906;
      color: #FDB813;
      border: none;
      border-radius: 4px;
      padding: 5px 10px;
      cursor: pointer;
      font-weight: bold;
    }
    
    .update-button:hover {
      background-color: #7a3708;
    }
  `;
  document.head.appendChild(style);
  
  // Adicionar evento de clique ao botão
  const updateButton = notification.querySelector('.update-button');
  updateButton.addEventListener('click', function() {
    // Recarregar a página para atualizar o service worker
    window.location.reload();
  });
}