// Variáveis Globais
let costsModalInstance = null;
let interactionModalInstance = null;
let assignProviderModalInstance = null;
let canEditCosts = false;

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando página de detalhes da ordem...');
    initializeModals();
    initializeForms();
    initializeTooltips();
    addAnimationEffects();
});

// Funções de Inicialização
function initializeModals() {
    const costsModalElement = document.getElementById('costsModal');
    if (costsModalElement) {
        costsModalInstance = new bootstrap.Modal(costsModalElement);
    }

    const interactionModalElement = document.getElementById('interactionModal');
    if (interactionModalElement) {
        interactionModalInstance = new bootstrap.Modal(interactionModalElement);
    }

    const assignProviderModalElement = document.getElementById('assignProviderModal');
    if (assignProviderModalElement) {
        assignProviderModalInstance = new bootstrap.Modal(assignProviderModalElement);
    }
}

function initializeForms() {
    const addCostForm = document.getElementById('addCostForm');
    if (addCostForm) {
        addCostForm.addEventListener('submit', handleAddCostSubmit);
    }

    const addInteractionForm = document.getElementById('addInteractionForm');
    if (addInteractionForm) {
        addInteractionForm.addEventListener('submit', handleAddInteractionSubmit);
    }

    const assignProviderForm = document.getElementById('assignProviderForm');
    if (assignProviderForm) {
        assignProviderForm.addEventListener('submit', handleAssignProviderSubmit);
    }
}

function initializeTooltips() {
    // Inicializa tooltips do Bootstrap
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function addAnimationEffects() {
    // Adiciona efeitos de animação aos cards
    const cards = document.querySelectorAll('.detail-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 * index);
    });
}

// Funções de Modal de Custos
async function viewCostsModal(event) {
    const orderId = event.currentTarget.dataset.orderId;
    if (!orderId) return console.error("ID da ordem não encontrado");

    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de visualizar custos da ordem #18 que é inválida/hardcoded');
        showToast('Ordem #18 não está disponível. Por favor, selecione outra ordem.', 'warning');
        return;
    }

    canEditCosts = false;
    setupCostsModal(orderId);
    await loadCostsForModal(orderId);
}

async function openCostsModal(event) {
    const orderId = event.currentTarget.dataset.orderId;
    if (!orderId) return console.error("ID da ordem não encontrado");

    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de editar custos da ordem #18 que é inválida/hardcoded');
        showToast('Ordem #18 não está disponível. Por favor, selecione outra ordem.', 'warning');
        return;
    }

    if (!canUserEditCosts()) {
        showToast("Você não tem permissão para editar custos nesta ordem ou neste status.", "warning");
        return;
    }

    canEditCosts = true;
    setupCostsModal(orderId);
    await loadCostsForModal(orderId);
}

function setupCostsModal(orderId) {
    document.getElementById('costsModalOrderId').textContent = `#${orderId}`;
    document.getElementById('addCostFormContainer').style.display = canEditCosts ? 'block' : 'none';
    document.getElementById('costsModalTotal').textContent = 'Calculando...';
    document.getElementById('costItemsList').innerHTML = '<p class="text-muted">Carregando custos...</p>';

    if (costsModalInstance) {
        costsModalInstance.show();
    }
}

function canUserEditCosts() {
    return CURRENT_USER_ROLE === 'provider' &&
           CURRENT_USER_ID === ASSIGNED_PROVIDER_ID &&
           (CURRENT_ORDER_STATUS === 'in_progress' || CURRENT_ORDER_STATUS === 'revision_needed');
}

// Funções de Modal de Interação
function openInteractionModal(event) {
    const orderId = event.currentTarget.dataset.orderId;
    if (!orderId) return console.error("ID da ordem não encontrado");

    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de adicionar interação à ordem #18 que é inválida/hardcoded');
        showToast('Ordem #18 não está disponível. Por favor, selecione outra ordem.', 'warning');
        return;
    }

    if (!canUserInteract()) {
        showToast('Não é possível adicionar interações a ordens pagas ou canceladas.', 'warning');
        return;
    }

    setupInteractionModal(orderId);
}

function setupInteractionModal(orderId) {
    document.getElementById('interactionModalOrderId').textContent = `#${orderId}`;
    document.getElementById('interactionOrderId').value = orderId;
    document.getElementById('interactionMessage').value = '';

    if (interactionModalInstance) {
        interactionModalInstance.show();
    }
}

function canUserInteract() {
    return CURRENT_ORDER_STATUS !== 'paid' && CURRENT_ORDER_STATUS !== 'cancelled';
}

// Funções de Modal de Atribuição
function openAssignModal(event) {
    const orderId = event.currentTarget.dataset.orderId;
    if (!orderId) return console.error("ID da ordem não encontrado");

    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de atribuir prestador à ordem #18 que é inválida/hardcoded');
        showToast('Ordem #18 não está disponível. Por favor, selecione outra ordem.', 'warning');
        return;
    }

    if (!canAssignProvider()) {
        showToast('Esta ordem não está no status correto para atribuição.', 'warning');
        return;
    }

    setupAssignModal(orderId);
    loadProviders();
}

function setupAssignModal(orderId) {
    document.getElementById('assignProviderModalOrderId').textContent = `#${orderId}`;
    document.getElementById('assignProviderOrderId').value = orderId;
    const providerSelect = document.getElementById('providerSelect');
    providerSelect.innerHTML = '<option value="" selected disabled>Carregando prestadores...</option>';
    providerSelect.disabled = true;
}

function canAssignProvider() {
    return CURRENT_ORDER_STATUS === 'pending' && ASSIGNED_PROVIDER_ID === 0;
}

async function loadProviders() {
    try {
        const response = await fetch('/api/providers');
        if (!response.ok) throw new Error(`Erro HTTP ${response.status}`);

        const providers = await response.json();
        updateProviderSelect(providers);
    } catch (error) {
        console.error("Erro ao carregar prestadores:", error);
        showErrorInProviderSelect(error);
    }
}

function updateProviderSelect(providers) {
    const providerSelect = document.getElementById('providerSelect');

    if (!providers || providers.length === 0) {
        providerSelect.innerHTML = '<option value="" selected disabled>Nenhum prestador disponível</option>';
        providerSelect.disabled = true;
        return;
    }

    providerSelect.innerHTML = '<option value="" selected disabled>Selecione um prestador</option>';
    providers.forEach(provider => {
        const option = document.createElement('option');
        option.value = provider.id;
        option.textContent = provider.name;
        providerSelect.appendChild(option);
    });

    providerSelect.disabled = false;
}

function showErrorInProviderSelect(error) {
    const providerSelect = document.getElementById('providerSelect');
    providerSelect.innerHTML = '<option value="" selected disabled>Erro ao carregar prestadores</option>';
    providerSelect.disabled = true;
    showToast(`Erro ao carregar prestadores: ${error.message}`, 'error');
}

// Funções de API
async function loadCostsForModal(orderId) {
    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de carregar custos da ordem #18 que é inválida/hardcoded');
        showErrorInCostsDisplay(new Error('Ordem #18 não está disponível. Por favor, selecione outra ordem.'));
        return;
    }

    try {
        const response = await fetch(`/api/orders/${orderId}/costs`);
        if (!response.ok) throw new Error(`Erro HTTP ${response.status}`);

        const costs = await response.json();
        updateCostsDisplay(costs);
    } catch (error) {
        console.error("Erro ao carregar custos:", error);
        showErrorInCostsDisplay(error);
    }
}

function updateCostsDisplay(costs) {
    const listElement = document.getElementById('costItemsList');
    const totalElement = document.getElementById('costsModalTotal');
    const orderTotalCostDisplay = document.getElementById('orderTotalCostDisplay');

    if (!costs || costs.length === 0) {
        listElement.innerHTML = '<p class="text-muted">Nenhum custo registrado.</p>';
        updateTotalDisplay(0, totalElement, orderTotalCostDisplay);
        return;
    }

    const table = createCostsTable(costs);
    listElement.innerHTML = '';
    listElement.appendChild(table);

    const totalCost = calculateTotalCost(costs);
    updateTotalDisplay(totalCost, totalElement, orderTotalCostDisplay);
}

function createCostsTable(costs) {
    const table = document.createElement('table');
    table.className = 'table table-sm table-dark table-striped table-hover';

    const thead = table.createTHead();
    const headerRow = thead.insertRow();
    headerRow.innerHTML = `
        <th>Descrição</th>
        <th class="text-end">Qtd.</th>
        <th class="text-end">Vlr. Unit.</th>
        <th class="text-end">Subtotal</th>
        ${canEditCosts ? '<th>Ação</th>' : ''}
    `;

    const tbody = table.createTBody();
    costs.forEach(cost => {
        const row = tbody.insertRow();
        addCostRowCells(row, cost);
    });

    return table;
}

function addCostRowCells(row, cost) {
    const quantity = Number(cost.quantity) || 0;
    const unitPrice = Number(cost.unit_price) || 0;
    const subtotal = Number(cost.total_item_cost) || (quantity * unitPrice);

    row.insertCell().textContent = cost.description;

    const qtyCell = row.insertCell();
    qtyCell.className = 'text-end';
    qtyCell.textContent = quantity;

    const unitCell = row.insertCell();
    unitCell.className = 'text-end';
    unitCell.textContent = formatCurrencyJS(unitPrice);

    const subtotalCell = row.insertCell();
    subtotalCell.className = 'text-end';
    subtotalCell.textContent = formatCurrencyJS(subtotal);

    if (canEditCosts) {
        const actionCell = row.insertCell();
        const deleteButton = createDeleteButton(cost.id);
        actionCell.appendChild(deleteButton);
    }
}

function createDeleteButton(costId) {
    const button = document.createElement('button');
    button.className = 'btn btn-outline-danger btn-sm p-1';
    button.innerHTML = '<i class="fas fa-trash-alt"></i>';
    button.onclick = () => deleteCostItem(CURRENT_ORDER_ID, costId);
    return button;
}

async function deleteCostItem(orderId, costId) {
    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === 18 || orderId === "18") {
        console.error('Tentativa de excluir custo da ordem #18 que é inválida/hardcoded');
        showToast('Ordem #18 não está disponível. Por favor, selecione outra ordem.', 'warning');
        return;
    }

    if (!confirm('Tem certeza que deseja excluir este item de custo?')) {
        return;
    }

    try {
        const response = await fetch(`/api/orders/${orderId}/costs/${costId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
        }

        // Recarregar custos
        await loadCostsForModal(orderId);
        showToast('Item de custo removido com sucesso!', 'success');
    } catch (error) {
        console.error('Erro ao remover item de custo:', error);
        showToast(`Erro ao remover item de custo: ${error.message}`, 'error');
    }
}

function calculateTotalCost(costs) {
    return costs.reduce((total, cost) => {
        const quantity = Number(cost.quantity) || 0;
        const unitPrice = Number(cost.unit_price) || 0;
        return total + (Number(cost.total_item_cost) || (quantity * unitPrice));
    }, 0);
}

function updateTotalDisplay(total, totalElement, orderTotalCostDisplay) {
    const formattedTotal = formatCurrencyJS(total);
    totalElement.textContent = formattedTotal;
    if (orderTotalCostDisplay) {
        orderTotalCostDisplay.textContent = formattedTotal;
    }
}

function showErrorInCostsDisplay(error) {
    const listElement = document.getElementById('costItemsList');
    const totalElement = document.getElementById('costsModalTotal');
    const orderTotalCostDisplay = document.getElementById('orderTotalCostDisplay');

    listElement.innerHTML = `<p class="text-danger">Erro ao carregar custos: ${escapeHTML(error.message)}. Tente novamente.</p>`;
    totalElement.textContent = 'Erro';
    if (orderTotalCostDisplay) {
        orderTotalCostDisplay.textContent = 'Erro';
    }
}

// Funções de Manipulação de Formulários
async function handleAddCostSubmit(event) {
    event.preventDefault();

    const description = document.getElementById('costDescription').value;
    const quantity = parseFloat(document.getElementById('costQuantity').value);
    const unitPrice = parseFloat(document.getElementById('costUnitPrice').value);

    if (!description || isNaN(quantity) || isNaN(unitPrice)) {
        showToast('Por favor, preencha todos os campos corretamente.', 'warning');
        return;
    }

    try {
        const response = await fetch(`/api/orders/${CURRENT_ORDER_ID}/costs`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                description: description,
                quantity: quantity,
                unit_price: unitPrice
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
        }

        // Limpar formulário
        document.getElementById('costDescription').value = '';
        document.getElementById('costQuantity').value = '1';
        document.getElementById('costUnitPrice').value = '';

        // Recarregar custos
        await loadCostsForModal(CURRENT_ORDER_ID);
        showToast('Custo adicionado com sucesso!', 'success');
    } catch (error) {
        console.error('Erro ao adicionar custo:', error);
        showToast(`Erro ao adicionar custo: ${error.message}`, 'error');
    }
}

async function handleAddInteractionSubmit(event) {
    event.preventDefault();

    const message = document.getElementById('interactionMessage').value;

    if (!message) {
        showToast('Por favor, digite uma mensagem.', 'warning');
        return;
    }

    try {
        const response = await fetch(`/api/orders/${CURRENT_ORDER_ID}/interactions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
        }

        // Fechar modal e recarregar página
        if (interactionModalInstance) {
            interactionModalInstance.hide();
        }

        showToast('Interação adicionada com sucesso!', 'success');
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    } catch (error) {
        console.error('Erro ao adicionar interação:', error);
        showToast(`Erro ao adicionar interação: ${error.message}`, 'error');
    }
}

async function handleAssignProviderSubmit(event) {
    event.preventDefault();

    const providerId = document.getElementById('providerSelect').value;

    if (!providerId) {
        showToast('Por favor, selecione um prestador.', 'warning');
        return;
    }

    try {
        const response = await fetch(`/api/orders/${CURRENT_ORDER_ID}/assign`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                provider_id: parseInt(providerId)
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
        }

        // Fechar modal e recarregar página
        if (assignProviderModalInstance) {
            assignProviderModalInstance.hide();
        }

        showToast('Prestador atribuído com sucesso!', 'success');
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    } catch (error) {
        console.error('Erro ao atribuir prestador:', error);
        showToast(`Erro ao atribuir prestador: ${error.message}`, 'error');
    }
}

// Funções de Ações da Ordem
async function updateOrderStatusHandler(event) {
    const button = event.currentTarget;
    const orderId = button.dataset.orderId;
    const newStatus = button.dataset.newStatus;

    if (!orderId || !newStatus) {
        showToast('Dados incompletos para atualização de status.', 'error');
        return;
    }

    // Verificar se o ID é 18 e bloqueá-lo
    if (orderId === "18" || parseInt(orderId) === 18) {
        console.error('Tentativa de atualizar status da ordem #18 que é inválida/hardcoded');
        showToast('Ordem #18 não está disponível. Por favor, selecione outra ordem.', 'warning');
        return;
    }

    try {
        const response = await fetch(`/api/orders/${orderId}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: newStatus,
                reason: ''
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
        }

        showToast(`Status atualizado para ${formatStatusJS(newStatus)}!`, 'success');
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    } catch (error) {
        console.error('Erro ao atualizar status:', error);
        showToast(`Erro ao atualizar status: ${error.message}`, 'error');
    }
}

async function submitForApproval(event) {
    const orderId = event.currentTarget.dataset.orderId;

    if (!orderId) {
        showToast('ID da ordem não encontrado.', 'error');
        return;
    }

    try {
        const response = await fetch(`/api/orders/${orderId}/submit-approval`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
        }

        showToast('Ordem enviada para aprovação com sucesso!', 'success');
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    } catch (error) {
        console.error('Erro ao enviar para aprovação:', error);
        showToast(`Erro ao enviar para aprovação: ${error.message}`, 'error');
    }
}

async function approveOrder(event) {
    const orderId = event.currentTarget.dataset.orderId;

    if (!orderId) {
        showToast('ID da ordem não encontrado.', 'error');
        return;
    }

    try {
        const response = await fetch(`/api/orders/${orderId}/approve`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
        }

        showToast('Ordem aprovada com sucesso!', 'success');
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    } catch (error) {
        console.error('Erro ao aprovar ordem:', error);
        showToast(`Erro ao aprovar ordem: ${error.message}`, 'error');
    }
}

async function rejectOrder(event) {
    const orderId = event.currentTarget.dataset.orderId;

    if (!orderId) {
        showToast('ID da ordem não encontrado.', 'error');
        return;
    }

    const reason = prompt('Por favor, informe o motivo da reprovação:');
    if (reason === null) return; // Usuário cancelou

    try {
        const response = await fetch(`/api/orders/${orderId}/reject`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                reason: reason
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
        }

        showToast('Ordem reprovada com sucesso!', 'success');
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    } catch (error) {
        console.error('Erro ao reprovar ordem:', error);
        showToast(`Erro ao reprovar ordem: ${error.message}`, 'error');
    }
}

async function cancelOrder(event) {
    const orderId = event.currentTarget.dataset.orderId;

    if (!orderId) {
        showToast('ID da ordem não encontrado.', 'error');
        return;
    }

    if (!confirm('Tem certeza que deseja cancelar esta ordem?')) {
        return;
    }

    try {
        const response = await fetch(`/api/orders/${orderId}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: 'cancelled',
                reason: 'Cancelado pela filial'
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
        }

        showToast('Ordem cancelada com sucesso!', 'success');
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    } catch (error) {
        console.error('Erro ao cancelar ordem:', error);
        showToast(`Erro ao cancelar ordem: ${error.message}`, 'error');
    }
}

async function markAsPaid(event) {
    const orderId = event.currentTarget.dataset.orderId;

    if (!orderId) {
        showToast('ID da ordem não encontrado.', 'error');
        return;
    }

    try {
        const response = await fetch(`/api/orders/${orderId}/mark-paid`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
        }

        showToast('Ordem marcada como paga com sucesso!', 'success');
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    } catch (error) {
        console.error('Erro ao marcar como pago:', error);
        showToast(`Erro ao marcar como pago: ${error.message}`, 'error');
    }
}

function printOrder(event) {
    window.print();
}

function openInvoiceModal(event) {
    const orderId = event.currentTarget.dataset.orderId;
    if (!orderId) return console.error("ID da ordem não encontrado");

    // Verificar se o modal existe
    const invoiceModalElement = document.getElementById('invoiceModal');
    if (!invoiceModalElement) {
        showToast('Modal de nota fiscal não encontrado. Entre em contato com o suporte.', 'error');
        return;
    }

    // Configurar o modal
    document.getElementById('invoiceModalOrderId').textContent = `#${orderId}`;
    document.getElementById('invoiceOrderId').value = orderId;
    document.getElementById('invoiceNumber').value = '';
    document.getElementById('invoiceAmount').value = '';
    document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];

    // Exibir o modal
    const invoiceModalInstance = new bootstrap.Modal(invoiceModalElement);
    invoiceModalInstance.show();
}

// Funções Utilitárias
function formatCurrencyJS(value) {
    const numValue = Number(value);
    if (isNaN(numValue)) return "R$ 0,00";
    return numValue.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
}

function escapeHTML(str) {
    if (str === null || str === undefined) return '';
    let element = document.createElement('div');
    element.innerText = str;
    return element.innerHTML;
}

function formatStatusJS(status) {
    const statusMap = {
        'pending': 'Pendente',
        'rejected_provider': 'Recusada pelo Prestador',
        'in_progress': 'Em Andamento',
        'pending_approval': 'Pendente Aprovação',
        'revision_needed': 'Necessita Revisão',
        'approved': 'Aprovada',
        'invoiced': 'Faturada',
        'paid': 'Paga',
        'cancelled': 'Cancelada'
    };
    return statusMap[status] || status;
}

function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toastPlacement');
    if (!toastContainer) {
        alert(`[${type.toUpperCase()}] ${message}`);
        return;
    }

    const toastId = `toast-${Date.now()}`;
    const toastTypeClass = type === 'error' ? 'bg-danger' : (type === 'success' ? 'bg-success' : 'bg-info');

    const toastHTML = `
        <div id="${toastId}" class="toast align-items-center text-white ${toastTypeClass} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    ${escapeHTML(message)}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHTML);
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
    toast.show();
    toastElement.addEventListener('hidden.bs.toast', () => toastElement.remove());
}
