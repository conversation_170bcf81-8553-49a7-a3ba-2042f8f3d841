body {
    background-color: var(--shell-light-gray);
    color: var(--shell-dark);
    font-family: Arial, sans-serif;
}

.main-content {
    margin-left: 250px;
    padding: 20px;
    transition: margin-left 0.3s;
}

.page-header {
    color: var(--shell-yellow);
    border-bottom: 2px solid var(--shell-yellow);
    padding-bottom: 10px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-header {
    background: linear-gradient(90deg, var(--shell-yellow) 0%, var(--shell-red) 100%);
    color: white;
    padding: 10px 15px;
    border-radius: 5px 5px 0 0;
    font-weight: bold;
    display: flex;
    align-items: center;
}

.section-header i {
    margin-right: 10px;
}

.section-content {
    background-color: white;
    border: 1px solid var(--shell-gray);
    border-top: none;
    padding: 15px;
    margin-bottom: 25px;
    border-radius: 0 0 5px 5px;
}

.info-box {
    background-color: var(--shell-light-gray);
    color: var(--shell-dark);
    padding: 10px 15px;
    border-radius: 5px;
    margin-bottom: 15px;
    border-left: 4px solid var(--shell-yellow);
}

.form-control, .form-select {
    background-color: white;
    border: 1px solid var(--shell-gray);
    color: var(--shell-dark);
    padding: 10px;
}

.form-control:focus, .form-select:focus {
    background-color: white;
    border-color: var(--shell-yellow);
    color: var(--shell-dark);
    box-shadow: 0 0 0 0.25rem rgba(244, 179, 29, 0.25);
}

.form-label {
    color: var(--shell-dark);
    font-weight: 500;
    margin-bottom: 5px;
}

.form-text {
    color: #aaa;
}

.btn-shell {
    background-color: var(--shell-yellow);
    border-color: var(--shell-yellow);
    color: #222;
    font-weight: bold;
}

.btn-shell:hover {
    background-color: #e0a30e;
    border-color: #e0a30e;
    color: #222;
}

.btn-outline-shell {
    color: #f4b31d;
    border-color: #f4b31d;
}

.btn-outline-shell:hover {
    background-color: #f4b31d;
    color: #222;
}

.btn-update {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    font-weight: bold;
}

.btn-update:hover {
    background-color: #c82333;
    color: white;
}

.breadcrumb-item a {
    color: white;
    text-decoration: none;
}

.breadcrumb-item.active {
    color: #aaa;
}

.card {
    background-color: #2d2d2d;
    border: 1px solid #3d3d3d;
    border-radius: 5px;
    margin-bottom: 20px;
}

.card-header {
    background-color: #333;
    border-bottom: 1px solid #444;
}

.badge-status-pendente {
    background-color: #f4b31d;
    color: #222;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.badge-status-andamento {
    background-color: #2196f3;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.badge-status-concluido {
    background-color: #4caf50;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.photo-upload-container {
    border: 2px dashed var(--shell-yellow);
    border-radius: var(--shell-radius);
    padding: 20px;
    text-align: center;
    background-color: rgba(253, 184, 19, 0.1);
    margin-bottom: 20px;
}

.photo-preview-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.photo-preview-item {
    width: 100px;
    height: 100px;
    border-radius: 5px;
    overflow: hidden;
    position: relative;
    border: 1px solid #444;
}

.photo-preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.photo-preview-remove {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(220, 53, 69, 0.8);
    color: white;
    border: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

/* Estilização específica para a sidebar */
.sidebar {
    width: 250px;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background-color: #111;
    overflow-x: hidden;
    z-index: 1000;
    transition: 0.3s;
}

.sidebar-header {
    padding: 15px;
    background-color: #000;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    height: 40px;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.menu-item {
    border-left: 4px solid transparent;
}

.menu-item a {
    padding: 15px;
    display: flex;
    align-items: center;
    color: #fff;
    text-decoration: none;
    transition: 0.3s;
}

.menu-item a i {
    margin-right: 10px;
    color: #f4b31d;
}

.menu-item:hover {
    background-color: #222;
}

.menu-item.active {
    border-left-color: #f4b31d;
    background-color: #222;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 15px;
    background-color: #000;
    text-align: center;
    color: #999;
}

.top-nav {
    background-color: #111;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    border-bottom: 1px solid #222;
}

.menu-toggle {
    background: transparent;
    border: none;
    color: #f4b31d;
    font-size: 1.2rem;
    cursor: pointer;
    display: none;
}

.top-nav-right {
    display: flex;
    align-items: center;
}

.user-info {
    margin-left: 15px;
    text-align: right;
}

.user-name {
    color: #fff;
    font-weight: 500;
    display: block;
    font-size: 0.9rem;
}

.user-role {
    color: #999;
    font-size: 0.8rem;
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .menu-toggle {
        display: block;
    }
}
