/* Variáveis do Design System Shell */
:root {
    --shell-red: #ED1C24;
    --shell-yellow: #FDB813;
    --shell-dark: #333333;
    --shell-light: #f8f9fa;
    --shell-gray: #808080;
    --shell-success: #28a745;
    --shell-info: #17a2b8;
    --shell-warning: #ffc107;
    --shell-danger: #dc3545;
}

/* Layout principal */
.consulta-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 20px;
    margin-bottom: 30px;
}

@media (max-width: 992px) {
    .consulta-container {
        grid-template-columns: 1fr;
    }
}

/* Estilos de formulário */
.consulta-form-card, .cotacoes-card {
    background-color: var(--shell-dark);
    border: 1px solid var(--shell-gray);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.form-header, .card-header {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--shell-gray);
}

.form-header h3, .card-header h3 {
    margin: 0;
    color: var(--shell-yellow);
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-type-selector {
    display: flex;
    gap: 5px;
}

.btn-type-select {
    background-color: #444444;
    border: 1px solid var(--shell-gray);
    color: var(--shell-light);
    font-family: 'Rajdhani', sans-serif;
    font-size: 0.9rem;
    font-weight: 600;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-type-select:hover {
    background-color: #555555;
}

.btn-type-select.active {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    border-color: var(--shell-yellow);
}

.active-form {
    display: block;
}

.hidden-form {
    display: none;
}

form {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 15px;
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--shell-light);
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
}

.form-select, .form-control {
    width: 100%;
    background-color: #444444;
    border: 1px solid var(--shell-gray);
    border-radius: 4px;
    color: var(--shell-light);
    padding: 10px 12px;
    font-family: 'Share Tech Mono', monospace;
    transition: all 0.3s ease;
}

.form-select:focus, .form-control:focus {
    background-color: #4a4a4a;
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 0.2rem rgba(253, 184, 19, 0.25);
    color: var(--shell-light);
}

.form-select option {
    background-color: #444444;
    color: var(--shell-light);
    padding: 8px;
}

.form-action {
    margin-top: 30px;
    text-align: right;
}

/* Estilos dos botões */
.shell-btn {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.shell-btn i {
    font-size: 1rem;
}

.shell-btn-yellow {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
}

.shell-btn-yellow:hover {
    background-color: #ffc107;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(253, 184, 19, 0.3);
}

.shell-btn-red {
    background-color: var(--shell-red);
    color: var(--shell-light);
}

.shell-btn-red:hover {
    background-color: #d41920;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(237, 28, 36, 0.3);
}

.shell-btn-grey {
    background-color: var(--shell-gray);
    color: var(--shell-light);
}

.shell-btn-grey:hover {
    background-color: #666666;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(128, 128, 128, 0.3);
}

/* Estilos de Cotações */
.cotacoes-card {
    height: 100%;
}

.refresh-action {
    color: var(--shell-light);
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.refresh-action:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: rotate(180deg);
}

.cotacoes-grid {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 15px;
}

.cotacao-item {
    background-color: #444444;
    border-radius: 6px;
    padding: 15px;
    transition: all 0.3s ease;
    animation: fadeIn 0.5s ease-out;
}

.cotacao-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.cotacao-nome {
    margin-bottom: 8px;
    font-family: 'Rajdhani', sans-serif;
    font-size: 0.95rem;
    color: var(--shell-gray);
    display: flex;
    justify-content: space-between;
}

.cotacao-valor {
    font-family: 'Share Tech Mono', monospace;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--shell-light);
    margin-bottom: 10px;
}

.cotacao-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
}

.cotacao-tempo {
    color: var(--shell-gray);
}

.cotacao-variacao {
    font-weight: 600;
}

.cotacao-variacao.positive {
    color: var(--shell-success);
}

.cotacao-variacao.negative {
    color: var(--shell-danger);
}

/* Estilos dos resultados */
.resultados-container {
    background-color: var(--shell-dark);
    border: 1px solid var(--shell-gray);
    border-radius: 8px;
    overflow: hidden;
    animation: fadeIn 0.5s ease-out;
}

.resultados-header {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--shell-gray);
}

.resultado-info h3 {
    margin: 0 0 5px 0;
    color: var(--shell-yellow);
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1.4rem;
}

.resultado-info p {
    margin: 0;
    color: var(--shell-gray);
    font-family: 'Share Tech Mono', monospace;
    font-size: 0.9rem;
}

.resultado-acoes {
    display: flex;
    gap: 10px;
}

/* Cards de impostos */
.impostos-cards {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 20px;
}

@media (max-width: 992px) {
    .impostos-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .impostos-cards {
        grid-template-columns: 1fr;
    }
}

.imposto-card {
    background-color: #444444;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    animation: slideIn 0.5s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
}

.imposto-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3);
}

.imposto-header {
    background-color: var(--shell-red);
    padding: 15px;
    position: relative;
    overflow: hidden;
}

.imposto-header::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1));
    transform: skewX(-30deg) translateX(130px);
    transition: all 0.5s ease;
}

.imposto-card:hover .imposto-header::after {
    transform: skewX(-30deg) translateX(60px);
}

.imposto-header h4 {
    margin: 0;
    color: var(--shell-light);
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1.1rem;
}

.imposto-icms .imposto-header {
    background-color: var(--shell-red);
}

.imposto-pis .imposto-header {
    background-color: var(--shell-info);
}

.imposto-cofins .imposto-header {
    background-color: var(--shell-warning);
}

.imposto-cide .imposto-header {
    background-color: var(--shell-success);
}

.imposto-total .imposto-header {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
}

.imposto-total .imposto-header h4 {
    color: var(--shell-dark);
}

.imposto-body {
    padding: 15px;
}

.imposto-valor {
    font-family: 'Share Tech Mono', monospace;
    font-size: 2rem;
    font-weight: 600;
    color: var(--shell-light);
    margin-bottom: 10px;
}

.imposto-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.imposto-descricao {
    color: var(--shell-gray);
    font-family: 'Rajdhani', sans-serif;
    font-size: 0.9rem;
}

.imposto-porcentagem {
    font-family: 'Share Tech Mono', monospace;
    font-weight: 600;
    color: var(--shell-yellow);
}

.imposto-total .imposto-valor {
    color: var(--shell-yellow);
}

.imposto-barra {
    height: 6px;
    width: 100%;
    background-color: #333333;
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.imposto-progresso {
    height: 100%;
    position: absolute;
    transition: width 1s ease-out;
}

.imposto-icms .imposto-progresso {
    background-color: var(--shell-red);
}

.imposto-pis .imposto-progresso {
    background-color: var(--shell-info);
}

.imposto-cofins .imposto-progresso {
    background-color: var(--shell-warning);
}

.imposto-cide .imposto-progresso {
    background-color: var(--shell-success);
}

.imposto-total .imposto-progresso {
    background-color: var(--shell-yellow);
}

/* Gráfico */
.grafico-container, .comparacao-container {
    padding: 20px;
    margin-top: 20px;
    animation: fadeIn 0.5s ease-out;
}

.grafico-container h4, .comparacao-container h4 {
    color: var(--shell-light);
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1.2rem;
    margin-bottom: 15px;
}

.grafico-wrapper {
    background-color: #444444;
    border-radius: 8px;
    padding: 20px;
    height: 300px;
}

/* Grid de comparação */
.comparacao-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 20px;
    margin-bottom: 20px;
}

.comparacao-estado {
    background-color: #444444;
    border-radius: 8px;
    padding: 20px;
}

.comparacao-estado h5 {
    margin: 0 0 15px 0;
    color: var(--shell-yellow);
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1.1rem;
}

.comparacao-lista {
    list-style: none;
    padding: 0;
    margin: 0;
}

.comparacao-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.comparacao-item:last-child {
    border-bottom: none;
}

.comparacao-imposto {
    color: var(--shell-light);
    font-family: 'Rajdhani', sans-serif;
}

.comparacao-valor {
    font-family: 'Share Tech Mono', monospace;
    color: var(--shell-yellow);
    font-weight: 600;
}

.comparacao-diferenca {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    margin-top: 20px;
    padding: 15px;
}

.comparacao-diferenca h6 {
    margin: 0 0 10px 0;
    color: var(--shell-light);
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
}

.diferenca-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.diferenca-imposto {
    color: var(--shell-gray);
    font-family: 'Rajdhani', sans-serif;
}

.diferenca-valor {
    font-family: 'Share Tech Mono', monospace;
    font-weight: 600;
}

.diferenca-valor.melhor {
    color: var(--shell-success);
}

.diferenca-valor.pior {
    color: var(--shell-danger);
}

/* Animações */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.delay-1 {
    animation-delay: 0.1s;
}

.delay-2 {
    animation-delay: 0.2s;
}

.delay-3 {
    animation-delay: 0.3s;
}

.delay-4 {
    animation-delay: 0.4s;
}

.delay-5 {
    animation-delay: 0.5s;
}

/* Estilos para página de consulta de impostos - Design System Shell */

/* Variáveis */
:root {
    --vermelho-shell: #ED1C24;
    --amarelo-shell: #FDB813;
    --escuro: #333333;
    --claro: #f8f9fa;
    --cinza: #808080;
    --success: #28a745;
    --info: #17a2b8;
    --warning: #ffc107;
    --danger: #dc3545;
    
    --bg-dark: #1e1e1e;
    --border-radius: 6px;
    --card-border: 1px solid rgba(255, 255, 255, 0.1);
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

/* Layout principal */
.consulta-impostos-container {
    padding: 20px;
    color: var(--claro);
}

.consulta-impostos-header {
    margin-bottom: 25px;
}

.consulta-impostos-header h2 {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 700;
    color: var(--claro);
    font-size: 1.8rem;
    border-bottom: 2px solid var(--vermelho-shell);
    padding-bottom: 10px;
    margin-bottom: 15px;
    display: inline-block;
}

.consulta-impostos-desc {
    font-family: 'Share Tech Mono', monospace;
    font-size: 0.95rem;
    color: var(--cinza);
    margin-bottom: 20px;
}

/* Navegação de abas */
.consulta-nav {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.consulta-nav-item {
    padding: 10px 20px;
    color: var(--cinza);
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.consulta-nav-item.active {
    color: var(--claro);
    background-color: rgba(237, 28, 36, 0.1);
}

.consulta-nav-item.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--vermelho-shell);
}

.consulta-nav-item:hover:not(.active) {
    color: var(--amarelo-shell);
}

.consulta-nav-item i {
    margin-right: 8px;
}

/* Formulários */
.consulta-form {
    background-color: rgba(30, 30, 30, 0.6);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 30px;
    border: var(--card-border);
    box-shadow: var(--card-shadow);
}

.hidden-form {
    display: none;
}

.active-form {
    display: block;
    animation: fadeIn 0.5s ease;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px 15px;
}

.form-group {
    flex: 1;
    padding: 0 10px;
    margin-bottom: 15px;
    min-width: 200px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    color: var(--claro);
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    background-color: #262626;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    color: var(--claro);
    transition: all 0.3s;
    font-family: 'Share Tech Mono', monospace;
}

.form-control:focus {
    outline: none;
    border-color: var(--amarelo-shell);
    box-shadow: 0 0 0 3px rgba(253, 184, 19, 0.2);
}

select.form-control {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23FDB813' d='M6 8.825L1.175 4 0 5.175 6 11.175 12 5.175 10.825 4z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    padding-right: 30px;
}

.btn-calcular {
    background-color: var(--amarelo-shell);
    color: var(--escuro);
    font-family: 'Rajdhani', sans-serif;
    font-weight: 700;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-calcular:hover {
    background-color: #e6a400;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-calcular i {
    margin-right: 8px;
}

/* Resultados */
.resultados-container {
    display: none;
    background-color: rgba(30, 30, 30, 0.6);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-top: 30px;
    border: var(--card-border);
    box-shadow: var(--card-shadow);
    animation: slideUp 0.5s ease;
}

.resultados-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.resultados-info {
    font-family: 'Share Tech Mono', monospace;
    font-size: 1.1rem;
    color: var(--claro);
}

.resultados-acoes {
    display: flex;
}

.btn-acao {
    background-color: transparent;
    color: var(--cinza);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    margin-left: 10px;
    padding: 6px 12px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
}

.btn-acao:hover {
    color: var(--claro);
    border-color: var(--cinza);
}

.btn-acao i {
    margin-right: 5px;
}

/* Cards de impostos */
.impostos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.imposto-card {
    background-color: rgba(30, 30, 30, 0.8);
    border-radius: var(--border-radius);
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
}

.imposto-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.1);
}

.imposto-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background-color: var(--amarelo-shell);
}

.imposto-total {
    background-color: rgba(237, 28, 36, 0.15);
    grid-column: 1 / -1;
}

.imposto-total::after {
    background-color: var(--vermelho-shell);
}

.imposto-titulo {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 10px;
    color: var(--claro);
}

.imposto-valor {
    font-family: 'Share Tech Mono', monospace;
    font-size: 1.3rem;
    margin-bottom: 5px;
    color: var(--amarelo-shell);
}

.imposto-total .imposto-valor {
    color: var(--vermelho-shell);
    font-size: 1.5rem;
}

.imposto-percentual {
    font-family: 'Share Tech Mono', monospace;
    font-size: 0.9rem;
    color: var(--cinza);
}

/* Gráficos */
.graficos-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 30px;
}

.grafico-box {
    flex: 1;
    min-width: 300px;
    background-color: rgba(30, 30, 30, 0.6);
    border-radius: var(--border-radius);
    padding: 20px;
    border: var(--card-border);
    box-shadow: var(--card-shadow);
}

.grafico-header {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 15px;
    color: var(--claro);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 10px;
}

.grafico-canvas-container {
    width: 100%;
    height: 300px;
    position: relative;
}

/* Comparação */
.comparacao-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.comparacao-estado {
    background-color: rgba(30, 30, 30, 0.8);
    border-radius: var(--border-radius);
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: var(--card-shadow);
    transition: all 0.3s;
}

.comparacao-estado.melhor-opcao {
    background-color: rgba(40, 167, 69, 0.1);
    border-color: var(--success);
}

.comparacao-estado.melhor-opcao::after {
    content: "Menor carga tributária";
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--success);
    color: white;
    font-size: 0.8rem;
    padding: 3px 8px;
    border-radius: 3px;
    font-family: 'Rajdhani', sans-serif;
}

.comparacao-estado h5 {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: var(--claro);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 10px;
}

.comparacao-impostos {
    font-family: 'Share Tech Mono', monospace;
}

.comparacao-imposto {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px dashed rgba(255, 255, 255, 0.05);
}

.comparacao-imposto:last-of-type {
    border-bottom: none;
    margin-bottom: 15px;
}

.comparacao-total,
.comparacao-valor-final {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-weight: bold;
}

.comparacao-total {
    color: var(--vermelho-shell);
}

.comparacao-valor-final {
    color: var(--amarelo-shell);
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsividade */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
    }
    
    .form-group {
        min-width: 100%;
    }
    
    .comparacao-grid {
        grid-template-columns: 1fr;
    }
    
    .consulta-nav-item {
        padding: 8px 15px;
        font-size: 0.9rem;
    }
    
    .consulta-impostos-header h2 {
        font-size: 1.5rem;
    }
    
    .imposto-valor {
        font-size: 1.1rem;
    }
    
    .imposto-total .imposto-valor {
        font-size: 1.3rem;
    }
}

/* Estilo para página de Consulta de Impostos */

.card-shell {
    margin-bottom: 1.5rem;
    border: none;
    border-radius: 8px;
    background-color: #333333;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s, box-shadow 0.3s;
}

.card-shell:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.card-shell .card-header {
    background-color: rgba(237, 28, 36, 0.1);
    border-bottom: 2px solid #ED1C24;
    padding: 1rem;
    border-radius: 8px 8px 0 0;
}

.card-shell .card-header h3 {
    margin: 0;
    color: #FDB813;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
}

.card-shell .card-body {
    padding: 1.5rem;
}

/* Estilos para o formulário */
.form-label {
    color: #f8f9fa;
    font-weight: 500;
    font-family: 'Rajdhani', sans-serif;
}

.form-control {
    background-color: #444444;
    border: 1px solid #555555;
    color: #f8f9fa;
    border-radius: 4px;
    padding: 0.75rem;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.form-control:focus {
    background-color: #444444;
    border-color: #FDB813;
    box-shadow: 0 0 0 0.2rem rgba(253, 184, 19, 0.25);
    color: #f8f9fa;
}

.btn-shell-red {
    background-color: #ED1C24;
    border-color: #ED1C24;
    color: #f8f9fa;
    font-weight: 600;
    letter-spacing: 0.5px;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s;
}

.btn-shell-red:hover {
    background-color: #d81217;
    border-color: #d81217;
    color: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(216, 18, 23, 0.4);
}

.btn-shell-yellow {
    background-color: #FDB813;
    border-color: #FDB813;
    color: #333333;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s;
}

.btn-shell-yellow:hover {
    background-color: #e0a40e;
    border-color: #e0a40e;
    color: #333333;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(224, 164, 14, 0.4);
}

/* Estilos para tabela de resultados */
.table-shell {
    color: #f8f9fa;
    background-color: transparent;
    margin-bottom: 1.5rem;
}

.table-shell thead th {
    background-color: rgba(237, 28, 36, 0.1);
    color: #FDB813;
    border-color: #555555;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.table-shell tbody td {
    border-color: #555555;
    vertical-align: middle;
}

/* Estilos para resumo de valores */
.resumo {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #555555;
}

.badge-shell {
    background-color: #333333;
    color: #f8f9fa;
    border: 1px solid #FDB813;
    font-size: 1.2rem;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-family: 'Share Tech Mono', monospace;
    font-weight: 400;
}

.badge-shell-red {
    background-color: rgba(237, 28, 36, 0.1);
    color: #f8f9fa;
    border: 1px solid #ED1C24;
    font-size: 1.2rem;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-family: 'Share Tech Mono', monospace;
    font-weight: 400;
}

/* Animações para os resultados */
#resultados {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s, transform 0.5s;
}

#resultados.show {
    opacity: 1;
    transform: translateY(0);
}

/* Alerta personalizado */
.alert-shell-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #f8f9fa;
    border: 1px solid #dc3545;
    border-radius: 4px;
}

/* Responsividade */
@media (max-width: 768px) {
    .card-actions {
        margin-top: 0.5rem;
    }
    
    .card-header {
        flex-direction: column;
        align-items: flex-start !important;
    }
} 