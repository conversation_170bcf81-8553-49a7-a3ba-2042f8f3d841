/* Variáveis */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #34495e;
    --accent-color: #3498db;
    --success-color: #2ecc71;
    --warning-color: #f1c40f;
    --danger-color: #e74c3c;
    --text-color: #2c3e50;
    --text-muted: #7f8c8d;
    --border-color: #e0e0e0;
    --card-shadow: 0 2px 4px rgba(0,0,0,0.1);
    --transition-speed: 0.3s;
}

/* Reset e Estilos Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    color: var(--text-color);
    background-color: #f5f6fa;
    line-height: 1.6;
}

/* Container Principal */
.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.header-title h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.date-filter select {
    min-width: 150px;
}

/* Grid de Métricas */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform var(--transition-speed);
}

.metric-card:hover {
    transform: translateY(-5px);
}

.metric-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.metric-icon i {
    color: white;
}

.metric-card:nth-child(1) .metric-icon {
    background-color: var(--accent-color);
}

.metric-card:nth-child(2) .metric-icon {
    background-color: var(--danger-color);
}

.metric-card:nth-child(3) .metric-icon {
    background-color: var(--success-color);
}

.metric-card:nth-child(4) .metric-icon {
    background-color: var(--warning-color);
}

.metric-info h3 {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 0.25rem;
}

.metric-info p {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

/* Grid de Gráficos */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.chart-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.chart-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.chart-container {
    height: 300px;
    position: relative;
}

/* Grid de Conteúdo */
.content-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.content-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.card-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

/* Lista de Pagamentos */
.payments-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.payment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    transition: background-color var(--transition-speed);
}

.payment-item:hover {
    background: #e9ecef;
}

.payment-info h4 {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.payment-info p {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin: 0;
}

.payment-amount {
    text-align: right;
}

.payment-amount .amount {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-color);
}

.payment-amount .status {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 0.25rem;
}

.status.paid {
    background-color: #d4edda;
    color: #155724;
}

.status.pending {
    background-color: #fff3cd;
    color: #856404;
}

.status.overdue {
    background-color: #f8d7da;
    color: #721c24;
}

/* Grid de Cotações */
.quotes-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.quote-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
}

.quote-item h4 {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
}

.quote-item .value {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.quote-item .change {
    font-size: 0.875rem;
    font-weight: 500;
}

.change.positive {
    color: var(--success-color);
}

.change.negative {
    color: var(--danger-color);
}

/* Grid de Ferramentas e Notícias */
.tools-news-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

/* Grid de Ferramentas */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.tool-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    text-decoration: none;
    color: var(--text-color);
    transition: all var(--transition-speed);
}

.tool-item:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.tool-item i {
    font-size: 1.5rem;
    color: var(--accent-color);
}

.tool-item span {
    font-size: 0.875rem;
    font-weight: 500;
}

/* Lista de Notícias */
.news-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.news-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    transition: background-color var(--transition-speed);
}

.news-item:hover {
    background: #e9ecef;
}

.news-content h4 {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.news-content p {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
}

.news-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Responsividade */
@media (max-width: 1200px) {
    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 1rem;
    }
    
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .header-actions {
        width: 100%;
        flex-direction: column;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .content-grid,
    .tools-news-grid {
        grid-template-columns: 1fr;
    }
    
    .quotes-grid {
        grid-template-columns: 1fr;
    }
    
    .tools-grid {
        grid-template-columns: 1fr;
    }
}

/* Animações */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease forwards;
}

/* Utilitários */
.text-muted {
    color: var(--text-muted) !important;
}

.btn-outline-primary {
    color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-outline-primary:hover {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-outline-success {
    color: var(--success-color);
    border-color: var(--success-color);
}

.btn-outline-success:hover {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-outline-secondary {
    color: var(--text-muted);
    border-color: var(--border-color);
}

.btn-outline-secondary:hover {
    background-color: var(--text-muted);
    border-color: var(--text-muted);
    color: white;
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(52, 152, 219, 0.1);
}

::-webkit-scrollbar-thumb {
    background: var(--accent-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #2980b9;
} 
} 