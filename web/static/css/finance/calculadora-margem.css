/**
 * Calculadora de Margem - CSS
 * Sistema Shell
 */

/* Estilos do Modal */
#calculadoraMargemModal .modal-content {
    background-color: #333333;
    color: #f8f9fa;
    border: 1px solid #ED1C24;
}

#calculadoraMargemModal .modal-header {
    border-bottom-color: #ED1C24;
}

#calculadoraMargemModal .modal-footer {
    border-top-color: #ED1C24;
}

#calculadoraMargemModal .close {
    color: #f8f9fa;
    text-shadow: none;
    opacity: 0.8;
}

#calculadoraMargemModal .close:hover {
    opacity: 1;
    color: #ED1C24;
}

/* Formulário */
#calculadoraMargemForm .form-group label {
    font-family: 'Share Tech Mono', monospace;
    font-weight: 500;
    color: #f8f9fa;
}

#calculadoraMargemForm .form-control {
    background-color: #444;
    border-color: #666;
    color: #fff;
}

#calculadoraMargemForm .form-control:focus {
    border-color: #FDB813;
    box-shadow: 0 0 0 0.2rem rgba(253, 184, 19, 0.25);
}

#calculadoraMargemForm .input-group-text {
    background-color: #ED1C24;
    color: #fff;
    border-color: #ED1C24;
}

/* Cards de Resultado */
.card-resultado {
    background-color: #444;
    border: none;
    border-radius: 0.25rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
}

.card-resultado:hover {
    transform: translateY(-5px);
}

.card-resultado .card-header {
    background-color: #333;
    color: #f8f9fa;
    font-family: 'Share Tech Mono', monospace;
    border-bottom: 2px solid #ED1C24;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-resultado .card-body {
    padding: 1.5rem;
}

.card-resultado .valor {
    font-size: 2rem;
    font-weight: bold;
    font-family: 'Rajdhani', sans-serif;
}

/* Barra de Progresso */
.progress {
    height: 12px;
    background-color: #333;
    margin-top: 0.5rem;
    border-radius: 6px;
    overflow: hidden;
}

.progress-bar-bruta {
    background-color: #FDB813;
}

.progress-bar-liquida {
    background-color: #ED1C24;
}

/* Tabela de Resultados */
.table-resultados {
    width: 100%;
    color: #f8f9fa;
    margin-top: 1rem;
}

.table-resultados th {
    font-family: 'Share Tech Mono', monospace;
    border-bottom: 2px solid #ED1C24;
    padding: 0.5rem;
    background-color: #333;
}

.table-resultados td {
    padding: 0.5rem;
    border-bottom: 1px solid #555;
}

.table-resultados tr:last-child td {
    border-bottom: 2px solid #FDB813;
    font-weight: bold;
    color: #FDB813;
}

/* Botões */
.btn-shell-red {
    background-color: #ED1C24;
    border-color: #ED1C24;
    color: #fff;
    transition: all 0.2s ease;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
}

.btn-shell-red:hover {
    background-color: #d01920;
    border-color: #d01920;
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(237, 28, 36, 0.3);
}

.btn-shell-yellow {
    background-color: #FDB813;
    border-color: #FDB813;
    color: #333;
    transition: all 0.2s ease;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
}

.btn-shell-yellow:hover {
    background-color: #e5a700;
    border-color: #e5a700;
    color: #333;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(253, 184, 19, 0.3);
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

#resultadosMargem {
    animation: fadeIn 0.5s ease-in-out;
}

/* Responsividade */
@media (max-width: 768px) {
    .card-resultado .valor {
        font-size: 1.5rem;
    }

    .table-resultados {
        font-size: 0.9rem;
    }
}

/* Calculadora de Margem - Design System Shell */

.card-shell {
    background-color: #333333;
    border-radius: 8px;
    border: 1px solid #444;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.card-shell .card-header {
    background-color: #222;
    color: #f8f9fa;
    border-bottom: 2px solid #ED1C24;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    padding: 15px 20px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.card-shell .card-body {
    padding: 20px;
    color: #f8f9fa;
}

.btn-shell-red {
    background-color: #ED1C24;
    border-color: #ED1C24;
    color: white;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-shell-red:hover, .btn-shell-red:focus {
    background-color: #c61017;
    border-color: #c61017;
    color: white;
    box-shadow: 0 0 10px rgba(237, 28, 36, 0.6);
}

.btn-shell-yellow {
    background-color: #FDB813;
    border-color: #FDB813;
    color: #333333;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-shell-yellow:hover, .btn-shell-yellow:focus {
    background-color: #e6a511;
    border-color: #e6a511;
    color: #333333;
    box-shadow: 0 0 10px rgba(253, 184, 19, 0.6);
}

.calculator-icon {
    font-size: 3rem;
    color: #FDB813;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.card-shell:hover .calculator-icon {
    transform: scale(1.1);
}

.badge-shell {
    background-color: #ED1C24;
    color: white;
    font-family: 'Share Tech Mono', monospace;
    font-weight: 400;
    padding: 5px 10px;
    border-radius: 4px;
    margin-left: 8px;
}

.modal-shell {
    border-radius: 8px;
}

.modal-shell .modal-content {
    background-color: #333333;
    color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #444;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

.modal-shell .modal-header {
    background-color: #222;
    color: #f8f9fa;
    border-bottom: 2px solid #ED1C24;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.modal-shell .modal-title {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    color: #f8f9fa;
}

.modal-shell .modal-footer {
    border-top: 1px solid #444;
    background-color: #2a2a2a;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

.form-control {
    background-color: #2a2a2a;
    color: #f8f9fa;
    border: 1px solid #444;
    transition: all 0.3s ease;
}

.form-control:focus {
    background-color: #2a2a2a;
    color: #f8f9fa;
    border-color: #FDB813;
    box-shadow: 0 0 0 0.2rem rgba(253, 184, 19, 0.25);
}

.form-label {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    color: #f8f9fa;
    margin-bottom: 8px;
}

.card-resultado {
    background-color: #2a2a2a;
    border: 1px solid #444;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.card-hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.3);
}

.margem-valor {
    font-family: 'Share Tech Mono', monospace;
    font-size: 2rem;
    font-weight: 700;
    color: #f8f9fa;
}

.progress {
    height: 8px;
    background-color: #444;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 10px;
}

.progress-bar {
    transition: width 0.6s ease;
}

.table-shell {
    color: #f8f9fa;
    background-color: transparent;
}

.table-shell th {
    border-top: none;
    border-bottom: 2px solid #ED1C24;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    color: #FDB813;
}

.table-shell td {
    border-top: 1px solid #444;
    font-family: 'Share Tech Mono', monospace;
}

.alert-shell {
    background-color: #333333;
    color: #f8f9fa;
    border-left: 4px solid #ED1C24;
    border-radius: 4px;
}

.alert-shell-danger {
    border-color: #dc3545;
}

.alert-shell-warning {
    border-color: #ffc107;
}

.alert-shell-info {
    border-color: #17a2b8;
}

.alert-shell-success {
    border-color: #28a745;
}

/* Animação para fadein */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

/* Media queries para responsividade */
@media (max-width: 768px) {
    .card-shell {
        margin-bottom: 15px;
    }
    
    .margem-valor {
        font-size: 1.5rem;
    }
    
    .calculator-icon {
        font-size: 2.5rem;
    }
}

@media (max-width: 576px) {
    .card-shell .card-header {
        padding: 10px 15px;
    }
    
    .card-shell .card-body {
        padding: 15px;
    }
    
    .table-shell {
        font-size: 0.85rem;
    }
} 