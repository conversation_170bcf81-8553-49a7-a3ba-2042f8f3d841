/* Estilos específicos para a página de painel financeiro */

:root {
    --shell-red: #ED1C24;
    --shell-red-hover: #CE181F;
    --shell-red-light: #ff6b6e;
    --shell-yellow: #FDB813;
    --shell-yellow-hover: #e0a100;
    --shell-yellow-light: #ffe07a;
    --shell-dark: #333333;
    --shell-dark-hover: #222222;
    --shell-light: #f8f9fa;
    --shell-light-hover: #e9ecef;
    --shell-gray: #808080;
    --shell-gray-light: #d9d9d9;

    /* Cores específicas para o financeiro */
    --finance-green: #28a745;
    --finance-green-light: rgba(40, 167, 69, 0.2);
    --finance-blue: #0275d8;
    --finance-blue-light: rgba(2, 117, 216, 0.2);
    --finance-orange: #fd7e14;
    --finance-orange-light: rgba(253, 126, 20, 0.2);
    --finance-purple: #6f42c1;
    --finance-purple-light: rgba(111, 66, 193, 0.2);
}

/* Corrigir fundo branco e espaços vazios */
body {
    background-color: #333 !important;
    color: #f8f9fa !important;
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

.content-with-sidebar {
    background-color: #333 !important;
    min-height: calc(100vh - 60px);
    margin: 0;
    padding: 0;
}

.main-content {
    background-color: #333 !important;
    padding-bottom: 30px;
}

.finance-tools-container {
    background-color: #333 !important;
    padding-bottom: 30px;
}

/* Cabeçalho do painel financeiro */
.finance-header {
    background: linear-gradient(135deg, #1a1a1a 0%, #333333 100%);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.finance-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--shell-red), var(--shell-yellow));
}

.finance-title-container {
    color: white;
}

.finance-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
}

.finance-title i {
    color: var(--shell-yellow);
    margin-right: 0.75rem;
}

.finance-subtitle {
    color: #aaa;
    font-size: 0.9rem;
}

.finance-actions {
    display: flex;
    align-items: center;
}

.date-range-selector {
    width: 250px;
}

/* Métricas financeiras */
.finance-metrics-container {
    margin-bottom: 1.5rem;
}

.metric-card {
    background: linear-gradient(135deg, rgba(30, 30, 30, 0.9) 0%, rgba(50, 50, 50, 0.9) 100%);
    border-radius: 12px;
    padding: 1.25rem;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.metric-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.metric-card.revenue::after {
    background: var(--shell-yellow);
}

.metric-card.expenses::after {
    background: var(--shell-red);
}

.metric-card.pending::after {
    background: var(--finance-orange);
}

.metric-card.profit::after {
    background: var(--finance-green);
}

.metric-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.metric-card.revenue .metric-icon {
    background-color: rgba(253, 184, 19, 0.2);
    color: var(--shell-yellow);
}

.metric-card.expenses .metric-icon {
    background-color: rgba(237, 28, 36, 0.2);
    color: var(--shell-red);
}

.metric-card.pending .metric-icon {
    background-color: var(--finance-orange-light);
    color: var(--finance-orange);
}

.metric-card.profit .metric-icon {
    background-color: var(--finance-green-light);
    color: var(--finance-green);
}

.metric-content {
    flex: 1;
}

.metric-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: white;
    line-height: 1.2;
    font-family: 'Share Tech Mono', monospace;
}

.metric-label {
    font-size: 0.85rem;
    color: #aaa;
    margin-bottom: 0.25rem;
}

.metric-trend {
    font-size: 0.8rem;
    display: flex;
    align-items: center;
}

.metric-trend.positive {
    color: var(--finance-green);
}

.metric-trend.negative {
    color: var(--shell-red);
}

.metric-trend i {
    margin-right: 0.25rem;
}

/* Indicador de combustível */
.metric-fuel-gauge {
    width: 15px;
    height: 60px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
    margin-left: 10px;
}

.fuel-level {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(to top, var(--shell-red), var(--shell-yellow));
    transition: height 1s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.fuel-bubble {
    position: absolute;
    width: 4px;
    height: 4px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 50%;
    animation: bubble 3s infinite ease-in-out;
}

.fuel-bubble:nth-child(1) {
    left: 3px;
    animation-delay: 0s;
}

.fuel-bubble:nth-child(2) {
    left: 7px;
    animation-delay: 1.5s;
}

.fuel-bubble:nth-child(3) {
    left: 5px;
    animation-delay: 0.7s;
}

@keyframes bubble {
    0% {
        bottom: 0;
        opacity: 0;
    }
    20% {
        opacity: 0.8;
    }
    80% {
        opacity: 0.8;
    }
    100% {
        bottom: 100%;
        opacity: 0;
    }
}

/* Seções do painel financeiro */
.finance-section {
    background-color: rgba(30, 30, 30, 0.7);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: white;
    margin: 0;
    display: flex;
    align-items: center;
}

.section-title i {
    color: var(--shell-yellow);
    margin-right: 0.75rem;
}

/* Cards de pagamento */
.payment-card {
    background-color: rgba(40, 40, 40, 0.7);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.payment-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
}

.payment-header {
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.payment-card.urgent .payment-header {
    background-color: rgba(237, 28, 36, 0.2);
}

.payment-card.warning .payment-header {
    background-color: rgba(253, 184, 19, 0.2);
}

.payment-card.normal .payment-header {
    background-color: rgba(40, 167, 69, 0.2);
}

.payment-status {
    font-size: 0.9rem;
    font-weight: 600;
}

.payment-card.urgent .payment-status {
    color: var(--shell-red);
}

.payment-card.warning .payment-status {
    color: var(--shell-yellow);
}

.payment-card.normal .payment-status {
    color: var(--finance-green);
}

.payment-amount {
    font-size: 1.2rem;
    font-weight: 700;
    color: white;
    font-family: 'Share Tech Mono', monospace;
}

.payment-body {
    padding: 1rem;
    flex: 1;
}

.payment-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: white;
}

.payment-details {
    margin-bottom: 0.75rem;
}

.payment-detail {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #ccc;
}

.payment-detail i {
    width: 20px;
    margin-right: 0.5rem;
    color: var(--shell-yellow);
}

.payment-footer {
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
}

/* Cards de preventivas */
.preventive-card {
    background-color: rgba(40, 40, 40, 0.7);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.preventive-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
}

.preventive-header {
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: rgba(2, 117, 216, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.preventive-date {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--finance-blue);
}

.preventive-estimate {
    font-size: 0.9rem;
    color: #ccc;
}

.preventive-body {
    padding: 1rem;
    flex: 1;
}

.preventive-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: white;
}

.preventive-details {
    margin-bottom: 0.75rem;
}

.preventive-detail {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #ccc;
}

.preventive-detail i {
    width: 20px;
    margin-right: 0.5rem;
    color: var(--finance-blue);
}

.preventive-history {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 0.75rem;
    margin-top: 0.75rem;
}

.history-title {
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #ddd;
}

.history-item {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
    color: #bbb;
    margin-bottom: 0.25rem;
}

.preventive-footer {
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
}

/* Cards de ferramentas financeiras */
.finance-tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.finance-tool-card {
    background: linear-gradient(135deg, rgba(40, 40, 40, 0.8) 0%, rgba(60, 60, 60, 0.8) 100%);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    height: 100%;
}

.finance-tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
    pointer-events: none;
}

.finance-tool-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.finance-tool-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--shell-yellow);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.finance-tool-card:hover::after {
    transform: scaleX(1);
}

.tool-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(253, 184, 19, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: var(--shell-yellow);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.finance-tool-card:hover .tool-icon {
    background-color: rgba(253, 184, 19, 0.25);
    transform: scale(1.1);
}

.tool-content {
    flex: 1;
}

.tool-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: white;
    margin-bottom: 0.5rem;
}

.tool-description {
    font-size: 0.9rem;
    color: #bbb;
    margin-bottom: 1rem;
}

.tool-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

.tool-status {
    display: flex;
    align-items: center;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-dot.online {
    background-color: var(--finance-green);
    box-shadow: 0 0 5px var(--finance-green);
    animation: pulse 2s infinite;
}

.status-text {
    font-size: 0.8rem;
    color: #aaa;
}

.btn-tool-action {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--shell-red);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.btn-tool-action:hover {
    background-color: var(--shell-red-hover);
    transform: scale(1.1);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 5px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* Botões personalizados */
.btn-shell-red {
    background-color: var(--shell-red);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-shell-red:hover {
    background-color: var(--shell-red-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-shell-yellow {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-shell-yellow:hover {
    background-color: var(--shell-yellow-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-outline-shell {
    background-color: transparent;
    color: #ccc;
    border: 1px solid #555;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-shell:hover {
    border-color: var(--shell-yellow);
    color: var(--shell-yellow);
    transform: translateY(-2px);
}

.btn-shell-success {
    background-color: var(--finance-green);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-shell-success:hover {
    background-color: #218838;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Cards de métricas operacionais */
.metrics-card {
    background-color: rgba(40, 40, 40, 0.7);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.metrics-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
}

.metrics-header {
    padding: 1rem;
    background-color: rgba(40, 40, 40, 0.9);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.metrics-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    color: white;
    display: flex;
    align-items: center;
}

.metrics-title i {
    margin-right: 0.75rem;
    color: var(--shell-yellow);
}

.metrics-body {
    padding: 1rem;
    flex: 1;
}

.metrics-chart {
    position: relative;
    height: 200px;
    margin-bottom: 1rem;
    background-color: rgba(30, 30, 30, 0.5);
    border-radius: 8px;
    overflow: hidden;
}

.chart-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(30, 30, 30, 0.8);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--shell-yellow);
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 0.5rem;
}

.chart-loading span {
    font-size: 0.9rem;
    color: #ccc;
}

.metrics-summary {
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
}

.summary-item {
    text-align: center;
    flex: 1;
}

.summary-label {
    font-size: 0.8rem;
    color: #aaa;
    margin-bottom: 0.25rem;
}

.summary-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    font-family: 'Share Tech Mono', monospace;
}

.summary-value.negative {
    color: var(--shell-red);
}

.summary-value.positive {
    color: var(--finance-green);
}

.metrics-table {
    margin-top: 1rem;
    background-color: rgba(30, 30, 30, 0.5);
    border-radius: 8px;
    overflow: hidden;
}

.metrics-table table {
    width: 100%;
    color: #ccc;
    margin: 0;
}

.metrics-table th {
    background-color: rgba(0, 0, 0, 0.2);
    color: var(--shell-yellow);
    font-weight: 600;
    font-size: 0.9rem;
    padding: 0.5rem;
    border-color: rgba(255, 255, 255, 0.1);
}

.metrics-table td {
    padding: 0.5rem;
    border-color: rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
}

.metrics-list {
    margin-top: 1rem;
}

.list-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    padding: 0.5rem;
    background-color: rgba(30, 30, 30, 0.5);
    border-radius: 5px;
}

.item-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.75rem;
}

.item-label {
    flex: 1;
    font-size: 0.9rem;
    color: #ddd;
}

.item-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: white;
    font-family: 'Share Tech Mono', monospace;
}

.metrics-footer {
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: center;
}

/* Responsividade */
@media (max-width: 992px) {
    .finance-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .finance-actions {
        margin-top: 1rem;
        width: 100%;
    }

    .date-range-selector {
        width: 100%;
    }

    .finance-tools-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    .metrics-summary {
        flex-direction: column;
        gap: 0.75rem;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .summary-label {
        margin-bottom: 0;
    }
}

@media (max-width: 768px) {
    .finance-tools-grid {
        grid-template-columns: 1fr;
    }

    .payment-footer, .preventive-footer, .metrics-footer {
        flex-direction: column;
        gap: 0.5rem;
    }

    .payment-footer button, .preventive-footer button, .metrics-footer button {
        width: 100%;
    }

    .chart-container {
        height: 300px !important;
    }
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* Toast Messages */
.toast-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 4px;
    background-color: var(--shell-dark);
    color: white;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    transform: translateX(120%);
    transition: transform 0.3s ease;
    z-index: 9999;
}

.toast-message.show {
    transform: translateX(0);
}

.toast-message i {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

.toast-message.success {
    background-color: var(--finance-green);
}

.toast-message.error {
    background-color: var(--shell-red);
}

/* Modal Styles */
.modal-content {
    background-color: var(--shell-dark);
    color: white;
    border: none;
    border-radius: 8px;
}

.modal-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

.modal-header .close {
    color: white;
    text-shadow: none;
    opacity: 0.7;
}

.modal-header .close:hover {
    opacity: 1;
}

.details-section {
    margin-bottom: 1.5rem;
}

.details-section h5 {
    color: var(--shell-yellow);
    margin-bottom: 1rem;
}

.details-section ul {
    list-style: none;
    padding: 0;
}

.details-section ul li {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.details-section table {
    color: white;
}

.details-section table th {
    border-top: none;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.details-section table td {
    border-color: rgba(255, 255, 255, 0.1);
}

.form-group label {
    color: var(--shell-yellow);
    margin-bottom: 0.5rem;
}

.form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.form-control:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: var(--shell-yellow);
    color: white;
    box-shadow: 0 0 0 0.2rem rgba(253, 184, 19, 0.25);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-actions {
    margin-top: 1.5rem;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.indicator {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    margin-bottom: 1rem;
}

.indicator-label {
    color: var(--shell-yellow);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.indicator-value {
    font-size: 1.5rem;
    font-weight: 700;
    font-family: 'Share Tech Mono', monospace;
}

.indicator-value.negative {
    color: var(--shell-red);
}

.indicator-value.positive {
    color: var(--finance-green);
}

/* DateRangePicker Customization */
.daterangepicker {
    background-color: var(--shell-dark);
    border-color: rgba(255, 255, 255, 0.1);
}

.daterangepicker:before,
.daterangepicker:after {
    border-bottom-color: var(--shell-dark);
}

.daterangepicker .calendar-table {
    background-color: var(--shell-dark);
    border: none;
}

.daterangepicker td.off,
.daterangepicker td.off.in-range,
.daterangepicker td.off.start-date,
.daterangepicker td.off.end-date {
    background-color: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.3);
}

.daterangepicker td.available:hover,
.daterangepicker th.available:hover {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
}

.daterangepicker td.active,
.daterangepicker td.active:hover {
    background-color: var(--shell-red);
    color: var(--shell-white);
}

.daterangepicker td.in-range {
    background-color: rgba(237, 28, 36, 0.3);
    color: var(--shell-white);
}

.daterangepicker .calendar-table .next span,
.daterangepicker .calendar-table .prev span {
    border-color: var(--shell-white);
}

.daterangepicker .ranges li {
    color: var(--shell-white);
}

.daterangepicker .ranges li:hover {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
}

.daterangepicker .ranges li.active {
    background-color: var(--shell-red);
    color: var(--shell-white);
}

/* Calculadora de Margem */
.calculator-section {
    background: linear-gradient(135deg, rgba(30, 30, 30, 0.9) 0%, rgba(50, 50, 50, 0.9) 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.calculator-section h3 {
    color: var(--shell-yellow);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    font-family: 'Rajdhani', sans-serif;
    border-bottom: 2px solid var(--shell-yellow);
    padding-bottom: 0.5rem;
}

.calculator-form .form-group {
    margin-bottom: 1.25rem;
}

.calculator-form label {
    color: var(--shell-light);
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: block;
}

.calculator-form .form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--shell-light);
    transition: all 0.3s ease;
}

.calculator-form .form-control:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 0.2rem rgba(253, 184, 19, 0.25);
}

.calculator-results {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 1.25rem;
    margin-top: 1.5rem;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.result-item:last-child {
    border-bottom: none;
}

.result-label {
    color: var(--shell-gray-light);
    font-size: 0.9rem;
}

.result-value {
    color: var(--shell-light);
    font-family: 'Share Tech Mono', monospace;
    font-size: 1.1rem;
    font-weight: 600;
}

.result-value.positive {
    color: var(--finance-green);
}

.result-value.negative {
    color: var(--shell-red);
}

/* Consulta de Impostos */
.tax-section {
    background: linear-gradient(135deg, rgba(30, 30, 30, 0.9) 0%, rgba(50, 50, 50, 0.9) 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.tax-section h3 {
    color: var(--shell-red);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    font-family: 'Rajdhani', sans-serif;
    border-bottom: 2px solid var(--shell-red);
    padding-bottom: 0.5rem;
}

.tax-results {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.tax-item {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
}

.tax-item-label {
    color: var(--shell-gray-light);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.tax-item-value {
    color: var(--shell-light);
    font-family: 'Share Tech Mono', monospace;
    font-size: 1.5rem;
    font-weight: 600;
}

.tax-chart-container {
    margin-top: 2rem;
    height: 300px;
    position: relative;
}

/* Botões personalizados */
.btn-calculate {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    border: none;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-calculate:hover {
    background-color: var(--shell-yellow-hover);
    transform: translateY(-2px);
}

.btn-consult {
    background-color: var(--shell-red);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-consult:hover {
    background-color: var(--shell-red-hover);
    transform: translateY(-2px);
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.calculator-results,
.tax-results {
    animation: fadeIn 0.3s ease-out forwards;
}

/* Calculadora de Margem */
.tool-form {
    padding: 15px 0;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #f8f9fa;
}

.form-group input {
    width: 100%;
    padding: 8px 12px;
    background-color: #424242;
    border: 1px solid #555;
    border-radius: 4px;
    color: #f8f9fa;
}

.form-section {
    margin: 20px 0;
    padding: 15px;
    background-color: rgba(0,0,0,0.1);
    border-radius: 5px;
}

.form-section h4 {
    margin-bottom: 15px;
    font-size: 1rem;
    color: #FDB813;
}

.custo-item {
    margin-bottom: 10px;
}

.input-group {
    display: flex;
    margin-bottom: 10px;
}

.input-group input {
    flex: 1;
    margin-right: 5px;
}

.btn-remove-custo {
    background: #ED1C24;
    color: white;
    border: none;
    border-radius: 4px;
    width: 30px;
    cursor: pointer;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.btn-shell-yellow {
    background-color: #FDB813;
    color: #333;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.btn-outline-shell {
    background-color: transparent;
    color: #FDB813;
    border: 1px solid #FDB813;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}

/* Resultados */
.results-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.result-card {
    background-color: #424242;
    padding: 15px;
    border-radius: 5px;
    text-align: center;
}

.result-title {
    color: #ccc;
    font-size: 0.9rem;
    margin-bottom: 8px;
}

.result-value {
    color: #fff;
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.result-percent {
    color: #FDB813;
    font-weight: 500;
}

.chart-container {
    position: relative;
    width: 100%;
    height: 400px;
    margin-bottom: 1rem;
}

.tool-section {
    margin-bottom: 25px;
}

.tool-section-title {
    margin-bottom: 15px;
    color: #f8f9fa;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.tool-section-title i {
    margin-right: 8px;
    color: #FDB813;
}

/* ----- Efeito de Flip para Ferramentas Financeiras ----- */

.finance-flip-container {
    perspective: 1000px;
    min-height: 600px;
    width: 100%;
    position: relative;
}

.finance-flipper {
    transition: transform 0.8s;
    transform-style: preserve-3d;
    position: relative;
    width: 100%;
    height: 100%;
}

.finance-front, .finance-back {
    backface-visibility: hidden;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    min-height: 600px;
}

.finance-front {
    z-index: 2;
    transform: rotateY(0deg);
}

.finance-back {
    transform: rotateY(180deg);
    background-color: #2a2a2a;
    border-radius: 10px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.finance-flip-container.flipped .finance-flipper {
    transform: rotateY(180deg);
}

/* Barra de navegação de ferramentas */
.tool-nav {
    background: #333;
    border-bottom: 2px solid #ED1C24;
    padding: 10px 20px;
    display: flex;
    justify-content: center;
}

.tool-nav-items {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
}

.tool-nav-item {
    padding: 8px 15px;
    background-color: #424242;
    border-radius: 30px;
    color: #f8f9fa;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
}

.tool-nav-item i {
    margin-right: 8px;
    font-size: 14px;
    color: #FDB813;
}

.tool-nav-item.active {
    background-color: #FDB813;
    color: #333;
}

.tool-nav-item.active i {
    color: #333;
}

.tool-nav-item:hover:not(.active) {
    background-color: #555;
    transform: translateY(-2px);
}

/* Painéis de conteúdo das ferramentas */
.tool-content-container {
    padding: 20px;
    height: calc(100% - 60px);
    overflow-y: auto;
}

.tool-content-panel {
    display: none;
    animation: fadeIn 0.5s ease;
}

.tool-content-panel.active {
    display: block;
}

.tool-content-header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #444;
}

.tool-content-header h3 {
    color: #f8f9fa;
    font-size: 1.4rem;
    display: flex;
    align-items: center;
}

.tool-content-header h3 i {
    margin-right: 10px;
    color: #FDB813;
}

.tool-content-body {
    padding: 10px 0;
}

/* Botão voltar */
#btnVoltarFerramentas {
    margin-left: 15px;
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

#btnVoltarFerramentas i {
    margin-right: 8px;
}

#btnVoltarFerramentas:hover {
    transform: translateX(-3px);
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse-effect {
    animation: pulse 1s infinite;
}

/* Ajustes responsivos para o flip */
@media (max-width: 768px) {
    .finance-flip-container {
        min-height: 800px;
    }

    .finance-front, .finance-back {
        min-height: 800px;
    }

    .tool-nav-items {
        flex-direction: row;
        overflow-x: auto;
        padding-bottom: 10px;
    }

    .tool-nav-item {
        flex-shrink: 0;
    }
}

/* Melhorias visuais para os cards */
.finance-tool-card {
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.finance-tool-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.finance-tool-card:hover::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(253, 184, 19, 0.1);
    z-index: 0;
}

.finance-tool-card:active {
    transform: translateY(0) scale(0.98);
}

.tool-icon i {
    transition: all 0.3s ease;
}

.finance-tool-card:hover .tool-icon i {
    transform: rotate(10deg) scale(1.2);
}

.btn-tool-action {
    transition: all 0.3s ease;
}

.finance-tool-card:hover .btn-tool-action {
    background-color: #ED1C24;
    color: white;
}

/* Efeito de brilho no hover */
@keyframes shine {
    0% { background-position: -100px; }
    40% { background-position: 300px; }
    100% { background-position: 300px; }
}

.finance-tool-card::after {
    content: "";
    position: absolute;
    top: -110%;
    left: -210%;
    width: 200%;
    height: 200%;
    opacity: 0;
    transform: rotate(30deg);
    background: rgba(255, 255, 255, 0.13);
    background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0.13) 0%,
        rgba(255, 255, 255, 0.13) 77%,
        rgba(255, 255, 255, 0.5) 92%,
        rgba(255, 255, 255, 0.0) 100%
    );
}

.finance-tool-card:hover::after {
    opacity: 1;
    top: -30%;
    left: -30%;
    transition-property: left, top, opacity;
    transition-duration: 0.7s, 0.7s, 0.15s;
    transition-timing-function: ease;
}

/* Ajustes para o Card Histórico */
.aviso-normalizacao {
    background-color: rgba(51, 51, 51, 0.7);
    border-left: 3px solid #FDB813;
    padding: 5px 10px;
    margin-bottom: 10px;
    border-radius: 4px;
}

/* Estilos para o arrastar do gráfico */
.chart-container canvas {
    cursor: grab;
}

.chart-container canvas:active {
    cursor: grabbing;
}

/* Remover estilos da tela cheia que não são mais necessários */
.fullscreen-chart {
    display: none;
}

.fullscreen-close {
    display: none;
}

/* Botão de ajuda no título */
.help-icon-wrapper {
    display: inline-flex;
    margin-left: 0.5rem;
    align-items: center;
}

.help-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: none;
    background-color: transparent;
    color: var(--shell-yellow);
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.help-icon:hover {
    color: #fff;
    transform: scale(1.1);
    background-color: rgba(253, 184, 19, 0.1);
}

.help-icon:active {
    transform: scale(0.95);
}

/* Seletores de combustível */
.fuel-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.form-check-inline {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 0.5rem 0.75rem;
    margin-right: 0;
    transition: background-color 0.2s;
}

.form-check-inline:hover {
    background-color: rgba(255, 255, 255, 0.15);
}

.form-check-input {
    margin-right: 5px;
}

.form-check-input:checked + .form-check-label {
    color: var(--shell-yellow);
    font-weight: 500;
}

/* Atalhos de período */
.period-shortcuts {
    display: flex;
    gap: 0.25rem;
}

.period-shortcuts .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}



.chart-footnote {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 0.75rem;
    font-style: italic;
}

/* Card de estatísticas */
.stats-card {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1.25rem;
    height: 100%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--shell-yellow), var(--shell-red));
}

.stats-title {
    font-size: 1.1rem;
    margin-bottom: 1.25rem;
    color: var(--shell-yellow);
    font-weight: 600;
}

.stats-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.stat-item {
    padding: 0.75rem;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    position: relative;
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.stat-name {
    font-weight: 500;
    font-size: 0.9rem;
}

.stat-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    color: #fff;
}

.stat-body {
    padding-left: 0.5rem;
    border-left: 3px solid rgba(255, 255, 255, 0.1);
}

.stat-value-large {
    font-size: 1.5rem;
    font-weight: 700;
    font-family: 'Share Tech Mono', monospace;
    color: #fff;
}

.stat-trend {
    font-size: 0.9rem;
    margin: 0.25rem 0 0.5rem;
}

.stat-trend.positive {
    color: var(--finance-green);
}

.stat-trend.negative {
    color: var(--shell-red);
}

.stat-range {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

.stat-forecast {
    padding: 0.75rem;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
}

.stat-forecast h5 {
    font-size: 0.95rem;
    color: var(--shell-yellow);
    margin-bottom: 0.75rem;
}

.forecast-indicator {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.forecast-label {
    font-size: 0.9rem;
}

.forecast-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    font-size: 0.8rem;
}

.forecast-arrow.up {
    background-color: rgba(40, 167, 69, 0.2);
    color: var(--finance-green);
}

.forecast-arrow.down {
    background-color: rgba(237, 28, 36, 0.2);
    color: var(--shell-red);
}

.forecast-arrow.stable {
    background-color: rgba(253, 184, 19, 0.2);
    color: var(--shell-yellow);
}

.forecast-value {
    font-size: 0.9rem;
    font-weight: 500;
    font-family: 'Share Tech Mono', monospace;
}

/* Card de fatores de influência */
.factors-card {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1.25rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-top: 1rem;
    position: relative;
    overflow: hidden;
}

.factors-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--shell-red), var(--finance-green));
}

.factors-title {
    font-size: 1.1rem;
    margin-bottom: 1.25rem;
    color: var(--shell-yellow);
    font-weight: 600;
}

.factors-content {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

.factor-item {
    display: flex;
    padding: 0.75rem;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    align-items: center;
}

.factor-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.1rem;
    color: var(--shell-yellow);
}

.factor-info {
    flex: 1;
}

.factor-title {
    font-weight: 500;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.factor-value {
    font-size: 1rem;
    font-weight: 600;
    font-family: 'Share Tech Mono', monospace;
    margin-bottom: 0.25rem;
}

.trend-indicator {
    font-size: 0.8rem;
    padding: 0.1rem 0.3rem;
    border-radius: 3px;
}

.trend-indicator.up {
    background-color: rgba(40, 167, 69, 0.2);
    color: var(--finance-green);
}

.trend-indicator.down {
    background-color: rgba(237, 28, 36, 0.2);
    color: var(--shell-red);
}

.trend-indicator.stable {
    background-color: rgba(253, 184, 19, 0.2);
    color: var(--shell-yellow);
}

.factor-correlation {
    font-size: 0.8rem;
}

.factor-correlation.high {
    color: var(--finance-green);
}

.factor-correlation.medium {
    color: var(--shell-yellow);
}

.factor-correlation.low {
    color: var(--shell-red);
}

/* Responsividade para a ferramenta de histórico */
@media (max-width: 992px) {
    .factors-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .factors-content {
        grid-template-columns: 1fr;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .chart-legend {
        margin-top: 0.5rem;
    }
}

/* Estilos para botão de toggle de filtros */
.filter-toggle-container {
    position: absolute;
    right: 15px;
    top: 0;
}

.btn-filter-toggle {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(253, 184, 19, 0.3);
    color: var(--shell-yellow);
    border-radius: 4px;
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-filter-toggle:hover {
    background-color: rgba(253, 184, 19, 0.1);
    border-color: var(--shell-yellow);
}

.btn-filter-toggle .toggle-icon {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.btn-filter-toggle:hover .toggle-icon {
    transform: translateY(2px);
}

/* Estilos para os filtros colapsáveis */
.filtros-container {
    max-height: 200px;
    overflow: hidden;
    transition: max-height 0.5s ease, opacity 0.5s ease, transform 0.5s ease;
    opacity: 1;
    transform: translateY(0);
    margin-bottom: 1.5rem !important;
}

.filtros-container.collapsed {
    max-height: 0;
    opacity: 0;
    transform: translateY(-20px);
    margin: 0 !important;
    padding: 0;
    pointer-events: none;
}

/* Estilos para botão de toggle de correlações */
.btn-toggle-correlacoes {
    background: transparent;
    border: none;
    color: var(--shell-yellow);
    font-size: 0.8rem;
    margin-left: 0.75rem;
    padding: 0.2rem 0.4rem;
    cursor: pointer;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.btn-toggle-correlacoes:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Estilo para conteúdo de correlações colapsáveis */
.factors-content {
    transition: max-height 0.5s ease, opacity 0.3s ease;
    max-height: 500px;
    overflow: hidden;
    opacity: 1;
}

.factors-content.collapsed {
    max-height: 0;
    opacity: 0;
    margin: 0;
    padding: 0;
}








