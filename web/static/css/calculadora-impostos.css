/* Est<PERSON>s da Calculadora de Impostos */
.calculadora-impostos {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.calculadora-impostos .form-group {
    margin-bottom: 1.5rem;
}

.calculadora-impostos label {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    font-weight: 600;
    color: #333333;
    margin-bottom: 0.5rem;
}

.calculadora-impostos .form-control {
    font-family: 'Rajdhani', sans-serif;
    color: #333333;
    border: 1px solid #808080;
    transition: all 0.3s ease;
}

.calculadora-impostos .form-control:focus {
    border-color: #ED1C24;
    box-shadow: 0 0 0 0.2rem rgba(237, 28, 36, 0.25);
}

.calculadora-impostos .btn-shell-red {
    width: 100%;
    padding: 0.75rem 1.5rem;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.calculadora-impostos .alert-shell-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-color: #dc3545;
    color: #dc3545;
    font-family: 'Share Tech Mono', monospace;
}

/* Estilos da Tabela de Resultados */
.calculadora-impostos .table-shell {
    margin-top: 2rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    overflow: hidden;
}

.calculadora-impostos .table-shell thead th {
    background-color: #333333;
    color: #f8f9fa;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 1rem;
}

.calculadora-impostos .table-shell tbody td {
    font-family: 'Share Tech Mono', monospace;
    padding: 1rem;
    border-bottom: 1px solid #808080;
}

/* Estilos dos Totais */
.calculadora-impostos .totais {
    margin-top: 2rem;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
}

.calculadora-impostos .totais h4 {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    color: #333333;
    margin-bottom: 1rem;
}

.calculadora-impostos .totais span {
    font-family: 'Share Tech Mono', monospace;
    font-size: 1.25rem;
}

.calculadora-impostos .totais .valor {
    color: #ED1C24;
    font-weight: 600;
}

/* Animações */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.calculadora-impostos .resultados {
    animation: fadeIn 0.5s ease-out;
}

/* Responsividade */
@media (max-width: 768px) {
    .card-shell {
        padding: 1.5rem;
    }
    
    .table-shell th,
    .table-shell td {
        padding: 0.75rem;
        font-size: 0.875rem;
    }
    
    .total-impostos,
    .valor-final {
        font-size: 1.125rem;
    }
} 