/* Variáveis do Design System Shell */
:root {
    --shell-red: #ED1C24;
    --shell-yellow: #FDB813;
    --shell-dark: #333333;
    --shell-light: #f8f9fa;
    --shell-gray: #808080;
    --shell-success: #28a745;
    --shell-info: #17a2b8;
    --shell-warning: #ffc107;
    --shell-danger: #dc3545;
}

/* Estilos gerais */
.tax-calculator {
    font-family: 'Rajdhani', sans-serif;
    background-color: var(--shell-dark);
    color: var(--shell-light);
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.tax-calculator h2 {
    color: var(--shell-yellow);
    font-family: 'Share Tech Mono', monospace;
    font-size: 1.75rem;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid var(--shell-red);
    padding-bottom: 0.5rem;
}

/* Formulário */
.form-group label {
    color: var(--shell-yellow);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-select, .form-control {
    background-color: #444444;
    border: 1px solid #808080;
    color: #f8f9fa;
    font-family: 'Share Tech Mono', monospace;
    padding: 0.75rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.form-select:focus, .form-control:focus {
    background-color: #4a4a4a;
    border-color: #FDB813;
    box-shadow: 0 0 0 0.2rem rgba(253, 184, 19, 0.25);
    color: #f8f9fa;
}

.form-select option {
    background-color: #444444;
    color: #f8f9fa;
    padding: 10px;
}

/* Botão de cálculo */
.shell-btn {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.shell-btn-yellow {
    background-color: #FDB813;
    color: #333333;
}

.shell-btn-yellow:hover {
    background-color: #ffc107;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(253, 184, 19, 0.3);
}

.shell-btn-red {
    background-color: #ED1C24;
    color: #f8f9fa;
}

.shell-btn-red:hover {
    background-color: #d41920;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(237, 28, 36, 0.3);
}

.shell-btn-grey {
    background-color: #808080;
    color: #f8f9fa;
}

.shell-btn-grey:hover {
    background-color: #666666;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(128, 128, 128, 0.3);
}

/* Cards */
.card-shell {
    background-color: #333333;
    border: 1px solid #808080;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.card-shell:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.card-shell .card-title {
    color: #FDB813;
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.card-shell .card-text {
    color: #f8f9fa;
    font-family: 'Share Tech Mono', monospace;
    margin-bottom: 1.5rem;
}

.card-shell .card-footer {
    border-top: 1px solid #808080;
    padding-top: 1rem;
    text-align: right;
}

/* Status Indicator */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-available {
    background-color: #28a745;
}

.status-updated {
    background-color: #17a2b8;
}

/* Mensagem de erro */
#errorMessage {
    background-color: var(--shell-danger);
    color: var(--shell-light);
    padding: 1rem;
    border-radius: 4px;
    margin: 1rem 0;
    display: none;
}

/* Tabela de resultados */
.results-container {
    display: none;
    margin-top: 2rem;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1.5rem;
}

.table-shell {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--shell-light);
}

.table-shell thead th {
    background-color: var(--shell-red);
    color: var(--shell-light);
    border: none;
    font-family: 'Share Tech Mono', monospace;
    padding: 1rem;
}

.table-shell tbody td {
    border-color: rgba(255, 255, 255, 0.1);
    padding: 0.75rem 1rem;
}

.table-shell tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Totais */
.totals {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.total-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.total-item:last-child {
    margin-bottom: 0;
    font-size: 1.25rem;
    color: var(--shell-yellow);
}

.total-label {
    font-weight: 600;
}

.total-value {
    font-family: 'Share Tech Mono', monospace;
}

/* Animações */
.fade-in {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsividade */
@media (max-width: 768px) {
    .tax-calculator {
        padding: 1rem;
    }

    .tax-calculator h2 {
        font-size: 1.5rem;
    }

    .shell-btn {
        width: 100%;
        margin-top: 1rem;
    }

    .table-shell {
        display: block;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
}

.tax-calculator-modal {
    background-color: #333333;
    color: #f8f9fa;
    font-family: 'Rajdhani', sans-serif;
}

.tax-calculator-modal .modal-content {
    background-color: #333333;
    color: #f8f9fa;
    border: 1px solid #808080;
    border-radius: 8px;
}

.tax-calculator-modal .modal-header {
    border-bottom: 1px solid #808080;
    padding: 1.5rem;
}

.tax-calculator-modal .modal-title {
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.5rem;
    color: #FDB813;
}

.tax-calculator-modal .modal-body {
    padding: 1.5rem;
}

.tax-calculator-modal .form-label {
    font-family: 'Share Tech Mono', monospace;
    color: #f8f9fa;
    margin-bottom: 0.5rem;
}

.tax-calculator-modal .form-control {
    background-color: #444444;
    border: 1px solid #808080;
    color: #f8f9fa;
    font-family: 'Share Tech Mono', monospace;
    transition: border-color 0.3s ease;
}

.tax-calculator-modal .form-control:focus {
    background-color: #444444;
    border-color: #FDB813;
    color: #f8f9fa;
    box-shadow: 0 0 0 0.2rem rgba(253, 184, 19, 0.25);
}

.tax-calculator-modal .btn-shell-red {
    background-color: #ED1C24;
    border-color: #ED1C24;
    color: #f8f9fa;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.tax-calculator-modal .btn-shell-red:hover {
    background-color: #d01920;
    border-color: #d01920;
    transform: translateY(-1px);
}

.tax-calculator-modal .alert {
    font-family: 'Share Tech Mono', monospace;
    border-radius: 4px;
}

.tax-calculator-modal .alert-danger {
    background-color: rgba(220, 53, 69, 0.2);
    border-color: #dc3545;
    color: #f8f9fa;
}

.tax-calculator-modal .results-table {
    width: 100%;
    margin-top: 1.5rem;
    border: 1px solid #808080;
    border-radius: 4px;
    overflow: hidden;
}

.tax-calculator-modal .results-table th,
.tax-calculator-modal .results-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #808080;
    font-family: 'Share Tech Mono', monospace;
}

.tax-calculator-modal .results-table th {
    background-color: #444444;
    color: #FDB813;
    font-weight: 600;
    text-align: left;
}

.tax-calculator-modal .results-table tbody tr:hover {
    background-color: rgba(253, 184, 19, 0.1);
}

.tax-calculator-modal .results-table .tax-total {
    background-color: #444444;
    font-weight: 600;
}

.tax-calculator-modal .results-table .tax-total td {
    color: #FDB813;
}

/* Animações */
.tax-calculator-modal .fade-enter {
    opacity: 0;
}

.tax-calculator-modal .fade-enter-active {
    opacity: 1;
    transition: opacity 300ms ease-in;
}

.tax-calculator-modal .fade-exit {
    opacity: 1;
}

.tax-calculator-modal .fade-exit-active {
    opacity: 0;
    transition: opacity 300ms ease-in;
}

/* Responsividade */
@media (max-width: 768px) {
    .tax-calculator-modal .modal-body {
        padding: 1rem;
    }

    .tax-calculator-modal .results-table {
        display: block;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
}

.tax-results-table {
    width: 100%;
    margin-top: 1.5rem;
    border-collapse: collapse;
    font-family: 'Rajdhani', sans-serif;
}

.tax-results-table th {
    background-color: #ED1C24;
    color: #f8f9fa;
    font-family: 'Share Tech Mono', monospace;
    padding: 0.75rem;
    text-align: left;
}

.tax-results-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #808080;
}

.tax-results-table tbody tr:hover {
    background-color: rgba(253, 184, 19, 0.1);
}

.tax-results-total {
    background-color: #FDB813;
    color: #333333;
    font-weight: bold;
}

.tax-results-total td {
    border-bottom: none;
}

.tax-calculator-modal .tax-results-table {
    width: 100%;
    margin-top: 20px;
    border: 1px solid #808080;
    border-radius: 4px;
    overflow: hidden;
}

.tax-calculator-modal .tax-results-table th {
    background-color: #FDB813;
    color: #333333;
    font-family: 'Share Tech Mono', monospace;
    padding: 10px;
    text-align: left;
}

.tax-calculator-modal .tax-results-table td {
    padding: 10px;
    border-top: 1px solid #808080;
    font-family: 'Share Tech Mono', monospace;
}

.tax-calculator-modal .tax-results-table tr:nth-child(even) {
    background-color: #444444;
}

.tax-calculator-modal .tax-results-total {
    background-color: #ED1C24 !important;
    color: #f8f9fa;
    font-weight: 600;
}

.tax-calculator-modal .table {
    margin-bottom: 0;
    border-color: #333;
}

.tax-calculator-modal .table th,
.tax-calculator-modal .table td {
    padding: 0.5rem;
    vertical-align: middle;
    border-color: #333;
}

.tax-calculator-modal .table-warning {
    --bs-table-bg: #FDB813;
    --bs-table-striped-bg: #FDB813;
    --bs-table-active-bg: #FDB813;
    --bs-table-hover-bg: #FDB813;
}

#taxResultContainer {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
} 