/* Estilos específicos para a página inicial */
.shell-theme {
    background-color: #f8f9fa;
    color: #333333;
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    position: relative;
    background-image: url('/static/images/shell-background.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
}

.hero-section .container {
    position: relative;
    z-index: 1;
}

/* Cards */
.card-shell {
    background-color: white;
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-shell:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.card-shell .card-title {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    color: #333333;
}

.card-shell .card-text {
    color: #666666;
    font-size: 0.9rem;
}

/* <PERSON><PERSON>ões */
.shell-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.25rem;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.shell-btn-red {
    background-color: #ED1C24;
    color: white;
    border: none;
}

.shell-btn-red:hover {
    background-color: #c4181e;
    color: white;
    transform: translateY(-2px);
}

.shell-btn-yellow {
    background-color: #FDB813;
    color: #333333;
    border: none;
}

.shell-btn-yellow:hover {
    background-color: #e6a611;
    color: #333333;
    transform: translateY(-2px);
}

/* Responsividade */
@media (max-width: 768px) {
    .hero-section {
        padding: 2rem;
    }
    
    .shell-btn {
        width: 100%;
        margin-bottom: 1rem;
    }

    .card-shell {
        margin-bottom: 1rem;
    }
} 