/* Variáveis do Design System Shell */
:root {
    --shell-red: #ED1C24;
    --shell-red-hover: #CE181F;
    --shell-red-light: #ff6b6e;
    --shell-yellow: #FDB813;
    --shell-yellow-hover: #e0a100;
    --shell-yellow-light: #ffe07a;
    --shell-dark: #333333;
    --shell-dark-hover: #222222;
    --shell-light: #f8f9fa;
    --shell-light-hover: #e9ecef;
    --shell-gray: #808080;
    --shell-gray-light: #d9d9d9;
    --shell-success: #28a745;
    --shell-info: #17a2b8;
    --shell-warning: #ffc107;
    --shell-danger: #dc3545;
}

/* Estilos gerais */
body {
    font-family: 'Rajdhani', sans-serif;
    background-color: var(--shell-dark);
    color: var(--shell-light);
}

h1, h2, h3, h4, h5, h6 {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    font-weight: 600;
}

/* Estilos para cards */
.card-shell {
    background-color: rgba(30, 30, 30, 0.7);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    transition: all 0.3s ease;
}

.card-shell:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.card-shell .card-header {
    background-color: rgba(237, 28, 36, 0.1);
    border-bottom: 2px solid var(--shell-red);
    padding: 15px 20px;
}

.card-shell .card-header h3 {
    color: var(--shell-yellow);
    margin: 0;
    font-size: 1.25rem;
}

.card-shell .card-body {
    padding: 20px;
}

/* Estilos para formulários */
.form-label {
    color: var(--shell-yellow);
    font-weight: 500;
    margin-bottom: 8px;
}

.form-control, .form-select {
    background-color: #444444;
    border: 1px solid #555555;
    color: var(--shell-light);
    border-radius: 6px;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    background-color: #444444;
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 0.25rem rgba(253, 184, 19, 0.25);
    color: var(--shell-light);
}

/* Estilos para botões */
.btn-shell-red {
    background-color: var(--shell-red);
    color: var(--shell-light);
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-shell-red:hover {
    background-color: var(--shell-red-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(237, 28, 36, 0.3);
}

.btn-shell-yellow {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-shell-yellow:hover {
    background-color: var(--shell-yellow-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(253, 184, 19, 0.3);
}

/* Estilos para cards de impostos */
.imposto-card {
    background-color: #444444;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    position: relative;
}

.imposto-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.imposto-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.imposto-header h4 {
    margin: 0;
    color: var(--shell-light);
    font-weight: 600;
    font-size: 1.1rem;
}

.imposto-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.imposto-body {
    padding: 15px;
}

.imposto-valor {
    font-family: 'Share Tech Mono', monospace;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.imposto-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.imposto-descricao {
    font-size: 0.85rem;
    color: var(--shell-gray-light);
}

.imposto-porcentagem {
    font-family: 'Share Tech Mono', monospace;
    font-weight: 600;
}

.imposto-barra {
    height: 6px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.imposto-progresso {
    height: 100%;
    width: 0;
    transition: width 1s ease-out;
}

/* Estilos específicos para cada tipo de imposto */
.imposto-icms .imposto-header {
    background-color: var(--shell-red);
}

.imposto-icms .imposto-valor,
.imposto-icms .imposto-porcentagem {
    color: var(--shell-red-light);
}

.imposto-icms .imposto-progresso {
    background-color: var(--shell-red);
}

.imposto-pis .imposto-header {
    background-color: var(--shell-info);
}

.imposto-pis .imposto-valor,
.imposto-pis .imposto-porcentagem {
    color: var(--shell-info);
}

.imposto-pis .imposto-progresso {
    background-color: var(--shell-info);
}

.imposto-cofins .imposto-header {
    background-color: var(--shell-warning);
}

.imposto-cofins .imposto-valor,
.imposto-cofins .imposto-porcentagem {
    color: var(--shell-warning);
}

.imposto-cofins .imposto-progresso {
    background-color: var(--shell-warning);
}

.imposto-cide .imposto-header {
    background-color: var(--shell-success);
}

.imposto-cide .imposto-valor,
.imposto-cide .imposto-porcentagem {
    color: var(--shell-success);
}

.imposto-cide .imposto-progresso {
    background-color: var(--shell-success);
}

.imposto-total .imposto-header {
    background-color: var(--shell-yellow);
}

.imposto-total .imposto-header h4 {
    color: var(--shell-dark);
}

.imposto-total .imposto-valor,
.imposto-total .imposto-porcentagem {
    color: var(--shell-yellow);
}

.imposto-total .imposto-progresso {
    background-color: var(--shell-yellow);
}

/* Estilos para tabelas */
.table-shell {
    color: var(--shell-light);
    margin-bottom: 0;
}

.table-shell thead th {
    background-color: rgba(237, 28, 36, 0.1);
    color: var(--shell-yellow);
    border-color: #555555;
    font-weight: 600;
    padding: 12px 15px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.table-shell tbody td {
    border-color: #555555;
    padding: 12px 15px;
}

.table-shell tbody tr:hover {
    background-color: rgba(253, 184, 19, 0.05);
}

.table-shell tfoot th {
    background-color: rgba(253, 184, 19, 0.1);
    color: var(--shell-yellow);
    border-color: #555555;
    font-weight: 600;
    padding: 12px 15px;
}

/* Estilos para accordion */
.accordion-item {
    background-color: transparent;
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 10px;
}

.accordion-button {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
}

.accordion-button:not(.collapsed) {
    background-color: rgba(237, 28, 36, 0.1);
    color: var(--shell-yellow);
}

.accordion-button:focus {
    box-shadow: none;
    border-color: var(--shell-yellow);
}

.accordion-body {
    font-size: 0.9rem;
    line-height: 1.6;
}

/* Animações */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#resultados {
    animation: fadeIn 0.5s ease-out;
}

/* Responsividade */
@media (max-width: 768px) {
    .card-shell .card-header {
        padding: 12px 15px;
    }
    
    .card-shell .card-body {
        padding: 15px;
    }
    
    .imposto-valor {
        font-size: 1.5rem;
    }
    
    .imposto-descricao {
        font-size: 0.8rem;
    }
    
    .table-shell thead th,
    .table-shell tbody td,
    .table-shell tfoot th {
        padding: 8px 10px;
        font-size: 0.9rem;
    }
}
