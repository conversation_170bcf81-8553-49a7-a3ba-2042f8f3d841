:root {
    --shell-red: #ED1C24;
    --shell-yellow: #FDB813;
}

body {
    background: url('/static/images/image.jpeg') no-repeat center center fixed;
    background-size: cover;
    min-height: 100vh;
    color: white;
    position: relative;
}
.content-wrapper {
    background: rgba(0, 0, 0, 0.6);
    min-height: calc(100vh - 80px);
    padding-bottom: 30px;
}

.hero-section {
    background: radial-gradient(circle at center, rgba(0, 0, 0, 0.7) 0%, transparent 70%);
    min-height: 600px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 50px 0;
}
.service-card {
    background: linear-gradient(145deg, rgba(237, 28, 36, 0.4), rgba(253, 184, 19, 0.4));
    border-radius: 15px;
    transition: all 0.5s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(8px);
}
.service-card::before {
    background: linear-gradient(145deg, rgba(253, 184, 19, 0.3), rgba(237, 28, 36, 0.3));
}
.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg, rgba(253, 183, 19, 0.251), rgba(16, 35, 17, 0.7));
    opacity: 0;
    transition: opacity 0.5s ease;
}
@keyframes titleAnimation {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}
.animated-title {
    animation: titleAnimation 3s infinite ease-in-out;
    background: linear-gradient(45deg, var(--shell-red), var(--shell-yellow));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 3px 3px 6px rgba(0,0,0,0.5);
}
.service-card {
    background: linear-gradient(145deg, rgba(111, 20, 23, 0.593), rgba(127, 99, 32, 0.645));
    border-radius: 15px;
    transition: all 0.5s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    transform: translateY(0);
    backdrop-filter: blur(5px);
}
.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg, rgba(99, 106, 95, 0.8), rgba(125, 33, 36, 0.8));
    opacity: 0;
    transition: opacity 0.5s ease;
}
.service-card:hover::before {
    opacity: 1;
}
.service-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}
.service-card > * {
    position: relative;
    z-index: 1;
}
.shell-btn {
    background: linear-gradient(45deg, var(--shell-red), var(--shell-yellow));
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    transition: all 0.4s ease;
    font-weight: bold;
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-decoration: none;
}
.shell-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(97, 22, 22, 0.117);
    color: white;
}
.shell-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--shell-yellow), var(--shell-red));
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: -1;
}
.shell-btn:hover::after {
    opacity: 1;
}
.icon-feature {
    font-size: 2.5rem;
    background: linear-gradient(45deg, var(--shell-red), var(--shell-yellow));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}
.feature-card {
    background: rgba(0, 0, 0, 0.7);
    border-radius: 15px;
    padding: 25px;
    transition: all 0.4s ease;
    height: 100%;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(81, 23, 23, 0.133);
}
.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    background: rgba(0, 0, 0, 0.8);
}
footer {
    background-color: rgba(0, 0, 0, 0.8);
    color: #bbb;
    padding: 20px 0;
    position: absolute;
    bottom: 0;
    width: 100%;
}
