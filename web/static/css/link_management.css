/* Estilos para a página de Gerenciamento de Relações */

:root {
    --shell-red: #ED1C24;
    --shell-red-hover: #CE181F;
    --shell-red-light: #ff6b6e;
    --shell-yellow: #FDB813;
    --shell-yellow-hover: #e0a100;
    --shell-yellow-light: #ffe07a;
    --shell-dark: #333333;
    --shell-dark-hover: #222222;
    --shell-light: #f8f9fa;
    --shell-light-hover: #e9ecef;
    --shell-gray: #808080;
    --shell-gray-light: #d9d9d9;
}

/* Estilos gerais da página */
.link-management-container {
    padding: 20px;
    color: var(--shell-light);
}

.page-header {
    margin-bottom: 25px;
    border-bottom: 1px solid rgba(253, 184, 19, 0.3);
    padding-bottom: 15px;
}

.page-header h1 {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    color: var(--shell-light);
    margin-bottom: 5px;
}

.page-header p {
    color: var(--shell-gray-light);
    font-size: 1rem;
}

/* Estilos das abas */
.nav-tabs {
    border-bottom: 1px solid rgba(253, 184, 19, 0.3);
    margin-bottom: 20px;
}

.nav-tabs .nav-link {
    color: #ffffff;
    border: none;
    border-bottom: 3px solid transparent;
    background-color: transparent;
    padding: 12px 24px;
    font-weight: 700;
    font-family: 'Rajdhani', sans-serif;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.nav-tabs .nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background-color: var(--shell-yellow);
    transition: width 0.3s ease;
}

.nav-tabs .nav-link:hover {
    color: var(--shell-yellow);
    transform: translateY(-2px);
}

.nav-tabs .nav-link:hover::before {
    width: 100%;
}

.nav-tabs .nav-link.active {
    color: var(--shell-yellow);
    background-color: rgba(253, 184, 19, 0.1);
    border-bottom-color: var(--shell-yellow);
    box-shadow: 0 -3px 10px rgba(253, 184, 19, 0.2);
}

.nav-tabs .nav-link i {
    transition: transform 0.3s ease;
}

.nav-tabs .nav-link:hover i {
    transform: scale(1.2);
}

.custom-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}

/* Estilos dos cards */
.card-shell {
    background-color: rgba(30, 30, 30, 0.7);
    border: 1px solid rgba(253, 184, 19, 0.2);
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-shell .card-header {
    background-color: rgba(40, 40, 40, 0.95);
    border-bottom: 1px solid rgba(253, 184, 19, 0.3);
    padding: 15px 20px;
    font-weight: 700;
    color: var(--shell-yellow);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.card-shell .card-body {
    padding: 20px;
}

/* Estilos dos filtros */
.filter-section {
    margin-bottom: 20px;
    padding: 15px;
    background-color: rgba(30, 30, 30, 0.5);
    border-radius: 8px;
    border: 1px solid rgba(253, 184, 19, 0.1);
}

.filter-section .form-label {
    color: var(--shell-yellow);
    font-weight: 600;
    margin-bottom: 8px;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

.filter-section .form-control,
.filter-section .form-select {
    background-color: rgba(40, 40, 40, 0.8);
    border: 1px solid rgba(253, 184, 19, 0.3);
    color: #ffffff;
    font-weight: 500;
    padding: 10px 15px;
}

.filter-section .form-control:focus,
.filter-section .form-select:focus {
    background-color: rgba(30, 30, 30, 0.9);
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 0.25rem rgba(253, 184, 19, 0.25);
}

/* Estilos da tabela */
.table-shell {
    width: 100%;
    color: var(--shell-light);
    border-collapse: separate;
    border-spacing: 0;
}

.table-shell thead th {
    background-color: rgba(40, 40, 40, 0.95);
    color: var(--shell-yellow);
    font-weight: 700;
    padding: 12px 15px;
    border-bottom: 2px solid rgba(253, 184, 19, 0.5);
    text-align: left;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.5px;
}

.table-shell tbody tr {
    background-color: rgba(30, 30, 30, 0.5);
    transition: all 0.3s ease;
    color: #ffffff;
}

.table-shell tbody tr:hover {
    background-color: rgba(40, 40, 40, 0.8);
}

.table-shell td {
    padding: 12px 15px;
    border-bottom: 1px solid rgba(253, 184, 19, 0.2);
    font-weight: 500;
}

/* Estilos dos botões */
.btn-shell-red {
    background-color: var(--shell-red);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 600;
    font-family: 'Rajdhani', sans-serif;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(237, 28, 36, 0.3);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-shell-red::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
    z-index: -1;
}

.btn-shell-red:hover {
    background-color: var(--shell-red-hover);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(237, 28, 36, 0.4);
}

.btn-shell-red:hover::before {
    left: 100%;
}

.btn-shell-yellow {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 600;
    font-family: 'Rajdhani', sans-serif;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(253, 184, 19, 0.3);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-shell-yellow::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
    z-index: -1;
}

.btn-shell-yellow:hover {
    background-color: var(--shell-yellow-hover);
    color: var(--shell-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(253, 184, 19, 0.4);
}

.btn-shell-yellow:hover::before {
    left: 100%;
}

.btn-shell-dark {
    background-color: var(--shell-dark);
    color: #ffffff;
    border: 1px solid rgba(253, 184, 19, 0.4);
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 700;
    font-family: 'Rajdhani', sans-serif;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

.btn-shell-dark:hover {
    background-color: var(--shell-dark-hover);
    border-color: var(--shell-yellow);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.btn-shell-yellow i, .btn-shell-red i, .btn-shell-dark i {
    transition: transform 0.3s ease;
}

.btn-shell-yellow:hover i, .btn-shell-red:hover i, .btn-shell-dark:hover i {
    transform: scale(1.2);
}

/* Estilos dos modais */
.modal-content {
    background-color: var(--shell-dark);
    color: #ffffff;
    border: 1px solid rgba(253, 184, 19, 0.4);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), 0 0 30px rgba(253, 184, 19, 0.2);
    border-radius: 8px;
    overflow: hidden;
    font-weight: 500;
}

.modal-header {
    border-bottom: 1px solid rgba(253, 184, 19, 0.3);
    background-color: rgba(30, 30, 30, 0.9);
    padding: 15px 20px;
}

.modal-footer {
    border-top: 1px solid rgba(253, 184, 19, 0.3);
    background-color: rgba(30, 30, 30, 0.9);
    padding: 15px 20px;
}

.modal-title {
    color: var(--shell-yellow);
    font-weight: 600;
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.4rem;
    letter-spacing: 0.5px;
}

.modal-body {
    padding: 20px;
    background-color: rgba(30, 30, 30, 0.8);
}

.modal-body p {
    color: #ffffff;
    font-weight: 500;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

.modal .form-label {
    color: var(--shell-yellow);
    font-weight: 500;
    margin-bottom: 8px;
}

.modal .form-select {
    background-color: rgba(40, 40, 40, 0.9);
    border: 1px solid rgba(253, 184, 19, 0.3);
    color: var(--shell-light);
    padding: 10px 15px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.modal .form-select:focus {
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 0.25rem rgba(253, 184, 19, 0.25);
}

.modal .form-select option {
    background-color: var(--shell-dark);
    color: var(--shell-light);
}

/* Estilos para badges */
.badge-shell-yellow {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
}

.badge-shell-red {
    background-color: var(--shell-red);
    color: white;
}

/* Classe para melhorar contraste de texto */
.text-white {
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Estilos para estados vazios */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #ffffff;
    background-color: rgba(30, 30, 30, 0.5);
    border-radius: 8px;
    border: 1px dashed rgba(253, 184, 19, 0.2);
    margin: 20px 0;
    transition: all 0.3s ease;
}

.empty-state:hover {
    background-color: rgba(30, 30, 30, 0.7);
    border-color: rgba(253, 184, 19, 0.4);
    transform: scale(1.01);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.empty-state i {
    font-size: 3.5rem;
    color: var(--shell-yellow);
    margin-bottom: 20px;
    display: inline-block;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.empty-state h4 {
    font-weight: 600;
    margin-bottom: 15px;
    font-family: 'Rajdhani', sans-serif;
    color: var(--shell-yellow);
    font-size: 1.5rem;
}

.empty-state p {
    color: #ffffff;
    margin-bottom: 20px;
    max-width: 80%;
    margin-left: auto;
    margin-right: auto;
    font-weight: 500;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

/* Animações */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsividade */
@media (max-width: 768px) {
    .table-responsive {
        overflow-x: auto;
    }

    .filter-section .row > div {
        margin-bottom: 15px;
    }
}
