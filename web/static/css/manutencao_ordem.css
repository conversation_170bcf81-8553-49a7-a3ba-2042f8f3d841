/* Estilos para a página ManutencaoOrdem do técnico */

:root {
    --shell-red: #ED1C24;
    --shell-yellow: #FDB813;
    --shell-dark: #1a1a1a;
    --shell-medium-dark: #2a2a2a;
    --shell-light-dark: #333333;
    --shell-border: #444;
    --shell-text: #f8f9fa;
}

/* Estilos gerais da página */
.content-with-sidebar {
    margin-left: var(--sidebar-width);
    transition: margin-left 0.3s ease;
    min-height: 100vh;
    background-color: var(--shell-dark);
}

.tradicio-sidebar.collapsed + .content-with-sidebar {
    margin-left: var(--sidebar-width-collapsed);
}

.calendar-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
    color: var(--shell-text);
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    background: linear-gradient(90deg, #222, #111);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid var(--shell-yellow);
    box-shadow: 0 3px 6px rgba(0,0,0,0.2);
}

.calendar-header h1 {
    color: var(--shell-yellow);
    margin: 0;
    font-size: 1.8rem;
    font-weight: bold;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
}

.calendar-header div {
    color: var(--shell-text);
}

.calendar-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

#monthYearText {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--shell-yellow);
    min-width: 150px;
    text-align: center;
}

.calendar-content {
    margin-top: 1rem;
    perspective: 1000px;
}

/* Estilos para o flip card */
.flip-card {
    width: 100%;
    transition: transform 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform-style: preserve-3d;
    position: relative;
    min-height: 500px;
}

.flip-card.flipped {
    transform: rotateY(180deg);
}

.flip-card-front,
.flip-card-back {
    backface-visibility: hidden;
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    border-radius: 12px;
    overflow: hidden;
}

.flip-card-front {
    background-color: var(--shell-medium-dark);
    border: 2px solid var(--shell-yellow);
    padding: 20px;
    z-index: 1;
}

.flip-card-back {
    transform: rotateY(180deg);
    padding: 20px;
    background: linear-gradient(135deg, rgba(50, 30, 15, 0.8), rgba(20, 20, 20, 0.85));
    border: 2px solid var(--shell-yellow);
    box-shadow: 0 10px 25px rgba(0,0,0,0.3);
    z-index: 2;
}

/* Estilos para o calendário */
.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    margin-bottom: 20px;
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    margin-bottom: 8px;
}

.calendar-weekdays div {
    text-align: center;
    font-weight: bold;
    color: var(--shell-yellow);
    padding: 10px;
    background: linear-gradient(180deg, #333, #222);
    border-radius: 5px;
    border: 1px solid #444;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.calendar-day {
    background: rgba(50, 50, 50, 0.5);
    border-radius: 8px;
    padding: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(100, 100, 100, 0.3);
    color: #fff;
    position: relative;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.calendar-day:hover {
    background: rgba(70, 70, 70, 0.7);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    border-color: var(--shell-yellow);
}

.calendar-day.has-event {
    background: rgba(237, 28, 36, 0.2);
    border-color: rgba(237, 28, 36, 0.5);
}

.calendar-day.today {
    background: rgba(244, 179, 29, 0.2);
    border: 2px solid var(--shell-yellow);
    font-weight: bold;
}

.calendar-day .event-indicator {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 8px;
    height: 8px;
    background: var(--shell-red);
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 0.7; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
    100% { opacity: 0.7; transform: scale(1); }
}

/* Estilos para os cards de detalhes */
.cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.card {
    background: var(--shell-medium-dark);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    padding: 20px;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--shell-border);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.3);
    border-color: var(--shell-yellow);
}

.card h2 {
    margin-top: 0;
    font-size: 1.2rem;
    margin-bottom: 15px;
    border-bottom: 1px solid var(--shell-yellow);
    padding-bottom: 10px;
    color: var(--shell-yellow);
}

.card textarea,
.card input[type="text"],
.card input[type="number"],
.card input[type="file"] {
    width: 100%;
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid var(--shell-border);
    border-radius: 4px;
    resize: vertical;
    background-color: var(--shell-light-dark);
    color: var(--shell-text);
}

.card textarea:focus,
.card input[type="text"]:focus,
.card input[type="number"]:focus {
    outline: none;
    border-color: var(--shell-yellow);
}

.card button {
    align-self: flex-end;
    padding: 8px 16px;
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.card button:hover {
    background-color: #e0a100;
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(0,0,0,0.2);
}

.card button:disabled {
    background-color: #aaa;
    cursor: not-allowed;
}

/* Estilos para o card de chat */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    border: 1px solid var(--shell-border);
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 4px;
    background: var(--shell-light-dark);
    min-height: 150px;
    max-height: 250px;
}

/* Estilos para grupos de formulário */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: var(--shell-yellow);
    font-weight: 500;
}

.chat-input {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.chat-input textarea {
    flex: 1;
    resize: none;
    min-height: 80px;
}

/* Instrução para clicar no calendário */
.flip-instruction {
    text-align: center;
    margin-top: 15px;
    padding: 10px;
    color: var(--shell-yellow);
    animation: pulse 2s infinite;
    font-size: 0.9rem;
}

.flip-instruction i {
    font-size: 18px;
    margin-bottom: 5px;
    color: var(--shell-yellow);
}

.fa-bounce {
    animation: bounce 1s ease infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {transform: translateY(0);}
    40% {transform: translateY(-8px);}
    60% {transform: translateY(-4px);}
}

/* Estilos para ordens no calendário */
.order-item {
    background: rgba(237, 28, 36, 0.2);
    border: 1px solid rgba(237, 28, 36, 0.5);
    border-radius: 4px;
    padding: 5px;
    margin-top: 5px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #fff;
    text-align: left;
    width: 100%;
}

.order-item:hover {
    background: rgba(237, 28, 36, 0.4);
    transform: scale(1.05);
}

/* Responsividade */
@media (max-width: 768px) {
    .cards-container {
        grid-template-columns: 1fr;
    }

    .calendar-container {
        padding: 0.5rem;
    }

    .calendar-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .calendar-day {
        min-height: 50px;
        padding: 5px;
    }
}

/* Botão para voltar ao calendário */
.back-button {
    background-color: var(--shell-red);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    margin-bottom: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    font-weight: bold;
}

.back-button:hover {
    background-color: #c51017;
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(0,0,0,0.2);
}

/* Mensagem de chat */
.chat-message {
    margin-bottom: 10px;
    padding: 8px 12px;
    border-radius: 8px;
    background-color: var(--shell-medium-dark);
    border-left: 3px solid var(--shell-yellow);
}

.chat-message-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 0.85rem;
}

.chat-message-sender {
    font-weight: bold;
    color: var(--shell-yellow);
}

.chat-message-time {
    color: #aaa;
}

.chat-message-content {
    color: var(--shell-text);
}
