/* Reset e estilos gerais */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Cores do tema Shell */
  --shell-red: #DD1D21;
  --shell-yellow: #FFCC00;
  --shell-dark-red: #A40000;
  --shell-light-yellow: #FFE566;
  
  /* Cores complementares */
  --white: #FFFFFF;
  --off-white: #F8F9FA;
  --light-gray: #E9ECEF;
  --medium-gray: #CED4DA;
  --dark-gray: #6C757D;
  --black: #212529;
  
  /* Cores para status */
  --status-pending: #FF6600;
  --status-in-progress: #3366CC;
  --status-completed: #33AA33;
  --status-cancelled: #CC3333;
  
  /* Cores para prioridades */
  --priority-low: #d4edda;
  --priority-medium: #fff3cd;
  --priority-high: #f8d7da;
  --priority-critical: #dc3545;
  
  /* Espaçamento */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  
  /* Fontes */
  --font-family: 'Arial', sans-serif;
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.25rem;
  --font-size-xl: 1.5rem;
  
  /* Sombras */
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  
  /* Bordas */
  --border-radius: 0.25rem;
  --border-radius-lg: 0.5rem;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--black);
  background-color: var(--off-white);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

a {
  color: var(--shell-red);
  text-decoration: none;
}

a:hover {
  color: var(--shell-dark-red);
  text-decoration: underline;
}

/* Layout principal */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

.main-content {
  flex: 1;
  padding: var(--space-lg) 0;
}

/* Header */
.header {
  background-color: var(--white);
  box-shadow: var(--shadow-sm);
  padding: var(--space-md) 0;
  border-bottom: 5px solid var(--shell-yellow);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
}

.logo img {
  height: 40px;
  margin-right: var(--space-md);
}

.logo h1 {
  color: var(--shell-red);
  font-size: var(--font-size-xl);
  margin: 0;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-info .avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--shell-yellow);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: var(--space-sm);
  font-weight: bold;
  color: var(--black);
}

/* Sidebar */
.sidebar {
  position: fixed;
  top: 70px;
  left: 0;
  bottom: 0;
  width: 250px;
  background-color: var(--white);
  box-shadow: var(--shadow-sm);
  z-index: 1000;
  overflow-y: auto;
  transition: transform 0.3s ease;
}

.sidebar-menu {
  list-style: none;
  padding: var(--space-md);
}

.sidebar-menu li {
  margin-bottom: var(--space-md);
}

.sidebar-menu a {
  display: flex;
  align-items: center;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--border-radius);
  color: var(--black);
  transition: all 0.2s ease;
}

.sidebar-menu a i {
  margin-right: var(--space-sm);
  color: var(--shell-red);
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
  background-color: var(--shell-yellow);
  color: var(--black);
  text-decoration: none;
}

.sidebar-toggle {
  display: none;
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--shell-red);
  color: var(--white);
  border: none;
  box-shadow: var(--shadow-md);
  z-index: 1001;
  cursor: pointer;
  font-size: 1.5rem;
  text-align: center;
  line-height: 50px;
}

/* Área principal com sidebar */
.content-with-sidebar {
  margin-left: 250px;
  padding: var(--space-md);
  transition: margin 0.3s ease;
}

/* Forms */
.form-card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
}

.form-group {
  margin-bottom: var(--space-md);
}

.form-label {
  display: block;
  margin-bottom: var(--space-xs);
  font-weight: bold;
}

.form-control {
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--black);
  background-color: var(--white);
  border: 1px solid var(--medium-gray);
  border-radius: var(--border-radius);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: var(--shell-yellow);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(255, 204, 0, 0.25);
}

.form-select {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--black);
  background-color: var(--white);
  border: 1px solid var(--medium-gray);
  border-radius: var(--border-radius);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
}

.form-check {
  display: block;
  margin-bottom: var(--space-sm);
}

.form-check-input {
  margin-right: var(--space-xs);
}

/* Botões */
.btn {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: var(--font-size-md);
  line-height: 1.5;
  border-radius: var(--border-radius);
  transition: all 0.15s ease-in-out;
}

.btn-primary {
  color: var(--white);
  background-color: var(--shell-red);
  border-color: var(--shell-red);
}

.btn-primary:hover {
  background-color: var(--shell-dark-red);
  border-color: var(--shell-dark-red);
}

.btn-secondary {
  color: var(--black);
  background-color: var(--shell-yellow);
  border-color: var(--shell-yellow);
}

.btn-secondary:hover {
  background-color: var(--shell-light-yellow);
  border-color: var(--shell-light-yellow);
}

.btn-danger {
  color: var(--white);
  background-color: var(--status-cancelled);
  border-color: var(--status-cancelled);
}

.btn-success {
  color: var(--white);
  background-color: var(--status-completed);
  border-color: var(--status-completed);
}

.btn-info {
  color: var(--white);
  background-color: var(--status-in-progress);
  border-color: var(--status-in-progress);
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: var(--font-size-sm);
}

.btn-group {
  display: flex;
  gap: var(--space-sm);
}

/* Cards */
.card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--space-md);
  overflow: hidden;
}

.card-header {
  padding: var(--space-md);
  background-color: var(--light-gray);
  border-bottom: 1px solid var(--medium-gray);
  font-weight: bold;
}

.card-body {
  padding: var(--space-md);
}

.card-footer {
  padding: var(--space-md);
  background-color: var(--light-gray);
  border-top: 1px solid var(--medium-gray);
}

/* Tabelas */
.table-container {
  overflow-x: auto;
  margin-bottom: var(--space-lg);
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: var(--space-sm);
  text-align: left;
  border-bottom: 1px solid var(--medium-gray);
}

.table th {
  background-color: var(--light-gray);
  font-weight: bold;
}

.table tbody tr:hover {
  background-color: var(--light-gray);
}

/* Status e prioridades */
.status {
  display: inline-block;
  padding: 0.25em 0.6em;
  font-size: 0.75em;
  font-weight: bold;
  border-radius: 0.25rem;
  text-transform: uppercase;
}

.status-pendente {
  background-color: var(--status-pending);
  color: var(--white);
}

.status-em_andamento {
  background-color: var(--status-in-progress);
  color: var(--white);
}

.status-concluida {
  background-color: var(--status-completed);
  color: var(--white);
}

.status-cancelada {
  background-color: var(--status-cancelled);
  color: var(--white);
}

.priority {
  display: inline-block;
  padding: 0.25em 0.6em;
  font-size: 0.75em;
  font-weight: bold;
  border-radius: 0.25rem;
  text-transform: uppercase;
}

.priority-baixa {
  background-color: var(--priority-low);
  color: #155724;
}

.priority-media {
  background-color: var(--priority-medium);
  color: #856404;
}

.priority-alta {
  background-color: var(--priority-high);
  color: #721c24;
}

.priority-critica {
  background-color: var(--priority-critical);
  color: var(--white);
}

/* Dashboard */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.stat-card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  padding: var(--space-md);
  display: flex;
  flex-direction: column;
  border-left: 4px solid var(--shell-yellow);
}

.stat-card.pending {
  border-left-color: var(--status-pending);
}

.stat-card.in-progress {
  border-left-color: var(--status-in-progress);
}

.stat-card.completed {
  border-left-color: var(--status-completed);
}

.stat-card.cancelled {
  border-left-color: var(--status-cancelled);
}

.stat-title {
  font-size: var(--font-size-sm);
  color: var(--dark-gray);
  margin-bottom: var(--space-xs);
}

.stat-value {
  font-size: var(--font-size-xl);
  font-weight: bold;
}

.stat-subtitle {
  font-size: var(--font-size-sm);
  color: var(--dark-gray);
  margin-top: auto;
}

.chart-container {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  padding: var(--space-md);
  margin-bottom: var(--space-lg);
}

/* Notificações */
.notifications-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1050;
  max-width: 350px;
}

.notification {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  padding: var(--space-md);
  margin-bottom: var(--space-md);
  opacity: 0;
  transform: translateX(50px);
  animation: slide-in 0.3s ease forwards;
  border-left: 4px solid var(--shell-yellow);
}

.notification.error {
  border-left-color: var(--status-cancelled);
}

.notification.success {
  border-left-color: var(--status-completed);
}

.notification.info {
  border-left-color: var(--status-in-progress);
}

.notification.warning {
  border-left-color: var(--status-pending);
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-xs);
}

.notification-title {
  font-weight: bold;
}

.notification-close {
  background: none;
  border: none;
  cursor: pointer;
  font-size: var(--font-size-md);
  color: var(--dark-gray);
}

.notification-body {
  margin-bottom: var(--space-xs);
}

.notification-footer {
  font-size: var(--font-size-sm);
  color: var(--dark-gray);
}

@keyframes slide-in {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Login */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--light-gray);
  background-image: linear-gradient(45deg, rgba(255, 204, 0, 0.1), rgba(221, 29, 33, 0.1));
}

.login-card {
  width: 100%;
  max-width: 400px;
  background-color: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--space-xl);
}

.login-logo {
  text-align: center;
  margin-bottom: var(--space-lg);
}

.login-logo img {
  height: 60px;
}

.login-title {
  text-align: center;
  color: var(--shell-red);
  margin-bottom: var(--space-lg);
}

.login-button {
  width: 100%;
}

/* Manutenção - Detalhes */
.order-details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.order-detail-item {
  margin-bottom: var(--space-md);
}

.order-detail-label {
  font-weight: bold;
  margin-bottom: var(--space-xs);
  color: var(--dark-gray);
}

.order-detail-value {
  font-size: var(--font-size-lg);
}

.order-timeline {
  margin: var(--space-lg) 0;
}

.timeline-item {
  position: relative;
  padding-left: 30px;
  padding-bottom: var(--space-md);
  border-left: 2px solid var(--medium-gray);
}

.timeline-item:last-child {
  border-left: 2px solid transparent;
}

.timeline-item::before {
  content: "";
  position: absolute;
  left: -8px;
  top: 0;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: var(--shell-yellow);
}

.timeline-date {
  font-size: var(--font-size-sm);
  color: var(--dark-gray);
}

.timeline-content {
  background-color: var(--light-gray);
  border-radius: var(--border-radius);
  padding: var(--space-sm);
  margin-top: var(--space-xs);
}

.timeline-user {
  font-weight: bold;
}

/* Footer */
.footer {
  background-color: var(--black);
  color: var(--white);
  padding: var(--space-lg) 0;
  margin-top: auto;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-logo {
  display: flex;
  align-items: center;
}

.footer-logo img {
  height: 30px;
  margin-right: var(--space-md);
}

.footer-links {
  list-style: none;
  display: flex;
}

.footer-links li {
  margin-left: var(--space-md);
}

.footer-links a {
  color: var(--medium-gray);
}

.footer-links a:hover {
  color: var(--shell-yellow);
}

/* Paginação */
.pagination {
  display: flex;
  justify-content: center;
  list-style: none;
  margin: var(--space-lg) 0;
}

.pagination-item {
  margin: 0 var(--space-xs);
}

.pagination-link {
  display: block;
  padding: var(--space-xs) var(--space-sm);
  border: 1px solid var(--medium-gray);
  border-radius: var(--border-radius);
  color: var(--dark-gray);
  min-width: 32px;
  text-align: center;
}

.pagination-link:hover {
  background-color: var(--light-gray);
  text-decoration: none;
}

.pagination-link.active {
  background-color: var(--shell-yellow);
  border-color: var(--shell-yellow);
  color: var(--black);
  font-weight: bold;
}

.pagination-link.disabled {
  color: var(--medium-gray);
  pointer-events: none;
}

/* Responsividade */
@media (max-width: 992px) {
  .content-with-sidebar {
    margin-left: 0;
  }
  
  .sidebar {
    transform: translateX(-100%);
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
  
  .sidebar-toggle {
    display: block;
  }
  
  .dashboard-stats {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .order-details-grid {
    grid-template-columns: 1fr;
  }
  
  .footer-content {
    flex-direction: column;
    text-align: center;
  }
  
  .footer-links {
    margin-top: var(--space-md);
    justify-content: center;
  }
  
  .header-content {
    flex-direction: column;
  }
  
  .user-info {
    margin-top: var(--space-md);
  }
}

@media (max-width: 576px) {
  .btn-group {
    flex-direction: column;
  }
  
  .dashboard-stats {
    grid-template-columns: 1fr;
  }
  
  .login-card {
    padding: var(--space-md);
  }
}
