/* Estilos para a página Editar Perfil */

/* Variáveis de cores */
:root {
    --shell-red: #ED1C24;
    --shell-red-hover: #CE181F;
    --shell-yellow: #FDB813;
    --shell-yellow-hover: #e0a100;
    --shell-dark: #333333;
    --shell-darker: #222222;
    --shell-light: #f8f9fa;
    --shell-gray: #808080;
    --shell-success: #28a745;
    --shell-info: #17a2b8;
    --shell-warning: #ffc107;
    --shell-danger: #dc3545;
}

/* Estilos gerais da página */
body {
    background-color: var(--shell-dark);
    color: var(--shell-light);
    font-family: 'Rajdhani', sans-serif;
}

.container-fluid {
    max-width: 1600px;
    margin: 0 auto;
}

/* Cabe<PERSON><PERSON><PERSON> da página */
.page-header {
    margin-bottom: 2rem;
}

.page-title {
    font-family: 'Raj<PERSON><PERSON>', sans-serif;
    font-weight: 700;
    font-size: 1.8rem;
    color: var(--shell-red);
    margin-bottom: 0.25rem;
}

.page-subtitle {
    font-family: 'Share Tech Mono', monospace;
    font-size: 0.9rem;
    color: var(--shell-yellow);
    opacity: 0.9;
}

/* Cards */
.card-shell {
    background-color: #2a2a2a;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--shell-red);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 1.5rem;
}

.card-shell:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
    border-color: var(--shell-yellow);
}

.card-header-shell {
    background-color: #222222;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--shell-red);
    display: flex;
    align-items: center;
}

.card-body-shell {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.card-title {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--shell-yellow);
    margin: 0;
    text-transform: uppercase;
}

/* Formulário */
.profile-form {
    width: 100%;
}

.form-label {
    color: var(--shell-yellow);
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.form-control, .form-select {
    background-color: #444;
    border: 1px solid #555;
    color: var(--shell-light);
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
}

.form-control:focus, .form-select:focus {
    background-color: #4a4a4a;
    color: var(--shell-light);
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 0.25rem rgba(253, 184, 19, 0.25);
}

.form-text {
    color: #aaa;
    font-size: 0.8rem;
}

.form-check-input {
    background-color: #444;
    border-color: #555;
}

.form-check-input:checked {
    background-color: var(--shell-yellow);
    border-color: var(--shell-yellow);
}

.form-check-label {
    color: var(--shell-light);
    font-size: 0.9rem;
}

/* Seções do formulário */
.section-title {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1.2rem;
    color: var(--shell-yellow);
    margin-bottom: 1rem;
}

hr {
    border-color: rgba(255, 255, 255, 0.1);
}

/* Botões */
.form-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1.5rem;
}

.btn-shell-red {
    background-color: var(--shell-red);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    cursor: pointer;
}

.btn-shell-red:hover {
    background-color: var(--shell-red-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    color: white;
}

.btn-shell {
    background-color: #333333;
    color: var(--shell-light);
    border: 1px solid var(--shell-yellow);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    cursor: pointer;
}

.btn-shell:hover {
    background-color: #444444;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    color: var(--shell-light);
}

/* Avatar */
.avatar-container {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto;
}

.profile-avatar-large {
    width: 120px;
    height: 120px;
    background-color: var(--shell-red);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    font-weight: 700;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border: 2px solid var(--shell-yellow);
}

.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.avatar-container:hover .avatar-overlay {
    opacity: 1;
}

.btn-avatar-edit {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-avatar-edit:hover {
    transform: scale(1.1);
}

.avatar-help {
    color: var(--shell-gray);
    font-size: 0.85rem;
}

/* Opções de Segurança */
.security-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.security-option {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border: 1px solid rgba(237, 28, 36, 0.3);
    transition: all 0.3s ease;
    text-decoration: none;
    color: var(--shell-light);
}

.security-option:hover {
    border-color: var(--shell-red);
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    color: var(--shell-light);
}

.security-icon {
    width: 40px;
    height: 40px;
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.security-content {
    flex-grow: 1;
}

.security-content h4 {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1rem;
    color: var(--shell-yellow);
    margin-bottom: 0.25rem;
}

.security-content p {
    font-size: 0.85rem;
    color: var(--shell-gray);
    margin-bottom: 0;
}

/* Modais */
.modal-shell {
    background-color: #2a2a2a;
    color: var(--shell-light);
    border: 1px solid var(--shell-red);
}

.modal-shell .modal-header {
    background-color: #222222;
    border-bottom: 2px solid var(--shell-red);
    padding: 1rem 1.5rem;
}

.modal-shell .modal-title {
    color: var(--shell-yellow);
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
}

.modal-shell .modal-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 1.5rem;
}

.modal-shell .btn-close {
    color: white;
    opacity: 0.8;
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Responsividade */
@media (max-width: 1199.98px) {
    .container-fluid {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (max-width: 991.98px) {
    .page-title {
        font-size: 1.5rem;
    }
    
    .page-subtitle {
        font-size: 0.85rem;
    }
}

@media (max-width: 767.98px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .page-title {
        font-size: 1.3rem;
    }
    
    .page-subtitle {
        font-size: 0.8rem;
    }
    
    .card-header-shell {
        padding: 0.5rem 0.75rem;
    }
    
    .card-body-shell {
        padding: 1rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions > * {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
