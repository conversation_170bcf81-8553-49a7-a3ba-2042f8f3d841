/* Estilos específicos para módulo de ordens de manutenção */

/* Cores de status */
.status-badge {
    display: inline-block;
    padding: 0.25em 0.6em;
    font-size: 0.75em;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
}

.status-confirmada {
    background-color: #17a2b8;
    color: #fff;
}

.status-em-atendimento {
    background-color: #fd7e14;
    color: #fff;
}

.status-aguardando-pecas {
    background-color: #6c757d;
    color: #fff;
}

.status-pendente-aprovacao {
    background-color: #ffc107;
    color: #212529;
}

.status-aprovada {
    background-color: #20c997;
    color: #fff;
}

.status-rejeitada {
    background-color: #dc3545;
    color: #fff;
}

.status-concluida-tecnicamente {
    background-color: #6610f2;
    color: #fff;
}

.status-validada-filial {
    background-color: #0d6efd;
    color: #fff;
}

.status-concluida {
    background-color: #198754;
    color: #fff;
}

.status-cancelada {
    background-color: #343a40;
    color: #fff;
}

/* Cores de prioridade */
.prioridade-badge {
    display: inline-block;
    padding: 0.25em 0.6em;
    font-size: 0.75em;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
}

.prioridade-baixa {
    background-color: #198754;
    color: #fff;
}

.prioridade-media {
    background-color: #17a2b8;
    color: #fff;
}

.prioridade-alta {
    background-color: #ffc107;
    color: #212529;
}

.prioridade-critica {
    background-color: #dc3545;
    color: #fff;
}

/* Estilos para detalhes da ordem */
.ordem-header {
    padding: 1.5rem;
    border-bottom: 1px solid #dee2e6;
    position: relative;
}

.ordem-header .badge {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
}

.ordem-info {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.ordem-info-item {
    padding: 0.75rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
}

.ordem-info-item .label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.ordem-info-item .value {
    font-weight: 600;
}

.ordem-body {
    padding: 1.5rem;
}

.ordem-timeline {
    position: relative;
    padding-left: 2rem;
}

.ordem-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0.5rem;
    width: 2px;
    background-color: #dee2e6;
}

.timeline-item {
    position: relative;
    padding-bottom: 1.5rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -2rem;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    background-color: #fff;
    border: 2px solid #6c757d;
    z-index: 1;
}

.timeline-item:last-child {
    padding-bottom: 0;
}

.timeline-date {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.timeline-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.timeline-content {
    background-color: #f8f9fa;
    padding: 0.75rem;
    border-radius: 0.375rem;
}

/* Estilos para o formulário de criação */
.dropzone-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dropzone-area:hover {
    border-color: #6c757d;
    background-color: #f8f9fa;
}

/* Estilos para a lista de ordens */
.ordem-list-item {
    border-left: 4px solid #dee2e6;
    transition: all 0.2s ease;
}

.ordem-list-item:hover {
    background-color: #f8f9fa;
}

.ordem-list-item.prioridade-baixa {
    border-left-color: #198754;
}

.ordem-list-item.prioridade-media {
    border-left-color: #17a2b8;
}

.ordem-list-item.prioridade-alta {
    border-left-color: #ffc107;
}

.ordem-list-item.prioridade-critica {
    border-left-color: #dc3545;
}

/* Estilos para a página de ordens */
.table-shell {
    background-color: var(--shell-bg);
    border-radius: 8px;
    overflow: hidden;
}

.table-shell thead th {
    background-color: var(--shell-primary);
    color: white;
    border: none;
    padding: 12px;
}

.table-shell tbody td {
    padding: 12px;
    vertical-align: middle;
}

.table-shell tbody tr:hover {
    background-color: var(--shell-hover);
}

.shell-btn {
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.shell-btn-primary {
    background-color: var(--shell-primary);
    color: white;
}

.shell-btn-info {
    background-color: var(--shell-info);
    color: white;
}

.shell-btn-danger {
    background-color: var(--shell-danger);
    color: white;
}

.shell-btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

/* Estilos para os modais */
.modal-shell .modal-content {
    background-color: var(--shell-bg);
    border-radius: 8px;
}

.modal-shell .modal-header {
    border-bottom: 1px solid var(--shell-border);
}

.modal-shell .modal-footer {
    border-top: 1px solid var(--shell-border);
}

/* Estilos para o formulário de edição */
.form-control {
    background-color: var(--shell-bg);
    border: 1px solid var(--shell-border);
    color: var(--shell-text);
}

.form-control:focus {
    background-color: var(--shell-bg);
    border-color: var(--shell-primary);
    color: var(--shell-text);
    box-shadow: 0 0 0 0.2rem rgba(237, 28, 36, 0.25);
}

.form-label {
    color: var(--shell-text);
}

/* Estilos para mensagens */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 4px;
    color: white;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

.message-success {
    background-color: var(--shell-success);
}

.message-error {
    background-color: var(--shell-danger);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Responsividade */
@media (max-width: 768px) {
    .table-responsive {
        margin: 0 -15px;
    }
    
    .shell-btn {
        padding: 6px 12px;
    }
    
    .message {
        top: 10px;
        right: 10px;
        padding: 10px 15px;
    }
}

/* Estilos Gerais */
.orders-container {
    padding: 2rem;
    background-color: var(--shell-light);
    min-height: 100vh;
}

/* Cabeçalho */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.page-title {
    font-family: 'Rajdhani', sans-serif;
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-bold);
    color: var(--shell-dark);
}

/* Filtros */
.filters-section {
    background-color: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.filter-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.filter-group {
    flex: 1;
}

.filter-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: var(--font-weight-medium);
    color: var(--shell-dark);
}

.filter-control {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--shell-gray-light);
    border-radius: 4px;
    font-family: 'Share Tech Mono', monospace;
}

.filter-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* Métricas */
.metrics-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.metric-card {
    background-color: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.metric-title {
    font-size: var(--font-size-sm);
    color: var(--shell-gray);
    margin-bottom: 0.5rem;
}

.metric-value {
    font-family: 'Rajdhani', sans-serif;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--shell-dark);
}

/* Lista de Ordens */
.orders-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.order-item {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.2s;
}

.order-item:hover {
    transform: translateY(-2px);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--shell-gray-light);
}

.order-id {
    font-family: 'Share Tech Mono', monospace;
    font-size: var(--font-size-sm);
    color: var(--shell-gray);
}

.order-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.status-pendente {
    background-color: var(--shell-yellow-light);
    color: var(--shell-yellow-hover);
}

.status-em_andamento {
    background-color: var(--shell-red-light);
    color: var(--shell-red-hover);
}

.status-concluida {
    background-color: var(--shell-success);
    color: white;
}

.status-cancelada {
    background-color: var(--shell-gray-light);
    color: var(--shell-gray);
}

.order-body {
    padding: 1rem;
}

.order-title {
    font-family: 'Rajdhani', sans-serif;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--shell-dark);
    margin-bottom: 1rem;
}

.order-meta {
    display: flex;
    gap: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: var(--font-size-sm);
    color: var(--shell-gray);
}

.order-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    padding: 1rem;
    border-top: 1px solid var(--shell-gray-light);
}

/* Paginação */
.pagination-section {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.pagination {
    display: flex;
    gap: 0.5rem;
}

.page-item {
    list-style: none;
}

.page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid var(--shell-gray-light);
    border-radius: 4px;
    color: var(--shell-dark);
    text-decoration: none;
    transition: all 0.2s;
}

.page-link:hover {
    background-color: var(--shell-light);
}

.page-item.active .page-link {
    background-color: var(--shell-yellow);
    border-color: var(--shell-yellow);
    color: white;
}

.page-item.disabled .page-link {
    color: var(--shell-gray-light);
    pointer-events: none;
}

/* Estados */
.empty-state, .error-state {
    text-align: center;
    padding: 2rem;
}

.empty-state i, .error-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.empty-state h5, .error-state h5 {
    font-family: 'Rajdhani', sans-serif;
    font-size: var(--font-size-lg);
    margin-bottom: 0.5rem;
}

.empty-state p, .error-state p {
    color: var(--shell-gray);
}

/* Responsividade */
@media (max-width: 768px) {
    .orders-container {
        padding: 1rem;
    }

    .page-header {
        flex-direction: column;
        gap: 1rem;
    }

    .filter-row {
        flex-direction: column;
    }

    .metrics-section {
        grid-template-columns: 1fr;
    }

    .orders-list {
        grid-template-columns: 1fr;
    }
}