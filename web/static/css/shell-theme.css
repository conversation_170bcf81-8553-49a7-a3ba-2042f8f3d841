/* Shell Theme CSS */
:root {
    --shell-red: #da291c;
    --shell-yellow: #fdb813;
    --shell-black: #222222;
    --shell-white: #ffffff;
    --shell-grey: #f4f4f4;
    --shell-dark-grey: #333333;
}

/* <PERSON><PERSON> es<PERSON>ro (padrão) */
body {
    background-color: var(--shell-dark-grey);
    color: var(--shell-white);
    font-family: 'Roboto', Arial, sans-serif;
}

/* Te<PERSON> claro (opcional) */
body.light-theme {
    background-color: var(--shell-grey);
    color: var(--shell-black);
}

/* Cores de marca */
.shell-bg-red {
    background-color: var(--shell-red);
    color: var(--shell-white);
}

.shell-bg-yellow {
    background-color: var(--shell-yellow);
    color: var(--shell-black);
}

.shell-text-red {
    color: var(--shell-red);
}

.shell-text-yellow {
    color: var(--shell-yellow);
}

/* Botões com tema Shell */
.btn-shell-primary {
    background-color: var(--shell-red);
    border-color: var(--shell-red);
    color: var(--shell-white);
}

.btn-shell-primary:hover {
    background-color: #b82218;
    border-color: #b82218;
    color: var(--shell-white);
}

.btn-shell-secondary {
    background-color: var(--shell-yellow);
    border-color: var(--shell-yellow);
    color: var(--shell-black);
}

.btn-shell-secondary:hover {
    background-color: #e59e00;
    border-color: #e59e00;
    color: var(--shell-black);
}

.btn-shell-outline {
    background-color: transparent;
    border-color: var(--shell-red);
    color: var(--shell-red);
}

.btn-shell-outline:hover {
    background-color: var(--shell-red);
    color: var(--shell-white);
}

/* Cards com tema Shell */
.card-shell {
    border-top: 3px solid var(--shell-red);
    border-radius: 0.25rem;
}

.card-shell .card-header {
    background-color: rgba(218, 41, 28, 0.1);
    border-bottom: 1px solid rgba(218, 41, 28, 0.2);
}

.card-shell-secondary {
    border-top: 3px solid var(--shell-yellow);
    border-radius: 0.25rem;
}

.card-shell-secondary .card-header {
    background-color: rgba(253, 184, 19, 0.1);
    border-bottom: 1px solid rgba(253, 184, 19, 0.2);
}

/* Sidebar */
.sidebar {
    background-color: var(--shell-black);
    color: var(--shell-white);
    min-height: 100vh;
    width: 250px;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1030;
    transition: all 0.3s ease-in-out;
}

.sidebar.collapsed {
    transform: translateX(-250px);
}

.sidebar-header {
    background: linear-gradient(90deg, var(--shell-red), var(--shell-yellow));
    padding: 15px;
    text-align: center;
}

.sidebar-menu {
    padding: 0;
    list-style: none;
}

.sidebar-menu li {
    margin: 0;
    padding: 0;
}

.sidebar-menu li a {
    color: var(--shell-white);
    display: block;
    padding: 12px 20px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.sidebar-menu li a:hover,
.sidebar-menu li a.active {
    background-color: rgba(218, 41, 28, 0.7);
}

.sidebar-menu li a i {
    margin-right: 10px;
}

/* Header */
.main-header {
    background-color: var(--shell-black);
    border-bottom: 1px solid #444;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    padding: 10px 20px;
    position: sticky;
    top: 0;
    z-index: 1020;
}

.light-theme .main-header {
    background-color: var(--shell-white);
    border-bottom: 1px solid #ddd;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Content area */
.content-with-sidebar {
    margin-left: 250px;
    padding: 20px;
    min-height: calc(100vh - 60px);
    transition: all 0.3s ease-in-out;
}

.sidebar.collapsed + .content-with-sidebar {
    margin-left: 0;
}

/* Notificações */
.notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    width: 300px;
}

.notification {
    background-color: var(--shell-black);
    border-left: 4px solid var(--shell-red);
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    margin-bottom: 10px;
    padding: 15px;
    transition: all 0.3s ease;
    color: var(--shell-white);
}

.notification.success {
    border-left-color: #28a745;
}

.notification.info {
    border-left-color: #17a2b8;
}

.notification.warning {
    border-left-color: var(--shell-yellow);
}

.notification.error {
    border-left-color: var(--shell-red);
}

.notification-close {
    cursor: pointer;
    float: right;
    font-size: 16px;
    margin-left: 10px;
}

/* Tamanhos de fonte */
.font-small {
    font-size: 0.85rem;
}

.font-medium {
    font-size: 1rem;
}

.font-large {
    font-size: 1.15rem;
}

/* Toggle sidebar button */
.sidebar-toggle {
    background-color: var(--shell-red);
    border: none;
    border-radius: 50%;
    bottom: 20px;
    color: var(--shell-white);
    cursor: pointer;
    display: none;
    height: 50px;
    position: fixed;
    right: 20px;
    width: 50px;
    z-index: 1040;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* Modais no tema escuro */
.modal-content {
    background-color: #222;
    color: var(--shell-white);
}

.modal-header {
    border-bottom: 1px solid #444;
}

.modal-footer {
    border-top: 1px solid #444;
}

.modal .close {
    color: var(--shell-white);
    text-shadow: none;
    opacity: 0.7;
}

.modal .close:hover {
    color: var(--shell-white);
    opacity: 1;
}

/* Inputs e controles de formulário no tema escuro */
.form-control, .custom-select {
    background-color: #333;
    border-color: #444;
    color: var(--shell-white);
}

.form-control:focus, .custom-select:focus {
    background-color: #333;
    border-color: var(--shell-red);
    color: var(--shell-white);
    box-shadow: 0 0 0 0.2rem rgba(218, 41, 28, 0.25);
}

.input-group-text {
    background-color: #333;
    border-color: #444;
    color: var(--shell-white);
}

/* Tooltips e popovers no tema escuro */
.tooltip-inner {
    background-color: var(--shell-black);
    color: var(--shell-white);
}

.bs-tooltip-top .arrow::before, 
.bs-tooltip-auto[x-placement^="top"] .arrow::before {
    border-top-color: var(--shell-black);
}

.popover {
    background-color: #222;
    border-color: #444;
}

.popover-header {
    background-color: #333;
    border-bottom: 1px solid #444;
    color: var(--shell-white);
}

.popover-body {
    color: var(--shell-white);
}

/* Listas e dropdowns no tema escuro */
.dropdown-menu {
    background-color: #222;
    border-color: #444;
}

.dropdown-item {
    color: var(--shell-white);
}

.dropdown-item:hover, .dropdown-item:focus {
    background-color: #333;
    color: var(--shell-white);
}

.dropdown-divider {
    border-top: 1px solid #444;
}

/* Tabelas no tema escuro */
.table {
    color: var(--shell-white);
}

.table thead th {
    border-bottom: 2px solid #444;
    background-color: #1a1a1a;
}

.table td, .table th {
    border-top: 1px solid #444;
}

.table-bordered {
    border: 1px solid #444;
}

.table-bordered td, .table-bordered th {
    border: 1px solid #444;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.table .thead-dark th {
    background-color: var(--shell-black);
    border-color: #444;
}

/* Paginação no tema escuro */
.pagination {
    margin-top: 1rem;
}

.page-link {
    background-color: #222;
    border-color: #444;
    color: var(--shell-white);
}

.page-link:hover {
    background-color: #333;
    border-color: #444;
    color: var(--shell-white);
}

.page-item.active .page-link {
    background-color: var(--shell-red);
    border-color: var(--shell-red);
    color: var(--shell-white);
}

.page-item.disabled .page-link {
    background-color: #222;
    border-color: #444;
    color: #777;
}

/* Responsive styles */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-250px);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .content-with-sidebar {
        margin-left: 0;
    }
    
    .sidebar-toggle {
        display: block;
    }
}