/* Estilos específicos para a página de ordem técnico */
:root {
    --shell-red: #ED1C24;
    --shell-yellow: #f4b31d;
    --shell-dark: #333333;
    --shell-brown: rgba(50, 25, 7, 0.9);
    --shell-light: #f9f9f9;
}

body {
    background-color: #1a1a1a;
    background-image: linear-gradient(45deg, #111 25%, transparent 25%, transparent 75%, #111 75%, #111),
        linear-gradient(45deg, #111 25%, transparent 25%, transparent 75%, #111 75%, #111);
    background-size: 60px 60px;
    background-position: 0 0, 30px 30px;
}

/* Botão para voltar ao dashboard */
.btn-back-to-dashboard {
    position: absolute;
    top: 20px;
    right: 20px;
    background-color: #FFCC00;
    color: #000;
    padding: 8px 15px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 100;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-back-to-dashboard:hover {
    background-color: #ffcd00;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Estilos específicos para ordens urgentes */
.urgent-styled {
    border-left: 4px solid #dc3545 !important;
    background-color: rgba(220, 53, 69, 0.1);
    box-shadow: 0 3px 6px rgba(220, 53, 69, 0.2);
    position: relative;
}

.urgent-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #dc3545;
    color: white;
    font-size: 10px;
    font-weight: bold;
    padding: 3px 6px;
    border-radius: 3px;
    letter-spacing: 0.5px;
}

/* Estilos para o modal de seleção de ordens */
.order-selection-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.order-selection-modal.active {
    opacity: 1;
    visibility: visible;
}

.order-selection-content {
    background-color: #1e1e1e;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    padding: 20px;
    position: relative;
    border: 2px solid #FFCC00;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.order-selection-modal.active .order-selection-content {
    transform: scale(1);
}

.close-selection-modal {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    color: #fff;
    font-size: 20px;
    cursor: pointer;
}

.order-option {
    display: flex;
    padding: 15px;
    border-bottom: 1px solid #333;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.order-option:hover {
    background-color: #2a2a2a;
}

.order-option:last-child {
    border-bottom: none;
}

.order-indicator {
    width: 10px;
    height: 100%;
    margin-right: 15px;
    border-radius: 3px;
}

.order-details {
    flex: 1;
}

.order-details h4 {
    margin: 0 0 8px 0;
    color: #FFCC00;
}

.order-details p {
    margin: 5px 0;
    color: #ccc;
}

.container-fluid {
    padding: 20px;
}

/* Para corrigir possíveis problemas de escala */
.calendar-dashboard-container {
    transform: scale(1);
    transform-origin: center top;
}

/* Estilos para a seção de ordens em destaque */
.featured-orders-section {
    margin-bottom: 20px;
    padding: 15px;
    background-color: rgba(30, 30, 30, 0.9);
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
    border: 1px solid #444;
}

.featured-orders-section h4 {
    color: var(--shell-yellow);
    margin-bottom: 15px;
    border-bottom: 2px solid var(--shell-yellow);
    padding-bottom: 10px;
}

.featured-orders-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.featured-orders-category h5 {
    font-size: 1rem;
    margin-bottom: 10px;
    color: #eee;
}

.featured-orders-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.featured-order-item {
    display: flex;
    align-items: center;
    background: #222;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    border: 1px solid #333;
}

.featured-order-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.featured-order-indicator {
    width: 10px;
    height: 40px;
    border-radius: 3px;
    margin-right: 10px;
}

.featured-order-indicator.urgent {
    background-color: #dc3545;
}

.featured-order-indicator.scheduled {
    background-color: #0d6efd;
}

.featured-order-content {
    flex-grow: 1;
}

.featured-order-content h6 {
    margin: 0 0 5px 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: #ddd;
}

.featured-order-details {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    font-size: 0.8rem;
    color: #bbb;
}

/* Aumentando o tamanho do calendário após remover o painel lateral */
.calendar-dashboard-layout {
    grid-template-columns: 1fr !important;
}

/* Estilos para o painel do calendário */
.pump-border {
    background-color: rgba(30, 30, 30, 0.8);
    border: 8px solid var(--shell-yellow);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    position: relative;
    overflow: hidden;
}

.pump-border::before {
    content: '';
    position: absolute;
    top: -10px;
    right: 20px;
    width: 30px;
    height: 16px;
    background: var(--shell-red);
    border-radius: 8px 8px 0 0;
    z-index: -1;
}

.pump-border::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    pointer-events: none;
    border-radius: 12px;
}

/* Estilo para o botão de legendas */
.legend-btn {
    background-color: #dc3545 !important;
    border: none;
    border-radius: 4px;
    color: white !important;
    padding: 6px 12px;
    font-size: 0.9rem;
}

.legend-btn:hover {
    background-color: #c82333 !important;
    color: white !important;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
}

.date {
    background: rgba(50, 50, 50, 0.5);
    border-radius: 8px;
    padding: 6px 3px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 1px solid rgba(100, 100, 100, 0.3);
    color: #fff;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
    font-size: 0.9em;
    height: 70px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.date::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    border-radius: 8px;
    pointer-events: none;
}

.date:hover {
    background: rgba(237, 28, 36, 0.7);
    color: white;
    transform: scale(1.08);
    z-index: 5;
    font-weight: bold;
    box-shadow: 0 5px 15px rgba(237, 28, 36, 0.3), inset 0 0 10px rgba(255, 255, 255, 0.1);
}

.date.has-events {
    border: 2px solid var(--shell-red);
    position: relative;
    z-index: 1;
    background: rgba(60, 60, 60, 0.7);
}

.date.empty {
    background: rgba(30, 30, 30, 0.3);
    color: rgba(255, 255, 255, 0.3);
    cursor: default;
    box-shadow: none;
    border-color: rgba(80, 80, 80, 0.2);
}

/* Estilos para indicadores de eventos no calendário */
.event-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    position: absolute;
    bottom: 4px;
    left: 50%;
    transform: translateX(-50%);
}

.event-indicator.waiting {
    background-color: #17a2b8; /* info */
}

.event-indicator.approved {
    background-color: #28a745; /* success */
}

.event-indicator.approved-scheduled {
    background-color: #28a745; /* success */
    opacity: 0.7;
}

.event-indicator.urgent {
    background-color: #dc3545; /* danger */
}

.event-indicator.scheduled {
    background-color: #007bff; /* primary */
}

.event-indicator.completed {
    background-color: #6c757d; /* secondary */
}

.event-indicators {
    display: flex;
    gap: 2px;
    position: absolute;
    bottom: 4px;
    left: 50%;
    transform: translateX(-50%);
}

.event-indicators .event-indicator {
    position: static;
    transform: none;
}

/* Estilos para notificações */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 5px;
    background-color: #333;
    color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    max-width: 350px;
    animation: slide-in 0.3s ease-out;
}

.notification.success {
    background-color: #28a745;
    border-left: 5px solid #1e7e34;
}

.notification.error {
    background-color: #dc3545;
    border-left: 5px solid #bd2130;
}

.notification.warning {
    background-color: #ffc107;
    border-left: 5px solid #d39e00;
    color: #333;
}

.notification.info {
    background-color: #17a2b8;
    border-left: 5px solid #138496;
}

.notification-icon {
    margin-right: 15px;
    font-size: 1.5rem;
}

.notification-content {
    flex: 1;
}

.notification-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0;
    margin-left: 10px;
    opacity: 0.7;
}

.notification-close:hover {
    opacity: 1;
}

.notification.fade-out {
    opacity: 0;
    transform: translateX(30px);
    transition: opacity 0.5s, transform 0.5s;
}

@keyframes slide-in {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Estilos para o container de ordens de serviço */
.service-orders-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.service-order-item {
    display: flex;
    background-color: #2a2a2a;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    border: 1px solid #444;
}

.service-order-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.order-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background-color: #333;
    border-radius: 50%;
    margin-right: 15px;
    color: var(--shell-yellow);
    font-size: 1.2rem;
}

.order-details {
    flex: 1;
}

.order-title {
    margin: 0 0 8px 0;
    color: #fff;
    font-size: 1rem;
}

.order-info {
    display: flex;
    gap: 15px;
    font-size: 0.85rem;
    color: #bbb;
    margin-bottom: 5px;
}

.order-priority {
    margin-top: 5px;
}

/* Status específicos */
.service-order-item[data-status="pendente"] {
    border-left: 4px solid #ffc107;
}

.service-order-item[data-status="em_andamento"] {
    border-left: 4px solid #0d6efd;
}

.service-order-item[data-status="concluido"] {
    border-left: 4px solid #28a745;
}

.service-order-item[data-status="cancelado"] {
    border-left: 4px solid #6c757d;
    opacity: 0.8;
}

.service-order-item[data-status="urgente"] {
    border-left: 4px solid #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
}

/* Estilos para os cards de manutenção */
.maintenance-card {
    background-color: #2a2a2a;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border: 1px solid #444;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.maintenance-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.maintenance-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #444;
}

.maintenance-card-title {
    color: var(--shell-yellow);
    margin: 0;
    font-size: 1.2rem;
}

.maintenance-card-content {
    color: #ddd;
}

.maintenance-form-group {
    margin-bottom: 20px;
}

.maintenance-form-group label {
    display: block;
    margin-bottom: 8px;
    color: #bbb;
    font-weight: 500;
}

.maintenance-input,
.maintenance-select,
.maintenance-textarea {
    width: 100%;
    padding: 10px 15px;
    background-color: #333;
    border: 1px solid #555;
    border-radius: 5px;
    color: #fff;
    font-size: 0.9rem;
}

.maintenance-input:focus,
.maintenance-select:focus,
.maintenance-textarea:focus {
    outline: none;
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 2px rgba(255, 204, 0, 0.25);
}

.maintenance-textarea {
    min-height: 100px;
    resize: vertical;
}

.maintenance-checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.maintenance-checkbox {
    margin-right: 10px;
}

.maintenance-checkbox-label {
    color: #ddd;
    font-size: 0.9rem;
}

.maintenance-btn-group {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.maintenance-btn {
    padding: 8px 15px;
    border-radius: 5px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.maintenance-btn-primary {
    background-color: var(--shell-yellow);
    color: #000;
    border: none;
}

.maintenance-btn-primary:hover {
    background-color: #e6b800;
    transform: translateY(-2px);
}

.maintenance-btn-secondary {
    background-color: transparent;
    color: #ddd;
    border: 1px solid #555;
}

.maintenance-btn-secondary:hover {
    background-color: #333;
    transform: translateY(-2px);
}

/* Estilos para o card de detalhes da ordem */
.order-details-card {
    background-color: #2a2a2a;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border: 1px solid #444;
}

.order-details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #444;
}

.order-details-title {
    color: var(--shell-yellow);
    margin: 0;
    font-size: 1.2rem;
}

.order-details-status {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.order-details-status.pending {
    background-color: #ffc107;
    color: #000;
}

.order-details-status.in-progress {
    background-color: #0d6efd;
    color: #fff;
}

.order-details-status.completed {
    background-color: #28a745;
    color: #fff;
}

.order-details-status.urgent {
    background-color: #dc3545;
    color: #fff;
}

.order-details-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.order-details-item {
    margin-bottom: 10px;
}

.order-details-label {
    color: #bbb;
    font-size: 0.85rem;
    margin-bottom: 5px;
}

.order-details-value {
    color: #fff;
    font-size: 0.95rem;
    font-weight: 500;
}

.order-details-description {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #444;
}

.order-details-description h4 {
    color: #ddd;
    margin-bottom: 10px;
    font-size: 1rem;
}

.order-details-description p {
    color: #bbb;
    line-height: 1.6;
}
