/* Estilos específicos para a página de relatórios */

:root {
    --shell-red: #ED1C24;
    --shell-red-hover: #CE181F;
    --shell-red-light: #ff6b6e;
    --shell-yellow: #FDB813;
    --shell-yellow-hover: #e0a100;
    --shell-yellow-light: #ffe07a;
    --shell-dark: #333333;
    --shell-dark-hover: #222222;
    --shell-light: #f8f9fa;
    --shell-light-hover: #e9ecef;
    --shell-gray: #808080;
    --shell-gray-light: #d9d9d9;
}

/* Cabeçalho de relatórios personalizado */
.reports-header {
    background: linear-gradient(135deg, #1a1a1a 0%, #333333 100%);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.reports-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--shell-red), var(--shell-yellow));
}

.reports-title-container {
    color: white;
}

.reports-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
}

.reports-title i {
    color: var(--shell-yellow);
    margin-right: 0.75rem;
}

.reports-subtitle {
    color: #aaa;
    font-size: 0.9rem;
}

.reports-actions {
    display: flex;
    align-items: center;
}

.date-range-selector {
    width: 250px;
}

/* Painel de filtros específico para relatórios */
.reports-filter-panel {
    background-color: rgba(30, 30, 30, 0.7);
    border-radius: 10px;
    padding: 1.25rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.reports-filter-panel label {
    color: #ddd;
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
}

.reports-filter-panel .form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid #444;
    color: white;
}

.reports-filter-panel .form-control:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: var(--shell-yellow);
    color: white;
}

.reports-filter-panel .btn-outline-secondary {
    border-color: #444;
    color: #ddd;
}

.reports-filter-panel .btn-outline-secondary:hover,
.reports-filter-panel .btn-outline-secondary.active {
    background-color: var(--shell-yellow);
    border-color: var(--shell-yellow);
    color: var(--shell-dark);
}

/* Cartões de KPI */
.reports-kpi-container {
    margin-bottom: 1.5rem;
}

.kpi-card {
    background: linear-gradient(135deg, rgba(30, 30, 30, 0.9) 0%, rgba(50, 50, 50, 0.9) 100%);
    border-radius: 12px;
    padding: 1.25rem;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.kpi-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
}

.kpi-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.kpi-revenue::after {
    background: var(--shell-yellow);
}

.kpi-orders::after {
    background: var(--shell-red);
}

.kpi-time::after {
    background: var(--shell-info);
}

.kpi-satisfaction::after {
    background: var(--shell-success);
}

.kpi-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.kpi-revenue .kpi-icon {
    background-color: rgba(253, 184, 19, 0.2);
    color: var(--shell-yellow);
}

.kpi-orders .kpi-icon {
    background-color: rgba(237, 28, 36, 0.2);
    color: var(--shell-red);
}

.kpi-time .kpi-icon {
    background-color: rgba(23, 162, 184, 0.2);
    color: var(--shell-info);
}

.kpi-satisfaction .kpi-icon {
    background-color: rgba(40, 167, 69, 0.2);
    color: var(--shell-success);
}

.kpi-content {
    flex: 1;
}

.kpi-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: white;
    line-height: 1.2;
}

.kpi-label {
    font-size: 0.85rem;
    color: #aaa;
    margin-bottom: 0.25rem;
}

.kpi-trend {
    font-size: 0.8rem;
    display: flex;
    align-items: center;
}

.kpi-trend.positive {
    color: var(--shell-success);
}

.kpi-trend.negative {
    color: var(--shell-danger);
}

.kpi-trend i {
    margin-right: 0.25rem;
}

/* Cartões de gráficos */
.reports-charts-container {
    margin-bottom: 1.5rem;
}

.chart-card {
    background-color: rgba(30, 30, 30, 0.7);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.chart-header {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 1rem 1.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #444;
}

.chart-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    margin: 0;
}

.chart-actions {
    display: flex;
    gap: 0.5rem;
}

.chart-body {
    padding: 1.25rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chart-placeholder {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
}

.chart-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.loading-text {
    color: #aaa;
    margin-top: 1rem;
    font-size: 0.9rem;
}

/* Tabela de dados */
.reports-data-table-container {
    background-color: rgba(30, 30, 30, 0.7);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.data-table-header {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 1rem 1.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #444;
}

.data-table-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    margin: 0;
}

.data-table-actions {
    display: flex;
    gap: 0.5rem;
}

.data-table-pagination {
    padding: 1rem 1.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #444;
}

.pagination-info {
    color: #aaa;
    font-size: 0.9rem;
}

.pagination-controls {
    display: flex;
    gap: 0.25rem;
}

.pagination-controls .btn.active {
    background-color: var(--shell-yellow);
    border-color: var(--shell-yellow);
    color: var(--shell-dark);
}

/* Responsividade */
@media (max-width: 992px) {
    .reports-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .reports-actions {
        margin-top: 1rem;
        width: 100%;
    }
    
    .date-range-selector {
        width: 100%;
    }
    
    .kpi-card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 768px) {
    .reports-filter-panel .row > div {
        margin-bottom: 1rem;
    }
    
    .chart-placeholder {
        height: 250px;
    }
    
    .data-table-pagination {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .pagination-controls {
        margin-top: 0.5rem;
    }
}
