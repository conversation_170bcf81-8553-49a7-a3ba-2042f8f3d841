/* Estilos para o layout base */
:root {
    --shell-red: #ED1C24;
    --shell-yellow: #f4b31d;
    --dark-gray: #1a1a1a;
    --medium-gray: #333;
    --light-gray: #444;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    background-color: var(--dark-gray);
    color: #fff;
    min-height: 100vh;
}

/* Estilo para o conteúdo principal */
.main-content {
    padding: 20px;
    background: var(--medium-gray);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--light-gray);
    overflow-y: auto;
    min-height: calc(100vh - 60px);
}

/* Estilo para cabeçalhos de página */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    margin-bottom: 20px;
    border-bottom: 2px solid var(--shell-yellow);
}

.page-title {
    color: var(--shell-yellow);
    font-size: 24px;
    font-weight: 600;
    margin: 0;
}

/* Estilo para cards e elementos visuais */
.tradicio-card {
    background: rgba(0, 0, 0, 0.5);
    border: 2px solid var(--shell-yellow);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.tradicio-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.tradicio-card-header {
    background: linear-gradient(135deg, var(--shell-yellow) 0%, var(--shell-red) 100%);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Botão estilo Shell */
.shell-btn {
    background: var(--shell-red);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 0 10px rgba(237, 28, 36, 0.5);
}

.shell-btn:hover {
    background: #c51017;
    transform: translateY(-2px);
    box-shadow: 0 0 15px rgba(237, 28, 36, 0.8);
}

.shell-btn-yellow {
    background: var(--shell-yellow);
    color: #333;
    box-shadow: 0 0 10px rgba(244, 179, 29, 0.5);
}

.shell-btn-yellow:hover {
    background: #d9980a;
    box-shadow: 0 0 15px rgba(244, 179, 29, 0.8);
}

/* Borda estilo bomba de combustível */
.pump-border {
    border: 4px solid var(--shell-yellow);
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 20px;
    background: rgba(0, 0, 0, 0.5);
    position: relative;
}

/* Efeito "medidor de combustível" */
.fuel-gauge {
    height: 8px;
    width: 100%;
    background: linear-gradient(90deg, 
        var(--shell-red) 0%, 
        var(--shell-red) 20%, 
        var(--shell-yellow) 20%, 
        var(--shell-yellow) 40%, 
        var(--shell-red) 40%, 
        var(--shell-red) 60%, 
        var(--shell-yellow) 60%, 
        var(--shell-yellow) 80%, 
        var(--shell-red) 80%, 
        var(--shell-red) 100%);
    border-radius: 4px;
    margin-top: 10px;
}

/* Responsividade */
@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
        width: 100%;
    }
}

/* Notificações e badges */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -8px;
    background: var(--shell-red);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
}
