/* Estilos específicos para exemplo-page.html */

/* Animações adicionais */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Estilos para notificações */
.notifications-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.notification-item {
    display: flex;
    padding: 1rem;
    border-bottom: 1px solid var(--shell-gray-light);
    transition: background-color 0.3s;
}

.notification-item:hover {
    background-color: rgba(253, 184, 19, 0.05);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: var(--font-weight-semibold);
    font-family: 'Rajdhani', sans-serif;
    margin-bottom: 0.25rem;
}

.notification-text {
    color: var(--shell-gray);
    font-size: var(--font-size-sm);
    margin-bottom: 0.25rem;
}

.notification-time {
    color: var(--shell-gray);
    font-size: var(--font-size-xs);
    font-style: italic;
}

/* Link estilizado */
.btn-link {
    color: var(--shell-red);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: color 0.3s;
}

.btn-link:hover {
    color: var(--shell-red-hover);
    text-decoration: underline;
}

/* Estilos específicos para badges */
.badge {
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 600;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Ajustes para responsividade em dispositivos móveis */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .page-header h1 {
        font-size: var(--font-size-xl);
    }
    
    .card-header h3 {
        font-size: var(--font-size-lg);
    }
    
    .notification-icon {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }
}

/* Estilos para o gráfico */
.chart-container {
    position: relative;
    margin: auto;
    height: 250px;
    width: 100%;
}

/* Estilo de hover para cards */
.card-shell {
    transition: transform 0.3s, box-shadow 0.3s;
}

.card-shell:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

/* Estilo para botões pequenos */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 3px;
}

/* Estilo para ícones em botões */
button i {
    transition: transform 0.3s;
}

button:hover i {
    transform: scale(1.2);
}

/* Estilos de alerta e mensagens */
.alert-shell {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.alert-shell-info {
    background-color: rgba(23, 162, 184, 0.1);
    border-left-color: var(--shell-info);
    color: var(--shell-info);
}

.alert-shell-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-left-color: var(--shell-success);
    color: var(--shell-success);
}

.alert-shell-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-left-color: var(--shell-warning);
    color: var(--shell-warning);
}

.alert-shell-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-left-color: var(--shell-danger);
    color: var(--shell-danger);
} 