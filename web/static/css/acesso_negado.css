/* 
 * Estilos para a página de Acesso Negado
 * Sistema Shell Tradição
 */

:root {
    --shell-red: #ED1C24;
    --shell-yellow: #FDB813;
    --shell-dark: #222222;
    --shell-light: #f8f9fa;
}

body {
    background-color: var(--shell-dark);
    color: var(--shell-light);
    font-family: 'Raj<PERSON><PERSON>', sans-serif;
    overflow-x: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

/* Efeito de fundo com padrão de listras */
body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        45deg,
        rgba(237, 28, 36, 0.05),
        rgba(237, 28, 36, 0.05) 20px,
        rgba(253, 184, 19, 0.05) 20px,
        rgba(253, 184, 19, 0.05) 40px
    );
    z-index: -1;
}

.acesso-negado-container {
    max-width: 800px;
    width: 100%;
    padding: 20px;
}

.acesso-negado-card {
    background: rgba(34, 34, 34, 0.9);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    position: relative;
    border: 1px solid rgba(237, 28, 36, 0.3);
    transform-style: preserve-3d;
    perspective: 1000px;
}

.acesso-negado-header {
    background: linear-gradient(135deg, var(--shell-red), #990000);
    color: white;
    padding: 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.acesso-negado-header h2 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
    position: relative;
    z-index: 2;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Efeito de fita de segurança */
.security-tape {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: repeating-linear-gradient(
        45deg,
        var(--shell-yellow),
        var(--shell-yellow) 10px,
        #000 10px,
        #000 20px
    );
    opacity: 0.2;
    z-index: 1;
}

.acesso-negado-body {
    padding: 30px;
    text-align: center;
    position: relative;
}

.icon-container {
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
}

.icon-container i {
    font-size: 5rem;
    color: var(--shell-red);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.message-container {
    margin-bottom: 30px;
}

.message-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--shell-yellow);
}

.message-text {
    font-size: 1.2rem;
    margin-bottom: 20px;
    color: rgba(255, 255, 255, 0.8);
}

.countdown-container {
    margin: 30px 0;
    position: relative;
}

.countdown-text {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: var(--shell-light);
}

.countdown-timer {
    font-size: 3rem;
    font-weight: 700;
    color: var(--shell-yellow);
    font-family: 'Share Tech Mono', monospace;
    background: rgba(0, 0, 0, 0.3);
    display: inline-block;
    padding: 10px 20px;
    border-radius: 10px;
    min-width: 80px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    text-shadow: 0 0 10px rgba(253, 184, 19, 0.7);
}

.countdown-timer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--shell-yellow);
    animation: countdown-progress 10s linear forwards;
    transform-origin: left;
}

@keyframes countdown-progress {
    0% {
        transform: scaleX(1);
    }
    100% {
        transform: scaleX(0);
    }
}

.btn-container {
    margin-top: 20px;
}

.btn-shell {
    background-color: var(--shell-red);
    color: white;
    border: none;
    padding: 10px 25px;
    border-radius: 50px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(237, 28, 36, 0.3);
}

.btn-shell:hover {
    background-color: #c91017;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(237, 28, 36, 0.4);
    color: white;
}

.btn-shell::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.btn-shell:hover::after {
    animation: ripple 1s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20);
        opacity: 0;
    }
}

/* Elementos decorativos */
.decorative-element {
    position: absolute;
    background-color: var(--shell-yellow);
    opacity: 0.1;
    border-radius: 50%;
}

.decorative-element-1 {
    width: 100px;
    height: 100px;
    top: -50px;
    left: -50px;
}

.decorative-element-2 {
    width: 150px;
    height: 150px;
    bottom: -75px;
    right: -75px;
}

/* Animação de rotação para o ícone */
@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.rotating-gear {
    position: absolute;
    font-size: 2rem;
    color: var(--shell-yellow);
    opacity: 0.2;
    animation: rotate 10s linear infinite;
}

.gear-1 {
    top: 20px;
    right: 20px;
}

.gear-2 {
    bottom: 20px;
    left: 20px;
}

/* Animação de entrada */
.fade-in {
    animation: fadeIn 0.8s ease-out forwards;
    opacity: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animação de saída */
.fade-out {
    animation: fadeOut 0.8s ease-in forwards;
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* Animação de shake para o ícone de acesso negado */
.shake {
    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    perspective: 1000px;
}

@keyframes shake {
    10%, 90% {
        transform: translate3d(-1px, 0, 0);
    }
    
    20%, 80% {
        transform: translate3d(2px, 0, 0);
    }

    30%, 50%, 70% {
        transform: translate3d(-4px, 0, 0);
    }

    40%, 60% {
        transform: translate3d(4px, 0, 0);
    }
}

/* Responsividade */
@media (max-width: 768px) {
    .acesso-negado-header h2 {
        font-size: 2rem;
    }
    
    .message-title {
        font-size: 1.5rem;
    }
    
    .message-text {
        font-size: 1rem;
    }
    
    .countdown-timer {
        font-size: 2.5rem;
    }
}
