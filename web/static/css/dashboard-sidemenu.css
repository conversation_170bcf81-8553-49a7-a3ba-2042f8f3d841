/*
 * TRADIÇÃO - Estilos para Dashboard com Menu Lateral
 * Contém estilos específicos para a versão do dashboard com menu lateral
 */

@import url('theme-variables.css');

/* Container principal */
.dashboard-container {
    padding: var(--spacing-md);
}

/* <PERSON><PERSON><PERSON><PERSON> */
.page-title {
    color: var(--shell-white);
    font-weight: 600;
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-lg);
    border-left: 5px solid var(--shell-yellow);
    padding-left: var(--spacing-md);
}

/* Seções do dashboard */
.dashboard-section {
    margin-bottom: var(--spacing-xl);
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
}

.section-title {
    margin-bottom: var(--spacing-lg);
    color: var(--shell-white);
    font-weight: 600;
    display: flex;
    align-items: center;
}

.section-title i {
    margin-right: var(--spacing-sm);
    color: var(--shell-yellow);
}

/* Ordens urgentes */
.urgent-orders-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.urgent-order-card {
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    position: relative;
    transition: var(--transition-normal);
    border-left: 5px solid var(--shell-red);
    overflow: hidden;
}

.urgent-order-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(237, 28, 36, 0.05), rgba(0, 0, 0, 0));
    z-index: -1;
}

.urgent-order-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.urgency-badge {
    background: var(--shell-red);
    color: white;
    padding: 4px 8px;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    display: inline-block;
    margin-right: var(--spacing-sm);
}

.high-priority {
    background: var(--priority-high);
}

.medium-priority {
    background: var(--priority-medium);
    color: var(--shell-black);
}

.order-time {
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--font-size-xs);
    margin-bottom: var(--spacing-sm);
}

.order-title {
    color: var(--shell-yellow);
    font-size: var(--font-size-md);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.order-location {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-md);
}

.order-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

.order-action-btn {
    background: transparent;
    border: 1px solid;
    border-radius: var(--border-radius-sm);
    padding: 5px 10px;
    font-size: var(--font-size-xs);
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    gap: 5px;
}

.edit-btn {
    color: var(--shell-yellow);
    border-color: var(--shell-yellow);
}

.edit-btn:hover {
    background: rgba(253, 184, 19, 0.1);
}

.complete-btn {
    color: var(--status-completed);
    border-color: var(--status-completed);
}

.complete-btn:hover {
    background: rgba(40, 167, 69, 0.1);
}

/* Seção de calendário (inicialmente oculta) */
.calendar-section {
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    margin-top: var(--spacing-xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0;
    max-height: 0;
    overflow: hidden;
    transition: var(--transition-slow);
}

.calendar-section.visible {
    opacity: 1;
    max-height: 1000px;
    margin-bottom: var(--spacing-xl);
}

.calendar-container {
    height: 600px;
    position: relative;
}

/* Botão de biometria */
.biometric-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: var(--z-index-fixed);
    perspective: 1000px;
}

/* Responsividade */
@media (max-width: 992px) {
    .main-content {
        padding-top: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .page-title {
        font-size: var(--font-size-lg);
    }

    .dashboard-section {
        padding: var(--spacing-md);
    }

    .order-actions {
        flex-direction: column;
    }

    .order-action-btn {
        width: 100%;
        justify-content: center;
    }

    .calendar-container {
        height: 450px;
    }
}

@media (max-width: 576px) {
    .page-title {
        font-size: var(--font-size-md);
    }
}

/* Estilo para o cabeçalho simplificado da sidebar */
.sidebar-simple-header {
    padding: 20px 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-simple-header .sidebar-title {
    font-size: 1.2rem;
    color: var(--shell-yellow);
    margin: 0;
    text-align: center;
    text-shadow: 0 0 5px rgba(244, 179, 29, 0.5);
}

/* Estilo da barra lateral */
.sidebar {
    width: 250px;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    background-color: var(--shell-dark);
    color: #fff;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    z-index: 1000;
}

.sidebar-menu {
    flex: 1;
    overflow-y: auto;
    padding: 20px 0;
}

.sidebar-menu ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu li {
    margin-bottom: 5px;
}

.sidebar-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
}

.sidebar-link:hover, .sidebar-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--shell-yellow);
}

.sidebar-link i {
    margin-right: 15px;
    width: 20px;
    text-align: center;
}

.sidebar-section {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-section-title {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.5);
    margin: 0 0 15px 0;
    text-transform: uppercase;
    font-weight: 500;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout-btn {
    width: 100%;
    padding: 12px;
    background-color: rgba(237, 28, 36, 0.2);
    color: #fff;
    border: none;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background-color: rgba(237, 28, 36, 0.4);
}

.logout-btn i {
    margin-right: 10px;
}

.main-content {
    margin-left: 250px;
    padding: 20px;
    min-height: 100vh;
    transition: all 0.3s ease;
    background-color: var(--shell-light);
}

/* Responsividade */
@media (max-width: 992px) {
    .sidebar {
        width: 70px;
    }
    
    .sidebar-title, .sidebar-link span, .logout-btn span, .sidebar-section-title {
        display: none;
    }
    
    .sidebar-link i {
        margin-right: 0;
        font-size: 1.2rem;
    }
    
    .logout-btn i {
        margin-right: 0;
    }
    
    .main-content {
        margin-left: 70px;
    }
}

@media (max-width: 576px) {
    .sidebar {
        width: 0;
        transform: translateX(-100%);
    }
    
    .sidebar.active {
        width: 200px;
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .sidebar.active + .main-content {
        margin-left: 200px;
    }
}