/* Estilos para a página Minha Conta */

/* Variáveis de cores */
:root {
    --shell-red: #ED1C24;
    --shell-red-hover: #CE181F;
    --shell-yellow: #FDB813;
    --shell-yellow-hover: #e0a100;
    --shell-dark: #333333;
    --shell-darker: #222222;
    --shell-light: #f8f9fa;
    --shell-gray: #808080;
    --shell-success: #28a745;
    --shell-info: #17a2b8;
    --shell-warning: #ffc107;
    --shell-danger: #dc3545;
}

/* Estilos gerais da página */
body {
    background-color: var(--shell-dark);
    color: var(--shell-light);
    font-family: 'Rajdhani', sans-serif;
}

.container-fluid {
    max-width: 1600px;
    margin: 0 auto;
}

/* Cabe<PERSON><PERSON><PERSON> da página */
.page-header {
    margin-bottom: 2rem;
}

.page-title {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    font-weight: 700;
    font-size: 1.8rem;
    color: var(--shell-red);
    margin-bottom: 0.25rem;
}

.page-subtitle {
    font-family: 'Share Tech Mono', monospace;
    font-size: 0.9rem;
    color: var(--shell-yellow);
    opacity: 0.9;
}

/* Cards */
.card-shell {
    background-color: #2a2a2a;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--shell-red);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.card-shell:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
    border-color: var(--shell-yellow);
}

.card-header-shell {
    background-color: #222222;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--shell-red);
    display: flex;
    align-items: center;
}

.card-body-shell {
    padding: 1rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.card-title {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--shell-yellow);
    margin: 0;
    text-transform: uppercase;
}

/* Perfil */
.profile-avatar-large {
    width: 80px;
    height: 80px;
    background-color: var(--shell-red);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 700;
    margin: 0 auto 1.5rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border: 2px solid var(--shell-yellow);
}

.info-grid {
    display: grid;
    gap: 0.75rem;
}

.info-item {
    position: relative;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-label {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: var(--shell-yellow);
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.info-value {
    color: var(--shell-light);
    font-size: 1rem;
    padding-left: 1.5rem;
}

.security-info {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
}

.section-subtitle {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--shell-yellow);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

/* Botões */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.btn-shell-red {
    background-color: var(--shell-red);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    cursor: pointer;
}

.btn-shell-red:hover {
    background-color: var(--shell-red-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    color: white;
}

.btn-shell {
    background-color: #333333;
    color: var(--shell-light);
    border: 1px solid var(--shell-yellow);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    cursor: pointer;
}

.btn-shell:hover {
    background-color: #444444;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    color: var(--shell-light);
}

.btn-shell-yellow {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    cursor: pointer;
}

.btn-shell-yellow:hover {
    background-color: var(--shell-yellow-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    color: var(--shell-dark);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* Badges */
.badge-shell-yellow {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.8rem;
    display: inline-block;
}

.badge-shell-red {
    background-color: var(--shell-red);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.8rem;
    display: inline-block;
}

.badge-shell-green {
    background-color: var(--shell-success);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.8rem;
    display: inline-block;
}

/* Atividades */
.user-activities {
    display: grid;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border: 1px solid rgba(237, 28, 36, 0.3);
    transition: all 0.3s ease;
}

.activity-item:hover {
    border-color: var(--shell-red);
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.activity-icon {
    width: 40px;
    height: 40px;
    background-color: var(--shell-red);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.activity-content {
    flex-grow: 1;
}

.activity-title {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--shell-yellow);
    margin-bottom: 0.5rem;
}

.activity-value {
    color: var(--shell-light);
}

/* Timers */
.timers-container {
    display: grid;
    gap: 1rem;
}

.timer-card {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid rgba(237, 28, 36, 0.3);
    transition: all 0.3s ease;
}

.timer-card:hover {
    border-color: var(--shell-red);
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.timer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.timer-title {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--shell-yellow);
    margin: 0;
}

.timer-info p {
    margin: 0.25rem 0;
    color: var(--shell-light);
    font-size: 0.95rem;
}

.timer-display {
    font-family: 'Share Tech Mono', monospace;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--shell-red);
    text-align: center;
    margin-top: 0.75rem;
    padding: 0.5rem;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
}

/* Histórico */
.section-subtitle {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1.2rem;
    color: var(--shell-yellow);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.recent-orders-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.recent-orders-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.recent-orders-list li:last-child {
    border-bottom: none;
}

.order-link {
    color: var(--shell-yellow);
    text-decoration: none;
    font-weight: 500;
    display: block;
    transition: all 0.3s ease;
}

.order-link:hover {
    color: var(--shell-red);
    transform: translateX(3px);
}

.order-date {
    font-size: 0.85rem;
    color: var(--shell-gray);
    display: block;
    margin-top: 0.25rem;
}

/* Segurança */
.security-log {
    display: grid;
    gap: 1rem;
}

.log-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border: 1px solid rgba(237, 28, 36, 0.3);
    transition: all 0.3s ease;
}

.log-item:hover {
    border-color: var(--shell-red);
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.log-icon {
    width: 40px;
    height: 40px;
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.log-icon-small {
    width: 30px;
    height: 30px;
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.log-content {
    flex-grow: 1;
}

.log-action {
    font-weight: 600;
    color: var(--shell-yellow);
    display: block;
    margin-bottom: 0.25rem;
}

.log-action-small {
    font-weight: 600;
    color: var(--shell-yellow);
    display: block;
    font-size: 0.9rem;
}

.log-time {
    color: var(--shell-gray);
    font-size: 0.85rem;
    font-style: italic;
}

.log-time-small {
    color: var(--shell-gray);
    font-size: 0.75rem;
    font-style: italic;
}

/* Notificações */
.notifications-preview {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-height: 200px;
    overflow-y: auto;
}

.notification-item-preview {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.notification-item-preview:hover {
    border-color: var(--shell-red);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.notification-icon-small {
    width: 30px;
    height: 30px;
    background-color: var(--shell-red);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.notification-content-preview {
    flex-grow: 1;
}

.notification-title-preview {
    font-weight: 600;
    color: var(--shell-light);
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.notification-time-preview {
    color: var(--shell-gray);
    font-size: 0.75rem;
}

.empty-notifications-preview {
    text-align: center;
    padding: 1.5rem;
    color: var(--shell-gray);
    font-size: 0.9rem;
}

.notification-actions {
    display: flex;
    gap: 0.5rem;
}

/* Equipamentos */
.equipment-table-container {
    overflow-x: auto;
    margin-bottom: 0.5rem;
}

.equipment-table {
    width: 100%;
    margin-bottom: 0;
    font-size: 0.85rem;
}

.equipment-table th {
    background-color: #222;
    color: var(--shell-yellow);
    font-weight: 600;
    padding: 0.5rem;
    border-bottom: 1px solid var(--shell-red);
}

.equipment-table td {
    padding: 0.5rem;
    border-color: rgba(255, 255, 255, 0.05);
}

.btn-icon {
    background: transparent;
    border: none;
    color: var(--shell-light);
    padding: 0.25rem;
    font-size: 0.85rem;
    cursor: pointer;
    transition: color 0.3s;
}

.btn-icon:hover {
    color: var(--shell-yellow);
}

/* Cards de Equipamentos Responsivos */
.section-title {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--shell-red);
    margin-bottom: 0.5rem;
}

.equipment-card-responsive {
    height: 100%;
    transition: all 0.3s ease;
    cursor: pointer;
}

.equipment-card-responsive:hover {
    transform: translateY(-5px);
}

.equipment-card-responsive .card-header-shell {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.equipment-card-responsive .equipment-image {
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    margin-bottom: 1rem;
    overflow: hidden;
}

.equipment-card-responsive .equipment-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.equipment-card-responsive .equipment-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.equipment-card-responsive .equipment-detail-item {
    margin-bottom: 0.5rem;
}

.equipment-card-responsive .equipment-actions {
    display: flex;
    justify-content: space-between;
    margin-top: auto;
}

/* Botão Flutuante */
.floating-action-button {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    cursor: pointer;
    z-index: 1000;
    transition: all 0.3s ease;
}

.floating-action-button:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.5);
}

.floating-action-button:active {
    transform: translateY(0) scale(0.95);
}

@media (max-width: 768px) {
    .floating-action-button {
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}

/* Cards de Equipamentos Responsivos */

.equipment-card-responsive {
    height: 100%;
    transition: all 0.3s ease;
    cursor: pointer;
}

.equipment-card-responsive:hover {
    transform: translateY(-5px);
}

.equipment-card-responsive .card-header-shell {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.equipment-card-responsive .equipment-image {
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    margin-bottom: 1rem;
    overflow: hidden;
}

.equipment-card-responsive .equipment-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.equipment-card-responsive .equipment-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.equipment-card-responsive .equipment-detail-item {
    margin-bottom: 0.5rem;
}

.equipment-card-responsive .equipment-actions {
    display: flex;
    justify-content: space-between;
    margin-top: auto;
}

/* Card Flip de Equipamentos */
.equipment-card-container {
    perspective: 1000px;
    height: 100%;
}

.equipment-card-flip {
    position: relative;
    width: 100%;
    height: 100%;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}

.equipment-card-container.flipped .equipment-card-flip {
    transform: rotateY(180deg);
}

.equipment-card-front,
.equipment-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-radius: 8px;
    overflow: hidden;
}

.equipment-card-back {
    transform: rotateY(180deg);
    background-color: #1a1a1a;
    padding: 15px;
    display: flex;
    flex-direction: column;
}

.equipment-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #333;
    padding-bottom: 10px;
}

.equipment-detail-title {
    font-size: 1.2rem;
    color: var(--shell-yellow);
    font-weight: 600;
    margin: 0;
}

.equipment-detail-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.equipment-detail-item {
    margin-bottom: 10px;
}

.equipment-detail-label {
    font-size: 0.8rem;
    color: #adb5bd;
    display: block;
    margin-bottom: 2px;
}

.equipment-detail-value {
    font-size: 0.9rem;
    color: var(--shell-light);
    font-weight: 500;
}

.equipment-detail-image-container {
    text-align: center;
    margin: 10px 0;
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.equipment-detail-image {
    max-width: 100%;
    max-height: 120px;
    object-fit: contain;
    background-color: #333;
    border-radius: 4px;
    padding: 5px;
}

.equipment-detail-footer {
    margin-top: auto;
    text-align: center;
    padding-top: 15px;
    border-top: 1px solid #333;
}

.btn-flip-back {
    background-color: var(--shell-red);
    border: none;
    color: white;
    padding: 8px 15px;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    width: 100%;
}

.btn-flip-back:hover {
    background-color: var(--shell-red-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Alertas */
.alert-shell {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.alert-shell-warning {
    background-color: rgba(255, 193, 7, 0.2);
    border: 1px solid var(--shell-warning);
    color: var(--shell-light);
}

/* Modais */
.modal-shell {
    background-color: #2a2a2a;
    color: var(--shell-light);
    border: 1px solid var(--shell-red);
}

.modal-shell .modal-header {
    background-color: #222222;
    border-bottom: 2px solid var(--shell-red);
    padding: 1rem 1.5rem;
}

.modal-shell .modal-title {
    color: var(--shell-yellow);
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
}

.modal-shell .modal-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 1.5rem;
}

.modal-shell .btn-close {
    color: white;
    opacity: 0.8;
    filter: invert(1) grayscale(100%) brightness(200%);
}

.notifications-list {
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-icon {
    width: 40px;
    height: 40px;
    background-color: var(--shell-red);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.notification-content {
    flex-grow: 1;
}

.notification-title {
    font-weight: 600;
    color: var(--shell-yellow);
    margin-bottom: 0.25rem;
}

.notification-message {
    color: var(--shell-light);
    margin-bottom: 0.5rem;
}

.notification-time {
    color: var(--shell-gray);
    font-size: 0.85rem;
    font-style: italic;
}

.empty-notifications {
    text-align: center;
    padding: 2rem;
    color: var(--shell-gray);
}

/* Formulários */
.form-label {
    color: var(--shell-yellow);
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.form-control, .form-select {
    background-color: #444;
    border: 1px solid #555;
    color: var(--shell-light);
    font-size: 0.9rem;
    padding: 0.4rem 0.75rem;
}

.form-control:focus, .form-select:focus {
    background-color: #4a4a4a;
    color: var(--shell-light);
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 0.25rem rgba(253, 184, 19, 0.25);
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Responsividade */
@media (max-width: 1199.98px) {
    .container-fluid {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (max-width: 991.98px) {
    .page-title {
        font-size: 1.5rem;
    }

    .page-subtitle {
        font-size: 0.85rem;
    }
}

@media (max-width: 767.98px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .page-title {
        font-size: 1.3rem;
    }

    .page-subtitle {
        font-size: 0.8rem;
    }

    .card-header-shell {
        padding: 0.5rem 0.75rem;
    }

    .card-body-shell {
        padding: 0.75rem;
    }

    .profile-avatar-large {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .btn-shell-red, .btn-shell, .btn-shell-yellow {
        font-size: 0.8rem;
        padding: 0.4rem 0.75rem;
    }

    .action-buttons {
        flex-direction: column;
    }

    .action-buttons > * {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Modal de Transferência */
.modal-content.bg-dark {
    border: 1px solid var(--shell-dark-hover);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.modal-content.bg-dark .modal-header {
    padding: 1rem 1.5rem;
}

.modal-content.bg-dark .modal-title {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1.25rem;
}

.modal-content.bg-dark .form-label {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.modal-content.bg-dark .form-select,
.modal-content.bg-dark .form-control {
    border-width: 2px;
    transition: border-color 0.2s ease-in-out;
}

.modal-content.bg-dark .form-select:focus,
.modal-content.bg-dark .form-control:focus {
    border-color: var(--shell-yellow);
    box-shadow: none;
}

.modal-content.bg-dark .form-select option {
    background-color: var(--shell-dark);
    color: #fff;
    padding: 8px;
}

.modal-content.bg-dark .alert-shell {
    border: none;
    background-color: rgba(23, 162, 184, 0.1);
    border-left: 4px solid var(--shell-info);
}

.modal-content.bg-dark .btn-shell,
.modal-content.bg-dark .btn-shell-yellow {
    min-width: 120px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Animação de carregamento para os selects */
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.form-select.loading {
    pointer-events: none;
    opacity: 0.7;
}
