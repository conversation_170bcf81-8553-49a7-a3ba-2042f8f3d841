/* Estilos para o botão de toggle mobile do sidebar */

/* Botão de toggle mobile */
.mobile-menu-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--shell-red, #ED1C24);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    cursor: pointer;
    z-index: 1050;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover, 
.mobile-menu-toggle:focus {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

.mobile-menu-toggle:active {
    transform: scale(0.95);
}

/* Ajustes para o sidebar em dispositivos móveis */
@media (max-width: 768px) {
    .tradicio-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .tradicio-sidebar.mobile-open {
        transform: translateX(0);
    }
    
    /* Overlay para fechar o menu ao clicar fora */
    .sidebar-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1040;
    }
    
    .sidebar-overlay.visible {
        display: block;
    }
}
