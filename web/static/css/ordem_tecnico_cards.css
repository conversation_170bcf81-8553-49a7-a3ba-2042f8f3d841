/**
 * Estilos para os Cards de Ordem Técnico
 * Sistema Tradição - Módulo de Técnicos
 */

/* Cards de Manutenção */
.maintenance-card {
    background-color: #222;
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.maintenance-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
}

.maintenance-card .card-header {
    background-color: rgba(237, 28, 36, 0.1);
    border-bottom: 1px solid rgba(237, 28, 36, 0.2);
    padding: 1rem 1.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.maintenance-card .card-header h5 {
    color: var(--shell-yellow);
    margin: 0;
    font-weight: 600;
    font-size: 1.1rem;
}

.maintenance-card .card-body {
    padding: 1.25rem;
    color: var(--shell-white);
}

.maintenance-card .card-footer {
    background-color: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 0.75rem 1.25rem;
    text-align: right;
}

/* Botões de Edição */
.btn-edit-card {
    background-color: transparent;
    border: none;
    color: var(--shell-yellow);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.btn-edit-card:hover {
    background-color: rgba(253, 184, 19, 0.2);
    color: var(--shell-yellow);
}

/* Preview de Conteúdo */
.card-preview {
    min-height: 100px;
    color: #aaa;
    font-size: 0.95rem;
}

.card-preview.empty {
    display: flex;
    align-items: center;
    justify-content: center;
    font-style: italic;
    color: #777;
}

/* Estilos específicos para cada tipo de card */
#manutencao-card .card-header {
    border-top: 3px solid var(--shell-red);
}

#custos-card .card-header {
    border-top: 3px solid var(--shell-yellow);
}

#interacao-card .card-header {
    border-top: 3px solid #17a2b8;
}

#cronograma-card .card-header {
    border-top: 3px solid #28a745;
}

/* Chat Preview */
.chat-preview {
    max-height: 100px;
    overflow-y: auto;
}

.chat-message-preview {
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 0.25rem;
}

.chat-message-preview p {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Cronograma */
.schedule-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.schedule-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.schedule-label {
    color: #aaa;
}

.schedule-value {
    color: var(--shell-white);
    font-weight: 500;
}

/* Sistema de Notificações */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 350px;
    background-color: #222;
    color: white;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: stretch;
    z-index: 9999;
    overflow: hidden;
    animation: slide-in 0.3s ease-out forwards;
}

.notification.fade-out {
    animation: slide-out 0.5s ease-out forwards;
}

.notification-icon {
    width: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.notification-content {
    flex-grow: 1;
    padding: 1rem;
}

.notification-content p {
    margin: 0;
}

.notification-close {
    background: transparent;
    border: none;
    color: white;
    padding: 0.5rem;
    cursor: pointer;
    align-self: flex-start;
}

/* Tipos de notificação */
.notification.info .notification-icon {
    background-color: #17a2b8;
}

.notification.success .notification-icon {
    background-color: #28a745;
}

.notification.warning .notification-icon {
    background-color: var(--shell-yellow);
}

.notification.error .notification-icon {
    background-color: var(--shell-red);
}

/* Animações */
@keyframes slide-in {
    from {
        transform: translateX(400px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slide-out {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(400px);
        opacity: 0;
    }
}

/* Responsividade */
@media (max-width: 1200px) {
    .maintenance-card .card-header h5 {
        font-size: 1rem;
    }
    
    .card-preview {
        font-size: 0.9rem;
    }
}

@media (max-width: 992px) {
    .maintenance-card {
        margin-bottom: 1rem;
    }
    
    .maintenance-card .card-body {
        padding: 1rem;
    }
    
    .maintenance-card .card-footer {
        padding: 0.5rem 1rem;
    }
}

@media (max-width: 768px) {
    .notification {
        width: calc(100% - 40px);
        max-width: 350px;
    }
    
    .maintenance-card .card-header {
        padding: 0.75rem 1rem;
    }
    
    .maintenance-card .card-body {
        padding: 0.75rem;
    }
    
    .card-preview {
        min-height: 80px;
    }
}

@media (max-width: 576px) {
    .maintenance-card .card-header h5 {
        font-size: 0.95rem;
    }
    
    .btn-edit-card {
        padding: 0.2rem 0.4rem;
    }
    
    .card-preview {
        font-size: 0.85rem;
        min-height: 60px;
    }
}
