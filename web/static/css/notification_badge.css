/* Estilos para o badge de notificação */
.notification-badge {
    position: relative;
    display: inline-block;
    margin-right: 15px;
    cursor: pointer;
}

.sidebar-footer .notification-badge {
    margin-right: 20px;
    color: var(--shell-light);
}

.sidebar-footer .notification-badge i {
    font-size: 1.2rem;
    transition: color 0.3s;
}

.sidebar-footer .notification-badge:hover i {
    color: var(--shell-yellow);
}

.notification-badge .badge {
    position: absolute;
    top: -8px;
    right: -8px;
    padding: 3px 6px;
    border-radius: 50%;
    background-color: var(--shell-red);
    color: white;
    font-size: 0.7rem;
    font-weight: bold;
    min-width: 18px;
    height: 18px;
    line-height: 12px;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    z-index: 1;
    animation: pulse 1.5s infinite;
}

.notification-badge .badge.hidden {
    display: none;
}

/* Animação de pulso para o badge */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Estilos para o dropdown de notificações */
.notification-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 320px;
    max-height: 400px;
    overflow-y: auto;
    overflow-x: hidden; /* Impede vazamento horizontal */
    background-color: var(--shell-dark);
    border: 1px solid var(--shell-border);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    display: none;
    visibility: hidden; /* Garante que o conteúdo não seja visível quando fechado */
}

.notification-dropdown.show {
    display: block;
    visibility: visible; /* Garante que o conteúdo seja visível quando aberto */
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.notification-dropdown-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid var(--shell-border);
}

.notification-dropdown-header h6 {
    margin: 0;
    color: var(--shell-yellow);
    font-weight: 600;
}

.notification-dropdown-header .mark-all-read {
    color: var(--shell-light);
    font-size: 0.8rem;
    cursor: pointer;
    transition: color 0.2s;
}

.notification-dropdown-header .mark-all-read:hover {
    color: var(--shell-yellow);
}

.notification-dropdown-body {
    padding: 0;
}

.notification-item {
    padding: 10px 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    transition: background-color 0.2s;
    cursor: pointer;
}

.notification-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.notification-item.unread {
    background-color: rgba(237, 28, 36, 0.1);
}

.notification-item.unread:hover {
    background-color: rgba(237, 28, 36, 0.15);
}

.notification-item-content {
    display: flex;
    align-items: flex-start;
}

.notification-item-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    flex-shrink: 0;
}

.notification-item-text {
    flex-grow: 1;
}

.notification-item-title {
    font-weight: 600;
    color: var(--shell-light);
    margin-bottom: 3px;
    font-size: 0.9rem;
}

.notification-item.unread .notification-item-title {
    color: var(--shell-yellow);
}

.notification-item-message {
    color: var(--shell-gray);
    font-size: 0.8rem;
    margin-bottom: 5px;
}

.notification-item-time {
    color: var(--shell-gray);
    font-size: 0.7rem;
    text-align: right;
}

.notification-dropdown-footer {
    padding: 10px 15px;
    text-align: center;
    border-top: 1px solid var(--shell-border);
}

.notification-dropdown-footer a {
    color: var(--shell-yellow);
    font-size: 0.9rem;
    text-decoration: none;
}

.notification-dropdown-footer a:hover {
    text-decoration: underline;
}

.empty-notifications {
    padding: 20px;
    text-align: center;
    color: var(--shell-gray);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.empty-notifications i {
    font-size: 2rem;
    margin-bottom: 10px;
    color: var(--shell-gray);
}

/* Responsividade */
@media (max-width: 576px) {
    .notification-dropdown {
        width: 100%;
        position: fixed;
        top: 60px;
        left: 0;
        right: 0;
        max-height: calc(100vh - 60px);
        border-radius: 0;
    }
}
