/*
 * Galeria de Equipamentos - Rede Tradição
 * Estilos para a nova página de galeria de equipamentos
 * Seguindo os padrões visuais do dashboard (fundo escuro, elementos compactos)
 */

/* Estilos gerais */
.card-dark {
    background-color: #1e1e1e;
    border: 1px solid #333;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.card-dark:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    border-color: rgba(253, 184, 19, 0.3);
}

.card-dark .card-header {
    background-color: #252525;
    border-bottom: 1px solid #333;
    padding: 0.75rem 1rem;
    border-radius: 10px 10px 0 0;
}

.card-dark .card-title {
    color: #fff;
    font-weight: 600;
    font-size: 1.1rem;
    margin: 0;
}

.card-dark .card-body {
    padding: 1rem;
}

/* Estilos para os filtros */
.filter-container .form-label {
    color: #adb5bd;
    font-size: 0.85rem;
    margin-bottom: 0.25rem;
}

.filter-container .form-select,
.filter-container .form-control {
    background-color: #2a2a2a;
    border: 1px solid #444;
    color: #fff;
    font-size: 0.9rem;
}

.filter-container .form-select:focus,
.filter-container .form-control:focus {
    background-color: #333;
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 0.2rem rgba(253, 184, 19, 0.25);
}

.filter-container .btn-shell-yellow {
    background-color: var(--shell-yellow);
    color: #222;
    border: none;
    font-weight: 500;
}

.filter-container .btn-shell-yellow:hover {
    background-color: #e6a700;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Estilos para as estatísticas da filial */
.stats-container {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 1rem;
}

.stat-card {
    background: linear-gradient(145deg, #252525, #1a1a1a);
    border-radius: 10px;
    padding: 1.25rem;
    flex: 1;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    border: 1px solid #333;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    border-color: var(--shell-yellow);
}

.stat-icon {
    font-size: 1.5rem;
    color: var(--shell-yellow);
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--shell-yellow);
    font-family: 'Share Tech Mono', monospace;
    text-shadow: 0 0 10px rgba(253, 184, 19, 0.3);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.85rem;
    color: #adb5bd;
    font-weight: 500;
}

/* Estilos para a galeria de equipamentos */
.gallery-container {
    margin-bottom: 2rem;
}

.view-options .btn {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.5rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.view-options .btn.active {
    background-color: var(--shell-yellow);
    color: #222;
}

.view-options .btn:not(.active):hover {
    background-color: #333;
    color: var(--shell-yellow);
}

.equipment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.25rem;
    padding: 1.25rem;
}

.equipment-item {
    transition: all 0.3s ease;
}

.equipment-card {
    background: linear-gradient(145deg, #252525, #1a1a1a);
    border-radius: 12px;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    border: 1px solid #333;
    transition: all 0.3s ease;
}

.equipment-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3);
    border-color: var(--shell-yellow);
}

.equipment-card .card-img-top {
    height: 160px;
    background-color: #151515;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.equipment-card .card-img-top img {
    max-height: 100%;
    max-width: 100%;
    object-fit: contain;
    transition: transform 0.5s ease;
}

.equipment-card:hover .card-img-top img {
    transform: scale(1.1);
}

.equipment-card .card-img-top::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(to right, var(--shell-red), var(--shell-yellow));
    opacity: 0.7;
}

.equipment-card .card-img-top .no-image {
    color: #444;
    font-size: 3rem;
    transition: all 0.3s ease;
}

.equipment-card:hover .card-img-top .no-image {
    color: var(--shell-yellow);
    transform: scale(1.1) rotate(5deg);
}

.equipment-card .card-body {
    padding: 1rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.equipment-card .card-title {
    color: var(--shell-yellow);
    font-weight: 600;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.equipment-card .card-text {
    color: #ddd;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
}

.equipment-card .card-text i {
    color: var(--shell-yellow);
    width: 20px;
    margin-right: 0.5rem;
}

.equipment-card .card-footer {
    background-color: #1a1a1a;
    border-top: 1px solid #333;
    padding: 0.75rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.equipment-card .badge {
    padding: 0.35rem 0.65rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.badge-shell-green {
    background-color: rgba(40, 167, 69, 0.2);
    color: #2ecc71;
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.badge-shell-yellow {
    background-color: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.badge-shell-red {
    background-color: rgba(220, 53, 69, 0.2);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.equipment-card .btn-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #333;
    color: #fff;
    border: 1px solid #444;
    transition: all 0.2s ease;
}

.equipment-card .btn-icon:hover {
    background-color: var(--shell-yellow);
    color: #222;
    transform: translateY(-3px);
}

/* Estilos para a visualização em lista */
.equipment-list {
    padding: 0;
}

.table-dark {
    background-color: #1a1a1a;
    color: #ddd;
    margin-bottom: 0;
}

.table-dark thead th {
    background-color: #252525;
    color: var(--shell-yellow);
    font-weight: 600;
    border-bottom: 2px solid #333;
    padding: 0.75rem 1rem;
}

.table-dark tbody td {
    padding: 0.75rem 1rem;
    vertical-align: middle;
    border-color: #333;
}

.table-dark tbody tr:hover {
    background-color: rgba(253, 184, 19, 0.1);
}

.media-count {
    background-color: var(--shell-yellow);
    color: #222;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 20px;
    font-size: 0.75rem;
}

/* Estilos para o modal de detalhes */
.modal-content {
    background-color: #1e1e1e;
    border: 1px solid #333;
    border-radius: 12px;
}

.modal-header {
    background-color: #252525;
    border-bottom: 1px solid #333;
    padding: 1rem 1.25rem;
}

.modal-title {
    color: #fff;
    font-weight: 600;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    background-color: #252525;
    border-top: 1px solid #333;
    padding: 1rem 1.25rem;
}

.btn-shell {
    background-color: var(--shell-yellow);
    color: #222;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    border: none;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.btn-shell:hover {
    background-color: #e6a700;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Estilos para as informações do equipamento */
.equipment-info h3 {
    font-weight: 700;
    margin-bottom: 0.75rem;
}

.equipment-id {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.equipment-id .badge {
    font-family: 'Share Tech Mono', monospace;
    font-size: 0.9rem;
    padding: 0.4rem 0.8rem;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 0.75rem;
}

.info-label {
    color: #adb5bd;
    font-size: 0.85rem;
    margin-bottom: 0.25rem;
}

.info-label i {
    color: var(--shell-yellow);
}

.info-value {
    color: #fff;
    font-weight: 500;
}

.equipment-notes {
    background-color: #252525;
    border: 1px solid #333;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 0.5rem;
    max-height: 150px;
    overflow-y: auto;
    color: #ddd;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Estilos para a galeria de mídias */
.media-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.media-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 1;
    background-color: #151515;
    border: 1px solid #333;
    transition: all 0.3s ease;
}

.media-item:hover {
    transform: scale(1.05) translateY(-5px);
    border-color: var(--shell-yellow);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.media-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.media-item:hover img {
    transform: scale(1.1);
}

.media-type-icon {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--shell-yellow);
    color: #222;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    z-index: 2;
}

.media-description {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 8px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0));
    color: white;
    font-size: 0.8rem;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.media-item:hover .media-description {
    opacity: 1;
    transform: translateY(0);
}

/* Estado vazio e carregamento */
.empty-state {
    padding: 2rem;
    text-align: center;
    color: #adb5bd;
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--shell-yellow);
    opacity: 0.7;
}

.shell-spinner {
    margin: 0 auto;
    width: 3rem;
    height: 3rem;
    border: 0.25rem solid rgba(253, 184, 19, 0.2);
    border-radius: 50%;
    border-top-color: var(--shell-yellow);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.equipment-grid {
    animation: fadeIn 0.5s ease;
}

.equipment-card {
    animation: fadeIn 0.5s ease;
}

/* Responsividade */
@media (max-width: 1200px) {
    .equipment-grid {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }

    .info-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}

@media (max-width: 992px) {
    .equipment-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        padding: 1rem;
    }

    .stats-container {
        flex-wrap: wrap;
    }

    .stat-card {
        min-width: 120px;
    }

    .modal-dialog {
        max-width: 95%;
    }
}

@media (max-width: 768px) {
    .equipment-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        padding: 0.75rem;
    }

    .stats-container {
        flex-direction: column;
    }

    .stat-card {
        margin-bottom: 0.75rem;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .media-gallery {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
}

@media (max-width: 576px) {
    .equipment-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        padding: 0.5rem;
        gap: 0.75rem;
    }

    .equipment-card .card-img-top {
        height: 140px;
    }

    .equipment-card .card-body {
        padding: 0.75rem;
    }

    .equipment-card .card-title {
        font-size: 1rem;
    }

    .equipment-card .card-text {
        font-size: 0.8rem;
    }

    .media-gallery {
        grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
        gap: 0.75rem;
    }
}
