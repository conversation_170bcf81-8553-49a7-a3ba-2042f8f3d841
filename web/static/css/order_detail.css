/* Estilos para badges baseados em status */
.status-badge { 
    padding: 0.4em 0.7em; 
    font-size: 0.9em; 
    color: white; 
    border-radius: 0.25rem; 
}

.status-pending { background-color: var(--bs-secondary); }
.status-rejected_provider { background-color: var(--bs-warning); color: black; }
.status-in_progress { background-color: var(--bs-primary); }
.status-pending_approval { background-color: var(--bs-info); }
.status-revision_needed { background-color: var(--bs-warning); color: black; }
.status-approved { background-color: var(--bs-success); }
.status-invoiced { background-color: var(--bs-purple); }
.status-paid { background-color: var(--bs-success); }
.status-cancelled { background-color: var(--bs-danger); }

/* Estilos para cards de detalhes */
.detail-card {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--shell-yellow);
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.detail-card h5 { color: var(--shell-yellow); }
.detail-card i.icon { 
    font-size: 2rem; 
    margin-bottom: 1rem; 
    color: var(--shell-yellow); 
}

.detail-card-content { 
    flex-grow: 1; 
    display: flex; 
    flex-direction: column; 
    justify-content: center; 
}

.detail-card-action { margin-top: 1rem; }
.detail-card-action .shell-btn-sm { 
    font-size: 0.8rem; 
    padding: 0.25rem 0.5rem; 
}

/* Estilos para itens de interação */
.interaction-item {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 0.75rem 0;
}

.interaction-item:last-child { border-bottom: none; }
.interaction-meta { 
    font-size: 0.8em; 
    color: #ccc; 
}

/* Estilos para modais */
.modal-content.bg-dark { background-color: var(--bs-dark); }
.modal-header.border-secondary { border-color: var(--bs-secondary); }
.modal-footer.border-secondary { border-color: var(--bs-secondary); }

/* Estilos para toasts */
.toast-container {
    position: fixed;
    top: 0;
    right: 0;
    padding: 1rem;
    z-index: 1055;
} 