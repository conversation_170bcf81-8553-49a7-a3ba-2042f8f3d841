/* Estilos para a página Alterar Senha */

/* Variáveis de cores */
:root {
    --shell-red: #ED1C24;
    --shell-red-hover: #CE181F;
    --shell-yellow: #FDB813;
    --shell-yellow-hover: #e0a100;
    --shell-dark: #333333;
    --shell-darker: #222222;
    --shell-light: #f8f9fa;
    --shell-gray: #808080;
    --shell-success: #28a745;
    --shell-info: #17a2b8;
    --shell-warning: #ffc107;
    --shell-danger: #dc3545;
}

/* Estilos gerais da página */
body {
    background-color: var(--shell-dark);
    color: var(--shell-light);
    font-family: 'Rajdhani', sans-serif;
}

.container-fluid {
    max-width: 1600px;
    margin: 0 auto;
}

/* Cabe<PERSON><PERSON><PERSON> da página */
.page-header {
    margin-bottom: 2rem;
}

.page-title {
    font-family: 'Raj<PERSON><PERSON>', sans-serif;
    font-weight: 700;
    font-size: 1.8rem;
    color: var(--shell-red);
    margin-bottom: 0.25rem;
}

.page-subtitle {
    font-family: 'Share Tech Mono', monospace;
    font-size: 0.9rem;
    color: var(--shell-yellow);
    opacity: 0.9;
}

/* Cards */
.card-shell {
    background-color: #2a2a2a;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--shell-red);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 1.5rem;
}

.card-shell:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
    border-color: var(--shell-yellow);
}

.card-header-shell {
    background-color: #222222;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--shell-red);
    display: flex;
    align-items: center;
}

.card-body-shell {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.card-title {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--shell-yellow);
    margin: 0;
    text-transform: uppercase;
}

/* Formulário */
.password-form {
    width: 100%;
}

.form-label {
    color: var(--shell-yellow);
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.form-control {
    background-color: #444;
    border: 1px solid #555;
    color: var(--shell-light);
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
}

.form-control:focus {
    background-color: #4a4a4a;
    color: var(--shell-light);
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 0.25rem rgba(253, 184, 19, 0.25);
}

/* Grupo de input de senha */
.password-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.password-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--shell-gray);
    cursor: pointer;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: var(--shell-yellow);
}

/* Medidor de força da senha */
.password-strength {
    margin-top: 0.5rem;
}

.strength-meter {
    height: 5px;
    background-color: #444;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.25rem;
}

.strength-meter-fill {
    height: 100%;
    width: 0;
    background-color: var(--shell-danger);
    transition: width 0.3s ease, background-color 0.3s ease;
}

.strength-meter-fill.weak {
    background-color: var(--shell-danger);
    width: 25%;
}

.strength-meter-fill.medium {
    background-color: var(--shell-warning);
    width: 50%;
}

.strength-meter-fill.strong {
    background-color: var(--shell-info);
    width: 75%;
}

.strength-meter-fill.very-strong {
    background-color: var(--shell-success);
    width: 100%;
}

.strength-text {
    font-size: 0.8rem;
    color: var(--shell-gray);
}

/* Requisitos de senha */
.password-requirements {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.requirements-title {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1rem;
    color: var(--shell-yellow);
    margin-bottom: 0.75rem;
}

.requirements-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.requirements-list li {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--shell-gray);
}

.requirements-list li i {
    font-size: 0.6rem;
    margin-right: 0.5rem;
}

.requirements-list li.valid {
    color: var(--shell-success);
}

.requirements-list li.valid i {
    color: var(--shell-success);
}

/* Botões */
.form-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1.5rem;
}

.btn-shell-red {
    background-color: var(--shell-red);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    cursor: pointer;
}

.btn-shell-red:hover {
    background-color: var(--shell-red-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    color: white;
}

.btn-shell {
    background-color: #333333;
    color: var(--shell-light);
    border: 1px solid var(--shell-yellow);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    cursor: pointer;
}

.btn-shell:hover {
    background-color: #444444;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    color: var(--shell-light);
}

/* Dicas de segurança */
.security-tips-card {
    height: 100%;
}

.security-tips {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.security-tip {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border: 1px solid rgba(237, 28, 36, 0.3);
    transition: all 0.3s ease;
}

.security-tip:hover {
    border-color: var(--shell-red);
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.tip-icon {
    width: 40px;
    height: 40px;
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.tip-content {
    flex-grow: 1;
}

.tip-content h4 {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1rem;
    color: var(--shell-yellow);
    margin-bottom: 0.25rem;
}

.tip-content p {
    font-size: 0.85rem;
    color: var(--shell-gray);
    margin-bottom: 0;
}

/* Modais */
.modal-shell {
    background-color: #2a2a2a;
    color: var(--shell-light);
    border: 1px solid var(--shell-red);
}

.modal-shell .modal-header {
    background-color: #222222;
    border-bottom: 2px solid var(--shell-red);
    padding: 1rem 1.5rem;
}

.modal-shell .modal-title {
    color: var(--shell-yellow);
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
}

.modal-shell .modal-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 1.5rem;
}

.modal-shell .btn-close {
    color: white;
    opacity: 0.8;
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Responsividade */
@media (max-width: 1199.98px) {
    .container-fluid {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (max-width: 991.98px) {
    .page-title {
        font-size: 1.5rem;
    }
    
    .page-subtitle {
        font-size: 0.85rem;
    }
    
    .security-tips-card {
        margin-top: 1.5rem;
    }
}

@media (max-width: 767.98px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .page-title {
        font-size: 1.3rem;
    }
    
    .page-subtitle {
        font-size: 0.8rem;
    }
    
    .card-header-shell {
        padding: 0.5rem 0.75rem;
    }
    
    .card-body-shell {
        padding: 1rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions > * {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
