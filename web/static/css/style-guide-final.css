/* <PERSON><PERSON><PERSON> <PERSON> Estilo <PERSON> Final para o Site */

/* Cores */
:root {
    --shell-yellow: #FDB813;
    --shell-red: #ED1C24;
    --shell-blue: #005DB9;
    --shell-light-blue: #0077C8;
    --shell-dark: #1E1E1E;
    --shell-gray: #6C757D;
    --shell-light-gray: #F8F9FA;
    --shell-gradient: linear-gradient(180deg, var(--shell-dark) 0%, #2D2D2D 100%);
    --shell-shadow: 0 2px 4px rgba(0,0,0,0.1);
    --shell-radius: 8px;
}

body {
    background-color: var(--shell-light-gray);
    color: var(--shell-dark);
    font-family: Arial, sans-serif;
}

.sidebar {
    background: var(--shell-gradient);
    width: 250px;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    padding: 1rem;
    transition: transform 0.3s;
    z-index: 1000;
}

.menu-item a:hover,
.menu-item.active a {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--shell-blue);
    color: var(--shell-dark);
}

.section-header {
    background-color: var(--shell-blue);
    color: white;
    padding: 1rem;
    border-radius: var(--shell-radius) var(--shell-radius) 0 0;
    margin-top: 2rem;
}

.btn-shell {
    background-color: var(--shell-blue);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--shell-radius);
    transition: all 0.3s;
}

.photo-upload-container {
    border: 2px dashed var(--shell-blue);
    border-radius: var(--shell-radius);
    padding: 2rem;
    text-align: center;
    background-color: rgba(0,93,185,0.05);
    transition: all 0.3s;
}

/* Estilos de Botões */
.shell-btn {
    background: linear-gradient(45deg, #FFCC00, #FF9900); /* Degradê de amarelo */
    color: #000;
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s ease, transform 0.2s ease;
}

.shell-btn:hover {
    background: linear-gradient(45deg, #FF9900, #FFCC00); /* Degradê invertido no hover */
    transform: translateY(-2px);
}

/* Estilos de Cartões */
.stats-card {
    background-color: var(--shell-blue);
    border-radius: var(--shell-radius);
    padding: 15px;
    box-shadow: var(--shell-shadow);
    color: #fff;
}

.stats-card-title {
    font-size: 1.2rem;
    font-weight: bold;
}

.stats-card-value {
    font-size: 2rem;
    font-weight: bold;
}

/* Estilos de Ordens Urgentes */
.urgent-styled {
    border-left: 4px solid var(--shell-red) !important;
    background-color: rgba(220, 53, 69, 0.1);
    box-shadow: var(--shell-shadow);
    position: relative;
}

/* Estilos de Layout */
.dashboard-container {
    max-width: 1500px;
    margin: auto;
    padding: 20px;
}

.calendar-dashboard-container {
    display: flex;
    flex-direction: column;
    padding: 20px;
}

/* Estilos de Títulos */
.page-title {
    font-size: 2rem;
    color: var(--shell-yellow);
}

h4 {
    color: var(--shell-yellow);
}

/* Estilos de Responsividade */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 10px;
    }
    
    .calendar-dashboard-container {
        padding: 10px;
    }
}
