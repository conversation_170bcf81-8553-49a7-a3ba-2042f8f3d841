/* Estilos específicos para o dashboard */

:root {
    --shell-red: #ED1C24;
    --shell-yellow: #f4b31d;
}

/* Borda estilo bomba de combustível */
.pump-border {
    border: 4px solid var(--shell-yellow);
    border-radius: 15px;
    padding: 15px;
    background: rgba(30, 30, 30, 0.8);
    position: relative;
    height: 100%;
    transition: all 0.4s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    color: white;
    backdrop-filter: blur(8px);
}

.pump-border::before {
    content: '';
    position: absolute;
    top: -10px;
    right: 20px;
    width: 30px;
    height: 16px;
    background: var(--shell-red);
    border-radius: 8px 8px 0 0;
    z-index: -1;
}

.pump-border::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    pointer-events: none;
    border-radius: 12px;
}

/* Estilos para o layout de caixas do dashboard */
.dashboard-boxes-container {
    padding: 15px;
}

/* Cabeçalho das caixas */
.shell-header {
    background: linear-gradient(135deg, var(--shell-yellow) 0%, var(--shell-red) 100%);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Placeholder para detalhes da ordem */
.order-detail-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px;
    color: #777;
    text-align: center;
}

/* Caixa de ordens abertas */
.orders-container {
    background-color: rgba(139, 69, 19, 0.1); /* Fundo marrom suave */
    border: 2px solid var(--shell-yellow);
    min-height: 500px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Visualização de ordem */
.order-detail-container {
    display: none;
    padding: 20px;
    margin-top: 20px;
}

/* Área de filtros */
.order-filters {
    margin-bottom: 15px;
}

/* Grid de ordens de serviço */
.service-orders-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

/* Card de ordem de serviço */
.service-order-item {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid #444;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.service-order-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border-color: var(--shell-yellow);
}

.service-order-item .order-title {
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 10px;
    color: var(--shell-yellow);
}

.service-order-item .order-status {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 8px;
}

.service-order-item .order-status.pending {
    background-color: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    border: 1px solid #ffc107;
}

.service-order-item .order-status.in-progress {
    background-color: rgba(13, 110, 253, 0.2);
    color: #0d6efd;
    border: 1px solid #0d6efd;
}

.service-order-item .order-status.completed {
    background-color: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid #28a745;
}

.service-order-item .order-status.rejected {
    background-color: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border: 1px solid #dc3545;
}

.service-order-item .order-priority {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
}

.service-order-item .order-priority.low {
    background-color: #28a745;
}

.service-order-item .order-priority.medium {
    background-color: #ffc107;
}

.service-order-item .order-priority.high {
    background-color: #fd7e14;
}

.service-order-item .order-priority.urgent {
    background-color: #dc3545;
    animation: pulse 1.5s infinite;
}

/* Animações */
@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
}

/* Efeito "medidor de combustível" */
.fuel-gauge {
    height: 8px;
    width: 100%;
    background: linear-gradient(90deg, 
        var(--shell-red) 0%, 
        var(--shell-red) 20%, 
        var(--shell-yellow) 20%, 
        var(--shell-yellow) 40%, 
        var(--shell-red) 40%, 
        var(--shell-red) 60%, 
        var(--shell-yellow) 60%, 
        var(--shell-yellow) 80%, 
        var(--shell-red) 80%, 
        var(--shell-red) 100%);
    border-radius: 4px;
    margin-top: 10px;
}

/* Botão estilo Shell */
.shell-btn {
    background: var(--shell-red);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 0 10px rgba(237, 28, 36, 0.5);
}

.shell-btn:hover {
    background: #c51017;
    transform: translateY(-2px);
    box-shadow: 0 0 15px rgba(237, 28, 36, 0.8);
}

.shell-btn-yellow {
    background: var(--shell-yellow);
    color: #333;
    box-shadow: 0 0 10px rgba(244, 179, 29, 0.5);
}

.shell-btn-yellow:hover {
    background: #d9980a;
    box-shadow: 0 0 15px rgba(244, 179, 29, 0.8);
}

/* Estilo para o calendário no dashboard */
.calendar-dashboard {
    border-radius: 10px;
    padding: 15px;
    background-color: rgba(255, 255, 255, 0.05);
    margin-bottom: 20px;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.current-month {
    font-size: 18px;
    font-weight: bold;
    color: var(--shell-yellow);
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
}

.calendar-day {
    border: 1px solid #444;
    border-radius: 5px;
    padding: 8px;
    min-height: 60px;
    background-color: rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.calendar-day:hover {
    background-color: rgba(244, 179, 29, 0.1);
    border-color: var(--shell-yellow);
}

.calendar-day.has-events {
    border-left: 3px solid var(--shell-red);
}

.day-number {
    font-weight: bold;
    margin-bottom: 5px;
}

.day-events {
    font-size: 11px;
    color: #ddd;
}

.calendar-day.today {
    background-color: rgba(237, 28, 36, 0.1);
    border: 1px solid var(--shell-red);
}

.calendar-day.other-month {
    opacity: 0.5;
}

.weekday-headers {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
    margin-bottom: 10px;
    text-align: center;
}

.weekday-header {
    font-weight: bold;
    color: var(--shell-yellow);
    padding: 5px 0;
}

/* Estilo para cartões de estatísticas e métricas */
.stats-card {
    background-color: rgba(30, 30, 30, 0.7);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.stats-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--shell-red), var(--shell-yellow));
}

.stats-card-title {
    font-size: 14px;
    color: #aaa;
    margin-bottom: 5px;
}

.stats-card-value {
    font-size: 32px;
    font-weight: bold;
    color: white;
    margin-bottom: 10px;
}

.stats-card-pending .stats-card-value {
    color: var(--shell-yellow);
}

.stats-card-progress .stats-card-value {
    color: #007bff;
}

.stats-card-completed .stats-card-value {
    color: #28a745;
}

.stats-card-rejected .stats-card-value {
    color: var(--shell-red);
}

.stats-card-trend {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #888;
}

.stats-card-trend .trend-up {
    color: #28a745;
}

.stats-card-trend .trend-down {
    color: var(--shell-red);
}

/* Estilo para gráficos */
.chart-container {
    background-color: rgba(30, 30, 30, 0.7);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    min-height: 300px;
    position: relative;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}