/* 
 * Estilos específicos para o perfil no sidebar
 * Este arquivo contém apenas os estilos para o perfil do usuário no sidebar
 */

/* Avatar do perfil */
.profile-avatar {
    width: 46px !important;
    height: 46px !important;
    border-radius: 50% !important;
    background: #ED1C24 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin-right: 15px !important;
    font-weight: 700 !important;
    font-size: 18px !important;
    box-shadow: 0 0 15px rgba(237, 28, 36, 0.6) !important;
    border: 2px solid rgba(255, 255, 255, 0.15) !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    color: rgba(255, 255, 255, 0.95) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    transition: transform 0.3s ease, box-shadow 0.3s ease !important;
}

.profile-avatar:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 0 20px rgba(237, 28, 36, 0.8) !important;
}

/* Informações do perfil */
.profile-info {
    white-space: nowrap !important;
    overflow: hidden !important;
    padding-left: 2px !important;
}

.profile-name {
    font-weight: 600 !important;
    font-size: 15px !important;
    letter-spacing: 0.3px !important;
    color: rgba(255, 255, 255, 0.95) !important;
    margin-bottom: 3px !important;
    font-family: 'Rajdhani', sans-serif !important;
}

.profile-role {
    font-size: 12px !important;
    color: rgba(255, 255, 255, 0.6) !important;
    font-weight: 400 !important;
    letter-spacing: 0.2px !important;
    text-transform: capitalize !important;
    font-family: 'Rajdhani', sans-serif !important;
}

/* Área do perfil */
.sidebar-profile {
    padding: 20px 15px !important;
    display: flex !important;
    align-items: center !important;
    border-bottom: 1px solid #333 !important;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.3), rgba(40, 40, 40, 0.2)) !important;
    position: relative !important;
    overflow: hidden !important;
}

.sidebar-profile::after {
    content: '' !important;
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 1px !important;
    background: linear-gradient(to right, transparent, #f4b31d, transparent) !important;
    opacity: 0.5 !important;
}

/* Comportamento quando colapsado */
.tradicio-sidebar.collapsed .profile-avatar {
    margin-right: 0 !important;
    width: 48px !important;
    height: 48px !important;
    font-size: 20px !important;
}

.tradicio-sidebar.collapsed .sidebar-profile {
    justify-content: center !important;
    padding: 15px 0 !important;
}
