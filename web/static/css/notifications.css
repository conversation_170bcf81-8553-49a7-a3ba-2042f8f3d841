.notifications-container {
    max-width: 800px;
    margin: 0 auto;
}

.notification-card {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.notification-card.unread {
    border-left-color: var(--shell-red);
    background-color: rgba(237, 28, 36, 0.1);
}

.notification-type {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: 'Share Tech Mono', monospace;
    text-transform: uppercase;
    font-size: 0.75rem;
}

.badge-shell-info {
    background-color: var(--info);
    color: #fff;
}

.badge-shell-warning {
    background-color: var(--warning);
    color: #000;
}

.badge-shell-error {
    background-color: var(--danger);
    color: #fff;
}

.badge-shell-success {
    background-color: var(--success);
    color: #fff;
}

.notification-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.notification-card .card-title {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.notification-card .card-text {
    color: #808080;
    margin-bottom: 0;
}

.notification-card .text-muted {
    font-family: 'Share Tech Mono', monospace;
    font-size: 0.875rem;
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(-100%);
    }
}

.notification-card.marking-as-read {
    animation: fadeOut 0.5s ease forwards;
} 