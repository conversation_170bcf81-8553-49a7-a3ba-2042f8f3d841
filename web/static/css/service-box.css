/* Estilos para os service-box usados na página de manutenção de ordens */

.service-box {
    background: linear-gradient(135deg, rgba(40, 40, 40, 0.7), rgba(30, 30, 30, 0.9));
    border: 1px solid var(--shell-yellow);
    border-radius: 10px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.service-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 50%);
    pointer-events: none;
}

.service-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    border-color: #ffcc00;
}

.service-box-icon {
    width: 50px;
    height: 50px;
    background-color: var(--shell-red);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.service-box h6 {
    color: var(--shell-yellow);
    margin-bottom: 10px;
    font-weight: 600;
    text-align: center;
    font-size: 1.1rem;
}

.service-box p {
    color: #ddd;
    text-align: center;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.service-box .text-muted {
    color: #aaa !important;
    font-size: 0.85rem;
}

.service-box .text-warning {
    color: var(--shell-yellow) !important;
}

.timeline-simple {
    margin: 10px 0;
    font-size: 0.85rem;
    color: #ddd;
}

.timeline-simple p {
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.timeline-simple i {
    font-size: 0.9rem;
}

/* Estilos para o painel de detalhes da ordem */
.glass-panel {
    background: rgba(30, 30, 30, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 1px solid var(--shell-yellow);
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    color: #fff;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(255, 204, 0, 0.3);
    padding-bottom: 15px;
}

.panel-header h3 {
    color: var(--shell-yellow);
    margin: 0;
    font-weight: 600;
}

.back-to-calendar {
    background-color: var(--shell-red);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.back-to-calendar:hover {
    background-color: #c51017;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.order-detail-panel {
    color: #fff;
}

.order-detail-panel h4 {
    color: var(--shell-yellow) !important;
    margin-bottom: 15px;
    font-weight: 600;
}

.order-detail-panel .text-dark {
    color: var(--shell-yellow) !important;
}

.order-detail-panel .text-muted {
    color: #aaa !important;
}

.order-detail-panel .border-start {
    border-left: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.order-detail-panel .border-bottom {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Botões de ação */
.shell-btn {
    background-color: var(--shell-red);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.shell-btn:hover {
    background-color: #c51017;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.shell-btn-success {
    background-color: #28a745;
}

.shell-btn-success:hover {
    background-color: #218838;
}

.shell-btn-danger {
    background-color: #dc3545;
}

.shell-btn-danger:hover {
    background-color: #c82333;
}

/* Responsividade */
@media (max-width: 767.98px) {
    .panel-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .order-detail-panel .col-md-4.border-start {
        border-left: none !important;
        border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
        margin-top: 20px;
        padding-top: 20px;
    }
}
