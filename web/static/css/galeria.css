/*
 * Galeria de Equipamentos - Rede Tradição
 * Estilos para a página de galeria de equipamentos
 * Seguindo os padrões visuais do projeto
 */

/* Estilos para o cabeçalho animado */
.header-animation-container {
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.shell-header {
    background: linear-gradient(145deg, #1a1a1a, #2a2a2a);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    border: 1px solid #333;
    z-index: 1;
}

.shell-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(to right, var(--shell-red), var(--shell-yellow));
    z-index: 2;
}

.header-title {
    color: var(--shell-yellow);
    font-size: 2rem;
    font-weight: 700;
    text-align: center;
    margin: 0;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    letter-spacing: 2px;
    text-shadow: 0 0 10px rgba(253, 184, 19, 0.5);
    animation: pulse 2s infinite;
}

.led-display {
    background-color: #111;
    border-radius: 10px;
    padding: 0.75rem;
    overflow: hidden;
    position: relative;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.8), 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid #333;
}

.led-text {
    color: #f4b31d;
    font-family: 'Share Tech Mono', monospace;
    font-size: 1.1rem;
    white-space: nowrap;
    text-shadow: 0 0 5px rgba(244, 179, 29, 0.7);
    animation: marquee 20s linear infinite;
}

/* Estilos para a seção de filtros */
.filter-section {
    margin-bottom: 2rem;
}

.glass-effect {
    background: rgba(30, 30, 30, 0.7);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.glass-effect:hover {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 15px rgba(253, 184, 19, 0.2);
    border-color: rgba(253, 184, 19, 0.3);
}

.section-title {
    color: var(--shell-yellow);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-title i {
    color: var(--shell-yellow);
    font-size: 1.25rem;
}

.shell-divider-animated {
    height: 2px;
    background: linear-gradient(to right, transparent, var(--shell-yellow), transparent);
    margin: 1rem 0;
    position: relative;
    overflow: hidden;
}

.shell-divider-animated::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.8), transparent);
    animation: shine 3s infinite;
}

.form-select-shell {
    background-color: rgba(20, 20, 20, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #fff;
    transition: all 0.3s ease;
}

.form-select-shell:focus {
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 0.25rem rgba(253, 184, 19, 0.25);
}

.view-options {
    display: flex;
    gap: 0.5rem;
}

.btn-outline-shell-yellow {
    border-color: var(--shell-yellow);
    color: var(--shell-yellow);
    background-color: transparent;
}

.btn-outline-shell-yellow:hover,
.btn-outline-shell-yellow.active {
    background-color: var(--shell-yellow);
    color: #222;
}

/* Estilos para o card de boas-vindas */
.welcome-card {
    padding: 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 1.25rem;
    height: 100%;
}

.welcome-icon {
    font-size: 2.5rem;
    color: var(--shell-yellow);
    background: rgba(0, 0, 0, 0.2);
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 0 15px rgba(253, 184, 19, 0.3);
    flex-shrink: 0;
}

.welcome-content {
    flex-grow: 1;
}

.welcome-content h2 {
    color: var(--shell-yellow);
    font-size: 1.75rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.welcome-message {
    color: #ddd;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.branch-stats-card {
    padding: 1.5rem;
    height: 100%;
}

.branch-stats-card h3 {
    color: var(--shell-yellow);
    font-size: 1.25rem;
    margin-bottom: 1.25rem;
    font-weight: 600;
}

/* Estilos para a seção de informações da filial */
.branch-info h3 {
    color: var(--shell-yellow);
    margin-bottom: 1rem;
    font-weight: 600;
}

.branch-stats {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 1rem;
    gap: 1rem;
}

.stat-item {
    background: rgba(30, 30, 30, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1.25rem;
    flex: 1;
    min-width: 120px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    border-color: var(--shell-yellow);
}

.stat-label {
    font-size: 0.9rem;
    color: #adb5bd;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--shell-yellow);
    font-family: 'Share Tech Mono', monospace;
    text-shadow: 0 0 10px rgba(253, 184, 19, 0.3);
}

/* Estilos para a galeria de equipamentos */
.equipment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
    width: 100%;
    margin: 0;
}

.equipment-item {
    width: 100%;
    margin: 0;
    padding: 0;
}

.equipment-card {
    background: linear-gradient(145deg, #2a2a2a, #222222);
    border-radius: 15px;
    border: 1px solid #333;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    margin: 0;
    width: 100%;
}

.equipment-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.25);
    border-color: var(--shell-yellow);
}

.equipment-card .card-img-top {
    height: 180px;
    object-fit: cover;
    background-color: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    border-bottom: 1px solid #333;
}

.equipment-card .card-img-top::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(to right, var(--shell-red), var(--shell-yellow));
    opacity: 0.7;
}

.equipment-card .card-img-top img {
    max-height: 100%;
    max-width: 100%;
    object-fit: contain;
    transition: transform 0.5s ease;
}

.equipment-card:hover .card-img-top img {
    transform: scale(1.05);
}

.equipment-card .card-img-top .no-image {
    color: #555;
    font-size: 3rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.equipment-card:hover .card-img-top .no-image {
    color: var(--shell-yellow);
    transform: scale(1.1) rotate(5deg);
}

.equipment-card .card-body {
    padding: 1rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    background-color: #222;
}

.equipment-card .card-title {
    color: var(--shell-yellow);
    font-weight: 600;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.equipment-card .card-text {
    color: var(--shell-light-gray);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.equipment-card .card-footer {
    background-color: #1a1a1a;
    border-top: 1px solid #333;
    padding: 0.75rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

.equipment-card .badge-shell {
    padding: 0.35rem 0.65rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-block;
}

.equipment-card .badge-shell-green {
    background-color: rgba(40, 167, 69, 0.2);
    color: #2ecc71;
    border: 1px solid rgba(46, 204, 113, 0.3);
    text-align: center;
}

.equipment-card .badge-shell-yellow {
    background-color: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
    text-align: center;
}

.equipment-card .badge-shell-red {
    background-color: rgba(220, 53, 69, 0.2);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
    text-align: center;
}

.equipment-card .card-actions {
    display: flex;
    gap: 0.5rem;
}

.equipment-card .btn-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--medium-dark);
    color: var(--shell-white);
    border: 1px solid var(--light-dark);
    transition: all 0.2s ease;
}

.equipment-card .btn-icon:hover {
    background-color: var(--shell-yellow);
    color: var(--dark-bg);
}

/* Estilos para a visualização em lista */
.equipment-list {
    margin-top: 1rem;
}

.equipment-list .table-responsive {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.equipment-list .table {
    margin-bottom: 0;
}

.equipment-list th {
    background-color: var(--shell-red);
    color: white;
    font-weight: 600;
    border: none;
    padding: 12px 15px;
}

.equipment-list td {
    vertical-align: middle;
    padding: 12px 15px;
    border-color: rgba(255, 255, 255, 0.1);
}

.equipment-list tbody tr:hover {
    background-color: rgba(253, 184, 19, 0.1);
}

.equipment-list .media-count {
    background-color: var(--shell-yellow);
    color: var(--dark-bg);
    font-weight: 600;
    padding: 0.25rem 0.6rem;
    border-radius: 20px;
    display: inline-block;
    font-size: 0.85rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Estilos para o modal de detalhes */
.modal-content {
    background-color: #222;
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.25rem 1.5rem;
}

.modal-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.25rem 1.5rem;
}

.equipment-info h3 {
    color: var(--shell-yellow);
    margin-bottom: 1.25rem;
    font-weight: 700;
    font-size: 1.75rem;
    text-shadow: 0 0 10px rgba(253, 184, 19, 0.2);
}

.equipment-info p {
    margin-bottom: 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
}

.equipment-info strong {
    color: var(--shell-yellow);
    font-weight: 600;
}

.equipment-notes {
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1.25rem;
    margin-top: 0.75rem;
    max-height: 180px;
    overflow-y: auto;
    line-height: 1.6;
    color: #ddd;
}

/* Estilos para a galeria de mídias */
.media-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    gap: 1rem;
    margin-top: 1.25rem;
}

.media-item {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    aspect-ratio: 1;
    background-color: #1a1a1a;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.media-item:hover {
    transform: scale(1.05) translateY(-5px);
    border-color: var(--shell-yellow);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.media-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.media-item:hover img {
    transform: scale(1.1);
}

.media-item .media-type-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: var(--shell-yellow);
    color: #222;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    z-index: 2;
}

.media-item .media-description {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 12px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0));
    color: white;
    font-size: 0.85rem;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.media-item:hover .media-description {
    opacity: 1;
    transform: translateY(0);
}

/* Estado vazio e carregamento */
.empty-state {
    padding: 2rem;
    text-align: center;
    color: #adb5bd;
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--shell-yellow);
    opacity: 0.7;
}

.shell-spinner {
    margin: 0 auto;
    width: 3rem;
    height: 3rem;
    border: 0.25rem solid rgba(253, 184, 19, 0.2);
    border-radius: 50%;
    border-top-color: var(--shell-yellow);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes pulse {
    0% { text-shadow: 0 0 10px rgba(253, 184, 19, 0.5); }
    50% { text-shadow: 0 0 20px rgba(253, 184, 19, 0.8), 0 0 30px rgba(253, 184, 19, 0.4); }
    100% { text-shadow: 0 0 10px rgba(253, 184, 19, 0.5); }
}

@keyframes marquee {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

@keyframes shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.equipment-grid {
    animation: fadeIn 0.5s ease;
}

.equipment-card {
    animation: fadeIn 0.5s ease;
}

.fade-in {
    opacity: 0;
    animation: fadeIn 0.5s ease forwards;
}

.delay-1 {
    animation-delay: 0.2s;
}

.delay-2 {
    animation-delay: 0.4s;
}

.delay-3 {
    animation-delay: 0.6s;
}

.delay-4 {
    animation-delay: 0.8s;
}

/* Responsividade */
@media (max-width: 1200px) {
    .equipment-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1.25rem;
    }
}

@media (max-width: 992px) {
    .equipment-grid {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 1rem;
        padding: 0.75rem;
    }

    .modal-dialog {
        max-width: 95%;
    }
}

@media (max-width: 768px) {
    .branch-stats {
        flex-direction: column;
    }

    .stat-item {
        margin-bottom: 1rem;
    }

    .equipment-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 0.75rem;
        padding: 0.5rem;
    }

    .media-gallery {
        grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
    }

    .equipment-info h3 {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .equipment-card .card-img-top {
        height: 150px;
    }

    .equipment-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 0.5rem;
        padding: 0.5rem;
    }

    .equipment-card .card-title {
        font-size: 1rem;
    }

    .equipment-card .card-text {
        font-size: 0.8rem;
    }

    .equipment-card .card-body {
        padding: 0.75rem;
    }

    .equipment-card .card-footer {
        padding: 0.5rem 0.75rem;
    }

    .media-gallery {
        grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
        gap: 0.75rem;
    }

    .modal-header, .modal-footer {
        padding: 1rem;
    }

    .modal-body {
        padding: 1rem;
    }
}
