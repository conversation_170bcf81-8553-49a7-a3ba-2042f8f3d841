/**
 * Estilos para Modais do Sistema - Rede Tradição Shell
 */

/* Base do Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    overflow: auto;
    padding: 2rem;
}

.modal.show {
    display: flex !important;
    opacity: 1;
    animation: modalFade 0.3s ease forwards;
}

/* Animação de entrada */
@keyframes modalFade {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Conteúdo do Modal */
.modal-content {
    background-color: var(--shell-dark);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    max-width: 90%;
    width: 500px;
    position: relative;
    padding: 0;
    animation: modalContentSlide 0.3s ease forwards;
}

@keyframes modalContentSlide {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Cabeçalho do Modal */
.modal-header {
    background: linear-gradient(90deg, rgba(237, 28, 36, 0.8), rgba(206, 24, 31, 0.8));
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 10px 10px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
}

.modal-header h3 i {
    margin-right: 0.75rem;
    color: var(--shell-yellow);
}

/* Botão de Fechar */
.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.modal-close:hover {
    color: var(--shell-yellow);
}

/* Corpo do Modal */
.modal-body {
    padding: 1.5rem;
}

/* Modal Grande */
.modal-large {
    width: 90%;
    max-width: 1000px;
} 