/* Estilos para o gráfico de cotações aprimorado */

/* Layout principal */
.cotacoes-enhanced-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    background-color: #222;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Cabeçalho */
.cotacoes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 15px;
}

.cotacoes-title {
    font-family: 'Raj<PERSON>ni', sans-serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: #FDB813;
    margin: 0;
}

.cotacoes-actions {
    display: flex;
    gap: 10px;
}

/* Grid de cards de cotações */
.cotacoes-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.cotacao-mini-card {
    background-color: #333;
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
}

.cotacao-mini-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

.cotacao-mini-card.selected {
    border-color: #FDB813;
    background-color: rgba(253, 184, 19, 0.1);
}

.cotacao-mini-card.selected::after {
    content: '✓';
    position: absolute;
    top: 8px;
    right: 8px;
    color: #FDB813;
    font-weight: bold;
}

.cotacao-mini-card-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.cotacao-mini-card-title {
    font-family: 'Rajdhani', sans-serif;
    font-size: 0.9rem;
    font-weight: 600;
    color: #ccc;
    margin: 0;
}

.cotacao-mini-card-symbol {
    font-family: 'Share Tech Mono', monospace;
    font-size: 0.8rem;
    color: #999;
}

.cotacao-mini-card-value {
    font-family: 'Share Tech Mono', monospace;
    font-size: 1.2rem;
    font-weight: 600;
    color: #fff;
    margin-bottom: 8px;
}

.cotacao-mini-card-trend {
    display: flex;
    align-items: center;
    font-family: 'Share Tech Mono', monospace;
    font-size: 0.9rem;
}

.cotacao-mini-card-trend.positive {
    color: #28a745;
}

.cotacao-mini-card-trend.negative {
    color: #dc3545;
}

.cotacao-mini-card-trend i {
    margin-right: 5px;
}

/* Área do gráfico */
.cotacoes-chart-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 500px; /* Aumentado para 3/4 da página */
    background-color: #333;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.cotacoes-chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.cotacoes-chart-title {
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    color: #fff;
    margin: 0;
}

.cotacoes-chart-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.cotacoes-chart-legend-item {
    display: flex;
    align-items: center;
    font-family: 'Rajdhani', sans-serif;
    font-size: 1rem; /* Legendas maiores */
    color: #ccc;
}

.cotacoes-chart-legend-color {
    width: 16px;
    height: 16px;
    border-radius: 4px;
    margin-right: 8px;
}

.cotacoes-chart-canvas-container {
    flex: 1;
    width: 100%;
    position: relative;
}

/* Controles do gráfico */
.cotacoes-chart-controls {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.cotacoes-chart-control-group {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.cotacoes-chart-control-btn {
    background-color: #444;
    border: none;
    color: #ccc;
    padding: 8px 12px;
    border-radius: 4px;
    font-family: 'Rajdhani', sans-serif;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cotacoes-chart-control-btn:hover {
    background-color: #555;
    color: #fff;
}

.cotacoes-chart-control-btn.active {
    background-color: #FDB813;
    color: #333;
    font-weight: 600;
}

.cotacoes-chart-display-options {
    display: flex;
    gap: 10px;
    align-items: center;
}

.cotacoes-chart-display-label {
    font-family: 'Rajdhani', sans-serif;
    font-size: 0.9rem;
    color: #ccc;
}

.cotacoes-chart-display-select {
    background-color: #444;
    border: none;
    color: #ccc;
    padding: 6px 10px;
    border-radius: 4px;
    font-family: 'Rajdhani', sans-serif;
    font-size: 0.9rem;
}

/* Mensagem de seleção */
.cotacoes-selection-message {
    background-color: rgba(253, 184, 19, 0.1);
    border-left: 4px solid #FDB813;
    padding: 10px 15px;
    margin-bottom: 15px;
    font-family: 'Rajdhani', sans-serif;
    color: #ccc;
    border-radius: 0 4px 4px 0;
}

/* Responsividade */
@media (max-width: 992px) {
    .cotacoes-cards-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
    
    .cotacoes-chart-container {
        height: 400px;
    }
}

@media (max-width: 768px) {
    .cotacoes-cards-grid {
        grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    }
    
    .cotacoes-chart-container {
        height: 350px;
    }
    
    .cotacoes-chart-controls {
        flex-direction: column;
        gap: 15px;
    }
}
