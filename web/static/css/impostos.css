/* Estilos para a página de consulta de impostos */

.card-shell {
    background-color: #333333;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    border: none;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.card-shell:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
}

.card-shell .card-header {
    background-color: rgba(237, 28, 36, 0.1);
    border-bottom: 2px solid #ED1C24;
    padding: 15px 20px;
}

.card-shell .card-header h2 {
    color: #FDB813;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    margin: 0;
    font-size: 1.5rem;
}

.card-shell .card-body {
    padding: 20px;
}

/* Estilos para formulários */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    color: #f8f9fa;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
}

.form-control {
    background-color: #444444;
    border: 1px solid #555555;
    color: #f8f9fa;
    border-radius: 4px;
    padding: 10px 15px;
    height: auto;
    transition: border-color 0.3s, box-shadow 0.3s;
    font-family: 'Share Tech Mono', monospace;
}

.form-control:focus {
    background-color: #444444;
    border-color: #FDB813;
    box-shadow: 0 0 0 0.2rem rgba(253, 184, 19, 0.25);
    color: #f8f9fa;
}

/* Botões */
.btn-shell-red {
    background-color: #ED1C24;
    border-color: #ED1C24;
    color: #ffffff;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 4px;
    transition: all 0.3s;
    font-family: 'Rajdhani', sans-serif;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-shell-red:hover, .btn-shell-red:focus {
    background-color: #d01119;
    border-color: #d01119;
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(237, 28, 36, 0.3);
}

/* Tabelas */
.table-shell {
    color: #f8f9fa;
    background-color: transparent;
    margin-bottom: 0;
}

.table-shell thead th {
    background-color: rgba(237, 28, 36, 0.1);
    color: #FDB813;
    border-bottom: 2px solid #444444;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    padding: 12px 15px;
}

.table-shell tbody td {
    border-top: 1px solid #444444;
    padding: 12px 15px;
}

.table-shell tbody tr:hover {
    background-color: rgba(253, 184, 19, 0.05);
}

.table-shell tfoot td {
    border-top: 2px solid #444444;
    font-weight: bold;
    padding: 12px 15px;
}

/* Alertas */
.alert-shell-danger {
    background-color: rgba(237, 28, 36, 0.1);
    border: 1px solid #ED1C24;
    color: #f8f9fa;
    border-radius: 4px;
    padding: 12px 15px;
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

#resultados {
    animation: fadeIn 0.5s ease-out;
}

/* Responsividade */
@media (max-width: 767px) {
    .form-group {
        margin-bottom: 15px;
    }
    
    .card-shell .card-body {
        padding: 15px;
    }
} 