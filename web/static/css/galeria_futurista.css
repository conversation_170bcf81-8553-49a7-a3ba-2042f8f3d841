/*
 * Galeria de Equipamentos - Shell Tradição
 * Estilos futuristas para a página de galeria de equipamentos
 * Design moderno com tema escuro e efeitos visuais avançados
 */

/* Variáveis de cores e efeitos */
:root {
    --shell-red: #FF0000;
    --shell-yellow: #FFFF00;
    --shell-dark: #121212;
    --shell-darker: #0a0a0a;
    --shell-light-gray: #e0e0e0;
    --neon-glow: 0 0 10px rgba(253, 184, 19, 0.7), 0 0 20px rgba(253, 184, 19, 0.4);
    --red-glow: 0 0 10px rgba(237, 28, 36, 0.7), 0 0 20px rgba(237, 28, 36, 0.4);
    --glass-bg: rgba(18, 18, 18, 0.8);
    --glass-border: rgba(255, 255, 255, 0.1);
}

/* Estilos base */
body {
    background-color: #FF0000 !important;
    background-image: none !important;
    background-attachment: fixed;
    color: #FFFFFF !important;
    font-family: 'Rajdhani', sans-serif;
    overflow-x: hidden;
}

/* Header futurista com efeito de neon */
.galeria-header {
    position: relative;
    padding: 2rem 0;
    margin-bottom: 2rem;
    overflow: hidden;
    border-radius: 15px;
    background: linear-gradient(135deg, rgba(18, 18, 18, 0.9) 0%, rgba(10, 10, 10, 0.95) 100%);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.galeria-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, var(--shell-yellow), transparent);
    animation: scanline 4s linear infinite;
}

.galeria-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, var(--shell-red), transparent);
    animation: scanline 4s linear infinite reverse;
}

@keyframes scanline {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.header-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.header-title {
    font-family: 'Orbitron', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    text-transform: uppercase;
    margin: 0;
    background: linear-gradient(90deg, var(--shell-red), var(--shell-yellow));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: var(--neon-glow);
    letter-spacing: 2px;
    animation: pulse 3s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.header-subtitle {
    font-family: 'Share Tech Mono', monospace;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 0.5rem;
    font-size: 1rem;
    letter-spacing: 1px;
}

.header-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(253, 184, 19, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(253, 184, 19, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.2;
    z-index: 1;
}

.header-glow {
    position: absolute;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: var(--shell-yellow);
    filter: blur(80px);
    opacity: 0.15;
    z-index: 0;
}

.glow-1 {
    top: -50px;
    left: 10%;
    animation: float 8s ease-in-out infinite;
}

.glow-2 {
    bottom: -50px;
    right: 10%;
    background: var(--shell-red);
    animation: float 8s ease-in-out infinite 2s;
}

@keyframes float {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(20px) scale(1.1); }
}

/* Contador de estatísticas animado */
.stats-counter {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 1.5rem;
}

.stat-counter-item {
    text-align: center;
    position: relative;
}

.stat-counter-value {
    font-family: 'Share Tech Mono', monospace;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--shell-yellow);
    text-shadow: var(--neon-glow);
    margin-bottom: 0.25rem;
}

.stat-counter-label {
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: rgba(255, 255, 255, 0.6);
}

.stat-counter-item::after {
    content: '';
    position: absolute;
    top: 0;
    right: -1rem;
    width: 1px;
    height: 100%;
    background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.stat-counter-item:last-child::after {
    display: none;
}

/* Estilos para os filtros futuristas */
.filter-container {
    margin-bottom: 2rem;
}

.filter-panel {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.filter-panel:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4), 0 0 20px rgba(253, 184, 19, 0.1);
    border-color: rgba(253, 184, 19, 0.2);
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 1.5rem;
    background: linear-gradient(90deg, rgba(18, 18, 18, 0.9), rgba(30, 30, 30, 0.8));
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
}

.filter-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--shell-yellow), transparent);
}

.filter-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.filter-title h2 {
    font-family: 'Orbitron', sans-serif;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    color: var(--shell-yellow);
    letter-spacing: 1px;
    text-transform: uppercase;
}

.filter-icon {
    font-size: 1.25rem;
    color: var(--shell-yellow);
    text-shadow: var(--neon-glow);
}

.btn-refresh {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(253, 184, 19, 0.1);
    border: 1px solid rgba(253, 184, 19, 0.3);
    color: var(--shell-yellow);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.btn-refresh:hover {
    background: var(--shell-yellow);
    color: var(--shell-dark);
    transform: rotate(180deg);
    box-shadow: var(--neon-glow);
}

.filter-body {
    padding: 1.5rem;
}

.filter-group {
    margin-bottom: 0.5rem;
}

.filter-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    letter-spacing: 0.5px;
}

.filter-group label i {
    color: var(--shell-yellow);
    margin-right: 0.5rem;
}

.custom-select-wrapper {
    position: relative;
}

.custom-select {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: white;
    font-family: 'Rajdhani', sans-serif;
    font-size: 1rem;
    appearance: none;
    transition: all 0.3s ease;
}

.custom-select:focus {
    outline: none;
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 2px rgba(253, 184, 19, 0.2);
}

.select-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--shell-yellow);
    pointer-events: none;
}

.search-input-wrapper {
    position: relative;
    display: flex;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem;
    padding-right: 3rem;
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: white;
    font-family: 'Rajdhani', sans-serif;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 2px rgba(253, 184, 19, 0.2);
}

.search-button {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 50px;
    background: transparent;
    border: none;
    color: var(--shell-yellow);
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-button:hover {
    color: white;
    text-shadow: var(--neon-glow);
}

.filter-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.view-toggle {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn.active, .view-btn:hover {
    background: var(--shell-yellow);
    color: var(--shell-dark);
    border-color: var(--shell-yellow);
}

.results-counter {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.counter-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--shell-yellow);
    color: var(--shell-dark);
    border-radius: 50%;
    font-weight: 700;
    font-size: 1.1rem;
    box-shadow: var(--neon-glow);
}

.counter-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

/* Estilos para a seção de informações da filial */
.branch-info-container {
    margin-bottom: 2rem;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease;
}

.branch-info-container.visible {
    opacity: 1;
    transform: translateY(0);
}

.branch-info-panel {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.branch-info-header {
    padding: 1.25rem 1.5rem;
    background: linear-gradient(90deg, rgba(237, 28, 36, 0.2), rgba(18, 18, 18, 0.9));
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
}

.branch-info-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: var(--shell-red);
}

.branch-name {
    font-family: 'Orbitron', sans-serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
    margin: 0;
    letter-spacing: 1px;
    text-shadow: 0 0 10px rgba(237, 28, 36, 0.7);
}

.branch-info-body {
    padding: 1.5rem;
}

.branch-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.branch-detail-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.branch-detail-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(237, 28, 36, 0.1);
    border: 1px solid rgba(237, 28, 36, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--shell-red);
    font-size: 1rem;
    flex-shrink: 0;
}

.branch-detail-content {
    flex-grow: 1;
}

.branch-detail-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.5);
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.branch-detail-value {
    font-size: 1rem;
    color: white;
    font-weight: 500;
}

.branch-stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-top: 1.5rem;
}

.branch-stat-card {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 1.25rem;
    text-align: center;
    transition: all 0.3s ease;
}

.branch-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border-color: rgba(253, 184, 19, 0.2);
}

.branch-stat-value {
    font-family: 'Share Tech Mono', monospace;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(90deg, var(--shell-red), var(--shell-yellow));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 10px rgba(253, 184, 19, 0.3);
}

.branch-stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Estilos para a galeria de equipamentos */
.equipment-gallery {
    margin-bottom: 2rem;
}

.equipment-gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.gallery-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.gallery-title h2 {
    font-family: 'Orbitron', sans-serif;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    color: var(--shell-yellow);
    letter-spacing: 1px;
    text-transform: uppercase;
}

.gallery-icon {
    font-size: 1.5rem;
    color: var(--shell-yellow);
    text-shadow: var(--neon-glow);
}

.gallery-actions {
    display: flex;
    gap: 0.75rem;
}

.action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(253, 184, 19, 0.1);
    border: 1px solid rgba(253, 184, 19, 0.3);
    color: var(--shell-yellow);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.action-btn:hover {
    background: var(--shell-yellow);
    color: var(--shell-dark);
    transform: scale(1.1);
    box-shadow: var(--neon-glow);
}

.equipment-gallery-body {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    padding: 1.5rem;
    min-height: 400px;
    position: relative;
}

/* Estilos para o grid de equipamentos */
.equipment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 0.5rem;
}

/* Estilos para o spinner de carregamento */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 10;
}

.loading-spinner {
    position: relative;
    width: 80px;
    height: 80px;
    margin-bottom: 1rem;
}

.spinner-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 4px solid transparent;
    border-top-color: var(--shell-yellow);
    border-radius: 50%;
    animation: spin 1.5s linear infinite;
}

.spinner-ring::before,
.spinner-ring::after {
    content: '';
    position: absolute;
    border-radius: 50%;
}

.spinner-ring::before {
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    border: 4px solid transparent;
    border-top-color: var(--shell-red);
    animation: spin 2s linear infinite;
}

.spinner-dot {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 15px;
    height: 15px;
    background: var(--shell-yellow);
    border-radius: 50%;
    box-shadow: var(--neon-glow);
    animation: pulse 1s ease-in-out infinite alternate;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: white;
    font-size: 1.1rem;
    font-weight: 500;
    text-align: center;
    margin: 0;
    letter-spacing: 1px;
}

/* Estilos para o estado vazio */
.empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    grid-column: 1 / -1;
}

.empty-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 2rem;
    color: rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.empty-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.5rem;
    text-align: center;
}

.empty-message {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.5);
    text-align: center;
    max-width: 400px;
}

/* Estilos para a tabela de dados */
.data-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.data-table thead th {
    background: rgba(0, 0, 0, 0.3);
    color: var(--shell-yellow);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 1px;
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.data-table thead th:first-child {
    border-top-left-radius: 10px;
}

.data-table thead th:last-child {
    border-top-right-radius: 10px;
}

.data-table tbody tr {
    transition: all 0.3s ease;
}

.data-table tbody tr:hover {
    background: rgba(253, 184, 19, 0.1);
}

.data-table tbody td {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.8);
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

.data-table tbody tr:last-child td:first-child {
    border-bottom-left-radius: 10px;
}

.data-table tbody tr:last-child td:last-child {
    border-bottom-right-radius: 10px;
}

/* Estilos para os cards de equipamentos */
.equipment-item {
    transition: all 0.3s ease;
}

.equipment-item:hover {
    transform: translateY(-10px);
}

.equipment-card {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    position: relative;
}

.equipment-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--shell-red), var(--shell-yellow));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.equipment-card:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4), 0 0 20px rgba(253, 184, 19, 0.1);
    border-color: rgba(253, 184, 19, 0.2);
}

.equipment-card:hover::before {
    opacity: 1;
}

.card-img-top {
    height: 180px;
    overflow: hidden;
    position: relative;
    background: rgba(0, 0, 0, 0.3);
}

.card-img-top img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.equipment-card:hover .card-img-top img {
    transform: scale(1.1);
}

.no-image {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: rgba(255, 255, 255, 0.3);
    font-size: 3rem;
}

.card-body {
    padding: 1.25rem;
    flex-grow: 1;
}

.card-title {
    font-family: 'Orbitron', sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-text {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 0.5rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-text i {
    color: var(--shell-yellow);
    width: 20px;
    text-align: center;
}

.card-footer {
    padding: 1rem 1.25rem;
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.badge-shell-green {
    background: rgba(40, 167, 69, 0.2);
    color: #2ecc71;
    border: 1px solid rgba(46, 204, 113, 0.3);
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 600;
}

.badge-shell-yellow {
    background: rgba(253, 184, 19, 0.2);
    color: var(--shell-yellow);
    border: 1px solid rgba(253, 184, 19, 0.3);
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 600;
}

.badge-shell-red {
    background: rgba(237, 28, 36, 0.2);
    color: var(--shell-red);
    border: 1px solid rgba(237, 28, 36, 0.3);
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 600;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-icon:hover {
    background: var(--shell-yellow);
    color: var(--shell-dark);
    border-color: var(--shell-yellow);
    transform: scale(1.1);
    box-shadow: var(--neon-glow);
}

/* Estilos para o modal de detalhes */
.modal-content {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.5);
}

.modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.25rem 1.5rem;
}

.modal-title {
    font-family: 'Orbitron', sans-serif;
    font-size: 1.25rem;
    font-weight: 600;
    color: white;
}

.modal-title i {
    color: var(--shell-yellow);
    text-shadow: var(--neon-glow);
}

.btn-close {
    color: white;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.btn-close:hover {
    opacity: 1;
    transform: rotate(90deg);
}

.modal-body {
    padding: 1.5rem;
}

.equipment-info h3 {
    font-family: 'Orbitron', sans-serif;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.25rem;
    color: white;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 0.75rem;
}

.equipment-info p {
    margin-bottom: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
}

.equipment-info strong {
    color: var(--shell-yellow);
    font-weight: 600;
    margin-right: 0.5rem;
}

.equipment-notes {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 0.5rem;
    color: rgba(255, 255, 255, 0.7);
    max-height: 150px;
    overflow-y: auto;
}

.equipment-media h4 {
    font-family: 'Orbitron', sans-serif;
    font-size: 1.25rem;
    font-weight: 600;
    color: white;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 0.75rem;
}

.media-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.media-item {
    position: relative;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.media-item:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border-color: var(--shell-yellow);
}

.media-item img, .media-item video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.media-type-icon {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--shell-yellow);
    font-size: 0.8rem;
}

.media-description {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 0.5rem;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    font-size: 0.8rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.media-item:hover .media-description {
    transform: translateY(0);
}

.modal-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 1.5rem;
}

.btn-shell {
    background: linear-gradient(135deg, var(--shell-red), var(--shell-yellow));
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.5rem 1.5rem;
    border-radius: 50px;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-shell:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3), var(--neon-glow);
}

/* Animações adicionais */
@keyframes shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Responsividade */
@media (max-width: 992px) {
    .header-title {
        font-size: 2rem;
    }

    .stats-counter {
        flex-wrap: wrap;
        justify-content: space-around;
    }

    .branch-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .header-title {
        font-size: 1.5rem;
    }

    .filter-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .filter-footer {
        flex-direction: column;
        gap: 1rem;
    }

    .branch-detail-item {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .branch-detail-icon {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .galeria-header {
        padding: 1.5rem 0;
    }

    .header-title {
        font-size: 1.25rem;
    }

    .stat-counter-item {
        width: 100%;
        margin-bottom: 1rem;
    }

    .stat-counter-item::after {
        display: none;
    }

    .branch-stats-grid {
        grid-template-columns: 1fr;
    }
}
