/* Estilos para o layout do calendário com efeito de flip 3D */

/* Centralização global de conteúdo */
.calendar-dashboard-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 15px;
    position: relative;
}

/* Botão para retornar ao dashboard */
.back-to-dashboard {
    position: fixed;
    top: 15px;
    left: 15px;
    z-index: 1000;
    background-color: var(--shell-red);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.back-to-dashboard:hover {
    background-color: #c51017;
    transform: scale(1.1);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
}

.back-to-dashboard i {
    font-size: 1.5rem;
}

.flip-card-container {
    width: 100%;
    display: flex;
    justify-content: center;
}

/* Estilos para integração com agenda do Google */

/* Estilos para notificações da agenda */
.agenda-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px;
    border-radius: 8px;
    background-color: rgba(50, 25, 7, 0.9);
    border-left: 4px solid var(--shell-yellow);
    color: #fff;
    z-index: 9999;
    max-width: 350px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
}

.agenda-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.agenda-notification.success {
    border-left-color: #28a745;
}

.agenda-notification.warning {
    border-left-color: #ffc107;
}

.agenda-notification.error {
    border-left-color: #dc3545;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification-content i {
    font-size: 1.2rem;
    color: var(--shell-yellow);
}

.agenda-notification.success i {
    color: #28a745;
}

.agenda-notification.warning i {
    color: #ffc107;
}

.agenda-notification.error i {
    color: #dc3545;
}
.agenda-integration-container {
    background: transparent;
}

.agenda-title {
    color: var(--shell-yellow);
    margin-bottom: 15px;
    font-weight: bold;
    font-size: 1.3rem;
    text-align: center;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--shell-yellow);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.agenda-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.agenda-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.agenda-user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #fff;
}

.agenda-user-info i {
    font-size: 1.5rem;
    color: var(--shell-yellow);
}

.agenda-controls {
    display: flex;
    gap: 8px;
}

.agenda-btn {
    font-size: 0.8rem;
    padding: 5px 10px;
}

.agenda-events {
    flex: 1;
    overflow-y: auto;
    padding: 10px 0;
}

.agenda-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 15px;
    height: 250px;
    text-align: center;
    color: #ccc;
}

.agenda-placeholder i {
    color: var(--shell-yellow);
    opacity: 0.7;
}

.agenda-placeholder h5 {
    color: #fff;
    margin: 0;
}

.agenda-placeholder p {
    color: #ccc;
    max-width: 80%;
    margin: 0 auto;
}

.agenda-events-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.agenda-event-item {
    background-color: rgba(0, 0, 0, 0.2);
    border-left: 3px solid var(--shell-yellow);
    border-radius: 6px;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.agenda-event-item:hover {
    background-color: rgba(0, 0, 0, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.agenda-event-title {
    font-weight: bold;
    color: #fff;
    margin-bottom: 5px;
}

.agenda-event-details {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    font-size: 0.85rem;
    color: #ccc;
}

.agenda-event-time, 
.agenda-event-location {
    display: flex;
    align-items: center;
    gap: 5px;
}

.agenda-event-actions {
    margin-top: 8px;
    display: flex;
    gap: 8px;
}

.agenda-event-btn {
    font-size: 0.75rem;
    padding: 3px 8px;
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #ccc;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.agenda-event-btn:hover {
    background-color: rgba(244, 179, 29, 0.2);
    color: var(--shell-yellow);
    border-color: var(--shell-yellow);
}

/* Modal para adicionar eventos */
.agenda-modal-content {
    background-color: var(--shell-brown);
    border: 2px solid var(--shell-yellow);
    color: #fff;
}

.agenda-form-group {
    margin-bottom: 15px;
}

.agenda-form-group label {
    display: block;
    margin-bottom: 5px;
    color: var(--shell-yellow);
}

.agenda-form-control {
    width: 100%;
    padding: 8px 12px;
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    color: #fff;
}

.agenda-form-control:focus {
    border-color: var(--shell-yellow);
    outline: none;
    box-shadow: 0 0 5px rgba(244, 179, 29, 0.5);
}

/* Responsividade para dispositivos móveis */
@media (max-width: 767.98px) {
    .agenda-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .agenda-controls {
        width: 100%;
        justify-content: space-between;
    }

    .agenda-btn {
        flex: 1;
        text-align: center;
    }
}

/* Estilo para o botão de legendas dentro do flip-instruction */
/* Estilo para a instrução e botão legendas */
.instruction-legend-container {
    position: relative;
    width: 100%;
    margin-top: 5px !important;
    margin-bottom: 10px;
    z-index: 10;
}

.instruction-text {
    color: var(--shell-yellow) !important;
    font-size: 0.9rem;
}

.legend-btn {
    background-color: #dc3545 !important;
    border: none;
    border-radius: 4px;
    color: white !important; 
    padding: 6px 12px;
    font-size: 0.9rem;
}

.legend-btn:hover {
    background-color: #c82333 !important;
    color: white !important;
}

/* Estilos para indicadores de eventos no calendário */
.event-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    position: absolute;
    bottom: 4px;
    left: 50%;
    transform: translateX(-50%);
}

.event-indicator.waiting {
    background-color: #17a2b8; /* info */
}

.event-indicator.approved {
    background-color: #28a745; /* success */
}

.event-indicator.approved-scheduled {
    background-color: #28a745; /* success */
    opacity: 0.7;
}

.event-indicator.urgent {
    background-color: #dc3545; /* danger */
}

.event-indicator.scheduled {
    background-color: #007bff; /* primary */
}

.event-indicator.completed {
    background-color: #6c757d; /* secondary */
}

.event-indicators {
    display: flex;
    gap: 2px;
    position: absolute;
    bottom: 4px;
    left: 50%;
    transform: translateX(-50%);
}

.event-indicators .event-indicator {
    position: static;
    transform: none;
}

/* Estilos para botão de legendas */
.legend-button-container {
    margin-top: 15px;
    text-align: center;
}

.legend-btn {
    background-color: #dc3545 !important;
    border: none;
    border-radius: 4px;
    color: white !important; 
    padding: 6px 12px;
    font-size: 0.9rem;
}

.legend-btn:hover {
    background-color: #c82333 !important;
    color: white !important;
}

/* Estilos para modal de legendas */
.legend-modal-content {
    background-color: var(--shell-brown);
    border: 2px solid var(--shell-yellow);
    color: #fff;
}

.legend-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.legend-badge {
    width: 15px;
    height: 15px;
    border-radius: 3px;
}

.legend-text {
    flex: 1;
}

/* Estilos para serviços agendados reposicionados */
.scheduled-services-container {
    max-width: 1200px;
    margin: 30px auto;
    padding: 0;
    background: linear-gradient(135deg, rgba(44, 36, 22, 0.7), rgba(32, 28, 20, 0.8));
    border: 2px solid rgba(244, 179, 29, 0.3);
    border-radius: 12px;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.glass-container {
    padding: 15px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 5px 0 12px;
    border-bottom: 1px solid rgba(244, 179, 29, 0.2);
}

.scheduled-services-title {
    color: var(--shell-yellow);
    margin: 0;
    font-weight: 600;
    font-size: 1.2rem;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.header-controls {
    display: flex;
    align-items: center;
}

.toggle-btn {
    background: rgba(60, 35, 15, 0.7);
    border: 1px solid rgba(244, 179, 29, 0.4);
    color: var(--shell-yellow);
    width: clamp(32px, 8vw, 40px);
    height: clamp(32px, 8vw, 40px);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    padding: 0;
    min-width: 32px;
    min-height: 32px;
    touch-action: manipulation;
}

.toggle-btn:hover {
    background: rgba(244, 179, 29, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.filter-buttons-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
    margin-bottom: 15px;
}

.filter-btn {
    background: rgba(40, 30, 20, 0.7);
    border: 1px solid rgba(244, 179, 29, 0.2);
    color: #eee;
    padding: 6px 14px;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.85rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.filter-btn:hover {
    background: rgba(244, 179, 29, 0.15);
    border-color: var(--shell-yellow);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.filter-btn.active {
    background: var(--shell-yellow);
    color: #000;
    border-color: var(--shell-yellow);
    box-shadow: 0 0 10px rgba(244, 179, 29, 0.5);
    font-weight: 600;
}

.service-orders-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    max-height: 500px;
    transition: all 0.5s ease-in-out;
    overflow-y: auto;
    padding: 5px 2px;
    margin-top: 10px;
    scrollbar-width: thin;
    scrollbar-color: var(--shell-yellow) rgba(0, 0, 0, 0.2);
}

.service-orders-container::-webkit-scrollbar {
    width: 6px;
}

.service-orders-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
}

.service-orders-container::-webkit-scrollbar-thumb {
    background: var(--shell-yellow);
    border-radius: 10px;
}

.service-orders-container.collapsed {
    max-height: 0 !important;
    overflow: hidden !important;
    margin-top: 0 !important;
    padding: 0 !important;
    display: none !important;
}

.service-order-item {
    background-color: rgba(60, 35, 15, 0.5);
    border-radius: 8px;
    padding: 10px;
    position: relative;
    border: 1px solid rgba(244, 179, 29, 0.2);
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    height: 140px;
    margin-bottom: 0;
}

.service-order-item:last-child {
    margin-bottom: 0;
}

.service-order-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3);
    border-color: var(--shell-yellow);
}

.order-status-indicator {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 6px;
    border-radius: 3px 0 0 3px;
}

.order-status-indicator.waiting-approval {
    background-color: #17a2b8;
}

.order-status-indicator.approved {
    background-color: #28a745;
}

.order-status-indicator.approved-scheduled {
    background-color: #28a745;
    opacity: 0.7;
}

.order-status-indicator.scheduled {
    background-color: #007bff;
}

.order-status-indicator.urgent {
    background-color: #dc3545;
}

.order-status-indicator.completed {
    background-color: #6c757d;
}

.order-content {
    padding-left: 12px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.order-title {
    margin-bottom: 10px;
    font-size: 1rem;
    color: var(--shell-yellow);
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

.order-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
}

.order-date, .order-location {
    font-size: 0.85rem;
    color: #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    width: 100%;
    text-align: center;
    padding-left: 0;
}

.order-date i, .order-location i {
    color: var(--shell-yellow);
    width: 16px;
    text-align: center;
    margin-right: 3px;
}

.order-priority {
    margin-top: 8px;
    display: flex;
    justify-content: center;
    width: 100%;
}

.order-priority .badge {
    padding: 6px 12px;
    font-size: 0.8rem;
    font-weight: 500;
    border-radius: 50px;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    min-width: 120px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Responsividade para telas pequenas */
@media (max-width: 767.98px) {
    .calendar-dashboard-container {
        padding: 10px 5px;
    }

    .calendar-dashboard-layout {
        flex-direction: column;
    }

    .calendar-container {
        width: 100%;
        margin-right: 0;
        margin-bottom: 15px;
    }

    .side-orders-panel {
        width: 100%;
        margin-left: 0;
        max-height: 300px;
    }

    .calendar-day {
        height: clamp(35px, 8vw, 45px);
        font-size: clamp(0.75rem, 2vw, 0.9rem);
    }

    .calendar-weekdays div {
        font-size: clamp(0.7rem, 1.8vw, 0.85rem);
        padding: clamp(4px, 1vw, 6px);
    }

    .order-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .filter-buttons-container {
        flex-direction: column;
        align-items: center;
        margin-bottom: 15px;
    }

    .filter-btn {
        width: 95%;
        text-align: center;
        margin-bottom: 5px;
        padding: 8px 5px;
        font-size: 0.9rem;
    }

    .service-orders-container {
        grid-template-columns: 1fr;
    }

    .scheduled-services-container .glass-container {
        padding: 15px 10px;
    }

    .service-order-item {
        padding: 12px 10px;
    }

    .order-title {
        font-size: 0.95rem;
    }

    .order-info {
        gap: 6px;
    }

    .order-date, .order-location {
        font-size: 0.8rem;
    }

    .order-priority .badge {
        padding: 3px 6px;
        font-size: 0.7rem;
    }
}

/* Responsividade para telas muito pequenas */
@media (max-width: 480px) {
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .header-controls {
        width: 100%;
        text-align: center;
    }

    .scheduled-services-title {
        width: 100%;
        text-align: center;
        font-size: 1.3rem;
    }

    .service-order-item {
        padding: 10px 8px;
    }

    .order-info {
        font-size: 0.75rem;
    }

    .toggleServicesBtn {
        width: 100%;
    }

    .order-content {
        padding-left: 8px;
    }
}

:root {
    --shell-red: #ED1C24;
    --shell-yellow: #f4b31d;
    --shell-dark: #333333;
    --shell-brown: rgba(50, 25, 7, 0.9);
    --shell-light: #f9f9f9;
}

/* Estilo geral da página */
body {
    font-family: 'Nunito', sans-serif;
    background-color: #1a1a1a;
    background-image: linear-gradient(45deg, #111 25%, transparent 25%, transparent 75%, #111 75%, #111), 
                      linear-gradient(45deg, #111 25%, transparent 25%, transparent 75%, #111 75%, #111);
    background-size: 60px 60px;
    background-position: 0 0, 30px 30px;
    color: #fff;
    line-height: 1.6;
}

/* Layout principal em grid */
.calendar-dashboard-container {
    display: grid;
    grid-template-columns: 1fr; /* Uma coluna por padrão */
    grid-template-rows: auto auto;
    gap: 20px;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 10px;
    box-sizing: border-box;
}

/* Layout em telas maiores: flip-card e eventos lado a lado */
@media (min-width: 992px) {
    .calendar-dashboard-container {
        grid-template-columns: 1fr; /* calendário ocupa 100% para layout com painel lateral */
        grid-template-rows: auto;
    }
}

/* Layout para a visualização de calendário com painel lateral */
.calendar-dashboard-layout {
    display: flex;
    flex-direction: row;
    align-items: stretch;
    width: 100%;
    flex-wrap: wrap;
    overflow-x: hidden;
    max-width: 100%;
}

.calendar-container {
    flex: 1;
    margin-right: 15px;
    position: relative;
    min-width: 300px;
    max-width: 100%;
}

.side-orders-panel {
    width: 270px;
    margin-left: auto;
    max-width: 100%;
}

/* Responsividade para o layout do calendário */
@media (max-width: 992px) {
    .calendar-dashboard-layout {
        flex-direction: column;
        overflow-x: hidden;
    }

    .calendar-container {
        margin-right: 0;
        margin-bottom: 20px;
        width: 100%;
        max-width: 100%;
        overflow-x: hidden;
    }

    .side-orders-panel {
        width: 100%;
        margin-left: 0;
    }
}

/* Estilos para o painel lateral de ordens */
.side-orders-panel {
    background-color: #2c2416; /* Marrom escuro sólido para consistência */
    border-radius: 12px;
    border: 8px solid var(--shell-yellow); /* Borda mais espessa como nas imagens */
    padding: 15px;
    margin-top: 0;
    height: 100%;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--shell-yellow) rgba(0, 0, 0, 0.2);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.side-orders-panel::-webkit-scrollbar {
    width: 5px;
}

.side-orders-panel::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
}

.side-orders-panel::-webkit-scrollbar-thumb {
    background: var(--shell-yellow);
    border-radius: 10px;
}

.side-orders-header {
    margin-bottom: 15px;
    text-align: center;
}

.side-orders-header h4 {
    color: var(--shell-yellow);
    font-size: 1.1rem;
    margin: 0 0 10px 0;
    text-align: right;
    padding-right: 5px;
}

.side-orders-section {
    margin-bottom: 20px;
}

.side-orders-section h5 {
    color: #fff;
    font-size: 1rem;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.side-orders-section h5 .fa-exclamation-circle {
    color: #dc3545;
    margin-right: 8px;
    font-size: 1.1em;
}

.side-orders-section h5 .fa-tools {
    color: #007bff;
    margin-right: 8px;
    font-size: 1.1em;
}

.side-orders-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.side-order-item {
    background-color: #2c2416; /* Cor de fundo marrom escuro consistente */
    border-radius: 8px;
    padding: 10px;
    position: relative;
    border: 2px solid var(--shell-yellow); /* Borda amarela mais visível */
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
    display: flex;
    margin-bottom: 10px; /* Espaçamento entre os itens */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Sombra para destacar */
}

.side-order-item:hover {
    background-color: rgba(80, 50, 20, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border-color: var(--shell-yellow);
}

.side-order-indicator {
    width: 8px; /* Indicador mais largo para melhor visibilidade */
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    border-radius: 8px 0 0 8px;
}

.side-order-indicator.urgent {
    background-color: #dc3545;
}

.side-order-indicator.scheduled {
    background-color: #007bff;
}

.side-order-content {
    flex: 1;
    padding-left: 10px;
}

.side-order-content h6 {
    color: var(--shell-yellow);
    margin: 0 0 8px 0;
    font-size: 0.9rem;
    font-weight: 600;
}

.side-order-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 0.8rem;
    color: #fff;
}

.side-order-details span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.side-order-details i {
    width: 14px;
    text-align: center;
    color: var(--shell-yellow);
}

/* Estilos para o flip-card */
.flip-card-container {
    perspective: 2000px;
    width: 100%;
    min-height: 260px;
    max-height: 85vh;
    perspective-origin: center 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow-x: hidden;
    max-width: 100%;
}

.flip-card {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 260px;
    max-width: 100%;
    margin: 0 auto;
    transition: transform 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform-style: preserve-3d;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    overflow-x: hidden;
}

.flip-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    box-shadow: 0 30px 50px rgba(0, 0, 0, 0.5);
    transition: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    opacity: 0;
    z-index: -1;
}

.flip-card.flipped::before {
    opacity: 1;
    transform: translateY(20px) scale(0.95);
}

.flip-card.flipped .flip-card-inner {
    transform: rotateX(180deg);
}

/* Garante que o front não seja rotacionado por padrão */


.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform-style: preserve-3d;
}

.flip-card-front, .flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-radius: 15px;
    overflow: hidden;
}

.flip-card-front {
    background-color: transparent;
    display: flex;
    flex-direction: column;
    justify-content: center;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.flip-card-back {
    background: linear-gradient(135deg, rgba(50, 30, 15, 0.8), rgba(20, 20, 20, 0.85));
    /* Configurando a rotação do verso do cartão */
    transform: rotateX(180deg);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    overflow-y: auto;
    max-height: 85vh;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.8);
    border: 3px solid var(--shell-yellow);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    position: relative;
}

.flip-card-back::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 30%, rgba(244, 179, 29, 0.1), transparent 70%);
    pointer-events: none;
    z-index: 0;
}

.flip-card-back > * {
    position: relative;
    z-index: 1;
}

/* Animação de indicação para clicar no calendário */
.flip-instruction {
    text-align: center;
    margin-top: 5px;
    padding: 5px;
    color: var(--shell-yellow);
    animation: pulse 2s infinite;
    font-size: 0.85rem;
}

.flip-instruction i {
    font-size: 14px;
    margin-bottom: 3px;
    color: var(--shell-yellow);
}

.fa-bounce {
    animation: bounce 1s ease infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {transform: translateY(0);}
    40% {transform: translateY(-8px);}
    60% {transform: translateY(-4px);}
}

@keyframes pulse {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

/* Painel de vidro para detalhes da ordem */
.glass-panel {
    background: linear-gradient(135deg, rgba(35, 35, 35, 0.85), rgba(25, 25, 25, 0.90));
    border-radius: 15px;
    padding: 25px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 3px solid var(--shell-yellow);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    height: 100%;
    overflow-y: auto;
    color: white;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(244, 179, 29, 0.3);
}

.order-detail-panel {
    padding: 10px;
}

/* Estilo para bombas de combustível */
.pump-border {
    border: 4px solid var(--shell-yellow);
    border-radius: 15px;
    padding: 15px;
    background: rgba(30, 30, 30, 0.8);
    position: relative;
    height: 100%;
    transition: all 0.4s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    color: white;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.pump-border::before {
    content: '';
    position: absolute;
    top: -10px;
    right: 20px;
    width: 30px;
    height: 16px;
    background: var(--shell-red);
    border-radius: 8px 8px 0 0;
    z-index: -1;
}

.pump-border::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    pointer-events: none;
    border-radius: 12px;
}

/* Cabeçalho do calendário */
.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    background: linear-gradient(90deg, #222, #111);
    padding: 10px 15px;
    border-radius: 8px;
    border: 1px solid var(--shell-yellow);
    box-shadow: 0 3px 6px rgba(0,0,0,0.2);
}

.calendar-title {
    color: var(--shell-yellow);
    margin: 0;
    font-weight: bold;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
}

/* Botões do calendário */
.shell-btn {
    background: var(--shell-red);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    font-size: 0.9rem;
}

.shell-btn:hover {
    background: #c51017;
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 10px rgba(0,0,0,0.3);
}

.shell-btn:active {
    transform: translateY(1px);
    box-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.shell-btn-lg {
    padding: 12px 24px;
    font-size: 1.1rem;
    border-radius: 8px;
}

.shell-btn-approve {
    background: #28a745;
}

.shell-btn-approve:hover {
    background: #218838;
}

.shell-btn-reject {
    background: #dc3545;
}

.shell-btn-reject:hover {
    background: #c82333;
}

/* Dias da semana */
.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 4px;
    margin-bottom: 6px;
}

.calendar-weekdays div {
    text-align: center;
    font-weight: bold;
    color: var(--shell-yellow);
    padding: 6px 3px;
    background: linear-gradient(180deg, #333, #222);
    border-radius: 5px;
    border: 1px solid #444;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    font-size: 0.9em;
}

/* Dias do calendário */
.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 4px;
}

.calendar-day {
    background: rgba(50, 50, 50, 0.5);
    border-radius: 8px;
    padding: 6px 3px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 1px solid rgba(100, 100, 100, 0.3);
    color: #fff;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
    font-size: 0.9em;
}

.calendar-day::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    border-radius: 8px;
    pointer-events: none;
}

.calendar-day:hover {
    background: rgba(237, 28, 36, 0.7);
    color: white;
    transform: scale(1.08);
    z-index: 5;
    font-weight: bold;
    box-shadow: 0 5px 15px rgba(237, 28, 36, 0.3), inset 0 0 10px rgba(255, 255, 255, 0.1);
}

.calendar-day.today {
    background: rgba(244, 179, 29, 0.7);
    color: black;
    font-weight: bold;
    box-shadow: 0 0 15px rgba(244, 179, 29, 0.5);
    transform: scale(1.05);
    z-index: 2;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

.calendar-day.has-event {
    border: 2px solid var(--shell-red);
    position: relative;
    z-index: 1;
    background: rgba(60, 60, 60, 0.7);
}

.calendar-day.has-event::after {
    content: '';
    position: absolute;
    top: 5px;
    right: 5px;
    width: 8px;
    height: 8px;
    background: var(--shell-red);
    border-radius: 50%;
    animation: pulse 1.5s infinite;
    box-shadow: 0 0 5px rgba(237, 28, 36, 0.7);
}

.calendar-day.selected {
    transform: scale(1.15) !important;
    box-shadow: 0 0 25px rgba(244, 179, 29, 0.7) !important;
    z-index: 10;
    font-weight: bold;
    background: linear-gradient(145deg, rgba(244, 179, 29, 0.8), rgba(237, 28, 36, 0.8));
    color: white;
    border: 2px solid white;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.calendar-day.prev-month {
    background: rgba(30, 30, 30, 0.3);
    color: rgba(255, 255, 255, 0.3);
    cursor: default;
    box-shadow: none;
    border-color: rgba(80, 80, 80, 0.2);
}

/* Lista de eventos */
.events-wrapper {
    overflow-y: auto;
    width: 100%;
    height: 100%;
    transition: all 0.5s ease-in-out;
}

.events-title {
    color: var(--shell-yellow);
    margin-bottom: 15px;
    font-weight: bold;
    font-size: 1.3rem;
    text-align: center;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--shell-yellow);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.events-list-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 5px;
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--shell-red) rgba(255,255,255,0.3);
}

.events-list-container::-webkit-scrollbar {
    width: 6px;
}

.events-list-container::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.3);
    border-radius: 10px;
}

.events-list-container::-webkit-scrollbar-thumb {
    background: var(--shell-red);
    border-radius: 10px;
}

/* Item de evento */
.event-item {
    padding: 8px 12px;
    border-radius: 8px;
    margin-bottom: 5px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border-left: 4px solid var(--shell-red);
    background-color: rgba(50, 50, 50, 0.7);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    cursor: grab;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    color: white;
}

.event-item .mb-1 {
    font-size: 0.95rem;
    margin-bottom: 2px !important;
}

.event-item small {
    font-size: 0.75rem;
}

.event-item p.mt-2 {
    margin-top: 5px !important;
    font-size: 0.85rem;
}

.event-item:hover {
    background: rgba(60, 60, 60, 0.9);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2), 0 0 15px rgba(244, 179, 29, 0.2);
    border-left-width: 8px;
}

.event-item:active {
    cursor: grabbing;
    transform: translateY(0) scale(0.98);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* Caixas de atendimento */
.service-box {
    background: rgba(25, 25, 25, 0.85);
    padding: 10px;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
    color: white;
    border: 2px solid var(--shell-yellow);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    height: 100%;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    display: flex;
    flex-direction: column;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    font-size: 0.9rem;
}

.service-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(244, 179, 29, 0.2);
    border-color: var(--shell-yellow);
}

.service-box::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: linear-gradient(135deg, rgba(244, 179, 29, 0.15), transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-box:hover::after {
    opacity: 1;
}

.service-box::before {
    content: '';
    position: absolute;
    width: 150px;
    height: 20px;
    top: -10px;
    left: -50px;
    background: rgba(244, 179, 29, 0.2);
    filter: blur(15px);
    opacity: 0;
    transform: rotate(45deg);
    transition: all 0.6s ease;
}

.service-box:hover::before {
    left: 120%;
    opacity: 0.8;
}

/* Modal para visualização detalhada do card */
.service-detail-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease;
}

.service-detail-modal.active {
    visibility: visible;
    opacity: 1;
}

.service-detail-content {
    background: rgba(60, 35, 15, 0.95);
    border: 4px solid var(--shell-yellow);
    border-radius: 15px;
    padding: 30px;
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.7);
    transform: scale(0.9);
    transition: transform 0.3s ease, width 0.3s ease, padding 0.3s ease;
    color: white;
}

/* Media queries para dispositivos móveis */
@media (max-width: 576px) {
    .service-detail-content {
        width: 95%;
        padding: 20px 15px;
        transform: scale(0.95);
    }

    /* Ajusta o espaçamento do cabeçalho do modal em dispositivos móveis */
    .service-detail-content h2 {
        font-size: 1.5rem;
        margin-bottom: 15px;
    }

    /* Remove padding extra em dispositivos pequenos */
    .row {
        margin-right: -10px;
        margin-left: -10px;
    }

    .col-md-6, .col-12 {
        padding-right: 10px;
        padding-left: 10px;
    }

    /* Ajustes específicos para o layout responsivo em telas muito pequenas */
    .calendar-day {
        padding: 4px 2px;
        font-size: 0.8em;
    }

    .calendar-weekdays div {
        padding: 4px 2px;
        font-size: 0.8em;
    }

    .shell-btn {
        padding: 4px 8px;
        font-size: 0.85rem;
    }

    .calendar-header {
        padding: 8px 10px;
    }

    .calendar-title {
        font-size: 0.95rem;
    }

    /* Garante que o conteúdo não ultrapasse a largura da tela */
    .flip-card-container, 
    .flip-card, 
    .calendar-dashboard-layout,
    .pump-border,
    .calendar-container,
    .calendar-days,
    .side-orders-panel {
        max-width: 100%;
        overflow-x: hidden;
    }
}

.service-detail-modal.active .service-detail-content {
    transform: scale(1);
}

.service-detail-close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 24px;
    color: var(--shell-red);
    background: none;
    border: none;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.service-detail-close:hover {
    transform: scale(1.2);
    color: var(--shell-yellow);
}

/* Estilos para os detalhes do card no modal */
.detailed-service-info {
    padding: 20px;
    border-radius: 10px;
    background: rgba(25, 25, 25, 0.85);
    backdrop-filter: blur(8px);
    border: 2px solid var(--shell-yellow);
    height: 100%;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin: 10px 0;
    padding: 8px;
    border-bottom: 1px solid rgba(244, 179, 29, 0.3);
}

.info-label {
    font-weight: bold;
    color: var(--shell-yellow);
}

.info-value {
    text-align: right;
}

/* Estilos para histórico de manutenção */
.maintenance-history, .part-specs, .cost-comparison, .technician-info {
    padding: 20px;
    border-radius: 10px;
    background: rgba(25, 25, 25, 0.85);
    backdrop-filter: blur(8px);
    border: 2px solid var(--shell-yellow);
    height: auto;
    min-height: 100px;
    max-height: 80vh;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    overflow-y: auto;
}

.history-timeline {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 15px;
}

.history-item {
    display: flex;
    gap: 10px;
    background: rgba(30, 30, 30, 0.7);
    padding: 10px;
    border-radius: 8px;
    border-left: 3px solid var(--shell-yellow);
}

.history-date {
    background: var(--shell-yellow);
    color: #000;
    font-weight: bold;
    padding: 5px 10px;
    border-radius: 5px;
    min-width: 100px;
    text-align: center;
}

.history-content {
    flex: 1;
}

.history-content h5 {
    margin-bottom: 5px;
    color: var(--shell-yellow);
}

/* Estilos para especificações técnicas */
.specs-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 15px;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px dashed rgba(244, 179, 29, 0.3);
}

.spec-label {
    font-weight: bold;
    color: var(--shell-yellow);
}

/* Estilos para detalhamento de custos */
.detailed-costs {
    text-align: left;
    margin-top: 20px;
}

.detailed-costs h4 {
    color: var(--shell-yellow);
    margin-bottom: 10px;
}

.cost-item-detailed {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.cost-total-detailed {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    font-size: 1.2rem;
    padding: 10px 0;
    border-top: 2px solid var(--shell-yellow);
    color: var(--shell-yellow);
}

/* Estilos para o gráfico de barras de custos */
.cost-chart {
    display: flex;
    justify-content: space-around;
    align-items: flex-end;
    height: 220px;
    margin-top: 20px;
    margin-bottom: 20px;
}

.chart-bar {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 60px;
}

.chart-value {
    width: 100%;
    background: var(--shell-yellow);
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-bottom: 5px;
    border-radius: 5px 5px 0 0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.chart-value span {
    transform: rotate(-90deg);
    white-space: nowrap;
    font-size: 0.8rem;
    color: #000;
    font-weight: bold;
    margin-bottom: 30px;
}

.chart-label {
    margin-top: 5px;
    text-align: center;
}

/* Estilos para cronograma detalhado */
/* Estilo para o cronograma detalhado */
.cronograma-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 15px;
    background: rgba(40, 30, 20, 0.4);
    border-radius: 10px;
    border: 1px solid rgba(244, 179, 29, 0.3);
}

.cronograma-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 10px;
    background: rgba(30, 25, 20, 0.6);
    border-radius: 8px;
    border-left: 3px solid var(--shell-yellow);
    transition: transform 0.2s ease;
}

.cronograma-item:hover {
    transform: translateX(5px);
    background: rgba(40, 35, 25, 0.7);
}

.cronograma-item i {
    color: var(--shell-yellow);
    font-size: 1.5rem;
    flex-shrink: 0;
    margin-top: 3px;
}

.cronograma-content {
    flex: 1;
}

.cronograma-content h5 {
    margin: 0 0 5px 0;
    color: var(--shell-yellow);
    font-size: 1rem;
}

.cronograma-content p {
    margin: 0 0 5px 0;
    font-size: 0.9rem;
}

@media (max-width: 576px) {
    .cronograma-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .cronograma-item i {
        margin-bottom: 8px;
    }
}
.maintenance-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 15px;
    margin: 15px 0;
}

.maintenance-card {
    background: rgba(40, 30, 20, 0.8);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    border: 1px solid rgba(244, 179, 29, 0.3);
}

.maintenance-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: var(--shell-yellow);
}

.maintenance-card .card-header {
    background: rgba(50, 30, 15, 0.7);
    padding: 12px 15px 12px 15px;
    border-bottom: 1px solid rgba(244, 179, 29, 0.2);
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.maintenance-card .card-header h5 {
    margin: 0;
    color: var(--shell-yellow);
    font-size: 1rem;
    font-weight: 600;
    max-width: calc(100% - 100px);
}

.maintenance-card .date-badge {
    background: rgba(30, 20, 10, 0.6);
    color: #fff;
    font-size: 0.8rem;
    padding: 2px 10px;
    border-radius: 12px;
    border: 1px solid rgba(244, 179, 29, 0.3);
    display: inline-block;
    margin-left: auto;
}

.maintenance-card .card-body {
    padding: 15px;
    color: #eee;
}

.maintenance-card .card-body p {
    margin-bottom: 15px;
    font-size: 0.9rem;
    line-height: 1.5;
}

.maintenance-card .card-info {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.maintenance-card .badge {
    padding: 5px 10px;
    font-weight: normal;
    font-size: 0.8rem;
}

/* Mantendo timeline-item-big apenas para compatibilidade com outras seções */
.timeline-item-big {
    display: flex;
    padding: 10px;
    background: rgba(30, 30, 30, 0.7);
    border-radius: 8px;
    position: relative;
    z-index: 1;
    border-left: 3px solid var(--shell-yellow);
}

.timeline-item-big i {
    font-size: 1.5rem;
    color: var(--shell-yellow);
    margin-right: 15px;
    flex-shrink: 0;
}

.timeline-content {
    flex: 1;
    min-width: 0; /* Importante para evitar transbordamento */
    word-wrap: break-word; /* Para quebrar palavras longas */
}

/* Adiciona responsividade para itens da timeline em telas menores */
@media (max-width: 576px) {
    .timeline-item-big {
        flex-direction: column;
    }

    .timeline-item-big i {
        margin-bottom: 10px;
        margin-right: 0;
    }

    .timeline-content .badge {
        display: inline-block;
        margin-top: 5px;
        margin-right: 5px;
    }

    /* Ajustes específicos para dispositivos realmente pequenos */
    .service-detail-content {
        padding: 15px 10px;
        width: 98%;
    }

    .maintenance-cards {
        grid-template-columns: 1fr;
    }

    .maintenance-card .card-header {
        padding: 10px;
        flex-direction: column;
        align-items: flex-start;
    }

    .maintenance-card .card-header h5 {
        max-width: 100%;
        margin-bottom: 5px;
    }

    .maintenance-card .date-badge {
        margin: 0;
        position: static;
        display: inline-block;
        width: auto;
    }

    .maintenance-history, 
    .part-specs, 
    .cost-comparison, 
    .technician-info {
        padding: 15px 10px;
    }

    /* Ajustes para os itens de timeline */
    .timeline-content p,
    .timeline-content small {
        font-size: 0.9rem;
    }

    /* Reduz o tamanho dos botões e ícones em dispositivos pequenos */
    .service-detail-close {
        top: 10px;
        right: 10px;
        font-size: 20px;
    }
}

.timeline-content h5 {
    margin-bottom: 5px;
    color: var(--shell-yellow);
}

.timeline-status {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8rem;
    margin-top: 5px;
}

.timeline-status.completed {
    background-color: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.timeline-status.current {
    background-color: rgba(255, 193, 7, 0.2);
    color: #ffc107;
}

.timeline-status.pending {
    background-color: rgba(108, 117, 125, 0.2);
    color: #6c757d;
}

/* Estilos para cartão do técnico */
.technician-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: rgba(25, 25, 25, 0.85);
    border-radius: 10px;
    margin-top: 15px;
    border: 2px solid var(--shell-yellow);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.technician-photo {
    margin-bottom: 15px;
}

.technician-details {
    text-align: center;
    width: 100%;
}

.technician-details h5 {
    color: var(--shell-yellow);
    margin-bottom: 10px;
}

.technician-details p {
    margin-bottom: 8px;
}

.technician-details i {
    color: var(--shell-yellow);
    margin-right: 5px;
}

.service-box-icon {
    font-size: 1.5rem;
    margin-bottom: 8px;
    color: var(--shell-yellow);
}

.service-box h6 {
    color: var(--shell-yellow);
    margin-bottom: 5px;
    font-weight: bold;
}

.service-box p {
    margin: 0;
    font-size: 0.9rem;
}

/* Detalhes adicionais dentro das caixas de serviço */
.service-details {
    margin-top: 8px;
    text-align: left;
    padding-top: 8px;
    border-top: 1px dashed rgba(244, 179, 29, 0.3);
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.service-badge {
    display: inline-block;
    background: rgba(244, 179, 29, 0.2);
    color: var(--shell-yellow);
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8rem;
    margin-top: 5px;
    text-align: center;
}

.price-tag {
    background: rgba(60, 60, 60, 0.8);
    color: #fff;
    padding: 3px 8px;
    border-radius: 5px;
    margin-top: 5px;
    font-weight: bold;
    font-size: 0.85rem;
    display: inline-block;
}

/* Estilo para breakdown de custos */
.cost-breakdown {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-top: 5px;
    text-align: left;
}

.cost-item {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
}

.cost-total {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
    padding-top: 5px;
    border-top: 1px solid rgba(244, 179, 29, 0.3);
    font-weight: bold;
    color: var(--shell-yellow);
}

/* Estilo para timeline */
.timeline {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 5px;
    text-align: left;
}

.timeline-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
}

.timeline-item i {
    color: var(--shell-yellow);
    font-size: 0.9rem;
}

.fa-check-circle {
    color: #28a745 !important;
}

/* Estilos para as seções de aprovação/reprovação */
.approval-section {
    background: linear-gradient(145deg, #222, #333);
    border-radius: 8px;
    padding: 12px;
    color: white;
    height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 3px solid var(--shell-yellow);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    overflow: hidden;
    position: relative;
    margin-bottom: 15px;
}

.approval-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, transparent 70%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.approval-section:hover {
    transform: translateY(-5px) scale(1.03);
    box-shadow: 0 15px 30px rgba(0,0,0,0.3);
}

.approval-section:hover::before {
    opacity: 1;
}

.approval-section h3 {
    color: var(--shell-yellow);
    margin-bottom: 8px;
    font-weight: bold;
    font-size: 1.1rem;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
    position: relative;
    z-index: 1;
}

/* Bordas das seções de aprovação/reprovação */
.approve-section {
    border-left: 6px solid #28a745;
}

.approve-section:hover {
    background: linear-gradient(145deg, #222, #133a22);
}

.reject-section {
    border-left: 6px solid #dc3545;
}

.reject-section:hover {
    background: linear-gradient(145deg, #222, #3a1313);
}



/* Responsividade */
@media (max-width: 768px) {
    .flip-card-container {
        min-height: 230px;
    }

    .flip-card {
        min-height: 230px;
    }

    .glass-panel {
        padding: 15px;
    }

    .panel-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .service-box {
        margin-bottom: 15px;
    }

    /* Ajustes para modal de histórico em telas menores */
    .service-detail-content {
        padding: 20px 15px;
        width: 95%;
        max-height: 85vh;
    }

    /* Ajuste para empilhar colunas em dispositivos pequenos */
    .row {
        display: flex;
        flex-direction: column;
    }

    .col-md-6 {
        width: 100%;
        margin-bottom: 20px;
    }

    /* Ajuste para tabela responsiva */
    .table-responsive {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Ajuste para títulos nas caixas de histórico */
    .detailed-service-info h3, 
    .detailed-service-info h4,
    .maintenance-history h4,
    .maintenance-history h5,
    .technician-info h4 {
        font-size: 1.3rem;
        word-wrap: break-word;
    }

    /* Ajuste para badges de manutenção */
    .badge {
        white-space: normal;
        text-align: center;
        display: inline-block;
        margin-bottom: 3px;
    }

    /* Ajuste para as legendas das tabelas */
    .table th {
        font-size: 0.9rem;
    }

    /* Adiciona margens vertical para os itens de timeline */
    .timeline-item-big {
        margin-bottom: 10px;
    }

    /* Ajusta o espaçamento interno nas tabelas */
    .table td, .table th {
        padding: 0.5rem;
    }
}

/* Efeito de texto para o vidro */
.text-dark {
    color: var(--shell-yellow) !important;
}

.text-muted, h5.text-muted {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Override para badges */
.badge {
    padding: 0.5em 0.8em;
}

/* Animação de borda para os cards selecionados */
.service-box.highlight {
    animation: border-pulse 1.5s ease-in-out infinite;
}

@keyframes border-pulse {
    0% { border-color: rgba(244, 179, 29, 0.3); }
    50% { border-color: var(--shell-yellow); }
    100% { border-color: rgba(244, 179, 29, 0.3); }
}

/* Correções de responsividade para toda a página */
.container-fluid {
    max-width: 100%;
    overflow-x: hidden;
    padding-left: 0;
    padding-right: 0;
}

.row {
    margin-left: 0;
    margin-right: 0;
}

.col, .col-md-3, .col-sm-6, .col-md-6, .col-md-4 {
    padding-left: 5px;
    padding-right: 5px;
}

/* Melhorias de responsividade para action-buttons-container */
.action-buttons-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    width: 100%;
    max-width: 100%;
}

/* Garantir que o cartão flip se ajuste à tela */
#flip-card {
    width: 100%;
    max-width: 100%;
    perspective: 1000px;
}

.flip-card-inner {
    width: 100%;
}

.flip-card-front, .flip-card-back {
    width: 100%;
    max-width: 100%;
}

/* Ajustes responsivos para telas pequenas */
@media (max-width: 576px) {
    .flip-card-container {
        min-height: auto;
        height: clamp(450px, 80vh, 600px);
    }

    .action-buttons-container {
        padding: 5px;
    }

    .action-buttons-container .badge, 
    .action-buttons-container button {
        margin: 3px;
        font-size: 0.9rem;
    }

    .filter-buttons-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 8px;
        padding: 0 5px;
    }

    .filter-btn {
        width: 100%;
        padding: 8px;
        font-size: clamp(0.75rem, 2vw, 0.85rem);
    }

    /* Ajustes para os cards de manutenção em telas pequenas */
    .service-box {
        padding: 12px;
    }

    .service-order-item {
        height: clamp(130px, 25vh, 160px);
        padding: 10px;
    }

    .order-title {
        font-size: clamp(0.85rem, 2.2vw, 1rem);
        margin-bottom: 8px;
    }

    .order-info {
        font-size: clamp(0.75rem, 2vw, 0.85rem);
    }

    /* Centralização de conteúdo nos cards */
    .service-box .service-title,
    .service-box .service-date,
    .service-box .service-description,
    .service-box .service-footer {
        text-align: center;
    }

    /* Ajuste no tamanho dos textos para melhor legibilidade */
    .service-box-title {
        font-size: 1rem;
    }

    .service-box-subtitle {
        font-size: 0.9rem;
    }

    /* Melhorias nos botões do calendário flip para telas pequenas */
    .calendar-actions {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 8px;
    }

    .calendar-header-button {
        min-width: 100px;
        margin: 4px;
        font-size: 0.85rem;
    }

    /* Ajuste de espaçamento para o corpo do calendário */
    .calendar-body {
        padding: 10px 5px;
    }

    /* Melhoria na visualização de datas para telas pequenas */
    .calendar-date-box {
        padding: 8px 4px;
    }

    /* Ajuste nas mensagens de sistema */
    .system-message {
        max-width: 95%;
        margin: 10px auto;
    }
}

/* Ajustes para dispositivos móveis gerais */
@media (max-width: 767px) {
    /* Ajustar o container principal para caber em dispositivos móveis */
    .calendar-container {
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        padding: 5px !important;
        overflow-x: hidden !important;
    }

    /* Ajustar áreas com container-fluid para evitar overflow horizontal */
    .container-fluid {
        width: 100% !important;
        padding-left: 5px !important;
        padding-right: 5px !important;
    }

    /* Ajuste do menu lateral quando em telas pequenas */
    .sidebar {
        width: 100% !important;
        max-width: 100% !important;
    }

    /* Ajuste dos dias do calendário para telas pequenas */
    .calendar-day {
        height: 40px;
        line-height: 40px;
        font-size: 14px;
    }

    /* Ajustes no estilo do flip-card */
    #flip-card, .flip-card-inner, .flip-card-front, .flip-card-back {
        width: 100% !important;
        max-width: 100% !important;
        height: auto !important;
        min-height: auto !important;
    }

    /* Garantir que o conteúdo interno do card flip não cause overflow */
    .order-details-container {
        max-width: 100% !important;
        padding: 10px !important;
    }

    /* Ajustar cards de serviços para melhor visualização em mobile */
    .service-box {
        width: 100% !important;
        max-width: 100% !important;
        margin-bottom: 10px !important;
    }

    /* Ajustar serviços agendados para visualização em mobile */
    .service-order-item {
        padding: 10px !important;
    }

    /* Ajustar o sistema de grid para mobile */
    .row > [class*="col-"] {
        padding-left: 5px !important;
        padding-right: 5px !important;
    }
}

/* Ajustes para telas extremamente pequenas */
@media (max-width: 400px) {
    .calendar-header-button,
    .action-buttons-container .badge,
    .action-buttons-container button {
        min-width: 85px;
        font-size: 0.8rem;
        padding: 6px 8px;
    }

    /* Aumentar legibilidade em telas muito pequenas */
    .date-box {
        font-size: 0.9rem;
        padding: 6px 4px;
    }

    /* Ajustar cabeçalhos */
    .section-title, .calendar-title {
        font-size: 1.1rem;
    }

    /* Garantir que cards não fiquem muito pequenos */
    .service-box {
        min-height: 100px;
    }

    /* Reduzir padding ainda mais para telas muito pequenas */
    body {
        padding: 5px !important;
    }

    /* Botões mais compactos */
    .shell-btn {
        padding: 6px 10px !important;
        font-size: 0.75rem !important;
    }

    /* Esconder alguns elementos menos importantes em telas muito pequenas */
    .desktop-only {
        display: none !important;
    }
}