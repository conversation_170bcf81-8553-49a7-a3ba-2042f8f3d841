/* CSS Simplificado para a página de Ordens */

/* Variáveis */
:root {
    --shell-red: #ED1C24;
    --shell-yellow: #FDB813;
    --shell-dark: #333333;
    --shell-light: #f8f9fa;
    --shell-gray: #808080;
    
    --status-pending: #ffc107;
    --status-in-progress: #17a2b8;
    --status-completed: #28a745;
    --status-cancelled: #dc3545;
    
    --priority-low: #6c757d;
    --priority-medium: #17a2b8;
    --priority-high: #ffc107;
    --priority-urgent: #dc3545;
}

/* Cabe<PERSON><PERSON><PERSON> da página */
.page-header {
    background-color: rgba(30, 30, 30, 0.7);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.page-title {
    color: var(--shell-yellow);
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    font-weight: 600;
    margin-bottom: 0;
}

/* Cards de métricas */
.metrics-card {
    background-color: rgba(30, 30, 30, 0.7);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    color: white;
    transition: transform 0.3s, box-shadow 0.3s;
}

.metrics-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.metrics-card .card-title {
    font-size: 0.9rem;
    color: var(--shell-gray);
    margin-bottom: 10px;
}

.metrics-card .card-value {
    font-size: 2rem;
    font-weight: 700;
    font-family: 'Share Tech Mono', monospace;
}

.metrics-card.pending .card-value {
    color: var(--status-pending);
}

.metrics-card.progress .card-value {
    color: var(--status-in-progress);
}

.metrics-card.completed .card-value {
    color: var(--status-completed);
}

.metrics-card.total .card-value {
    color: var(--shell-yellow);
}

/* Tabela de ordens */
.orders-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
}

.orders-table thead th {
    background-color: var(--shell-dark);
    color: var(--shell-yellow);
    padding: 12px 15px;
    font-weight: 600;
    text-align: left;
    border: none;
}

.orders-table tbody tr {
    background-color: rgba(30, 30, 30, 0.7);
    transition: background-color 0.3s;
}

.orders-table tbody tr:hover {
    background-color: rgba(50, 50, 50, 0.8);
}

.orders-table td {
    padding: 12px 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
}

/* Status e prioridade */
.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-pending {
    background-color: var(--status-pending);
    color: #212529;
}

.status-in-progress {
    background-color: var(--status-in-progress);
    color: white;
}

.status-completed {
    background-color: var(--status-completed);
    color: white;
}

.status-cancelled {
    background-color: var(--status-cancelled);
    color: white;
}

.priority-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.priority-low {
    background-color: var(--priority-low);
    color: white;
}

.priority-medium {
    background-color: var(--priority-medium);
    color: white;
}

.priority-high {
    background-color: var(--priority-high);
    color: #212529;
}

.priority-urgent {
    background-color: var(--priority-urgent);
    color: white;
}

/* Botões de ação */
.action-btn {
    padding: 5px 10px;
    border-radius: 5px;
    border: none;
    margin-right: 5px;
    transition: all 0.3s;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.btn-view {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
}

.btn-edit {
    background-color: #17a2b8;
    color: white;
}

.btn-delete {
    background-color: #dc3545;
    color: white;
}

/* Filtros */
.filter-section {
    background-color: rgba(30, 30, 30, 0.7);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.filter-section select,
.filter-section input {
    background-color: rgba(50, 50, 50, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 5px;
    padding: 8px 12px;
}

.filter-section select:focus,
.filter-section input:focus {
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 0.2rem rgba(253, 184, 19, 0.25);
}

/* Estado vazio */
.empty-state {
    text-align: center;
    padding: 40px 20px;
}

.empty-state i {
    font-size: 3rem;
    color: var(--shell-gray);
    margin-bottom: 15px;
}

.empty-state h5 {
    color: var(--shell-light);
    margin-bottom: 10px;
}

.empty-state p {
    color: var(--shell-gray);
    margin-bottom: 20px;
}

/* Responsividade */
@media (max-width: 768px) {
    .metrics-card {
        margin-bottom: 15px;
    }
    
    .orders-table {
        display: block;
        overflow-x: auto;
    }
}
