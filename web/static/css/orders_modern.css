/* Estilos para a nova página de ordens de serviço */

:root {
    --shell-red: #ED1C24;
    --shell-yellow: #FDB813;
    --shell-dark: #1E1E1E;
    --shell-darker: #121212;
    --shell-light: #f8f9fa;
    --shell-gray: #6c757d;
    --shell-border: #343a40;

    --status-pending: #ffc107;
    --status-progress: #0d6efd;
    --status-completed: #198754;
    --status-cancelled: #dc3545;

    --priority-low: #198754;
    --priority-medium: #0dcaf0;
    --priority-high: #ffc107;
    --priority-urgent: #dc3545;
}

/* ===== Estilos Gerais ===== */
.page-title {
    color: var(--shell-yellow);
    font-size: 1.8rem;
    font-weight: 600;
    font-family: 'Rajdhani', sans-serif;
}

.shell-divider-animated {
    height: 3px;
    background: linear-gradient(90deg, var(--shell-yellow) 0%, rgba(253, 184, 19, 0.1) 100%);
    margin: 1rem 0;
    border-radius: 3px;
    position: relative;
    overflow: hidden;
}

.shell-divider-animated::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    100% {
        left: 100%;
    }
}

/* ===== Efeitos de Vidro e Neon ===== */
.glass-effect {
    background: rgba(30, 30, 30, 0.7) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.glass-card {
    background: rgba(30, 30, 30, 0.7) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.glass-card:hover {
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.3);
    transform: translateY(-5px);
}

.btn-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--shell-light);
    transition: all 0.3s ease;
}

.btn-glass:hover, .btn-glass.active {
    background: rgba(253, 184, 19, 0.2);
    border-color: var(--shell-yellow);
    color: var(--shell-yellow);
    box-shadow: 0 0 15px rgba(253, 184, 19, 0.3);
}

.btn-neon {
    background: rgba(30, 30, 30, 0.7);
    border: 1px solid var(--shell-yellow);
    color: var(--shell-yellow);
    position: relative;
    overflow: hidden;
    z-index: 1;
    transition: all 0.3s ease;
}

.btn-neon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(253, 184, 19, 0.2), transparent);
    transition: all 0.3s ease;
    z-index: -1;
}

.btn-neon:hover::before {
    left: 100%;
    transition: 0.5s;
}

.btn-neon:hover {
    box-shadow: 0 0 15px rgba(253, 184, 19, 0.5);
    color: var(--shell-yellow);
}

.pulse-btn {
    position: relative;
}

.pulse-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: inherit;
    box-shadow: 0 0 0 0 rgba(253, 184, 19, 0.7);
    animation: pulse-animation 2s infinite;
}

@keyframes pulse-animation {
    0% {
        box-shadow: 0 0 0 0 rgba(253, 184, 19, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(253, 184, 19, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(253, 184, 19, 0);
    }
}

/* ===== Header e Ícones ===== */
.page-header-wrapper {
    margin-bottom: 1.5rem;
}

.header-icon-container {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: rgba(253, 184, 19, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: var(--shell-yellow);
}

.header-icon-bubble {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(253, 184, 19, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--shell-yellow);
}

.modal-icon-container {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: rgba(253, 184, 19, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: var(--shell-yellow);
}

.modal-icon-container.danger {
    background: rgba(220, 53, 69, 0.15);
    color: var(--status-cancelled);
}

.bx-tada-hover:hover {
    animation: tada 1s ease infinite;
}

.bx-spin-hover:hover {
    animation: spin 2s linear infinite;
}

.bx-flashing-hover:hover {
    animation: flashing 1.5s ease infinite;
}

@keyframes tada {
    0% {transform: scale(1);}
    10%, 20% {transform: scale(0.9) rotate(-3deg);}
    30%, 50%, 70%, 90% {transform: scale(1.1) rotate(3deg);}
    40%, 60%, 80% {transform: scale(1.1) rotate(-3deg);}
    100% {transform: scale(1) rotate(0);}
}

@keyframes spin {
    0% {transform: rotate(0deg);}
    100% {transform: rotate(360deg);}
}

@keyframes flashing {
    0%, 50%, 100% {opacity: 1;}
    25%, 75% {opacity: 0.5;}
}

/* ===== Métricas ===== */
.metrics-row {
    margin-top: 1.5rem;
}

.metric-card {
    background-color: rgba(30, 30, 30, 0.7);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    height: 100%;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, transparent 100%);
    pointer-events: none;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.3);
}

.metric-pending {
    border-left: 4px solid var(--status-pending);
}

.metric-progress {
    border-left: 4px solid var(--status-progress);
}

.metric-completed {
    border-left: 4px solid var(--status-completed);
}

.metric-total {
    border-left: 4px solid var(--shell-yellow);
}

.metric-icon-container {
    position: relative;
    width: 80px;
    height: 80px;
    flex-shrink: 0;
}

.metric-icon {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
}

.metric-pending .metric-icon {
    background-color: rgba(255, 193, 7, 0.15);
    color: var(--status-pending);
}

.metric-progress .metric-icon {
    background-color: rgba(13, 110, 253, 0.15);
    color: var(--status-progress);
}

.metric-completed .metric-icon {
    background-color: rgba(25, 135, 84, 0.15);
    color: var(--status-completed);
}

.metric-total .metric-icon {
    background-color: rgba(253, 184, 19, 0.15);
    color: var(--shell-yellow);
}

.metric-circle-progress {
    position: absolute;
    top: 0;
    left: 0;
    transform: rotate(-90deg);
}

.metric-circle-bg {
    fill: none;
    stroke: rgba(255, 255, 255, 0.05);
    stroke-width: 4;
}

.metric-circle-fill {
    fill: none;
    stroke-width: 4;
    stroke-linecap: round;
    stroke-dasharray: 226;
    stroke-dashoffset: 226;
    transition: stroke-dashoffset 1s ease;
}

.metric-pending-stroke {
    stroke: var(--status-pending);
}

.metric-progress-stroke {
    stroke: var(--status-progress);
}

.metric-completed-stroke {
    stroke: var(--status-completed);
}

.metric-total-stroke {
    stroke: var(--shell-yellow);
}

.metric-content {
    flex-grow: 1;
}

.metric-value-container {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    font-family: 'Share Tech Mono', monospace;
    color: var(--shell-light);
    line-height: 1;
}

.metric-percentage {
    font-size: 0.875rem;
    color: var(--shell-gray);
    font-family: 'Share Tech Mono', monospace;
}

.metric-label {
    font-size: 0.875rem;
    color: var(--shell-gray);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.metric-trend {
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.metric-trend i {
    font-size: 0.875rem;
}

/* ===== Filtros ===== */
.filter-container {
    margin-bottom: 2rem;
}

.filter-section {
    transition: all 0.3s ease;
}

.filter-label {
    font-size: 0.875rem;
    color: var(--shell-light);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.filter-group {
    position: relative;
    margin-bottom: 0.5rem;
}

.filter-select {
    background-color: rgba(30, 30, 30, 0.7);
    border: 1px solid var(--shell-border);
    color: var(--shell-light);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.filter-select:focus {
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 0.25rem rgba(253, 184, 19, 0.25);
}

.filter-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--shell-yellow);
    transition: width 0.3s ease;
}

.status-indicator.active {
    background-color: var(--status-pending);
    width: 100%;
}

.priority-indicator.active {
    background-color: var(--priority-high);
    width: 100%;
}

.location-indicator.active {
    background-color: var(--shell-red);
    width: 100%;
}

.date-indicator.active {
    background-color: var(--status-completed);
    width: 100%;
}

.search-container {
    margin-bottom: 1.5rem;
}

.search-group {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.search-icon {
    background-color: rgba(30, 30, 30, 0.7);
    border: none;
    color: var(--shell-yellow);
    font-size: 1.2rem;
}

.search-input {
    background-color: rgba(30, 30, 30, 0.7);
    border: none;
    color: var(--shell-light);
    font-size: 1rem;
    padding: 0.75rem 1rem;
}

.search-input:focus {
    box-shadow: none;
    background-color: rgba(30, 30, 30, 0.8);
}

.filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.filter-tag {
    display: inline-flex;
    align-items: center;
    background-color: rgba(253, 184, 19, 0.1);
    color: var(--shell-yellow);
    border: 1px solid rgba(253, 184, 19, 0.3);
    border-radius: 50px;
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.filter-tag:hover {
    background-color: rgba(253, 184, 19, 0.2);
}

.filter-tag .bx-x {
    margin-left: 0.5rem;
    cursor: pointer;
}

.filter-tag.status-tag {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--status-pending);
    border-color: rgba(255, 193, 7, 0.3);
}

.filter-tag.priority-tag {
    background-color: rgba(13, 202, 240, 0.1);
    color: var(--priority-medium);
    border-color: rgba(13, 202, 240, 0.3);
}

.filter-tag.location-tag {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--status-cancelled);
    border-color: rgba(220, 53, 69, 0.3);
}

.filter-tag.date-tag {
    background-color: rgba(25, 135, 84, 0.1);
    color: var(--status-completed);
    border-color: rgba(25, 135, 84, 0.3);
}

/* ===== Tabela de Ordens ===== */
.orders-container {
    margin-bottom: 2rem;
}

.table-header-shell {
    background-color: rgba(18, 18, 18, 0.8);
    color: var(--shell-yellow);
    border-bottom: 2px solid var(--shell-yellow);
}

.table-header-shell th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    padding: 0.75rem 1rem;
    position: relative;
}

.sortable {
    cursor: pointer;
    transition: all 0.3s ease;
}

.sortable:hover {
    background-color: rgba(253, 184, 19, 0.05);
}

.sortable i {
    font-size: 1rem;
    margin-left: 0.25rem;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.sortable:hover i {
    opacity: 1;
}

.sortable.asc i, .sortable.desc i {
    opacity: 1;
    color: var(--shell-yellow);
}

.table-dark {
    background-color: var(--shell-dark);
    color: var(--shell-light);
    border-color: var(--shell-border);
}

.table-dark tbody tr {
    border-bottom: 1px solid var(--shell-border);
    transition: all 0.3s ease;
}

.table-dark tbody tr:hover {
    background-color: rgba(253, 184, 19, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.order-row {
    cursor: pointer;
}

.order-id-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.order-id {
    font-family: 'Share Tech Mono', monospace;
    font-weight: 600;
    color: var(--shell-yellow);
}

.order-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.priority-baixa-indicator {
    background-color: var(--priority-low);
}

.priority-media-indicator {
    background-color: var(--priority-medium);
}

.priority-alta-indicator {
    background-color: var(--priority-high);
}

.priority-urgente-indicator {
    background-color: var(--priority-urgent);
}

.order-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.status-pendente-bg {
    background-color: rgba(255, 193, 7, 0.15);
    color: var(--status-pending);
}

.status-em_andamento-bg {
    background-color: rgba(13, 110, 253, 0.15);
    color: var(--status-progress);
}

.status-concluida-bg {
    background-color: rgba(25, 135, 84, 0.15);
    color: var(--status-completed);
}

.status-cancelada-bg {
    background-color: rgba(220, 53, 69, 0.15);
    color: var(--status-cancelled);
}

.order-title-container {
    display: flex;
    flex-direction: column;
}

.order-title {
    font-weight: 500;
    color: var(--shell-light);
    margin-bottom: 0.25rem;
}

.status-badge-container, .priority-badge-container {
    display: flex;
    align-items: center;
}

.status-badge, .priority-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
    gap: 0.25rem;
}

.status-pendente {
    background-color: rgba(255, 193, 7, 0.15);
    color: var(--status-pending);
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-em_andamento {
    background-color: rgba(13, 110, 253, 0.15);
    color: var(--status-progress);
    border: 1px solid rgba(13, 110, 253, 0.3);
}

.status-concluida {
    background-color: rgba(25, 135, 84, 0.15);
    color: var(--status-completed);
    border: 1px solid rgba(25, 135, 84, 0.3);
}

.status-cancelada {
    background-color: rgba(220, 53, 69, 0.15);
    color: var(--status-cancelled);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.priority-baixa {
    background-color: rgba(25, 135, 84, 0.15);
    color: var(--priority-low);
    border: 1px solid rgba(25, 135, 84, 0.3);
}

.priority-media {
    background-color: rgba(13, 202, 240, 0.15);
    color: var(--priority-medium);
    border: 1px solid rgba(13, 202, 240, 0.3);
}

.priority-alta {
    background-color: rgba(255, 193, 7, 0.15);
    color: var(--priority-high);
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.priority-urgente {
    background-color: rgba(220, 53, 69, 0.15);
    color: var(--priority-urgent);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.pulse-animation {
    position: relative;
}

.pulse-animation::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: inherit;
    animation: pulse-badge 2s infinite;
    opacity: 0;
}

@keyframes pulse-badge {
    0% {
        transform: scale(1);
        opacity: 0.7;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

.status-pendente.pulse-animation::after {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
}

.status-em_andamento.pulse-animation::after {
    box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.7);
}

.branch-info, .date-info {
    display: flex;
    align-items: center;
    color: var(--shell-gray);
    font-size: 0.875rem;
}

.date-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.date-value {
    color: var(--shell-light);
    font-weight: 500;
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

/* ===== Cards de Ordens ===== */
.order-card {
    background-color: rgba(30, 30, 30, 0.7);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    border-top: 3px solid transparent;
}

.order-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.3);
}

.order-card.priority-baixa {
    border-top-color: var(--priority-low);
}

.order-card.priority-media {
    border-top-color: var(--priority-medium);
}

.order-card.priority-alta {
    border-top-color: var(--priority-high);
}

.order-card.priority-urgente {
    border-top-color: var(--priority-urgent);
}

.order-card-header {
    padding: 1.25rem;
    background-color: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid var(--shell-border);
}

.order-id-badge {
    display: inline-flex;
    align-items: center;
    background-color: rgba(253, 184, 19, 0.1);
    color: var(--shell-yellow);
    border-radius: 50px;
    padding: 0.25rem 0.75rem;
    font-family: 'Share Tech Mono', monospace;
    font-weight: 600;
}

.order-date {
    color: var(--shell-gray);
    font-size: 0.875rem;
}

.order-status-container {
    margin-top: 0.75rem;
}

.order-card-body {
    padding: 1.25rem;
}

.order-card .order-title {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    color: var(--shell-light);
    font-weight: 600;
}

.order-info {
    margin-top: 1rem;
}

.info-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    color: var(--shell-gray);
    font-size: 0.875rem;
}

.info-item i {
    width: 20px;
    text-align: center;
}

.priority-text {
    font-weight: 600;
}

.priority-text.priority-baixa {
    color: var(--priority-low);
}

.priority-text.priority-media {
    color: var(--priority-medium);
}

.priority-text.priority-alta {
    color: var(--priority-high);
}

.priority-text.priority-urgente {
    color: var(--priority-urgent);
}

.order-card-footer {
    padding: 1.25rem;
    background-color: rgba(0, 0, 0, 0.1);
    border-top: 1px solid var(--shell-border);
}

.card-hover-actions {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.order-card:hover .card-hover-actions {
    opacity: 1;
    pointer-events: auto;
}

/* ===== Visualização Kanban ===== */
.kanban-container {
    height: 600px;
    overflow: hidden;
}

.kanban-board {
    height: 100%;
    overflow: auto;
}

.kanban-columns {
    display: flex;
    gap: 1.5rem;
    height: 100%;
    min-width: 900px;
}

.kanban-column {
    flex: 1;
    background-color: rgba(30, 30, 30, 0.5);
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    height: 100%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-top: 3px solid transparent;
}

.pending-column {
    border-top-color: var(--status-pending);
}

.progress-column {
    border-top-color: var(--status-progress);
}

.completed-column {
    border-top-color: var(--status-completed);
}

.kanban-column-header {
    padding: 1rem;
    background-color: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid var(--shell-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.kanban-counter {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--shell-light);
    font-size: 0.75rem;
    font-weight: 600;
}

.kanban-items {
    padding: 1rem;
    overflow-y: auto;
    flex-grow: 1;
}

.kanban-item {
    background-color: rgba(30, 30, 30, 0.7);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 3px solid transparent;
    cursor: grab;
    transition: all 0.2s ease;
}

.kanban-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.kanban-item.priority-baixa {
    border-left-color: var(--priority-low);
}

.kanban-item.priority-media {
    border-left-color: var(--priority-medium);
}

.kanban-item.priority-alta {
    border-left-color: var(--priority-high);
}

.kanban-item.priority-urgente {
    border-left-color: var(--priority-urgent);
}

.kanban-item-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.kanban-item-id {
    font-family: 'Share Tech Mono', monospace;
    font-weight: 600;
    color: var(--shell-yellow);
    font-size: 0.875rem;
}

.kanban-item-title {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.kanban-item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.75rem;
    font-size: 0.75rem;
    color: var(--shell-gray);
}

/* ===== Paginação Moderna ===== */
.pagination-modern {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.pagination-modern .page-item {
    margin: 0 0.125rem;
}

.pagination-modern .page-link {
    background-color: rgba(30, 30, 30, 0.7);
    border-color: var(--shell-border);
    color: var(--shell-light);
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
}

.pagination-modern .page-link:hover {
    background-color: rgba(253, 184, 19, 0.1);
    color: var(--shell-yellow);
    border-color: rgba(253, 184, 19, 0.3);
}

.pagination-modern .page-item.active .page-link {
    background-color: var(--shell-yellow);
    border-color: var(--shell-yellow);
    color: var(--shell-darker);
    font-weight: 600;
}

.pagination-modern .page-item.disabled .page-link {
    background-color: rgba(30, 30, 30, 0.5);
    border-color: var(--shell-border);
    color: var(--shell-gray);
    opacity: 0.5;
}

.page-info .page-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.5rem;
}

.page-input {
    width: 40px;
    text-align: center;
    background-color: rgba(30, 30, 30, 0.7);
    border: 1px solid var(--shell-border);
    color: var(--shell-light);
    border-radius: 4px;
    padding: 0.25rem;
}

.page-size-selector {
    display: flex;
    align-items: center;
}

.btn-page-size {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.btn-page-size.active {
    background-color: var(--shell-yellow);
    color: var(--shell-darker);
    border-color: var(--shell-yellow);
    font-weight: 600;
}

/* ===== Estado Vazio ===== */
.empty-state {
    padding: 3rem 1rem;
    text-align: center;
}

.empty-icon-container {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: rgba(253, 184, 19, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: var(--shell-yellow);
    margin: 0 auto 1.5rem;
}

/* ===== Loading Spinner ===== */
.spinner-container {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 0 auto;
}

.spinner-border-shell {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 3px solid transparent;
    border-top-color: var(--shell-yellow);
    animation: spin 1s linear infinite;
}

.spinner-border-inner {
    position: absolute;
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    border-radius: 50%;
    border: 3px solid transparent;
    border-top-color: var(--shell-red);
    animation: spin 1.5s linear infinite reverse;
}

/* ===== Modal de Detalhes ===== */
.order-detail-header {
    position: relative;
}

.order-detail-status-bar {
    height: 6px;
    background-color: var(--status-pending);
}

.order-detail-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--shell-light);
}

.order-detail-id {
    font-family: 'Share Tech Mono', monospace;
    font-weight: 600;
    color: var(--shell-yellow);
    font-size: 1.1rem;
}

.order-detail-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-top: 0.5rem;
}

.meta-item {
    display: flex;
    align-items: center;
    color: var(--shell-gray);
    font-size: 0.875rem;
}

.nav-tabs-custom {
    border-bottom: 1px solid var(--shell-border);
}

.nav-tabs-custom .nav-link {
    color: var(--shell-gray);
    border: none;
    border-bottom: 2px solid transparent;
    padding: 0.75rem 1.25rem;
    background-color: transparent;
    transition: all 0.3s ease;
}

.nav-tabs-custom .nav-link:hover {
    color: var(--shell-light);
    border-color: rgba(253, 184, 19, 0.3);
}

.nav-tabs-custom .nav-link.active {
    color: var(--shell-yellow);
    background-color: transparent;
    border-color: var(--shell-yellow);
    font-weight: 500;
}

.detail-section {
    background-color: rgba(30, 30, 30, 0.5);
    border-radius: 12px;
    padding: 1.25rem;
    border: 1px solid var(--shell-border);
}

.detail-section-title {
    display: flex;
    align-items: center;
    color: var(--shell-yellow);
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.detail-section-content {
    color: var(--shell-light);
}

.order-description {
    line-height: 1.6;
}

.equipment-info {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.info-list, .date-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.info-list-item, .date-list-item {
    display: flex;
    justify-content: space-between;
}

.info-list-label, .date-list-label {
    color: var(--shell-gray);
    font-size: 0.875rem;
}

.info-list-value, .date-list-value {
    font-weight: 500;
}

.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 8px;
    height: 100%;
    width: 2px;
    background-color: var(--shell-border);
}

.timeline-item {
    position: relative;
    padding-bottom: 1.5rem;
}

.timeline-dot {
    position: absolute;
    top: 0;
    left: -2rem;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: var(--shell-yellow);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    color: var(--shell-darker);
    z-index: 1;
}

.timeline-content {
    background-color: rgba(30, 30, 30, 0.5);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid var(--shell-border);
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.timeline-title {
    font-weight: 600;
    color: var(--shell-light);
}

.timeline-date {
    font-size: 0.875rem;
    color: var(--shell-gray);
}

.timeline-body {
    color: var(--shell-gray);
    font-size: 0.875rem;
}

.attachments-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.attachment-item {
    background-color: rgba(30, 30, 30, 0.5);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid var(--shell-border);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    transition: all 0.3s ease;
}

.attachment-item:hover {
    background-color: rgba(30, 30, 30, 0.7);
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.attachment-icon {
    font-size: 2rem;
    color: var(--shell-yellow);
    margin-bottom: 0.5rem;
}

.attachment-name {
    font-weight: 500;
    color: var(--shell-light);
    margin-bottom: 0.25rem;
    word-break: break-word;
}

.attachment-size {
    font-size: 0.75rem;
    color: var(--shell-gray);
}

.attachment-actions {
    margin-top: 0.75rem;
    display: flex;
    gap: 0.5rem;
}

.comments-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.comment-item {
    background-color: rgba(30, 30, 30, 0.5);
    border-radius: 12px;
    padding: 1.25rem;
    border: 1px solid var(--shell-border);
}

.comment-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.comment-author {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.comment-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(253, 184, 19, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--shell-yellow);
    font-weight: 600;
    font-size: 1rem;
}

.comment-author-info {
    display: flex;
    flex-direction: column;
}

.comment-author-name {
    font-weight: 600;
    color: var(--shell-light);
}

.comment-date {
    font-size: 0.75rem;
    color: var(--shell-gray);
}

.comment-content {
    color: var(--shell-light);
    line-height: 1.6;
}

.comment-form textarea {
    background-color: rgba(30, 30, 30, 0.5);
    border: 1px solid var(--shell-border);
    color: var(--shell-light);
    border-radius: 8px;
    resize: none;
}

.comment-form textarea:focus {
    background-color: rgba(30, 30, 30, 0.7);
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 0.25rem rgba(253, 184, 19, 0.25);
}

/* ===== Modal de Exclusão ===== */
.delete-icon-container {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: rgba(220, 53, 69, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.delete-icon-pulse {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: rgba(220, 53, 69, 0.1);
    animation: delete-pulse 2s infinite;
}

@keyframes delete-pulse {
    0% {
        transform: scale(1);
        opacity: 0.7;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

/* ===== Toast Notifications ===== */
.toast-shell {
    background-color: rgba(30, 30, 30, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.toast-shell .toast-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--shell-light);
}

.toast-shell .toast-body {
    color: var(--shell-light);
}

.toast-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    margin-right: 0.75rem;
}

.toast-success .toast-icon {
    background-color: rgba(25, 135, 84, 0.2);
    color: var(--status-completed);
}

.toast-warning .toast-icon {
    background-color: rgba(255, 193, 7, 0.2);
    color: var(--status-pending);
}

.toast-error .toast-icon {
    background-color: rgba(220, 53, 69, 0.2);
    color: var(--status-cancelled);
}

.toast-info .toast-icon {
    background-color: rgba(13, 202, 240, 0.2);
    color: var(--priority-medium);
}

/* ===== Responsividade ===== */
@media (max-width: 992px) {
    .metric-card {
        margin-bottom: 1rem;
    }

    .order-card-footer {
        flex-direction: column;
    }

    .order-card-footer .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .kanban-columns {
        flex-direction: column;
        height: auto;
    }

    .kanban-column {
        height: 400px;
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 768px) {
    .page-header-wrapper {
        flex-direction: column;
        align-items: flex-start;
    }

    .page-actions {
        margin-top: 1rem;
        width: 100%;
        display: flex;
        justify-content: space-between;
    }

    .order-detail-meta {
        flex-direction: column;
        gap: 0.75rem;
    }

    .nav-tabs-custom .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
}
