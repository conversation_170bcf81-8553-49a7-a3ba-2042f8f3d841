/* Estilos para a página Perfil da Empresa - Tema Shell */

/* Tema Shell para cards */
.card {
    background-color: #222;
    border: none;
    border-top: 3px solid var(--shell-red);
    color: var(--shell-white);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: rgba(237, 28, 36, 0.1);
    border-bottom: 1px solid rgba(237, 28, 36, 0.2);
    padding: 1rem 1.25rem;
}

.card-header h6 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--shell-yellow);
    margin: 0;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 1.25rem;
}

/* Upload de Logomarca */
.logo-upload {
    position: relative;
    max-width: 250px;
    margin: 0 auto;
}

.logo-preview img {
    max-width: 100%;
    max-height: 150px;
    border-radius: 8px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
    border: 2px solid #333;
}

.logo-edit {
    margin-top: 15px;
}

.logo-edit input {
    display: none;
}

/* Avaliação com estrelas */
.rating-stars {
    font-size: 1.2rem;
}

/* Formulário */
.form-label {
    font-weight: 600;
    color: var(--shell-yellow);
    font-size: 1rem;
    margin-bottom: 0.5rem;
    display: block;
}

.form-control {
    background-color: #333;
    border-color: #444;
    color: var(--shell-white);
    font-size: 1rem;
    padding: 0.75rem 1rem;
    border-radius: 0.25rem;
    height: auto;
}

.form-control:focus {
    background-color: #333;
    border-color: var(--shell-red);
    color: var(--shell-white);
    box-shadow: 0 0 0 0.2rem rgba(237, 28, 36, 0.25);
}

/* Títulos de seção */
.section-title {
    color: var(--shell-yellow);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid rgba(237, 28, 36, 0.3);
    padding-bottom: 0.5rem;
}

/* Textos */
.text-primary {
    color: var(--shell-yellow) !important;
}

.text-info {
    color: #17a2b8 !important;
}

.text-success {
    color: #28a745 !important;
}

.text-warning {
    color: var(--shell-yellow) !important;
}

.text-danger {
    color: var(--shell-red) !important;
}

/* Estatísticas */
.card .small.font-weight-bold {
    color: var(--shell-yellow);
    font-size: 0.8rem;
    text-transform: uppercase;
}

.text-gray-800 {
    color: var(--shell-white) !important;
}

/* Status da empresa */
.form-switch .form-check-input:checked {
    background-color: #1cc88a;
    border-color: #1cc88a;
}

.form-switch .form-check-input:not(:checked) {
    background-color: #e74a3b;
    border-color: #e74a3b;
}

/* Botões */
.btn {
    font-size: 0.95rem;
    padding: 0.5rem 1.25rem;
    border-radius: 0.25rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--shell-red);
    border-color: var(--shell-red);
    color: white;
}

.btn-primary:hover {
    background-color: var(--shell-red-hover);
    border-color: var(--shell-red-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
    background-color: #444;
    border-color: #444;
    color: white;
}

.btn-secondary:hover {
    background-color: #555;
    border-color: #555;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-outline-secondary {
    color: var(--shell-white);
    border-color: #444;
}

.btn-outline-secondary:hover {
    background-color: #444;
    color: var(--shell-white);
}

/* Botão Salvar Alterações */
.btn-save-profile {
    display: block;
    width: 100%;
    margin-top: 1rem;
    padding: 0.75rem;
    font-size: 1rem;
}

/* Container de ações */
.actions-container {
    background-color: #222;
    border-radius: 0.5rem;
    padding: 1.25rem;
    margin-top: 1.5rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
    border-left: 3px solid var(--shell-yellow);
}

/* Responsividade */
@media (max-width: 1200px) {
    .col-xl-4, .col-xl-8 {
        width: 100%;
    }

    .container-fluid {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (max-width: 992px) {
    .card {
        margin-bottom: 1.25rem;
    }

    .form-label {
        margin-bottom: 0.35rem;
    }

    .form-control {
        padding: 0.6rem 0.8rem;
    }
}

@media (max-width: 768px) {
    .logo-preview img {
        max-height: 120px;
    }

    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .card-body {
        padding: 1.25rem;
    }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    h1.h3 {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .card-body {
        padding: 1rem;
    }

    .form-control {
        font-size: 0.95rem;
    }

    h1.h3 {
        font-size: 1.35rem;
    }
}
