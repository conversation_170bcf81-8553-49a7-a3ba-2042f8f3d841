/* Estilos para a página de detalhes da ordem de serviço - Versão Moderna */
:root {
    --shell-red: #ED1C24;
    --shell-yellow: #FDB813;
    --shell-dark: #1E1E1E;
    --shell-darker: #121212;
    --shell-light-gray: rgba(255, 255, 255, 0.1);
    --shell-border: rgba(255, 255, 255, 0.15);

    /* Status colors */
    --status-pending: #ffc107;
    --status-in-progress: #0d6efd;
    --status-pending-approval: #17a2b8;
    --status-revision-needed: #fd7e14;
    --status-approved: #28a745;
    --status-invoiced: #6f42c1;
    --status-paid: #20c997;
    --status-cancelled: #dc3545;
    --status-rejected: #dc3545;
    --status-atrasado: #dc3545;

    /* Priority colors */
    --priority-low: #28a745;
    --priority-medium: #17a2b8;
    --priority-high: #ffc107;
    --priority-urgent: #dc3545;

    /* Circle colors */
    --circle-success: #28a745;
    --circle-pending: #ffc107;
}

body {
    background-color: var(--shell-darker);
    color: #fff;
    font-family: 'Rajdhani', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Container principal */
.order-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    border: 2px solid var(--shell-yellow);
    border-radius: 15px;
    background-color: var(--shell-dark);
}

/* Cabeçalho da página */
.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--shell-yellow);
}

.header-left {
    flex: 1;
}

.header-center {
    flex: 2;
    text-align: center;
}

.order-title {
    color: white;
    font-size: 1.8rem;
    margin: 0;
    font-weight: 600;
}

.back-button {
    background-color: var(--shell-red);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    font-weight: 600;
}

.back-button:hover {
    background-color: #ff3c43;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    color: white;
}

/* Conteúdo principal */
.order-main-content {
    display: flex;
    margin-bottom: 30px;
}

/* Seção de título e detalhes */
.maintenance-title-section {
    flex: 3;
    padding: 20px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    margin-right: 20px;
}

.maintenance-title {
    color: var(--shell-yellow);
    font-size: 1.6rem;
    margin-top: 0;
    margin-bottom: 15px;
    font-weight: 600;
}

.maintenance-details {
    margin-bottom: 20px;
}

.details-label {
    color: var(--shell-yellow);
    font-weight: 600;
    margin-bottom: 5px;
}

.details-text {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 15px;
    border-radius: 5px;
    border: 1px solid var(--shell-light-gray);
    min-height: 100px;
}

.maintenance-meta {
    display: flex;
    justify-content: space-between;
}

.meta-item {
    display: flex;
    flex-direction: column;
}

.meta-label {
    color: var(--shell-yellow);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.meta-value {
    display: flex;
    align-items: center;
}

.meta-value i {
    margin-right: 5px;
    color: var(--shell-yellow);
}

/* Sidebar */
.order-sidebar {
    flex: 1;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 20px;
}

.sidebar-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--shell-light-gray);
}

.sidebar-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.sidebar-title {
    color: var(--shell-yellow);
    font-size: 1.1rem;
    margin-top: 0;
    margin-bottom: 10px;
}

.responsible-info {
    display: flex;
    align-items: center;
}

.responsible-info i {
    font-size: 1.5rem;
    margin-right: 10px;
    color: var(--shell-yellow);
}

.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-weight: 600;
    text-align: center;
}

.status-atrasado {
    background-color: var(--status-atrasado);
    color: white;
}

.time-estimate {
    display: flex;
    align-items: center;
}

.time-estimate i {
    margin-right: 10px;
    color: var(--shell-yellow);
}

.print-button {
    width: 100%;
    background-color: var(--shell-dark);
    color: white;
    border: 1px solid var(--shell-yellow);
    border-radius: 5px;
    padding: 8px 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.print-button i {
    margin-right: 5px;
}

.print-button:hover {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.action-button {
    padding: 8px 15px;
    border: none;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.approve-button {
    background-color: var(--status-approved);
    color: white;
}

.reject-button {
    background-color: var(--status-rejected);
    color: white;
}

/* Cards de Categorias */
.order-details-section {
    margin-top: 30px;
}

.details-section-title {
    color: var(--shell-yellow);
    font-size: 1.4rem;
    margin-bottom: 20px;
    text-align: center;
    font-weight: 600;
    position: relative;
}

.details-section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 2px;
    background-color: var(--shell-yellow);
}

.order-cards {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 20px;
}

.order-card {
    flex: 1;
    min-width: 200px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    border: 1px solid var(--shell-border);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.order-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    border-color: var(--shell-yellow);
}

.card-icon {
    width: 50px;
    height: 50px;
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.card-title {
    color: var(--shell-yellow);
    font-weight: 600;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.card-content {
    width: 100%;
}

.cost-total {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--shell-yellow);
    margin-bottom: 10px;
}

.clickable-text {
    color: #aaa;
    font-size: 0.9rem;
    cursor: pointer;
    margin-top: 10px;
    transition: all 0.3s ease;
}

.clickable-text:hover {
    color: var(--shell-yellow);
}

.interaction-prompt {
    color: #aaa;
    margin-bottom: 5px;
}

.schedule-date {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.schedule-date i {
    margin-right: 8px;
    font-size: 0.7rem;
}

.schedule-date i.success {
    color: var(--circle-success);
}

.schedule-date i.pending {
    color: var(--circle-pending);
}

/* Estilos para badges de status */
.status-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    width: 100%;
    justify-content: center;
}

.status-badge i {
    margin-right: 5px;
}

.status-pending { background-color: var(--status-pending); color: black; }
.status-in_progress { background-color: var(--status-in-progress); color: white; }
.status-pending_approval { background-color: var(--status-pending-approval); color: white; }
.status-revision_needed { background-color: var(--status-revision-needed); color: black; }
.status-approved { background-color: var(--status-approved); color: white; }
.status-invoiced { background-color: var(--status-invoiced); color: white; }
.status-paid { background-color: var(--status-paid); color: white; }
.status-cancelled { background-color: var(--status-cancelled); color: white; }
.status-rejected { background-color: var(--status-rejected); color: white; }

/* Estilos para badges de prioridade */
.priority-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    margin-left: 10px;
}

.priority-badge i {
    margin-right: 5px;
}

.priority-low { background-color: var(--priority-low); color: white; }
.priority-medium { background-color: var(--priority-medium); color: white; }
.priority-high { background-color: var(--priority-high); color: black; }
.priority-urgent { background-color: var(--priority-urgent); color: white; }

/* Estilos para a seção de ações */
.actions-section {
    background: rgba(30, 30, 30, 0.7);
    border-radius: 10px;
    border: 1px solid var(--shell-border);
    padding: 20px;
    margin-top: 30px;
    margin-bottom: 30px;
}

.actions-title {
    color: var(--shell-yellow);
    font-weight: 600;
    margin-bottom: 20px;
    font-size: 1.3rem;
    text-align: center;
}

/* Estilos para botões */
.shell-btn {
    background-color: #2c2c2c;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    transition: all 0.3s ease;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    text-decoration: none;
}

.shell-btn i {
    margin-right: 8px;
}

.shell-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    color: white;
    text-decoration: none;
}

.shell-btn-red {
    background-color: var(--shell-red);
}

.shell-btn-red:hover {
    background-color: #ff3c43;
}

.shell-btn-yellow {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
}

.shell-btn-yellow:hover {
    background-color: #ffc234;
    color: var(--shell-dark);
}

.shell-btn-green {
    background-color: #198754;
}

.shell-btn-green:hover {
    background-color: #27ae60;
}

.shell-btn-grey {
    background-color: #6c757d;
}

.shell-btn-grey:hover {
    background-color: #5a6268;
}

/* Estilos para o card de custos */
.cost-item {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid var(--shell-light-gray);
}

.cost-item-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.cost-item-title {
    font-weight: 600;
    color: var(--shell-yellow);
}

.cost-item-price {
    font-weight: 600;
}

.cost-item-details {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #ccc;
}

/* Estilos para o card de interações */
.interaction-item {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid var(--shell-light-gray);
}

.interaction-message {
    margin-bottom: 5px;
}

.interaction-meta {
    font-size: 0.8rem;
    color: #aaa;
    display: flex;
    align-items: center;
}

.interaction-meta i {
    margin-right: 5px;
    color: var(--shell-yellow);
}

/* Estilos para modais */
.modal-content {
    background-color: var(--shell-dark);
    color: white;
    border: 1px solid var(--shell-border);
}

.modal-header {
    border-bottom: 1px solid var(--shell-light-gray);
}

.modal-footer {
    border-top: 1px solid var(--shell-light-gray);
}

.modal-title {
    color: var(--shell-yellow);
}

.form-control {
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid var(--shell-light-gray);
    color: white;
}

.form-control:focus {
    background-color: rgba(0, 0, 0, 0.3);
    color: white;
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 0.25rem rgba(253, 184, 19, 0.25);
}

.form-label {
    color: #ccc;
}

/* Estilos para toasts */
.toast {
    background-color: var(--shell-dark);
    color: white;
    border: 1px solid var(--shell-border);
}

.toast-header {
    background-color: rgba(0, 0, 0, 0.2);
    color: white;
    border-bottom: 1px solid var(--shell-light-gray);
}

/* Responsividade */
@media (max-width: 992px) {
    .order-main-content {
        flex-direction: column;
    }

    .maintenance-title-section {
        margin-right: 0;
        margin-bottom: 20px;
    }

    .order-card {
        min-width: calc(50% - 20px);
    }
}

@media (max-width: 768px) {
    .order-title {
        font-size: 1.5rem;
    }

    .card-title {
        font-size: 1.1rem;
    }

    .order-card {
        min-width: 100%;
    }

    .action-buttons {
        flex-direction: column;
    }

    .shell-btn {
        width: 100%;
        justify-content: center;
        margin-bottom: 10px;
    }
}

/* Estilos para impressão */
@media print {
    .back-button, .card-footer, .actions-section, .shell-btn, .clickable-text {
        display: none !important;
    }

    body {
        background-color: white !important;
        color: black !important;
    }

    .order-container, .order-main-content, .maintenance-title-section, .order-sidebar, .order-card {
        background: white !important;
        border: 1px solid #ddd !important;
        box-shadow: none !important;
        color: black !important;
    }

    .order-title, .maintenance-title, .sidebar-title, .card-title, .details-section-title {
        color: black !important;
    }

    .card-icon, .meta-value i, .responsible-info i, .time-estimate i {
        color: black !important;
        background-color: #eee !important;
    }
}
