:root {
    --shell-red: #ED1C24;
    --shell-yellow: #f4b31d; /* <PERSON><PERSON><PERSON> o amarelo específico da sidebar original */
    --sidebar-width: 260px;
    --sidebar-width-collapsed: 70px;
    --sidebar-bg: #1a1a1a;
    --sidebar-border: #333;
    --dark-gray: #1a1a1a; /* Pode ser redundante se já definido globalmente */
    --medium-gray: #2a2a2a; /* Pode ser redundante se já definido globalmente */
    --light-gray: #444; /* Pode ser redundante se já definido globalmente */
    --sidebar-transition: all 0.3s ease;
}

/* Sidebar principal */
.tradicio-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: var(--sidebar-width);
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--sidebar-border);
    color: #fff;
    z-index: 1000;
    transition: var(--sidebar-transition);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
}

.tradicio-sidebar.collapsed {
    width: var(--sidebar-width-collapsed);
}

/* Header do sidebar */
.sidebar-header {
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 2px solid var(--shell-yellow); /* Amarelo específico */
    background: linear-gradient(90deg, rgba(15,15,15,1) 0%, rgba(40,40,40,1) 100%);
}

.sidebar-logo {
    display: flex;
    align-items: center;
}

.logo-img {
    width: 40px;
    height: 40px;
    object-fit: contain;
    margin-right: 10px;
}

.logo-text {
    font-weight: bold;
    font-size: 18px;
    color: var(--shell-yellow); /* Amarelo específico */
    white-space: nowrap;
    overflow: hidden;
    transition: var(--sidebar-transition);
}

.sidebar-toggle {
    background: transparent;
    border: none;
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: var(--sidebar-transition);
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--shell-yellow); /* Amarelo específico */
}

/* Área do perfil */
.sidebar-profile {
    padding: 20px 15px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--sidebar-border);
    background: linear-gradient(to right, rgba(0, 0, 0, 0.3), rgba(40, 40, 40, 0.2));
    position: relative;
    overflow: hidden;
}

.sidebar-profile::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, var(--shell-yellow), transparent);
    opacity: 0.5;
}

.profile-avatar {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: var(--shell-red);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
    font-weight: 700;
    font-size: 18px;
    box-shadow: 0 0 15px rgba(237, 28, 36, 0.6);
    border: 2px solid rgba(255, 255, 255, 0.1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.profile-avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(237, 28, 36, 0.8);
}

.profile-info {
    white-space: nowrap;
    overflow: hidden;
    transition: var(--sidebar-transition);
    padding-left: 2px;
}

.profile-name {
    font-weight: 600;
    font-size: 15px;
    letter-spacing: 0.3px;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 3px;
    font-family: 'Rajdhani', sans-serif;
}

.profile-role {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 400;
    letter-spacing: 0.2px;
    text-transform: capitalize;
    font-family: 'Rajdhani', sans-serif;
}

/* Área de navegação */
.sidebar-nav {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    /* Barra de rolagem personalizada */
    scrollbar-width: thin;
    scrollbar-color: var(--light-gray) transparent; /* Usa var */
}

.sidebar-nav::-webkit-scrollbar {
    width: 6px;
}

.sidebar-nav::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
    background-color: var(--light-gray); /* Usa var */
    border-radius: 6px;
    border: transparent;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin: 2px 0;
    position: relative; /* Para tooltip */
}

/* Estilo para item ativo - Gradiente amarelo */
.nav-item.active {
    background: linear-gradient(90deg, var(--shell-yellow) 0%, var(--shell-yellow) 4px, transparent 40%); /* Linha amarela mais visível */
}

.nav-item.active .nav-link {
    color: var(--shell-yellow); /* Amarelo específico */
    font-weight: bold;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #fff;
    text-decoration: none;
    transition: var(--sidebar-transition);
    /* Removido border-radius para permitir gradiente */
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--shell-yellow); /* Amarelo específico */
}

.nav-icon {
    font-size: 18px;
    min-width: 30px; /* Largura mínima do ícone */
    text-align: center;
    margin-right: 10px;
    transition: var(--sidebar-transition);
}

.nav-text {
    transition: var(--sidebar-transition);
    white-space: nowrap;
    opacity: 1;
    visibility: visible;
}

/* Divisor de seções */
.sidebar-divider {
    height: 1px;
    background: var(--sidebar-border);
    margin: 10px 15px;
    opacity: 0.5;
}

/* Rodapé do sidebar */
.sidebar-footer {
    padding: 15px;
    border-top: 1px solid var(--sidebar-border);
    background: rgba(0, 0, 0, 0.2);
}

.logout-btn {
    display: flex;
    align-items: center;
    justify-content: center; /* Centraliza quando colapsado */
    color: #fff;
    text-decoration: none;
    padding: 10px;
    border-radius: 5px;
    transition: var(--sidebar-transition);
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--shell-red); /* Usa var global */
}

.logout-btn i {
    margin-right: 10px;
    transition: var(--sidebar-transition);
}

.logout-btn span {
    transition: var(--sidebar-transition);
    white-space: nowrap;
    opacity: 1;
    visibility: visible;
}

/* Comportamento quando colapsado */
.tradicio-sidebar.collapsed .logo-text,
.tradicio-sidebar.collapsed .profile-info,
.tradicio-sidebar.collapsed .nav-text,
.tradicio-sidebar.collapsed .logout-btn span {
    opacity: 0;
    visibility: hidden;
    width: 0; /* Ajuda a evitar overflow */
}

.tradicio-sidebar.collapsed .nav-link {
    padding: 12px;
    justify-content: center;
}

.tradicio-sidebar.collapsed .nav-icon {
    margin-right: 0;
    font-size: 20px;
}

.tradicio-sidebar.collapsed .sidebar-profile {
    justify-content: center;
    padding: 15px 0; /* Ajusta padding */
}

.tradicio-sidebar.collapsed .profile-avatar {
    margin-right: 0;
    width: 48px;
    height: 48px;
    font-size: 20px;
    transition: all 0.3s ease;
}

.tradicio-sidebar.collapsed .logout-btn i {
    margin-right: 0;
}

/* Tooltip ao passar o mouse quando colapsado */
.tradicio-sidebar.collapsed .nav-item,
.tradicio-sidebar.collapsed .sidebar-profile {
    position: relative; /* Garante que o item seja a referência */
}

.tradicio-sidebar.collapsed .profile-avatar::after {
    content: attr(data-tooltip);
    position: absolute;
    left: calc(100% + 10px);
    top: 50%;
    transform: translateY(-50%);
    background: var(--medium-gray);
    color: #fff;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 14px;
    white-space: nowrap;
    z-index: 1001;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    border-left: 3px solid var(--shell-yellow);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    pointer-events: none;
}

.tradicio-sidebar.collapsed .profile-avatar:hover::after {
    opacity: 1;
    visibility: visible;
}

.tradicio-sidebar.collapsed .nav-item .nav-link::after {
    content: attr(data-tooltip); /* Pega o texto do atributo */
    position: absolute;
    left: calc(100% + 10px); /* Posição à direita do ícone */
    top: 50%;
    transform: translateY(-50%);
    background: var(--medium-gray); /* Usa var */
    color: #fff;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 14px;
    white-space: nowrap;
    z-index: 1001; /* Acima da sidebar */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    border-left: 3px solid var(--shell-yellow); /* Amarelo específico */
    opacity: 0; /* Escondido por padrão */
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    pointer-events: none; /* Não interfere no mouse */
}

.tradicio-sidebar.collapsed .nav-item:hover .nav-link::after {
    opacity: 1; /* Mostra no hover */
    visibility: visible;
}


/* Ajuste principal para o conteúdo - Deve estar no layout base ou CSS global */
/* Esta parte pode precisar ser ajustada dependendo de como o JS controla a classe */
.content-with-sidebar {
    margin-left: var(--sidebar-width);
    transition: var(--sidebar-transition);
    /* min-height: 100vh; */ /* Melhor controlar no body ou wrapper principal */
    width: calc(100% - var(--sidebar-width));
    padding-top: 0; /* Evita espaçamento duplo se o header estiver fixo */
    position: relative; /* Para contexto de posicionamento */
}

/* Classe aplicada ao wrapper do conteúdo quando sidebar está colapsada */
.content-with-sidebar.collapsed {
    margin-left: var(--sidebar-width-collapsed);
    width: calc(100% - var(--sidebar-width-collapsed));
}

/* Responsividade Mobile */
@media (max-width: 768px) {
    .tradicio-sidebar {
        width: var(--sidebar-width); /* Começa com largura total */
        transform: translateX(-100%); /* Escondido fora da tela */
        z-index: 1030; /* Acima do conteúdo mas abaixo de modais talvez */
    }

    .tradicio-sidebar.mobile-open {
        transform: translateX(0); /* Aparece na tela */
    }

    .content-with-sidebar,
    .content-with-sidebar.collapsed { /* Ignora colapso no mobile, sempre ocupa tudo */
        margin-left: 0;
        width: 100%;
    }

    /* O botão toggle pode precisar de ajustes no header principal */
    /* .sidebar-toggle { display: block; } */

    /* Overlay para fechar o menu no mobile */
    .sidebar-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1029; /* Abaixo da sidebar */
    }

    .sidebar-overlay.visible {
        display: block;
    }
}