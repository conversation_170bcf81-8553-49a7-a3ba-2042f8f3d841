/* Estilos para a nova página de ordens de serviço */

:root {
    --shell-red: #ED1C24;
    --shell-yellow: #FDB813;
    --shell-dark: #1E1E1E;
    --shell-darker: #121212;
    --shell-light: #f8f9fa;
    --shell-gray: #6c757d;
    --shell-border: #343a40;

    --status-pending: #ffc107;
    --status-progress: #0d6efd;
    --status-completed: #198754;
    --status-cancelled: #dc3545;

    --priority-low: #198754;
    --priority-medium: #0dcaf0;
    --priority-high: #ffc107;
    --priority-urgent: #dc3545;
}

/* ===== Estilos Gerais ===== */
.page-title {
    color: var(--shell-yellow);
    font-size: 1.8rem;
    font-weight: 600;
    font-family: 'Rajdhani', sans-serif;
}

.shell-divider-animated {
    height: 3px;
    background: linear-gradient(90deg, var(--shell-yellow) 0%, rgba(253, 184, 19, 0.1) 100%);
    margin: 1rem 0;
    border-radius: 3px;
    position: relative;
    overflow: hidden;
}

.shell-divider-animated::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    100% {
        left: 100%;
    }
}

/* ===== Efeitos de Vidro e Neon ===== */
.glass-effect {
    background: rgba(30, 30, 30, 0.7) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.glass-card {
    background: rgba(30, 30, 30, 0.7) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.glass-card:hover {
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.3);
    transform: translateY(-5px);
}

.btn-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--shell-light);
    transition: all 0.3s ease;
}

.btn-glass:hover, .btn-glass.active {
    background: rgba(253, 184, 19, 0.2);
    border-color: var(--shell-yellow);
    color: var(--shell-yellow);
    box-shadow: 0 0 15px rgba(253, 184, 19, 0.3);
}

.btn-neon {
    background: rgba(30, 30, 30, 0.7);
    border: 1px solid var(--shell-yellow);
    color: var(--shell-yellow);
    position: relative;
    overflow: hidden;
    z-index: 1;
    transition: all 0.3s ease;
}

.btn-neon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(253, 184, 19, 0.2), transparent);
    transition: all 0.3s ease;
    z-index: -1;
}

.btn-neon:hover::before {
    left: 100%;
    transition: 0.5s;
}

.btn-neon:hover {
    box-shadow: 0 0 15px rgba(253, 184, 19, 0.5);
    color: var(--shell-yellow);
}

.pulse-btn {
    position: relative;
}

.pulse-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: inherit;
    box-shadow: 0 0 0 0 rgba(253, 184, 19, 0.7);
    animation: pulse-animation 2s infinite;
}

@keyframes pulse-animation {
    0% {
        box-shadow: 0 0 0 0 rgba(253, 184, 19, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(253, 184, 19, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(253, 184, 19, 0);
    }
}

/* ===== Header e Ícones ===== */
.page-header-wrapper {
    margin-bottom: 1.5rem;
}

.header-icon-container {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: rgba(253, 184, 19, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: var(--shell-yellow);
}

.header-icon-bubble {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(253, 184, 19, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--shell-yellow);
}

.modal-icon-container {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: rgba(253, 184, 19, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: var(--shell-yellow);
}

.modal-icon-container.danger {
    background: rgba(220, 53, 69, 0.15);
    color: var(--status-cancelled);
}

.bx-tada-hover:hover {
    animation: tada 1s ease infinite;
}

.bx-spin-hover:hover {
    animation: spin 2s linear infinite;
}

.bx-flashing-hover:hover {
    animation: flashing 1.5s ease infinite;
}

@keyframes tada {
    0% {transform: scale(1);}
    10%, 20% {transform: scale(0.9) rotate(-3deg);}
    30%, 50%, 70%, 90% {transform: scale(1.1) rotate(3deg);}
    40%, 60%, 80% {transform: scale(1.1) rotate(-3deg);}
    100% {transform: scale(1) rotate(0);}
}

@keyframes spin {
    0% {transform: rotate(0deg);}
    100% {transform: rotate(360deg);}
}

@keyframes flashing {
    0%, 50%, 100% {opacity: 1;}
    25%, 75% {opacity: 0.5;}
}

/* ===== Métricas ===== */
.metrics-row {
    margin-top: 1.5rem;
}

.metric-card {
    background-color: rgba(30, 30, 30, 0.7);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    height: 100%;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, transparent 100%);
    pointer-events: none;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.3);
}

.metric-pending {
    border-left: 4px solid var(--status-pending);
}

.metric-progress {
    border-left: 4px solid var(--status-progress);
}

.metric-completed {
    border-left: 4px solid var(--status-completed);
}

.metric-total {
    border-left: 4px solid var(--shell-yellow);
}

.metric-icon-container {
    position: relative;
    width: 80px;
    height: 80px;
    flex-shrink: 0;
}

.metric-icon {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
}

.metric-pending .metric-icon {
    background-color: rgba(255, 193, 7, 0.15);
    color: var(--status-pending);
}

.metric-progress .metric-icon {
    background-color: rgba(13, 110, 253, 0.15);
    color: var(--status-progress);
}

.metric-completed .metric-icon {
    background-color: rgba(25, 135, 84, 0.15);
    color: var(--status-completed);
}

.metric-total .metric-icon {
    background-color: rgba(253, 184, 19, 0.15);
    color: var(--shell-yellow);
}

.metric-circle-progress {
    position: absolute;
    top: 0;
    left: 0;
    transform: rotate(-90deg);
}

.metric-circle-bg {
    fill: none;
    stroke: rgba(255, 255, 255, 0.05);
    stroke-width: 4;
}

.metric-circle-fill {
    fill: none;
    stroke-width: 4;
    stroke-linecap: round;
    stroke-dasharray: 226;
    stroke-dashoffset: 226;
    transition: stroke-dashoffset 1s ease;
}

.metric-pending-stroke {
    stroke: var(--status-pending);
}

.metric-progress-stroke {
    stroke: var(--status-progress);
}

.metric-completed-stroke {
    stroke: var(--status-completed);
}

.metric-total-stroke {
    stroke: var(--shell-yellow);
}

.metric-content {
    flex-grow: 1;
}

.metric-value-container {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    font-family: 'Share Tech Mono', monospace;
    color: var(--shell-light);
    line-height: 1;
}

.metric-percentage {
    font-size: 0.875rem;
    color: var(--shell-gray);
    font-family: 'Share Tech Mono', monospace;
}

.metric-label {
    font-size: 0.875rem;
    color: var(--shell-gray);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.metric-trend {
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.metric-trend i {
    font-size: 0.875rem;
}

/* Estilos para a tabela */
.table-header-shell {
    background-color: var(--shell-darker);
    color: var(--shell-yellow);
    border-bottom: 2px solid var(--shell-yellow);
}

.table-header-shell th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    padding: 0.75rem 1rem;
}

.table-dark {
    background-color: var(--shell-dark);
    color: var(--shell-light);
    border-color: var(--shell-border);
}

.table-dark tbody tr {
    border-bottom: 1px solid var(--shell-border);
    transition: background-color 0.2s ease;
}

.table-dark tbody tr:hover {
    background-color: rgba(253, 184, 19, 0.05);
}

.order-id {
    font-family: 'Share Tech Mono', monospace;
    font-weight: 600;
    color: var(--shell-yellow);
}

.order-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(253, 184, 19, 0.1);
    color: var(--shell-yellow);
}

.order-title {
    font-weight: 500;
    color: var(--shell-light);
}

/* Estilos para badges de status e prioridade */
.status-badge {
    display: inline-block;
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
}

.status-pending {
    background-color: rgba(255, 193, 7, 0.2);
    color: var(--status-pending);
    border: 1px solid var(--status-pending);
}

.status-progress {
    background-color: rgba(13, 110, 253, 0.2);
    color: var(--status-progress);
    border: 1px solid var(--status-progress);
}

.status-completed {
    background-color: rgba(25, 135, 84, 0.2);
    color: var(--status-completed);
    border: 1px solid var(--status-completed);
}

.status-cancelled {
    background-color: rgba(220, 53, 69, 0.2);
    color: var(--status-cancelled);
    border: 1px solid var(--status-cancelled);
}

.priority-badge {
    display: inline-block;
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
}

.priority-baixa {
    background-color: rgba(25, 135, 84, 0.2);
    color: var(--priority-low);
    border: 1px solid var(--priority-low);
}

.priority-media {
    background-color: rgba(13, 202, 240, 0.2);
    color: var(--priority-medium);
    border: 1px solid var(--priority-medium);
}

.priority-alta {
    background-color: rgba(255, 193, 7, 0.2);
    color: var(--priority-high);
    border: 1px solid var(--priority-high);
}

.priority-urgente {
    background-color: rgba(220, 53, 69, 0.2);
    color: var(--priority-urgent);
    border: 1px solid var(--priority-urgent);
}

/* Estilos para os cards de ordens */
.order-card {
    background-color: var(--shell-darker);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    border-top: 3px solid transparent;
}

.order-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.order-card.priority-baixa {
    border-top-color: var(--priority-low);
}

.order-card.priority-media {
    border-top-color: var(--priority-medium);
}

.order-card.priority-alta {
    border-top-color: var(--priority-high);
}

.order-card.priority-urgente {
    border-top-color: var(--priority-urgent);
}

.order-card-header {
    padding: 1rem;
    background-color: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid var(--shell-border);
}

.order-card-body {
    padding: 1rem;
}

.order-card-footer {
    padding: 1rem;
    background-color: rgba(0, 0, 0, 0.1);
    border-top: 1px solid var(--shell-border);
    display: flex;
    justify-content: space-between;
    gap: 0.5rem;
}

.order-info {
    margin-top: 1rem;
}

.info-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    color: var(--shell-gray);
    font-size: 0.875rem;
}

.info-item i {
    width: 20px;
    text-align: center;
}

.priority-text {
    font-weight: 600;
}

.priority-text.priority-baixa {
    color: var(--priority-low);
}

.priority-text.priority-media {
    color: var(--priority-medium);
}

.priority-text.priority-alta {
    color: var(--priority-high);
}

.priority-text.priority-urgente {
    color: var(--priority-urgent);
}

/* Estilos para paginação */
.pagination .page-link {
    background-color: var(--shell-darker);
    border-color: var(--shell-border);
    color: var(--shell-light);
}

.pagination .page-item.active .page-link {
    background-color: var(--shell-yellow);
    border-color: var(--shell-yellow);
    color: var(--shell-dark);
}

.pagination .page-item.disabled .page-link {
    background-color: var(--shell-darker);
    border-color: var(--shell-border);
    color: var(--shell-gray);
}

/* Estilos para o estado vazio */
.empty-state {
    padding: 2rem;
    text-align: center;
}

/* Estilos para botões de visualização */
.btn-group .btn-outline-light.active {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    border-color: var(--shell-yellow);
}

/* Estilos para o modal de detalhes */
.modal-content {
    border: none;
    border-radius: 10px;
    overflow: hidden;
}

.modal-header {
    padding: 1rem 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
}

.delete-warning {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Responsividade */
@media (max-width: 992px) {
    .metric-card {
        margin-bottom: 1rem;
    }

    .order-card-footer {
        flex-direction: column;
    }

    .order-card-footer .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .page-actions {
        margin-top: 1rem;
        width: 100%;
        display: flex;
        justify-content: space-between;
    }
}
