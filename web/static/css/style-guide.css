/* <PERSON>uia de Estilo <PERSON> para o Site */

/* Cores */
:root {
    --primary-color: #FFCC00; /* <PERSON><PERSON> */
    --secondary-color: #1a1a1a; /* Cinza escuro */
    --accent-color: #dc3545; /* Vermel<PERSON> */
    --background-color: #222; /* Fundo escuro */
    --text-color: #fff; /* Texto branco */
    --shell-yellow: #FFCC00; /* Amarelo Shell */
}

/* Estilos de Botões */
.shell-btn {
    background-color: var(--primary-color);
    color: #000;
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.shell-btn:hover {
    background-color: #ffcd00;
    transform: translateY(-2px);
}

/* Estilos de Cartões */
.stats-card {
    background-color: var(--secondary-color);
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    color: #fff;
}

.stats-card-title {
    font-size: 1.2rem;
    font-weight: bold;
}

.stats-card-value {
    font-size: 2rem;
    font-weight: bold;
}

/* Estilos de Layout */
.dashboard-container {
    max-width: 1500px;
    margin: auto;
    padding: 20px;
}

.calendar-dashboard-container {
    display: flex;
    flex-direction: column;
    padding: 20px;
}

/* Estilos de Títulos */
.page-title {
    font-size: 2rem;
    color: var(--shell-yellow);
}

h4 {
    color: var(--shell-yellow);
}

/* Estilos de Responsividade */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 10px;
    }
    
    .calendar-dashboard-container {
        padding: 10px;
    }
}
