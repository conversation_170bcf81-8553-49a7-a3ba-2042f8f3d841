/* Correções específicas para o dashboard */

/* Corrigir problemas de layout */
html, body {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* Garantir que o conteúdo seja exibido corretamente */
.content-with-sidebar {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.main-content {
    flex: 1;
    overflow-y: auto;
}

/* Corrigir problemas de compatibilidade */
.service-order-item .order-status.in_progress {
    background-color: rgba(13, 110, 253, 0.2);
    color: #0d6efd;
    border: 1px solid #0d6efd;
}

/* Garantir que os cards de métricas tenham altura consistente */
.stats-card {
    height: 100%;
    min-height: 120px;
}

/* Melhorar a responsividade */
@media (max-width: 768px) {
    .service-orders-grid {
        grid-template-columns: 1fr;
    }
    
    .row > [class*="col-"] {
        margin-bottom: 15px;
    }
}
