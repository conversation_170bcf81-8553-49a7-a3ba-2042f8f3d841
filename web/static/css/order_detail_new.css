/* Estilos para a página de detalhes da ordem - Versão Moderna */
:root {
    --shell-red: #ED1C24;
    --shell-yellow: #FDB813;
    --shell-dark: #1E1E1E;
    --shell-darker: #121212;
    --shell-light-gray: rgba(255, 255, 255, 0.1);
    --shell-border: rgba(255, 255, 255, 0.15);
}

body {
    background-color: var(--shell-darker);
    color: #fff;
    font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Estilos para o cabeçalho da página */
.order-detail-header {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.7), rgba(30, 30, 30, 0.7));
    border-radius: 10px;
    border: 1px solid var(--shell-border);
    padding: 20px;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.order-detail-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(to right, var(--shell-red), var(--shell-yellow));
}

.order-detail-title {
    color: var(--shell-yellow);
    font-weight: 700;
    margin-bottom: 15px;
    font-size: 2rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.back-button {
    background-color: var(--shell-red);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    margin-right: 15px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    font-weight: 600;
}

.back-button:hover {
    background-color: #ff3c43;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    color: white;
}

/* Estilos para os cards */
.detail-card {
    background: rgba(30, 30, 30, 0.7);
    border-radius: 10px;
    border: 1px solid var(--shell-border);
    padding: 20px;
    height: 100%;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.detail-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(to right, transparent, var(--shell-yellow), transparent);
    opacity: 0.7;
}

.detail-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid var(--shell-light-gray);
    padding-bottom: 15px;
}

.card-icon {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.2rem;
}

.card-title {
    color: var(--shell-yellow);
    font-weight: 600;
    margin: 0;
    font-size: 1.3rem;
}

.card-content {
    flex-grow: 1;
    margin-bottom: 15px;
}

.card-footer {
    border-top: 1px solid var(--shell-light-gray);
    padding-top: 15px;
    display: flex;
    justify-content: flex-end;
}

/* Estilos para badges de status */
.status-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
}

.status-badge i {
    margin-right: 5px;
}

.status-pending { background-color: #6c757d; color: white; }
.status-rejected_provider { background-color: #ffc107; color: black; }
.status-in_progress { background-color: #0d6efd; color: white; }
.status-pending_approval { background-color: #17a2b8; color: white; }
.status-revision_needed { background-color: #ffc107; color: black; }
.status-approved { background-color: #198754; color: white; }
.status-invoiced { background-color: #6f42c1; color: white; }
.status-paid { background-color: #198754; color: white; }
.status-cancelled { background-color: #dc3545; color: white; }

/* Estilos para badges de prioridade */
.priority-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    margin-left: 10px;
}

.priority-badge i {
    margin-right: 5px;
}

.priority-low { background-color: #198754; color: white; }
.priority-medium { background-color: #ffc107; color: black; }
.priority-high { background-color: #dc3545; color: white; }

/* Estilos para a seção de ações */
.actions-section {
    background: rgba(30, 30, 30, 0.7);
    border-radius: 10px;
    border: 1px solid var(--shell-border);
    padding: 20px;
    margin-top: 30px;
    margin-bottom: 30px;
}

.actions-title {
    color: var(--shell-yellow);
    font-weight: 600;
    margin-bottom: 20px;
    font-size: 1.3rem;
    text-align: center;
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

/* Estilos para botões */
.shell-btn {
    background-color: #2c2c2c;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    transition: all 0.3s ease;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
}

.shell-btn i {
    margin-right: 8px;
}

.shell-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.shell-btn-red {
    background-color: var(--shell-red);
}

.shell-btn-red:hover {
    background-color: #ff3c43;
}

.shell-btn-yellow {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
}

.shell-btn-yellow:hover {
    background-color: #ffc234;
}

.shell-btn-green {
    background-color: #198754;
}

.shell-btn-green:hover {
    background-color: #21af6e;
}

.shell-btn-grey {
    background-color: #6c757d;
}

.shell-btn-grey:hover {
    background-color: #7d8790;
}

/* Estilos para listas */
.info-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.info-list li {
    padding: 8px 0;
    border-bottom: 1px solid var(--shell-light-gray);
    display: flex;
    align-items: center;
}

.info-list li:last-child {
    border-bottom: none;
}

.info-list li i {
    margin-right: 10px;
    color: var(--shell-yellow);
    width: 20px;
    text-align: center;
}

.info-label {
    font-weight: 600;
    margin-right: 10px;
    color: #aaa;
}

/* Estilos para timeline */
.timeline {
    list-style: none;
    padding: 0;
    position: relative;
}

.timeline:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 10px;
    width: 2px;
    background: var(--shell-light-gray);
}

.timeline li {
    position: relative;
    padding-left: 30px;
    padding-bottom: 15px;
}

.timeline li:last-child {
    padding-bottom: 0;
}

.timeline li i {
    position: absolute;
    left: 0;
    top: 0;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    border-radius: 50%;
    background: var(--shell-dark);
    color: var(--shell-yellow);
    z-index: 1;
}

.timeline-date {
    font-size: 0.85rem;
    color: #aaa;
    display: block;
}

/* Estilos para interações */
.interaction-item {
    padding: 10px;
    border-radius: 5px;
    background: rgba(0, 0, 0, 0.2);
    margin-bottom: 10px;
}

.interaction-message {
    margin-bottom: 5px;
}

.interaction-meta {
    font-size: 0.8rem;
    color: #aaa;
    display: flex;
    align-items: center;
}

.interaction-meta i {
    margin-right: 5px;
    color: var(--shell-yellow);
}

/* Responsividade */
@media (max-width: 768px) {
    .order-detail-title {
        font-size: 1.5rem;
    }
    
    .card-title {
        font-size: 1.1rem;
    }
    
    .detail-card {
        margin-bottom: 20px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .shell-btn {
        width: 100%;
        justify-content: center;
        margin-bottom: 10px;
    }
}
