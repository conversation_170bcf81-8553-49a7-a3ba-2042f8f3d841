/* Variáveis do Design System Shell */
:root {
    --shell-red: #ED1C24;
    --shell-yellow: #FDB813;
    --shell-dark: #333333;
    --shell-light: #f8f9fa;
    --shell-gray: #808080;
    --shell-success: #28a745;
    --shell-info: #17a2b8;
    --shell-warning: #ffc107;
    --shell-danger: #dc3545;
}

/* Layout da calculadora */
.calculadora-container {
    display: grid;
    grid-template-columns: 300px 1fr 350px;
    grid-gap: 20px;
    min-height: 700px;
}

@media (max-width: 1200px) {
    .calculadora-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
    }
}

/* Painel de configuração */
.config-panel {
    background-color: var(--shell-dark);
    border: 1px solid var(--shell-gray);
    border-radius: 8px;
    padding: 20px;
    height: 100%;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--shell-gray);
}

.panel-header h3 {
    color: var(--shell-yellow);
    margin: 0;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
}

/* Componentes disponíveis */
.componentes-disponiveis {
    margin-bottom: 30px;
}

.componentes-disponiveis h4 {
    color: var(--shell-light);
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.componentes-lista {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.componente-item {
    background-color: #444444;
    border: 1px solid var(--shell-gray);
    border-radius: 4px;
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: grab;
    transition: all 0.2s ease;
    color: var(--shell-light);
}

.componente-item:hover {
    background-color: #555555;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.componente-item i {
    color: var(--shell-yellow);
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
}

.componente-item span {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 500;
}

/* Configurações salvas */
.configuracoes-salvas h4 {
    color: var(--shell-light);
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.configs-lista {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.config-item {
    background-color: #444444;
    border: 1px solid var(--shell-gray);
    border-radius: 4px;
    padding: 10px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.2s ease;
    color: var(--shell-light);
}

.config-item:hover {
    background-color: #555555;
}

.config-item span {
    font-family: 'Rajdhani', sans-serif;
}

.config-actions {
    display: flex;
    gap: 5px;
}

.btn-icon {
    background-color: transparent;
    border: none;
    color: var(--shell-gray);
    font-size: 0.9rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    color: var(--shell-yellow);
    background-color: rgba(255, 255, 255, 0.1);
}

/* Calculadora principal */
.calculadora-principal {
    background-color: var(--shell-dark);
    border: 1px solid var(--shell-gray);
    border-radius: 8px;
    padding: 20px;
    height: 100%;
}

.calculadora-header {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.calculadora-header > div {
    flex: 1;
}

.calculadora-header label {
    color: var(--shell-yellow);
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
}

.form-select, .form-control {
    background-color: #444444;
    border: 1px solid var(--shell-gray);
    color: var(--shell-light);
    font-family: 'Share Tech Mono', monospace;
    padding: 10px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.form-select:focus, .form-control:focus {
    background-color: #4a4a4a;
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 0.2rem rgba(253, 184, 19, 0.25);
    color: var(--shell-light);
}

.form-select option {
    background-color: #444444;
    color: var(--shell-light);
    padding: 10px;
}

/* Valores principais */
.valores-principais {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.valor-item {
    flex: 1;
}

.valor-item label {
    color: var(--shell-yellow);
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
}

/* Componentes adicionados */
.componentes-adicionados {
    margin-bottom: 30px;
}

.componentes-adicionados h4 {
    color: var(--shell-light);
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.instrucao {
    color: var(--shell-gray);
    font-style: italic;
    margin-bottom: 15px;
}

.drop-zone {
    min-height: 200px;
    border: 2px dashed var(--shell-gray);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    background-color: rgba(255, 255, 255, 0.05);
}

.drop-zone.drag-over {
    border-color: var(--shell-yellow);
    background-color: rgba(253, 184, 19, 0.1);
}

.componente-container {
    background-color: #444444;
    border: 1px solid var(--shell-gray);
    border-radius: 6px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.componente-container:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.componente-header {
    background-color: #333333;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--shell-gray);
}

.componente-titulo {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--shell-light);
}

.componente-titulo i {
    color: var(--shell-yellow);
}

.componente-titulo span {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
}

.componente-acoes {
    display: flex;
    gap: 5px;
}

.btn-componente {
    background-color: transparent;
    border: none;
    color: var(--shell-gray);
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.btn-editar:hover {
    color: var(--shell-yellow);
    background-color: rgba(255, 255, 255, 0.1);
}

.btn-remover:hover {
    color: var(--shell-danger);
    background-color: rgba(255, 255, 255, 0.1);
}

.componente-body {
    padding: 15px;
}

.campo-container {
    margin-bottom: 15px;
}

.campo-container label {
    color: var(--shell-light);
    font-family: 'Rajdhani', sans-serif;
    display: block;
    margin-bottom: 5px;
}

.campo-info {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

.info-label {
    color: var(--shell-gray);
    font-family: 'Share Tech Mono', monospace;
    font-size: 0.9rem;
}

.info-valor {
    color: var(--shell-yellow);
    font-family: 'Share Tech Mono', monospace;
    font-weight: 600;
}

/* Ações da calculadora */
.acoes-calculadora {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    margin-top: 20px;
}

.acoes-calculadora button {
    flex: 1;
}

/* Estilos dos botões */
.shell-btn {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.shell-btn i {
    font-size: 1rem;
}

.shell-btn-red {
    background-color: var(--shell-red);
    color: var(--shell-light);
}

.shell-btn-red:hover {
    background-color: #d41920;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(237, 28, 36, 0.3);
}

.shell-btn-yellow {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
}

.shell-btn-yellow:hover {
    background-color: #ffc107;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(253, 184, 19, 0.3);
}

.shell-btn-grey {
    background-color: var(--shell-gray);
    color: var(--shell-light);
}

.shell-btn-grey:hover {
    background-color: #666666;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(128, 128, 128, 0.3);
}

/* Resultados */
.resultados-container {
    background-color: var(--shell-dark);
    border: 1px solid var(--shell-gray);
    border-radius: 8px;
    padding: 20px;
    height: 100%;
}

.resultados-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--shell-gray);
}

.resultados-header h3 {
    color: var(--shell-yellow);
    margin: 0;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
}

.acoes-resultados {
    display: flex;
    gap: 10px;
}

/* Cards de resultado */
.resultados-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 30px;
}

.resultado-card {
    background-color: #444444;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.resultado-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.margem-bruta {
    border-left: 4px solid var(--shell-success);
}

.custos-totais {
    border-left: 4px solid var(--shell-warning);
}

.margem-liquida {
    border-left: 4px solid var(--shell-red);
}

.resultado-icon {
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--shell-yellow);
    font-size: 1.2rem;
}

.resultado-info {
    flex: 1;
}

.resultado-valor {
    font-family: 'Share Tech Mono', monospace;
    font-size: 1.6rem;
    font-weight: 600;
    color: var(--shell-light);
    margin-bottom: 5px;
}

.resultado-label {
    font-family: 'Rajdhani', sans-serif;
    color: var(--shell-gray);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.resultado-percentual {
    font-family: 'Share Tech Mono', monospace;
    font-size: 1rem;
    font-weight: 600;
    color: var(--shell-yellow);
}

/* Tabela de detalhes */
.resultados-detalhes {
    margin-bottom: 30px;
}

.resultados-detalhes h4 {
    color: var(--shell-light);
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.table-shell {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--shell-light);
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
}

.table-shell thead th {
    background-color: var(--shell-red);
    color: var(--shell-light);
    border: none;
    font-family: 'Share Tech Mono', monospace;
    padding: 12px 15px;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.9rem;
}

.table-shell tbody td {
    padding: 12px 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-family: 'Rajdhani', sans-serif;
}

.table-shell tbody tr {
    background-color: #444444;
    transition: all 0.2s ease;
}

.table-shell tbody tr:hover {
    background-color: #555555;
}

.table-shell tfoot {
    background-color: var(--shell-dark);
}

.table-shell tfoot th {
    padding: 12px 15px;
    font-family: 'Share Tech Mono', monospace;
    color: var(--shell-yellow);
    font-weight: 600;
    border-top: 2px solid var(--shell-yellow);
}

/* Gráfico */
.resultados-grafico {
    margin-bottom: 30px;
}

.resultados-grafico h4 {
    color: var(--shell-light);
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.grafico-container {
    background-color: #444444;
    border-radius: 8px;
    padding: 15px;
    height: 200px;
}

/* Projeção */
.resultados-projecao h4 {
    color: var(--shell-light);
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.projecao-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.projecao-item {
    background-color: #444444;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.2s ease;
}

.projecao-item:hover {
    background-color: #555555;
}

.item-label {
    font-family: 'Rajdhani', sans-serif;
    color: var(--shell-gray);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.item-valor {
    font-family: 'Share Tech Mono', monospace;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--shell-light);
}

.item-valor.destaque {
    color: var(--shell-yellow);
    font-size: 1.4rem;
}

/* Modal customizado */
.modal-content {
    background-color: var(--shell-dark);
    color: var(--shell-light);
    border: 1px solid var(--shell-gray);
}

.modal-header {
    border-bottom: 1px solid var(--shell-gray);
}

.modal-title {
    color: var(--shell-yellow);
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
}

.modal-footer {
    border-top: 1px solid var(--shell-gray);
}

.btn-close {
    color: var(--shell-light);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    color: var(--shell-light);
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
}

.form-text {
    color: var(--shell-gray);
    font-family: 'Share Tech Mono', monospace;
    font-size: 0.8rem;
    margin-top: 5px;
}

/* Animações */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 1s ease-in-out infinite;
}

/* Responsividade */
@media (max-width: 992px) {
    .calculadora-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .valores-principais {
        flex-direction: column;
        gap: 15px;
    }
    
    .resultados-cards {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .projecao-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
} 