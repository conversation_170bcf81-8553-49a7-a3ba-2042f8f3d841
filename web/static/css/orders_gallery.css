/*
 * Ordens de Serviço - Estilo Galeria - Rede Tradição
 * Estilos para a página de ordens de serviço no estilo galeria
 * Seguindo os padrões visuais do projeto
 */

/* Variáveis de cores */
:root {
    --shell-red: #ED1C24;
    --shell-yellow: #FDB813;
    --shell-dark-red: #A40000;
    --shell-light-yellow: #FFE566;
    --dark-bg: #222222;
    --medium-dark: #333333;
    --light-dark: #444444;
    --shell-white: #FFFFFF;
    --shell-light-gray: #CCCCCC;
    --shell-gray: #888888;
    
    /* Status colors */
    --status-pending: #FDB813;
    --status-in_progress: #3498db;
    --status-completed: #2ecc71;
    --status-cancelled: #e74c3c;
    
    /* Priority colors */
    --priority-low: #3498db;
    --priority-medium: #FDB813;
    --priority-high: #e74c3c;
    --priority-critical: #9b59b6;
}

/* Estilos para o cabeçalho da página */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem 1.5rem;
    border-bottom: 2px solid var(--shell-yellow);
    animation: fadeIn 0.5s ease;
    background-color: rgba(20, 20, 20, 0.7);
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    flex-wrap: wrap;
    gap: 1rem;
}

.page-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
    color: var(--shell-white);
    display: flex;
    align-items: center;
}

.page-actions {
    display: flex;
    gap: 0.5rem;
}

/* Responsividade para o cabeçalho */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        padding: 1rem;
    }

    .page-title {
        margin-bottom: 1rem;
    }

    .page-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

/* Estilos para a seção de filtros */
.filter-section .card-shell {
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    background-color: rgba(30, 30, 30, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    overflow: hidden;
    animation: fadeIn 0.5s ease;
}

.filter-section .card-shell:hover {
    box-shadow: 0 0 15px rgba(253, 184, 19, 0.3);
}

.card-shell .card-header {
    background-color: rgba(20, 20, 20, 0.7);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 1.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-shell .card-title {
    color: var(--shell-white);
    font-weight: 600;
    font-size: 1.2rem;
    margin: 0;
    display: inline-block;
}

.card-shell .card-body {
    padding: 1.25rem;
    background-color: rgba(25, 25, 25, 0.5);
}

.btn-shell-yellow {
    background-color: var(--shell-yellow);
    color: #222;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-shell-yellow:hover {
    background-color: #ffcc29;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(30, 30, 30, 0.7);
    color: var(--shell-white);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.btn-icon:hover {
    background-color: var(--shell-yellow);
    color: #222;
    transform: translateY(-2px);
}

.btn-icon.active {
    background-color: var(--shell-yellow);
    color: #222;
}

.card-header-actions {
    display: flex;
    gap: 0.5rem;
}

/* Estilos para formulários */
.form-label {
    color: var(--shell-white);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-select, .form-control {
    background-color: rgba(20, 20, 20, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--shell-white);
    border-radius: 5px;
    padding: 0.5rem 0.75rem;
    transition: all 0.3s ease;
}

.form-select:focus, .form-control:focus {
    background-color: rgba(30, 30, 30, 0.7);
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 0.25rem rgba(253, 184, 19, 0.25);
    color: var(--shell-white);
}

.input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

/* Estilos para métricas */
.metrics-section .card-shell {
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    background-color: rgba(30, 30, 30, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    overflow: hidden;
    animation: fadeIn 0.5s ease;
}

.stat-item {
    background: linear-gradient(145deg, #2a2a2a, #222222);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
}

.stat-item::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
}

.stat-item.pending::after {
    background: linear-gradient(to right, var(--shell-yellow), var(--shell-yellow));
}

.stat-item.in-progress::after {
    background: linear-gradient(to right, var(--status-in_progress), var(--status-in_progress));
}

.stat-item.completed::after {
    background: linear-gradient(to right, var(--status-completed), var(--status-completed));
}

.stat-item.total::after {
    background: linear-gradient(to right, var(--shell-red), var(--shell-yellow));
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
}

.stat-label {
    font-size: 1rem;
    color: var(--shell-white);
    margin-bottom: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    font-family: 'Share Tech Mono', monospace;
    text-shadow: 0 0 10px rgba(253, 184, 19, 0.3);
    line-height: 1;
}

.stat-item.pending .stat-value {
    color: var(--shell-yellow);
}

.stat-item.in-progress .stat-value {
    color: var(--status-in_progress);
}

.stat-item.completed .stat-value {
    color: var(--status-completed);
}

.stat-item.total .stat-value {
    color: var(--shell-yellow);
}

/* Estilos para a galeria de ordens */
.orders-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
    width: 100%;
    margin: 0;
    animation: fadeIn 0.5s ease;
}

.order-item {
    width: 100%;
    margin: 0;
    padding: 0;
}

.order-card {
    background: linear-gradient(145deg, #2a2a2a, #222222);
    border-radius: 15px;
    border: 1px solid #333;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    margin: 0;
    width: 100%;
}

.order-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.25);
    border-color: var(--shell-yellow);
}

.order-card .card-header {
    padding: 0.75rem 1rem;
    background-color: rgba(20, 20, 20, 0.8);
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-card .order-id {
    font-family: 'Share Tech Mono', monospace;
    color: var(--shell-yellow);
    font-weight: 600;
    font-size: 1.1rem;
}

.order-card .order-date {
    font-size: 0.85rem;
    color: var(--shell-light-gray);
}

.order-card .card-body {
    padding: 1rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    background-color: #222;
}

.order-card .card-title {
    color: var(--shell-yellow);
    font-weight: 600;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.order-card .card-text {
    color: var(--shell-light-gray);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.order-card .card-text.description {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 4.5rem;
}

.order-card .order-meta {
    margin-top: auto;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.order-card .card-footer {
    background-color: #1a1a1a;
    border-top: 1px solid #333;
    padding: 0.75rem 1rem;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: auto;
}

.order-card .card-actions {
    display: flex;
    gap: 0.5rem;
}

/* Status badges */
.status-badge {
    padding: 0.35rem 0.65rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.35rem;
}

.status-badge.status-pending {
    background-color: rgba(253, 184, 19, 0.2);
    color: var(--status-pending);
    border: 1px solid rgba(253, 184, 19, 0.3);
}

.status-badge.status-in_progress {
    background-color: rgba(52, 152, 219, 0.2);
    color: var(--status-in_progress);
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.status-badge.status-completed {
    background-color: rgba(46, 204, 113, 0.2);
    color: var(--status-completed);
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.status-badge.status-cancelled {
    background-color: rgba(231, 76, 60, 0.2);
    color: var(--status-cancelled);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

/* Priority badges */
.priority-badge {
    padding: 0.35rem 0.65rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.35rem;
}

.priority-badge.priority-low {
    background-color: rgba(52, 152, 219, 0.2);
    color: var(--priority-low);
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.priority-badge.priority-medium {
    background-color: rgba(253, 184, 19, 0.2);
    color: var(--priority-medium);
    border: 1px solid rgba(253, 184, 19, 0.3);
}

.priority-badge.priority-high {
    background-color: rgba(231, 76, 60, 0.2);
    color: var(--priority-high);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.priority-badge.priority-critical {
    background-color: rgba(155, 89, 182, 0.2);
    color: var(--priority-critical);
    border: 1px solid rgba(155, 89, 182, 0.3);
}

/* Tabela de ordens */
.table-shell {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--shell-white);
    border-collapse: separate;
    border-spacing: 0;
}

.table-shell thead th {
    background-color: rgba(20, 20, 20, 0.7);
    border-bottom: 2px solid var(--shell-yellow);
    padding: 0.75rem 1rem;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 1px;
}

.table-shell tbody tr {
    background-color: rgba(30, 30, 30, 0.5);
    transition: all 0.3s ease;
}

.table-shell tbody tr:hover {
    background-color: rgba(40, 40, 40, 0.7);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.table-shell td {
    padding: 0.75rem 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    vertical-align: middle;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

/* Estado vazio */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
}

.empty-state-icon {
    font-size: 3rem;
    color: var(--shell-yellow);
    margin-bottom: 1rem;
    opacity: 0.7;
}

.empty-state p {
    color: var(--shell-light-gray);
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

/* Botão flutuante */
.floating-action-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: var(--shell-yellow);
    color: #222;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    transition: all 0.3s ease;
}

.floating-action-btn:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
}

.floating-action-btn:active {
    transform: translateY(0) scale(0.95);
}

/* Animações */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

/* Toast notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1055;
}

.toast-shell {
    background-color: rgba(30, 30, 30, 0.9);
    color: var(--shell-white);
    border-radius: 8px;
    border-left: 4px solid var(--shell-yellow);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    margin-bottom: 10px;
    min-width: 300px;
}

.toast-shell.success {
    border-left-color: var(--status-completed);
}

.toast-shell.error {
    border-left-color: var(--status-cancelled);
}

.toast-shell .toast-header {
    background-color: rgba(20, 20, 20, 0.7);
    color: var(--shell-white);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.toast-shell .toast-body {
    padding: 0.75rem 1rem;
}

/* Responsividade */
@media (max-width: 768px) {
    .orders-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-item {
        margin-bottom: 1rem;
    }
}
