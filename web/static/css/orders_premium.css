/* CSS Premium para a página de Ordens de Serviço */

:root {
    --shell-red: #ED1C24;
    --shell-yellow: #FDB813;
    --shell-dark: #1E1E1E;
    --shell-light: #f8f9fa;
    --shell-gray: #808080;
    --shell-card-bg: rgba(30, 30, 30, 0.85);
    --shell-card-border: rgba(253, 184, 19, 0.3);

    --status-pending: #ffc107;
    --status-in-progress: #17a2b8;
    --status-completed: #28a745;
    --status-cancelled: #dc3545;

    --priority-low: #6c757d;
    --priority-medium: #17a2b8;
    --priority-high: #ffc107;
    --priority-urgent: #dc3545;
}

/* Estilos Gerais */
body {
    background-color: var(--shell-dark);
    color: var(--shell-light);
    font-family: 'Rajdhani', sans-serif;
}

.content-with-sidebar {
    background: linear-gradient(135deg, rgba(30,30,30,0.95) 0%, rgba(50,50,50,0.9) 100%);
}

.main-content {
    padding: 25px;
}

/* <PERSON><PERSON><PERSON><PERSON><PERSON> */
.page-header {
    position: relative;
    background: linear-gradient(90deg, rgba(30,30,30,0.9) 0%, rgba(40,40,40,0.8) 100%);
    border-left: 4px solid var(--shell-yellow);
    border-radius: 8px;
    padding: 20px 25px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('/static/images/pattern-dark.png');
    opacity: 0.1;
    z-index: 0;
}

.page-header .content {
    position: relative;
    z-index: 1;
}

.page-title {
    color: var(--shell-yellow);
    font-weight: 700;
    font-size: 2.2rem;
    margin-bottom: 5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 1px;
}

.page-subtitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1rem;
    margin-bottom: 0;
}

.header-actions {
    display: flex;
    align-items: center;
}

.btn-shell-yellow {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    font-weight: 600;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-shell-yellow:hover {
    background-color: #e9a800;
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

.btn-outline-light {
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--shell-light);
    background-color: rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Cards de Métricas */
.metrics-container {
    margin-bottom: 30px;
}

.metric-card {
    background: var(--shell-card-bg);
    border-radius: 10px;
    border: 1px solid var(--shell-card-border);
    padding: 20px;
    height: 100%;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0) 100%);
    z-index: 0;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.metric-icon {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 2.5rem;
    opacity: 0.2;
    color: var(--shell-yellow);
}

.metric-title {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 10px;
    position: relative;
    z-index: 1;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.metric-value {
    font-family: 'Share Tech Mono', monospace;
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 5px;
    position: relative;
    z-index: 1;
    line-height: 1;
}

.metric-card.pending .metric-value {
    color: var(--status-pending);
}

.metric-card.progress .metric-value {
    color: var(--status-in-progress);
}

.metric-card.completed .metric-value {
    color: var(--status-completed);
}

.metric-card.total .metric-value {
    color: var(--shell-yellow);
}

.metric-percentage {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.7);
    position: relative;
    z-index: 1;
    margin-top: 5px;
}

.metric-trend {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 20px;
    font-size: 0.8rem;
    margin-left: 10px;
}

.trend-up {
    background-color: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.trend-down {
    background-color: rgba(220, 53, 69, 0.2);
    color: #dc3545;
}

/* Seção de Filtros */
.filters-section {
    background: var(--shell-card-bg);
    border-radius: 10px;
    border: 1px solid var(--shell-card-border);
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.filters-title {
    font-size: 1.2rem;
    color: var(--shell-yellow);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.filters-title i {
    margin-right: 10px;
}

.form-control, .form-select {
    background-color: rgba(30, 30, 30, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 6px;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    background-color: rgba(40, 40, 40, 0.8);
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 0.25rem rgba(253, 184, 19, 0.25);
    color: white;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

/* Tabela de Ordens */
.orders-table-container {
    background: var(--shell-card-bg);
    border-radius: 10px;
    border: 1px solid var(--shell-card-border);
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-title {
    font-size: 1.2rem;
    color: var(--shell-yellow);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.table-title i {
    margin-right: 10px;
}

.orders-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.orders-table thead th {
    background-color: rgba(20, 20, 20, 0.5);
    color: var(--shell-yellow);
    padding: 15px;
    font-weight: 600;
    text-align: left;
    border-bottom: 2px solid rgba(253, 184, 19, 0.3);
}

.orders-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.orders-table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.orders-table td {
    padding: 15px;
    vertical-align: middle;
}

.order-id {
    font-family: 'Share Tech Mono', monospace;
    font-weight: 600;
    color: var(--shell-yellow);
}

.order-title {
    font-weight: 500;
    color: white;
}

.order-description {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 5px;
}

/* Status e Prioridade */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.status-badge i {
    margin-right: 5px;
}

.status-pending {
    background-color: rgba(255, 193, 7, 0.2);
    color: var(--status-pending);
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-in-progress {
    background-color: rgba(23, 162, 184, 0.2);
    color: var(--status-in-progress);
    border: 1px solid rgba(23, 162, 184, 0.3);
}

.status-completed {
    background-color: rgba(40, 167, 69, 0.2);
    color: var(--status-completed);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-cancelled {
    background-color: rgba(220, 53, 69, 0.2);
    color: var(--status-cancelled);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.priority-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
}

.priority-badge i {
    margin-right: 5px;
}

.priority-low {
    background-color: rgba(108, 117, 125, 0.2);
    color: var(--priority-low);
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.priority-medium {
    background-color: rgba(23, 162, 184, 0.2);
    color: var(--priority-medium);
    border: 1px solid rgba(23, 162, 184, 0.3);
}

.priority-high {
    background-color: rgba(255, 193, 7, 0.2);
    color: var(--priority-high);
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.priority-urgent {
    background-color: rgba(220, 53, 69, 0.2);
    color: var(--priority-urgent);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

/* Botões de Ação */
.action-buttons {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    border: none;
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

.btn-view:hover {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
}

.btn-edit:hover {
    background-color: var(--status-in-progress);
    color: white;
}

.btn-delete:hover {
    background-color: var(--status-cancelled);
    color: white;
}

/* Paginação */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
}

.pagination-info {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.pagination {
    margin-bottom: 0;
}

.page-item:not(.active) .page-link {
    background-color: rgba(30, 30, 30, 0.7);
    border-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.page-item.active .page-link {
    background-color: var(--shell-yellow);
    border-color: var(--shell-yellow);
    color: var(--shell-dark);
}

.page-item:not(.active) .page-link:hover {
    background-color: rgba(50, 50, 50, 0.8);
    border-color: rgba(255, 255, 255, 0.2);
}

/* Estado Vazio */
.empty-state {
    text-align: center;
    padding: 50px 20px;
}

.empty-state-icon {
    font-size: 4rem;
    color: rgba(255, 255, 255, 0.2);
    margin-bottom: 20px;
}

.empty-state-title {
    font-size: 1.5rem;
    color: var(--shell-yellow);
    margin-bottom: 10px;
}

.empty-state-text {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 20px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsividade */
@media (max-width: 1200px) {
    .metric-value {
        font-size: 2.8rem;
    }

    .metric-title {
        font-size: 0.9rem;
    }
}

@media (max-width: 992px) {
    .page-header {
        flex-direction: column;
    }

    .header-actions {
        margin-top: 15px;
        width: 100%;
        display: flex;
        justify-content: space-between;
    }

    .metric-card {
        margin-bottom: 15px;
    }

    .metric-value {
        font-size: 2.5rem;
    }

    .filters-section .row {
        margin-bottom: 0 !important;
    }

    .table-title {
        flex-direction: column;
        align-items: flex-start;
    }

    .table-title .btn-group {
        margin-top: 10px;
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: 15px;
    }

    .page-title {
        font-size: 1.8rem;
    }

    .orders-table-container {
        overflow-x: auto;
        padding: 15px;
    }

    .orders-table {
        min-width: 800px;
    }

    .metric-card {
        padding: 15px;
    }

    .metric-value {
        font-size: 2.2rem;
    }

    .filters-section {
        padding: 15px;
    }

    .pagination-container {
        flex-direction: column;
        align-items: center;
    }

    .pagination-info {
        margin-bottom: 10px;
    }

    .notification {
        width: 90%;
        right: 5%;
        left: 5%;
    }
}

@media (max-width: 576px) {
    .page-title {
        font-size: 1.5rem;
    }

    .btn-shell-yellow, .btn-outline-light {
        padding: 8px 12px;
        font-size: 0.9rem;
    }

    .metric-value {
        font-size: 2rem;
    }

    .metric-title {
        font-size: 0.8rem;
    }

    .header-actions {
        flex-direction: column;
        gap: 10px;
    }

    .header-actions .btn {
        width: 100%;
    }

    .empty-state-icon {
        font-size: 3rem;
    }

    .empty-state-title {
        font-size: 1.2rem;
    }
}

/* Botão flutuante */
.floating-action-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background-color: var(--shell-yellow);
    color: #000;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    transition: all 0.3s ease;
    text-decoration: none;
}

.floating-action-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
    color: #000;
}

.floating-action-btn:active {
    transform: scale(0.95);
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse-animation {
    animation: pulse 0.3s ease-in-out;
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }

/* Efeito de vidro (glass effect) */
.glass-effect {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}
