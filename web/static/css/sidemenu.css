/* Sidebar Shell Estilizada para o Sistema Tradição */

/* Layout base */
.app-container {
    display: flex;
    min-height: 100vh;
    width: 100%;
}

/* Estilo do sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 260px;
    height: 100vh;
    background: linear-gradient(180deg, #333333 0%, #222222 100%);
    color: #fff;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    overflow-y: auto;
    overflow-x: hidden;
    box-shadow: 3px 0 10px rgba(0, 0, 0, 0.1);
}

/* Cabeçalho do sidebar */
.sidebar-header {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.logo {
    display: flex;
    align-items: center;
    color: #fff;
    text-decoration: none;
    transition: all 0.3s;
}

.logo img {
    height: 32px;
    width: auto;
    margin-right: 10px;
    filter: drop-shadow(0 0 5px rgba(253, 184, 19, 0.5));
}

.logo-text {
    font-size: 1.2rem;
    font-weight: 600;
    color: #fff;
    white-space: nowrap;
    transition: opacity 0.3s;
}

.toggle-btn {
    background: transparent;
    color: #fff;
    border: none;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.toggle-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Seções do sidebar */
.sidebar-section {
    padding: 0.5rem 0; /* Reduzido de 1rem 0 */
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.sidebar-section-title {
    padding: 0 1.2rem;
    margin-bottom: 0.3rem; /* Reduzido de 0.5rem */
    font-size: 0.65rem; /* Reduzido de 0.7rem */
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--shell-yellow);
    font-weight: 600;
}

/* Itens de menu */
.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    position: relative;
    transition: all 0.3s;
    margin-bottom: 2px; /* Reduzido do valor anterior para economizar espaço */
}

.nav-item.active {
    background-color: rgba(237, 28, 36, 0.15);
}

.nav-item.active .nav-link {
    color: var(--shell-yellow);
}

.nav-item.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 4px;
    background-color: var(--shell-red);
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.5rem 1.2rem;  /* Reduzido de 0.8rem 1.5rem */
    color: #fff;
    text-decoration: none;
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--shell-yellow);
}

.nav-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    position: relative;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s;
}

.nav-link:hover .nav-icon {
    color: var(--shell-yellow);
    transform: translateY(-2px);
}

.nav-text {
    flex: 1;
    white-space: nowrap;
    transition: opacity 0.3s;
}

/* Badge de notificação */
.nav-badge {
    background-color: var(--shell-red);
    color: white;
    font-size: 0.7rem;
    padding: 0.1rem 0.5rem;
    border-radius: 10px;
    margin-left: 0.5rem;
    transition: all 0.3s;
}

/* Tooltip para modo collapsed */
.nav-tooltip {
    position: absolute;
    left: 70px;
    background-color: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s;
    transform: translateX(-10px);
    z-index: 1100;
    white-space: nowrap;
}

.nav-tooltip::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -5px;
    transform: translateY(-50%);
    border-width: 5px 5px 5px 0;
    border-style: solid;
    border-color: transparent rgba(0, 0, 0, 0.8) transparent transparent;
}

/* Marquee de mensagens */
.quote-marquee {
    padding: 0.4rem 0.8rem; /* Reduzido de 0.8rem 1rem */
    background-color: rgba(253, 184, 19, 0.1);
    margin: 0.5rem; /* Reduzido de 1rem */
    border-radius: 5px;
    overflow: hidden;
    display: flex;
    align-items: center;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
}

.quote-text {
    color: var(--shell-yellow);
    font-size: 0.8rem;
    white-space: nowrap;
    animation: marquee 30s linear infinite;
}

@keyframes marquee {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-100%);
    }
}

/* Perfil de usuário */
.user-profile {
    margin-top: auto;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.2);
    transition: all 0.3s;
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;
    flex-shrink: 0;
    border: 2px solid var(--shell-yellow);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info {
    flex: 1;
    transition: opacity 0.3s;
}

.user-name {
    font-weight: 600;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-role {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
}

/* Estado collapsed do sidebar */
.sidebar.collapsed {
    width: 70px;
}

.sidebar.collapsed .logo-text,
.sidebar.collapsed .nav-text,
.sidebar.collapsed .user-info,
.sidebar.collapsed .sidebar-section-title,
.sidebar.collapsed .nav-badge,
.sidebar.collapsed .quote-marquee {
    opacity: 0;
    visibility: hidden;
}

.sidebar.collapsed .nav-link:hover .nav-tooltip {
    opacity: 1;
    transform: translateX(0);
}

/* Área de conteúdo principal */
.main-content {
    flex: 1;
    margin-left: 260px;
    transition: all 0.3s ease;
    width: calc(100% - 260px);
    min-height: 100vh;
}

.main-content.expanded {
    margin-left: 70px;
    width: calc(100% - 70px);
}

/* Toggle do menu para mobile */
.mobile-menu-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--shell-red);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    cursor: pointer;
    z-index: 1050;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s;
}

.mobile-menu-toggle:hover {
    background-color: var(--shell-red-hover);
    transform: scale(1.05);
}

/* Responsividade */
@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
        width: 260px;
    }
    
    .sidebar.mobile-visible {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        width: 100%;
    }
    
    .main-content.expanded {
        margin-left: 0;
        width: 100%;
    }
}