/* Common CSS styles for Tradicao */

/* Base */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: '<PERSON>o', Arial, sans-serif;
}

/* Utility classes */
.full-height {
    height: 100%;
}

.min-vh-100 {
    min-height: 100vh;
}

.p-relative {
    position: relative;
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Shadows */
.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Spacing utilities */
.mb-half {
    margin-bottom: 0.5rem;
}

.mt-half {
    margin-top: 0.5rem;
}

.ml-half {
    margin-left: 0.5rem;
}

.mr-half {
    margin-right: 0.5rem;
}

/* Transitions */
.transition-all {
    transition: all 0.3s ease;
}

/* Cards with hover effect */
.hover-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-pending {
    background-color: #ffc107;
}

.status-in-progress {
    background-color: #17a2b8;
}

.status-completed {
    background-color: #28a745;
}

.status-cancelled {
    background-color: #dc3545;
}

/* Action buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    padding: 0.25rem 0.5rem;
    line-height: 1;
}

/* Loader styles */
.loader {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #da291c;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
    display: inline-block;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Page loader overlay */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.page-loader .loader {
    width: 50px;
    height: 50px;
    border-width: 5px;
}

/* Table styles */
.table-responsive {
    overflow-x: auto;
}

.table-hover tbody tr:hover {
    background-color: rgba(218, 41, 28, 0.05);
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-control:focus {
    border-color: #da291c;
    box-shadow: 0 0 0 0.2rem rgba(218, 41, 28, 0.25);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #da291c;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #b82218;
}

/* Error messages */
.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Tooltips */
.tooltip-inner {
    background-color: #222;
    border-radius: 0.25rem;
    padding: 0.5rem;
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: #222;
}

.tooltip.bs-tooltip-bottom .tooltip-arrow::before {
    border-bottom-color: #222;
}

.tooltip.bs-tooltip-start .tooltip-arrow::before {
    border-left-color: #222;
}

.tooltip.bs-tooltip-end .tooltip-arrow::before {
    border-right-color: #222;
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #adb5bd;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .content-with-sidebar {
        margin-left: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        border: none !important;
    }
    
    .card-header, .card-footer {
        background-color: transparent !important;
    }
}

/* Mobile optimizations */
@media (max-width: 767.98px) {
    .d-sm-none {
        display: none !important;
    }
    
    .table-responsive-mobile {
        display: block;
        width: 100%;
        overflow-x: auto;
    }
    
    .table-responsive-mobile table {
        width: 100%;
        min-width: 500px;
    }
}