/* Estilos globais do Sistema Tradição */
:root {
    --shell-red: #ED1C24;
    --shell-red-hover: #CE181F;
    --shell-red-light: #ff6b6e;
    --shell-yellow: #FDB813;
    --shell-yellow-hover: #e0a100;
    --shell-yellow-light: #ffe07a;
    --shell-dark: #333333;
    --shell-dark-hover: #222222;
    --shell-light: #f8f9fa;
    --shell-light-hover: #e9ecef;
    --shell-gray: #808080;
    --shell-gray-light: #d9d9d9;
    --shell-success: #28a745;
    --shell-info: #17a2b8;
    --shell-warning: #ffc107;
    --shell-danger: #dc3545;
}

/* Classes de Utilitários */
.shell-bg-red {
    background-color: var(--shell-red);
}

.shell-bg-yellow {
    background-color: var(--shell-yellow);
}

.shell-text-red {
    color: var(--shell-red);
}

.shell-text-yellow {
    color: var(--shell-yellow);
}

/* Estilização do Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--shell-red);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--shell-red-hover);
}

/* Spinner Shell animado */
.shell-spinner {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 4px solid var(--shell-yellow);
    border-top-color: var(--shell-red);
    animation: shell-spin 1s infinite linear;
    position: relative;
}

.shell-spinner::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 50%;
    border: 2px solid rgba(0, 0, 0, 0.1);
}

.shell-spinner::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    background-image: url('/static/images/shell-logo-small.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

@keyframes shell-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Botões Shell */
.btn-shell-red {
    background-color: var(--shell-red);
    color: white;
    border: none;
    transition: all 0.3s;
}

.btn-shell-red:hover {
    background-color: var(--shell-red-hover);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-shell-yellow {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    border: none;
    transition: all 0.3s;
}

.btn-shell-yellow:hover {
    background-color: var(--shell-yellow-hover);
    color: var(--shell-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Formulários personalizados */
.form-control:focus {
    border-color: var(--shell-yellow);
    box-shadow: 0 0 0 0.25rem rgba(253, 184, 19, 0.25);
}

/* Alertas customizados */
.alert-shell {
    background-color: var(--shell-yellow-light);
    border-color: var(--shell-yellow);
    color: var(--shell-dark);
}

.alert-shell-danger {
    background-color: var(--shell-red-light);
    border-color: var(--shell-red);
    color: white;
}

/* Cartões personalizados */
.card-shell {
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
    border: none;
    overflow: hidden;
}

.card-shell:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.card-shell .card-header {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    font-weight: 600;
    border-bottom: none;
}

.card-shell .card-footer {
    background-color: var(--shell-light);
    border-top: none;
}

/* Tabelas Shell */
.table-shell {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.table-shell thead {
    background-color: var(--shell-red);
    color: white;
}

.table-shell th {
    font-weight: 600;
    border: none;
}

.table-shell tbody tr:nth-child(odd) {
    background-color: rgba(253, 184, 19, 0.05);
}

.table-shell tbody tr:hover {
    background-color: rgba(253, 184, 19, 0.1);
}

/* Status coloridos */
.status-pending {
    color: var(--shell-yellow);
}

.status-in-progress {
    color: var(--shell-info);
}

.status-completed {
    color: var(--shell-success);
}

.status-canceled {
    color: var(--shell-danger);
}

/* Badges especiais */
.badge-shell {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
}

.badge-shell-red {
    background-color: var(--shell-red);
    color: white;
}

/* Notificações Toast */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
}

.toast-shell {
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.toast-shell .toast-header {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    border-bottom: none;
}

.toast-shell-danger .toast-header {
    background-color: var(--shell-red);
    color: white;
}

/* Sidebar */
.sidebar {
    background-color: var(--shell-dark);
    color: white;
}

/* FullCalendar Customização */
.fc-theme-standard .fc-toolbar {
    background-color: var(--shell-red);
    color: white;
    padding: 10px;
    border-radius: 8px 8px 0 0;
}

.fc .fc-button-primary {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    border: none;
}

.fc .fc-button-primary:hover {
    background-color: var(--shell-yellow-hover);
    color: var(--shell-dark);
}

.fc .fc-button-primary:disabled {
    background-color: var(--shell-gray);
}

.fc .fc-daygrid-day.fc-day-today {
    background-color: rgba(253, 184, 19, 0.2);
}

.fc .fc-highlight {
    background-color: rgba(237, 28, 36, 0.1);
}

.fc .fc-event {
    border-radius: 4px;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Animações e efeitos */
@keyframes fuel-bubble {
    0%, 100% {
        transform: translateY(0) scale(1);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-15px) scale(1.1);
        opacity: 0.9;
    }
}

@keyframes fuel-flow {
    0% {
        height: 0;
    }
    100% {
        height: 100%;
    }
}

/* Esquema de cores para o tema Shell */
.shell-theme {
    --primary: var(--shell-red);
    --secondary: var(--shell-yellow);
    --success: #2ecc71;
    --info: #3498db;
    --warning: #f39c12;
    --danger: #e74c3c;
    --light: #f8f9fa;
    --dark: #333333;
}

/* Responsividade */
@media (max-width: 768px) {
    .hide-on-mobile {
        display: none !important;
    }

    .card-shell {
        margin-bottom: 15px;
    }
}

/* Tema escuro (pode ser ativado com uma classe no body) */
.dark-theme {
    --shell-light: #333333;
    --shell-dark: #f8f9fa;
    background-color: #222;
    color: #f8f9fa;
}

.dark-theme .card,
.dark-theme .modal-content {
    background-color: #333;
    color: #f8f9fa;
}

.dark-theme .table {
    color: #f8f9fa;
}

/* Estilo do preloader */
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease-in-out;
}

.preloader.fade-out {
    opacity: 0;
    pointer-events: none;
}

.preloader .shell-spinner {
    width: 80px;
    height: 80px;
}

.preloader .loader-text {
    position: absolute;
    bottom: 20%;
    color: var(--shell-yellow);
    font-size: 1.2rem;
    font-weight: 500;
    text-align: center;
}

/* Estilização para o dashboard-calendario */
.dashboard-calendario-container {
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(30, 30, 30, 1) 0%, rgba(50, 50, 50, 0.9) 50%, rgba(40, 40, 40, 1) 100%);
    border-radius: 10px;
    width: 100%;
    max-height: 500px; /* Limite de altura adicionado */
    overflow: auto; /* Permitir rolagem */
    box-sizing: border-box;
}

/* Cartões de estatísticas */
.stat-card {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    background: linear-gradient(145deg, #2a2a2a, #222222);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    height: 100%;
    min-height: 140px;
    color: var(--shell-white);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.stat-card-fuel {
    position: absolute;
    top: 0;
    right: 0;
    width: 50px;
    height: 100%;
    overflow: hidden;
}

.fuel-gauge {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(240, 240, 240, 0.5);
}

.fuel-level {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 0%;
    transition: height 1.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.stat-pending .fuel-level {
    background-color: var(--shell-yellow);
    box-shadow: 0 0 10px var(--shell-yellow);
}

.stat-progress .fuel-level {
    background-color: #3498db;
    box-shadow: 0 0 10px #3498db;
}

.stat-completed .fuel-level {
    background-color: #2ecc71;
    box-shadow: 0 0 10px #2ecc71;
}

.stat-station .fuel-level {
    background-color: var(--shell-red);
    box-shadow: 0 0 10px var(--shell-red);
}

.fuel-bubble {
    position: absolute;
    background-color: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: fuel-bubble 3s infinite;
}

.stat-card-fuel .fuel-bubble:nth-child(2) {
    width: 10px;
    height: 10px;
    left: 10px;
    bottom: 10px;
    animation-delay: 0s;
}

.stat-card-fuel .fuel-bubble:nth-child(3) {
    width: 8px;
    height: 8px;
    left: 25px;
    bottom: 25px;
    animation-delay: 0.5s;
}

.stat-card-fuel .fuel-bubble:nth-child(4) {
    width: 12px;
    height: 12px;
    left: 15px;
    bottom: 50px;
    animation-delay: 1s;
}

.stat-card-body {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    padding-right: 3rem;
}

.stat-card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 12px;
    margin-right: 1rem;
    font-size: 1.5rem;
}

.stat-pending .stat-card-icon {
    background-color: rgba(253, 184, 19, 0.2);
    color: var(--shell-yellow);
}

.stat-progress .stat-card-icon {
    background-color: rgba(52, 152, 219, 0.2);
    color: #3498db;
}

.stat-completed .stat-card-icon {
    background-color: rgba(46, 204, 113, 0.2);
    color: #2ecc71;
}

.stat-station .stat-card-icon {
    background-color: rgba(237, 28, 36, 0.2);
    color: var(--shell-red);
}

.stat-card-content {
    flex: 1;
}

.stat-card-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    line-height: 1;
}

.stat-pending .stat-card-value {
    color: var(--shell-yellow);
}

.stat-progress .stat-card-value {
    color: #3498db;
}

.stat-completed .stat-card-value {
    color: #2ecc71;
}

.stat-station .stat-card-value {
    color: var(--shell-red);
}

.stat-card-label {
    font-size: 0.875rem;
    margin-bottom: 0;
    color: #adb5bd;
}

/* Barra de ações */
.action-bar {
    background-color: #222;
    border-radius: 15px;
    padding: 1rem 1.5rem;
    margin: 1.5rem 0;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0;
    color: var(--shell-white);
    display: flex;
    align-items: center;
}

.section-title i {
    margin-right: 0.5rem;
    color: var(--shell-red);
}

.title-decoration {
    height: 3px;
    width: 30px;
    background-color: var(--shell-yellow);
    margin-left: 1rem;
    border-radius: 3px;
}

.action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

.btn-filter {
    background-color: #333;
    color: var(--shell-white);
    border: 1px solid #444;
}

.btn-filter:hover {
    background-color: #444;
    color: var(--shell-white);
}

.btn-view {
    background-color: #333;
    color: var(--shell-white);
    border: 1px solid #444;
}

.btn-view:hover,
.btn-view.active {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    border-color: var(--shell-yellow);
}

.btn-add-order {
    background-color: var(--shell-red);
    color: white;
    border: none;
}

.btn-add-order:hover {
    background-color: var(--shell-red-hover);
    color: white;
}

/* Container do Calendário */
.calendar-container {
    background-color: #222;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.calendar-decoration {
    position: absolute;
    width: 150px;
    height: 150px;
    background-image: url('/static/images/shell-pump.svg');
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0.05;
    z-index: 0;
}

.calendar-decoration.top-left {
    top: -50px;
    left: -50px;
    transform: rotate(-15deg);
}

.calendar-decoration.top-right {
    top: -50px;
    right: -50px;
    transform: rotate(15deg);
}

.calendar-decoration.bottom-left {
    bottom: -50px;
    left: -50px;
    transform: rotate(-40deg);
}

.calendar-decoration.bottom-right {
    bottom: -50px;
    right: -50px;
    transform: rotate(40deg);
}

.calendar-wrapper {
    position: relative;
    z-index: 1;
    min-height: 600px;
}

/* Customizações específicas do FullCalendar */
.event-status-pending {
    background-color: var(--shell-yellow) !important;
    border-left: 3px solid var(--shell-yellow-hover) !important;
}

.event-status-in_progress {
    background-color: #3498db !important;
    border-left: 3px solid #2980b9 !important;
}

.event-status-completed {
    background-color: #2ecc71 !important;
    border-left: 3px solid #27ae60 !important;
}

.event-status-canceled {
    background-color: #e74c3c !important;
    border-left: 3px solid #c0392b !important;
}

.event-status-waiting_parts {
    background-color: #9b59b6 !important;
    border-left: 3px solid #8e44ad !important;
}

.event-status-on_hold {
    background-color: #95a5a6 !important;
    border-left: 3px solid #7f8c8d !important;
}

.event-status-waiting_approval {
    background-color: #34495e !important;
    border-left: 3px solid #2c3e50 !important;
}

.event-priority-gauge {
    height: 4px;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.1);
    position: absolute;
    bottom: 0;
    left: 0;
}

.event-priority-level {
    height: 100%;
    background-color: #fff;
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

.event-priority-baixa .event-priority-level {
    width: 25%;
}

.event-priority-media .event-priority-level {
    width: 50%;
}

.event-priority-alta .event-priority-level {
    width: 75%;
}

.event-priority-critica .event-priority-level {
    width: 100%;
}

.event-status-icon {
    margin-right: 5px;
}

/* Estilos para o tooltip do evento */
.calendar-event-tooltip {
    max-width: 300px;
}

.event-tooltip {
    font-size: 0.9rem;
}

.event-tooltip hr {
    margin: 0.5rem 0;
}

.event-tooltip p {
    margin-bottom: 0.25rem;
}

.event-tooltip i {
    margin-right: 0.5rem;
    width: 15px;
}

/* Área de Legenda */
.legend-container {
    background-color: #222;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    height: 100%;
    color: var(--shell-white);
}

.legend-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--shell-white);
    display: flex;
    align-items: center;
}

.legend-title i {
    margin-right: 0.5rem;
    color: var(--shell-red);
}

.legend-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.legend-groups {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.legend-group {
    flex: 1;
    min-width: 200px;
}

.legend-group h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--shell-white);
}

.legend-items {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 4px;
}

.priority-indicators {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.priority-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.priority-gauge {
    flex: 1;
    height: 8px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.priority-level {
    height: 100%;
    background-color: var(--shell-red);
    border-radius: 4px;
}

/* Área de dicas rápidas */
.tips-container {
    background-color: #222;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    height: 100%;
    color: var(--shell-white);
}

.tips-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--shell-white);
    display: flex;
    align-items: center;
}

.tips-title i {
    margin-right: 0.5rem;
    color: var(--shell-yellow);
}

.tips-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tips-list li {
    padding: 0.75rem 0;
    border-bottom: 1px dashed var(--shell-gray-light);
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.tips-list li:last-child {
    border-bottom: none;
}

.tips-icon {
    color: var(--shell-yellow);
    font-size: 1.25rem;
    flex-shrink: 0;
    margin-top: 0.25rem;
}

.tips-content {
    flex: 1;
}

.tips-content p {
    margin-bottom: 0;
    color: var(--shell-white);
}

.tips-content .tips-shortcut {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    margin: 0.25rem 0.25rem 0 0;
    background-color: #333;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--shell-yellow);
}

/* Animação para contador de números */
@keyframes countUp {
    from {
        content: attr(data-count);
        opacity: 0;
    }
    to {
        content: attr(data-count);
        opacity: 1;
    }
}

.counter-value {
    position: relative;
    overflow: hidden;
}

.counter-value::after {
    content: attr(data-count);
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    animation: countUp 1s forwards;
    animation-delay: calc(var(--animation-order) * 0.1s + 0.5s);
}

/* Modal detalhes de ordem */
.order-details-modal .modal-content {
    background-color: #222;
    color: var(--shell-white);
}

.order-details-modal .modal-header {
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
}

.order-details-modal .modal-body {
    color: var(--shell-white);
}

.order-details-modal .modal-footer {
    border-top: 1px solid #444;
}

.order-progress {
    margin: 1.5rem 0;
}

.order-progress-bar {
    height: 10px;
    border-radius: 5px;
    background-color: rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.order-progress-level {
    height: 100%;
    border-radius: 5px;
    transition: width 1s ease-in-out;
}

.order-progress-text {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: var(--shell-gray);
}

/* Responsividade */
@media (max-width: 992px) {
    .legend-groups {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .action-buttons {
        flex-wrap: wrap;
    }

    .btn-add-order {
        width: 100%;
        margin-top: 0.5rem;
    }

    .calendar-container {
        padding: 1rem;
    }
}

/* Estilos para mensagens engraçadas do calendário */
.calendar-jokes {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: var(--shell-yellow);
    color: var(--shell-dark);
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.8rem;
    opacity: 0.8;
    cursor: pointer;
    transition: all 0.3s;
}

.calendar-jokes:hover {
    opacity: 1;
    transform: translateY(-3px);
}

.calendar-jokes i {
    margin-right: 5px;
}

/* Animações para entradas */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Floating Action Button para adicionar ordem */
.fab-add-order {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: var(--shell-red);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.3s;
    z-index: 1000;
}

.fab-add-order:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
}

.fab-add-order i {
    font-size: 1.5rem;
}