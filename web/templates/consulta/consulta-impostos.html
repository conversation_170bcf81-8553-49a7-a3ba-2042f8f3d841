{{ define "consulta/consulta-impostos.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Consulta de Impostos - Shell</title>

    <!-- CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/consulta-impostos.css">

    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    {{ template "sidebar" . }}

    <!-- Conteúdo Principal -->
    <div class="content-with-sidebar">
        <div class="main-content">
            <!-- Cabeçalho da Página -->
            <div class="page-header">
                <h1><i class="fas fa-calculator me-2"></i> Consulta de Impostos</h1>
                <p>Calcule os impostos aplicados nos diferentes tipos de combustíveis por estado.</p>
            </div>

            <!-- Conteúdo da Página -->
            <div class="page-content">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card card-shell">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h3><i class="fas fa-gas-pump me-2"></i> Calculadora de Impostos</h3>
                            </div>
                            <div class="card-body">
                                <form id="formCalculadora" class="mb-4">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="estadoSelect" class="form-label">Estado</label>
                                            <select id="estadoSelect" class="form-select">
                                                <option value="">Selecione o estado</option>
                                                <!-- Os estados serão preenchidos via JavaScript -->
                                            </select>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="combustivelSelect" class="form-label">Combustível</label>
                                            <select id="combustivelSelect" class="form-select">
                                                <option value="">Selecione o combustível</option>
                                                <!-- Os combustíveis serão preenchidos via JavaScript -->
                                            </select>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="valorBaseInput" class="form-label">Valor Base (R$)</label>
                                            <input type="text" id="valorBaseInput" class="form-control" placeholder="0,00">
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <div id="mensagemErro" class="alert alert-shell-danger d-none" role="alert"></div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-12 text-end">
                                            <button type="submit" class="btn btn-shell-red">
                                                <i class="fas fa-calculator me-2"></i> Calcular Impostos
                                            </button>
                                        </div>
                                    </div>
                                </form>

                                <div id="resultados" class="d-none">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h4>Resultados do Cálculo</h4>
                                        <div class="btn-group">
                                            <button id="btnExportarPDF" class="btn btn-shell-yellow btn-sm">
                                                <i class="fas fa-file-pdf me-1"></i> Exportar PDF
                                            </button>
                                            <button id="btnImprimir" class="btn btn-shell-yellow btn-sm ms-2">
                                                <i class="fas fa-print me-1"></i> Imprimir
                                            </button>
                                        </div>
                                    </div>

                                    <div class="table-responsive">
                                        <table class="table table-shell">
                                            <thead>
                                                <tr>
                                                    <th>Imposto/Taxa</th>
                                                    <th class="text-center">Percentual</th>
                                                    <th class="text-end">Valor (R$)</th>
                                                </tr>
                                            </thead>
                                            <tbody id="tabelaResultados">
                                                <!-- Os resultados serão preenchidos via JavaScript -->
                                            </tbody>
                                        </table>
                                    </div>

                                    <div class="resumo">
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <h5>Total de Impostos:</h5>
                                                    <span id="totalImpostos" class="badge-shell-red">R$ 0,00</span>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <h5>Valor Final (com impostos):</h5>
                                                    <span id="valorFinal" class="badge-shell">R$ 0,00</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/impostos.js"></script>
    <script src="/static/js/calculadora-impostos.js"></script>
    <script src="/static/js/consulta-impostos.js"></script>
</body>
</html>
{{ end }}