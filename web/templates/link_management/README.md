# Documentação da Página de Gerenciamento de Relações

## Visão Geral

A página de Gerenciamento de Relações é uma interface centralizada para administrar os vínculos entre filiais, prestadoras de serviço e técnicos no Sistema de Gestão Tradição. Esta interface permite visualizar, criar, editar e excluir relações entre estas entidades, facilitando a gestão de quais prestadoras podem atender a quais filiais e quais técnicos estão associados a quais filiais.

## Arquivos Relacionados

### Templates HTML
- **Página Principal**: `web/templates/link_management/link_management.html`
- **Componente de Prestadoras e Filiais**: `web/templates/link_management/components/provider_branch_tab.html`
- **Componente de Técnicos e Filiais**: `web/templates/link_management/components/technician_branch_tab.html`
- **Componente de Prestadoras e Técnicos**: `web/templates/link_management/components/provider_technician_tab.html`

### Arquivos CSS
- **Estilos da Página**: `web/static/css/link_management.css`

### Arquivos JavaScript
- **Funcionalidades da Página**: `web/static/js/link_management.js`

## Estrutura da Página

A página de Gerenciamento de Relações é composta por três abas principais:

1. **Prestadoras e Filiais**: Gerencia quais prestadoras de serviço estão autorizadas a atender quais filiais.
2. **Técnicos e Filiais**: Gerencia quais técnicos estão associados a quais filiais, incluindo suas especialidades.
3. **Prestadoras e Técnicos**: Visualiza quais técnicos estão associados a quais prestadoras de serviço.

Cada aba contém:
- Filtros para facilitar a busca
- Tabela para visualização das relações existentes
- Botões para adicionar, editar e excluir relações
- Modais para criação e edição de relações

## Funcionalidades

### Gerenciamento de Prestadoras e Filiais
- Visualizar todas as relações entre prestadoras e filiais
- Adicionar uma nova prestadora a uma filial
- Remover uma prestadora de uma filial
- Filtrar por prestadora ou filial

### Gerenciamento de Técnicos e Filiais
- Visualizar todas as relações entre técnicos e filiais
- Adicionar um técnico a uma filial com uma especialidade específica
- Remover um técnico de uma filial
- Filtrar por técnico, filial ou especialidade

### Visualização de Prestadoras e Técnicos
- Visualizar todos os técnicos associados a uma prestadora
- Filtrar por prestadora

## Integração com API

A página utiliza os seguintes endpoints da API:

### Vínculos entre Prestadoras e Filiais
- `GET /api/links/provider-branch` - Listar todos os vínculos
- `POST /api/links/provider-branch` - Criar um novo vínculo
- `DELETE /api/links/provider-branch/:provider_id/:branch_id` - Remover um vínculo
- `GET /api/links/branch/:branch_id/providers` - Listar prestadoras de uma filial

### Vínculos entre Técnicos e Filiais
- `GET /api/links/technician-branch` - Listar todos os vínculos
- `POST /api/links/technician-branch` - Criar um novo vínculo
- `DELETE /api/links/technician-branch/:technician_id/:branch_id` - Remover um vínculo
- `GET /api/links/branch/:branch_id/technicians` - Listar técnicos de uma filial

### Vínculos entre Prestadoras e Técnicos
- `GET /api/links/provider/:provider_id/technicians` - Listar técnicos de uma prestadora

## Considerações de Segurança

- Apenas usuários com perfil de administrador, gerente ou financeiro têm acesso a esta página
- Usuários com perfil de filial não têm acesso a esta página nem às suas APIs
- Todas as operações são registradas para auditoria
- Validações são realizadas tanto no frontend quanto no backend para garantir a integridade dos dados
- O acesso é controlado tanto no nível da interface (rotas) quanto no nível da API (endpoints)
