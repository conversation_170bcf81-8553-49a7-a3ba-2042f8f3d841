# Exemplos de Código para Modais na Página de Gerenciamento de Vínculos

Este documento contém exemplos de código para implementação e manipulação de modais na página de gerenciamento de vínculos do Sistema de Gestão Tradição.

## Estrutura HTML de um Modal

```html
<!-- Modal: Adicionar Vínculo Prestadora-Filial -->
<div class="modal fade" id="addProviderBranchModal" tabindex="-1" aria-labelledby="addProviderBranchModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProviderBranchModalLabel">Novo Vínculo: Prestadora e Filial</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
            </div>
            <div class="modal-body">
                <form id="providerBranchForm">
                    <div class="mb-3">
                        <label for="modalProviderSelect" class="form-label">Prestadora</label>
                        <select id="modalProviderSelect" class="form-select provider-dropdown" required>
                            <option value="">Selecione uma Prestadora</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="modalBranchSelect" class="form-label">Filial</label>
                        <select id="modalBranchSelect" class="form-select branch-dropdown" required>
                            <option value="">Selecione uma Filial</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-shell-dark text-white" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn-shell-yellow" onclick="document.getElementById('providerBranchForm').dispatchEvent(new Event('submit'))">Salvar</button>
            </div>
        </div>
    </div>
</div>
```

## Função para Exibir um Modal

```javascript
/**
 * Exibe o modal para adicionar um vínculo entre prestadora e filial
 */
function showAddProviderBranchModal() {
    // Resetar o formulário
    const form = document.getElementById('providerBranchForm');
    if (form) form.reset();

    // Exibir o modal
    const modal = new bootstrap.Modal(document.getElementById('addProviderBranchModal'));
    modal.show();
}
```

## Função para Limpar o Estado do Modal

```javascript
/**
 * Limpa o estado do modal e garante que a página não trave após o fechamento
 * @param {string} modalId - ID do modal a ser limpo
 */
function cleanupModal(modalId) {
    const modalElement = document.getElementById(modalId);
    if (!modalElement) return;
    
    const modal = bootstrap.Modal.getInstance(modalElement);
    if (modal) {
        modal.hide();
        
        // Garantir limpeza completa após o fechamento
        setTimeout(() => {
            // Remover backdrop manualmente se ainda existir
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
            
            // Limpar classes e estilos do body
            document.body.classList.remove('modal-open');
            document.body.style.removeProperty('padding-right');
            document.body.style.removeProperty('overflow');
            
            // Resetar formulários sem afetar os event listeners
            const forms = modalElement.querySelectorAll('form');
            forms.forEach(form => form.reset());
        }, 300);
    }
}
```

## Configuração do Evento `hidden.bs.modal`

```javascript
// Garantir que os modais sejam resetados ao fechar e que a página não trave
const modals = document.querySelectorAll('.modal');
modals.forEach(modalElement => {
    // Adicionar evento para quando o modal for fechado
    modalElement.addEventListener('hidden.bs.modal', function() {
        // Resetar formulários quando o modal for fechado
        const forms = this.querySelectorAll('form');
        forms.forEach(form => form.reset());
        
        // Limpar qualquer estado pendente
        setTimeout(() => {
            // Garantir que o backdrop seja removido completamente
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
            
            // Remover classes que o Bootstrap adiciona ao body
            document.body.classList.remove('modal-open');
            document.body.style.removeProperty('padding-right');
            document.body.style.removeProperty('overflow');
        }, 300);
    });
});
```

## Manipulação de Formulário com Limpeza de Modal

```javascript
/**
 * Manipula o envio do formulário de vínculo entre prestadora e filial
 */
function handleProviderBranchSubmit(event) {
    event.preventDefault();

    // Obter valores do formulário
    const providerId = document.getElementById('modalProviderSelect').value;
    const branchId = document.getElementById('modalBranchSelect').value;

    // Validação
    if (!providerId || !branchId) {
        showErrorMessage('Por favor, selecione uma prestadora e uma filial.');
        return;
    }

    // Mostrar indicador de carregamento
    showLoading(true);

    // Enviar requisição para criar vínculo
    fetch('/api/links/provider-branch', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            provider_id: parseInt(providerId),
            branch_id: parseInt(branchId)
        })
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.error || 'Erro ao criar vínculo');
            });
        }
        return response.json();
    })
    .then(data => {
        // Exibir mensagem de sucesso
        showSuccessMessage('Vínculo criado com sucesso!');

        // Recarregar os vínculos
        fetch('/api/links/provider-branch')
            .then(response => response.json())
            .then(data => {
                if (data && data.links) {
                    providerBranchLinks = data.links;
                    renderProviderBranchTable();
                }
            })
            .catch(error => {
                console.error('Erro ao recarregar vínculos:', error);
            });

        // Fechar o modal com limpeza adequada
        cleanupModal('addProviderBranchModal');
    })
    .catch(error => {
        console.error('Erro ao criar vínculo:', error);
        showErrorMessage(`Erro ao criar vínculo: ${error.message}`);
    })
    .finally(() => {
        showLoading(false);
    });
}
```

## Modal de Confirmação para Exclusão

```javascript
/**
 * Remove o vínculo entre uma prestadora e uma filial
 * @param {number} providerId - ID da prestadora
 * @param {number} branchId - ID da filial
 */
function unlinkProviderBranch(providerId, branchId) {
    console.log(`Solicitação para desvincular prestadora ${providerId} da filial ${branchId}`);

    // Configurar o modal de confirmação
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (!confirmDeleteBtn) return;

    // Remover qualquer listener anterior
    const newConfirmBtn = confirmDeleteBtn.cloneNode(true);
    confirmDeleteBtn.parentNode.replaceChild(newConfirmBtn, confirmDeleteBtn);

    // Adicionar novo listener
    newConfirmBtn.addEventListener('click', function() {
        // Mostrar indicador de carregamento
        showLoading(true);

        // Enviar requisição para remover vínculo
        fetch(`/api/links/provider-branch/${providerId}/${branchId}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || 'Erro ao remover vínculo');
                });
            }
            return response.json();
        })
        .then(data => {
            // Exibir mensagem de sucesso
            showSuccessMessage('Vínculo removido com sucesso!');

            // Recarregar os vínculos
            fetch('/api/links/provider-branch')
                .then(response => response.json())
                .then(data => {
                    if (data && data.links) {
                        providerBranchLinks = data.links;
                        renderProviderBranchTable();
                    }
                })
                .catch(error => {
                    console.error('Erro ao recarregar vínculos:', error);
                });

            // Fechar o modal com limpeza adequada
            cleanupModal('confirmDeleteModal');
        })
        .catch(error => {
            console.error('Erro ao remover vínculo:', error);
            showErrorMessage(`Erro ao remover vínculo: ${error.message}`);
        })
        .finally(() => {
            showLoading(false);
        });
    });

    // Exibir o modal de confirmação
    const modal = new bootstrap.Modal(document.getElementById('confirmDeleteModal'));
    modal.show();
}
```

## Modal Dinâmico Criado via JavaScript

```javascript
/**
 * Exibe um modal com as filiais vinculadas a um técnico
 * @param {number} technicianId - ID do técnico
 */
function showTechnicianBranches(technicianId) {
    console.log(`Exibindo filiais vinculadas ao técnico ${technicianId}`);

    // Verificar se já existe um modal para isso
    let techBranchesModal = document.getElementById('technicianBranchesModal');

    // Se não existir, criar um
    if (!techBranchesModal) {
        const modalHtml = `
            <div class="modal fade" id="technicianBranchesModal" tabindex="-1" aria-labelledby="technicianBranchesModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="technicianBranchesModalLabel">Filiais Vinculadas ao Técnico</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                        </div>
                        <div class="modal-body">
                            <div id="technicianBranchesContent">
                                <div class="text-center">
                                    <div class="spinner-border text-warning" role="status">
                                        <span class="visually-hidden">Carregando...</span>
                                    </div>
                                    <p class="mt-2 text-warning">Carregando filiais, por favor aguarde...</p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn-shell-dark text-white" data-bs-dismiss="modal">Fechar</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Adicionar o modal ao corpo do documento
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        techBranchesModal = document.getElementById('technicianBranchesModal');
    }

    // Exibir o modal
    const modal = new bootstrap.Modal(techBranchesModal);
    modal.show();

    // Adicionar evento para limpar o modal quando for fechado
    techBranchesModal.addEventListener('hidden.bs.modal', function() {
        setTimeout(() => {
            // Remover backdrop manualmente se ainda existir
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }

            // Limpar classes e estilos do body
            document.body.classList.remove('modal-open');
            document.body.style.removeProperty('padding-right');
            document.body.style.removeProperty('overflow');
        }, 300);
    });

    // Carregar o conteúdo do modal (código omitido para brevidade)
}
```
