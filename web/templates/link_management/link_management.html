{{ define "link_management/link_management.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciamento de Relações - Rede Tradição Shell</title>

    <!-- CSS Base -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&family=Share+Tech+Mono&display=swap" rel="stylesheet">

    <!-- CSS Custom -->
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/shell-theme.css">
    <link rel="stylesheet" href="/static/css/link_management.css">
</head>
<body class="dark-theme shell-theme">
    <!-- Sidebar -->
    {{ template "layouts/sidebar.html" . }}

    <!-- Conteúdo Principal -->
    <div class="content-with-sidebar">
        <div class="main-content">
            <div class="link-management-container">
                <!-- Cabeçalho da Página -->
                <div class="page-header text-center">
                    <h1 class="text-white"><i class="fas fa-link me-2"></i>Gerenciamento de Relações</h1>
                    <p class="text-muted">Gerencie os vínculos entre filiais, prestadoras de serviço e técnicos</p>
                </div>

                <!-- Indicador de Carregamento -->
                <div id="loadingIndicator" class="text-center my-5" style="display: none;">
                    <div class="spinner-border text-warning" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <p class="mt-2 text-warning">Carregando dados, por favor aguarde...</p>
                </div>

                <!-- Abas de Navegação -->
                <ul class="nav nav-tabs custom-tabs" id="relationshipTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="provider-branch-tab" data-bs-toggle="tab" data-bs-target="#provider-branch" type="button" role="tab" aria-controls="provider-branch" aria-selected="true">
                            <i class="fas fa-building me-2 text-shell-yellow"></i>Prestadoras e Filiais
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="technician-branch-tab" data-bs-toggle="tab" data-bs-target="#technician-branch" type="button" role="tab" aria-controls="technician-branch" aria-selected="false">
                            <i class="fas fa-user-cog me-2 text-shell-yellow"></i>Técnicos e Filiais
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="provider-technician-tab" data-bs-toggle="tab" data-bs-target="#provider-technician" type="button" role="tab" aria-controls="provider-technician" aria-selected="false">
                            <i class="fas fa-users-cog me-2 text-shell-yellow"></i>Prestadoras e Técnicos
                        </button>
                    </li>
                </ul>

                <!-- Conteúdo das Abas -->
                <div class="tab-content" id="relationshipTabsContent">
                    <!-- Aba: Prestadoras e Filiais -->
                    <div class="tab-pane fade show active" id="provider-branch" role="tabpanel" aria-labelledby="provider-branch-tab">
                        <div class="card-shell mt-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h3 class="m-0 text-center flex-grow-1">Vínculos entre Prestadoras e Filiais</h3>
                                <button id="addProviderBranchBtn" class="btn-shell-yellow" onclick="showAddProviderBranchModal()">
                                    <i class="fas fa-plus me-2"></i>Novo Vínculo
                                </button>
                            </div>
                            <div class="card-body">
                                <!-- Filtros -->
                                <div class="filter-section mb-4">
                                    <div class="row">
                                        <div class="col-md-5">
                                            <label for="providerFilter" class="form-label">Filtrar por Prestadora</label>
                                            <select id="providerFilter" class="form-select provider-dropdown">
                                                <option value="">Todas as Prestadoras</option>
                                            </select>
                                        </div>
                                        <div class="col-md-5">
                                            <label for="branchFilter" class="form-label">Filtrar por Filial</label>
                                            <select id="branchFilter" class="form-select branch-dropdown">
                                                <option value="">Todas as Filiais</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2 d-flex align-items-end">
                                            <button id="clearProviderBranchFilters" class="btn-shell-dark w-100 text-white">
                                                <i class="fas fa-filter-circle-xmark me-2"></i>Limpar Filtros
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Tabela de Vínculos -->
                                <div class="table-responsive">
                                    <table class="table-shell">
                                        <thead>
                                            <tr>
                                                <th class="text-white">Prestadora</th>
                                                <th class="text-white">Filial</th>
                                                <th class="text-white">Data de Vínculo</th>
                                                <th class="text-white">Ações</th>
                                            </tr>
                                        </thead>
                                        <tbody id="providerBranchTableBody">
                                            <!-- Conteúdo será preenchido via JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Aba: Técnicos e Filiais -->
                    <div class="tab-pane fade" id="technician-branch" role="tabpanel" aria-labelledby="technician-branch-tab">
                        <div class="card-shell mt-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h3 class="m-0 text-center flex-grow-1">Vínculos entre Técnicos e Filiais</h3>
                                <button id="addTechnicianBranchBtn" class="btn-shell-yellow" onclick="showAddTechnicianBranchModal()">
                                    <i class="fas fa-plus me-2"></i>Novo Vínculo
                                </button>
                            </div>
                            <div class="card-body">
                                <!-- Filtros -->
                                <div class="filter-section mb-4">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label for="technicianFilter" class="form-label">Filtrar por Técnico</label>
                                            <select id="technicianFilter" class="form-select technician-dropdown">
                                                <option value="">Todos os Técnicos</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="branchTechFilter" class="form-label">Filtrar por Filial</label>
                                            <select id="branchTechFilter" class="form-select branch-dropdown">
                                                <option value="">Todas as Filiais</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="specialtyFilter" class="form-label">Filtrar por Especialidade</label>
                                            <select id="specialtyFilter" class="form-select specialty-dropdown">
                                                <option value="">Todas as Especialidades</option>
                                            </select>
                                        </div>
                                        <div class="col-md-1 d-flex align-items-end">
                                            <button id="clearTechnicianBranchFilters" class="btn-shell-dark w-100 text-white">
                                                <i class="fas fa-filter-circle-xmark"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Tabela de Vínculos -->
                                <div class="table-responsive">
                                    <table class="table-shell">
                                        <thead>
                                            <tr>
                                                <th class="text-white">Técnico</th>
                                                <th class="text-white">Filial</th>
                                                <th class="text-white">Especialidade</th>
                                                <th class="text-white">Data de Vínculo</th>
                                                <th class="text-white">Ações</th>
                                            </tr>
                                        </thead>
                                        <tbody id="technicianBranchTableBody">
                                            <!-- Conteúdo será preenchido via JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Aba: Prestadoras e Técnicos -->
                    <div class="tab-pane fade" id="provider-technician" role="tabpanel" aria-labelledby="provider-technician-tab">
                        <div class="card-shell mt-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h3 class="m-0 text-center w-100">Técnicos por Prestadora</h3>
                                <button id="addProviderTechnicianBtn" class="btn-shell-yellow" onclick="showAddProviderTechnicianModal()">
                                    <i class="fas fa-plus me-2"></i>Novo Vínculo
                                </button>
                            </div>
                            <div class="card-body">
                                <!-- Filtros -->
                                <div class="filter-section mb-4">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label for="providerTechFilter" class="form-label">Selecionar Prestadora</label>
                                            <select id="providerTechFilter" class="form-select provider-dropdown">
                                                <option value="">Todas as Prestadoras</option>
                                                <option value="no_technicians">Prestadoras sem técnicos</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="technicianProviderFilter" class="form-label">Selecionar Técnico</label>
                                            <select id="technicianProviderFilter" class="form-select technician-dropdown">
                                                <option value="">Todos os Técnicos</option>
                                                <option value="no_provider">Técnicos sem prestadora</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label for="relationshipFilter" class="form-label">Tipo de Relação</label>
                                            <select id="relationshipFilter" class="form-select">
                                                <option value="">Todas as Relações</option>
                                                <option value="with_branch">Com vínculo a filial</option>
                                                <option value="without_branch">Sem vínculo a filial</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2 d-flex align-items-end">
                                            <button id="clearProviderTechFilter" class="btn-shell-dark w-100 text-white">
                                                <i class="fas fa-filter-circle-xmark me-2"></i>Limpar
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Tabela de Técnicos -->
                                <div class="table-responsive">
                                    <table class="table-shell">
                                        <thead>
                                            <tr>
                                                <th class="text-white">Técnico</th>
                                                <th class="text-white">Email</th>
                                                <th class="text-white">Telefone</th>
                                                <th class="text-white">Filiais Atendidas</th>
                                            </tr>
                                        </thead>
                                        <tbody id="providerTechnicianTableBody">
                                            <!-- Conteúdo será preenchido via JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: Adicionar Vínculo Prestadora-Filial -->
    <div class="modal fade" id="addProviderBranchModal" tabindex="-1" aria-labelledby="addProviderBranchModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addProviderBranchModalLabel">Novo Vínculo: Prestadora e Filial</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form id="providerBranchForm">
                        <div class="mb-3">
                            <label for="modalProviderSelect" class="form-label">Prestadora</label>
                            <select id="modalProviderSelect" class="form-select provider-dropdown" required>
                                <option value="">Selecione uma Prestadora</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="modalBranchSelect" class="form-label">Filial</label>
                            <select id="modalBranchSelect" class="form-select branch-dropdown" required>
                                <option value="">Selecione uma Filial</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-shell-dark text-white" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="providerBranchForm" class="btn-shell-yellow">Salvar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: Adicionar Vínculo Técnico-Filial -->
    <div class="modal fade" id="addTechnicianBranchModal" tabindex="-1" aria-labelledby="addTechnicianBranchModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addTechnicianBranchModalLabel">Novo Vínculo: Técnico e Filial</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form id="technicianBranchForm">
                        <div class="mb-3">
                            <label for="modalTechnicianSelect" class="form-label">Técnico</label>
                            <select id="modalTechnicianSelect" class="form-select technician-dropdown" required>
                                <option value="">Selecione um Técnico</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="modalBranchTechSelect" class="form-label">Filial</label>
                            <select id="modalBranchTechSelect" class="form-select branch-dropdown" required>
                                <option value="">Selecione uma Filial</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="modalSpecialtySelect" class="form-label">Especialidade</label>
                            <select id="modalSpecialtySelect" class="form-select specialty-dropdown" required>
                                <option value="">Selecione uma Especialidade</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-shell-dark text-white" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="technicianBranchForm" class="btn-shell-yellow">Salvar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: Adicionar Vínculo Prestadora-Técnico -->
    <div class="modal fade" id="addProviderTechnicianModal" tabindex="-1" aria-labelledby="addProviderTechnicianModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addProviderTechnicianModalLabel">Novo Vínculo: Prestadora e Técnico</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form id="providerTechnicianForm">
                        <div class="mb-3">
                            <label for="modalProviderTechSelect" class="form-label">Prestadora</label>
                            <select id="modalProviderTechSelect" class="form-select provider-dropdown" required>
                                <option value="">Selecione uma Prestadora</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="modalTechnicianProviderSelect" class="form-label">Técnico</label>
                            <select id="modalTechnicianProviderSelect" class="form-select technician-dropdown" required>
                                <option value="">Selecione um Técnico</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="inheritBranchLinks" checked>
                                <label class="form-check-label text-white" for="inheritBranchLinks">
                                    Herdar vínculos de filiais da prestadora
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-shell-dark text-white" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="providerTechnicianForm" class="btn-shell-yellow">Salvar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Confirmação para Exclusão -->
    <div class="modal fade" id="confirmDeleteModal" tabindex="-1" aria-labelledby="confirmDeleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmDeleteModalLabel">Confirmar Exclusão</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <p class="text-white">Tem certeza que deseja remover este vínculo? Esta ação não pode ser desfeita.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-shell-dark text-white" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" id="confirmDeleteBtn" class="btn-shell-red">Confirmar Exclusão</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts Base -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Scripts Custom -->
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/link_management.js"></script>
</body>
</html>
{{ end }}
