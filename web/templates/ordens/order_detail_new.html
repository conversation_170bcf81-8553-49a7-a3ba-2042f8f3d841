{{ define "ordens/order_detail_new.html" }}
{{ block "page_styles" . }}
<link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<link rel="stylesheet" href="/static/css/dashboard.css">
<link rel="stylesheet" href="/static/css/order_detail_new.css">
{{ end }}

{{ block "content" . }}
<div class="container-fluid py-4">
    <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> da Página -->
    <div class="order-detail-header">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div>
                <a href="/orders" class="back-button">
                    <i class="fas fa-arrow-left me-2"></i> Voltar
                </a>
            </div>
            <div>
                <span class="status-badge status-{{ .Order.Status }}">
                    <i class="fas fa-circle"></i> {{ formatStatus (printf "%s" .Order.Status) }}
                </span>
                <span class="priority-badge priority-{{ .Order.Priority }}">
                    <i class="fas fa-flag"></i> {{ formatPriority (printf "%s" .Order.Priority) }}
                </span>
            </div>
        </div>
        <h1 class="order-detail-title">Ordem #{{ .Order.ID }} - {{ .Order.Title }}</h1>
    </div>

    <!-- Primeira Linha de Cards -->
    <div class="row g-4 mb-4">
        <!-- Card de Detalhes Principais -->
        <div class="col-md-6">
            <div class="detail-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <h3 class="card-title">Detalhes Principais</h3>
                </div>
                <div class="card-content">
                    <ul class="info-list">
                        <li>
                            <i class="fas fa-clipboard-list"></i>
                            <span class="info-label">Descrição:</span>
                            <span>{{ if .Order.Description }}{{ .Order.Description }}{{ else }}-{{ end }}</span>
                        </li>
                        <li>
                            <i class="fas fa-map-marker-alt"></i>
                            <span class="info-label">Filial:</span>
                            <span>{{ if .Branch.Name }}{{ .Branch.Name }}{{ else }}N/A{{ end }}</span>
                        </li>
                        <li>
                            <i class="fas fa-wrench"></i>
                            <span class="info-label">Equipamento:</span>
                            <span>{{ if .Equipment.Name }}{{ .Equipment.Name }}{{ else }}N/A{{ end }} ({{ if .Equipment.Type }}{{ .Equipment.Type }}{{ else }}-{{ end }})</span>
                        </li>
                        <li>
                            <i class="fas fa-user"></i>
                            <span class="info-label">Criado por:</span>
                            <span>{{ if .CreatedUser.Name }}{{ .CreatedUser.Name }}{{ else }}N/A{{ end }} em {{ .Order.CreatedAt | formatDateTime }}</span>
                        </li>
                        <li>
                            <i class="fas fa-user-cog"></i>
                            <span class="info-label">Responsável:</span>
                            <span>
                                {{ if .Order.AssignedProviderID }}
                                    {{ .Provider.Name | defaultString "N/A" }}
                                {{ else }}
                                    <span class="text-muted">Não Atribuído</span>
                                {{ end }}
                            </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Card de Cronograma -->
        <div class="col-md-6">
            <div class="detail-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h3 class="card-title">Cronograma</h3>
                </div>
                <div class="card-content">
                    <ul class="timeline">
                        <li>
                            <i class="fas fa-flag"></i>
                            <strong>Criada</strong>
                            <span class="timeline-date">{{ .Order.CreatedAt | formatDateTime }}</span>
                        </li>
                        {{ if .Order.AssignedAt }}
                        <li>
                            <i class="fas fa-user-plus"></i>
                            <strong>Atribuída</strong>
                            <span class="timeline-date">{{ .Order.AssignedAt | formatDateTime }}</span>
                        </li>
                        {{ end }}
                        {{ if .Order.AcceptedAt }}
                        <li>
                            <i class="fas fa-play-circle"></i>
                            <strong>Aceita</strong>
                            <span class="timeline-date">{{ .Order.AcceptedAt | formatDateTime }}</span>
                        </li>
                        {{ end }}
                        {{ if .Order.SubmittedForApprovalAt }}
                        <li>
                            <i class="fas fa-paper-plane"></i>
                            <strong>Submetida</strong>
                            <span class="timeline-date">{{ .Order.SubmittedForApprovalAt | formatDateTime }}</span>
                        </li>
                        {{ end }}
                        {{ if .Order.ApprovedAt }}
                        <li>
                            <i class="fas fa-check-circle"></i>
                            <strong>Aprovada</strong>
                            <span class="timeline-date">{{ .Order.ApprovedAt | formatDateTime }}</span>
                        </li>
                        {{ end }}
                        {{ if .Order.InvoicedAt }}
                        <li>
                            <i class="fas fa-file-invoice-dollar"></i>
                            <strong>Faturada</strong>
                            <span class="timeline-date">{{ .Order.InvoicedAt | formatDateTime }}</span>
                        </li>
                        {{ end }}
                        {{ if .Order.PaidAt }}
                        <li>
                            <i class="fas fa-money-check-alt"></i>
                            <strong>Paga</strong>
                            <span class="timeline-date">{{ .Order.PaidAt | formatDateTime }}</span>
                        </li>
                        {{ end }}
                        {{ if .Order.CancelledAt }}
                        <li>
                            <i class="fas fa-ban"></i>
                            <strong>Cancelada</strong>
                            <span class="timeline-date">{{ .Order.CancelledAt | formatDateTime }}</span>
                        </li>
                        {{ end }}
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Segunda Linha de Cards -->
    <div class="row g-4 mb-4">
        <!-- Card de Custos -->
        <div class="col-md-6 col-lg-4">
            <div class="detail-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <h3 class="card-title">Custos</h3>
                </div>
                <div class="card-content text-center">
                    <p>Total Estimado/Aprovado:</p>
                    <h2 class="mb-3" id="orderTotalCostDisplay">{{ .Order.TotalCost | formatCurrency }}</h2>
                </div>
                <div class="card-footer">
                    {{ if and (eq .CurrentUser.Role "provider") (eq .CurrentUser.ID .Order.AssignedProviderID) (or (eq (printf "%s" .Order.Status) "in_progress") (eq (printf "%s" .Order.Status) "revision_needed")) }}
                        <button class="shell-btn shell-btn-yellow" data-order-id="{{ .Order.ID }}" onclick="openCostsModal(event)">
                            <i class="fas fa-plus"></i> Adicionar/Editar Custos
                        </button>
                    {{ else }}
                        <button class="shell-btn" data-order-id="{{ .Order.ID }}" onclick="viewCostsModal(event)">
                            <i class="fas fa-eye"></i> Ver Detalhes dos Custos
                        </button>
                    {{ end }}
                </div>
            </div>
        </div>

        <!-- Card de Histórico do Equipamento -->
        <div class="col-md-6 col-lg-4">
            <div class="detail-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <h3 class="card-title">Histórico</h3>
                </div>
                <div class="card-content">
                    <ul class="info-list">
                        <li>
                            <i class="fas fa-wrench"></i>
                            <span class="info-label">Equipamento:</span>
                            <span>{{ .Equipment.Name | defaultString "N/A" }}</span>
                        </li>
                        <li>
                            <i class="fas fa-calendar-check"></i>
                            <span class="info-label">Última Manutenção:</span>
                            <span>{{ .Equipment.LastMaintenanceDate | defaultString "N/A" }}</span>
                        </li>
                        <li>
                            <i class="fas fa-tools"></i>
                            <span class="info-label">Total de Manutenções:</span>
                            <span>{{ .Equipment.MaintenanceCount | default 0 }}</span>
                        </li>
                    </ul>
                </div>
                <div class="card-footer">
                    <a href="/equipment/{{ .Equipment.ID }}/history" class="shell-btn">
                        <i class="fas fa-eye"></i> Ver Histórico Completo
                    </a>
                </div>
            </div>
        </div>

        <!-- Card de Interações -->
        <div class="col-md-6 col-lg-4">
            <div class="detail-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3 class="card-title">Interações</h3>
                </div>
                <div class="card-content" style="max-height: 200px; overflow-y: auto;">
                    {{ if .Order.Interactions }}
                        {{ range .Order.Interactions }}
                        <div class="interaction-item">
                            <div class="interaction-message">{{ .Message }}</div>
                            <div class="interaction-meta">
                                <i class="fas fa-user"></i> {{ .User.Name | defaultString "Sistema" }} - {{ .Timestamp.Format "02/01/06 15:04" }}
                            </div>
                        </div>
                        {{ end }}
                    {{ else }}
                        <p class="text-muted text-center">Nenhuma interação registrada.</p>
                    {{ end }}
                </div>
                <div class="card-footer">
                    {{ if and (ne (printf "%s" .Order.Status) "paid") (ne (printf "%s" .Order.Status) "cancelled") }}
                        <button class="shell-btn shell-btn-yellow" data-order-id="{{ .Order.ID }}" onclick="openInteractionModal(event)">
                            <i class="fas fa-plus"></i> Adicionar Comentário
                        </button>
                    {{ else }}
                        <span class="text-muted">(Interações bloqueadas)</span>
                    {{ end }}
                </div>
            </div>
        </div>
    </div>

    <!-- Seção de Ações -->
    <div class="actions-section">
        <h3 class="actions-title">Ações Disponíveis</h3>
        <div class="action-buttons">
            {{/* Ações da Filial */}}
            {{ if eq .CurrentUser.Role "branch" }}
                {{ if and (eq (printf "%s" .Order.Status) "pending") (not .Order.AssignedProviderID) }}
                    <button class="shell-btn shell-btn-yellow" data-order-id="{{ .Order.ID }}" onclick="openAssignModal(event)">
                        <i class="fas fa-user-plus"></i> Atribuir Prestador
                    </button>
                {{ end }}
                {{ if and (ne (printf "%s" .Order.Status) "cancelled") (ne (printf "%s" .Order.Status) "paid") (ne (printf "%s" .Order.Status) "invoiced") }}
                    <button class="shell-btn shell-btn-red" data-order-id="{{ .Order.ID }}" onclick="cancelOrder(event)">
                        <i class="fas fa-ban"></i> Cancelar Ordem
                    </button>
                {{ end }}
            {{ end }}

            {{/* Ações do Prestador */}}
            {{ if and (eq .CurrentUser.Role "provider") (eq .CurrentUser.ID .Order.AssignedProviderID) }}
                {{ if eq (printf "%s" .Order.Status) "pending" }}
                    <button class="shell-btn shell-btn-green" data-order-id="{{ .Order.ID }}" data-new-status="in_progress" onclick="updateOrderStatusHandler(event)">
                        <i class="fas fa-check"></i> Aceitar Ordem
                    </button>
                    <button class="shell-btn shell-btn-red" data-order-id="{{ .Order.ID }}" data-new-status="rejected_provider" onclick="updateOrderStatusHandler(event)">
                        <i class="fas fa-times"></i> Recusar Ordem
                    </button>
                {{ end }}
                {{ if eq (printf "%s" .Order.Status) "in_progress" }}
                    <button class="shell-btn shell-btn-yellow" data-order-id="{{ .Order.ID }}" onclick="submitForApproval(event)">
                        <i class="fas fa-paper-plane"></i> Enviar p/ Aprovação
                    </button>
                {{ end }}
                {{ if eq (printf "%s" .Order.Status) "revision_needed" }}
                    <button class="shell-btn shell-btn-yellow" data-order-id="{{ .Order.ID }}" onclick="submitForApproval(event)">
                        <i class="fas fa-paper-plane"></i> Reenviar p/ Aprovação
                    </button>
                {{ end }}
                {{ if eq (printf "%s" .Order.Status) "approved" }}
                    <button class="shell-btn" data-order-id="{{ .Order.ID }}" onclick="openInvoiceModal(event)">
                        <i class="fas fa-file-invoice-dollar"></i> Anexar Nota Fiscal
                    </button>
                {{ end }}
            {{ end }}

            {{/* Ações do Gerente/Financeiro */}}
            {{ if or (eq .CurrentUser.Role "manager") (eq .CurrentUser.Role "finance") }}
                {{ if eq (printf "%s" .Order.Status) "pending_approval" }}
                    <button class="shell-btn shell-btn-green" data-order-id="{{ .Order.ID }}" onclick="approveOrder(event)">
                        <i class="fas fa-check-circle"></i> Aprovar
                    </button>
                    <button class="shell-btn shell-btn-red" data-order-id="{{ .Order.ID }}" onclick="rejectOrder(event)">
                        <i class="fas fa-times-circle"></i> Reprovar
                    </button>
                {{ end }}
                {{ if and (eq .CurrentUser.Role "finance") (eq (printf "%s" .Order.Status) "invoiced") }}
                    <button class="shell-btn shell-btn-green" data-order-id="{{ .Order.ID }}" onclick="markAsPaid(event)">
                        <i class="fas fa-money-check-alt"></i> Marcar como Pago
                    </button>
                {{ end }}
                <button class="shell-btn shell-btn-grey" data-order-id="{{ .Order.ID }}" onclick="printOrder(event)">
                    <i class="fas fa-print"></i> Imprimir
                </button>
            {{ end }}
        </div>
    </div>
</div>

{{/* Modais */}}
{{ template "modals/order_costs_modal.html" . }}
{{ template "modals/order_interaction_modal.html" . }}
{{ template "modals/order_assign_provider_modal.html" . }}
{{ template "modals/order_invoice_modal.html" . }}

{{/* Container de Toasts */}}
<div aria-live="polite" aria-atomic="true" class="position-fixed top-0 end-0 p-3" style="z-index: 1055">
    <div id="toastPlacement" class="toast-container"></div>
</div>

{{ end }}

{{/* JavaScript */}}
{{ block "page_scripts" . }}
<script>
    // Variáveis globais necessárias para o JavaScript
    const CURRENT_ORDER_ID = {{ printf "%d" (.Order.ID | default 0) }};
    const CURRENT_USER_ROLE = {{ printf "%q" (.CurrentUser.Role | default "") }};
    const CURRENT_USER_ID = {{ printf "%d" (.CurrentUser.ID | default 0) }};
    const ASSIGNED_PROVIDER_ID = {{ printf "%d" (.Order.AssignedProviderID | default 0) }};
    const CURRENT_ORDER_STATUS = {{ printf "%q" (printf "%s" .Order.Status) }};
</script>
<script src="/static/js/order_detail_new.js"></script>
{{ end }}
{{ end }}
