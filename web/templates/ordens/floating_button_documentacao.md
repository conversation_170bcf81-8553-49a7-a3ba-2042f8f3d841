# Documentação do Botão Flutuante - Página de Ordens

## Visão Geral

O botão flutuante "Nova Ordem" foi implementado na página de ordens para proporcionar uma experiência de usuário mais moderna e intuitiva. Este componente segue o padrão de design Material Design conhecido como FAB (Floating Action Button), que é amplamente utilizado em interfaces modernas.

## Implementação

### HTML

O botão flutuante é implementado como um link `<a>` posicionado fora do fluxo normal da página, usando CSS para fixá-lo no canto inferior direito da tela.

```html
<!-- Botão flutuante Nova Ordem -->
<a href="/orders/create" class="floating-action-btn" onclick="window.location.href='/orders/create'; return false;">
    <i class="fas fa-plus"></i>
</a>
```

### CSS

O estilo do botão é definido no arquivo `web/static/css/orders_premium.css`:

```css
/* Botão flutuante */
.floating-action-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background-color: var(--shell-yellow);
    color: #000;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    transition: all 0.3s ease;
    text-decoration: none;
}

.floating-action-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
    color: #000;
}

.floating-action-btn:active {
    transform: scale(0.95);
}

/* Animação de pulso para feedback visual */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse-animation {
    animation: pulse 0.3s ease-in-out;
}
```

### JavaScript

O comportamento do botão é controlado por JavaScript no arquivo `web/templates/ordens/orders_premium.html`:

```javascript
document.addEventListener('DOMContentLoaded', function() {
    // Botão flutuante Nova Ordem
    const floatingBtn = document.querySelector('.floating-action-btn');
    if (floatingBtn) {
        floatingBtn.addEventListener('click', function(e) {
            e.preventDefault();
            // Adicionar efeito visual
            this.classList.add('pulse-animation');
            // Redirecionar após um pequeno delay
            setTimeout(() => {
                window.location.href = '/orders/create';
            }, 200);
        });
    }
});
```

## Responsividade

O botão flutuante é totalmente responsivo e funciona bem em todos os tamanhos de tela. Em dispositivos móveis, o botão mantém sua posição no canto inferior direito, proporcionando fácil acesso à ação de criar uma nova ordem.

## Acessibilidade

Para melhorar a acessibilidade, recomenda-se adicionar os seguintes atributos ao botão:

```html
<a href="/orders/create" class="floating-action-btn" 
   onclick="window.location.href='/orders/create'; return false;"
   aria-label="Criar nova ordem de serviço">
    <i class="fas fa-plus" aria-hidden="true"></i>
</a>
```

## Como Modificar

### Alterar a Cor

Para alterar a cor do botão, modifique a propriedade `background-color` na classe `.floating-action-btn`:

```css
.floating-action-btn {
    background-color: var(--sua-cor-aqui);
}
```

### Alterar o Ícone

Para alterar o ícone, substitua a classe do elemento `<i>` dentro do botão:

```html
<a href="/orders/create" class="floating-action-btn">
    <i class="fas fa-nome-do-icone"></i>
</a>
```

### Alterar o Tamanho

Para alterar o tamanho do botão, modifique as propriedades `width` e `height` na classe `.floating-action-btn`:

```css
.floating-action-btn {
    width: 70px;
    height: 70px;
}
```

### Alterar a Posição

Para alterar a posição do botão, modifique as propriedades `bottom` e `right` na classe `.floating-action-btn`:

```css
.floating-action-btn {
    bottom: 40px;
    right: 40px;
}
```

## Boas Práticas

1. **Mantenha o botão visível**: Certifique-se de que o botão não seja obscurecido por outros elementos da página.
2. **Use cores contrastantes**: O botão deve se destacar do fundo da página.
3. **Mantenha o ícone simples**: Use ícones intuitivos que comuniquem claramente a ação.
4. **Forneça feedback visual**: A animação de pulso ajuda o usuário a entender que sua ação foi registrada.
5. **Considere a acessibilidade**: Adicione atributos `aria-label` para usuários de leitores de tela.

## Conclusão

O botão flutuante "Nova Ordem" é um componente moderno e eficiente que melhora significativamente a experiência do usuário na página de ordens. Ele segue as melhores práticas de design de interface e proporciona uma maneira intuitiva de acessar a funcionalidade de criação de novas ordens.
