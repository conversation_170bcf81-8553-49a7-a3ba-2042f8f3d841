{{ define "ordens/orders_premium.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="theme-color" content="#1E1E1E">
    <meta name="description" content="Sistema de gerenciamento de ordens de serviço da Rede Tradição">
    <title>{{ .title }}</title>
    <link rel="icon" href="/static/images/favicon.ico" type="image/x-icon">

    <!-- Bootstrap e estilos base -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/orders_premium.css">

    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Incluindo o menu lateral independente -->
    {{ template "sidebar" . }}

    <!-- Botão flutuante Nova Ordem -->
    <a href="/orders/create" class="floating-action-btn" onclick="window.location.href='/orders/create'; return false;">
        <i class="fas fa-plus"></i>
    </a>

    <!-- Conteúdo principal -->
    <div class="content-with-sidebar">
        <div class="main-content">
            <!-- Cabeçalho da página -->
            <div class="page-header d-flex justify-content-between align-items-center mb-4">
                <div class="content">
                    <h1 class="page-title">
                        <i class="fas fa-clipboard-list me-2"></i>Ordens de Serviço
                    </h1>
                    <p class="page-subtitle">Gerencie todas as ordens de serviço do sistema</p>
                </div>
                <div class="header-actions">
                    <a href="javascript:void(0)" class="btn btn-outline-light" id="refreshBtn" data-bs-toggle="tooltip" title="Atualizar lista">
                        <i class="fas fa-sync-alt me-1"></i> Atualizar
                    </a>
                </div>
            </div>

            <!-- Métricas -->
            <div class="row metrics-container">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="metric-card pending fade-in delay-1">
                        <i class="fas fa-clock metric-icon"></i>
                        <div class="metric-title">Pendentes</div>
                        <div class="metric-value">{{ .PendingCount }}</div>
                        <div class="metric-percentage">
                            0%
                            <span class="metric-trend trend-up">
                                <i class="fas fa-arrow-up"></i> 5%
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="metric-card progress fade-in delay-2">
                        <i class="fas fa-spinner metric-icon"></i>
                        <div class="metric-title">Em Andamento</div>
                        <div class="metric-value">{{ .InProgressCount }}</div>
                        <div class="metric-percentage">
                            {{ if gt .TotalCount 0 }}
                                {{ printf "%.1f%%" (mul (div (float64 .InProgressCount) (float64 .TotalCount)) 100) }}
                                <span class="metric-trend trend-up">
                                    <i class="fas fa-arrow-up"></i> 8%
                                </span>
                            {{ else }}
                                0%
                            {{ end }}
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="metric-card completed fade-in delay-3">
                        <i class="fas fa-check-circle metric-icon"></i>
                        <div class="metric-title">Concluídas</div>
                        <div class="metric-value">{{ .CompletedCount }}</div>
                        <div class="metric-percentage">
                            {{ if gt .TotalCount 0 }}
                                {{ printf "%.1f%%" (mul (div (float64 .CompletedCount) (float64 .TotalCount)) 100) }}
                                <span class="metric-trend trend-up">
                                    <i class="fas fa-arrow-up"></i> 12%
                                </span>
                            {{ else }}
                                0%
                            {{ end }}
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="metric-card total fade-in delay-4">
                        <i class="fas fa-clipboard-list metric-icon"></i>
                        <div class="metric-title">Total de Ordens</div>
                        <div class="metric-value" id="totalCount">{{ .TotalCount }}</div>
                        <div class="metric-percentage">
                            100%
                            <span class="metric-trend trend-up">
                                <i class="fas fa-arrow-up"></i> 15%
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filtros -->
            <div class="filters-section fade-in">
                <div class="filters-title">
                    <i class="fas fa-filter"></i> Filtros
                </div>
                <div class="row mb-3">
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="input-group">
                            <span class="input-group-text bg-dark border-secondary text-light">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" id="searchInput" placeholder="Buscar por título, ID...">
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="input-group">
                            <span class="input-group-text bg-dark border-secondary text-light">
                                <i class="fas fa-tag"></i>
                            </span>
                            <select class="form-select" id="statusFilter">
                                <option value="all">Todos os Status</option>
                                {{ range $status, $label := .StatusOptions }}
                                    <option value="{{ $status }}">{{ $label }}</option>
                                {{ end }}
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="input-group">
                            <span class="input-group-text bg-dark border-secondary text-light">
                                <i class="fas fa-flag"></i>
                            </span>
                            <select class="form-select" id="priorityFilter">
                                <option value="all">Todas as Prioridades</option>
                                {{ range $prio, $label := .PriorityOptions }}
                                    <option value="{{ $prio }}">{{ $label }}</option>
                                {{ end }}
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-6 mb-3">
                        <a href="javascript:void(0)" class="btn btn-outline-light w-100" id="clearFiltersBtn">
                            <i class="fas fa-times me-1"></i> Limpar
                        </a>
                    </div>
                </div>
            </div>

            <!-- Tabela de Ordens -->
            <div class="orders-table-container fade-in">
                <div class="table-title">
                    <div>
                        <i class="fas fa-list"></i> Lista de Ordens
                    </div>
                    <div class="btn-group">
                        <a href="javascript:void(0)" class="btn btn-sm btn-outline-light active" id="tableViewBtn">
                            <i class="fas fa-table"></i> Tabela
                        </a>
                        <a href="javascript:void(0)" class="btn btn-sm btn-outline-light" id="cardViewBtn">
                            <i class="fas fa-th-large"></i> Cards
                        </a>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="orders-table">
                        <thead>
                            <tr>
                                <th style="width: 60px">ID</th>
                                <th>Título</th>
                                <th style="width: 130px">Status</th>
                                <th style="width: 110px">Prioridade</th>
                                <th style="width: 100px">Data</th>
                                <th style="width: 110px">Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {{ if .Orders }}
                                {{ range .Orders }}
                                <tr class="order-row" data-id="{{ .ID }}" data-status="{{ .Status }}" data-priority="{{ .Priority }}" data-date="{{ formatDate .CreatedAt }}">
                                    <td>
                                        <span class="order-id">#{{ .ID }}</span>
                                    </td>
                                    <td>
                                        <div class="order-title">{{ .Title }}</div>
                                        <div class="order-description">{{ .Description }}</div>
                                    </td>
                                    <td>
                                        <span class="status-badge status-pending">
                                            <i class="fas fa-clock"></i>
                                            Pendente
                                        </span>
                                    </td>
                                    <td>
                                        <span class="priority-badge priority-low">
                                            <i class="fas fa-arrow-down"></i>
                                            Baixa
                                        </span>
                                    </td>
                                    <td>{{ formatDate .CreatedAt }}</td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="/orders/{{ .ID }}" class="action-btn btn-view" data-bs-toggle="tooltip" title="Ver Detalhes">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="/orders/{{ .ID }}/edit" class="action-btn btn-edit" data-bs-toggle="tooltip" title="Editar">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="/orders/{{ .ID }}/delete" class="action-btn btn-delete" data-bs-toggle="tooltip" title="Excluir">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {{ end }}
                            {{ else }}
                                <tr>
                                    <td colspan="6">
                                        <div class="empty-state-container">
                                            <div class="empty-state">
                                                <div class="empty-state-icon">
                                                    <i class="fas fa-clipboard"></i>
                                                </div>
                                                <h5 class="empty-state-title">Nenhuma ordem encontrada</h5>
                                                <p class="empty-state-text">Não existem ordens de serviço cadastradas no sistema ou nenhuma corresponde aos filtros aplicados.</p>
                                                <form action="/orders/create" method="get" style="display: inline;">
                                                    <button type="submit" class="btn btn-shell-yellow">
                                                        <i class="fas fa-plus-circle me-2"></i> Criar Nova Ordem
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            {{ end }}
                        </tbody>
                    </table>
                </div>

                <!-- Paginação -->
                {{ if gt .TotalCount 10 }}
                <div class="pagination-container">
                    <div class="pagination-info">
                        Mostrando <span id="filteredCount">{{ len .Orders }}</span> de <span>{{ .TotalCount }}</span> ordens
                    </div>
                    <nav aria-label="Paginação">
                        <ul class="pagination">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item">
                                <a class="page-link" href="#">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
                {{ end }}
            </div>
        </div>
    </div>

    <!-- Estilos para notificações -->
    <style>
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            background: rgba(30, 30, 30, 0.9);
            border-left: 4px solid var(--shell-yellow);
            border-radius: 6px;
            padding: 15px;
            display: flex;
            align-items: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            transform: translateX(350px);
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 9999;
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification-success {
            border-left-color: var(--status-completed);
        }

        .notification-error {
            border-left-color: var(--status-cancelled);
        }

        .notification-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            color: var(--shell-yellow);
        }

        .notification-success .notification-icon {
            color: var(--status-completed);
        }

        .notification-error .notification-icon {
            color: var(--status-cancelled);
        }

        .notification-content {
            flex: 1;
        }

        .notification-content p {
            margin: 0;
            color: white;
        }

        .notification-close {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .notification-close:hover {
            color: white;
        }
    </style>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/orders_premium.js"></script>

    <!-- Script específico para os botões Nova Ordem -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Botão flutuante Nova Ordem
            const floatingBtn = document.querySelector('.floating-action-btn');
            if (floatingBtn) {
                floatingBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    // Adicionar efeito visual
                    this.classList.add('pulse-animation');
                    // Redirecionar após um pequeno delay
                    setTimeout(() => {
                        window.location.href = '/orders/create';
                    }, 200);
                });
            }

            // O botão Nova Ordem no cabeçalho foi removido
            // Agora usamos apenas o botão flutuante
        });
    </script>
</body>
</html>
{{ end }}
