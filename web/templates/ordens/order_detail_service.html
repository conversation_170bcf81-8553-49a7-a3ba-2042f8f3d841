{{ define "ordens/order_detail_service.html" }}
{{ block "page_styles" . }}
<style>
    /* Estilos inline para garantir que sejam aplicados */
    .order-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        border: 2px solid #FDB813;
        border-radius: 15px;
        background-color: #1E1E1E;
    }

    .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #FDB813;
    }

    .header-left {
        flex: 1;
    }

    .header-center {
        flex: 2;
        text-align: center;
    }

    .order-title {
        color: white;
        font-size: 1.8rem;
        margin: 0;
        font-weight: 600;
    }

    .back-button {
        background-color: #ED1C24;
        color: white;
        border: none;
        border-radius: 5px;
        padding: 8px 15px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-weight: 600;
    }

    .details-label {
        color: #FDB813;
        font-weight: 600;
        margin-bottom: 5px;
    }
</style>
<link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<link rel="stylesheet" href="/static/css/dashboard.css">
<link rel="stylesheet" href="/static/css/order_detail_service.css">
{{ end }}

{{ block "content" . }}
<div class="container-fluid py-4">
    <div class="order-container">
        <!-- Cabeçalho da Página -->
        <div class="order-header">
            <div class="header-left">
                <a href="/orders" class="back-button">
                    <i class="fas fa-arrow-left"></i> Voltar às Ordens
                </a>
            </div>
            <div class="header-center">
                <h1 class="order-title">Ordem #{{ .Order.ID }} - {{ .Order.Title }}</h1>
            </div>
            <div class="header-right">
                <span class="status-badge status-{{ .Order.Status }}">
                    <i class="fas fa-circle"></i> {{ .Order.Status }}
                </span>
                <span class="priority-badge priority-{{ .Order.Priority }}">
                    <i class="fas fa-flag"></i> {{ .Order.Priority }}
                </span>
            </div>
        </div>

        <!-- Conteúdo Principal -->
        <div class="order-main-content">
            <!-- Seção de Título e Detalhes -->
            <div class="maintenance-title-section">
                <h2 class="maintenance-title">Detalhes da Manutenção</h2>
                <div class="maintenance-details">
                    <div class="details-text">
                        {{ .Order.Description }}
                    </div>
                </div>
                <div class="maintenance-meta">
                    <div class="meta-item">
                        <span class="meta-label">Filial</span>
                        <div class="meta-value">
                            <i class="fas fa-map-marker-alt"></i>
                            {{ .Branch.Name }}
                        </div>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">Equipamento</span>
                        <div class="meta-value">
                            <i class="fas fa-tools"></i>
                            {{ .Equipment.Name }}
                        </div>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">Data de Criação</span>
                        <div class="meta-value">
                            <i class="fas fa-calendar-alt"></i>
                            {{ .Order.CreatedAt.Format "02/01/2006" }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="order-sidebar">
                <div class="sidebar-section">
                    <h3 class="sidebar-title">Responsável</h3>
                    <div class="responsible-info">
                        <i class="fas fa-user-circle"></i>
                        <div>
                            {{ if .AssignedTo }}
                                {{ .AssignedTo.Name }}
                            {{ else }}
                                Não atribuído
                            {{ end }}
                        </div>
                    </div>
                </div>
                <div class="sidebar-section">
                    <h3 class="sidebar-title">Status</h3>
                    <div class="status-badge status-{{ .Order.Status }}" style="width: 100%;">
                        {{ .Order.Status }}
                    </div>
                </div>
                <div class="sidebar-section">
                    <h3 class="sidebar-title">Tempo Estimado</h3>
                    <div class="time-estimate">
                        <i class="fas fa-clock"></i>
                        <span>{{ if .Order.EstimatedTime }}{{ .Order.EstimatedTime }}{{ else }}2 horas{{ end }}</span>
                    </div>
                </div>
                <div class="sidebar-section">
                    <button class="print-button" onclick="window.print()">
                        <i class="fas fa-print"></i> Imprimir Ordem
                    </button>
                </div>
                <div class="sidebar-section">
                    <div class="action-buttons">
                        {{ if eq .Order.Status "pending_approval" }}
                            <button class="action-button approve-button" onclick="approveOrder()">
                                <i class="fas fa-check"></i> Aprovar
                            </button>
                            <button class="action-button reject-button" onclick="rejectOrder()">
                                <i class="fas fa-times"></i> Rejeitar
                            </button>
                        {{ end }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Cards de Categorias -->
        <div class="order-details-section">
            <h3 class="details-section-title">Categorias</h3>
            <div class="order-cards">
                <!-- Card: Histórico -->
                <div class="order-card" onclick="showEquipmentHistory()">
                    <div class="card-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <div class="card-title">Histórico</div>
                    <div class="card-content">
                        <p>Equipamento: {{ .Equipment.Name }}</p>
                        <p class="clickable-text">Ver histórico completo</p>
                    </div>
                </div>

                <!-- Card: Custos -->
                <div class="order-card" onclick="openCostsModal()">
                    <div class="card-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="card-title">Custos</div>
                    <div class="card-content">
                        <div class="cost-total">R$ {{ .Order.TotalCost }}</div>
                        <p class="clickable-text">Adicionar/Ver custos</p>
                    </div>
                </div>

                <!-- Card: Interação -->
                <div class="order-card" onclick="openInteractionModal()">
                    <div class="card-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="card-title">Interação</div>
                    <div class="card-content">
                        <p class="interaction-prompt">Adicione comentários ou interações</p>
                        <p class="clickable-text">Adicionar interação</p>
                    </div>
                </div>

                <!-- Card: Cronograma -->
                <div class="order-card" onclick="viewSchedule()">
                    <div class="card-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="card-title">Cronograma</div>
                    <div class="card-content">
                        <div class="schedule-date">
                            <i class="fas fa-circle success"></i>
                            <span>Criação: {{ .Order.CreatedAt.Format "02/01/2006" }}</span>
                        </div>
                        {{ if .Order.AssignedAt }}
                        <div class="schedule-date">
                            <i class="fas fa-circle success"></i>
                            <span>Atribuição: {{ .Order.AssignedAt.Format "02/01/2006" }}</span>
                        </div>
                        {{ end }}
                        {{ if .Order.DueDate }}
                        <div class="schedule-date">
                            <i class="fas fa-circle pending"></i>
                            <span>Prazo: {{ .Order.DueDate.Format "02/01/2006" }}</span>
                        </div>
                        {{ end }}
                        <p class="clickable-text">Ver calendário</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{/* Modais */}}
{{ template "modals/order_costs_modal.html" . }}
{{ template "modals/order_interaction_modal.html" . }}
{{ template "modals/order_assign_provider_modal.html" . }}

{{/* Container de Toasts */}}
<div aria-live="polite" aria-atomic="true" class="position-fixed top-0 end-0 p-3" style="z-index: 1055">
    <div id="toastPlacement" class="toast-container"></div>
</div>

{{ end }}

{{/* JavaScript */}}
{{ block "page_scripts" . }}
<script>
    // Variáveis globais necessárias para o JavaScript
    const CURRENT_ORDER_ID = {{ printf "%d" (.Order.ID | default 0) }};
    const CURRENT_USER_ROLE = {{ printf "%q" (.CurrentUser.Role | default "") }};
    const CURRENT_USER_ID = {{ printf "%d" (.CurrentUser.ID | default 0) }};
    const ASSIGNED_PROVIDER_ID = {{ printf "%d" (.Order.AssignedProviderID | default 0) }};
    const CURRENT_ORDER_STATUS = {{ printf "%q" (printf "%s" .Order.Status) }};

    // Funções para interação com os cards
    function showEquipmentHistory() {
        window.location.href = `/equipment/{{ .Equipment.ID }}/history`;
    }

    function openCostsModal() {
        const costsModal = new bootstrap.Modal(document.getElementById('orderCostsModal'));
        costsModal.show();
    }

    function openInteractionModal() {
        const interactionModal = new bootstrap.Modal(document.getElementById('orderInteractionModal'));
        interactionModal.show();
    }

    function viewSchedule() {
        window.location.href = '/orders/calendar';
    }

    function approveOrder() {
        if (confirm('Tem certeza que deseja aprovar esta ordem?')) {
            fetch(`/api/orders/${CURRENT_ORDER_ID}/status`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    status: 'approved'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showToast('Erro ao aprovar ordem: ' + data.error, 'error');
                } else {
                    showToast('Ordem aprovada com sucesso!', 'success');
                    setTimeout(() => window.location.reload(), 1500);
                }
            })
            .catch(error => {
                showToast('Erro ao aprovar ordem: ' + error.message, 'error');
            });
        }
    }

    function rejectOrder() {
        const reason = prompt('Por favor, informe o motivo da rejeição:');
        if (reason) {
            fetch(`/api/orders/${CURRENT_ORDER_ID}/status`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    status: 'rejected',
                    reason: reason
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showToast('Erro ao rejeitar ordem: ' + data.error, 'error');
                } else {
                    showToast('Ordem rejeitada com sucesso!', 'success');
                    setTimeout(() => window.location.reload(), 1500);
                }
            })
            .catch(error => {
                showToast('Erro ao rejeitar ordem: ' + error.message, 'error');
            });
        }
    }

    // Função para exibir toasts
    function showToast(message, type = 'info') {
        const toastContainer = document.getElementById('toastPlacement');

        const toastEl = document.createElement('div');
        toastEl.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} border-0`;
        toastEl.setAttribute('role', 'alert');
        toastEl.setAttribute('aria-live', 'assertive');
        toastEl.setAttribute('aria-atomic', 'true');

        toastEl.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;

        toastContainer.appendChild(toastEl);

        const toast = new bootstrap.Toast(toastEl, {
            autohide: true,
            delay: 5000
        });

        toast.show();

        // Remover o toast do DOM após ser ocultado
        toastEl.addEventListener('hidden.bs.toast', function () {
            toastEl.remove();
        });
    }
</script>
<script src="/static/js/order_detail_service.js"></script>
{{ end }}
{{ end }}