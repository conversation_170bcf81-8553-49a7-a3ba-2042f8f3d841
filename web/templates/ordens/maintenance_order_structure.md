# Estrutura do Modelo de Ordem de Manutenção

## 1. <PERSON><PERSON> Principal (`MaintenanceOrder`)

```go
type MaintenanceOrder struct {
    // Campos de Identificação
    ID                 uint          
    Title              string        
    Description        string        
    
    // Status e Prioridade
    Status             OrderStatus   
    Priority           PriorityLevel 
    
    // IDs de Relacionamento
    BranchID           uint          
    CreatedByUserID    uint          
    AssignedProviderID *uint         
    ApprovedByUserID   *uint         
    CancelledByUserID  *uint         
    RejectedByUserID   *uint         
    
    // Informações do Equipamento
    Equipment          string        
    Location          string        
    
    // Campos de Motivo
    CancellationReason string        
    RejectionReason    string        
    
    // Informações Financeiras
    TotalCost          float64       
    
    // Datas de Controle
    ScheduledStartDate *time.Time    
    ScheduledEndDate   *time.Time    
    ActualStartDate    *time.Time    
    ActualEndDate      *time.Time    
    AcceptedAt         *time.Time    
    CompletedAt        *time.Time    
    PaidAt             *time.Time    
    InvoicedAt         *time.Time    
    CancelledAt        *time.Time    
    CreatedAt          time.Time     
    UpdatedAt          time.Time     
    
    // Relacionamentos
    Branch           *Branch       
    CreatedByUser    *User         
    AssignedProvider *User         
    ApprovedByUser   *User         
    CancelledByUser  *User         
    RejectedByUser   *User         
    CostItems        []CostItem    
    Interactions     []Interaction 
}
```

## 2. Status Possíveis

```go
const (
    StatusPendente           = "pending"
    StatusAprovada           = "approved"
    StatusEmAndamento        = "in_progress"
    StatusPendenteAprovacao  = "pending_approval"
    StatusRevisaoNecessaria  = "review_needed"
    StatusConcluida          = "completed"
    StatusCancelada          = "cancelled"
    StatusRejeitada          = "rejected"
    StatusRejeitadaPrestador = "provider_rejected"
    StatusPaga               = "paid"
    StatusFaturada           = "invoiced"
)
```

## 3. Níveis de Prioridade

```go
const (
    PriorityBaixa   = "low"
    PriorityMedia   = "medium"
    PriorityAlta    = "high"
    PriorityUrgente = "urgent"
)
```

## 4. Papéis de Usuário

```go
const (
    RoleAdmin      = "admin"
    RoleGerente    = "manager"
    RoleFinanceiro = "financial"
    RoleFilial     = "branch_user"
    RoleTecnico    = "technician"
    RolePrestador  = "provider"
)
```

## 5. Estruturas Relacionadas e Dependências

### Componentes Principais

#### CostItem (Item de Custo)
- Registra custos associados à ordem
- Mantém informações de quantidade, preço unitário e total
- Vincula ao usuário que adicionou o custo

#### Interaction (Interação)
- Registra comentários e interações na ordem
- Mantém histórico de comunicação entre usuários
- Vincula ao usuário que fez a interação

#### Invoice (Fatura)
- Registra informações de faturamento
- Mantém valor total e status do pagamento

#### Branch (Filial)
- Representa a filial onde a ordem foi criada
- Contém informações da localização

#### User (Usuário)
- Representa os diferentes usuários envolvidos
- Mantém informações de perfil e permissões

### Relacionamentos e Dependências

- **Ordem de Manutenção** → **CostItem**: Uma ordem pode ter múltiplos itens de custo
- **Ordem de Manutenção** → **Interaction**: Uma ordem pode ter múltiplas interações
- **Ordem de Manutenção** → **Invoice**: Uma ordem pode ter uma fatura associada
- **Ordem de Manutenção** → **Branch**: Uma ordem pertence a uma filial
- **Ordem de Manutenção** → **User**: Uma ordem pode ter múltiplos usuários associados (criador, aprovador, etc.)

### Integridade dos Dados

- Todas as estruturas mantêm referências para rastreabilidade
- Relacionamentos são mantidos através de chaves estrangeiras
- Histórico de alterações é preservado para auditoria

### CostItem (Item de Custo)
- Registra custos associados à ordem
- Mantém informações de quantidade, preço unitário e total
- Vincula ao usuário que adicionou o custo

### Interaction (Interação)
- Registra comentários e interações na ordem
- Mantém histórico de comunicação entre usuários
- Vincula ao usuário que fez a interação

### Invoice (Fatura)
- Registra informações de faturamento
- Mantém valor total e status do pagamento

### Branch (Filial)
- Representa a filial onde a ordem foi criada
- Contém informações da localização

### User (Usuário)
- Representa os diferentes usuários envolvidos
- Mantém informações de perfil e permissões

## 6. Fluxo de Status

### Estados Principais
1. **🔄 Pendente** (Inicial)
   - 📝 Estado inicial da ordem
   - ⏳ Aguardando atribuição de prestador

2. **🚀 Em Andamento** (Após aceitação pelo prestador)
   - ✅ Prestador aceitou a ordem
   - 🔧 Serviço em execução

3. **⌛ Pendente Aprovação** (Após conclusão do serviço)
   - ✨ Serviço concluído
   - 👀 Aguardando aprovação do gestor

4. **✔️ Aprovada** (Após aprovação do gestor)
   - 👍 Gestor aprovou o serviço
   - 📄 Pronta para faturamento

5. **📋 Faturada** (Após emissão da nota fiscal)
   - 🧾 Nota fiscal emitida
   - 💰 Aguardando pagamento

6. **💵 Paga** (Após confirmação do pagamento)
   - Pagamento confirmado
   - Ordem finalizada

### Estados Alternativos ❌
- **Cancelada** (Em qualquer momento)
  - Ordem interrompida por qualquer motivo
  - Requer justificativa

- **Rejeitada** (Após análise)
  - Serviço não aprovado
  - Requer correções ou nova submissão

- **Revisão Necessária** (Após análise)
  - Ajustes solicitados
  - Aguardando correções do prestador

## 7. Funcionalidades Principais

### Gestão de Ordens
- ✨ Criação e configuração de novas ordens
- 👥 Atribuição e gestão de prestadores
- 🔄 Controle de status e transições
- 💰 Gestão de custos e orçamentos
- 💬 Registro e acompanhamento de interações
- ✅ Processo de aprovação e rejeição
- 📊 Faturamento e controle financeiro
- 💳 Gestão de pagamentos

## 8. Validações e Controles

### Regras de Negócio
- ⚡ Hooks automáticos para validações
- 🏷️ Status inicial padronizado
- 🔒 Controle granular de permissões
- 🔄 Validação de transições de status



## 9. Métricas e Análises

### Indicadores de Desempenho
- 📈 Volume total de ordens
- 📊 Análise de distribuição por status
- 🎯 Distribuição por nível de prioridade
- ⚠️ Monitoramento de atrasos
- ⏱️ Análise de tempo de resolução
- 💵 Análise financeira e custos




## 10. Interface do Sistema

### Módulos Principais
- 📋 Dashboard de ordens
- 🔍 Visualização detalhada
- ➕ Formulários de cadastro
- 📅 Gestão de agenda

### Níveis de Acesso
- 👑 **Administrador**: Acesso completo ao sistema
- 👨‍💼 **Gerente**: Gestão e aprovações
- 💼 **Financeiro**: Gestão de pagamentos
- 🏢 **Filial**: Gestão local
- 🔧 **Prestador**: Execução de serviços
- 🛠️ **Técnico**: Suporte operacional



## 11. 🔒 Considerações de Segurança

### Controles de Acesso
- 👥 RBAC (Role-Based Access Control)
- 🔐 Autenticação em múltiplos fatores
- 🛡️ Validação de tokens JWT
- 🔑 Criptografia de dados sensíveis

### Auditoria e Rastreabilidade
- 📝 Logs detalhados de ações
- 🔍 Histórico de modificações
- ⚖️ Registro de tentativas de acesso
- 📊 Relatórios de segurança

### Proteção de Dados
- 🔒 Criptografia em trânsito (TLS)
- 💾 Criptografia em repouso
- 🚫 Sanitização de inputs
- 🛡️ Proteção contra ataques comuns (XSS, CSRF, SQL Injection)

### Conformidade
- 📋 LGPD/GDPR
- ✅ Políticas de retenção
- 📊 Relatórios de conformidade
- 🔄 Revisões periódicas de segurança

- Controle de acesso baseado em papéis
- Registro de todas as alterações
- Validação de permissões por ação
- Histórico de modificações 