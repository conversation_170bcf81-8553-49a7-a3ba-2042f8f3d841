{{ define "ordens/order_detail.html" }}
{{ block "page_styles" . }}
<link rel="stylesheet" href="/static/css/dashboard.css">
<link rel="stylesheet" href="/static/css/order_detail.css">
{{ end }}

{{ block "content" . }}
<div class="container-fluid order-detail-page">
    {{/* Cabeçalho com botão Voltar e Título */}}
    <div class="page-header mb-4">
        <a href="/dashboard" class="shell-btn shell-btn-red me-3">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
        <h1 class="page-title">Detalhes da Ordem: {{ .Order.ID }} - {{ .Order.Title }}</h1>
    </div>

    <div class="row mb-4">
        {{/* Coluna Esquerda: Detalhes da Manutenção */}}
        <div class="col-md-7">
            <div class="pump-border p-4 h-100">
                <h4 class="mb-3" style="color: var(--shell-yellow);">Detalhes da Manutenção</h4>
                <p>{{ if .Order.Description }}{{ .Order.Description }}{{ else }}-{{ end }}</p>
                <hr style="border-color: rgba(255,255,255,0.2);">
                <div>
                    <p><i class="fas fa-map-marker-alt me-2"></i>{{ if .Branch.Name }}{{ .Branch.Name }}{{ else }}N/A{{ end }}</p>
                </div>
                <div>
                    <strong class="text-white">Equipamento:</strong>
                    <p><i class="fas fa-wrench me-2"></i> <a href="/equipment/{{ .Equipment.ID }}" class="text-white">{{ if .Equipment.Name }}{{ .Equipment.Name }}{{ else }}N/A{{ end }} ({{ if .Equipment.Type }}{{ .Equipment.Type }}{{ else }}-{{ end }})</a></p>
                </div>
                <div>
                    <strong class="text-white">Criado por:</strong>
                    <p><i class="fas fa-user me-2"></i>{{ if .CreatedUser.Name }}{{ .CreatedUser.Name }}{{ else }}N/A{{ end }} em {{ .Order.CreatedAt | formatDateTime }}</p>
                </div>
            </div>
        </div>

        {{/* Coluna Direita: Status e Ações */}}
        <div class="col-md-5">
            <div class="pump-border p-4 h-100">
                <h4 class="mb-3" style="color: var(--shell-yellow);">Status e Ações</h4>

                <ul class="list-unstyled text-white">
                    <li class="mb-2">
                        <strong class="me-2">Responsável (Prestador):</strong>
                        <i class="fas fa-user-cog me-1"></i>
                        {{ if .Order.AssignedProviderID }}
                            {{ .AssignedProvider.Name | defaultString "N/A" }}
                        {{ else }}
                            <span class="text-muted">Não Atribuído</span>
                        {{ end }}
                    </li>
                    <li class="mb-2">
                        <strong class="me-2">Status Atual:</strong>
                        <span class="status-badge {{ .Order.Status | statusToClass }}">{{ .Order.Status | formatStatus }}</span>
                    </li>
                    <li class="mb-2">
                        <strong class="me-2">Prioridade:</strong>
                        {{ .Order.Priority | formatPriority }}
                    </li>
                    {{ if .Order.ApprovedByUserID }}
                    <li class="mb-2">
                        <strong class="me-2">Aprovado por:</strong>
                        <i class="fas fa-user-check me-1"></i>{{ .ApprovedUser.Name | defaultString "N/A" }} em {{ .Order.ApprovedAt | formatDateTime }}
                    </li>
                    {{ end }}
                </ul>

                <hr style="border-color: rgba(255,255,255,0.2);">

                {{/* Área de Botões de Ação */}}
                <div class="d-flex flex-wrap gap-2 justify-content-end">
                    {{/* Ações da Filial */}}
                    {{ if eq .CurrentUser.Role "branch" }}
                        {{ if and (eq .Order.Status "pending") (not .Order.AssignedProviderID) }}
                            <button class="shell-btn" data-order-id="{{ .Order.ID }}" onclick="openAssignModal(event)">
                                <i class="fas fa-user-plus"></i> Atribuir Prestador
                            </button>
                        {{ end }}
                        {{ if and (ne .Order.Status "cancelled") (ne .Order.Status "paid") (ne .Order.Status "invoiced") }}
                            <button class="shell-btn shell-btn-red" data-order-id="{{ .Order.ID }}" onclick="cancelOrder(event)">
                                <i class="fas fa-ban"></i> Cancelar Ordem
                            </button>
                        {{ end }}
                    {{ end }}

                    {{/* Ações do Prestador */}}
                    {{ if and (eq .CurrentUser.Role "provider") (eq .CurrentUser.ID .Order.AssignedProviderID) }}
                        {{ if eq .Order.Status "pending" }}
                            <button class="shell-btn shell-btn-green" data-order-id="{{ .Order.ID }}" data-new-status="in_progress" onclick="updateOrderStatusHandler(event)">
                                <i class="fas fa-check"></i> Aceitar Ordem
                            </button>
                            <button class="shell-btn shell-btn-red" data-order-id="{{ .Order.ID }}" data-new-status="rejected_provider" onclick="updateOrderStatusHandler(event)">
                                <i class="fas fa-times"></i> Recusar Ordem
                            </button>
                        {{ end }}
                        {{ if eq .Order.Status "in_progress" }}
                            <button class="shell-btn shell-btn-yellow" data-order-id="{{ .Order.ID }}" onclick="submitForApproval(event)">
                                <i class="fas fa-paper-plane"></i> Enviar p/ Aprovação
                            </button>
                        {{ end }}
                        {{ if eq .Order.Status "revision_needed" }}
                            <button class="shell-btn shell-btn-yellow" data-order-id="{{ .Order.ID }}" onclick="submitForApproval(event)">
                                <i class="fas fa-paper-plane"></i> Reenviar p/ Aprovação
                            </button>
                        {{ end }}
                        {{ if eq .Order.Status "approved" }}
                            <button class="shell-btn" data-order-id="{{ .Order.ID }}" onclick="openInvoiceModal(event)">
                                <i class="fas fa-file-invoice-dollar"></i> Anexar Nota Fiscal
                            </button>
                        {{ end }}
                    {{ end }}

                    {{/* Ações do Gerente/Financeiro */}}
                    {{ if or (eq .CurrentUser.Role "manager") (eq .CurrentUser.Role "finance") }}
                        {{ if eq .Order.Status "pending_approval" }}
                            <button class="shell-btn shell-btn-green" data-order-id="{{ .Order.ID }}" onclick="approveOrder(event)">
                                <i class="fas fa-check-circle"></i> Aprovar
                            </button>
                            <button class="shell-btn shell-btn-red" data-order-id="{{ .Order.ID }}" onclick="rejectOrder(event)">
                                <i class="fas fa-times-circle"></i> Reprovar
                            </button>
                        {{ end }}
                        {{ if and (eq .CurrentUser.Role "finance") (eq .Order.Status "invoiced") }}
                            <button class="shell-btn shell-btn-success" data-order-id="{{ .Order.ID }}" onclick="markAsPaid(event)">
                                <i class="fas fa-money-check-alt"></i> Marcar como Pago
                            </button>
                        {{ end }}
                        <button class="shell-btn shell-btn-grey" data-order-id="{{ .Order.ID }}" onclick="printOrder(event)">
                            <i class="fas fa-print"></i> Imprimir
                        </button>
                    {{ end }}
                </div>
            </div>
        </div>
    </div>

    {{/* Linha com os Cards de Detalhes */}}
    <hr style="border-color: rgba(255,255,255,0.2); margin: 2rem 0;">
    <h3 class="text-center mb-4" style="color: var(--shell-yellow);">Detalhes da Ordem</h3>

    <div class="row g-4">
        {{/* Card: Histórico do Equipamento */}}
        <div class="col-md-6 col-lg-3 d-flex">
            <div class="detail-card w-100">
                <div>
                    <i class="fas fa-history icon"></i>
                    <h5>Histórico</h5>
                </div>
                <div class="detail-card-content">
                    <p><strong>Equipamento:</strong><br>{{ .Equipment.Name | defaultString "N/A" }}</p>
                    <p>Última Manutenção: {{ .Equipment.LastMaintenanceDate | defaultString "N/A" }}</p>
                </div>
                <div class="detail-card-action">
                    <a href="/equipment/{{ .Equipment.ID }}/history" class="shell-btn shell-btn-sm">
                        <i class="fas fa-eye"></i> Ver Histórico Completo
                    </a>
                </div>
            </div>
        </div>

        {{/* Card: Custos */}}
        <div class="col-md-6 col-lg-3 d-flex">
            <div class="detail-card w-100">
                <div>
                    <i class="fas fa-dollar-sign icon"></i>
                    <h5>Custos</h5>
                </div>
                <div class="detail-card-content">
                    <p>Total Estimado/Aprovado:</p>
                    <h3 class="mb-0" id="orderTotalCostDisplay">{{ .Order.TotalCost | formatCurrency }}</h3>
                </div>
                <div class="detail-card-action">
                    {{ if and (eq .CurrentUser.Role "provider") (eq .CurrentUser.ID .Order.AssignedProviderID) (or (eq .Order.Status "in_progress") (eq .Order.Status "revision_needed")) }}
                        <button class="shell-btn shell-btn-sm" data-order-id="{{ .Order.ID }}" onclick="openCostsModal(event)">
                            <i class="fas fa-plus"></i> Adicionar/Editar Custos
                        </button>
                    {{ else }}
                        <button class="shell-btn shell-btn-sm" data-order-id="{{ .Order.ID }}" onclick="viewCostsModal(event)">
                            <i class="fas fa-eye"></i> Ver Detalhes dos Custos
                        </button>
                    {{ end }}
                </div>
            </div>
        </div>

        {{/* Card: Interação */}}
        <div class="col-md-6 col-lg-3 d-flex">
            <div class="detail-card w-100">
                <div>
                    <i class="fas fa-comments icon"></i>
                    <h5>Interação</h5>
                </div>
                <div class="detail-card-content text-start" style="max-height: 200px; overflow-y: auto;">
                    {{ if .Order.Interactions }}
                        {{ range .Order.Interactions }}
                        <div class="interaction-item">
                            <p class="mb-1">{{ .Message }}</p>
                            <div class="interaction-meta">
                                <i class="fas fa-user"></i> {{ .User.Name | defaultString "Sistema" }} - {{ .Timestamp.Format "02/01/06 15:04" }}
                            </div>
                        </div>
                        {{ end }}
                    {{ else }}
                        <p class="text-muted">Nenhuma interação registrada.</p>
                    {{ end }}
                </div>
                <div class="detail-card-action">
                    {{ if and (ne .Order.Status "paid") (ne .Order.Status "cancelled") }}
                        <button class="shell-btn shell-btn-sm" data-order-id="{{ .Order.ID }}" onclick="openInteractionModal(event)">
                            <i class="fas fa-plus"></i> Adicionar Comentário
                        </button>
                    {{ else }}
                        <span class="text-muted">(Interações bloqueadas)</span>
                    {{ end }}
                </div>
            </div>
        </div>

        {{/* Card: Cronograma */}}
        <div class="col-md-6 col-lg-3 d-flex">
            <div class="detail-card w-100">
                <div>
                    <i class="fas fa-calendar-alt icon"></i>
                    <h5>Cronograma</h5>
                </div>
                <div class="detail-card-content text-start">
                    <ul class="list-unstyled timeline">
                        <li><i class="fas fa-flag text-info me-2"></i>Criada: {{ .Order.CreatedAt | formatDateTime }}</li>
                        {{ if .Order.AssignedAt }}<li><i class="fas fa-user-plus text-primary me-2"></i>Atribuída: {{ .Order.AssignedAt | formatDateTime }}</li>{{ end }}
                        {{ if .Order.AcceptedAt }}<li><i class="fas fa-play-circle text-success me-2"></i>Aceita: {{ .Order.AcceptedAt | formatDateTime }}</li>{{ end }}
                        {{ if .Order.SubmittedForApprovalAt }}<li><i class="fas fa-paper-plane text-warning me-2"></i>Submetida: {{ .Order.SubmittedForApprovalAt | formatDateTime }}</li>{{ end }}
                        {{ if .Order.ApprovedAt }}<li><i class="fas fa-check-circle text-success me-2"></i>Aprovada: {{ .Order.ApprovedAt | formatDateTime }}</li>{{ end }}
                        {{ if .Order.InvoicedAt }}<li><i class="fas fa-file-invoice-dollar text-purple me-2"></i>Faturada: {{ .Order.InvoicedAt | formatDateTime }}</li>{{ end }}
                        {{ if .Order.PaidAt }}<li><i class="fas fa-money-check-alt text-success me-2"></i>Paga: {{ .Order.PaidAt | formatDateTime }}</li>{{ end }}
                        {{ if .Order.CancelledAt }}<li><i class="fas fa-ban text-danger me-2"></i>Cancelada: {{ .Order.CancelledAt | formatDateTime }}</li>{{ end }}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

{{/* Modais */}}
{{ template "modals/order_costs_modal.html" . }}
{{ template "modals/order_interaction_modal.html" . }}
{{ template "modals/order_assign_provider_modal.html" . }}

{{/* Container de Toasts */}}
<div aria-live="polite" aria-atomic="true" class="position-fixed top-0 end-0 p-3" style="z-index: 1055">
    <div id="toastPlacement" class="toast-container"></div>
</div>

{{ end }}

{{/* JavaScript */}}
{{ block "page_scripts" . }}
<script>
    // Variáveis globais necessárias para o JavaScript
    const CURRENT_ORDER_ID = {{ printf "%d" (.Order.ID | default 0) }};
    const CURRENT_USER_ROLE = {{ printf "%q" (.CurrentUser.Role | default "") }};
    const CURRENT_USER_ID = {{ printf "%d" (.CurrentUser.ID | default 0) }};
    const ASSIGNED_PROVIDER_ID = {{ printf "%d" (.Order.AssignedProviderID | default 0) }};
    const CURRENT_ORDER_STATUS = {{ printf "%q" (.Order.Status | default "") }};
</script>
<script src="/static/js/order_detail.js"></script>
{{ end }}
{{ end }}