# Documentação da Página de Detalhes da Ordem

## Visão Geral

A página de detalhes da ordem foi completamente redesenhada para oferecer uma experiência mais moderna, funcional e intuitiva. A nova interface segue o padrão visual do dashboard, com fundo escuro e elementos visuais organizados em cards que facilitam a visualização e gerenciamento das informações da ordem.

## Arquivos Relacionados

### HTML
- `web/templates/ordens/order_detail_new.html`: Template principal da página

### CSS
- `web/static/css/order_detail_new.css`: Estilos específicos para a nova página de detalhes

### JavaScript
- `web/static/js/order_detail_new.js`: Funcionalidades específicas da nova página

### Modais
- `web/templates/modals/order_costs_modal.html`: Modal para visualização e edição de custos
- `web/templates/modals/order_interaction_modal.html`: Modal para adição de interações
- `web/templates/modals/order_assign_provider_modal.html`: Modal para atribuição de prestador

## Estrutura da Página

A página está organizada em seções claramente definidas:

1. **Cabeçalho da Ordem**
   - Botão de voltar para a listagem de ordens
   - Título da ordem com ID e descrição
   - Badges de status e prioridade

2. **Cards de Informações Principais**
   - **Detalhes Principais**: Informações básicas da ordem como descrição, filial, equipamento, etc.
   - **Cronograma**: Timeline com as datas importantes da ordem (criação, atribuição, aprovação, etc.)

3. **Cards de Informações Secundárias**
   - **Custos**: Valor total e botão para visualizar/editar custos
   - **Histórico do Equipamento**: Informações sobre o equipamento e link para histórico completo
   - **Interações**: Lista de comentários e interações relacionadas à ordem

4. **Seção de Ações**
   - Botões de ação específicos para cada perfil de usuário e status da ordem

## Funcionalidades

### Visualização de Informações
- Informações organizadas em cards temáticos
- Timeline visual para acompanhamento do progresso da ordem
- Badges coloridos para status e prioridade

### Ações Disponíveis
As ações disponíveis variam de acordo com o perfil do usuário e o status atual da ordem:

#### Perfil de Filial
- Atribuir prestador (quando pendente)
- Cancelar ordem (exceto quando já cancelada, paga ou faturada)

#### Perfil de Prestador
- Aceitar/recusar ordem (quando pendente)
- Enviar para aprovação (quando em andamento)
- Reenviar para aprovação (quando necessita revisão)
- Anexar nota fiscal (quando aprovada)

#### Perfil de Gerente/Financeiro
- Aprovar/reprovar ordem (quando pendente de aprovação)
- Marcar como paga (quando faturada)
- Imprimir ordem

### Modais
- **Modal de Custos**: Permite visualizar, adicionar e remover itens de custo
- **Modal de Interações**: Permite adicionar comentários à ordem
- **Modal de Atribuição**: Permite selecionar um prestador para a ordem

## Personalização Visual

### Cores e Temas
- Utiliza as cores da identidade visual da Shell (vermelho e amarelo)
- Fundo escuro para melhor contraste e leitura
- Gradientes e efeitos sutis para dar profundidade aos elementos

### Elementos Visuais
- Cards com bordas arredondadas e efeitos de hover
- Ícones intuitivos para cada tipo de informação
- Badges coloridos para status e prioridade
- Timeline visual para o cronograma da ordem

## Responsividade

A página é totalmente responsiva e se adapta a diferentes tamanhos de tela:

- **Desktop**: Layout completo com todas as funcionalidades
- **Tablet**: Reorganização dos cards para melhor visualização
- **Mobile**: Empilhamento dos cards e ajuste dos botões de ação

## Como Editar

### Modificar o Layout
Para modificar o layout da página, edite o arquivo `web/templates/ordens/order_detail_new.html`. O template utiliza a estrutura de blocos do Go Templates, o que facilita a manutenção e extensão.

### Alterar Estilos
Para alterar os estilos visuais, edite o arquivo `web/static/css/order_detail_new.css`. Os estilos estão organizados por seções para facilitar a localização e edição.

### Adicionar ou Modificar Funcionalidades
Para adicionar ou modificar funcionalidades, edite o arquivo `web/static/js/order_detail_new.js`. O código JavaScript está organizado em funções específicas para cada funcionalidade.

## Considerações Técnicas

### Compatibilidade com Navegadores
A página foi desenvolvida utilizando tecnologias modernas e é compatível com os principais navegadores:
- Google Chrome (recomendado)
- Mozilla Firefox
- Microsoft Edge
- Safari

### Dependências
- Bootstrap 5 para o grid e componentes básicos
- Font Awesome para ícones
- Rajdhani como fonte principal

### Performance
- Animações leves para não comprometer a performance
- Carregamento assíncrono de dados para melhor experiência do usuário
- Código JavaScript modularizado para facilitar a manutenção

## Exemplos de Uso

### Visualizar Detalhes da Ordem
1. Acesse a página de listagem de ordens
2. Clique no botão de visualização (ícone de olho) de uma ordem
3. A página de detalhes será exibida com todas as informações organizadas em cards

### Adicionar um Custo (Perfil de Prestador)
1. Na página de detalhes, localize o card de Custos
2. Clique no botão "Adicionar/Editar Custos"
3. Preencha os campos do formulário no modal
4. Clique em "Adicionar Item"

### Adicionar uma Interação
1. Na página de detalhes, localize o card de Interações
2. Clique no botão "Adicionar Comentário"
3. Digite sua mensagem no modal
4. Clique em "Salvar Interação"
