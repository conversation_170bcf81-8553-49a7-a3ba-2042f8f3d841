{{ define "ordens/orders_simple.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .title }}</title>
    <link rel="icon" href="/static/images/favicon.ico" type="image/x-icon">

    <!-- Bootstrap e estilos base -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/orders_simple.css">

    <!-- <PERSON>ontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Incluindo o menu lateral independente -->
    {{ template "sidebar" . }}

    <!-- Conteúdo principal -->
    <div class="content-with-sidebar">
        <div class="main-content">
            <!-- Cabeçalho da página -->
            <div class="page-header d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title">Ordens de Serviço</h1>
                    <p class="text-muted">{{ .TotalCount }} ordens no sistema</p>
                </div>
                <div>
                    <button class="btn btn-outline-light me-2" id="refreshBtn">
                        <i class="fas fa-sync-alt"></i> Atualizar
                    </button>
                    <a href="/orders/create" class="btn btn-shell-yellow">
                        <i class="fas fa-plus-circle"></i> Nova Ordem
                    </a>
                </div>
            </div>

            <!-- Métricas -->
            <div class="row mb-4">
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="metrics-card pending">
                        <div class="card-title">Pendentes</div>
                        <div class="card-value">{{ .PendingCount }}</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="metrics-card progress">
                        <div class="card-title">Em Andamento</div>
                        <div class="card-value">{{ .InProgressCount }}</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="metrics-card completed">
                        <div class="card-title">Concluídas</div>
                        <div class="card-value">{{ .CompletedCount }}</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="metrics-card total">
                        <div class="card-title">Total</div>
                        <div class="card-value">{{ .TotalCount }}</div>
                    </div>
                </div>
            </div>

            <!-- Filtros -->
            <div class="filter-section mb-4">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <input type="text" class="form-control" id="searchInput" placeholder="Buscar por título, ID...">
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="statusFilter">
                            <option value="all">Todos os Status</option>
                            {{ range $status, $label := .StatusOptions }}
                                <option value="{{ $status }}">{{ $label }}</option>
                            {{ end }}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="priorityFilter">
                            <option value="all">Todas as Prioridades</option>
                            {{ range $prio, $label := .PriorityOptions }}
                                <option value="{{ $prio }}">{{ $label }}</option>
                            {{ end }}
                        </select>
                    </div>
                </div>
                <div class="text-end">
                    <button class="btn btn-outline-light" id="clearFiltersBtn">
                        <i class="fas fa-times"></i> Limpar Filtros
                    </button>
                </div>
            </div>

            <!-- Tabela de Ordens -->
            <div class="table-responsive">
                <table class="orders-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Título</th>
                            <th>Status</th>
                            <th>Prioridade</th>
                            <th>Data</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{ if .Orders }}
                            {{ range .Orders }}
                            <tr class="order-row" data-id="{{ .ID }}" data-status="{{ .Status }}" data-priority="{{ .Priority }}">
                                <td>#{{ .ID }}</td>
                                <td>
                                    <div class="order-title">{{ .Title }}</div>
                                </td>
                                <td>
                                    <span class="status-badge status-{{ .Status }}">
                                        {{ formatStatus .Status }}
                                    </span>
                                </td>
                                <td>
                                    <span class="priority-badge priority-{{ .Priority }}">
                                        {{ formatPriority .Priority }}
                                    </span>
                                </td>
                                <td>{{ formatDate .CreatedAt }}</td>
                                <td>
                                    <button class="action-btn btn-view" data-id="{{ .ID }}" data-bs-toggle="tooltip" title="Ver Detalhes">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <a href="/orders/{{ .ID }}/edit" class="action-btn btn-edit" data-bs-toggle="tooltip" title="Editar">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button class="action-btn btn-delete delete-order" data-id="{{ .ID }}" data-bs-toggle="tooltip" title="Excluir">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {{ end }}
                        {{ else }}
                            <tr>
                                <td colspan="6">
                                    <div class="empty-state">
                                        <i class="fas fa-clipboard"></i>
                                        <h5>Nenhuma ordem encontrada</h5>
                                        <p>Tente ajustar os filtros ou criar uma nova ordem</p>
                                        <a href="/orders/create" class="btn btn-shell-yellow">
                                            <i class="fas fa-plus-circle"></i> Criar Nova Ordem
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {{ end }}
                    </tbody>
                </table>
            </div>

            <!-- Paginação Simples -->
            {{ if gt .TotalCount 10 }}
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    <span class="text-muted">Mostrando <span id="filteredCount">{{ len .Orders }}</span> de {{ .TotalCount }} ordens</span>
                </div>
                <nav aria-label="Paginação">
                    <ul class="pagination">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Anterior</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">Próxima</a>
                        </li>
                    </ul>
                </nav>
            </div>
            {{ end }}
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/orders_simple.js"></script>
</body>
</html>
{{ end }}
