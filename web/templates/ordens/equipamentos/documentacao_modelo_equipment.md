# Documentação do Modelo Equipment

## Visão Geral

O modelo `Equipment` representa equipamentos no sistema de gestão, como bombas de combustível, tanques, compressores, etc. Estes equipamentos estão associados a filiais (branches) e podem ter ordens de manutenção associadas a eles.

## Estrutura do Banco de Dados

A tabela `equipment` no banco de dados PostgreSQL tem a seguinte estrutura:

| Coluna | Tipo | Descrição |
|--------|------|-----------|
| id | integer | Chave primária |
| serial_number | varchar(50) | Número de série do equipamento (único) |
| model | varchar(100) | Modelo do equipamento |
| brand | varchar(100) | Marca do equipamento |
| type | varchar(50) | Tipo de equipamento (bomba, tanque, compressor, etc.) |
| installation_date | timestamp | Data de instalação |
| last_maintenance | timestamp | Data da última manutenção |
| last_preventive | timestamp | Data da última manutenção preventiva |
| next_preventive | timestamp | Data da próxima manutenção preventiva |
| status | varchar(20) | Status do equipamento (ativo, inativo, manutenção, descontinuado) |
| location | varchar(255) | Localização do equipamento na filial |
| branch_id | integer | ID da filial (referência à tabela `branches`) |
| created_at | timestamp | Data de criação do registro |
| updated_at | timestamp | Data da última atualização do registro |
| deleted_at | timestamp | Data de exclusão (soft delete) |

## Modelo Go

O modelo `Equipment` em Go está definido em `internal/models/equipment.go` e tem a seguinte estrutura:

```go
type Equipment struct {
    ID               uint           `gorm:"primaryKey" json:"id"`
    Name             string         `gorm:"-" json:"name"` // Campo virtual, não existe na tabela
    SerialNumber     string         `gorm:"column:serial_number" json:"serial_number"`
    Model            string         `gorm:"column:model" json:"model"`
    Brand            string         `gorm:"column:brand" json:"brand"`
    Type             string         `gorm:"column:type" json:"type"` // Tipo de equipamento: bomba, tanque, compressor, etc.
    Category         string         `gorm:"-" json:"category"`       // Campo virtual, não existe na tabela
    InstallationDate *time.Time     `gorm:"column:installation_date" json:"installation_date"`
    WarrantyEndDate  *time.Time     `gorm:"-" json:"warranty_end_date"`     // Campo virtual, não existe na tabela
    MaintenanceFreq  int            `gorm:"-" json:"maintenance_frequency"` // Campo virtual, não existe na tabela
    LastMaintenance  *time.Time     `gorm:"column:last_maintenance" json:"last_maintenance"`
    LastPreventive   *time.Time     `gorm:"column:last_preventive" json:"last_preventive"`
    NextPreventive   *time.Time     `gorm:"column:next_preventive" json:"next_preventive"`
    NextMaintenance  *time.Time     `gorm:"-" json:"next_maintenance"`   // Campo virtual, mapeado para next_preventive
    Status           string         `gorm:"column:status" json:"status"` // ativo, inativo, manutenção, descontinuado
    Location         string         `gorm:"column:location" json:"location"`
    BranchID         uint           `json:"branch_id" gorm:"column:branch_id"`
    FilialID         uint           `gorm:"-" json:"filial_id"`         // Campo virtual, mapeado para branch_id
    Notes            string         `gorm:"-" json:"notes"`             // Campo virtual, não existe na tabela
    ManufacturerDocs string         `gorm:"-" json:"manufacturer_docs"` // Campo virtual, não existe na tabela
    CreatedAt        time.Time      `gorm:"column:created_at" json:"created_at"`
    UpdatedAt        time.Time      `gorm:"column:updated_at" json:"updated_at"`
    DeletedAt        gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`

    // Relações
    MaintenanceOrders []MaintenanceOrder `gorm:"foreignKey:EquipmentID" json:"-"`
    Tags              []Tag              `gorm:"many2many:equipment_tags;" json:"tags"`
}
```

## Problemas Identificados e Soluções

### 1. Discrepância entre Modelo e Banco de Dados

**Problema:**
- O modelo Go tinha campos que não existiam na tabela do banco de dados.
- O modelo Go usava `FilialID` mapeado para `station_id`, mas na tabela real, a coluna era `branch_id`.
- Havia relações circulares entre os modelos `Equipment`, `Station` e `Filial`.

**Solução:**
1. Atualizamos o modelo para refletir a estrutura real da tabela:
   - Mapeamos corretamente os campos para as colunas existentes.
   - Marcamos campos inexistentes como virtuais (`gorm:"-"`).
   - Adicionamos campos virtuais para manter compatibilidade com código existente.

2. Implementamos o método `AfterFind` para preencher campos virtuais:
   ```go
   func (e *Equipment) AfterFind(tx *gorm.DB) error {
       // Preencher o campo Name com base nos outros campos
       e.Name = e.GetName()

       // Preencher campos virtuais com valores padrão se necessário
       e.NextMaintenance = e.NextPreventive

       // Mapear FilialID para BranchID para compatibilidade com código existente
       e.FilialID = e.BranchID

       return nil
   }
   ```

3. Implementamos o método `GetName` para gerar um nome baseado em outros campos:
   ```go
   func (e *Equipment) GetName() string {
       if e.Name != "" {
           return e.Name
       }

       name := ""
       if e.Brand != "" {
           name += e.Brand
       }

       if e.Model != "" {
           if name != "" {
               name += " "
           }
           name += e.Model
       }

       if name == "" && e.SerialNumber != "" {
           name = "Equipamento " + e.SerialNumber
       } else if name == "" {
           name = "Equipamento #" + strconv.FormatUint(uint64(e.ID), 10)
       }

       return name
   }
   ```

### 2. Relações Circulares

**Problema:**
- Havia relações circulares entre os modelos `Equipment`, `Station` e `Filial`.
- Isso causava erros de validação no GORM.

**Solução:**
1. Removemos as relações circulares:
   - Removemos a relação `Filial *Station` do modelo `Equipment`.
   - Removemos a relação `Equipment []Equipment` do modelo `Station`.
   - Mantivemos apenas as relações essenciais para o funcionamento do sistema.

### 3. Consultas Problemáticas

**Problema:**
- As consultas no repositório de equipamentos tentavam selecionar colunas que não existiam na tabela.
- Isso causava erros SQL como `column equipment.name does not exist`.

**Solução:**
1. Simplificamos as consultas no repositório:
   ```go
   // GetByFilialID retorna todos os equipamentos de uma filial
   func (r *EquipmentRepository) GetByFilialID(filialID uint) ([]models.Equipment, error) {
       var equipments []models.Equipment
       // Usar uma consulta mais simples para evitar problemas com colunas inexistentes
       result := r.db.Where("branch_id = ?", filialID).Find(&equipments)
       return equipments, result.Error
   }
   ```

2. Usamos consultas mais simples e robustas:
   - Evitamos selecionar campos específicos quando não há certeza sobre a estrutura da tabela.
   - Usamos `Where` para filtrar por condições específicas.
   - Deixamos o GORM mapear automaticamente os campos existentes.

### 4. Problema com a Criação de Equipamentos

**Problema:**
- Ao criar um equipamento, o campo `BranchID` não estava sendo definido corretamente.
- Isso causava um erro de violação de chave estrangeira: `ERROR: insert or update on table "equipment" violates foreign key constraint "fk_branches_equipment"`.

**Solução:**
1. Modificamos o handler `CreateEquipment` para definir tanto `FilialID` quanto `BranchID`:
   ```go
   // Definir tanto FilialID (campo virtual) quanto BranchID (campo real na tabela)
   equipment.FilialID = filialID
   equipment.BranchID = filialID // Este é o campo que realmente importa para o banco de dados
   ```

2. Fizemos o mesmo para o método `UpdateEquipment`:
   ```go
   // Preservar o FilialID e BranchID do equipamento existente
   equipment.FilialID = existingEquipment.FilialID
   equipment.BranchID = existingEquipment.BranchID // Este é o campo que realmente importa para o banco de dados
   ```

## Tipos de Equipamentos

O sistema suporta os seguintes tipos de equipamentos:

1. **Bomba de Combustível** (`bomba`): Bombas para abastecimento de veículos
2. **Tanque de Armazenamento** (`tanque`): Tanques para armazenamento de combustíveis
3. **Compressor de Ar** (`compressor`): Equipamentos para calibragem de pneus
4. **Gerador de Energia** (`gerador`): Geradores para fornecimento de energia de emergência
5. **Sistema de Pagamento** (`sistema_pagamento`): Terminais e máquinas de pagamento
6. **Sistema de Automação** (`sistema_automacao`): Sistemas de controle e automação do posto
7. **Sistema de Iluminação** (`iluminacao`): Equipamentos de iluminação e sinalização
8. **Sistema de Refrigeração** (`refrigeracao`): Equipamentos de refrigeração e climatização
9. **Detector de Vazamento** (`detector_vazamento`): Sistemas para detecção de vazamentos
10. **Outro** (`outro`): Outros tipos de equipamentos

## Rotas da API

As seguintes rotas da API estão disponíveis para gerenciar equipamentos:

| Método | Rota | Descrição | Permissões |
|--------|------|-----------|------------|
| GET | /api/equipments | Listar todos os equipamentos | admin, gerente, financeiro, tecnico, filial, prestadores, branch_user |
| GET | /api/equipments/:id | Obter um equipamento específico | admin, gerente, financeiro, tecnico, filial, prestadores, branch_user |
| POST | /api/equipments | Criar um novo equipamento | admin, gerente, tecnico, filial |
| PUT | /api/equipments/:id | Atualizar um equipamento existente | admin, gerente, tecnico, filial |
| DELETE | /api/equipments/:id | Excluir um equipamento | admin, gerente |
| GET | /api/equipments/types | Listar tipos de equipamentos disponíveis | admin, gerente, financeiro, tecnico, filial, prestadores, branch_user |
| GET | /api/equipments/filial/:id | Listar equipamentos de uma filial específica | admin, gerente, financeiro, tecnico, filial, prestadores, branch_user |

## Considerações para Desenvolvimento

1. **Campos Virtuais**:
   - Alguns campos no modelo Go são virtuais e não existem na tabela do banco de dados.
   - Estes campos são preenchidos pelo método `AfterFind` após carregar os dados do banco.
   - Ao criar ou atualizar equipamentos, estes campos são ignorados pelo GORM.

2. **Compatibilidade**:
   - O campo `FilialID` é mantido por compatibilidade com código existente.
   - Internamente, ele é mapeado para `BranchID`, que é o campo real na tabela.

3. **Relações**:
   - Um equipamento pertence a uma filial (branch).
   - Um equipamento pode ter várias ordens de manutenção.
   - Um equipamento pode ter várias tags.

4. **Consultas**:
   - Ao consultar equipamentos, use `Where("branch_id = ?", filialID)` em vez de `Where("station_id = ?", filialID)`.
   - Evite selecionar campos específicos quando não há certeza sobre a estrutura da tabela.

## Exemplo de Uso

```go
// Obter equipamentos de uma filial
equipments, err := equipmentRepository.GetByFilialID(filialID)
if err != nil {
    // Tratar erro
}

// Criar um novo equipamento
equipment := &models.Equipment{
    SerialNumber:     "SN12345",
    Model:            "Modelo X",
    Brand:            "Marca Y",
    Type:             "bomba",
    InstallationDate: &installationDate,
    Status:           "ativo",
    Location:         "Pista 1",
    BranchID:         filialID, // Este é o campo que realmente importa para o banco de dados
    FilialID:         filialID, // Campo virtual para compatibilidade
}
err = equipmentRepository.CreateEquipment(equipment)
if err != nil {
    // Tratar erro
}
```

## Relação com Outros Modelos

### Relação com Branch/Filial

- Um equipamento pertence a uma filial (branch).
- A filial é identificada pelo campo `BranchID` no modelo `Equipment`.
- A tabela `equipment` tem uma restrição de chave estrangeira `fk_branches_equipment` que exige que o valor de `branch_id` exista na tabela `branches`.

### Relação com User

- Um usuário do tipo `filial` está associado a uma filial (branch) através do campo `BranchID` no modelo `User`.
- Quando um usuário do tipo `filial` cria um equipamento, o `BranchID` do usuário é usado como `BranchID` do equipamento.
- Isso garante que o equipamento seja associado à filial correta.

### Relação com MaintenanceOrder

- Um equipamento pode ter várias ordens de manutenção.
- As ordens de manutenção são identificadas pelo campo `EquipmentID` no modelo `MaintenanceOrder`.
- A tabela `maintenance_orders` tem uma restrição de chave estrangeira `fk_equipment_maintenance_orders` que exige que o valor de `equipment_id` exista na tabela `equipment`.

## Conclusão

O modelo `Equipment` é uma parte importante do sistema de gestão, permitindo o cadastro e gerenciamento de equipamentos associados a filiais. As correções implementadas garantem que o modelo funcione corretamente com a estrutura do banco de dados, mantendo a compatibilidade com o código existente.

A principal lição aprendida é a importância de manter a consistência entre o modelo Go e a estrutura do banco de dados, especialmente quando há campos virtuais e relações entre tabelas. Ao criar ou atualizar equipamentos, é essencial definir corretamente o campo `BranchID` para evitar violações de chave estrangeira.
