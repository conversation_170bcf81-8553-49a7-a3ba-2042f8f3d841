# Documentação da Página de Detalhes da Ordem de Serviço

## Visão Geral

A página de detalhes da ordem de serviço foi completamente redesenhada para oferecer uma experiência mais moderna, funcional e intuitiva. A nova interface segue o padrão visual do dashboard, com fundo escuro e elementos visuais organizados em cards que facilitam a visualização e gerenciamento das informações da ordem.

## Arquivos Relacionados

### HTML
- `web/templates/ordens/order_detail_service.html`: Template principal da página

### CSS
- `web/static/css/order_detail_service.css`: Estilos específicos para a página

### JavaScript
- `web/static/js/order_detail_service.js`: Funcionalidades específicas da página

### Modais
- `web/templates/modals/order_costs_modal.html`: Modal para visualização e adição de custos
- `web/templates/modals/order_interaction_modal.html`: Modal para adição de interações
- `web/templates/modals/order_assign_provider_modal.html`: Modal para atribuição de prestador

## Estrutura da Página

A página está organizada em seções claramente definidas:

1. **Cabeçalho da Ordem**
   - Botão de voltar para a listagem de ordens
   - Título da ordem com ID e descrição
   - Badges de status e prioridade

2. **Card de Manutenção**
   - Detalhes da manutenção a ser realizada
   - Caixa de texto para inserção de informações (apenas para admin, gerente e financeiro)
   - Botão para salvar alterações (apenas para admin, gerente e financeiro)

3. **Card de Informações Básicas**
   - Data de criação
   - Filial
   - Solicitante
   - Equipamento

4. **Card de Custos**
   - Lista de custos associados à ordem
   - Total de custos
   - Botão para adicionar custos (apenas para prestadores)

5. **Card de Interações**
   - Lista de interações/comentários
   - Botão para adicionar interações

6. **Seção de Ações**
   - Botões para diferentes ações dependendo do status da ordem e papel do usuário
   - Exemplos: atribuir prestador, iniciar serviço, concluir serviço, aprovar, rejeitar, etc.

## Funcionalidades Principais

### Visualização de Detalhes
- Exibição clara e organizada de todas as informações relevantes da ordem
- Badges coloridos para status e prioridade
- Separação em cards para melhor organização visual

### Edição de Manutenção
- Caixa de texto para edição da descrição da manutenção
- Permissão restrita a admin, gerente e financeiro
- Salvamento via API com feedback visual

### Gestão de Custos
- Visualização de custos existentes
- Adição de novos custos (para prestadores)
- Cálculo automático do total

### Comunicação
- Visualização de interações anteriores
- Adição de novas interações
- Registro de usuário e data/hora

### Fluxo de Trabalho
- Botões de ação específicos para cada status da ordem
- Permissões baseadas no papel do usuário
- Feedback visual após cada ação

## Permissões e Controle de Acesso

A página implementa um sistema de permissões baseado no papel do usuário:

1. **Admin/Gerente/Financeiro**
   - Podem editar a descrição da manutenção
   - Têm acesso a todas as ações

2. **Prestador**
   - Pode adicionar custos (quando a ordem está em andamento)
   - Pode marcar a ordem como concluída
   - Pode adicionar interações

3. **Filial**
   - Pode atribuir prestadores
   - Pode cancelar ordens
   - Pode adicionar interações

## Modais

### Modal de Custos
- Exibe a lista de custos existentes
- Permite adicionar novos custos
- Campos: descrição, quantidade, valor unitário

### Modal de Interações
- Permite adicionar novas interações/comentários
- Campo de texto para a mensagem

### Modal de Atribuição de Prestador
- Lista de prestadores disponíveis
- Seleção e atribuição à ordem

## Responsividade

A página é totalmente responsiva e se adapta a diferentes tamanhos de tela:

- **Desktop**: Layout completo com todas as funcionalidades
- **Tablet**: Reorganização dos cards para melhor visualização
- **Mobile**: Empilhamento dos cards e ajuste dos botões de ação

## Como Editar

### Modificar o Layout
Para modificar o layout da página, edite o arquivo `web/templates/ordens/order_detail_service.html`. O template utiliza a estrutura de blocos do Go Templates, o que facilita a manutenção e extensão.

### Alterar Estilos
Para alterar os estilos visuais, edite o arquivo `web/static/css/order_detail_service.css`. Os estilos estão organizados por seções para facilitar a localização e edição.

### Adicionar ou Modificar Funcionalidades
Para adicionar ou modificar funcionalidades, edite o arquivo `web/static/js/order_detail_service.js`. O código JavaScript está organizado em funções específicas para cada funcionalidade.

## Considerações Técnicas

### Compatibilidade com Navegadores
A página foi desenvolvida utilizando tecnologias modernas e é compatível com os principais navegadores:
- Google Chrome (recomendado)
- Mozilla Firefox
- Microsoft Edge
- Safari

### Performance
- Carregamento assíncrono de dados via API
- Manipulação eficiente do DOM
- Animações otimizadas para melhor desempenho

### Segurança
- Validação de dados no cliente e no servidor
- Controle de acesso baseado em permissões
- Proteção contra XSS e CSRF

## Troubleshooting

### Problemas Comuns e Soluções

1. **Dados não carregam**
   - Verifique se as APIs estão funcionando corretamente
   - Verifique se o usuário tem permissões adequadas
   - Verifique o console do navegador para erros

2. **Botões de ação não aparecem**
   - Verifique o papel do usuário atual
   - Verifique o status atual da ordem
   - Verifique se o usuário tem permissões adequadas

3. **Estilos não são aplicados**
   - Verifique se o arquivo CSS está sendo carregado corretamente
   - Verifique se há conflitos de CSS com outros componentes
   - Limpe o cache do navegador

4. **Modais não abrem**
   - Verifique se o Bootstrap está carregado corretamente
   - Verifique se os IDs dos elementos estão corretos
   - Verifique o console do navegador para erros
