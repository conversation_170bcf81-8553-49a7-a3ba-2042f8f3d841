{{ define "ordens/orders.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .title }}</title>
    <link rel="icon" href="/static/images/favicon.ico" type="image/x-icon">

    <!-- Bootstrap e estilos base -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/orders_simple.css">
    <link rel="stylesheet" href="/static/css/mobile-sidebar.css">

    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">
</head>
<body>
    {{ template "sidebar" . }}

    <div class="content-with-sidebar">
        <div class="main-content">
            <!-- Header com efeito de destaque -->
            <div class="page-header-wrapper">
                <div class="page-header d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <div class="header-icon-container me-3">
                            <i class="bx bx-clipboard text-warning"></i>
                        </div>
                        <div>
                            <h1 class="page-title mb-0">Ordens de Serviço</h1>
                            <p class="text-muted mb-0"><span id="totalOrdersCounter">0</span> ordens no sistema</p>
                        </div>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-glass me-2" id="refreshBtn">
                            <i class="bx bx-refresh me-1"></i> Atualizar
                        </button>
                        <a href="/orders/create" class="btn btn-shell-yellow">
                            <i class="bx bx-plus-circle me-1"></i> Nova Ordem
                        </a>
                    </div>
                </div>
            </div>

            <div class="shell-divider"></div>

            <!-- Área de Métricas -->
            <div class="row mb-4 metrics-row">
                <div class="col-md-3 col-sm-6 mb-3 mb-md-0">
                    <div class="metric-card metric-pending">
                        <div class="metric-icon-container">
                            <i class="bx bx-time-five"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value" id="pendingOrdersCount">0</div>
                            <div class="metric-label">Pendentes</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3 mb-md-0">
                    <div class="metric-card metric-progress">
                        <div class="metric-icon-container">
                            <i class="bx bx-loader-alt"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value" id="inProgressOrdersCount">0</div>
                            <div class="metric-label">Em Andamento</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3 mb-md-0">
                    <div class="metric-card metric-completed">
                        <div class="metric-icon-container">
                            <i class="bx bx-check-circle"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value" id="completedOrdersCount">0</div>
                            <div class="metric-label">Concluídas</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="metric-card metric-total">
                        <div class="metric-icon-container">
                            <i class="bx bx-clipboard"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value" id="totalOrdersCount">0</div>
                            <div class="metric-label">Total</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Área de Filtros -->
            <div class="filter-container mb-4">
                <div class="card card-shell">
                    <div class="card-header card-header-shell">
                        <div class="d-flex align-items-center">
                            <i class="bx bx-filter-alt me-2"></i>
                            <h5 class="mb-0">Filtros</h5>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <select class="form-select" id="statusFilter">
                                    <option value="all">Todos os Status</option>
                                    {{ range $status, $label := .StatusOptions }}
                                        <option value="{{ $status }}">{{ $label }}</option>
                                    {{ end }}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="branchFilter">
                                    <option value="all">Todas as Filiais</option>
                                    {{ range .Branches }}
                                        <option value="{{ .ID }}">{{ .Name }}</option>
                                    {{ end }}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control" id="dateFilter">
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-shell-yellow w-100" id="applyFilter">
                                    <i class="bx bx-filter me-1"></i> Aplicar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Lista de Ordens -->
            <div class="orders-list" id="ordersList">
                <!-- As ordens serão carregadas via JavaScript -->
            </div>

            <!-- Paginação -->
            <div class="pagination-container mt-4">
                <nav aria-label="Navegação de páginas">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- A paginação será gerada via JavaScript -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/orders.js"></script>
</body>
</html>
{{ end }}