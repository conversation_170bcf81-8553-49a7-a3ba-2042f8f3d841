# Documentação da Visualização de Ordem de Serviço com Cards

## Visão Geral

Esta documentação descreve a implementação da nova visualização de ordem de serviço, que utiliza um layout com cards organizados por categorias. O design segue o padrão visual mostrado na referência, com um cabeçalho principal, seção de detalhes da manutenção, informações laterais e cards de categorias.

## Arquivos Relacionados

### HTML
- `web/templates/ordens/order_detail_service.html`: Template principal da página

### CSS
- `web/static/css/order_detail_service.css`: Estilos específicos para a página

### JavaScript
- `web/static/js/order_detail_service.js`: Funcionalidades específicas da página

### Modais
- `web/templates/modals/order_costs_modal.html`: Modal para visualização e adição de custos
- `web/templates/modals/order_interaction_modal.html`: Modal para adição de interações
- `web/templates/modals/order_assign_provider_modal.html`: Modal para atribuição de prestador

## Estrutura da Página

A página está organizada em seções claramente definidas:

1. **Cabeçalho da Página**
   - Botão de voltar ao calendário
   - Título "Detalhes da Ordem"

2. **Conteúdo Principal**
   - **Seção de Título e Detalhes da Manutenção**
     - Título da manutenção
     - Detalhes da manutenção
     - Informações de filial e equipamento
   
   - **Sidebar com Informações Laterais**
     - Responsável pela ordem
     - Status atual
     - Tempo estimado
     - Botão de impressão
     - Botões de ação (aprovar/reprovar)

3. **Cards de Categorias**
   - **Card Histórico**
     - Exibe o nome do equipamento
     - Clicável para ver histórico do equipamento
   
   - **Card Custos**
     - Exibe o valor total dos custos
     - Clicável para adicionar/visualizar custos
   
   - **Card Interação**
     - Permite adicionar interações/comentários
     - Clicável para abrir modal de interação
   
   - **Card Cronograma**
     - Exibe datas importantes
     - Clicável para ver calendário

## Funcionalidades Principais

### Visualização de Detalhes
- Exibição clara e organizada de todas as informações relevantes da ordem
- Badges coloridos para status
- Organização em cards para melhor visualização

### Interação com Cards
- Cada card é clicável e leva a uma funcionalidade específica
- Efeitos de hover para melhor feedback visual
- Organização por categorias para facilitar o acesso às funcionalidades

### Gestão de Custos
- Visualização do total de custos
- Acesso rápido à adição de novos custos
- Modal dedicado para gerenciamento de custos

### Comunicação
- Card dedicado para interações
- Acesso rápido à adição de novas interações
- Modal para inserção de mensagens

### Fluxo de Trabalho
- Botões de ação específicos para cada status da ordem
- Permissões baseadas no papel do usuário
- Feedback visual após cada ação

## Personalização dos Cards

### Card de Histórico
O card de histórico exibe o nome do equipamento atual e permite acessar o histórico completo do equipamento. Ao clicar no card, o usuário é redirecionado para a página de histórico do equipamento.

### Card de Custos
O card de custos exibe o valor total dos custos associados à ordem. Ao clicar no card, é aberto o modal de custos, onde o usuário pode visualizar os custos existentes e adicionar novos custos.

### Card de Interação
O card de interação permite adicionar comentários e interações relacionadas à ordem. Ao clicar no card, é aberto o modal de interações, onde o usuário pode inserir uma nova mensagem.

### Card de Cronograma
O card de cronograma exibe as datas importantes relacionadas à ordem, como data de criação e prazo estimado. Ao clicar no card, o usuário é redirecionado para a página de calendário.

## Responsividade

A página é totalmente responsiva e se adapta a diferentes tamanhos de tela:

- **Desktop**: Layout completo com todas as funcionalidades
- **Tablet**: Reorganização dos elementos para melhor visualização
  - Conteúdo principal em coluna única
  - Cards em duas colunas
- **Mobile**: Empilhamento de todos os elementos
  - Conteúdo principal em coluna única
  - Cards em coluna única
  - Ajuste dos botões de ação

## Como Editar

### Modificar o Layout
Para modificar o layout da página, edite o arquivo `web/templates/ordens/order_detail_service.html`. O template utiliza a estrutura de blocos do Go Templates, o que facilita a manutenção e extensão.

### Alterar Estilos
Para alterar os estilos visuais, edite o arquivo `web/static/css/order_detail_service.css`. Os estilos estão organizados por seções para facilitar a localização e edição:

- Estilos do container principal: `.order-container`
- Estilos do cabeçalho: `.order-header`
- Estilos do conteúdo principal: `.order-main-content`
- Estilos da seção de título: `.maintenance-title-section`
- Estilos da sidebar: `.order-sidebar`
- Estilos dos cards: `.order-cards` e `.order-card`

### Adicionar ou Modificar Funcionalidades
Para adicionar ou modificar funcionalidades, edite o arquivo `web/static/js/order_detail_service.js`. O código JavaScript está organizado em funções específicas para cada funcionalidade:

- Inicialização: `initializeModals()`, `initializeForms()`, `initializeButtons()`, `addAnimationEffects()`
- Manipulação de formulários: `handleAddCostSubmit()`, `handleAddInteractionSubmit()`, etc.
- Manipulação de modais: `openCostsModal()`, `openInteractionModal()`, etc.
- Funções de API: `loadCostsForOrder()`, `loadInteractionsForOrder()`, etc.
- Funções utilitárias: `formatCurrencyJS()`, `formatDateTimeJS()`, etc.

### Adicionar Novos Cards
Para adicionar novos cards à seção de categorias, siga estes passos:

1. Adicione o HTML do novo card na seção `.order-cards` no arquivo `web/templates/ordens/order_detail_service.html`:
   ```html
   <div class="order-card">
       <div class="card-icon">
           <i class="fas fa-icon-name"></i>
       </div>
       <div class="card-title">Título do Card</div>
       <div class="card-content">
           <p>Conteúdo do card</p>
           <p class="clickable-text">Clique para interagir</p>
       </div>
   </div>
   ```

2. Adicione o evento de clique no arquivo `web/static/js/order_detail_service.js`:
   ```javascript
   const newCard = document.querySelector('.order-card:nth-child(5)'); // Ajuste o índice conforme necessário
   if (newCard) {
       newCard.addEventListener('click', function() {
           // Ação do card
           // Por exemplo: abrir um modal ou redirecionar para outra página
       });
   }
   ```

## Considerações Técnicas

### Compatibilidade com Navegadores
A página foi desenvolvida utilizando tecnologias modernas e é compatível com os principais navegadores:
- Google Chrome (recomendado)
- Mozilla Firefox
- Microsoft Edge
- Safari

### Performance
- Carregamento assíncrono de dados via API
- Manipulação eficiente do DOM
- Animações otimizadas para melhor desempenho

### Segurança
- Validação de dados no cliente e no servidor
- Controle de acesso baseado em permissões
- Proteção contra XSS e CSRF

## Troubleshooting

### Problemas Comuns e Soluções

1. **Cards não são clicáveis**
   - Verifique se os eventos de clique estão sendo adicionados corretamente no JavaScript
   - Verifique se os seletores CSS estão corretos
   - Verifique o console do navegador para erros

2. **Modais não abrem**
   - Verifique se o Bootstrap está carregado corretamente
   - Verifique se os IDs dos elementos estão corretos
   - Verifique o console do navegador para erros

3. **Estilos não são aplicados**
   - Verifique se o arquivo CSS está sendo carregado corretamente
   - Verifique se há conflitos de CSS com outros componentes
   - Limpe o cache do navegador

4. **Dados não são carregados**
   - Verifique se as APIs estão funcionando corretamente
   - Verifique se o usuário tem permissões adequadas
   - Verifique o console do navegador para erros
