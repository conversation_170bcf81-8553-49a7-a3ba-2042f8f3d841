# Documentação da Página de Ordens de Serviço

## Visão Geral
A página de Ordens de Serviço foi completamente redesenhada para oferecer uma experiência mais moderna, funcional e intuitiva. A nova interface segue o padrão visual do dashboard, com fundo escuro e elementos visuais que facilitam a identificação e gerenciamento das ordens.

## Principais Recursos

### 1. Métricas em Tempo Real
- **Cards de Métricas**: Exibem contadores para ordens pendentes, em andamento, concluídas e total
- **Atualização Dinâmica**: Os contadores são atualizados automaticamente ao filtrar ou adicionar novas ordens

### 2. Sistema de Filtros Avançados
- **Filtros por Status**: Permite filtrar ordens por seu status atual (aberta, em andamento, concluída, etc.)
- **Filtros por Prioridade**: Filtra ordens por nível de prioridade (baixa, média, alta, urgente)
- **Filtros por Posto/Filial**: Permite visualizar ordens de postos específicos
- **Filtro por Data**: Filtra ordens por data de criação
- **Busca Textual**: Permite buscar por ID, título, equipamento ou outros campos

### 3. Visualização Flexível
- **Modo Tabela**: Visualização tradicional em formato de tabela, com todas as informações organizadas em colunas
- **Modo Cards**: Visualização alternativa em formato de cards, mais visual e adaptada para dispositivos móveis
- **Persistência de Preferência**: O sistema lembra a preferência de visualização do usuário entre sessões

### 4. Detalhes Completos
- **Modal de Detalhes**: Exibe todas as informações da ordem em um modal organizado
- **Histórico de Interações**: Mostra o histórico completo de interações e atualizações da ordem
- **Anexos Visuais**: Exibe miniaturas dos anexos e fotos relacionados à ordem

### 5. Ações Rápidas
- **Botões de Ação**: Botões para visualizar detalhes, editar e excluir ordens
- **Confirmação de Exclusão**: Modal de confirmação para evitar exclusões acidentais
- **Navegação Intuitiva**: Botões e links claramente identificados para facilitar a navegação

## Estrutura da Página

### Cabeçalho
- Título da página com ícone
- Botão de atualização
- Botão para criar nova ordem

### Seção de Métricas
- Cards com contadores para diferentes status de ordens
- Ícones e cores distintas para cada tipo de métrica

### Seção de Filtros
- Campos de seleção para status, prioridade, posto e data
- Campo de busca textual
- Botões para aplicar e limpar filtros

### Tabela de Ordens
- Colunas para ID, título, status, prioridade, posto, data de criação e ações
- Badges coloridos para status e prioridade
- Botões de ação para cada ordem

### Visualização em Cards
- Cards com informações resumidas de cada ordem
- Badges de status e prioridade
- Informações de posto e data
- Botões de ação

### Paginação
- Controles de navegação entre páginas
- Contador de total de ordens

### Modais
- Modal de detalhes da ordem
- Modal de confirmação de exclusão

## Personalização Visual

### Cores e Temas
- **Cores Principais**: Amarelo Shell (#FDB813) e Vermelho Shell (#ED1C24)
- **Fundo**: Tons de cinza escuro (#1E1E1E e #121212)
- **Status**: Cores específicas para cada status (amarelo para pendente, azul para em andamento, verde para concluído, vermelho para cancelado)
- **Prioridade**: Cores específicas para cada nível de prioridade (verde para baixa, azul para média, amarelo para alta, vermelho para urgente)

### Tipografia
- **Títulos**: Fonte Rajdhani para títulos e cabeçalhos
- **Monospace**: Fonte Share Tech Mono para IDs e valores numéricos
- **Texto**: Fonte padrão do Bootstrap para textos gerais

## Responsividade
A página foi projetada para funcionar bem em diferentes tamanhos de tela:

- **Desktop**: Layout completo com todas as funcionalidades
- **Tablet**: Adaptação dos cards de métricas e ajuste da tabela
- **Mobile**: Reorganização dos elementos para melhor visualização em telas pequenas, com preferência para o modo de visualização em cards

## Arquivos Relacionados

### HTML
- `web/templates/ordens/orders.html`: Template principal da página

### CSS
- `web/static/css/ordens_new.css`: Estilos específicos para a nova página de ordens

### JavaScript
- `web/static/js/ordens_new.js`: Funcionalidades específicas da nova página
- `web/static/js/orders.js`: Script original mantido para compatibilidade

## Como Editar

### Adicionar Novo Status
1. Adicione o novo status no backend (controlador e modelo)
2. Adicione a classe CSS correspondente em `ordens_new.css`
3. Atualize a função `formatStatus()` e `getStatusClass()` em `ordens_new.js`

### Adicionar Novo Filtro
1. Adicione o campo de filtro na seção de filtros em `orders.html`
2. Atualize o evento do botão `applyFiltersBtn` em `ordens_new.js`
3. Adicione o tratamento do novo filtro no controlador backend

### Personalizar Aparência dos Cards
1. Edite as classes CSS relacionadas aos cards em `ordens_new.css`
2. Para mudar cores, atualize as variáveis CSS no início do arquivo

## Notas de Implementação
- A página utiliza DataTables para funcionalidades avançadas de tabela
- O sistema de visualização dual (tabela/cards) é controlado via JavaScript
- As preferências do usuário são armazenadas no localStorage do navegador
- Todas as interações com o backend são feitas via API RESTful
