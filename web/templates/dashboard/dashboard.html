{{ define "dashboard/dashboard.html" }}
{{ template "layouts/base_layout.html" . }}
{{ end }}

{{ define "page_styles" }}
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/dashboard_fixes.css">
    <link rel="stylesheet" href="/static/css/mobile-sidebar.css">
{{ end }}

{{ define "content" }}
    <div class="page-header">
        <h1 class="page-title">
            {{ if eq .User.Role "branch" }}Dashboard da Filial{{ else if eq .User.Role "provider" }}Dashboard do Prestador{{ else }}Dashboard{{ end }}
        </h1>
        <div class="page-actions">
            {{/* Ações específicas podem ser adicionadas aqui com base no role */}}
            <button class="shell-btn" id="refreshDataBtn">
                <i class="fas fa-sync-alt"></i> Atualizar
            </button>
            {{ if eq .User.Role "branch" }}
            <button class="shell-btn shell-btn-yellow ms-2" id="createNewOrderBtn">
                 <i class="fas fa-plus"></i> Nova Ordem
            </button>
            {{ end }}
        </div>
    </div>

    <!-- Área de métricas -->
    <div class="row mb-4" id="metricsArea">
        {{ if eq .User.Role "branch" }}
        <!-- Métricas para Filial -->
        <div class="col-md-3">
            <div class="stats-card stats-card-pending">
                <div class="stats-card-title">Pendentes</div>
                <div class="stats-card-value" id="pendingOrdersCount">0</div>
                {{/* <div class="stats-card-trend"><i class="fas fa-arrow-up me-1 trend-up"></i> 8% desde ontem</div> */}}
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card stats-card-progress">
                <div class="stats-card-title">Em Andamento</div>
                <div class="stats-card-value" id="inProgressOrdersCount">0</div>
                 {{/* <div class="stats-card-trend"><i class="fas fa-arrow-up me-1 trend-up"></i> 12% desde ontem</div> */}}
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card stats-card-completed">
                <div class="stats-card-title">Concluídas</div>
                <div class="stats-card-value" id="completedOrdersCount">0</div>
                 {{/* <div class="stats-card-trend"><i class="fas fa-arrow-up me-1 trend-up"></i> 15% desde ontem</div> */}}
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card stats-card-rejected">
                <div class="stats-card-title">Canceladas</div>
                <div class="stats-card-value" id="rejectedOrdersCount">0</div>
                 {{/* <div class="stats-card-trend"><i class="fas fa-arrow-down me-1 trend-down"></i> 5% desde ontem</div> */}}
            </div>
        </div>
        {{ else if eq .User.Role "provider" }}
        <!-- Métricas para Prestador -->
        <div class="col-md-4">
            <div class="stats-card stats-card-assigned">
                <div class="stats-card-title">Ordens Atribuídas</div>
                <div class="stats-card-value" id="assignedOrdersCount">0</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card stats-card-progress">
                <div class="stats-card-title">Em Execução</div>
                <div class="stats-card-value" id="executingOrdersCount">0</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card stats-card-completed">
                <div class="stats-card-title">Finalizadas Hoje</div>
                <div class="stats-card-value" id="completedTodayCount">0</div>
            </div>
        </div>
        {{ end }}
    </div>

    <!-- Área principal -->
    <div class="dashboard-boxes-container">
        <div class="pump-border">
            <div class="shell-header">
                 {{ if eq .User.Role "branch" }}
                 <h4>Ordens de Serviço da Filial</h4>
                 {{ else if eq .UserRole "provider" }}
                 <h4>Minhas Ordens de Serviço Atribuídas</h4>
                 {{ else }}
                 <h4>Ordens de Serviço</h4>
                 {{ end }}
            </div>

            <!-- Filtros -->
            <div class="order-filters mb-3">
                <div class="row">
                     {{ if eq .User.Role "branch" }}
                    <div class="col-md-3">
                        <select class="form-select" id="orderStatusFilter">
                            <option value="all">Todos os Status</option>
                            <option value="pending">Pendentes</option>
                            <option value="in_progress">Em Andamento</option>
                            <option value="completed">Concluídas</option>
                            <option value="rejected">Canceladas</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="orderPriorityFilter">
                            <option value="all">Todas as Prioridades</option>
                            <option value="low">Baixa</option>
                            <option value="medium">Média</option>
                            <option value="high">Alta</option>
                            <option value="urgent">Urgente</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                         <select class="form-select" id="providerFilter">
                            <option value="all">Todos Prestadores</option>
                            {{/* Opções de prestadores seriam carregadas dinamicamente */}}
                            <option value="provider1">Prestador A</option>
                            <option value="provider2">Prestador B</option>
                        </select>
                    </div>
                     <div class="col-md-3">
                        <input type="text" class="form-control" id="orderSearch" placeholder="Buscar por ID ou Título...">
                    </div>
                     {{ else if eq .UserRole "provider" }}
                     <div class="col-md-4">
                        <select class="form-select" id="orderStatusFilter">
                            <option value="all">Todos os Status</option>
                            <option value="pending">Pendentes (Aguardando Aceite)</option>
                            <option value="in_progress">Em Andamento</option>
                            <option value="completed">Concluídas por mim</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" id="orderPriorityFilter">
                            <option value="all">Todas as Prioridades</option>
                            <option value="low">Baixa</option>
                            <option value="medium">Média</option>
                            <option value="high">Alta</option>
                            <option value="urgent">Urgente</option>
                        </select>
                    </div>
                     <div class="col-md-4">
                        <input type="text" class="form-control" id="orderSearch" placeholder="Buscar por ID ou Título...">
                    </div>
                     {{ end }}
                </div>
            </div>

            <!-- Grid de ordens de serviço -->
            <div class="service-orders-grid" id="serviceOrdersGrid">
                <div class="order-detail-placeholder" id="ordersPlaceholder">
                    <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                    <p>Carregando ordens...</p>
                </div>
            </div>
            <div class="fuel-gauge mt-3"></div>
        </div>

        <!-- Área para visualização detalhada da ordem selecionada -->
        <div class="order-detail-container pump-border" id="orderDetailContainer" style="display: none;">
             <div class="shell-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0" style="color: var(--shell-yellow);">Detalhes da Ordem</h4>
                <button class="btn-close btn-close-white" aria-label="Close" onclick="closeOrderDetails()"></button>
            </div>
            <div id="orderDetailContent" class="mt-3">
                <!-- Conteúdo será preenchido via JavaScript -->
            </div>
            <div class="fuel-gauge mt-3"></div>
        </div>
    </div>

{{/* <script src="/static/js/dashboard.js"></script> */}} {{/* Idealmente, a lógica JS estaria em um arquivo separado */}}
<script>
    window.userRole = "{{ .UserRole }}"; // Passa o role para o JS
    let currentOrders = []; // Armazena as ordens atuais (viriam do backend)

    // Arrays para armazenar ordens carregadas da API
    let branchSampleOrders = [];
    let providerSampleOrders = [];

    // --- Funções Principais ---

    function loadServiceOrders(orders) {
        const grid = document.getElementById('serviceOrdersGrid');
        const placeholder = document.getElementById('ordersPlaceholder');
        grid.innerHTML = ''; // Limpa o grid

        if (orders.length === 0) {
            placeholder.innerHTML = '<i class="fas fa-info-circle fa-3x mb-3"></i><p>Nenhuma ordem encontrada para os filtros selecionados.</p>';
            placeholder.style.display = 'flex'; // Reexibe se estava oculto
            return;
        }

        placeholder.style.display = 'none'; // Oculta o placeholder

        orders.forEach(order => {
            const orderCard = document.createElement('div');
            orderCard.className = 'service-order-item';
            orderCard.setAttribute('data-status', order.status);
            orderCard.setAttribute('data-priority', order.priority);
            // Add other data attributes as needed for filtering
            orderCard.setAttribute('data-id', order.id);
            if (userRole === 'branch' && order.provider) {
                 orderCard.setAttribute('data-provider', order.provider);
            }

            let cardContent = `
                <div class="order-priority ${order.priority}" title="Prioridade: ${order.priorityText}"></div>
                <div class="order-title">${order.id}: ${order.title}</div>
                <div class="order-status ${order.status}">${order.statusText}</div>
                <div class="order-info">
                    <div><i class="fas fa-map-marker-alt"></i> ${order.station}</div>
                    <div><i class="fas fa-calendar-alt"></i> ${order.date}</div>
                </div>
                 ${userRole === 'branch' ? `
                    <div class="order-info">
                        <div><i class="fas fa-user-cog"></i> Prestador: ${order.providerName || 'Não atribuído'}</div>
                    </div>` : ''}
                <p class="mt-2 description-preview">${order.description}</p>
            `;
            orderCard.innerHTML = cardContent;

            orderCard.addEventListener('click', () => showOrderDetails(order));
            grid.appendChild(orderCard);
        });
    }

    function showOrderDetails(order) {
        const detailContainer = document.getElementById('orderDetailContainer');
        const detailContent = document.getElementById('orderDetailContent');

        // Mostrar indicador de carregamento
        detailContent.innerHTML = '<div class="text-center p-4"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">Carregando detalhes da ordem...</p></div>';
        detailContainer.style.display = 'block';

        // Scroll para os detalhes suavemente
        detailContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });

        // Destacar o card selecionado
        document.querySelectorAll('.service-order-item.selected').forEach(card => card.classList.remove('selected'));
        const selectedCard = document.querySelector(`.service-order-item[data-id="${order.id}"]`);
        if (selectedCard) {
            selectedCard.classList.add('selected');
        }

        // Buscar detalhes completos da ordem via API
        fetch(`/api/ordens/${order.id}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    // Mesclar dados da API com os dados existentes
                    const orderDetail = {
                        ...order,
                        ...data.data,
                        // Garantir que os campos formatados sejam mantidos
                        statusText: order.statusText,
                        priorityText: order.priorityText,
                        date: order.date
                    };

                    // Renderizar os detalhes
                    renderOrderDetails(orderDetail);
                } else {
                    console.error('Erro ao carregar detalhes da ordem:', data.message || 'Erro desconhecido');
                    detailContent.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Erro ao carregar detalhes da ordem: ${data.message || 'Erro desconhecido'}
                        </div>
                        <div class="d-flex justify-content-center mt-3">
                            <button class="shell-btn shell-btn-grey" onclick="closeOrderDetails()">
                                <i class="fas fa-times"></i> Fechar
                            </button>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Erro ao carregar detalhes da ordem:', error);
                detailContent.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Erro ao carregar detalhes da ordem: ${error.message}
                    </div>
                    <div class="d-flex justify-content-center mt-3">
                        <button class="shell-btn shell-btn-grey" onclick="closeOrderDetails()">
                            <i class="fas fa-times"></i> Fechar
                        </button>
                    </div>
                `;
            });
    }

    // Renderiza os detalhes da ordem
    function renderOrderDetails(order) {
        const detailContent = document.getElementById('orderDetailContent');
        let detailHtml = '';

        // Conteúdo Comum
        detailHtml += `
            <div class="row">
                <div class="col-md-6">
                    <h5>Informações Gerais</h5>
                    <ul class="list-group list-group-flush bg-transparent">
                        <li class="list-group-item bg-transparent text-white">ID: <span class="fw-bold">${order.id}</span></li>
                        <li class="list-group-item bg-transparent text-white">Título: <span class="fw-bold">${order.title}</span></li>
                        <li class="list-group-item bg-transparent text-white">Status: <span class="badge rounded-pill status-badge-${order.status}">${order.statusText}</span></li>
                        <li class="list-group-item bg-transparent text-white">Prioridade: <span class="badge rounded-pill priority-badge-${order.priority}">${order.priorityText}</span></li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>Localização e Datas</h5>
                    <ul class="list-group list-group-flush bg-transparent">
                        <li class="list-group-item bg-transparent text-white">Posto/Filial: <span class="fw-bold">${order.station}</span></li>
                        <li class="list-group-item bg-transparent text-white">Data Abertura: <span class="fw-bold">${order.date}</span></li>
                         ${userRole === 'branch' ? `<li class="list-group-item bg-transparent text-white">Prestador: <span class="fw-bold">${order.providerName || 'Não atribuído'}</span></li>` : ''}
                         ${userRole === 'provider' ? `<li class="list-group-item bg-transparent text-white">Prazo Estimado: <span class="fw-bold">${order.estimated_time || order.tempo_estimado || 'A definir'}</span></li>` : ''}
                    </ul>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <h5>Descrição Completa</h5>
                    <p>${order.description}</p>
                </div>
            </div>
        `;

        // Informações adicionais se disponíveis
        if (order.equipment_name || order.equipamento) {
            detailHtml += `
                <div class="row mt-3">
                    <div class="col-12">
                        <h5>Equipamento</h5>
                        <p>${order.equipment_name || order.equipamento}</p>
                    </div>
                </div>
            `;
        }

        // Histórico de interações se disponível
        if (order.interactions && order.interactions.length > 0) {
            detailHtml += `
                <div class="row mt-3">
                    <div class="col-12">
                        <h5>Histórico de Interações</h5>
                        <div class="interaction-history">
            `;

            order.interactions.forEach(interaction => {
                detailHtml += `
                    <div class="interaction-item">
                        <div class="interaction-header">
                            <span class="interaction-user">${interaction.user}</span>
                            <span class="interaction-date">${interaction.date || interaction.datetime}</span>
                        </div>
                        <div class="interaction-message">${interaction.message}</div>
                    </div>
                `;
            });

            detailHtml += `
                        </div>
                    </div>
                </div>
            `;
        }

        // Ações Específicas por Role
        detailHtml += '<div class="row mt-4"><div class="col-12"><div class="d-flex justify-content-end flex-wrap gap-2">';
        if (userRole === 'branch') {
            detailHtml += `
                <button class="shell-btn shell-btn-grey" onclick="closeOrderDetails()">
                    <i class="fas fa-times"></i> Fechar
                </button>
                 ${order.status !== 'completed' && order.status !== 'rejected' &&
                   order.status !== 'concluido' && order.status !== 'concluída' &&
                   order.status !== 'cancelada' ? `
                <button class="shell-btn shell-btn-yellow">
                    <i class="fas fa-edit"></i> Editar Ordem
                </button>
                 <button class="shell-btn">
                    <i class="fas fa-user-plus"></i> ${order.provider ? 'Reatribuir' : 'Atribuir Prestador'}
                </button>
                 <button class="shell-btn shell-btn-red">
                    <i class="fas fa-ban"></i> Cancelar Ordem
                </button>` : ''}
            `;
        } else if (userRole === 'provider') {
             detailHtml += `
                <button class="shell-btn shell-btn-grey" onclick="closeOrderDetails()">
                    <i class="fas fa-times"></i> Fechar
                </button>
                ${(order.status === 'pending' || order.status === 'pendente') ? `
                <button class="shell-btn shell-btn-green">
                    <i class="fas fa-check"></i> Aceitar Ordem
                </button>
                 <button class="shell-btn shell-btn-red">
                    <i class="fas fa-times"></i> Recusar Ordem
                </button>` : ''}
                 ${(order.status === 'in_progress' || order.status === 'em_andamento') ? `
                 <button class="shell-btn shell-btn-blue">
                    <i class="fas fa-pause"></i> Pausar Execução
                </button>
                <button class="shell-btn shell-btn-yellow">
                    <i class="fas fa-flag-checkered"></i> Finalizar Ordem
                </button>` : ''}
                  ${order.status === 'paused' ? `
                 <button class="shell-btn shell-btn-green">
                    <i class="fas fa-play"></i> Retomar Execução
                </button>`: ''}
            `;
        }
        detailHtml += '</div></div></div>';

        detailContent.innerHTML = detailHtml;
    }

    function closeOrderDetails() {
        const detailContainer = document.getElementById('orderDetailContainer');
        detailContainer.style.display = 'none';
        document.querySelectorAll('.service-order-item.selected').forEach(card => card.classList.remove('selected'));

    }

    function applyFilters() {
        const statusFilter = document.getElementById('orderStatusFilter')?.value || 'all';
        const priorityFilter = document.getElementById('orderPriorityFilter')?.value || 'all';
        const providerFilter = document.getElementById('providerFilter')?.value || 'all'; // Only for branch
        const searchTerm = document.getElementById('orderSearch')?.value.toLowerCase() || '';

        // Usar os dados carregados da API
        let baseOrders = (userRole === 'branch') ? branchSampleOrders : providerSampleOrders;
        let filteredOrders = [...baseOrders];

        // Status Filter
        if (statusFilter !== 'all') {
            filteredOrders = filteredOrders.filter(order => {
                // Mapear valores de status para corresponder aos valores do filtro
                if (statusFilter === 'pending' && (order.status === 'pending' || order.status === 'pendente')) {
                    return true;
                }
                if (statusFilter === 'in_progress' && (order.status === 'in_progress' || order.status === 'em_andamento')) {
                    return true;
                }
                if (statusFilter === 'completed' && (order.status === 'completed' || order.status === 'concluido' || order.status === 'concluída')) {
                    return true;
                }
                if (statusFilter === 'rejected' && (order.status === 'rejected' || order.status === 'cancelada')) {
                    return true;
                }
                return order.status === statusFilter;
            });
        }

        // Priority Filter
        if (priorityFilter !== 'all') {
            filteredOrders = filteredOrders.filter(order => {
                // Mapear valores de prioridade para corresponder aos valores do filtro
                if (priorityFilter === 'low' && (order.priority === 'low' || order.priority === 'baixa')) {
                    return true;
                }
                if (priorityFilter === 'medium' && (order.priority === 'medium' || order.priority === 'média' || order.priority === 'media')) {
                    return true;
                }
                if (priorityFilter === 'high' && (order.priority === 'high' || order.priority === 'alta')) {
                    return true;
                }
                if (priorityFilter === 'urgent' && (order.priority === 'urgent' || order.priority === 'urgente')) {
                    return true;
                }
                return order.priority === priorityFilter;
            });
        }

        // Provider Filter (Branch only)
        if (userRole === 'branch' && providerFilter !== 'all') {
            filteredOrders = filteredOrders.filter(order => {
                // Verificar se o ID do prestador corresponde ao filtro
                return order.provider === providerFilter;
            });
        }

        // Search Term Filter (ID or Title)
        if (searchTerm) {
            filteredOrders = filteredOrders.filter(order => {
                const idStr = order.id.toString().toLowerCase();
                const title = (order.title || '').toLowerCase();
                const description = (order.description || '').toLowerCase();
                const station = (order.station || '').toLowerCase();

                return idStr.includes(searchTerm) ||
                       title.includes(searchTerm) ||
                       description.includes(searchTerm) ||
                       station.includes(searchTerm);
            });
        }

        // Atualizar a visualização atual
        currentOrders = filteredOrders;
        loadServiceOrders(currentOrders);
        closeOrderDetails(); // Fechar detalhes quando os filtros mudam
    }

    function updateMetricCounters() {
        // Usar os dados carregados da API
        let baseOrders = (userRole === 'branch') ? branchSampleOrders : providerSampleOrders;

        if (userRole === 'branch') {
            // Contar ordens por status para filial
            const pendingCount = baseOrders.filter(o =>
                o.status === 'pending' || o.status === 'pendente'
            ).length;

            const inProgressCount = baseOrders.filter(o =>
                o.status === 'in_progress' || o.status === 'em_andamento'
            ).length;

            const completedCount = baseOrders.filter(o =>
                o.status === 'completed' || o.status === 'concluido' || o.status === 'concluída'
            ).length;

            const rejectedCount = baseOrders.filter(o =>
                o.status === 'rejected' || o.status === 'cancelada'
            ).length;

            // Atualizar contadores na interface
            document.getElementById('pendingOrdersCount').textContent = pendingCount;
            document.getElementById('inProgressOrdersCount').textContent = inProgressCount;
            document.getElementById('completedOrdersCount').textContent = completedCount;
            document.getElementById('rejectedOrdersCount').textContent = rejectedCount;
        } else if (userRole === 'provider') {
            // Métricas específicas para prestador
            // Todas as ordens atribuídas ao prestador
            const assignedCount = baseOrders.length;

            // Ordens em andamento
            const executingCount = baseOrders.filter(o =>
                o.status === 'in_progress' || o.status === 'em_andamento'
            ).length;

            // Ordens concluídas hoje
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            const completedTodayCount = baseOrders.filter(o => {
                // Verificar se a ordem está concluída
                const isCompleted = o.status === 'completed' || o.status === 'concluido' || o.status === 'concluída';

                // Verificar se a data de conclusão é hoje (se disponível)
                let isToday = false;
                if (o.completion_date) {
                    const completionDate = new Date(o.completion_date);
                    completionDate.setHours(0, 0, 0, 0);
                    isToday = completionDate.getTime() === today.getTime();
                }

                return isCompleted && (isToday || true); // Se não tiver data de conclusão, incluir todas as concluídas
            }).length;

            // Atualizar contadores na interface
            document.getElementById('assignedOrdersCount').textContent = assignedCount;
            document.getElementById('executingOrdersCount').textContent = executingCount;
            document.getElementById('completedTodayCount').textContent = completedTodayCount;
        }
    }

    // Função para carregar ordens da API
    function loadOrdersFromAPI() {
        // Mostrar indicador de carregamento
        const grid = document.getElementById('serviceOrdersGrid');
        const placeholder = document.getElementById('ordersPlaceholder');
        if (placeholder) {
            placeholder.innerHTML = '<i class="fas fa-spinner fa-spin fa-3x mb-3"></i><p>Carregando ordens...</p>';
            placeholder.style.display = 'flex';
        }

        // Determinar a URL da API com base no papel do usuário
        let apiUrl = '/api/ordens';
        if (userRole === 'provider') {
            apiUrl = '/api/ordens/tecnico'; // Endpoint específico para técnicos/prestadores
        }

        // Buscar ordens do servidor via API
        fetch(apiUrl)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    // Processar dados para o formato esperado
                    const processedOrders = data.data.map(order => {
                        // Determinar o texto de status com base no status
                        let statusText = 'Pendente';
                        if (order.status === 'in_progress' || order.status === 'em_andamento') {
                            statusText = userRole === 'provider' ? 'Em Execução' : 'Em Andamento';
                        } else if (order.status === 'completed' || order.status === 'concluido' || order.status === 'concluída') {
                            statusText = userRole === 'provider' ? 'Finalizada' : 'Concluída';
                        } else if (order.status === 'rejected' || order.status === 'cancelada') {
                            statusText = 'Cancelada';
                        } else if (order.status === 'pending' && userRole === 'provider') {
                            statusText = 'Aguardando Aceite';
                        }

                        // Determinar o texto de prioridade com base na prioridade
                        let priorityText = 'Normal';
                        if (order.priority === 'high' || order.priority === 'alta') {
                            priorityText = 'Alta';
                        } else if (order.priority === 'medium' || order.priority === 'média' || order.priority === 'media') {
                            priorityText = 'Média';
                        } else if (order.priority === 'low' || order.priority === 'baixa') {
                            priorityText = 'Baixa';
                        } else if (order.priority === 'urgent' || order.priority === 'urgente') {
                            priorityText = 'Urgente';
                        }

                        // Formatar a data
                        let formattedDate = 'Data não disponível';
                        if (order.due_date) {
                            const dueDate = new Date(order.due_date);
                            formattedDate = `${dueDate.getDate().toString().padStart(2, '0')}/${(dueDate.getMonth() + 1).toString().padStart(2, '0')}/${dueDate.getFullYear()}`;
                        } else if (order.data) {
                            const dueDate = new Date(order.data);
                            formattedDate = `${dueDate.getDate().toString().padStart(2, '0')}/${(dueDate.getMonth() + 1).toString().padStart(2, '0')}/${dueDate.getFullYear()}`;
                        }

                        return {
                            id: order.id,
                            title: order.title || order.titulo || order.problem || 'Ordem de Serviço',
                            status: order.status,
                            statusText: order.status_display || statusText,
                            priority: order.priority || order.prioridade || 'normal',
                            priorityText: order.priority_display || priorityText,
                            date: formattedDate,
                            station: order.branch_name || order.filial || 'Não especificado',
                            description: order.description || order.problema || 'Sem descrição',
                            provider: order.service_provider_id || order.prestador_id,
                            providerName: order.service_provider_name || order.prestador || 'Não atribuído',
                            assignedToMe: true // Assumimos que se a ordem foi retornada pela API, está atribuída ao usuário
                        };
                    });

                    // Armazenar ordens processadas
                    if (userRole === 'branch') {
                        branchSampleOrders = processedOrders;
                    } else {
                        providerSampleOrders = processedOrders;
                    }

                    // Definir ordens atuais
                    currentOrders = processedOrders;

                    // Renderizar ordens e atualizar contadores
                    loadServiceOrders(currentOrders);
                    updateMetricCounters();
                } else {
                    console.error('Erro ao carregar ordens:', data.message || 'Erro desconhecido');
                    if (placeholder) {
                        placeholder.innerHTML = `
                            <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                            <p>Erro ao carregar ordens: ${data.message || 'Erro desconhecido'}</p>
                        `;
                        placeholder.style.display = 'flex';
                    }
                }
            })
            .catch(error => {
                console.error('Erro ao carregar ordens:', error);
                if (placeholder) {
                    placeholder.innerHTML = `
                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                        <p>Erro ao carregar ordens: ${error.message}</p>
                    `;
                    placeholder.style.display = 'flex';
                }
            });
    }

    // --- Inicialização ---
    document.addEventListener('DOMContentLoaded', function() {
        // Carregar ordens da API
        loadOrdersFromAPI();

        // Inicializar contadores de métricas (serão atualizados quando os dados forem carregados)
        updateMetricCounters();

        // Adiciona Listeners aos Filtros
        document.getElementById('orderStatusFilter')?.addEventListener('change', applyFilters);
        document.getElementById('orderPriorityFilter')?.addEventListener('change', applyFilters);
        document.getElementById('providerFilter')?.addEventListener('change', applyFilters); // Only exists for branch
        document.getElementById('orderSearch')?.addEventListener('input', applyFilters);


        // Botão de Atualizar (recarrega dados da API)
        document.getElementById('refreshDataBtn').addEventListener('click', function() {
            console.log("Atualizando dados...");

            // Adicionar feedback visual de atualização
            const refreshBtn = document.getElementById('refreshDataBtn');
            const originalContent = refreshBtn.innerHTML;
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Atualizando...';
            refreshBtn.disabled = true;

            // Recarregar dados da API
            loadOrdersFromAPI();

            // Fechar detalhes da ordem
            closeOrderDetails();

            // Restaurar botão após um breve delay
            setTimeout(() => {
                refreshBtn.innerHTML = originalContent;
                refreshBtn.disabled = false;
            }, 1000);
        });

         // Botão Nova Ordem (Branch only) - Adicionar lógica real
         document.getElementById('createNewOrderBtn')?.addEventListener('click', function() {
             alert('Funcionalidade "Nova Ordem" a ser implementada.');
             // Aqui abriria um modal ou navegaria para outra página
         });

         // Simula a adição de classes de status/prioridade dinamicamente (melhor seria no CSS)
         const styleSheet = document.styleSheets[0];
         const statuses = ['pending', 'in_progress', 'completed', 'rejected', 'assigned', 'paused'];
         const priorities = ['low', 'medium', 'high', 'urgent'];
         const statusColors = { pending: 'var(--bs-warning)', in_progress: 'var(--bs-primary)', completed: 'var(--bs-success)', rejected: 'var(--bs-danger)', assigned: 'var(--bs-info)', paused: 'var(--bs-secondary)' };
         const priorityColors = { low: 'var(--bs-success)', medium: 'var(--bs-warning)', high: 'var(--bs-danger)', urgent: 'var(--shell-red)' }; // Usando shell-red para urgent

         statuses.forEach(status => {
             styleSheet.insertRule(`.status-badge-${status} { background-color: ${statusColors[status]}; color: white; }`, styleSheet.cssRules.length);
             styleSheet.insertRule(`.service-order-item[data-status="${status}"] .order-status.${status} { background-color: ${statusColors[status]}; color: white; }`, styleSheet.cssRules.length); // Para o badge no card
         });
          priorities.forEach(priority => {
             styleSheet.insertRule(`.priority-badge-${priority} { background-color: ${priorityColors[priority]}; color: white; }`, styleSheet.cssRules.length);
             styleSheet.insertRule(`.service-order-item .order-priority.${priority} { background-color: ${priorityColors[priority]}; }`, styleSheet.cssRules.length); // Para a barra de prioridade no card
         });


    });
</script>
{{ end }}

{{ define "page_scripts" }}
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/dashboard.js"></script>
    <script src="/static/js/mobile-sidebar-toggle.js"></script>
{{ end }}