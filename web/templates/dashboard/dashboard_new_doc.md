# Documentação da Nova Dashboard - Shell Tradição

## Visão Geral

A nova dashboard foi projetada para servir como página inicial do sistema, com foco em informações sobre o sistema, dicas e elementos visuais interessantes, sem ferramentas de trabalho direto. A dashboard apresenta um design moderno, animações sutis e elementos interativos que tornam a experiência do usuário mais agradável.

## Arquivos Relacionados

- **Template HTML**: `web/templates/dashboard/dashboard_new.html`
- **CSS**: `web/static/css/dashboard_new.css`
- **JavaScript**: `web/static/js/dashboard_new.js`
- **Rota**: Configurada em `cmd/main.go` na rota `/dashboard`

## Estrutura da Dashboard

A dashboard é composta por várias seções:

1. **Cabeçalho Animado**
   - Título da dashboard com animação de entrada
   - Letreiro LED com frases motivacionais e dicas

2. **Seção de Boas-vindas**
   - Card com mensagem de boas-vindas personalizada com o nome do usuário
   - Informações sobre o horário atual e data
   - Charada ou curiosidade do dia (com animação de flip para ver a resposta)

3. **Seção de Dicas e Informações**
   - Cards com dicas sobre o sistema
   - Informações sobre a Shell Tradição
   - Curiosidades sobre combustíveis e postos

4. **Seção de Navegação Rápida**
   - Cards visuais para navegação rápida para as principais seções do sistema
   - Ícones animados e efeitos de hover

5. **Seção de Novidades**
   - Informações sobre atualizações recentes do sistema
   - Dicas de novos recursos

6. **Rodapé Interativo**
   - Informações de contato
   - Links úteis
   - Versão do sistema

## Elementos Visuais e Interativos

1. **Animações**
   - Efeitos de fade-in para os elementos ao carregar a página
   - Animações de hover nos cards
   - Efeito de flip nos cards de curiosidades
   - Animação de partículas no fundo (sutis, representando gotas de combustível)

2. **Elementos Interativos**
   - Cards clicáveis para navegação
   - Charadas interativas (clique para ver a resposta)
   - Botão para atualizar a curiosidade/charada do dia
   - Modais informativos (Manual do Usuário, FAQ, Changelog)

3. **Design Visual**
   - Cores da Shell (vermelho e amarelo) como destaques
   - Fundo escuro seguindo o tema do sistema
   - Efeitos de vidro (glass effect) nos cards
   - Ícones animados

## Funcionalidades JavaScript

1. **Relógio e Data**
   - Exibe a data e hora atual, atualizando a cada segundo
   - Função: `initDateTime()`

2. **Letreiro LED**
   - Exibe mensagens motivacionais e dicas em um letreiro animado
   - Função: `initLEDMarquee()`

3. **Card de Charada**
   - Exibe uma charada aleatória com efeito de flip para ver a resposta
   - Função: `initRiddleCard()`

4. **Modais Informativos**
   - Exibe modais com informações úteis (Manual, FAQ, Changelog)
   - Função: `initModalLinks()`

5. **Partículas de Fundo**
   - Cria um efeito de partículas animadas no fundo da página
   - Função: `initParticles()`

## Como Personalizar

### Adicionar Novas Charadas

Para adicionar novas charadas, edite o arquivo `web/static/js/dashboard_new.js` e adicione novos objetos ao array `riddles`:

```javascript
const riddles = [
    {
        question: "Sua pergunta aqui?",
        answer: "Sua resposta aqui!"
    },
    // Adicione mais charadas aqui
];
```

### Adicionar Novas Mensagens ao Letreiro

Para adicionar novas mensagens ao letreiro LED, edite o arquivo `web/static/js/dashboard_new.js` e adicione novas strings ao array `messages`:

```javascript
const messages = [
    "Sua mensagem aqui!",
    // Adicione mais mensagens aqui
];
```

### Modificar as Seções de Dicas e Informações

Para modificar as dicas e informações exibidas, edite o arquivo `web/templates/dashboard/dashboard_new.html` e atualize o conteúdo dos cards na seção `tips-section`.

### Atualizar as Novidades do Sistema

Para atualizar as novidades do sistema, edite o arquivo `web/templates/dashboard/dashboard_new.html` e atualize o conteúdo dos itens na seção `news-section`.

## Responsividade

A dashboard é totalmente responsiva e se adapta a diferentes tamanhos de tela:

- **Desktop**: Layout completo com todas as seções lado a lado
- **Tablet**: Layout ajustado com algumas seções empilhadas
- **Mobile**: Layout vertical com todas as seções empilhadas

## Acessibilidade

A dashboard foi projetada com considerações de acessibilidade:

- Cores contrastantes para melhor legibilidade
- Textos descritivos para ícones
- Estrutura semântica HTML para leitores de tela
- Tamanhos de fonte ajustáveis

## Manutenção e Atualizações

Para manter a dashboard atualizada:

1. **Atualizações de Conteúdo**: Edite o arquivo HTML para atualizar textos, links e informações
2. **Atualizações Visuais**: Edite o arquivo CSS para modificar cores, tamanhos e animações
3. **Atualizações Funcionais**: Edite o arquivo JavaScript para modificar comportamentos e interações

## Considerações de Desempenho

Para garantir o melhor desempenho:

1. As animações são leves e não sobrecarregam o navegador
2. As imagens são otimizadas para carregamento rápido
3. O JavaScript é modular e carregado de forma eficiente
4. As partículas de fundo são configuradas para não impactar o desempenho

## Conclusão

A nova dashboard oferece uma experiência de usuário moderna e agradável, com foco em informações úteis e navegação intuitiva. Ela serve como um ponto de entrada acolhedor para o sistema, apresentando informações relevantes e acesso rápido às principais funcionalidades.
