{{ define "tax-calculator.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculadora de Impostos - Shell</title>
    
    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&family=Share+Tech+Mono&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/tax-calculator.css">
</head>
<body class="bg-dark">
    {{ template "sidebar" . }}
    
    <div class="content-with-sidebar">
        <div class="main-content">
            <div class="container-fluid">
                <div class="page-header">
                    <h1><i class="fas fa-calculator"></i> Calculadora de Impostos</h1>
                </div>
                
                <div class="page-content">
                    <div class="tax-calculator">
                        <form id="taxCalculatorForm">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="state">Estado</label>
                                        <select class="form-control" id="state" required>
                                            <option value="">Selecione o estado...</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="fuelType">Tipo de Combustível</label>
                                        <select class="form-control" id="fuelType" required>
                                            <option value="">Selecione o combustível...</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="baseValue">Valor Base (R$)</label>
                                        <input type="number" class="form-control" id="baseValue" step="0.01" min="0" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-shell-calculate">
                                    <i class="fas fa-calculator"></i> Calcular Impostos
                                </button>
                            </div>
                        </form>
                        
                        <div id="errorMessage"></div>
                        
                        <div id="resultsContainer" class="results-container">
                            <h3 class="mb-4">Resultado do Cálculo</h3>
                            
                            <div class="table-responsive">
                                <table class="table table-shell">
                                    <thead>
                                        <tr>
                                            <th>Imposto</th>
                                            <th>Alíquota</th>
                                            <th>Valor (R$)</th>
                                        </tr>
                                    </thead>
                                    <tbody id="resultsTableBody"></tbody>
                                </table>
                            </div>
                            
                            <div class="totals">
                                <div class="total-item">
                                    <span class="total-label">Total de Impostos:</span>
                                    <span id="totalTax" class="total-value">R$ 0,00</span>
                                </div>
                                <div class="total-item">
                                    <span class="total-label">Valor Final:</span>
                                    <span id="finalValue" class="total-value">R$ 0,00</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/impostos.js"></script>
    <script src="/static/js/tax-calculator.js"></script>
</body>
</html>
{{ end }} 