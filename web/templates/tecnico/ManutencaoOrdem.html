{{ define "tecnico/Ordemtecnico.html" }}
<!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .title }}</title>
    <link rel="icon" href="/static/images/favicon.ico" type="image/x-icon">

    <!-- Bootstrap e estilos base -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/calendar-flip.css">

    <!-- <PERSON><PERSON><PERSON> -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">

    <style>
        /* Estas definições são específicas desta página e não estão no CSS global */
        body {
            background-color: #1a1a1a;
            background-image: linear-gradient(45deg, #111 25%, transparent 25%, transparent 75%, #111 75%, #111),
                linear-gradient(45deg, #111 25%, transparent 25%, transparent 75%, #111 75%, #111);
            background-size: 60px 60px;
            background-position: 0 0, 30px 30px;
        }

        /* Botão para voltar ao dashboard */
        .btn-back-to-dashboard {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: #FFCC00;
            color: #000;
            padding: 8px 15px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            z-index: 100;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-back-to-dashboard:hover {
            background-color: #ffcd00;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        /* Estilos específicos para ordens urgentes */
        .urgent-styled {
            border-left: 4px solid #dc3545 !important;
            background-color: rgba(220, 53, 69, 0.1);
            box-shadow: 0 3px 6px rgba(220, 53, 69, 0.2);
            position: relative;
        }

        .urgent-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #dc3545;
            color: white;
            font-size: 10px;
            font-weight: bold;
            padding: 3px 6px;
            border-radius: 3px;
            letter-spacing: 0.5px;
        }

        /* Estilos para o modal de seleção de ordens */
        .order-selection-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;Read file
            Ordemtecnico.html
            web/templates/tecnico
            Agora vamos verificar como os usuários são criados e gerenciados no sistema:

            Read file
            user.go
            internal/models

            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .order-selection-modal.active {
            opacity: 1;
            visibility: visible;
        }

        .order-selection-content {
            background-color: #1e1e1e;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            padding: 20px;
            position: relative;
            border: 2px solid #FFCC00;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .order-selection-modal.active .order-selection-content {
            transform: scale(1);
        }

        .close-selection-modal {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: #fff;
            font-size: 20px;
            cursor: pointer;
        }

        .order-option {
            display: flex;
            padding: 15px;
            border-bottom: 1px solid #333;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .order-option:hover {
            background-color: #2a2a2a;
        }

        .order-option:last-child {
            border-bottom: none;
        }

        .order-indicator {
            width: 10px;
            height: 100%;
            margin-right: 15px;
            border-radius: 3px;
        }

        .order-details {
            flex: 1;
        }

        .order-details h4 {
            margin: 0 0 8px 0;
            color: #FFCC00;
        }

        .order-details p {
            margin: 5px 0;
            color: #ccc;
        }

        .container-fluid {
            padding: 20px;
        }

        /* Para corrigir possíveis problemas de escala */
        .calendar-dashboard-container {
            transform: scale(1);
            transform-origin: center top;
        }

        /* Estilos para a seção de ordens em destaque */
        .featured-orders-section {
            margin-bottom: 20px;
            padding: 15px;
            background-color: rgba(30, 30, 30, 0.9);
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
            border: 1px solid #444;
        }

        .featured-orders-section h4 {
            color: var(--shell-yellow);
            margin-bottom: 15px;
            border-bottom: 2px solid var(--shell-yellow);
            padding-bottom: 10px;
        }

        .featured-orders-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .featured-orders-category h5 {
            font-size: 1rem;
            margin-bottom: 10px;
            color: #eee;
        }

        .featured-orders-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .featured-order-item {
            display: flex;
            align-items: center;
            background: #222;
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            border: 1px solid #333;
        }

        .featured-order-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .featured-order-indicator {
            width: 10px;
            height: 40px;
            border-radius: 3px;
            margin-right: 10px;
        }

        .featured-order-indicator.urgent {
            background-color: #dc3545;
        }

        .featured-order-indicator.scheduled {
            background-color: #0d6efd;
        }

        .featured-order-content {
            flex-grow: 1;
        }

        .featured-order-content h6 {
            margin: 0 0 5px 0;
            font-size: 0.9rem;
            font-weight: 600;
            color: #ddd;
        }

        .featured-order-details {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            font-size: 0.8rem;
            color: #bbb;
        }

        /* Aumentando o tamanho do calendário após remover o painel lateral */
        .calendar-dashboard-layout {
            grid-template-columns: 1fr !important;
        }

        .calendar-days {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 5px;
        }

        .calendar-day {
            height: 70px;
        }

        /* Estilos para notificações */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            padding: 15px;
            border-radius: 5px;
            background-color: #333;
            color: white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            z-index: 9999;
            max-width: 350px;
            animation: slide-in 0.3s ease-out;
        }

        .notification.success {
            background-color: #28a745;
            border-left: 5px solid #1e7e34;
        }

        .notification.error {
            background-color: #dc3545;
            border-left: 5px solid #bd2130;
        }

        .notification.warning {
            background-color: #ffc107;
            border-left: 5px solid #d39e00;
            color: #333;
        }

        .notification.info {
            background-color: #17a2b8;
            border-left: 5px solid #138496;
        }

        .notification-icon {
            margin-right: 15px;
            font-size: 1.5rem;
        }

        .notification-content {
            flex: 1;
        }

        .notification-close {
            background: none;
            border: none;
            color: inherit;
            cursor: pointer;
            padding: 0;
            margin-left: 10px;
            opacity: 0.7;
        }

        .notification-close:hover {
            opacity: 1;
        }

        .notification.fade-out {
            opacity: 0;
            transform: translateX(30px);
            transition: opacity 0.5s, transform 0.5s;
        }

        @keyframes slide-in {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    </style>
</head>

<body>
    <!-- Botão para voltar ao dashboard -->
    <a href="/dashboard" class="btn-back-to-dashboard">
        <i class="fas fa-tachometer-alt"></i> Voltar ao Dashboard
    </a>

    <div class="container-fluid">
        <!-- Modal para detalhes do card -->
        <div class="service-detail-modal" id="service-detail-modal">
            <div class="service-detail-content">
                <button class="service-detail-close" id="close-modal">
                    <i class="fas fa-times"></i>
                </button>
                <h2 id="modal-title" class="mb-4" style="color: var(--shell-yellow);">Detalhes do Serviço</h2>
                <div id="modal-content">
                    <!-- Conteúdo dinâmico será inserido aqui -->
                </div>
            </div>
        </div>

        <div class="calendar-dashboard-container">
            <!-- Card Giratório 3D -->
            <div class="flip-card-container">
                <div class="flip-card" id="flip-card">
                    <div class="flip-card-inner">
                        <!-- Frente: Calendário -->
                        <div class="flip-card-front">
                            <div class="pump-border calendar-dashboard-layout">
                                <div class="calendar-container">
                                    <div class="calendar-header">
                                        <button class="shell-btn prev-month"><i
                                                class="fas fa-chevron-left"></i></button>
                                        <h3 class="calendar-title">Março 2025</h3>
                                        <button class="shell-btn next-month"><i
                                                class="fas fa-chevron-right"></i></button>
                                    </div>

                                    <div class="calendar-weekdays">
                                        <div>Dom</div>
                                        <div>Seg</div>
                                        <div>Ter</div>
                                        <div>Qua</div>
                                        <div>Qui</div>
                                        <div>Sex</div>
                                        <div>Sáb</div>
                                    </div>

                                    <div class="calendar-days" id="calendar-days">
                                        <!-- Dias da semana já estão na div .calendar-weekdays acima -->
                                        <div class="date">1</div>
                                        <div class="date">2</div>
                                        <div class="date">3</div>
                                        <div class="date">4</div>
                                        <div class="date">5</div>
                                        <div class="date">6</div>
                                        <div class="date">7</div>
                                        <div class="date">8</div>
                                        <div class="date">9</div>
                                        <div class="date has-events" data-events="1" data-status="completed">
                                            10
                                            <div class="event-indicator completed"></div>
                                        </div>
                                        <div class="date">11</div>
                                        <div class="date has-events" data-events="2" data-status="multiple">
                                            12
                                            <div class="event-indicators">
                                                <div class="event-indicator urgent"></div>
                                                <div class="event-indicator scheduled"></div>
                                            </div>
                                        </div>
                                        <div class="date">13</div>
                                        <div class="date has-events" data-events="1" data-status="scheduled">
                                            14
                                            <div class="event-indicator scheduled"></div>
                                        </div>
                                        <div class="date has-events" data-events="1" data-status="urgent">
                                            15
                                            <div class="event-indicator urgent"></div>
                                        </div>
                                        <div class="date">16</div>
                                        <div class="date">17</div>
                                        <div class="date has-events" data-events="1" data-status="approved">
                                            18
                                            <div class="event-indicator approved"></div>
                                        </div>
                                        <div class="date">19</div>
                                        <div class="date has-events" data-events="1" data-status="waiting">
                                            20
                                            <div class="event-indicator waiting"></div>
                                        </div>
                                        <div class="date">21</div>
                                        <div class="date has-events" data-events="1" data-status="approved-scheduled">
                                            22
                                            <div class="event-indicator approved-scheduled"></div>
                                        </div>
                                        <div class="date">23</div>
                                        <div class="date">24</div>
                                        <div class="date has-events" data-events="1" data-status="scheduled">
                                            25
                                            <div class="event-indicator scheduled"></div>
                                        </div>
                                        <div class="date has-events" data-events="1" data-status="waiting">
                                            26
                                            <div class="event-indicator waiting"></div>
                                        </div>
                                        <div class="date has-events" data-events="1" data-status="approved">
                                            27
                                            <div class="event-indicator approved"></div>
                                        </div>
                                        <div class="date has-events" data-events="2" data-status="multiple">
                                            28
                                            <div class="event-indicators">
                                                <div class="event-indicator urgent"></div>
                                                <div class="event-indicator waiting"></div>
                                            </div>
                                        </div>
                                        <div class="date">29</div>
                                        <div class="date has-events" data-events="3" data-status="multiple">
                                            30
                                            <div class="event-indicators">
                                                <div class="event-indicator scheduled"></div>
                                                <div class="event-indicator approved"></div>
                                                <div class="event-indicator waiting"></div>
                                            </div>
                                        </div>
                                        <div class="date">31</div>
                                    </div>
                                </div>

                                <!-- Container removido: texto de instrução e botão de legendas -->
                            </div>
                        </div>

                        <!-- Verso: Detalhes da Ordem -->
                        <div class="flip-card-back">
                            <div class="glass-panel">
                                <div class="panel-header">
                                    <button class="shell-btn back-to-calendar" id="back-to-calendar">
                                        <i class="fas fa-arrow-left"></i> Voltar ao Calendário
                                    </button>
                                    <h3 id="order-panel-title">Detalhes da Ordem</h3>
                                </div>

                                <!-- Conteúdo da ordem -->
                                <div class="order-detail-panel">
                                    <!-- Seção principal de detalhes da ordem -->
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h4 id="order-title" class="text-dark"><span
                                                    id="order-maintenance-type">Manutenção Preventiva</span></h4>

                                            <!-- Prioridade movida para a seção de detalhes principais -->
                                            <div class="d-flex align-items-center mb-2">
                                                <span id="order-priority" class="badge bg-warning text-white">Prioridade
                                                    Média</span>
                                            </div>

                                            <div class="mb-3">
                                                <h5 class="text-muted fs-6">Detalhes da Manutenção</h5>
                                                <p id="order-description">Troca de filtro de combustível da bomba 2.
                                                    Cliente relatou baixa pressão durante abastecimento. Peças já
                                                    disponíveis no estoque local.</p>
                                            </div>

                                            <div class="mb-3">
                                                <h5 class="text-muted fs-6">Filial</h5>
                                                <p id="order-location"><i class="fas fa-map-marker-alt text-danger"></i>
                                                    Posto Shell Ipiranga</p>
                                            </div>

                                            <div class="mb-3">
                                                <h5 class="text-muted fs-6">Equipamento</h5>
                                                <p id="order-equipment"><i class="fas fa-tools text-secondary"></i>
                                                    Bomba de Combustível #2</p>
                                            </div>
                                        </div>

                                        <div class="col-md-4 border-start">
                                            <h5 class="text-muted fs-6">Responsável</h5>
                                            <p id="order-responsible"><i class="fas fa-user text-primary"></i> Carlos
                                                Silva</p>

                                            <h5 class="text-muted fs-6 mt-3">Status Atual</h5>
                                            <p id="order-status"><span class="badge bg-info">Aguardando Aprovação</span>
                                            </p>

                                            <h5 class="text-muted fs-6 mt-3">Tempo Estimado</h5>
                                            <p id="order-time"><i class="far fa-clock"></i> 2 horas</p>

                                            <!-- Botões abaixo do tempo estimado, sem alterar a disposição original -->
                                            <div class="d-flex justify-content-center mt-4">
                                                <button class="shell-btn" id="print-order"
                                                    onclick="printOrderDetails()"><i class="fas fa-print"></i>
                                                    Imprimir</button>
                                            </div>
                                            <div class="d-flex justify-content-between mt-3">
                                                <button class="shell-btn shell-btn-success me-2" id="approve-order"
                                                    onclick="approveOrder()"><i class="fas fa-check-circle"></i>
                                                    Aprovar</button>
                                                <button class="shell-btn shell-btn-danger" id="reject-order"
                                                    onclick="rejectOrder()"><i class="fas fa-times-circle"></i>
                                                    Reprovar</button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Detalhes Ordem -->
                                    <div class="row mt-4">
                                        <div class="col-12">
                                            <h5 class="text-muted border-bottom pb-2">Detalhes Ordem</h5>
                                        </div>
                                        <div class="col-md-3 col-sm-6 mt-3">
                                            <div class="service-box" style="height: 250px; cursor: pointer;" id="manutencao-card">
                                                <div class="service-box-icon">
                                                    <i class="fas fa-wrench"></i>
                                                </div>
                                                <h6>Manutenção</h6>
                                                <div class="text-center mt-2">
                                                    <p class="text-muted small mb-2">
                                                        Informações sobre a manutenção realizada
                                                    </p>
                                                    <div id="manutencao-preview" class="text-start px-2 small" style="max-height: 80px; overflow: hidden; color: #ddd;">
                                                        Clique para adicionar detalhes da manutenção
                                                    </div>
                                                    <button class="btn btn-sm btn-outline-warning mt-2" id="btn-editar-manutencao">
                                                        <i class="fas fa-edit"></i> Editar
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-sm-6 mt-3">
                                            <div class="service-box" style="height: 250px; cursor: pointer;" id="custos-card">
                                                <div class="service-box-icon">
                                                    <i class="fas fa-file-invoice-dollar"></i>
                                                </div>
                                                <h6>Custos</h6>
                                                <div class="text-center mt-2">
                                                    <p class="text-muted small mb-2">
                                                        Registre os custos da manutenção
                                                    </p>
                                                    <div id="custos-preview" class="text-start px-2 small" style="color: #ddd;">
                                                        <div class="d-flex justify-content-between">
                                                            <span>Peças:</span>
                                                            <span>R$ 0,00</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between">
                                                            <span>Mão de obra:</span>
                                                            <span>R$ 0,00</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between">
                                                            <span>Deslocamento:</span>
                                                            <span>R$ 0,00</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mt-2 text-warning fw-bold">
                                                            <span>Total:</span>
                                                            <span>R$ 0,00</span>
                                                        </div>
                                                    </div>
                                                    <button class="btn btn-sm btn-outline-warning mt-2" id="btn-editar-custos">
                                                        <i class="fas fa-edit"></i> Editar
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-sm-6 mt-3">
                                            <div class="service-box" style="height: 250px; cursor: pointer;" id="interacao-card">
                                                <div class="service-box-icon">
                                                    <i class="fas fa-comments"></i>
                                                </div>
                                                <h6>Interação</h6>
                                                <div class="text-center mt-2">
                                                    <p class="text-muted small mb-2">
                                                        Chat com gerentes e financeiro
                                                    </p>
                                                    <div class="chat-preview px-2 small" style="height: 80px; overflow-y: auto; background-color: #222; border-radius: 5px; text-align: left; padding: 5px; color: #ddd;">
                                                        <div class="chat-message-preview">
                                                            <small class="text-warning">Gerente:</small>
                                                            <p class="m-0">Precisa de autorização para compra?</p>
                                                        </div>
                                                        <div class="chat-message-preview">
                                                            <small class="text-info">Você:</small>
                                                            <p class="m-0">Sim, peças não disponíveis no estoque</p>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex justify-content-between mt-2">
                                                        <button class="btn btn-sm btn-outline-warning">
                                                            <i class="fas fa-comment-dots"></i> Chat
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-info">
                                                            <i class="fas fa-upload"></i> Anexar
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-sm-6 mt-3">
                                            <div class="service-box" style="height: 250px; cursor: pointer;" id="cronograma-card">
                                                <div class="service-box-icon">
                                                    <i class="fas fa-calendar-alt"></i>
                                                </div>
                                                <h6>Cronograma</h6>
                                                <div class="text-center mt-2">
                                                    <p class="text-muted small mb-2">
                                                        Estimativa de tempo para manutenção
                                                    </p>
                                                    <div class="schedule-preview px-2 small" style="color: #ddd; text-align: left;">
                                                        <div class="d-flex justify-content-between mb-1">
                                                            <span>Início:</span>
                                                            <span id="schedule-start">20/03/2025 14:00</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-1">
                                                            <span>Término:</span>
                                                            <span id="schedule-end">20/03/2025 16:00</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-1">
                                                            <span>Duração:</span>
                                                            <span id="schedule-duration">2 horas</span>
                                                        </div>
                                                        <div class="progress mt-2" style="height: 8px;">
                                                            <div class="progress-bar bg-warning" role="progressbar" style="width: 50%;" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <button class="btn btn-sm btn-outline-warning mt-2">
                                                        <i class="fas fa-edit"></i> Definir
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Espaço reservado para conteúdo adicional -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Botão Legendas removido daqui e movido para dentro do flip-instruction -->
            </div>
            <!-- A seção Agenda foi removida conforme solicitado -->

            <!-- Modal de Legendas -->
            <div class="modal fade" id="legendModal" tabindex="-1" aria-labelledby="legendModalLabel"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content legend-modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="legendModalLabel">Legendas - Status das Ordens</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                aria-label="Fechar"></button>
                        </div>
                        <div class="modal-body">
                            <div class="legend-grid">
                                <div class="legend-item">
                                    <span class="legend-badge bg-info"></span>
                                    <span class="legend-text">Aguardando Aprovação</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-badge bg-success"></span>
                                    <span class="legend-text">Aprovado</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-badge bg-success" style="opacity: 0.7;"></span>
                                    <span class="legend-text">Aprovado - Manutenção Programada</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-badge bg-danger"></span>
                                    <span class="legend-text">Reprovado</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-badge bg-danger"
                                        style="background-color: #dc3545 !important;"></span>
                                    <span class="legend-text">Urgente</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-badge bg-warning"></span>
                                    <span class="legend-text">Média Prioridade</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-badge bg-danger"></span>
                                    <span class="legend-text">Alta Prioridade</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-badge bg-primary"></span>
                                    <span class="legend-text">Programada</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-badge bg-secondary"></span>
                                    <span class="legend-text">Concluída</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- A seção de Serviços Agendados foi removida conforme solicitado -->
    </div>

    <!-- Modal de Manutenção -->
    <div class="modal fade" id="manutencaoModal" tabindex="-1" aria-labelledby="manutencaoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content text-white bg-dark">
                <div class="modal-header border-secondary">
                    <h5 class="modal-title" id="manutencaoModalLabel"><i class="fas fa-wrench me-2"></i>Detalhes da Manutenção</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="manutencaoForm">
                        <div class="mb-3">
                            <label for="descricaoServico" class="form-label">Descrição do Serviço</label>
                            <textarea class="form-control bg-secondary text-white border-dark" id="descricaoServico" rows="3" placeholder="Descreva o serviço realizado"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="pecasUtilizadas" class="form-label">Peças Utilizadas</label>
                            <textarea class="form-control bg-secondary text-white border-dark" id="pecasUtilizadas" rows="3" placeholder="Liste as peças utilizadas"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="observacoesManutencao" class="form-label">Observações</label>
                            <textarea class="form-control bg-secondary text-white border-dark" id="observacoesManutencao" rows="2" placeholder="Observações adicionais"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="fotosManutencao" class="form-label">Fotos da Manutenção</label>
                            <input class="form-control bg-secondary text-white border-dark" type="file" id="fotosManutencao" multiple>
                        </div>
                    </form>
                </div>
                <div class="modal-footer border-secondary justify-content-between">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" id="salvarManutencao">Salvar <i class="fas fa-save"></i></button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Custos -->
    <div class="modal fade" id="custosModal" tabindex="-1" aria-labelledby="custosModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content text-white bg-dark">
                <div class="modal-header border-secondary">
                    <h5 class="modal-title" id="custosModalLabel"><i class="fas fa-file-invoice-dollar me-2"></i>Custos da Manutenção</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="custosForm">
                        <div class="mb-3 row">
                            <label for="custoPecas" class="col-sm-4 col-form-label">Peças (R$)</label>
                            <div class="col-sm-8">
                                <input type="number" step="0.01" class="form-control bg-secondary text-white border-dark" id="custoPecas" placeholder="0.00">
                            </div>
                        </div>
                        <div class="mb-3 row">
                            <label for="custoMaoObra" class="col-sm-4 col-form-label">Mão de Obra (R$)</label>
                            <div class="col-sm-8">
                                <input type="number" step="0.01" class="form-control bg-secondary text-white border-dark" id="custoMaoObra" placeholder="0.00">
                            </div>
                        </div>
                        <div class="mb-3 row">
                            <label for="custoDeslocamento" class="col-sm-4 col-form-label">Deslocamento (R$)</label>
                            <div class="col-sm-8">
                                <input type="number" step="0.01" class="form-control bg-secondary text-white border-dark" id="custoDeslocamento" placeholder="0.00">
                            </div>
                        </div>
                        <hr class="border-secondary">
                        <div class="mb-3 row fw-bold">
                            <label class="col-sm-4 col-form-label">Custo Total (R$)</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control bg-secondary text-white border-dark" id="custoTotal" placeholder="0.00" readonly>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer border-secondary justify-content-between">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" id="salvarCustos">Salvar <i class="fas fa-save"></i></button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Interação -->
    <div class="modal fade" id="interacaoModal" tabindex="-1" aria-labelledby="interacaoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content text-white bg-dark">
                <div class="modal-header border-secondary">
                    <h5 class="modal-title" id="interacaoModalLabel"><i class="fas fa-comments me-2"></i>Interação / Chat</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="chatArea" style="height: 300px; overflow-y: auto; border: 1px solid #444; border-radius: 5px; padding: 10px; margin-bottom: 15px; background-color: #222;">
                        <!-- Mensagens do chat serão carregadas aqui -->
                        <div class="chat-message mb-2 text-start">
                            <small class="text-warning">Gerente (10:30):</small>
                            <p class="m-0 bg-secondary p-2 rounded d-inline-block">Precisa de autorização para compra?</p>
                        </div>
                        <div class="chat-message mb-2 text-end">
                            <small class="text-info">(10:32) Você:</small>
                            <p class="m-0 bg-primary p-2 rounded d-inline-block">Sim, peças não disponíveis no estoque.</p>
                        </div>
                    </div>
                    <div class="input-group">
                        <input type="text" class="form-control bg-secondary text-white border-dark" id="chatMessageInput" placeholder="Digite sua mensagem...">
                        <button class="btn btn-outline-primary" type="button" id="sendChatMessage">Enviar <i class="fas fa-paper-plane"></i></button>
                    </div>
                </div>
                <div class="modal-footer border-secondary">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Cronograma -->
    <div class="modal fade" id="cronogramaModal" tabindex="-1" aria-labelledby="cronogramaModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content text-white bg-dark">
                <div class="modal-header border-secondary">
                    <h5 class="modal-title" id="cronogramaModalLabel"><i class="fas fa-calendar-alt me-2"></i>Cronograma e Status</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="cronogramaForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="dataInicio" class="form-label">Data de Início</label>
                                <input type="date" class="form-control bg-secondary text-white border-dark" id="dataInicio">
                            </div>
                            <div class="col-md-6">
                                <label for="horaInicio" class="form-label">Hora de Início</label>
                                <input type="time" class="form-control bg-secondary text-white border-dark" id="horaInicio">
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="dataFim" class="form-label">Data de Fim</label>
                                <input type="date" class="form-control bg-secondary text-white border-dark" id="dataFim">
                            </div>
                            <div class="col-md-6">
                                <label for="horaFim" class="form-label">Hora de Fim</label>
                                <input type="time" class="form-control bg-secondary text-white border-dark" id="horaFim">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="statusServico" class="form-label">Status do Serviço</label>
                            <select class="form-select bg-secondary text-white border-dark" id="statusServico">
                                <option selected>Selecione...</option>
                                <option value="agendado">Agendado</option>
                                <option value="em_andamento">Em Andamento</option>
                                <option value="concluido">Concluído</option>
                                <option value="pendente">Pendente</option>
                                <option value="cancelado">Cancelado</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer border-secondary justify-content-between">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" id="salvarCronograma">Salvar <i class="fas fa-save"></i></button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Função para mostrar legendas
        document.addEventListener('DOMContentLoaded', function () {
            // Garantir que o conteúdo de serviços e ordens em destaque comece expandido
            const servicesContent = document.getElementById('servicesContent');
            const featuredSection = document.getElementById('featuredOrdersSection');

            servicesContent.classList.remove('collapsed');
            servicesContent.style.display = 'grid';

            if (featuredSection) {
                featuredSection.style.display = 'block';
            }

            console.log("Inicialização: servicesContent e featuredSection configurados como expandidos");

            // Configurar os cards de manutenção
            setupMaintenanceCards();

            // Configurar os modais
            setupModals();

            // Botão de legendas (deixado por compatibilidade, embora não seja mais usado)
            const showLegendBtn = document.getElementById('showLegendBtn');
            if (showLegendBtn) {
                showLegendBtn.addEventListener('click', function () {
                    const legendModal = new bootstrap.Modal(document.getElementById('legendModal'));
                    legendModal.show();
                });
            }

            // Botões da Agenda Google
            document.getElementById('connectGoogleBtn').addEventListener('click', function () {
                // Simulação da conexão com Google Calendar
                // Em produção, aqui seria redirecionado para autorização OAuth do Google
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Conectando...';

                setTimeout(() => {
                    // Simula conexão bem-sucedida após 2 segundos
                    document.getElementById('agenda-connection-status').style.display = 'none';
                    document.getElementById('agenda-events-list').style.display = 'block';
                    document.getElementById('agenda-user-name').textContent = 'João Silva';

                    // Carrega eventos de exemplo
                    loadSampleEvents();
                }, 2000);
            });

            document.getElementById('syncGoogleBtn').addEventListener('click', function () {
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-sync-alt"></i> Sincronizar';
                    showAgendaNotification('Agenda sincronizada com sucesso!', 'success');
                }, 1500);
            });

            document.getElementById('addAgendaEvent').addEventListener('click', function () {
                showAgendaModal();
            });

            // Função para expandir/recolher serviços agendados
            document.getElementById('toggleServicesBtn').addEventListener('click', function (e) {
                e.preventDefault();
                e.stopPropagation();

                const servicesContent = document.getElementById('servicesContent');
                const featuredSection = document.getElementById('featuredOrdersSection');
                const icon = this.querySelector('i');

                console.log("Toggle button clicked: Current state:", servicesContent.classList.contains('collapsed'));

                if (servicesContent.classList.contains('collapsed')) {
                    // Expandir tudo
                    servicesContent.classList.remove('collapsed');
                    servicesContent.style.display = 'grid';
                    if (featuredSection) {
                        featuredSection.style.display = 'block';
                    }
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                    console.log("Expanded services and featured section");
                } else {
                    // Recolher tudo
                    servicesContent.classList.add('collapsed');
                    servicesContent.style.display = 'none';
                    if (featuredSection) {
                        featuredSection.style.display = 'none';
                    }
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                    console.log("Collapsed services and featured section");
                }
            }, true);

            // Função para filtrar serviços agendados
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function () {
                    console.log('Botão de filtro clicado:', this.getAttribute('data-filter'));

                    // Remove classe ativa de todos os botões
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));

                    // Adiciona classe ativa ao botão clicado
                    this.classList.add('active');

                    // Obtém o filtro selecionado
                    const filter = this.getAttribute('data-filter');

                    // Mapeamento de filtros para status dos itens de serviço
                    const filterMap = {
                        'waiting': ['waiting'],
                        'approved': ['approved', 'approved-scheduled'],
                        'scheduled': ['scheduled'],
                        'urgent': ['urgent'],
                        'completed': ['completed'],
                        'all': ['waiting', 'approved', 'approved-scheduled', 'scheduled', 'urgent', 'completed']
                    };

                    // Filtra os itens de serviço
                    const items = document.querySelectorAll('.service-order-item');
                    console.log('Total de itens:', items.length);

                    items.forEach(item => {
                        const status = item.getAttribute('data-status');
                        console.log('Item status:', status);

                        if (filter === 'all' || filterMap[filter].includes(status)) {
                            item.style.display = 'flex';
                            console.log('Mostrando item:', status);
                        } else {
                            item.style.display = 'none';
                            console.log('Ocultando item:', status);
                        }
                    });
                });
            });

            // Função para processar clique em um item de ordem de serviço (comum para todos os tipos de itens)
            function handleOrderItemClick(title, orderId, date, status, location, priority) {
                // Log para depuração
                console.log(`Ordem clicada: ${orderId}, status: ${status}, data: ${date}`);

                // Atualizar título e detalhes no verso do card
                if (document.querySelector('.flip-card-back h3')) {
                    document.querySelector('.flip-card-back h3').textContent = title;
                }

                if (document.querySelector('.flip-card-back #order-location')) {
                    document.querySelector('.flip-card-back #order-location').textContent = location;
                }

                if (document.querySelector('.flip-card-back #order-priority')) {
                    document.querySelector('.flip-card-back #order-priority').textContent = priority;
                }

                // Executar a animação de flip
                document.getElementById('flip-card').classList.add('flipped');

                // Configurar os cards após o flip
                setTimeout(() => {
                    setupMaintenanceCards();
                }, 500); // Pequeno delay para garantir que o flip foi concluído
            }

            // Fazer itens de serviço regulares clicáveis
            document.querySelectorAll('.service-order-item').forEach(item => {
                item.addEventListener('click', function () {
                    try {
                        // Obter dados da ordem
                        const orderId = this.querySelector('.order-title').textContent.match(/\#(\d+)/)[1];
                        const title = this.querySelector('.order-title').textContent;
                        const date = this.getAttribute('data-date');
                        const status = this.getAttribute('data-status');
                        const location = this.querySelector('.order-location').textContent;
                        const priority = this.querySelector('.order-priority .badge').textContent;

                        console.log('Clicou em ordem de serviço regular:', title, orderId, date, status);

                        // Virar o cartão do calendário
                        document.getElementById('flip-card').classList.add('flipped');

                        // Configurar os cards após o flip
                        setTimeout(() => {
                            setupMaintenanceCards();
                        }, 500); // Pequeno delay para garantir que o flip foi concluído

                        // Mapeamento de status para tipo de evento
                        let eventType;
                        switch (status) {
                            case 'urgent':
                                eventType = 'urgent';
                                break;
                            case 'waiting':
                                eventType = 'medium';
                                break;
                            case 'approved':
                            case 'approved-scheduled':
                                eventType = 'approved';
                                break;
                            case 'scheduled':
                                eventType = 'scheduled';
                                break;
                            case 'completed':
                                eventType = 'completed';
                                break;
                            default:
                                eventType = 'medium';
                        }

                        // Extrair o dia do formato DD-MM-AAAA
                        const day = parseInt(date.split('-')[0]);

                        // Mostrar os detalhes da ordem no cartão
                        console.log('Chamando showOrderDetails com:', eventType, day);
                        showOrderDetails(eventType, day);

                        // Atualizar detalhes específicos desta ordem
                        handleOrderItemClick(title, orderId, date, status, location, priority);
                    } catch (error) {
                        console.error('Erro ao processar clique na ordem:', error);
                    }
                });
            });

            // Fazer itens de destaque clicáveis
            document.querySelectorAll('.featured-order-item').forEach(item => {
                item.addEventListener('click', function () {
                    try {
                        // Obter dados da ordem
                        const orderId = this.getAttribute('data-order-id');
                        const title = this.querySelector('h6').textContent;
                        const date = this.getAttribute('data-date');
                        const status = this.getAttribute('data-status');
                        const location = this.querySelector('.featured-order-details span:nth-child(2)').textContent.trim();
                        const priority = status === 'urgent' ? 'Urgente' : 'Programada';

                        console.log('Clicou em ordem de serviço em destaque:', title, orderId, date, status);

                        // Virar o cartão do calendário
                        document.getElementById('flip-card').classList.add('flipped');

                        // Configurar os cards após o flip
                        setTimeout(() => {
                            setupMaintenanceCards();
                        }, 500); // Pequeno delay para garantir que o flip foi concluído

                        // Mapeamento de status para tipo de evento
                        let eventType;
                        switch (status) {
                            case 'urgent':
                                eventType = 'urgent';
                                break;
                            case 'waiting':
                                eventType = 'medium';
                                break;
                            case 'approved':
                            case 'approved-scheduled':
                                eventType = 'approved';
                                break;
                            case 'scheduled':
                                eventType = 'scheduled';
                                break;
                            case 'completed':
                                eventType = 'completed';
                                break;
                            default:
                                eventType = 'medium';
                        }

                        // Extrair o dia do formato DD-MM-AAAA
                        const day = parseInt(date.split('-')[0]);

                        // Mostrar os detalhes da ordem no cartão
                        console.log('Chamando showOrderDetails com:', eventType, day);
                        showOrderDetails(eventType, day);

                        // Atualizar detalhes específicos desta ordem
                        handleOrderItemClick(title, orderId, date, status, location, priority);
                    } catch (error) {
                        console.error('Erro ao processar clique na ordem em destaque:', error);
                    }
                });
            });

            // Fazer itens do painel lateral clicáveis também
            document.querySelectorAll('.side-order-item').forEach(item => {
                item.addEventListener('click', function () {
                    // Obtém a data e id da ordem do item
                    const date = this.getAttribute('data-date');
                    const orderId = this.getAttribute('data-order-id');
                    console.log('Item de painel lateral clicado, data:', date, 'ordem:', orderId);

                    // Simula o flip do card e carrega os detalhes da ordem
                    document.getElementById('flip-card').classList.add('flipped');

                    // Configurar os cards após o flip
                    setTimeout(() => {
                        setupMaintenanceCards();
                    }, 500); // Pequeno delay para garantir que o flip foi concluído

                    // Aqui seria implementada a lógica para carregar os detalhes
                    // específicos desta ordem no verso do card
                    // Por enquanto usamos os detalhes de exemplo que já estão lá
                });
            });
        });

        // Função para configurar os cards de manutenção
        function setupMaintenanceCards() {
            // Adicionar eventos de clique aos cards
            const manutencaoCard = document.getElementById('manutencao-card');
            const custosCard = document.getElementById('custos-card');
            const interacaoCard = document.getElementById('interacao-card');
            const cronogramaCard = document.getElementById('cronograma-card');

            // Botões de edição
            const btnEditarManutencao = document.getElementById('btn-editar-manutencao');

            // Função para abrir o modal de manutenção
            function abrirModalManutencao() {
                const modal = new bootstrap.Modal(document.getElementById('manutencaoModal'));
                modal.show();
            }

            // Função para abrir o modal de custos
            function abrirModalCustos() {
                const modal = new bootstrap.Modal(document.getElementById('custosModal'));
                modal.show();
            }

            // Função para abrir o modal de interação
            function abrirModalInteracao() {
                const modal = new bootstrap.Modal(document.getElementById('interacaoModal'));
                modal.show();
            }

            // Função para abrir o modal de cronograma
            function abrirModalCronograma() {
                const modal = new bootstrap.Modal(document.getElementById('cronogramaModal'));
                modal.show();
            }

            // Configurar eventos para o card de manutenção
            if (manutencaoCard) {
                manutencaoCard.addEventListener('click', abrirModalManutencao);
                console.log('Evento de clique adicionado ao card de manutenção');
            } else {
                console.log('Card de manutenção não encontrado');
            }

            // Configurar evento para o botão de editar manutenção
            if (btnEditarManutencao) {
                btnEditarManutencao.addEventListener('click', function(e) {
                    e.stopPropagation(); // Evita que o clique propague para o card
                    abrirModalManutencao();
                });
                console.log('Evento de clique adicionado ao botão de editar manutenção');
            } else {
                console.log('Botão de editar manutenção não encontrado');
            }

            // Configurar eventos para os outros cards
            if (custosCard) {
                custosCard.addEventListener('click', abrirModalCustos);
            }

            if (interacaoCard) {
                interacaoCard.addEventListener('click', abrirModalInteracao);
            }

            if (cronogramaCard) {
                cronogramaCard.addEventListener('click', abrirModalCronograma);
            }

            // Adicionar logs para depuração
            console.log('Cards configurados:', {
                manutencaoCard: !!manutencaoCard,
                custosCard: !!custosCard,
                interacaoCard: !!interacaoCard,
                cronogramaCard: !!cronogramaCard,
                btnEditarManutencao: !!btnEditarManutencao
            });
        }

        // Função para configurar os modais
        function setupModals() {
            // Configurar cálculo automático do custo total
            const custoPecas = document.getElementById('custoPecas');
            const custoMaoObra = document.getElementById('custoMaoObra');
            const custoDeslocamento = document.getElementById('custoDeslocamento');
            const custoTotal = document.getElementById('custoTotal');

            const calcularTotal = () => {
                if (custoPecas && custoMaoObra && custoDeslocamento && custoTotal) {
                    const pecas = parseFloat(custoPecas.value) || 0;
                    const maoObra = parseFloat(custoMaoObra.value) || 0;
                    const deslocamento = parseFloat(custoDeslocamento.value) || 0;

                    const total = pecas + maoObra + deslocamento;
                    custoTotal.value = total.toFixed(2);
                }
            };

            if (custoPecas) custoPecas.addEventListener('input', calcularTotal);
            if (custoMaoObra) custoMaoObra.addEventListener('input', calcularTotal);
            if (custoDeslocamento) custoDeslocamento.addEventListener('input', calcularTotal);

            // Configurar envio de mensagens no chat
            const chatMessageInput = document.getElementById('chatMessageInput');
            const sendChatMessageBtn = document.getElementById('sendChatMessage');

            if (sendChatMessageBtn && chatMessageInput) {
                sendChatMessageBtn.addEventListener('click', function() {
                    enviarMensagemChat();
                });

                chatMessageInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        enviarMensagemChat();
                    }
                });
            }

            // Preencher datas do cronograma com valores padrão
            const dataInicio = document.getElementById('dataInicio');
            const horaInicio = document.getElementById('horaInicio');
            const dataFim = document.getElementById('dataFim');
            const horaFim = document.getElementById('horaFim');

            if (dataInicio && horaInicio && dataFim && horaFim) {
                const hoje = new Date();
                const dataFormatada = hoje.toISOString().split('T')[0];
                const horaFormatada = '14:00';

                dataInicio.value = dataFormatada;
                horaInicio.value = horaFormatada;

                const amanha = new Date();
                amanha.setDate(hoje.getDate() + 1);
                dataFim.value = amanha.toISOString().split('T')[0];
                horaFim.value = '16:00';
            }

            // Configurar botões de salvar
            const salvarManutencao = document.getElementById('salvarManutencao');
            if (salvarManutencao) {
                salvarManutencao.addEventListener('click', function() {
                    // Obter valores do formulário
                    const descricao = document.getElementById('descricaoServico').value;
                    const pecas = document.getElementById('pecasUtilizadas').value;
                    const observacoes = document.getElementById('observacoesManutencao').value;

                    // Atualizar o preview no card
                    const manutencaoPreview = document.getElementById('manutencao-preview');
                    if (manutencaoPreview) {
                        manutencaoPreview.innerHTML = descricao.substring(0, 100) + (descricao.length > 100 ? '...' : '');
                    }

                    // Fechar o modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('manutencaoModal'));
                    modal.hide();

                    // Mostrar notificação
                    alert('Informações de manutenção salvas com sucesso!');
                });
            }

            const salvarCustos = document.getElementById('salvarCustos');
            if (salvarCustos) {
                salvarCustos.addEventListener('click', function() {
                    // Obter valores do formulário
                    const pecas = document.getElementById('custoPecas').value;
                    const maoObra = document.getElementById('custoMaoObra').value;
                    const deslocamento = document.getElementById('custoDeslocamento').value;
                    const total = document.getElementById('custoTotal').value;

                    // Atualizar o preview no card
                    const custosPreview = document.getElementById('custos-preview');
                    if (custosPreview) {
                        custosPreview.innerHTML = `
                            <div class="d-flex justify-content-between">
                                <span>Peças:</span>
                                <span>R$ ${parseFloat(pecas).toFixed(2)}</span>
                            </div>
                        `;
                    }

                    // Fechar o modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('custosModal'));
                    modal.hide();

                    // Mostrar notificação
                    alert('Informações de custos salvas com sucesso!');
                });
            }

            const salvarCronograma = document.getElementById('salvarCronograma');
            if (salvarCronograma) {
                salvarCronograma.addEventListener('click', function() {
                    // Obter valores do formulário
                    const dataInicio = document.getElementById('dataInicio').value;
                    const horaInicio = document.getElementById('horaInicio').value;
                    const dataFim = document.getElementById('dataFim').value;
                    const horaFim = document.getElementById('horaFim').value;
                    const status = document.getElementById('statusServico').value;

                    // Formatar as datas
                    const dataInicioFormatada = formatarData(dataInicio);
                    const dataFimFormatada = formatarData(dataFim);

                    // Atualizar o preview no card
                    const scheduleStart = document.getElementById('schedule-start');
                    const scheduleEnd = document.getElementById('schedule-end');
                    if (scheduleStart && scheduleEnd) {
                        scheduleStart.textContent = `${dataInicioFormatada} ${horaInicio}`;
                        scheduleEnd.textContent = `${dataFimFormatada} ${horaFim}`;
                    }

                    // Fechar o modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('cronogramaModal'));
                    modal.hide();

                    // Mostrar notificação
                    alert('Informações de cronograma salvas com sucesso!');
                });
            }
        }

        // Função para enviar mensagem no chat
        function enviarMensagemChat() {
            const chatMessageInput = document.getElementById('chatMessageInput');
            const chatArea = document.getElementById('chatArea');

            if (chatMessageInput && chatArea && chatMessageInput.value.trim() !== '') {
                const mensagem = chatMessageInput.value.trim();
                const agora = new Date();
                const hora = agora.getHours().toString().padStart(2, '0');
                const minutos = agora.getMinutes().toString().padStart(2, '0');

                const novaMensagem = document.createElement('div');
                novaMensagem.className = 'chat-message mb-2 text-end';
                novaMensagem.innerHTML = `
                    <small class="text-info">(${hora}:${minutos}) Você:</small>
                    <p class="m-0 bg-primary p-2 rounded d-inline-block">${mensagem}</p>
                `;

                chatArea.appendChild(novaMensagem);
                chatArea.scrollTop = chatArea.scrollHeight;
                chatMessageInput.value = '';

                // Simular resposta após 1-2 segundos
                setTimeout(() => {
                    simularRespostaChat();
                }, Math.random() * 1000 + 1000);
            }
        }

        // Função para simular resposta no chat
        function simularRespostaChat() {
            const chatArea = document.getElementById('chatArea');

            if (chatArea) {
                const respostas = [
                    { remetente: 'Gerente', mensagem: 'Entendido. Vou verificar a disponibilidade.' },
                    { remetente: 'Financeiro', mensagem: 'Aprovado. Pode prosseguir com a compra.' },
                    { remetente: 'Gerente', mensagem: 'Precisa de mais alguma informação?' },
                    { remetente: 'Financeiro', mensagem: 'Por favor, envie a nota fiscal quando concluir.' }
                ];

                const resposta = respostas[Math.floor(Math.random() * respostas.length)];
                const agora = new Date();
                const hora = agora.getHours().toString().padStart(2, '0');
                const minutos = agora.getMinutes().toString().padStart(2, '0');

                const novaMensagem = document.createElement('div');
                novaMensagem.className = 'chat-message mb-2 text-start';
                novaMensagem.innerHTML = `
                    <small class="text-warning">${resposta.remetente} (${hora}:${minutos}):</small>
                    <p class="m-0 bg-secondary p-2 rounded d-inline-block">${resposta.mensagem}</p>
                `;

                chatArea.appendChild(novaMensagem);
                chatArea.scrollTop = chatArea.scrollHeight;
            }
        }

        // Função para formatar data (YYYY-MM-DD para DD/MM/YYYY)
        function formatarData(dataString) {
            if (!dataString) return '';
            const partes = dataString.split('-');
            if (partes.length !== 3) return dataString;
            return `${partes[2]}/${partes[1]}/${partes[0]}`;
        }

        // Função para imprimir todos os detalhes da ordem atual
        function printOrderDetails() {
            // Cria um estilo temporário para impressão que mostra todos os detalhes da ordem
            const style = document.createElement('style');
            style.id = 'print-style-temp';
            style.innerHTML = `
            @media print {
                body * {
                    visibility: hidden;
                }
                .shell-header, .calendar-container, .events-wrapper, .back-button, .modal {
                    display: none !important;
                }
                .flip-card {
                    transform: none !important;
                    -webkit-transform: none !important;
                }
                .flip-card-front {
                    transform: none !important;
                    -webkit-transform: none !important;
                }
                .flip-card-back {
                    position: static !important;
                    transform: none !important;
                    -webkit-transform: none !important;
                }
                .order-detail-panel {
                    visibility: visible;
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                }
                .order-detail-panel * {
                    visibility: visible;
                }
                #order-header h3, #order-header p, #order-header .badge,
                .order-basic-info h4, .order-basic-info p, .order-basic-info .order-info-item span,
                .service-box h6, .service-box div, .service-box p {
                    visibility: visible;
                    color: #000 !important;
                }
                .order-basic-info, .service-box {
                    border: 1px solid #ccc;
                    padding: 10px;
                    margin: 10px 0;
                }
                .text-warning {
                    font-weight: bold !important;
                }
                .row {
                    display: flex !important;
                    flex-wrap: wrap !important;
                }
                .col-md-3 {
                    width: 25% !important;
                    padding: 10px !important;
                }
                .service-box-icon, .order-actions {
                    display: none !important;
                }
            }
        `;
            document.head.appendChild(style);

            // Imprime a página
            window.print();

            // Remove o estilo temporário após a impressão
            setTimeout(() => {
                document.head.removeChild(style);
            }, 1000);
        }

        // Função para aprovar uma ordem
        function approveOrder() {
            const hasParts = true; // Verificar se a ordem tem peças (simulado)
            const statusElement = document.getElementById('order-status');
            const missingDocuments = document.querySelectorAll('.document-item.missing');

            if (statusElement) {
                // Verifica se existem documentos pendentes obrigatórios
                if (missingDocuments.length > 0) {
                    showNotification('Não é possível aprovar: documentos pendentes', 'danger');

                    // Destaca os documentos pendentes com animação
                    missingDocuments.forEach(doc => {
                        doc.style.animation = 'pulse 0.5s 3';
                        setTimeout(() => {
                            doc.style.animation = '';
                        }, 1500);
                    });

                    // Adiciona mensagem na seção de interação
                    addInteractionMessage('Sistema', 'Aprovação bloqueada: documentos pendentes.');
                    return;
                }

                if (hasParts) {
                    statusElement.innerHTML = '<span class="badge bg-success">Aprovado - Manutenção Programada</span>';
                    showNotification('Ordem aprovada para manutenção programada', 'success');
                } else {
                    statusElement.innerHTML = '<span class="badge bg-success">Aprovado</span>';
                    showNotification('Ordem aprovada com sucesso!', 'success');
                }

                // Adiciona mensagem na seção de interação
                addInteractionMessage('Sistema', 'Ordem aprovada pelo usuário.');

                // Neste ponto, em uma implementação real, enviaríamos a aprovação para o servidor
                // fetch('/api/orders/approve', {
                //     method: 'POST',
                //     headers: { 'Content-Type': 'application/json' },
                //     body: JSON.stringify({ orderId: '1234', status: 'approved' })
                // });
            }
        }

        // Função para reprovar uma ordem
        function rejectOrder() {
            const statusElement = document.getElementById('order-status');

            if (statusElement) {
                statusElement.innerHTML = '<span class="badge bg-danger">Reprovado</span>';

                // Feedback visual de reprovação
                showNotification('Ordem reprovada.', 'danger');

                // Adiciona mensagem na seção de interação
                addInteractionMessage('Sistema', 'Ordem reprovada pelo usuário.');

                // Neste ponto, em uma implementação real, enviaríamos a reprovação para o servidor
                // fetch('/api/orders/reject', {
                //     method: 'POST',
                //     headers: { 'Content-Type': 'application/json' },
                //     body: JSON.stringify({ orderId: '1234', status: 'rejected' })
                // });
            }
        }

        // Função para adicionar mensagem na seção de interação
        function addInteractionMessage(sender, text) {
            const chatMessages = document.querySelector('.chat-messages');
            if (chatMessages) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'chat-message';

                const senderSpan = document.createElement('small');
                senderSpan.className = 'message-sender';
                senderSpan.textContent = sender + ':';

                const messageP = document.createElement('p');
                messageP.className = 'message-text';
                messageP.textContent = text;

                messageDiv.appendChild(senderSpan);
                messageDiv.appendChild(messageP);

                chatMessages.appendChild(messageDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight; // Rola para a mensagem mais recente
            }
        }

        // Função para filtrar serviços agendados
        function filterServices(filter) {
            console.log('Função filterServices chamada com filtro:', filter);

            // Remove classe ativa de todos os botões
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Adiciona classe ativa ao botão clicado
            document.querySelector(`.filter-btn[data-filter="${filter}"]`).classList.add('active');

            // Mapeamento de filtros para status
            const filterMap = {
                'waiting': ['waiting'],
                'approved': ['approved', 'approved-scheduled'],
                'scheduled': ['scheduled'],
                'urgent': ['urgent'],
                'completed': ['completed'],
                'featured': ['featured'],
                'all': ['waiting', 'approved', 'approved-scheduled', 'scheduled', 'urgent', 'completed', 'featured']
            };

            // Filtra os itens de serviço regular
            const items = document.querySelectorAll('.service-order-item');
            console.log('Total de itens regulares:', items.length);

            items.forEach(item => {
                const status = item.getAttribute('data-status');
                console.log('Item status:', status);

                if (filter === 'all' || filterMap[filter].includes(status)) {
                    item.style.display = 'flex';
                    console.log('Mostrando item regular:', status);
                } else {
                    item.style.display = 'none';
                    console.log('Ocultando item regular:', status);
                }
            });

            // Filtra os itens de destaque na seção superior
            const featuredItems = document.querySelectorAll('.featured-order-item');
            console.log('Total de itens em destaque:', featuredItems.length);

            // Exibe/oculta toda a seção de destaques dependendo do filtro
            const featuredSection = document.getElementById('featuredOrdersSection');
            if (featuredSection) {
                if (filter === 'all' || filter === 'featured') {
                    featuredSection.style.display = 'block';
                    console.log('Exibindo seção de destaques');
                } else {
                    featuredSection.style.display = 'none';
                    console.log('Ocultando seção de destaques');
                }
            }
        }

        function showAgendaNotification(message, type) {
            const notificationDiv = document.createElement('div');
            notificationDiv.className = `agenda-notification ${type}`;
            notificationDiv.innerHTML = `
            <div class="notification-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

            document.body.appendChild(notificationDiv);

            // Anima a entrada da notificação
            setTimeout(() => {
                notificationDiv.classList.add('show');
            }, 10);

            // Remove a notificação após 3 segundos
            setTimeout(() => {
                notificationDiv.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(notificationDiv);
                }, 300);
            }, 3000);
        }

        // Função para carregar eventos de exemplo na agenda
        function loadSampleEvents() {
            const eventsList = document.getElementById('agenda-events-list');
            eventsList.innerHTML = ''; // Limpa qualquer conteúdo existente

            const sampleEvents = [
                {
                    title: 'Reunião com Fornecedores',
                    date: '30/03/2025',
                    time: '10:00 - 11:30',
                    location: 'Sala de Conferência'
                },
                {
                    title: 'Visita Técnica ao Posto Shell Centro',
                    date: '02/04/2025',
                    time: '14:00 - 16:00',
                    location: 'Posto Shell Centro'
                },
                {
                    title: 'Treinamento Equipe Técnica',
                    date: '05/04/2025',
                    time: '09:00 - 12:00',
                    location: 'Auditório Principal'
                },
                {
                    title: 'Almoço com Gerentes Regionais',
                    date: '07/04/2025',
                    time: '12:30 - 14:00',
                    location: 'Restaurante Tradição'
                },
                {
                    title: 'Manutenção Preventiva - Posto Ipiranga',
                    date: '12/04/2025',
                    time: '08:00 - 10:00',
                    location: 'Posto Shell Ipiranga'
                },
                {
                    title: 'Inspeção de Segurança',
                    date: '15/04/2025',
                    time: '13:00 - 15:00',
                    location: 'Posto Shell Tiradentes'
                }
            ];

            sampleEvents.forEach(event => {
                const eventItem = document.createElement('div');
                eventItem.className = 'agenda-event-item';
                eventItem.innerHTML = `
                <div class="agenda-event-title">${event.title}</div>
                <div class="agenda-event-details">
                    <div class="agenda-event-time">
                        <i class="far fa-calendar-alt"></i> ${event.date}
                    </div>
                    <div class="agenda-event-time">
                        <i class="far fa-clock"></i> ${event.time}
                    </div>
                </div>
                <div class="agenda-event-details">
                    <div class="agenda-event-location">
                        <i class="fas fa-map-marker-alt"></i> ${event.location}
                    </div>
                </div>
                <div class="agenda-event-actions">
                    <button class="agenda-event-btn"><i class="fas fa-edit"></i> Editar</button>
                    <button class="agenda-event-btn"><i class="fas fa-trash-alt"></i> Remover</button>
                </div>
            `;

                eventsList.appendChild(eventItem);
            });
        }

        // Função para mostrar modal de adicionar evento
        function showAgendaModal() {
            // Se o modal já existe, apenas o mostra
            let agendaModal = document.getElementById('addEventModal');

            if (!agendaModal) {
                // Cria o modal na primeira vez
                agendaModal = document.createElement('div');
                agendaModal.className = 'modal fade';
                agendaModal.id = 'addEventModal';
                agendaModal.setAttribute('tabindex', '-1');
                agendaModal.setAttribute('aria-labelledby', 'addEventModalLabel');
                agendaModal.setAttribute('aria-hidden', 'true');

                agendaModal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content agenda-modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="addEventModalLabel">Adicionar Evento na Agenda</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                        </div>
                        <div class="modal-body">
                            <form id="addEventForm">
                                <div class="agenda-form-group">
                                    <label for="event-title">Título do Evento</label>
                                    <input type="text" id="event-title" class="agenda-form-control" required>
                                </div>
                                <div class="agenda-form-group">
                                    <label for="event-date">Data</label>
                                    <input type="date" id="event-date" class="agenda-form-control" required>
                                </div>
                                <div class="agenda-form-group row">
                                    <div class="col-6">
                                        <label for="event-start-time">Hora Início</label>
                                        <input type="time" id="event-start-time" class="agenda-form-control" required>
                                    </div>
                                    <div class="col-6">
                                        <label for="event-end-time">Hora Fim</label>
                                        <input type="time" id="event-end-time" class="agenda-form-control" required>
                                    </div>
                                </div>
                                <div class="agenda-form-group">
                                    <label for="event-location">Local</label>
                                    <input type="text" id="event-location" class="agenda-form-control">
                                </div>
                                <div class="agenda-form-group">
                                    <label for="event-description">Descrição</label>
                                    <textarea id="event-description" class="agenda-form-control" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="shell-btn" data-bs-dismiss="modal">Cancelar</button>
                            <button type="button" class="shell-btn" id="saveEventBtn">Salvar Evento</button>
                        </div>
                    </div>
                </div>
            `;

                document.body.appendChild(agendaModal);

                // Adiciona o listener para o botão de salvar
                document.getElementById('saveEventBtn').addEventListener('click', function () {
                    // Aqui seria implementada a lógica para salvar o evento
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addEventModal'));
                    modal.hide();

                    showAgendaNotification('Evento adicionado com sucesso!', 'success');

                    // Adiciona o evento à lista (simulação)
                    const eventsList = document.getElementById('agenda-events-list');
                    if (eventsList.style.display === 'none') {
                        document.getElementById('agenda-connection-status').style.display = 'none';
                        eventsList.style.display = 'block';
                    }

                    const eventItem = document.createElement('div');
                    eventItem.className = 'agenda-event-item';
                    eventItem.innerHTML = `
                    <div class="agenda-event-title">${document.getElementById('event-title').value}</div>
                    <div class="agenda-event-details">
                        <div class="agenda-event-time">
                            <i class="far fa-calendar-alt"></i> ${formatDate(document.getElementById('event-date').value)}
                        </div>
                        <div class="agenda-event-time">
                            <i class="far fa-clock"></i> ${document.getElementById('event-start-time').value} - ${document.getElementById('event-end-time').value}
                        </div>
                    </div>
                    <div class="agenda-event-details">
                        <div class="agenda-event-location">
                            <i class="fas fa-map-marker-alt"></i> ${document.getElementById('event-location').value}
                        </div>
                    </div>
                    <div class="agenda-event-actions">
                        <button class="agenda-event-btn"><i class="fas fa-edit"></i> Editar</button>
                        <button class="agenda-event-btn"><i class="fas fa-trash-alt"></i> Remover</button>
                    </div>
                `;

                    eventsList.prepend(eventItem); // Adiciona no topo da lista
                });
            }

            // Mostra o modal
            const bsModal = new bootstrap.Modal(agendaModal);
            bsModal.show();
        }

        // Função auxiliar para formatar a data
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('pt-BR');
        }

        // Função para configurar eventos de documentos
        function setupDocumentItems() {
            // Adiciona eventos de clique para documentos disponíveis
            const documentItems = document.querySelectorAll('.document-item:not(.missing)');
            documentItems.forEach(item => {
                item.addEventListener('click', function () {
                    const docName = this.querySelector('span').textContent;
                    showNotification('Documento aberto: ' + docName, 'success');
                    // Em uma implementação real, aqui abriríamos o documento
                });
            });

            // Adiciona eventos de clique para documentos pendentes (ausentes)
            const missingDocItems = document.querySelectorAll('.document-item.missing');
            missingDocItems.forEach(item => {
                item.addEventListener('click', function () {
                    const docName = this.querySelector('span').textContent;
                    showNotification('Documento pendente. Favor enviar.', 'warning');
                    addInteractionMessage('Sistema', 'Solicitação de envio de documento: ' + docName);

                    // Simula uma animação de ênfase
                    this.style.animation = 'pulse 0.5s 2';
                    setTimeout(() => {
                        this.style.animation = '';
                    }, 1000);

                    // Em uma implementação real, aqui abriríamos um dialog para upload
                });
            });

            // Adiciona a animação de pulse para CSS se não existir
            if (!document.getElementById('pulse-animation')) {
                const style = document.createElement('style');
                style.id = 'pulse-animation';
                style.textContent = `
                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                    100% { transform: scale(1); }
                }

                /* Estilos para modal de Interação */
                .chat-message-full {
                    margin-bottom: 15px;
                    background-color: rgba(0, 0, 0, 0.2);
                    border-radius: 8px;
                    padding: 10px;
                }

                .chat-message-header {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 5px;
                }

                .message-sender-full {
                    color: var(--shell-yellow);
                    font-weight: 600;
                }

                .message-time {
                    color: #aaa;
                    font-size: 0.8rem;
                }

                .message-text-full {
                    margin: 0;
                    padding-left: 5px;
                    color: #fff;
                }

                /* Estilos para layout de interação modal */
                .interaction-modal-layout {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 20px;
                }

                .interaction-chat-panel {
                    flex: 1;
                    min-width: 300px;
                }

                .documents-panel {
                    flex: 1;
                    min-width: 300px;
                }

                .documents-sent, .documents-pending {
                    margin-bottom: 20px;
                }

                .document-preview {
                    background: rgba(0, 0, 0, 0.2);
                    border-radius: 8px;
                    padding: 10px;
                    margin-top: 10px;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }

                .document-preview i {
                    font-size: 24px;
                    color: var(--shell-yellow);
                }

                .document-info {
                    flex: 1;
                }

                .document-actions {
                    display: flex;
                    gap: 5px;
                }

                /* Responsividade para modal de interação */
                @media (max-width: 767.98px) {
                    .interaction-modal-layout {
                        flex-direction: column;
                    }

                    .interaction-chat-panel,
                    .documents-panel {
                        width: 100%;
                    }

                    /* Organiza documentos em lista para mobile */
                    .document-preview {
                        flex-direction: column;
                        align-items: flex-start;
                        text-align: left;
                    }

                    .document-actions {
                        width: 100%;
                        justify-content: center;
                        margin-top: 10px;
                    }
                }

                /* Estilo para caixa de custo total */
                .cost-total-box {
                    background: rgba(0, 0, 0, 0.2);
                    border-radius: 8px;
                    border: 1px solid var(--shell-yellow);
                    padding: 10px;
                    margin: 0 auto;
                    max-width: 90%;
                }

                .document-item-full {
                    display: flex;
                    margin-bottom: 15px;
                    background-color: rgba(0, 0, 0, 0.2);
                    border-radius: 8px;
                    padding: 10px;
                    align-items: center;
                    gap: 15px;
                }

                .document-item-full.missing {
                    border-left: 3px solid var(--danger-color);
                }

                .document-details {
                    flex: 1;
                }

                .document-details h6 {
                    margin-bottom: 5px;
                    color: #fff;
                }

                .doc-date, .doc-size, .doc-status {
                    display: block;
                    font-size: 0.8rem;
                    color: #aaa;
                    margin-bottom: 2px;
                }

                .doc-status {
                    color: var(--danger-color);
                }

                .document-actions {
                    margin-top: 8px;
                }

                /* Melhorias para responsividade em dispositivos móveis */
                @media (max-width: 767.98px) {
                    .flip-card {
                        width: 100%;
                        height: auto;
                        min-height: 450px;
                    }

                    .calendar-dashboard-container {
                        flex-direction: column;
                    }

                    .calendar-days > div {
                        font-size: 0.8rem;
                    }

                    .events-wrapper {
                        width: 100%;
                        margin-top: 20px;
                    }

                    .order-detail-panel .row .col-md-8,
                    .order-detail-panel .row .col-md-4 {
                        margin-bottom: 15px;
                    }

                    .order-detail-panel .col-md-4.border-start {
                        border-left: none !important;
                        border-top: 1px solid rgba(255, 255, 255, 0.1);
                        padding-top: 15px;
                    }

                    .chat-messages {
                        max-height: 120px; /* Mais espaço para mensagens em telas pequenas */
                    }

                    .document-item {
                        font-size: 0.75rem;
                    }

                    /* Ajustes para botões em telas pequenas */
                    .shell-btn {
                        padding: 0.375rem 0.5rem;
                        font-size: 0.875rem;
                    }

                    /* Adapta os cards para mobile */
                    .service-box {
                        margin-bottom: 15px;
                        height: auto !important; /* Anula a altura fixa em mobile */
                        min-height: 200px;
                    }

                    /* Melhora a visualização de documentos em mobile */
                    .documents-section .d-flex {
                        flex-direction: column;
                    }

                    .document-item {
                        margin-bottom: 5px;
                    }

                    .document-item.me-2 {
                        margin-right: 0 !important;
                        margin-bottom: 8px;
                    }

                    /* Ajusta a largura dos cards para ocupar toda a largura em mobile */
                    .col-md-3 {
                        width: 100%;
                    }

                    /* Melhora o espaçamento entre os cards em mobile */
                    .mt-3 {
                        margin-top: 0.75rem !important;
                    }
                }
            `;
                document.head.appendChild(style);
            }
        }

        // Função para mostrar notificações temporárias
        function showNotification(message, type) {
            // Cria elemento de notificação
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = message;

            // Adiciona ao corpo do documento
            document.body.appendChild(notification);

            // Remove após 3 segundos
            setTimeout(() => {
                notification.classList.add('hide');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 500);
            }, 3000);
        }

        document.addEventListener('DOMContentLoaded', function () {
            // Configura o tipo de manutenção no título (substituindo "Troca de Filtro")
            const maintenanceTypes = ["Manutenção Preventiva", "Manutenção Corretiva", "Calibração"];
            // Obtém o tipo atual da ordem (pode ser modificado para vir do backend)
            const currentMaintenanceType = maintenanceTypes[0]; // Usando o primeiro como exemplo

            // ID da ordem atual (em uma implementação real, seria obtido da URL ou do contexto)
            const currentOrderId = 1234; // Exemplo de ID

            // Define o tipo de manutenção no título principal do card e na barra superior
            document.getElementById('order-maintenance-type').textContent = currentMaintenanceType;

            // Configuração dos cards clicáveis
            setupServiceBoxes();

            // Inicializa eventos de clique para documentos
            setupDocumentItems();

            // Verifica se o usuário tem permissão para editar (role tecnico)
            const userRoleRaw = '{{ .UserRole }}'; // Obtém a role do usuário do contexto

            // Verifica se o valor foi substituído corretamente pelo template
            // Se não foi substituído, a string continuará como '{{ .UserRole }}'
            const userRole = userRoleRaw.includes('{') ? 'tecnico' : userRoleRaw;

            console.log("Role do usuário (raw):", userRoleRaw);
            console.log("Role do usuário (processada):", userRole);

            // Verifica se o papel do usuário é 'tecnico' (case insensitive)
            // Aceita tanto 'tecnico' exato quanto strings que contenham 'tecnico'
            const isTecnico = userRole && (
                userRole.toLowerCase() === 'tecnico' ||
                userRole.toLowerCase().includes('tecnico')
            );
            console.log("Usuário é técnico:", isTecnico);

            // Função para mostrar notificações
            function showNotification(message, type = 'info') {
                console.log(`Notificação (${type}): ${message}`);

                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.innerHTML = `
                    <div class="notification-icon">
                        <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                    </div>
                    <div class="notification-content">
                        <p>${message}</p>
                    </div>
                    <button class="notification-close"><i class="fas fa-times"></i></button>
                `;

                document.body.appendChild(notification);

                // Adiciona evento para fechar a notificação
                notification.querySelector('.notification-close').addEventListener('click', function() {
                    notification.remove();
                });

                // Remove a notificação após 5 segundos
                setTimeout(() => {
                    notification.classList.add('fade-out');
                    setTimeout(() => {
                        notification.remove();
                    }, 500);
                }, 5000);
            }

            // Adiciona evento específico para o botão de editar manutenção
            const btnEditarManutencao = document.getElementById('btn-editar-manutencao');
            if (btnEditarManutencao) {
                // Verifica permissão usando a mesma lógica atualizada
                // Usa a variável userRole já processada anteriormente
                const isTecnicoBtnVisual = userRole && (
                    userRole.toLowerCase() === 'tecnico' ||
                    userRole.toLowerCase().includes('tecnico')
                );

                console.log("Verificação de permissão para visual do botão manutenção:", userRole, isTecnicoBtnVisual);

                // Atualiza o visual do botão baseado na permissão
                if (!isTecnicoBtnVisual) {
                    btnEditarManutencao.classList.remove('btn-outline-warning');
                    btnEditarManutencao.classList.add('btn-outline-secondary');
                    btnEditarManutencao.title = 'Apenas técnicos podem editar informações de manutenção';
                }

                btnEditarManutencao.addEventListener('click', function(e) {
                    e.stopPropagation(); // Impede que o evento de clique do card seja acionado

                    // Verifica permissão usando a mesma lógica atualizada
                    // Usa a variável userRole já processada anteriormente
                    const isTecnicoBtn = userRole && (
                        userRole.toLowerCase() === 'tecnico' ||
                        userRole.toLowerCase().includes('tecnico')
                    );

                    console.log("Verificação de permissão no botão editar manutenção:", userRole, isTecnicoBtn);

                    if (!isTecnicoBtn) {
                        showNotification('Apenas usuários com perfil técnico podem editar informações de manutenção.', 'warning');
                        return;
                    }

                    // Obtém o card de manutenção
                    const manutencaoCard = document.getElementById('manutencao-card');

                    // Simula o clique no card para abrir o modal
                    if (manutencaoCard) {
                        manutencaoCard.click();
                    }
                });
            }

            // Adiciona evento específico para o botão de editar custos
            const btnEditarCustos = document.getElementById('btn-editar-custos');
            if (btnEditarCustos) {
                // Verifica permissão usando a mesma lógica atualizada
                // Usa a variável userRole já processada anteriormente
                const isTecnicoBtnCustosVisual = userRole && (
                    userRole.toLowerCase() === 'tecnico' ||
                    userRole.toLowerCase().includes('tecnico')
                );

                console.log("Verificação de permissão para visual do botão custos:", userRole, isTecnicoBtnCustosVisual);

                // Atualiza o visual do botão baseado na permissão
                if (!isTecnicoBtnCustosVisual) {
                    btnEditarCustos.classList.remove('btn-outline-warning');
                    btnEditarCustos.classList.add('btn-outline-secondary');
                    btnEditarCustos.title = 'Apenas técnicos podem editar informações de custos';
                }

                btnEditarCustos.addEventListener('click', function(e) {
                    e.stopPropagation(); // Impede que o evento de clique do card seja acionado

                    // Verifica permissão usando a mesma lógica atualizada
                    // Usa a variável userRole já processada anteriormente
                    const isTecnicoBtnCustos = userRole && (
                        userRole.toLowerCase() === 'tecnico' ||
                        userRole.toLowerCase().includes('tecnico')
                    );

                    console.log("Verificação de permissão no botão editar custos:", userRole, isTecnicoBtnCustos);

                    if (!isTecnicoBtnCustos) {
                        showNotification('Apenas usuários com perfil técnico podem editar informações de custos.', 'warning');
                        return;
                    }

                    // Obtém o card de custos
                    const custosCard = document.getElementById('custos-card');

                    // Simula o clique no card para abrir o modal
                    if (custosCard) {
                        custosCard.click();
                    }
                });
            }

            // Função para configurar os cards clicáveis
            function setupServiceBoxes() {
                console.log("Configurando cards clicáveis...");
                const serviceBoxes = document.querySelectorAll('.service-box');
                const modal = document.getElementById('service-detail-modal');
                const closeModal = document.getElementById('close-modal');
                const modalTitle = document.getElementById('modal-title');
                const modalContent = document.getElementById('modal-content');

                // Adiciona evento de clique a cada card de serviço
                console.log("Encontrados", serviceBoxes.length, "cards de serviço");
                serviceBoxes.forEach(box => {
                    box.addEventListener('click', function () {
                        console.log("Clique no card:", this.querySelector('h6').textContent);
                        // Obtém informações do card
                        const title = this.querySelector('h6').textContent;
                        const icon = this.querySelector('.service-box-icon i').className;
                        const content = this.innerHTML;

                        // Configura o modal
                        modalTitle.textContent = title;

                        // Cria conteúdo detalhado baseado no tipo de card
                        let detailedContent = '';

                        if (title === 'Manutenção') {
                            detailedContent = `
                            <div class="row">
                                <div class="col-12">
                                    <div class="detailed-service-info">
                                        <i class="${icon}" style="font-size: 3rem; color: var(--shell-yellow); margin-bottom: 15px;"></i>
                                        <h3>Manutenção</h3>
                                        <h4>Ordem #1234</h4>
                                        <div class="info-item mb-3">
                                            <span class="info-label">Equipamento:</span>
                                            <span class="info-value">Bomba de Combustível #2</span>
                                        </div>
                                        <div class="info-item mb-3">
                                            <span class="info-label">Status:</span>
                                            <span class="info-value"><span class="service-badge">Em Andamento</span></span>
                                        </div>

                                        <form id="manutencao-form">
                                            <div class="mb-4">
                                                <label for="info-geral" class="form-label text-white">Informações Gerais da Manutenção</label>
                                                <textarea class="form-control bg-dark text-white" id="info-geral" rows="4" placeholder="Descreva as informações gerais da manutenção realizada..."></textarea>
                                            </div>

                                            <div class="mb-4">
                                                <label for="pontos-importantes" class="form-label text-white">Pontos Importantes</label>
                                                <textarea class="form-control bg-dark text-white" id="pontos-importantes" rows="4" placeholder="Descreva os pontos importantes observados durante a manutenção..."></textarea>
                                            </div>

                                            <div class="d-flex justify-content-end">
                                                <button type="button" class="shell-btn shell-btn-success" id="salvar-manutencao">
                                                    <i class="fas fa-save"></i> Salvar Informações
                                                </button>
                                            </div>
                                        </form>

                                        <!-- Os scripts foram movidos para funções globais -->
                                    </div>
                                </div>
                            </div>
                        `;
                        } else if (title === 'Peças') {
                            detailedContent = `
                            <div class="row">
                                <div class="col-12">
                                    <div class="detailed-service-info">
                                        <i class="${icon}" style="font-size: 3rem; color: var(--shell-yellow); margin-bottom: 15px;"></i>
                                        <h3>Lista de Peças</h3>

                                        <div class="part-item" style="margin-bottom: 20px; padding: 15px; border-radius: 10px; background-color: rgba(255, 255, 255, 0.1);">
                                            <h4 style="color: var(--shell-yellow); margin-bottom: 10px;">Filtro Mod. F-23</h4>
                                            <div class="info-item">
                                                <span class="info-label">Quantidade:</span>
                                                <span class="info-value">2 unidades</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Preço unitário:</span>
                                                <span class="info-value">R$ 350,00</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Total:</span>
                                                <span class="info-value">R$ 700,00</span>
                                            </div>
                                        </div>

                                        <div class="part-item" style="margin-bottom: 20px; padding: 15px; border-radius: 10px; background-color: rgba(255, 255, 255, 0.1);">
                                            <h4 style="color: var(--shell-yellow); margin-bottom: 10px;">Vedação B-440</h4>
                                            <div class="info-item">
                                                <span class="info-label">Quantidade:</span>
                                                <span class="info-value">4 unidades</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Preço unitário:</span>
                                                <span class="info-value">R$ 42,50</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Total:</span>
                                                <span class="info-value">R$ 170,00</span>
                                            </div>
                                        </div>

                                        <div class="info-item" style="margin-top: 20px; border-top: 1px solid var(--shell-yellow); padding-top: 15px;">
                                            <span class="info-label" style="font-size: 1.2em; font-weight: bold;">Valor Total:</span>
                                            <span class="info-value" style="font-size: 1.2em; font-weight: bold;">R$ 870,00</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        } else if (title === 'Custos') {
                            detailedContent = `
                            <div class="row">
                                <div class="col-12">
                                    <div class="detailed-service-info">
                                        <i class="${icon}" style="font-size: 3rem; color: var(--shell-yellow); margin-bottom: 15px;"></i>
                                        <h3>Detalhamento de Custos</h3>

                                        <form id="custos-form">
                                            <!-- Seção de Peças -->
                                            <div class="mb-4">
                                                <h4 class="text-white mb-3">Peças Utilizadas</h4>
                                                <div id="pecas-container">
                                                    <div class="peca-item mb-3 p-3 rounded" style="background-color: rgba(255, 255, 255, 0.1);">
                                                        <div class="row mb-2">
                                                            <div class="col-md-6 mb-2">
                                                                <label class="form-label text-white">Descrição da Peça</label>
                                                                <input type="text" class="form-control bg-dark text-white" name="peca-descricao[]" placeholder="Ex: Filtro Mod. F-23">
                                                            </div>
                                                            <div class="col-md-2 mb-2">
                                                                <label class="form-label text-white">Quantidade</label>
                                                                <input type="number" class="form-control bg-dark text-white peca-quantidade" name="peca-quantidade[]" min="1" value="1">
                                                            </div>
                                                            <div class="col-md-3 mb-2">
                                                                <label class="form-label text-white">Valor Unitário (R$)</label>
                                                                <input type="number" step="0.01" class="form-control bg-dark text-white peca-valor" name="peca-valor[]" placeholder="0.00">
                                                            </div>
                                                            <div class="col-md-1 d-flex align-items-end mb-2">
                                                                <button type="button" class="btn btn-danger btn-sm remover-peca"><i class="fas fa-trash"></i></button>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-md-11">
                                                                <label class="form-label text-white">Observações</label>
                                                                <input type="text" class="form-control bg-dark text-white" name="peca-obs[]" placeholder="Observações sobre a peça">
                                                            </div>
                                                            <div class="col-md-1 d-flex align-items-end justify-content-end">
                                                                <span class="peca-subtotal text-warning fw-bold">R$ 0,00</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="text-center mt-2">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" id="adicionar-peca">
                                                        <i class="fas fa-plus"></i> Adicionar Peça
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- Seção de Mão de Obra e Deslocamento -->
                                            <div class="row mb-4">
                                                <div class="col-md-6">
                                                    <h4 class="text-white mb-3">Mão de Obra</h4>
                                                    <div class="mb-3">
                                                        <label class="form-label text-white">Descrição do Serviço</label>
                                                        <input type="text" class="form-control bg-dark text-white" id="mao-obra-descricao" placeholder="Ex: Técnico Nível 2 (2 horas)">
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label text-white">Valor (R$)</label>
                                                        <input type="number" step="0.01" class="form-control bg-dark text-white" id="mao-obra-valor" placeholder="0.00">
                                                    </div>
                                                </div>

                                                <div class="col-md-6">
                                                    <h4 class="text-white mb-3">Deslocamento</h4>
                                                    <div class="mb-3">
                                                        <label class="form-label text-white">Descrição</label>
                                                        <input type="text" class="form-control bg-dark text-white" id="deslocamento-descricao" placeholder="Ex: 50km (ida e volta)">
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label text-white">Valor (R$)</label>
                                                        <input type="number" step="0.01" class="form-control bg-dark text-white" id="deslocamento-valor" placeholder="0.00">
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Resumo de Custos -->
                                            <div class="cost-summary p-3 rounded" style="background-color: rgba(255, 255, 255, 0.1);">
                                                <h4 class="text-white mb-3">Resumo de Custos</h4>
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span>Total de Peças:</span>
                                                    <span id="total-pecas" class="text-warning">R$ 0,00</span>
                                                </div>
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span>Mão de Obra:</span>
                                                    <span id="total-mao-obra" class="text-warning">R$ 0,00</span>
                                                </div>
                                                <div class="d-flex justify-content-between mb-3">
                                                    <span>Deslocamento:</span>
                                                    <span id="total-deslocamento" class="text-warning">R$ 0,00</span>
                                                </div>
                                                <div class="d-flex justify-content-between pt-2 border-top border-secondary">
                                                    <span class="fw-bold">TOTAL GERAL:</span>
                                                    <span id="total-geral" class="text-warning fw-bold">R$ 0,00</span>
                                                </div>
                                            </div>

                                            <div class="d-flex justify-content-end mt-4">
                                                <button type="button" class="shell-btn shell-btn-success" id="salvar-custos">
                                                    <i class="fas fa-save"></i> Salvar Informações
                                                </button>
                                            </div>
                                        </form>

                                        <!-- Os scripts foram movidos para funções globais -->
                                    </div>
                                </div>
                            </div>
                        `;
                        } else if (title === 'Histórico') {
                            detailedContent = `
                            <div class="row">
                                <div class="col-12">
                                    <div class="detailed-service-info">
                                        <i class="${icon}" style="font-size: 3rem; color: var(--shell-yellow); margin-bottom: 15px;"></i>
                                        <h3>Histórico do Equipamento</h3>
                                        <h4>Bomba de Combustível #2</h4>

                                        <div class="maintenance-history mt-4">
                                            <h5 class="text-white mb-3">Manutenções Realizadas</h5>
                                            <div class="maintenance-cards">
                                                <div class="maintenance-card">
                                                    <div class="card-header">
                                                        <h5>Manutenção Preventiva</h5>
                                                        <span class="date-badge">15/01/2025</span>
                                                    </div>
                                                    <div class="card-body">
                                                        <p>Substituição da válvula B-12 e vedação do sistema de pressão.</p>
                                                        <div class="card-info">
                                                            <span class="badge bg-info">Duração: 1:30h</span>
                                                            <span class="badge bg-success">Custo: R$ 750,00</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="maintenance-card">
                                                    <div class="card-header">
                                                        <h5>Calibração</h5>
                                                        <span class="date-badge">20/10/2024</span>
                                                    </div>
                                                    <div class="card-body">
                                                        <p>Ajuste dos sensores e calibração dos medidores de fluxo.</p>
                                                        <div class="card-info">
                                                            <span class="badge bg-info">Duração: 1:00h</span>
                                                            <span class="badge bg-success">Custo: R$ 250,00</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="maintenance-card">
                                                    <div class="card-header">
                                                        <h5>Manutenção Corretiva</h5>
                                                        <span class="date-badge">05/07/2024</span>
                                                    </div>
                                                    <div class="card-body">
                                                        <p>Substituição do motor elétrico do sistema de bombeamento.</p>
                                                        <div class="card-info">
                                                            <span class="badge bg-info">Duração: 4:00h</span>
                                                            <span class="badge bg-success">Custo: R$ 1.650,00</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="mt-4">
                                                <h5 class="text-white">Próximas Manutenções Programadas</h5>
                                                <div class="table-responsive">
                                                    <table class="table table-dark table-sm mt-3">
                                                        <thead>
                                                            <tr>
                                                                <th>Tipo</th>
                                                                <th>Data Prevista</th>
                                                                <th>Descrição</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td>Preventiva</td>
                                                                <td>20/06/2025</td>
                                                                <td>Troca de filtros e inspeção do sistema</td>
                                                            </tr>
                                                            <tr>
                                                                <td>Calibração</td>
                                                                <td>15/09/2025</td>
                                                                <td>Calibração dos medidores e sensores</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        } else if (title === 'Interação') {
                            detailedContent = `
                            <div class="interaction-modal-layout">
                                <div class="interaction-chat-panel">
                                    <div class="text-center mb-4">
                                        <i class="${icon}" style="font-size: 3rem; color: var(--shell-yellow);"></i>
                                        <h3>Interações da Ordem</h3>
                                    </div>

                                    <div class="chat-expanded" style="max-height: 300px; overflow-y: auto; margin-bottom: 15px;">
                                        <div class="chat-message-full">
                                            <div class="chat-message-header">
                                                <span class="message-sender-full">João Silva (Gerente)</span>
                                                <span class="message-time">18/03/2025 09:15</span>
                                            </div>
                                            <p class="message-text-full">Necessário realizar manutenção na bomba 2. Clientes relataram problemas de pressão.</p>
                                        </div>
                                        <div class="chat-message-full">
                                            <div class="chat-message-header">
                                                <span class="message-sender-full">Carlos Silva (Técnico)</span>
                                                <span class="message-time">19/03/2025 10:30</span>
                                            </div>
                                            <p class="message-text-full">Confirmo disponibilidade para atendimento amanhã (20/03).</p>
                                        </div>
                                        <div class="chat-message-full">
                                            <div class="chat-message-header">
                                                <span class="message-sender-full">Maria Souza (Financeiro)</span>
                                                <span class="message-time">19/03/2025 11:45</span>
                                            </div>
                                            <p class="message-text-full">Aprovado o orçamento. Favor enviar nota fiscal após conclusão.</p>
                                        </div>
                                        <div class="chat-message-full">
                                            <div class="chat-message-header">
                                                <span class="message-sender-full">Carlos Silva (Técnico)</span>
                                                <span class="message-time">20/03/2025 14:30</span>
                                            </div>
                                            <p class="message-text-full">Iniciei os trabalhos. Filtros já substituídos. Aguardando nota fiscal do fornecedor para encaminhar.</p>
                                        </div>
                                        <div class="chat-message-full">
                                            <div class="chat-message-header">
                                                <span class="message-sender-full">Maria Souza (Financeiro)</span>
                                                <span class="message-time">20/03/2025 15:15</span>
                                            </div>
                                            <p class="message-text-full">Favor enviar NF para liberação.</p>
                                        </div>
                                    </div>

                                    <div class="send-message-form">
                                        <h5 class="text-white-50 mb-2">Enviar Mensagem</h5>
                                        <div class="input-group">
                                            <textarea class="form-control bg-dark text-white" placeholder="Digite sua mensagem" style="height: 80px;"></textarea>
                                        </div>
                                        <div class="mt-2 text-end">
                                            <button class="shell-btn" onclick="sendInteractionMessage()"><i class="fas fa-paper-plane"></i> Enviar</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="documents-panel">
                                    <h4 class="text-white mb-3">Documentos da Ordem</h4>

                                    <div class="documents-sent">
                                        <h5 class="text-white-50">Documentos Enviados</h5>
                                        <div class="document-preview">
                                            <i class="fas fa-file-pdf fa-2x text-danger"></i>
                                            <div class="document-info">
                                                <strong>Relatório Técnico</strong>
                                                <div>
                                                    <small>Enviado em: 20/03/2025 14:45</small><br>
                                                    <small>Tamanho: 1.2 MB</small>
                                                </div>
                                            </div>
                                            <div class="document-actions">
                                                <button class="btn btn-sm btn-dark"><i class="fas fa-eye"></i></button>
                                                <button class="btn btn-sm btn-dark"><i class="fas fa-download"></i></button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="documents-pending mt-4">
                                        <h5 class="text-white-50">Documentos Pendentes</h5>
                                        <div class="document-preview" style="border-left: 3px solid #ff4444;">
                                            <i class="fas fa-file-invoice fa-2x text-warning"></i>
                                            <div class="document-info">
                                                <strong>Nota Fiscal</strong>
                                                <div>
                                                    <small>Solicitado em: 20/03/2025 15:15</small><br>
                                                    <small class="text-danger">Status: Pendente</small>
                                                </div>
                                            </div>
                                            <div class="document-actions">
                                                <button class="btn btn-sm btn-warning"><i class="fas fa-upload"></i> Enviar</button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="upload-new-doc mt-4">
                                        <h5 class="text-white-50">Enviar Novo Documento</h5>
                                        <div class="upload-form mt-2">
                                            <div class="mb-3">
                                                <select class="form-select bg-dark text-white">
                                                    <option>Selecione o tipo de documento</option>
                                                    <option>Nota Fiscal</option>
                                                    <option>Relatório Técnico</option>
                                                    <option>Comprovante de Pagamento</option>
                                                    <option>Foto do Equipamento</option>
                                                    <option>Outro</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <div class="input-group">
                                                    <input type="file" class="form-control bg-dark text-white">
                                                </div>
                                            </div>
                                            <div class="text-end">
                                                <button class="shell-btn"><i class="fas fa-cloud-upload-alt"></i> Enviar</button>
                                            </div>
                                        </div>
                                    </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        } else if (title === 'Cronograma') {
                            detailedContent = `
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="detailed-service-info">
                                        <i class="${icon}" style="font-size: 3rem; color: var(--shell-yellow); margin-bottom: 15px;"></i>
                                        <h3>Cronograma Detalhado</h3>
                                        <div class="cronograma-container">
                                            <div class="cronograma-item">
                                                <i class="fas fa-file-alt"></i>
                                                <div class="cronograma-content">
                                                    <h5>Abertura da Ordem</h5>
                                                    <p>19/03/2025 - 10:30h</p>
                                                    <span class="timeline-status completed">Concluído</span>
                                                </div>
                                            </div>
                                            <div class="cronograma-item">
                                                <i class="fas fa-clipboard-check"></i>
                                                <div class="cronograma-content">
                                                    <h5>Confirmação do Técnico</h5>
                                                    <p>19/03/2025 - 15:45h</p>
                                                    <span class="timeline-status completed">Concluído</span>
                                                    <small class="d-block mt-1 text-muted">Data compromissada: 20/03/2025</small>
                                                </div>
                                            </div>
                                            <div class="cronograma-item">
                                                <i class="fas fa-tools"></i>
                                                <div class="cronograma-content">
                                                    <h5>Início do Serviço</h5>
                                                    <p>20/03/2025 - 14:00h</p>
                                                    <span class="timeline-status current">Em andamento</span>
                                                    <small class="d-block mt-1 text-muted">Abertura da ordem na filial</small>
                                                </div>
                                            </div>
                                            <div class="cronograma-item">
                                                <i class="fas fa-check-double"></i>
                                                <div class="cronograma-content">
                                                    <h5>Término Previsto</h5>
                                                    <p>20/03/2025 - 16:00h</p>
                                                    <span class="timeline-status pending">Pendente</span>
                                                    <small class="d-block mt-1 text-muted">Data estipulada pelo técnico</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="technician-info">
                                        <h4>Técnico Responsável</h4>
                                        <div class="technician-card">
                                            <div class="technician-photo">
                                                <i class="fas fa-user-circle" style="font-size: 5rem; color: #ddd;"></i>
                                            </div>
                                            <div class="technician-details">
                                                <h5>Carlos Silva</h5>
                                                <p><i class="fas fa-id-badge"></i> ID: T-2374</p>
                                                <p><i class="fas fa-certificate"></i> Técnico Nível 2</p>
                                                <p><i class="fas fa-star"></i> Avaliação: 4.8/5.0</p>
                                                <p><i class="fas fa-history"></i> 347 ordens concluídas</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        }

                        modalContent.innerHTML = detailedContent;

                        // Abre o modal
                        modal.classList.add('active');

                        // Configura os event listeners específicos para o tipo de modal
                        setupModalEventListeners(title);
                    });
                });

                // Fecha o modal quando o botão de fechar é clicado
                closeModal.addEventListener('click', function () {
                    modal.classList.remove('active');
                });

                // Fecha o modal se clicar fora do conteúdo
                modal.addEventListener('click', function (event) {
                    if (event.target === modal) {
                        modal.classList.remove('active');
                    }
                });
            }

            // Função para configurar os event listeners dos botões dentro do modal
            function setupModalEventListeners(modalType) {
                console.log("Configurando event listeners para o modal:", modalType);

                if (modalType === 'Manutenção') {
                    const salvarManutencaoBtn = document.getElementById('salvar-manutencao');
                    if (salvarManutencaoBtn) {
                        console.log("Botão salvar-manutencao encontrado, adicionando evento");

                        // Remover event listeners anteriores para evitar duplicação
                        const novoBtn = salvarManutencaoBtn.cloneNode(true);
                        salvarManutencaoBtn.parentNode.replaceChild(novoBtn, salvarManutencaoBtn);

                        novoBtn.addEventListener('click', function() {
                            // Verifica permissão usando a variável específica para manutenção
                            const isTecnicoManutencao = userRole && (
                                userRole.toLowerCase() === 'tecnico' ||
                                userRole.toLowerCase().includes('tecnico')
                            );

                            console.log("Verificação de permissão ao salvar manutenção:", userRole, isTecnicoManutencao);

                            if (!isTecnicoManutencao) {
                                showNotification('Você não tem permissão para editar informações de manutenção.', 'error');
                                return;
                            }

                            const infoGeral = document.getElementById('info-geral').value;
                            const pontosImportantes = document.getElementById('pontos-importantes').value;

                            if (infoGeral.trim() === '' && pontosImportantes.trim() === '') {
                                alert('Por favor, preencha pelo menos um dos campos antes de salvar.');
                                return;
                            }

                            // Atualiza o preview no card
                            const previewElement = document.getElementById('manutencao-preview');
                            if (previewElement) {
                                let previewText = '';
                                if (infoGeral) previewText += infoGeral + '\n';
                                if (pontosImportantes) previewText += '\nPontos importantes: ' + pontosImportantes;

                                previewElement.textContent = previewText.length > 80 ?
                                    previewText.substring(0, 80) + '...' : previewText;
                            }

                            // Prepara os dados para enviar ao backend
                            const dadosManutencao = {
                                ordem_id: currentOrderId,
                                info_geral: infoGeral,
                                pontos_importantes: pontosImportantes,
                                data_registro: new Date().toISOString(),
                                tecnico_id: '{{ .UserID }}',
                                tecnico_nome: '{{ .UserName }}'
                            };

                            console.log('Dados de manutenção a serem salvos:', dadosManutencao);

                            // Aqui seria implementada a lógica para salvar no backend via AJAX
                            // Por enquanto, apenas mostra uma notificação
                            showNotification('Informações de manutenção salvas com sucesso!', 'success');

                            // Fecha o modal
                            document.getElementById('service-detail-modal').classList.remove('active');
                        });
                    } else {
                        console.error("Botão salvar-manutencao não encontrado!");
                    }

                    // Verifica se o usuário tem permissão para editar (role tecnico)
                    // Aceita tanto 'tecnico' exato quanto strings que contenham 'tecnico'
                    const isTecnicoManutencao = userRole && (
                        userRole.toLowerCase() === 'tecnico' ||
                        userRole.toLowerCase().includes('tecnico')
                    );

                    console.log("Verificação de permissão para manutenção:", userRole, isTecnicoManutencao);

                    // Desabilita os campos se o usuário não for técnico
                    if (!isTecnicoManutencao) {
                        const infoGeral = document.getElementById('info-geral');
                        const pontosImportantes = document.getElementById('pontos-importantes');
                        const salvarBtn = document.getElementById('salvar-manutencao');

                        if (infoGeral) infoGeral.disabled = true;
                        if (pontosImportantes) pontosImportantes.disabled = true;
                        if (salvarBtn) {
                            salvarBtn.disabled = true;
                            salvarBtn.title = 'Apenas técnicos podem editar informações de manutenção';
                        }

                        // Adiciona mensagem de aviso
                        const form = document.getElementById('manutencao-form');
                        if (form) {
                            const alertDiv = document.createElement('div');
                            alertDiv.className = 'alert alert-warning mt-3';
                            alertDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Apenas usuários com perfil técnico podem editar informações de manutenção.';
                            form.prepend(alertDiv);
                        }
                    }
                } else if (modalType === 'Custos') {
                    // Configurar eventos para o modal de Custos
                    setupCustosModalEvents();
                }
            }

            // Função para formatar valor em Reais - movida para escopo global
            function formatarReais(valor) {
                return 'R$ ' + parseFloat(valor || 0).toFixed(2).replace('.', ',');
            }

            // Função para configurar os eventos do modal de Custos
            function setupCustosModalEvents() {
                console.log("Configurando eventos para o modal de Custos");

                // Função para calcular o subtotal de uma peça
                function calcularSubtotalPeca(pecaItem) {
                    const quantidade = parseFloat(pecaItem.querySelector('.peca-quantidade')?.value || 0);
                    const valorUnitario = parseFloat(pecaItem.querySelector('.peca-valor')?.value || 0);
                    const subtotal = quantidade * valorUnitario;
                    const subtotalEl = pecaItem.querySelector('.peca-subtotal');
                    if (subtotalEl) subtotalEl.textContent = formatarReais(subtotal);
                    return subtotal;
                }

                // Função para calcular todos os totais
                function calcularTotais() {
                    console.log("Calculando totais");
                    // Calcula total de peças
                    let totalPecas = 0;
                    document.querySelectorAll('.peca-item').forEach(pecaItem => {
                        totalPecas += calcularSubtotalPeca(pecaItem);
                    });

                    // Obtém valores de mão de obra e deslocamento
                    const maoObraValor = parseFloat(document.getElementById('mao-obra-valor')?.value || 0);
                    const deslocamentoValor = parseFloat(document.getElementById('deslocamento-valor')?.value || 0);

                    // Atualiza os totais na interface
                    const totalPecasEl = document.getElementById('total-pecas');
                    const totalMaoObraEl = document.getElementById('total-mao-obra');
                    const totalDeslocamentoEl = document.getElementById('total-deslocamento');
                    const totalGeralEl = document.getElementById('total-geral');

                    if (totalPecasEl) totalPecasEl.textContent = formatarReais(totalPecas);
                    if (totalMaoObraEl) totalMaoObraEl.textContent = formatarReais(maoObraValor);
                    if (totalDeslocamentoEl) totalDeslocamentoEl.textContent = formatarReais(deslocamentoValor);

                    // Calcula e atualiza o total geral
                    const totalGeral = totalPecas + maoObraValor + deslocamentoValor;
                    if (totalGeralEl) totalGeralEl.textContent = formatarReais(totalGeral);

                    return {
                        totalPecas,
                        maoObraValor,
                        deslocamentoValor,
                        totalGeral
                    };
                }

                // Adiciona evento para recalcular quando valores são alterados
                document.querySelectorAll('.peca-quantidade, .peca-valor, #mao-obra-valor, #deslocamento-valor').forEach(input => {
                    if (input) {
                        console.log("Adicionando evento de input para:", input);
                        input.addEventListener('input', calcularTotais);
                    }
                });

                // Adiciona evento para o botão de adicionar peça
                const adicionarPecaBtn = document.getElementById('adicionar-peca');
                if (adicionarPecaBtn) {
                    console.log("Botão adicionar-peca encontrado, adicionando evento");

                    // Remover event listeners anteriores para evitar duplicação
                    const novoBtn = adicionarPecaBtn.cloneNode(true);
                    adicionarPecaBtn.parentNode.replaceChild(novoBtn, adicionarPecaBtn);

                    novoBtn.addEventListener('click', function() {
                        console.log("Clique no botão adicionar peça");
                        const pecasContainer = document.getElementById('pecas-container');
                        if (!pecasContainer) {
                            console.error("Container de peças não encontrado!");
                            return;
                        }

                        const pecaItems = document.querySelectorAll('.peca-item');
                        if (pecaItems.length === 0) {
                            console.error("Nenhum item de peça encontrado para clonar!");
                            return;
                        }

                        const novaPeca = pecaItems[0].cloneNode(true);

                        // Limpa os valores do clone
                        novaPeca.querySelectorAll('input').forEach(input => {
                            input.value = input.type === 'number' && input.classList.contains('peca-quantidade') ? 1 : '';
                        });
                        const subtotalEl = novaPeca.querySelector('.peca-subtotal');
                        if (subtotalEl) subtotalEl.textContent = 'R$ 0,00';

                        // Adiciona eventos aos novos campos
                        novaPeca.querySelectorAll('.peca-quantidade, .peca-valor').forEach(input => {
                            input.addEventListener('input', () => calcularSubtotalPeca(novaPeca));
                            input.addEventListener('input', calcularTotais);
                        });

                        // Adiciona evento ao botão de remover
                        const removerBtn = novaPeca.querySelector('.remover-peca');
                        if (removerBtn) {
                            removerBtn.addEventListener('click', function() {
                                if (document.querySelectorAll('.peca-item').length > 1) {
                                    novaPeca.remove();
                                    calcularTotais();
                                } else {
                                    alert('É necessário manter pelo menos uma peça na lista.');
                                }
                            });
                        }

                        // Adiciona a nova peça ao container
                        pecasContainer.appendChild(novaPeca);
                        console.log("Nova peça adicionada ao container");
                    });
                } else {
                    console.error("Botão adicionar-peca não encontrado!");
                }

                // Adiciona evento ao botão de remover para as peças iniciais
                document.querySelectorAll('.remover-peca').forEach(button => {
                    if (button) {
                        console.log("Botão remover-peca encontrado, adicionando evento");

                        // Remover event listeners anteriores para evitar duplicação
                        const novoBtn = button.cloneNode(true);
                        button.parentNode.replaceChild(novoBtn, button);

                        novoBtn.addEventListener('click', function() {
                            console.log("Clique no botão remover peça");
                            if (document.querySelectorAll('.peca-item').length > 1) {
                                this.closest('.peca-item').remove();
                                calcularTotais();
                            } else {
                                alert('É necessário manter pelo menos uma peça na lista.');
                            }
                        });
                    }
                });

                // Verifica se o usuário tem permissão para editar (role tecnico)
                // Aceita tanto 'tecnico' exato quanto strings que contenham 'tecnico'
                // Usa a variável userRole já processada anteriormente
                const isTecnicoCusto = userRole && (
                    userRole.toLowerCase() === 'tecnico' ||
                    userRole.toLowerCase().includes('tecnico')
                );

                // Desabilita os campos se o usuário não for técnico
                if (!isTecnicoCusto) {
                    document.querySelectorAll('#custos-form input, #custos-form button').forEach(el => {
                        if (el) el.disabled = true;
                    });

                    // Adiciona mensagem de aviso
                    const form = document.getElementById('custos-form');
                    if (form) {
                        const alertDiv = document.createElement('div');
                        alertDiv.className = 'alert alert-warning mt-3';
                        alertDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Apenas usuários com perfil técnico podem editar informações de custos.';
                        form.prepend(alertDiv);
                    }
                }

                // Adiciona evento ao botão de salvar
                const salvarCustosBtn = document.getElementById('salvar-custos');
                if (salvarCustosBtn) {
                    console.log("Botão salvar-custos encontrado, adicionando evento");

                    // Remover event listeners anteriores para evitar duplicação
                    const novoBtn = salvarCustosBtn.cloneNode(true);
                    salvarCustosBtn.parentNode.replaceChild(novoBtn, salvarCustosBtn);

                    novoBtn.addEventListener('click', function() {
                        console.log("Clique no botão salvar custos");
                        // Verifica novamente a permissão (segurança adicional)
                        if (!isTecnicoCusto) {
                            showNotification('Você não tem permissão para editar informações de custos.', 'error');
                            return;
                        }

                        // Coleta os dados do formulário
                        const pecas = [];
                        document.querySelectorAll('.peca-item').forEach(pecaItem => {
                            const descricao = pecaItem.querySelector('[name="peca-descricao[]"]')?.value;
                            const quantidade = pecaItem.querySelector('[name="peca-quantidade[]"]')?.value;
                            const valor = pecaItem.querySelector('[name="peca-valor[]"]')?.value;
                            const obs = pecaItem.querySelector('[name="peca-obs[]"]')?.value;

                            if (descricao && quantidade && valor) {
                                pecas.push({
                                    descricao,
                                    quantidade: parseInt(quantidade),
                                    valor: parseFloat(valor),
                                    obs,
                                    subtotal: quantidade * valor
                                });
                            }
                        });

                        const maoObra = {
                            descricao: document.getElementById('mao-obra-descricao')?.value || '',
                            valor: parseFloat(document.getElementById('mao-obra-valor')?.value || 0)
                        };

                        const deslocamento = {
                            descricao: document.getElementById('deslocamento-descricao')?.value || '',
                            valor: parseFloat(document.getElementById('deslocamento-valor')?.value || 0)
                        };

                        const totais = calcularTotais();

                        // Cria o objeto com todos os dados
                        const dadosCustos = {
                            pecas,
                            maoObra,
                            deslocamento,
                            totais,
                            ordem_id: currentOrderId,
                            data_registro: new Date().toISOString(),
                            tecnico_id: '{{ .UserID }}',
                            tecnico_nome: '{{ .UserName }}'
                        };

                        console.log('Dados de custos a serem salvos:', dadosCustos);

                        // Atualiza o preview no card
                        const custosPreview = document.getElementById('custos-preview');
                        if (custosPreview) {
                            custosPreview.innerHTML = '';

                            // Adiciona linha de peças
                            const pecasDiv = document.createElement('div');
                            pecasDiv.className = 'd-flex justify-content-between';
                            pecasDiv.innerHTML = '<span>Peças:</span><span>' + formatarReais(totais.totalPecas) + '</span>';
                            custosPreview.appendChild(pecasDiv);

                            // Adiciona linha de mão de obra
                            const maoObraDiv = document.createElement('div');
                            maoObraDiv.className = 'd-flex justify-content-between';
                            maoObraDiv.innerHTML = '<span>Mão de obra:</span><span>' + formatarReais(totais.maoObraValor) + '</span>';
                            custosPreview.appendChild(maoObraDiv);

                            // Adiciona linha de deslocamento
                            const deslocamentoDiv = document.createElement('div');
                            deslocamentoDiv.className = 'd-flex justify-content-between';
                            deslocamentoDiv.innerHTML = '<span>Deslocamento:</span><span>' + formatarReais(totais.deslocamentoValor) + '</span>';
                            custosPreview.appendChild(deslocamentoDiv);

                            // Adiciona linha de total
                            const totalDiv = document.createElement('div');
                            totalDiv.className = 'd-flex justify-content-between mt-2 text-warning fw-bold';
                            totalDiv.innerHTML = '<span>Total:</span><span>' + formatarReais(totais.totalGeral) + '</span>';
                            custosPreview.appendChild(totalDiv);
                        }

                        // Aqui seria implementada a lógica para salvar no backend via AJAX
                        // Por enquanto, apenas mostra uma notificação
                        showNotification('Informações de custos salvas com sucesso!', 'success');

                        // Fecha o modal
                        document.getElementById('service-detail-modal').classList.remove('active');
                    });
                } else {
                    console.error("Botão salvar-custos não encontrado!");
                }

                // Inicializa os cálculos
                calcularTotais();
            }

            // Funções do calendário
            const calendar = {
                currentDate: new Date(),
                daysContainer: document.getElementById('calendar-days'),
                monthTitle: document.querySelector('.calendar-title'),

                init() {
                    this.renderCalendar();
                    this.setupNavigation();
                },

                renderCalendar() {
                    const year = this.currentDate.getFullYear();
                    const month = this.currentDate.getMonth();

                    // Atualiza o título do mês
                    this.monthTitle.textContent = `${this.getMonthName(month)} ${year}`;

                    // Limpa o container de dias
                    this.daysContainer.innerHTML = '';

                    // Obtém o primeiro dia do mês
                    const firstDay = new Date(year, month, 1);
                    // Obtém o último dia do mês
                    const lastDay = new Date(year, month + 1, 0);

                    // Número de dias no mês
                    const daysInMonth = lastDay.getDate();

                    // Dia da semana do primeiro dia (0 = Domingo, 6 = Sábado)
                    const firstDayIndex = firstDay.getDay();

                    // Dias do mês anterior para preencher o início do calendário
                    for (let i = 0; i < firstDayIndex; i++) {
                        const dayElement = document.createElement('div');
                        dayElement.className = 'calendar-day prev-month';
                        dayElement.style.opacity = '0.5';
                        this.daysContainer.appendChild(dayElement);
                    }

                    // Dias do mês atual
                    const today = new Date();
                    const isCurrentMonth = today.getMonth() === month && today.getFullYear() === year;

                    // Eventos do mês (simulados)
                    const events = [
                        { day: 10, type: 'completed' },
                        { day: 12, type: 'multiple', count: 2, types: ['urgent', 'scheduled'] },
                        { day: 14, type: 'scheduled' },
                        { day: 15, type: 'urgent' },
                        { day: 18, type: 'approved' },
                        { day: 20, type: 'waiting' },
                        { day: 22, type: 'approved-scheduled' },
                        { day: 25, type: 'scheduled' },
                        { day: 26, type: 'waiting' },
                        { day: 27, type: 'approved' },
                        { day: 28, type: 'multiple', count: 2, types: ['urgent', 'waiting'] },
                        { day: 30, type: 'multiple', count: 3, types: ['scheduled', 'approved', 'waiting'] }
                    ];

                    // Log para debug
                    console.log("Eventos carregados:", events);

                    for (let day = 1; day <= daysInMonth; day++) {
                        const dayElement = document.createElement('div');
                        dayElement.className = 'calendar-day';
                        dayElement.textContent = day;

                        // Verifica se é hoje
                        if (isCurrentMonth && day === today.getDate()) {
                            dayElement.classList.add('today');
                        }
                        // Verifica se tem evento
                        const hasEvent = events.find(event => event.day === day);
                        if (hasEvent) {
                            dayElement.classList.add('has-event');
                            dayElement.setAttribute('data-events', hasEvent.type === 'multiple' ? hasEvent.count : 1);
                            dayElement.setAttribute('data-status', hasEvent.type);

                            // Adiciona indicadores de eventos baseados no tipo
                            if (hasEvent.type === 'multiple') {
                                // Cria container para múltiplos indicadores
                                const indicatorsDiv = document.createElement('div');
                                indicatorsDiv.className = 'event-indicators';
                                dayElement.appendChild(indicatorsDiv);

                                // Adiciona cada indicador baseado nos tipos
                                hasEvent.types.forEach(type => {
                                    const indicator = document.createElement('div');
                                    indicator.className = `event-indicator ${type}`;
                                    indicatorsDiv.appendChild(indicator);
                                });

                                // Tooltip para múltiplos eventos
                                dayElement.setAttribute('title', `${hasEvent.count} ordens de manutenção neste dia`);
                            } else {
                                // Cria um único indicador para eventos simples
                                const indicator = document.createElement('div');
                                indicator.className = `event-indicator ${hasEvent.type}`;
                                dayElement.appendChild(indicator);

                                // Tooltips baseados no tipo
                                const tooltips = {
                                    'urgent': 'Manutenção Urgente',
                                    'scheduled': 'Manutenção Programada',
                                    'completed': 'Manutenção Concluída',
                                    'approved': 'Manutenção Aprovada',
                                    'waiting': 'Aguardando Aprovação',
                                    'approved-scheduled': 'Aprovado - Manutenção Programada'
                                };

                                dayElement.setAttribute('title', tooltips[hasEvent.type] || 'Manutenção');
                            }

                            // Adiciona efeito de clique para mostrar detalhes
                            dayElement.addEventListener('click', function () {
                                // Efeito visual de seleção
                                document.querySelectorAll('.calendar-day').forEach(day => {
                                    day.classList.remove('selected');
                                });
                                this.classList.add('selected');

                                // Para datas com múltiplas ordens, exibe um modal de seleção
                                if (hasEvent.type === 'multiple') {
                                    console.log("Abrindo modal para selecionar ordens:", hasEvent.types, day, hasEvent.day);
                                    showOrderSelectionModal(hasEvent.types, day, hasEvent.day);
                                } else {
                                    // Ativa a animação do cartão diretamente para ordens únicas
                                    document.getElementById('flip-card').classList.add('flipped');

                                    // Atualiza os detalhes da ordem
                                    const eventType = hasEvent.type !== 'waiting' ? hasEvent.type : 'medium';
                                    showOrderDetails(eventType, day);
                                }
                            });
                        }

                        this.daysContainer.appendChild(dayElement);
                    }
                },

                getMonthName(month) {
                    const months = [
                        'Janeiro', 'Fevereiro', 'Março', 'Abril',
                        'Maio', 'Junho', 'Julho', 'Agosto',
                        'Setembro', 'Outubro', 'Novembro', 'Dezembro'
                    ];
                    return months[month];
                },

                setupNavigation() {
                    document.querySelector('.prev-month').addEventListener('click', () => {
                        this.currentDate.setMonth(this.currentDate.getMonth() - 1);
                        this.renderCalendar();
                    });

                    document.querySelector('.next-month').addEventListener('click', () => {
                        this.currentDate.setMonth(this.currentDate.getMonth() + 1);
                        this.renderCalendar();
                    });
                }
            };

            // Função para mostrar detalhes da ordem
            function showOrderDetails(type, day) {
                // Dados para os diferentes tipos de ordem
                const orderData = {
                    'urgent': {
                        title: 'Manutenção Bomba 3',
                        date: '15 de Março, 2025',
                        priority: {
                            text: 'Urgente',
                            class: 'bg-danger'
                        },
                        description: 'Bomba 3 apresentando falhas intermitentes. Clientes relataram problemas de abastecimento. Necessário reparo imediato no sistema de bombeamento e verificação dos sensores.',
                        location: 'Posto Shell Paulista',
                        equipment: 'Bomba de Combustível #3',
                        responsible: 'Ana Oliveira',
                        status: 'Atrasado',
                        statusClass: 'bg-danger',
                        time: '3 horas'
                    },
                    'medium': {
                        title: 'Manutenção Preventiva',
                        date: '20 de Março, 2025',
                        priority: {
                            text: 'Prioridade Média',
                            class: 'bg-warning text-white'
                        },
                        description: 'Troca de filtro de combustível da bomba 2. Cliente relatou baixa pressão durante abastecimento. Peças já disponíveis no estoque local.',
                        location: 'Posto Shell Ipiranga',
                        equipment: 'Bomba de Combustível #2',
                        responsible: 'Carlos Silva',
                        status: 'Aguardando Aprovação',
                        statusClass: 'bg-info',
                        time: '2 horas'
                    },
                    'scheduled': {
                        title: 'Revisão Geral',
                        date: '25 de Março, 2025',
                        priority: {
                            text: 'Programada',
                            class: 'bg-primary'
                        },
                        description: 'Revisão geral do sistema de abastecimento para manutenção preventiva. Inclui calibração de bombas, verificação de mangueiras e inspeção do sistema eletrônico.',
                        location: 'Posto Shell Centro',
                        equipment: 'Painel Central e Bombas 1-4',
                        responsible: 'Pedro Santos',
                        status: 'Agendado',
                        statusClass: 'bg-success',
                        time: '4 horas'
                    },
                    'completed': {
                        title: 'Limpeza de Tanques',
                        date: '10 de Março, 2025',
                        priority: {
                            text: 'Concluída',
                            class: 'bg-secondary'
                        },
                        description: 'Limpeza completa dos tanques de combustível, incluindo remoção de sedimentos e inspeção de componentes. Serviço realizado com sucesso.',
                        location: 'Posto Shell Tiradentes',
                        equipment: 'Tanques de Armazenamento 1 e 2',
                        responsible: 'Roberto Almeida',
                        status: 'Concluído',
                        statusClass: 'bg-secondary',
                        time: '6 horas'
                    },
                    'approved': {
                        title: 'Calibração de Bombas',
                        date: '27 de Março, 2025',
                        priority: {
                            text: 'Aprovada',
                            class: 'bg-success'
                        },
                        description: 'Calibração de precisão em todas as bombas de combustível. Ajuste fino de medidores e verificação de conformidade com normas regulatórias.',
                        location: 'Posto Shell Vila Mariana',
                        equipment: 'Bombas de Combustível 1-6',
                        responsible: 'Luiz Ferreira',
                        status: 'Aprovado',
                        statusClass: 'bg-success',
                        time: '5 horas'
                    },
                    'waiting': {
                        title: 'Substituição de Sensores',
                        date: '26 de Março, 2025',
                        priority: {
                            text: 'Média',
                            class: 'bg-warning text-white'
                        },
                        description: 'Substituição dos sensores de pressão e temperatura nas bombas de combustível. Equipamentos apresentando leituras inconsistentes.',
                        location: 'Posto Shell Freguesia',
                        equipment: 'Painel de Sensores Central',
                        responsible: 'Marina Costa',
                        status: 'Aguardando Aprovação',
                        statusClass: 'bg-info',
                        time: '2 horas'
                    },
                    'approved-scheduled': {
                        title: 'Troca de Válvulas',
                        date: '22 de Março, 2025',
                        priority: {
                            text: 'Programada',
                            class: 'bg-primary'
                        },
                        description: 'Substituição programada das válvulas de controle de fluxo. Peças já disponíveis, aguardando data programada para execução.',
                        location: 'Posto Shell Morumbi',
                        equipment: 'Sistema de Distribuição Central',
                        responsible: 'Fábio Mendes',
                        status: 'Aprovado - Manutenção Programada',
                        statusClass: 'bg-primary',
                        time: '3 horas'
                    }
                };

                // Dados da ordem selecionada
                const data = orderData[type];

                // Atualiza o título da ordem no painel - substituído por "Detalhes da Ordem"
                document.getElementById('order-panel-title').textContent = 'Detalhes da Ordem';

                // Atualiza os elementos com os dados da ordem
                document.getElementById('order-title').textContent = data.title;
                // Data removida conforme solicitado
                // document.getElementById('order-date').textContent = data.date;
                // Prioridade removida conforme solicitado
                document.getElementById('order-priority').style.display = 'none';
                document.getElementById('order-description').textContent = data.description;
                // Alterado de "Local" para "Filial"
                document.getElementById('order-location').innerHTML = `<i class="fas fa-map-marker-alt text-danger"></i> ${data.branch || data.location}`;
                document.getElementById('order-equipment').innerHTML = `<i class="fas fa-tools text-secondary"></i> ${data.equipment}`;
                document.getElementById('order-responsible').innerHTML = `<i class="fas fa-user text-primary"></i> ${data.responsible}`;
                document.getElementById('order-status').innerHTML = `<span class="badge ${data.statusClass}">${data.status}</span>`;
                document.getElementById('order-time').innerHTML = `<i class="far fa-clock"></i> ${data.time}`;
            }

            // Função para mostrar modal de seleção de ordem
            function showOrderSelectionModal(types, day, date) {
                // Verifica se já existe um modal de seleção e remove se existir
                let existingModal = document.getElementById('orderSelectionModal');
                if (existingModal) {
                    document.body.removeChild(existingModal);
                }

                // Mapeia os tipos para descrições mais amigáveis
                const typeDescriptions = {
                    'urgent': 'Manutenção Urgente',
                    'scheduled': 'Manutenção Programada',
                    'completed': 'Manutenção Concluída',
                    'approved': 'Manutenção Aprovada',
                    'waiting': 'Aguardando Aprovação',
                    'approved-scheduled': 'Aprovado - Manutenção Programada'
                };

                // Obtém detalhes específicos para cada tipo de ordem
                const orderData = {
                    'urgent': {
                        title: 'Manutenção Bomba 3',
                        location: 'Posto Shell Paulista',
                        equipment: 'Bomba de Combustível #3',
                        status: 'Atrasado',
                        statusClass: 'danger'
                    },
                    'medium': {
                        title: 'Manutenção Preventiva',
                        location: 'Posto Shell Ipiranga',
                        equipment: 'Bomba de Combustível #2',
                        status: 'Aguardando Aprovação',
                        statusClass: 'info'
                    },
                    'scheduled': {
                        title: 'Revisão Geral',
                        location: 'Posto Shell Centro',
                        equipment: 'Painel Central e Bombas 1-4',
                        status: 'Agendado',
                        statusClass: 'success'
                    },
                    'completed': {
                        title: 'Limpeza de Tanques',
                        location: 'Posto Shell Tiradentes',
                        equipment: 'Tanques de Armazenamento 1 e 2',
                        status: 'Concluído',
                        statusClass: 'secondary'
                    },
                    'approved': {
                        title: 'Calibração de Bombas',
                        location: 'Posto Shell Vila Mariana',
                        equipment: 'Bombas de Combustível 1-6',
                        status: 'Aprovado',
                        statusClass: 'success'
                    },
                    'waiting': {
                        title: 'Substituição de Sensores',
                        location: 'Posto Shell Freguesia',
                        equipment: 'Painel de Sensores Central',
                        status: 'Aguardando Aprovação',
                        statusClass: 'info'
                    },
                    'approved-scheduled': {
                        title: 'Troca de Válvulas',
                        location: 'Posto Shell Morumbi',
                        equipment: 'Sistema de Distribuição Central',
                        status: 'Aprovado - Manutenção Programada',
                        statusClass: 'primary'
                    }
                };

                // Cria o modal
                const modal = document.createElement('div');
                modal.id = 'orderSelectionModal';
                modal.className = 'order-selection-modal';

                // Verifica se temos dados para o dia selecionado
                if (date) {
                    const formattedDate = `${date}/03/2025`;

                    let ordersHtml = '<div class="orders-list">';

                    // Gera os itens da lista de ordens
                    types.forEach((type, index) => {
                        const data = orderData[type === 'waiting' ? 'waiting' : type];

                        ordersHtml += `
                        <div class="order-option" data-type="${type}">
                            <div class="order-indicator ${type}"></div>
                            <div class="order-details">
                                <h4>${data.title}</h4>
                                <p><i class="fas fa-map-marker-alt text-danger"></i> ${data.location}</p>
                                <p><i class="fas fa-tools text-secondary"></i> ${data.equipment}</p>
                                <span class="badge bg-${data.statusClass}">${data.status}</span>
                            </div>
                            <button class="shell-btn view-order-btn">Visualizar</button>
                        </div>
                    `;
                    });

                    ordersHtml += '</div>';

                    // Monta o conteúdo do modal
                    modal.innerHTML = `
                    <div class="order-selection-content">
                        <div class="order-selection-header">
                            <h3>Ordens do dia ${formattedDate}</h3>
                            <button class="close-selection-modal"><i class="fas fa-times"></i></button>
                        </div>
                        <p class="selection-instruction">Selecione uma ordem para visualizar detalhes:</p>
                        ${ordersHtml}
                    </div>
                `;

                    // Adiciona o modal ao body
                    document.body.appendChild(modal);

                    // Mostra o modal com animação
                    setTimeout(() => {
                        modal.classList.add('active');
                    }, 10);

                    // Adiciona handler para fechar o modal
                    const closeBtn = modal.querySelector('.close-selection-modal');
                    closeBtn.addEventListener('click', function () {
                        modal.classList.remove('active');
                        setTimeout(() => {
                            document.body.removeChild(modal);
                        }, 300);
                    });

                    // Adiciona handler para os botões de visualizar ordem
                    const viewBtns = modal.querySelectorAll('.view-order-btn');
                    viewBtns.forEach((btn, index) => {
                        btn.addEventListener('click', function () {
                            const orderType = types[index];

                            // Fecha o modal
                            modal.classList.remove('active');

                            // Ativa a animação do cartão
                            document.getElementById('flip-card').classList.add('flipped');

                            // Atualiza os detalhes da ordem
                            const eventType = orderType !== 'waiting' ? orderType : 'medium';
                            showOrderDetails(eventType, day);

                            // Remove o modal após a animação
                            setTimeout(() => {
                                if (document.body.contains(modal)) {
                                    document.body.removeChild(modal);
                                }
                            }, 300);
                        });
                    });

                    // Adiciona handler para clicar nas opções de ordem (não apenas no botão)
                    const orderOptions = modal.querySelectorAll('.order-option');
                    orderOptions.forEach((option, index) => {
                        option.addEventListener('click', function (e) {
                            // Evita acionar quando clica no botão (que já tem seu próprio handler)
                            if (!e.target.closest('.view-order-btn')) {
                                const orderType = types[index];

                                // Fecha o modal
                                modal.classList.remove('active');

                                // Ativa a animação do cartão
                                document.getElementById('flip-card').classList.add('flipped');

                                // Atualiza os detalhes da ordem
                                const eventType = orderType !== 'waiting' ? orderType : 'medium';
                                showOrderDetails(eventType, day);

                                // Remove o modal após a animação
                                setTimeout(() => {
                                    if (document.body.contains(modal)) {
                                        document.body.removeChild(modal);
                                    }
                                }, 300);
                            }
                        });
                    });
                }
            }

            // Botão para voltar ao calendário
            document.getElementById('back-to-calendar').addEventListener('click', function () {
                document.getElementById('flip-card').classList.remove('flipped');
            });

            // Inicializa o calendário
            calendar.init();
        });
    </script>
</body>

</html>
{{ end }}