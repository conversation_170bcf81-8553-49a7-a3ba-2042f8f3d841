{{ define "notifications/list.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notificações - Shell</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&family=Share+Tech+Mono&display=swap" rel="stylesheet">
    <link href="/static/css/common.css" rel="stylesheet">
    <link href="/static/css/notifications.css" rel="stylesheet">
</head>
<body class="bg-dark text-light">
    {{ template "sidebar" . }}
    
    <div class="content-with-sidebar">
        <div class="main-content">
            <div class="page-header d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-bell me-2"></i>Notificações</h1>
                <button id="markAllAsRead" class="btn btn-shell-yellow">
                    <i class="fas fa-check-double me-2"></i>Marcar todas como lidas
                </button>
            </div>

            <div class="page-content">
                <div class="notifications-container">
                    {{ range .Notifications }}
                    <div class="notification-card card-shell mb-3 {{ if not .Read }}unread{{ end }}" data-id="{{ .ID }}">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h5 class="card-title">{{ .Title }}</h5>
                                    <p class="card-text">{{ .Message }}</p>
                                </div>
                                <span class="notification-type badge-shell-{{ .Type }}">
                                    {{ .Type }}
                                </span>
                            </div>
                            <div class="notification-footer">
                                <small class="text-muted">{{ .CreatedAt.Format "02/01/2006 15:04" }}</small>
                                {{ if not .Read }}
                                <button class="btn btn-sm btn-shell-red mark-as-read">
                                    <i class="fas fa-check me-1"></i>Marcar como lida
                                </button>
                                {{ end }}
                            </div>
                        </div>
                    </div>
                    {{ else }}
                    <div class="text-center py-5">
                        <i class="fas fa-bell-slash fa-3x mb-3"></i>
                        <h4>Nenhuma notificação encontrada</h4>
                        <p class="text-muted">Você não tem notificações no momento</p>
                    </div>
                    {{ end }}
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/notifications.js"></script>
</body>
</html>
{{ end }} 