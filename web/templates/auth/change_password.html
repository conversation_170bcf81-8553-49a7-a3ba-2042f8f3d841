{{ define "auth/change_password.html" }}
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alteração de Senha - Shell Tradição</title>
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <style>
        body {
            background-color: #f8f9fa;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
        }
        .card {
            max-width: 450px;
            width: 100%;
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
            border: none;
        }
        .card-header {
            background-color: #ffbf00;
            color: #444;
            border-bottom: none;
            padding: 1.5rem;
        }
        .card-body {
            padding: 2rem;
        }
        .form-control {
            height: calc(2.5rem + 2px);
            padding: 0.5rem 1rem;
        }
        .btn-primary {
            background-color: #d30000;
            border-color: #d30000;
            padding: 0.5rem 1rem;
            font-weight: 500;
        }
        .btn-primary:hover {
            background-color: #b80000;
            border-color: #b80000;
        }
        .password-requirements {
            font-size: 0.85rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: 0.25rem;
            margin-top: 1rem;
        }
        .password-requirements ul {
            padding-left: 1.25rem;
            margin-bottom: 0;
        }
        .password-strength {
            height: 5px;
            margin-top: 0.25rem;
            border-radius: 2px;
        }
        .logo-container {
            text-align: center;
            margin-bottom: 1rem;
        }
        .logo-container img {
            height: 70px;
        }
        .divider {
            margin: 1.5rem 0;
            border-top: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card">
                    <div class="card-header text-center">
                        <div class="logo-container">
                            <img src="/static/images/logo.png" alt="Shell Tradição">
                        </div>
                        <h4 class="mb-0">Alterar Senha</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning" role="alert">
                            <i class="fas fa-exclamation-triangle"></i> 
                            É necessário alterar sua senha para continuar.
                        </div>
                        
                        <div id="alert-container"></div>
                        
                        <form id="change-password-form">
                            <div class="mb-3">
                                <label for="current-password" class="form-label">Senha Atual</label>
                                <input type="password" class="form-control" id="current-password" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="new-password" class="form-label">Nova Senha</label>
                                <input type="password" class="form-control" id="new-password" required>
                                <div class="password-strength mt-2"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="confirm-password" class="form-label">Confirmar Nova Senha</label>
                                <input type="password" class="form-control" id="confirm-password" required>
                            </div>
                            
                            <div class="password-requirements">
                                <h6>Requisitos de Senha:</h6>
                                <div id="password-policy">
                                    <p>Carregando política de senhas...</p>
                                </div>
                            </div>
                            
                            <div class="divider"></div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">Alterar Senha</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/fontawesome.min.js"></script>
    <script>
        $(document).ready(function() {
            // Limpa qualquer token armazenado para garantir segurança
            localStorage.removeItem('auth_token');
            
            // Carrega a política de senhas
            loadPasswordPolicy();
            
            // Verifica a força da senha em tempo real
            $('#new-password').on('input', function() {
                checkPasswordStrength($(this).val());
            });
            
            // Manipulador de envio do formulário
            $('#change-password-form').on('submit', function(e) {
                e.preventDefault();
                
                const currentPassword = $('#current-password').val();
                const newPassword = $('#new-password').val();
                const confirmPassword = $('#confirm-password').val();
                
                // Validação básica
                if (newPassword !== confirmPassword) {
                    showAlert('As senhas não coincidem. Por favor, tente novamente.', 'danger');
                    return;
                }
                
                // Envia requisição para a API
                $.ajax({
                    url: '/api/password/change',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        current_password: currentPassword,
                        new_password: newPassword,
                        confirm_password: confirmPassword
                    }),
                    success: function(response) {
                        showAlert('Senha alterada com sucesso! Redirecionando...', 'success');
                        setTimeout(function() {
                            window.location.href = '/dashboard';
                        }, 2000);
                    },
                    error: function(xhr) {
                        let errorMessage = 'Ocorreu um erro ao alterar a senha.';
                        if (xhr.responseJSON && xhr.responseJSON.error) {
                            errorMessage = xhr.responseJSON.error;
                        }
                        showAlert(errorMessage, 'danger');
                    }
                });
            });
            
            // Função para mostrar alertas
            function showAlert(message, type) {
                $('#alert-container').html(`
                    <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
                    </div>
                `);
            }
            
            // Função para verificar a força da senha
            function checkPasswordStrength(password) {
                let strength = 0;
                const strengthBar = $('.password-strength');
                
                if (password.length === 0) {
                    strengthBar.css('width', '0%').css('background-color', '#e9ecef');
                    return;
                }
                
                // Comprimento mínimo
                if (password.length >= 8) strength += 1;
                if (password.length >= 12) strength += 1;
                
                // Letras maiúsculas e minúsculas
                if (password.match(/[a-z]/)) strength += 1;
                if (password.match(/[A-Z]/)) strength += 1;
                
                // Números e caracteres especiais
                if (password.match(/\d/)) strength += 1;
                if (password.match(/[^a-zA-Z\d]/)) strength += 1;
                
                // Mapeia a força para uma porcentagem
                const percent = (strength / 6) * 100;
                
                // Define a cor com base na força
                let color;
                if (percent < 30) color = '#dc3545'; // Vermelho
                else if (percent < 60) color = '#ffc107'; // Amarelo
                else if (percent < 80) color = '#17a2b8'; // Azul claro
                else color = '#28a745'; // Verde
                
                strengthBar.css('width', percent + '%').css('background-color', color);
            }
            
            // Carrega a política de senhas do servidor
            function loadPasswordPolicy() {
                $.ajax({
                    url: '/api/password/status',
                    type: 'GET',
                    success: function(response) {
                        if (response.password_policy) {
                            $('#password-policy').html(response.password_policy.replace(/\n/g, '<br>'));
                        }
                    },
                    error: function() {
                        $('#password-policy').html('<p>Não foi possível carregar a política de senhas.</p>');
                    }
                });
            }
        });
    </script>
</body>
</html>
{{ end }}