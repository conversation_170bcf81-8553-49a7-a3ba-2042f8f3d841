{{ define "auth/acesso_negado.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Acesso Negado - Shell Tradição</title>

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&family=Share+Tech+Mono&display=swap" rel="stylesheet">
    <link href="/static/css/common.css" rel="stylesheet">
    <link href="/static/css/styles.css" rel="stylesheet">
    <link href="/static/css/acesso_negado.css" rel="stylesheet">
</head>
<body>
    <div class="acesso-negado-container">
        <div class="acesso-negado-card">
            <!-- Elementos decorativos -->
            <div class="decorative-element decorative-element-1"></div>
            <div class="decorative-element decorative-element-2"></div>
            <i class="fas fa-cog rotating-gear gear-1"></i>
            <i class="fas fa-cog rotating-gear gear-2"></i>

            <!-- Cabeçalho -->
            <div class="acesso-negado-header">
                <div class="security-tape"></div>
                <h2>Acesso Negado</h2>
            </div>

            <!-- Corpo -->
            <div class="acesso-negado-body">
                <!-- Ícone -->
                <div class="icon-container">
                    <i class="fas fa-exclamation-triangle" id="icon"></i>
                </div>

                <!-- Mensagem -->
                <div class="message-container">
                    <h3 class="message-title">Área Restrita!</h3>
                    <p class="message-text" id="message">{{ .message }}</p>
                    <p class="message-text">Você não tem permissão para acessar esta página.</p>

                    <!-- Informações do usuário -->
                    <div class="user-info">
                        <p>Seu perfil: <strong>{{ if .user }}{{ .user.Role }}{{ else }}Desconhecido{{ end }}</strong></p>
                    </div>
                </div>

                <!-- Contador -->
                <div class="countdown-container">
                    <p class="countdown-text">Você será desconectado em:</p>
                    <div class="countdown-timer" id="countdown">10</div>
                </div>

                <!-- Botões -->
                <div class="btn-container">
                    <a href="/logout" class="btn-shell" id="redirect-button">Sair do Sistema</a>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/acesso_negado.js"></script>
</body>
</html>
{{ end }}