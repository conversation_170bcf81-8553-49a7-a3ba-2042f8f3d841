{{ define "auth/security_settings.html" }}
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .title }}</title>
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    {{ template "layouts/header.html" . }}

    <div class="container my-5">
        <div class="row">
            <div class="col-md-3">
                <!-- Menu lateral -->
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><PERSON><PERSON></h5>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="/profile" class="list-group-item list-group-item-action">
                            <i class="fas fa-user mr-2"></i> Meu Perfil
                        </a>
                        <a href="/profile/security" class="list-group-item list-group-item-action active">
                            <i class="fas fa-shield-alt mr-2"></i> Segurança
                        </a>
                        <a href="/profile/notifications" class="list-group-item list-group-item-action">
                            <i class="fas fa-bell mr-2"></i> Notificações
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-9">
                <!-- Conteúdo Principal -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Configurações de Segurança</h5>
                    </div>
                    <div class="card-body">
                        <!-- Seção Senha -->
                        <h4>Alteração de Senha</h4>
                        <form id="password-form" class="mb-4">
                            <div class="form-group">
                                <label for="current-password">Senha Atual</label>
                                <input type="password" class="form-control" id="current-password" required>
                            </div>
                            <div class="form-group">
                                <label for="new-password">Nova Senha</label>
                                <input type="password" class="form-control" id="new-password" required>
                            </div>
                            <div class="form-group">
                                <label for="confirm-password">Confirmar Nova Senha</label>
                                <input type="password" class="form-control" id="confirm-password" required>
                            </div>
                            <button type="submit" class="btn btn-primary">Alterar Senha</button>
                        </form>
                        
                        <hr>
                        
                        <!-- Seção Autenticação de Dois Fatores -->
                        <h4>Autenticação de Dois Fatores (2FA)</h4>
                        <div id="2fa-status" class="alert" role="alert">
                            <!-- Status do 2FA será mostrado aqui-->
                        </div>
                        
                        <div id="2fa-disabled-section">
                            <p>A autenticação de dois fatores adiciona uma camada extra de segurança à sua conta. Além da senha, você precisará informar um código temporário gerado pelo seu aplicativo autenticador.</p>
                            <button id="enable-2fa-btn" class="btn btn-success">Ativar Autenticação de Dois Fatores</button>
                        </div>
                        
                        <div id="2fa-setup-section" style="display: none;">
                            <h5>Configure o Autenticador</h5>
                            <p>Escaneie o código QR abaixo com um aplicativo autenticador como Google Authenticator, Microsoft Authenticator ou Authy:</p>
                            
                            <div class="text-center mb-3">
                                <div id="qrcode-container">
                                    <!-- O QR code será inserido aqui -->
                                </div>
                            </div>
                            
                            <div class="alert alert-warning">
                                <strong>Importante:</strong> Guarde estes códigos de recuperação em um local seguro. Se você perder acesso ao seu dispositivo, poderá utilizá-los para acessar sua conta.
                                <div class="mt-2">
                                    <ul id="recovery-codes" class="list-group">
                                        <!-- Os códigos de recuperação serão inseridos aqui -->
                                    </ul>
                                </div>
                                <button id="copy-recovery-codes" class="btn btn-sm btn-info mt-2">Copiar Códigos</button>
                            </div>
                            
                            <form id="verify-2fa-form">
                                <div class="form-group">
                                    <label for="totp-code">Digite o código de 6 dígitos do seu aplicativo:</label>
                                    <input type="text" class="form-control" id="totp-code" maxlength="6" required pattern="[0-9]{6}">
                                </div>
                                <button type="submit" class="btn btn-primary">Verificar e Ativar</button>
                                <button id="cancel-2fa-setup" class="btn btn-secondary">Cancelar</button>
                            </form>
                        </div>
                        
                        <div id="2fa-enabled-section" style="display: none;">
                            <p>A autenticação de dois fatores está <strong>ativada</strong> para sua conta. Isso ajuda a proteger sua conta mesmo que sua senha seja comprometida.</p>
                            <button id="disable-2fa-btn" class="btn btn-danger">Desativar Autenticação de Dois Fatores</button>
                            
                            <div id="disable-2fa-confirm" class="mt-3" style="display: none;">
                                <div class="alert alert-warning">
                                    <strong>Atenção:</strong> Desativar a autenticação de dois fatores reduzirá a segurança da sua conta.
                                </div>
                                <form id="disable-2fa-form">
                                    <div class="form-group">
                                        <label for="password-confirm">Digite sua senha para confirmar:</label>
                                        <input type="password" class="form-control" id="password-confirm" required>
                                    </div>
                                    <button type="submit" class="btn btn-danger">Confirmar Desativação</button>
                                    <button id="cancel-disable-2fa" class="btn btn-secondary">Cancelar</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Histórico de Atividades -->
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Histórico de Atividades</h5>
                    </div>
                    <div class="card-body">
                        <p>Últimos acessos à sua conta:</p>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Data/Hora</th>
                                    <th>Atividade</th>
                                    <th>Endereço IP</th>
                                    <th>Dispositivo</th>
                                </tr>
                            </thead>
                            <tbody id="activity-log">
                                <!-- O histórico será carregado dinamicamente -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{ template "layouts/footer.html" . }}

    <script src="/static/js/jquery-3.6.0.min.js"></script>
    <script src="/static/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.4.4/build/qrcode.min.js"></script>
    <script>
        $(document).ready(function() {
            // Verifica o status atual do 2FA
            checkTOTPStatus();
            
            // Botão para iniciar configuração do 2FA
            $("#enable-2fa-btn").click(function() {
                setupTOTP();
            });
            
            // Formulário para verificar e ativar 2FA
            $("#verify-2fa-form").submit(function(e) {
                e.preventDefault();
                verifyAndEnableTOTP();
            });
            
            // Botão para cancelar configuração
            $("#cancel-2fa-setup").click(function() {
                $("#2fa-setup-section").hide();
                $("#2fa-disabled-section").show();
            });
            
            // Botão para desativar 2FA
            $("#disable-2fa-btn").click(function() {
                $("#disable-2fa-confirm").show();
            });
            
            // Cancelar desativação
            $("#cancel-disable-2fa").click(function() {
                $("#disable-2fa-confirm").hide();
            });
            
            // Formulário para desativar 2FA
            $("#disable-2fa-form").submit(function(e) {
                e.preventDefault();
                disableTOTP();
            });
            
            // Formulário de alteração de senha
            $("#password-form").submit(function(e) {
                e.preventDefault();
                changePassword();
            });
            
            // Botão para copiar códigos de recuperação
            $("#copy-recovery-codes").click(function() {
                copyRecoveryCodes();
            });
            
            // Carrega o histórico de atividades
            loadActivityHistory();
        });
        
        // Verifica o status do 2FA
        function checkTOTPStatus() {
            $.ajax({
                url: '/api/auth/me',
                method: 'GET',
                success: function(response) {
                    if (response.totp_enabled) {
                        $("#2fa-status").addClass("alert-success").removeClass("alert-warning").text("Autenticação de dois fatores está ATIVADA");
                        $("#2fa-disabled-section").hide();
                        $("#2fa-setup-section").hide();
                        $("#2fa-enabled-section").show();
                    } else {
                        $("#2fa-status").addClass("alert-warning").removeClass("alert-success").text("Autenticação de dois fatores está DESATIVADA");
                        $("#2fa-disabled-section").show();
                        $("#2fa-setup-section").hide();
                        $("#2fa-enabled-section").hide();
                    }
                },
                error: function() {
                    $("#2fa-status").addClass("alert-danger").text("Erro ao verificar status do 2FA");
                }
            });
        }
        
        // Configura o TOTP
        function setupTOTP() {
            $.ajax({
                url: '/api/auth/2fa/setup',
                method: 'GET',
                success: function(response) {
                    // Exibe o QR code
                    $("#qrcode-container").empty();
                    QRCode.toCanvas(document.getElementById('qrcode-container'), response.qr_code_url, function (error) {
                        if (error) console.error(error);
                    });
                    
                    // Exibe os códigos de recuperação
                    $("#recovery-codes").empty();
                    response.recovery_codes.forEach(function(code) {
                        $("#recovery-codes").append(`<li class="list-group-item">${code}</li>`);
                    });
                    
                    // Mostra a seção de configuração
                    $("#2fa-disabled-section").hide();
                    $("#2fa-setup-section").show();
                },
                error: function(xhr) {
                    alert("Erro ao configurar 2FA: " + (xhr.responseJSON ? xhr.responseJSON.error : "Erro desconhecido"));
                }
            });
        }
        
        // Verifica e ativa o TOTP
        function verifyAndEnableTOTP() {
            const code = $("#totp-code").val();
            
            $.ajax({
                url: '/api/auth/2fa/enable',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ totp_code: code }),
                success: function() {
                    alert("Autenticação de dois fatores ativada com sucesso!");
                    checkTOTPStatus();
                },
                error: function(xhr) {
                    alert("Erro ao verificar código: " + (xhr.responseJSON ? xhr.responseJSON.error : "Código inválido"));
                }
            });
        }
        
        // Desativa o TOTP
        function disableTOTP() {
            const password = $("#password-confirm").val();
            
            $.ajax({
                url: '/api/auth/2fa/disable',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ password: password }),
                success: function() {
                    alert("Autenticação de dois fatores desativada com sucesso!");
                    $("#disable-2fa-confirm").hide();
                    checkTOTPStatus();
                },
                error: function(xhr) {
                    alert("Erro ao desativar 2FA: " + (xhr.responseJSON ? xhr.responseJSON.error : "Senha incorreta"));
                }
            });
        }
        
        // Altera a senha
        function changePassword() {
            const currentPassword = $("#current-password").val();
            const newPassword = $("#new-password").val();
            const confirmPassword = $("#confirm-password").val();
            
            if (newPassword !== confirmPassword) {
                alert("As senhas não coincidem!");
                return;
            }
            
            $.ajax({
                url: '/api/auth/password/change',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    current_password: currentPassword,
                    new_password: newPassword
                }),
                success: function() {
                    alert("Senha alterada com sucesso!");
                    $("#password-form")[0].reset();
                },
                error: function(xhr) {
                    alert("Erro ao alterar senha: " + (xhr.responseJSON ? xhr.responseJSON.error : "Erro desconhecido"));
                }
            });
        }
        
        // Copia os códigos de recuperação
        function copyRecoveryCodes() {
            const codes = [];
            $("#recovery-codes li").each(function() {
                codes.push($(this).text());
            });
            
            navigator.clipboard.writeText(codes.join("\n")).then(function() {
                alert("Códigos de recuperação copiados para a área de transferência!");
            }, function() {
                alert("Não foi possível copiar os códigos. Por favor, copie-os manualmente.");
            });
        }
        
        // Carrega o histórico de atividades
        function loadActivityHistory() {
            // Esta função seria implementada para carregar o histórico de atividades
            // Por enquanto, vamos adicionar alguns dados de exemplo
            const mockActivities = [
                { timestamp: "2023-04-03 14:35:22", activity: "Login bem-sucedido", ip: "***********", device: "Chrome / Windows" },
                { timestamp: "2023-04-02 10:22:15", activity: "Senha alterada", ip: "***********", device: "Chrome / Windows" },
                { timestamp: "2023-04-01 08:45:30", activity: "Login bem-sucedido", ip: "*************", device: "Safari / iOS" }
            ];
            
            $("#activity-log").empty();
            mockActivities.forEach(function(activity) {
                $("#activity-log").append(`
                    <tr>
                        <td>${activity.timestamp}</td>
                        <td>${activity.activity}</td>
                        <td>${activity.ip}</td>
                        <td>${activity.device}</td>
                    </tr>
                `);
            });
        }
    </script>
</body>
</html>
{{ end }}