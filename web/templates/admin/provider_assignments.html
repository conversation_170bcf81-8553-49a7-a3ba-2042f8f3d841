{{define "admin/provider_assignments.html"}}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <link rel="stylesheet" href="/static/css/shell.css">
    <link rel="stylesheet" href="/static/css/all.min.css">
    <style>
        .card {
            margin-bottom: 20px;
        }
        .table-responsive {
            margin-top: 15px;
        }
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
    </style>
</head>
<body class="bg-dark">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            {{template "layouts/sidebar.html" .}}
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 bg-dark text-light">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Gerenciamento de Atribuições de Prestadores</h1>
                </div>
                
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card bg-dark text-light border-secondary">
                            <div class="card-header bg-dark text-light border-secondary d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Atribuições de Prestadores a Filiais</h5>
                                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addBranchAssignmentModal">
                                    <i class="fas fa-plus"></i> Nova Atribuição
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-dark table-striped table-hover" id="branchAssignmentsTable">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Prestador</th>
                                                <th>Filial</th>
                                                <th>Data de Criação</th>
                                                <th>Ações</th>
                                            </tr>
                                        </thead>
                                        <tbody id="branchAssignmentsTableBody">
                                            <!-- Preenchido via JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card bg-dark text-light border-secondary">
                            <div class="card-header bg-dark text-light border-secondary d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Atribuições de Prestadores a Tipos de Equipamento</h5>
                                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addEquipmentTypeAssignmentModal">
                                    <i class="fas fa-plus"></i> Nova Atribuição
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-dark table-striped table-hover" id="equipmentTypeAssignmentsTable">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Prestador</th>
                                                <th>Tipo de Equipamento</th>
                                                <th>Data de Criação</th>
                                                <th>Ações</th>
                                            </tr>
                                        </thead>
                                        <tbody id="equipmentTypeAssignmentsTableBody">
                                            <!-- Preenchido via JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Modal para adicionar atribuição de prestador a filial -->
    <div class="modal fade" id="addBranchAssignmentModal" tabindex="-1" aria-labelledby="addBranchAssignmentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content bg-dark text-light border-secondary">
                <div class="modal-header bg-dark text-light border-secondary">
                    <h5 class="modal-title" id="addBranchAssignmentModalLabel">Nova Atribuição de Prestador a Filial</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="branchAssignmentForm">
                        <div class="mb-3">
                            <label for="serviceProviderSelect" class="form-label">Prestador</label>
                            <select class="form-select bg-dark text-light border-secondary" id="serviceProviderSelect" required>
                                <option value="">Selecione um prestador</option>
                                <!-- Preenchido via JavaScript -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="branchSelect" class="form-label">Filial</label>
                            <select class="form-select bg-dark text-light border-secondary" id="branchSelect" required>
                                <option value="">Selecione uma filial</option>
                                <!-- Preenchido via JavaScript -->
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer bg-dark text-light border-secondary">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" id="saveBranchAssignment">Salvar</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal para adicionar atribuição de prestador a tipo de equipamento -->
    <div class="modal fade" id="addEquipmentTypeAssignmentModal" tabindex="-1" aria-labelledby="addEquipmentTypeAssignmentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content bg-dark text-light border-secondary">
                <div class="modal-header bg-dark text-light border-secondary">
                    <h5 class="modal-title" id="addEquipmentTypeAssignmentModalLabel">Nova Atribuição de Prestador a Tipo de Equipamento</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="equipmentTypeAssignmentForm">
                        <div class="mb-3">
                            <label for="serviceProviderSelect2" class="form-label">Prestador</label>
                            <select class="form-select bg-dark text-light border-secondary" id="serviceProviderSelect2" required>
                                <option value="">Selecione um prestador</option>
                                <!-- Preenchido via JavaScript -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="equipmentTypeSelect" class="form-label">Tipo de Equipamento</label>
                            <select class="form-select bg-dark text-light border-secondary" id="equipmentTypeSelect" required>
                                <option value="">Selecione um tipo de equipamento</option>
                                <!-- Preenchido via JavaScript -->
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer bg-dark text-light border-secondary">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" id="saveEquipmentTypeAssignment">Salvar</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal de confirmação de exclusão -->
    <div class="modal fade" id="confirmDeleteModal" tabindex="-1" aria-labelledby="confirmDeleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content bg-dark text-light border-secondary">
                <div class="modal-header bg-dark text-light border-secondary">
                    <h5 class="modal-title" id="confirmDeleteModalLabel">Confirmar Exclusão</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Tem certeza que deseja excluir esta atribuição?</p>
                    <input type="hidden" id="deleteItemId">
                    <input type="hidden" id="deleteItemType">
                </div>
                <div class="modal-footer bg-dark text-light border-secondary">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-danger" id="confirmDelete">Excluir</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="/static/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Carregar dados iniciais
            loadBranchAssignments();
            loadEquipmentTypeAssignments();
            loadServiceProviders();
            loadBranches();
            loadEquipmentTypes();
            
            // Configurar eventos
            $("#saveBranchAssignment").click(saveBranchAssignment);
            $("#saveEquipmentTypeAssignment").click(saveEquipmentTypeAssignment);
            $("#confirmDelete").click(confirmDelete);
        });
        
        // Funções para carregar dados
        function loadBranchAssignments() {
            $.ajax({
                url: "/api/provider-assignments/branches",
                type: "GET",
                success: function(response) {
                    if (response.success) {
                        renderBranchAssignments(response.data);
                    } else {
                        alert("Erro ao carregar atribuições: " + response.message);
                    }
                },
                error: function(xhr) {
                    alert("Erro ao carregar atribuições: " + xhr.responseText);
                }
            });
        }
        
        function loadEquipmentTypeAssignments() {
            $.ajax({
                url: "/api/provider-assignments/equipment-types",
                type: "GET",
                success: function(response) {
                    if (response.success) {
                        renderEquipmentTypeAssignments(response.data);
                    } else {
                        alert("Erro ao carregar atribuições: " + response.message);
                    }
                },
                error: function(xhr) {
                    alert("Erro ao carregar atribuições: " + xhr.responseText);
                }
            });
        }
        
        function loadServiceProviders() {
            $.ajax({
                url: "/api/providers",
                type: "GET",
                success: function(response) {
                    if (response.success) {
                        renderServiceProviders(response.data);
                    } else {
                        alert("Erro ao carregar prestadores: " + response.message);
                    }
                },
                error: function(xhr) {
                    alert("Erro ao carregar prestadores: " + xhr.responseText);
                }
            });
        }
        
        function loadBranches() {
            $.ajax({
                url: "/api/branches",
                type: "GET",
                success: function(response) {
                    if (response.success) {
                        renderBranches(response.data);
                    } else {
                        alert("Erro ao carregar filiais: " + response.message);
                    }
                },
                error: function(xhr) {
                    alert("Erro ao carregar filiais: " + xhr.responseText);
                }
            });
        }
        
        function loadEquipmentTypes() {
            $.ajax({
                url: "/api/equipment-types",
                type: "GET",
                success: function(response) {
                    if (response.success) {
                        renderEquipmentTypes(response.data);
                    } else {
                        alert("Erro ao carregar tipos de equipamento: " + response.message);
                    }
                },
                error: function(xhr) {
                    alert("Erro ao carregar tipos de equipamento: " + xhr.responseText);
                }
            });
        }
        
        // Funções para renderizar dados
        function renderBranchAssignments(assignments) {
            var html = "";
            assignments.forEach(function(assignment) {
                html += `
                    <tr>
                        <td>${assignment.id}</td>
                        <td>${assignment.provider_name}</td>
                        <td>${assignment.branch_name}</td>
                        <td>${formatDate(assignment.created_at)}</td>
                        <td>
                            <button class="btn btn-danger btn-sm" onclick="showDeleteConfirmation(${assignment.id}, 'branch')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });
            $("#branchAssignmentsTableBody").html(html);
        }
        
        function renderEquipmentTypeAssignments(assignments) {
            var html = "";
            assignments.forEach(function(assignment) {
                html += `
                    <tr>
                        <td>${assignment.id}</td>
                        <td>${assignment.provider_name}</td>
                        <td>${assignment.equipment_type_name}</td>
                        <td>${formatDate(assignment.created_at)}</td>
                        <td>
                            <button class="btn btn-danger btn-sm" onclick="showDeleteConfirmation(${assignment.id}, 'equipment-type')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });
            $("#equipmentTypeAssignmentsTableBody").html(html);
        }
        
        function renderServiceProviders(providers) {
            var html = '<option value="">Selecione um prestador</option>';
            providers.forEach(function(provider) {
                html += `<option value="${provider.id}">${provider.name}</option>`;
            });
            $("#serviceProviderSelect").html(html);
            $("#serviceProviderSelect2").html(html);
        }
        
        function renderBranches(branches) {
            var html = '<option value="">Selecione uma filial</option>';
            branches.forEach(function(branch) {
                html += `<option value="${branch.id}">${branch.name}</option>`;
            });
            $("#branchSelect").html(html);
        }
        
        function renderEquipmentTypes(types) {
            var html = '<option value="">Selecione um tipo de equipamento</option>';
            types.forEach(function(type) {
                html += `<option value="${type.id}">${type.name}</option>`;
            });
            $("#equipmentTypeSelect").html(html);
        }
        
        // Funções para salvar dados
        function saveBranchAssignment() {
            var providerID = $("#serviceProviderSelect").val();
            var branchID = $("#branchSelect").val();
            
            if (!providerID || !branchID) {
                alert("Por favor, selecione um prestador e uma filial.");
                return;
            }
            
            $.ajax({
                url: "/api/provider-assignments/branches",
                type: "POST",
                contentType: "application/json",
                data: JSON.stringify({
                    service_provider_id: parseInt(providerID),
                    branch_id: parseInt(branchID)
                }),
                success: function(response) {
                    if (response.success) {
                        $("#addBranchAssignmentModal").modal("hide");
                        loadBranchAssignments();
                    } else {
                        alert("Erro ao salvar atribuição: " + response.message);
                    }
                },
                error: function(xhr) {
                    alert("Erro ao salvar atribuição: " + xhr.responseText);
                }
            });
        }
        
        function saveEquipmentTypeAssignment() {
            var providerID = $("#serviceProviderSelect2").val();
            var equipmentTypeID = $("#equipmentTypeSelect").val();
            
            if (!providerID || !equipmentTypeID) {
                alert("Por favor, selecione um prestador e um tipo de equipamento.");
                return;
            }
            
            $.ajax({
                url: "/api/provider-assignments/equipment-types",
                type: "POST",
                contentType: "application/json",
                data: JSON.stringify({
                    service_provider_id: parseInt(providerID),
                    equipment_type_id: parseInt(equipmentTypeID)
                }),
                success: function(response) {
                    if (response.success) {
                        $("#addEquipmentTypeAssignmentModal").modal("hide");
                        loadEquipmentTypeAssignments();
                    } else {
                        alert("Erro ao salvar atribuição: " + response.message);
                    }
                },
                error: function(xhr) {
                    alert("Erro ao salvar atribuição: " + xhr.responseText);
                }
            });
        }
        
        // Funções para excluir dados
        function showDeleteConfirmation(id, type) {
            $("#deleteItemId").val(id);
            $("#deleteItemType").val(type);
            $("#confirmDeleteModal").modal("show");
        }
        
        function confirmDelete() {
            var id = $("#deleteItemId").val();
            var type = $("#deleteItemType").val();
            
            var url = "/api/provider-assignments/" + type + "s/" + id;
            
            $.ajax({
                url: url,
                type: "DELETE",
                success: function(response) {
                    if (response.success) {
                        $("#confirmDeleteModal").modal("hide");
                        if (type === "branch") {
                            loadBranchAssignments();
                        } else {
                            loadEquipmentTypeAssignments();
                        }
                    } else {
                        alert("Erro ao excluir atribuição: " + response.message);
                    }
                },
                error: function(xhr) {
                    alert("Erro ao excluir atribuição: " + xhr.responseText);
                }
            });
        }
        
        // Função auxiliar para formatar data
        function formatDate(dateString) {
            var date = new Date(dateString);
            return date.toLocaleDateString('pt-BR') + ' ' + date.toLocaleTimeString('pt-BR');
        }
    </script>
</body>
</html>
{{end}}
