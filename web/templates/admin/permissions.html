{{ define "admin/permissions.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .title }}</title>
    <link rel="icon" href="/static/images/favicon.ico" type="image/x-icon">

    <!-- Bootstrap e estilos base -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/base_layout.css">

    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">

    <style>
        .permissions-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }

        .permissions-left-panel {
            flex: 1;
            background: rgba(30, 30, 30, 0.8);
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .permissions-right-panel {
            flex: 2;
            background: rgba(30, 30, 30, 0.8);
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .role-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .role-item {
            padding: 12px 15px;
            margin-bottom: 8px;
            background: rgba(50, 50, 50, 0.6);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .role-item:hover {
            background: rgba(60, 60, 60, 0.8);
            transform: translateX(5px);
        }

        .role-item.active {
            background: rgba(70, 70, 70, 0.9);
            border-left: 4px solid #f4b31d;
        }

        .role-name {
            font-weight: 600;
            color: #f4b31d;
            font-size: 16px;
            display: block;
        }

        .role-description {
            font-size: 12px;
            color: #aaa;
            margin-top: 4px;
        }

        .permissions-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .permissions-title {
            font-size: 20px;
            font-weight: 700;
            color: #f4b31d;
            margin: 0;
        }

        .permissions-actions {
            display: flex;
            gap: 10px;
        }

        .permissions-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .permissions-tab {
            padding: 10px 20px;
            cursor: pointer;
            color: #aaa;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
        }

        .permissions-tab:hover {
            color: #fff;
        }

        .permissions-tab.active {
            color: #f4b31d;
        }

        .permissions-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 3px;
            background: #f4b31d;
            border-radius: 3px 3px 0 0;
        }

        .permissions-content {
            display: none;
        }

        .permissions-content.active {
            display: block;
        }

        .permission-group {
            margin-bottom: 20px;
            background: rgba(40, 40, 40, 0.6);
            border-radius: 6px;
            padding: 15px;
        }

        .permission-group-title {
            font-size: 16px;
            font-weight: 600;
            color: #f4b31d;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .permission-group-title i {
            margin-right: 8px;
        }

        .permission-items {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
        }

        .permission-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: rgba(60, 60, 60, 0.6);
            border-radius: 4px;
            transition: all 0.3s ease;
            margin-bottom: 5px;
        }

        .permission-item:hover {
            background: rgba(70, 70, 70, 0.8);
        }

        .permission-checkbox {
            margin-right: 10px;
        }

        .permission-label {
            font-size: 14px;
            color: #ddd;
            cursor: pointer;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 180px;
        }

        /* Tooltip personalizado */
        [title] {
            position: relative;
        }

        [title]:hover::after {
            content: attr(title);
            position: absolute;
            bottom: 100%;
            left: 0;
            background: rgba(0, 0, 0, 0.8);
            color: #fff;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 10;
            opacity: 0;
            animation: fadeIn 0.3s forwards;
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
                transform: translateY(-5px);
            }
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .btn-shell-red {
            background: #ED1C24;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-shell-red:hover {
            background: #ff2a33;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(237, 28, 36, 0.4);
        }

        .btn-shell-light {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-shell-light:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .selected-permissions-container {
            background: rgba(40, 40, 40, 0.6);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .selected-permissions-title {
            font-size: 16px;
            font-weight: 600;
            color: #f4b31d;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .selected-permissions-title i {
            margin-right: 8px;
        }

        .selected-permissions-empty {
            color: #aaa;
            font-style: italic;
            padding: 10px;
            text-align: center;
        }

        .selected-permission-tag {
            display: inline-block;
            background: rgba(244, 179, 29, 0.2);
            color: #f4b31d;
            padding: 6px 12px;
            border-radius: 20px;
            margin: 5px;
            font-size: 12px;
            transition: all 0.3s ease;
            max-width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: default;
        }

        .selected-permission-tag:hover {
            background: rgba(244, 179, 29, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        .selected-permission-tag i {
            margin-right: 5px;
        }

        .selected-permission-tag .remove-tag {
            margin-left: 5px;
            cursor: pointer;
            opacity: 0.7;
        }

        .selected-permission-tag .remove-tag:hover {
            opacity: 1;
            color: #ff2a33;
        }

        .add-role-form {
            background: rgba(40, 40, 40, 0.6);
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #ddd;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            background: rgba(30, 30, 30, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            color: #fff;
        }

        .form-control:focus {
            outline: none;
            border-color: #f4b31d;
            box-shadow: 0 0 0 2px rgba(244, 179, 29, 0.2);
        }

        .alert {
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        .alert-success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid rgba(40, 167, 69, 0.4);
            color: #2ecc71;
        }

        .alert-danger {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid rgba(220, 53, 69, 0.4);
            color: #e74c3c;
        }

        .alert-warning {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.4);
            color: #f1c40f;
        }

        .public-resources-section {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <!-- Incluindo o menu lateral independente -->
    {{ template "sidebar" . }}

    <!-- Conteúdo principal -->
    <div class="content-with-sidebar">
        <div class="main-content">
            <div class="page-header">
                <h1 class="page-title">Gerenciamento de Permissões</h1>
                <p class="page-description">Configure as permissões de acesso para cada papel no sistema.</p>
            </div>

            <div id="alertContainer"></div>

            <div class="permissions-container">
                <div class="permissions-left-panel">
                    <div class="permissions-header">
                        <h2 class="permissions-title">Papéis</h2>
                        <div class="permissions-actions">
                            <button id="addRoleBtn" class="btn-shell-light btn-sm">
                                <i class="fas fa-plus"></i> Novo Papel
                            </button>
                        </div>
                    </div>

                    <ul class="role-list" id="roleList">
                        <!-- Os papéis serão carregados dinamicamente -->
                    </ul>

                    <div id="addRoleForm" class="add-role-form" style="display: none;">
                        <h3>Adicionar Novo Papel</h3>
                        <form id="newRoleForm">
                            <div class="form-group">
                                <label for="newRoleName">Nome do Papel</label>
                                <input type="text" id="newRoleName" name="name" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="newRoleDescription">Descrição</label>
                                <textarea id="newRoleDescription" name="description" class="form-control" rows="3"></textarea>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn-shell-red">Salvar</button>
                                <button type="button" id="cancelAddRole" class="btn-shell-light">Cancelar</button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="permissions-right-panel" id="permissionsRightPanel">
                    <div class="permissions-editor">
                        <h2 id="roleEditorTitle">Editar Papel</h2>
                        <form id="roleForm">
                            <input type="hidden" id="roleId" name="id" value="">
                            <div class="form-group">
                                <label for="roleName">Nome do Papel</label>
                                <input type="text" id="roleName" name="name" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="roleDescription">Descrição</label>
                                <textarea id="roleDescription" name="description" class="form-control" rows="3"></textarea>
                            </div>

                            <!-- Resumo de Permissões Selecionadas -->
                            <div class="selected-permissions-container">
                                <div class="selected-permissions-title">
                                    <i class="fas fa-shield-alt"></i> Permissões Selecionadas
                                </div>
                                <div id="selectedPermissionsSummary">
                                    <div class="selected-permissions-empty">
                                        Nenhuma permissão selecionada
                                    </div>
                                </div>
                            </div>

                            <div class="permissions-tabs">
                                <div class="permissions-tab active" data-tab="pages">Páginas</div>
                                <div class="permissions-tab" data-tab="apis">APIs</div>
                            </div>

                            <div class="permissions-content active" id="pagesContent">
                                <div class="permission-group">
                                    <div class="permission-group-title">
                                        <i class="fas fa-file"></i> Páginas do Sistema
                                    </div>
                                    <div class="permission-items" id="pagesPermissionItems">
                                        <!-- As permissões de páginas serão carregadas dinamicamente -->
                                    </div>
                                </div>
                            </div>

                            <div class="permissions-content" id="apisContent">
                                <div class="permission-group">
                                    <div class="permission-group-title">
                                        <i class="fas fa-code"></i> APIs do Sistema
                                    </div>
                                    <div class="permission-items" id="apisPermissionItems">
                                        <!-- As permissões de APIs serão carregadas dinamicamente -->
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn-shell-red">Salvar</button>
                                <button type="button" id="cancelButton" class="btn-shell-light">Cancelar</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Seção de Recursos Públicos -->
            <div class="public-resources-section">
                <div class="card">
                    <div class="card-header">
                        <h3>Recursos Públicos</h3>
                        <p>Configure quais recursos são acessíveis sem autenticação.</p>
                    </div>
                    <div class="card-body">
                        <form id="publicResourcesForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <h4>Páginas Públicas</h4>
                                    <div id="publicPagesContainer">
                                        <!-- As páginas públicas serão carregadas dinamicamente -->
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h4>APIs Públicas</h4>
                                    <div id="publicAPIsContainer">
                                        <!-- As APIs públicas serão carregadas dinamicamente -->
                                    </div>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn-shell-red">Salvar Recursos Públicos</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Carregar todos os papéis
            loadRoles();

            // Configurar abas de permissões
            $('.permissions-tab').click(function() {
                const tab = $(this).data('tab');
                $('.permissions-tab').removeClass('active');
                $(this).addClass('active');
                $('.permissions-content').removeClass('active');
                $(`#${tab}Content`).addClass('active');
            });

            // Mostrar formulário para adicionar papel
            $('#addRoleBtn').click(function() {
                $('#addRoleForm').show();
            });

            // Cancelar adição de papel
            $('#cancelAddRole').click(function() {
                $('#addRoleForm').hide();
                $('#newRoleForm')[0].reset();
            });

            // Cancelar edição de papel
            $('#cancelButton').click(function() {
                resetRoleForm();
            });

            // Formulário para adicionar novo papel
            $('#newRoleForm').submit(function(e) {
                e.preventDefault();
                const roleName = $('#newRoleName').val();
                const roleDescription = $('#newRoleDescription').val();

                // Aqui você enviaria os dados para o servidor
                // Por enquanto, vamos apenas simular
                showAlert('success', `Papel "${roleName}" adicionado com sucesso!`);
                $('#addRoleForm').hide();
                $('#newRoleForm')[0].reset();
                loadRoles(); // Recarregar a lista de papéis
            });

            // Formulário para editar papel
            $('#roleForm').submit(function(e) {
                e.preventDefault();
                const roleId = $('#roleId').val();
                const roleName = $('#roleName').val();

                // Coletar permissões selecionadas
                const selectedPages = [];
                $('.page-permission:checked').each(function() {
                    selectedPages.push($(this).val());
                });

                const selectedAPIs = [];
                $('.api-permission:checked').each(function() {
                    selectedAPIs.push($(this).val());
                });

                // Preparar dados para enviar
                const data = {
                    role: roleId,
                    permissions: {
                        pages: selectedPages,
                        apis: selectedAPIs
                    }
                };

                // Enviar para o servidor
                $.ajax({
                    url: '/api/permissions/role',
                    method: 'PUT',
                    contentType: 'application/json',
                    data: JSON.stringify(data),
                    success: function(response) {
                        showAlert('success', `Permissões do papel "${roleName}" atualizadas com sucesso!`);
                    },
                    error: function(xhr) {
                        showAlert('danger', `Erro ao atualizar permissões: ${xhr.responseJSON ? xhr.responseJSON.error : 'Erro desconhecido'}`);
                    }
                });
            });

            // Formulário para recursos públicos
            $('#publicResourcesForm').submit(function(e) {
                e.preventDefault();

                // Coletar recursos públicos selecionados
                const publicPages = [];
                $('.public-page:checked').each(function() {
                    publicPages.push($(this).val());
                });

                const publicAPIs = [];
                $('.public-api:checked').each(function() {
                    publicAPIs.push($(this).val());
                });

                // Preparar dados para enviar
                const data = {
                    publicPages: publicPages,
                    publicAPIs: publicAPIs
                };

                // Enviar para o servidor
                $.ajax({
                    url: '/api/permissions/public',
                    method: 'PUT',
                    contentType: 'application/json',
                    data: JSON.stringify(data),
                    success: function(response) {
                        showAlert('success', 'Recursos públicos atualizados com sucesso!');
                    },
                    error: function(xhr) {
                        showAlert('danger', `Erro ao atualizar recursos públicos: ${xhr.responseJSON ? xhr.responseJSON.error : 'Erro desconhecido'}`);
                    }
                });
            });
        });

        // Função para carregar todos os papéis
        function loadRoles() {
            $.ajax({
                url: '/api/permissions',
                method: 'GET',
                success: function(response) {
                    renderRoles(response);
                    loadAllPermissions(response);
                },
                error: function(xhr) {
                    showAlert('danger', `Erro ao carregar papéis: ${xhr.responseJSON ? xhr.responseJSON.error : 'Erro desconhecido'}`);
                }
            });
        }

        // Função para renderizar a lista de papéis
        function renderRoles(config) {
            const roleList = $('#roleList');
            roleList.empty();

            // Adicionar cada papel à lista
            for (const role in config.Roles) {
                const roleConfig = config.Roles[role];
                const roleItem = $(`
                    <li class="role-item" data-role="${role}">
                        <span class="role-name">${role}</span>
                        <span class="role-description">${roleConfig.Description || 'Sem descrição'}</span>
                    </li>
                `);

                roleItem.click(function() {
                    loadRolePermissions(role);
                });

                roleList.append(roleItem);
            }
        }

        // Função para carregar todas as permissões disponíveis
        function loadAllPermissions(config) {
            const pagesContainer = $('#pagesPermissionItems');
            const apisContainer = $('#apisPermissionItems');
            const publicPagesContainer = $('#publicPagesContainer');
            const publicAPIsContainer = $('#publicAPIsContainer');

            pagesContainer.empty();
            apisContainer.empty();
            publicPagesContainer.empty();
            publicAPIsContainer.empty();

            // Coletar todas as páginas e APIs únicas
            const allPages = new Set();
            const allAPIs = new Set();

            // Adicionar páginas e APIs de cada papel
            for (const role in config.Roles) {
                const roleConfig = config.Roles[role];

                if (roleConfig.Pages) {
                    roleConfig.Pages.forEach(page => allPages.add(page));
                }

                if (roleConfig.APIs) {
                    roleConfig.APIs.forEach(api => allAPIs.add(api));
                }
            }

            // Função para obter nome amigável para páginas
            function getPageFriendlyName(page) {
                const pageMappings = {
                    // Páginas principais
                    "dashboard": "Dashboard Principal",
                    "dashboard-enhanced": "Dashboard Avançado",
                    "dashboard-calendario": "Calendário do Dashboard",
                    "dashboard/dashboard": "Visão Geral do Dashboard",
                    "dashboard/dashboard_new": "Novo Dashboard",
                    "dashboard/orders": "Ordens no Dashboard",
                    "dashboard/approval": "Aprovações Pendentes",

                    // Calendário e Manutenção
                    "calendario": "Calendário",
                    "calendario-flip": "Calendário Flip",
                    "calendario-avancado": "Calendário Avançado",
                    "calendario/stats": "Estatísticas do Calendário",
                    "manutencao": "Manutenção",
                    "nova-manutencao": "Nova Manutenção",
                    "maintenance/new": "Criar Manutenção",
                    "maintenance/edit": "Editar Manutenção",
                    "maintenance/view": "Visualizar Manutenção",

                    // Ordens
                    "orders": "Ordens de Serviço",
                    "ordens": "Ordens",
                    "ordens/create": "Criar Ordem",
                    "ordens/:id/edit": "Editar Ordem",
                    "ordens/calendar": "Calendário de Ordens",

                    // Técnico
                    "ordemtecnica": "Ordem Técnica",
                    "tecnico/Ordemtecnico": "Ordem do Técnico",
                    "tecnico/ManutencaoOrdem": "Manutenção de Ordem",

                    // Conta e Perfil
                    "minha-conta": "Minha Conta",
                    "editar-perfil": "Editar Perfil",
                    "minha-conta/alterar-senha": "Alterar Senha",
                    "security-settings": "Configurações de Segurança",
                    "change-password": "Mudar Senha",
                    "meuperfil": "Meu Perfil",

                    // Financeiro
                    "financeiro": "Financeiro",
                    "finance/dashboard": "Dashboard Financeiro",
                    "financeiro/painel": "Painel Financeiro",
                    "financeiro/calculadora-impostos": "Calculadora de Impostos",
                    "financeiro/calculadora-margem": "Calculadora de Margem",
                    "financeiro/cotacoes-new": "Novas Cotações",
                    "financeiro/consulta-impostos": "Consulta de Impostos",
                    "financeiro/consulta-impostos-new": "Nova Consulta de Impostos",

                    // Relatórios e Galeria
                    "relatorios": "Relatórios",
                    "relatorios/reports": "Relatórios Detalhados",
                    "galeria": "Galeria",
                    "galeria/galeria": "Visualização da Galeria",
                    "galeria/galeria_new": "Nova Galeria",

                    // Postos e Filiais
                    "postos": "Postos de Combustível",

                    // Configurações e Admin
                    "settings": "Configurações",
                    "admin/permissions": "Gerenciamento de Permissões",

                    // Autenticação
                    "login": "Login",
                    "logout": "Logout",
                    "acesso_negado": "Acesso Negado"
                };

                // Verificar se existe um mapeamento para esta página
                if (pageMappings[page]) {
                    return pageMappings[page];
                }

                // Para páginas com wildcard (*), retornar um nome especial
                if (page === "*") {
                    return "Todas as Páginas";
                }

                // Se não encontrar um mapeamento específico, retornar a própria página com formatação melhorada
                return page.replace(/\//g, ' › ').replace(/[-_]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            }

            // Renderizar permissões de páginas
            allPages.forEach(page => {
                const isPublic = config.PublicPages && config.PublicPages.includes(page);
                const safeId = page.replace(/[\/\*:]/g, '_');
                const friendlyName = getPageFriendlyName(page);

                // Para seleção de permissões
                pagesContainer.append(`
                    <div class="permission-item">
                        <input type="checkbox" id="page_${safeId}" class="permission-checkbox page-permission" value="${page}">
                        <label for="page_${safeId}" class="permission-label" title="${page}">${friendlyName}</label>
                    </div>
                `);

                // Para recursos públicos
                publicPagesContainer.append(`
                    <div class="form-check">
                        <input type="checkbox" id="public_page_${safeId}" class="form-check-input public-page" value="${page}" ${isPublic ? 'checked' : ''}>
                        <label for="public_page_${safeId}" class="form-check-label" title="${page}">${friendlyName}</label>
                    </div>
                `);
            });

            // Função para obter nome amigável para APIs
            function getAPIFriendlyName(api) {
                const apiMappings = {
                    // Autenticação e Usuário
                    "api/auth/login": "Login de Usuário",
                    "api/auth/logout": "Logout de Usuário",
                    "api/auth/me": "Dados do Usuário Atual",
                    "api/auth/2fa/setup": "Configurar Autenticação 2FA",
                    "api/auth/2fa/enable": "Ativar Autenticação 2FA",
                    "api/auth/2fa/disable": "Desativar Autenticação 2FA",
                    "api/user/me": "Perfil do Usuário",
                    "api/user/profile": "Atualizar Perfil",
                    "api/user/change-password": "Alterar Senha",

                    // Equipamentos
                    "api/equipments": "Listar Equipamentos",
                    "api/equipments/:id": "Detalhes do Equipamento",
                    "api/equipments/types": "Tipos de Equipamentos",
                    "api/equipments/filial/:id": "Equipamentos por Filial",

                    // Transferências de Equipamentos
                    "api/equipment-transfers": "Transferências de Equipamentos",
                    "api/equipment-transfers/:id": "Detalhes da Transferência",
                    "api/equipment-transfers/equipment/:equipmentId": "Transferências por Equipamento",
                    "api/equipment-transfers/branch/:branchId": "Transferências por Filial",
                    "api/equipment-transfers/:id/approve": "Aprovar Transferência",
                    "api/equipment-transfers/:id/reject": "Rejeitar Transferência",
                    "api/equipment-transfers/:id/cancel": "Cancelar Transferência",

                    // Manutenção
                    "api/maintenance": "Ordens de Manutenção",
                    "api/maintenance/:id": "Detalhes da Manutenção",
                    "api/maintenance-orders": "Listar Ordens de Manutenção",
                    "api/maintenance-orders/metrics": "Métricas de Manutenção",

                    // Ordens de Serviço
                    "api/orders": "Ordens de Serviço",
                    "api/orders/:id": "Detalhes da Ordem",
                    "api/orders/:id/status": "Atualizar Status da Ordem",
                    "api/orders/:id/assign": "Atribuir Prestador",
                    "api/orders/:id/costs": "Custos da Ordem",
                    "api/orders/:id/interactions": "Interações da Ordem",

                    // Dashboard e Relatórios
                    "api/dashboard/metrics": "Métricas do Dashboard",
                    "api/dashboard/recent": "Atividades Recentes",
                    "api/dashboard/status": "Status do Sistema",
                    "api/reports": "Relatórios",
                    "api/reports/download": "Download de Relatórios",

                    // Financeiro
                    "api/financial/summary": "Resumo Financeiro",
                    "api/financial/transactions": "Transações Financeiras",

                    // Notificações
                    "api/notifications/me": "Minhas Notificações",
                    "api/notifications/unread-count": "Contagem de Não Lidas",
                    "api/notifications/:id/read": "Marcar Como Lida",
                    "api/notifications/mark-all-read": "Marcar Todas Como Lidas",
                    "api/notifications/settings": "Configurações de Notificações",
                    "api/notifications/subscribe": "Inscrever em Notificações",

                    // Filiais e Postos
                    "api/branches": "Listar Filiais",
                    "api/branches/:id": "Detalhes da Filial",
                    "api/branches/:id/stations": "Postos da Filial",
                    "api/branches/:id/filiais": "Sub-Filiais",
                    "api/stations": "Postos de Combustível",

                    // Permissões
                    "api/permissions": "Configurações de Permissões",
                    "api/permissions/role": "Permissões por Papel",
                    "api/permissions/role/:role": "Detalhes de Papel",
                    "api/permissions/public": "Recursos Públicos",
                    "api/permissions/test": "Testar Permissão",
                    "api/permissions/reload": "Recarregar Permissões",

                    // WebSockets
                    "ws": "Conexão WebSocket"
                };

                // Verificar se existe um mapeamento para esta API
                if (apiMappings[api]) {
                    return apiMappings[api];
                }

                // Para APIs com wildcard (*), tentar encontrar um padrão
                if (api === "*") {
                    return "Todas as APIs";
                }

                // Se não encontrar um mapeamento específico, retornar a própria API com formatação melhorada
                return api.replace(/^api\//, '').replace(/\//g, ' › ').replace(/:/g, ' ');
            }

            // Renderizar permissões de APIs
            allAPIs.forEach(api => {
                const isPublic = config.PublicAPIs && config.PublicAPIs.includes(api);
                const safeId = api.replace(/[\/\*:]/g, '_');
                const friendlyName = getAPIFriendlyName(api);

                // Para seleção de permissões
                apisContainer.append(`
                    <div class="permission-item">
                        <input type="checkbox" id="api_${safeId}" class="permission-checkbox api-permission" value="${api}">
                        <label for="api_${safeId}" class="permission-label" title="${api}">${friendlyName}</label>
                    </div>
                `);

                // Para recursos públicos
                publicAPIsContainer.append(`
                    <div class="form-check">
                        <input type="checkbox" id="public_api_${safeId}" class="form-check-input public-api" value="${api}" ${isPublic ? 'checked' : ''}>
                        <label for="public_api_${safeId}" class="form-check-label" title="${api}">${friendlyName}</label>
                    </div>
                `);
            });
        }

        // Função para carregar permissões de um papel específico
        function loadRolePermissions(role) {
            // Obter a configuração completa e extrair as informações do papel
            $.ajax({
                url: '/api/permissions',
                method: 'GET',
                success: function(config) {
                    if (config && config.Roles && config.Roles[role]) {
                        renderRolePermissions(role, config.Roles[role]);
                    } else {
                        showAlert('danger', `Papel não encontrado: ${role}`);
                    }
                },
                error: function(xhr) {
                    showAlert('danger', `Erro ao carregar permissões: ${xhr.responseJSON ? xhr.responseJSON.error : 'Erro desconhecido'}`);
                }
            });
        }

        // Função para renderizar permissões de um papel
        function renderRolePermissions(role, roleConfig) {
            console.log('Renderizando permissões para o papel:', role);
            console.log('Configuração do papel:', roleConfig);

            // Atualizar formulário
            $('#roleId').val(role);
            $('#roleName').val(role);
            $('#roleDescription').val(roleConfig.Description || '');
            $('#roleEditorTitle').text(`Editar Papel: ${role}`);

            // Destacar papel selecionado
            $('.role-item').removeClass('active');
            $(`.role-item[data-role="${role}"]`).addClass('active');

            // Limpar todas as seleções
            $('.page-permission, .api-permission').prop('checked', false);

            // Marcar permissões de páginas
            if (roleConfig.Pages) {
                console.log('Páginas do papel:', roleConfig.Pages);
                roleConfig.Pages.forEach(page => {
                    const selector = `#page_${page.replace(/[\/\*:]/g, '_')}`;
                    console.log('Seletor de página:', selector);
                    $(selector).prop('checked', true);
                });
            }

            // Marcar permissões de APIs
            if (roleConfig.APIs) {
                console.log('APIs do papel:', roleConfig.APIs);
                roleConfig.APIs.forEach(api => {
                    const selector = `#api_${api.replace(/[\/\*:]/g, '_')}`;
                    console.log('Seletor de API:', selector);
                    $(selector).prop('checked', true);
                });
            }

            // Atualizar resumo de permissões
            updatePermissionsSummary(roleConfig);
        }

        // Função para atualizar o resumo de permissões selecionadas
        function updatePermissionsSummary(roleConfig) {
            const summaryContainer = $('#selectedPermissionsSummary');
            summaryContainer.empty();

            let hasPermissions = false;

            // Adicionar páginas
            if (roleConfig.Pages && roleConfig.Pages.length > 0) {
                hasPermissions = true;
                roleConfig.Pages.forEach(page => {
                    const friendlyName = getPageFriendlyName(page);
                    summaryContainer.append(`
                        <span class="selected-permission-tag" title="${page}">
                            <i class="fas fa-file"></i> ${friendlyName}
                        </span>
                    `);
                });
            }

            // Adicionar APIs
            if (roleConfig.APIs && roleConfig.APIs.length > 0) {
                hasPermissions = true;
                roleConfig.APIs.forEach(api => {
                    const friendlyName = getAPIFriendlyName(api);
                    summaryContainer.append(`
                        <span class="selected-permission-tag" title="${api}">
                            <i class="fas fa-code"></i> ${friendlyName}
                        </span>
                    `);
                });
            }

            // Se não houver permissões, mostrar mensagem
            if (!hasPermissions) {
                summaryContainer.html('<div class="selected-permissions-empty">Nenhuma permissão selecionada</div>');
            }
        }

        // Função para resetar o formulário de edição de papel
        function resetRoleForm() {
            $('#roleForm')[0].reset();
            $('#roleId').val('');
            $('#roleEditorTitle').text('Editar Papel');
            $('.role-item').removeClass('active');
            $('.page-permission, .api-permission').prop('checked', false);
            $('#selectedPermissionsSummary').html('<div class="selected-permissions-empty">Nenhuma permissão selecionada</div>');
        }

        // Função para mostrar alertas
        function showAlert(type, message) {
            const alertContainer = $('#alertContainer');
            const alert = $(`
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
                </div>
            `);

            alertContainer.append(alert);

            // Auto-fechar após 5 segundos
            setTimeout(() => {
                alert.alert('close');
            }, 5000);
        }
    </script>
</body>
</html>
{{ end }}
