{{ define "exemplos/exemplo-page.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Página de Exemplo - Rede Tradição Shell</title>
    
    <!-- CSS Base -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&family=Share+Tech+Mono&display=swap" rel="stylesheet">
    
    <!-- CSS Custom -->
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/exemplo-page.css">
</head>
<body>
    <!-- Sidebar -->
    {{ template "layouts/sidebar.html" . }}
    
    <!-- Conteúdo Principal -->
    <div class="content-with-sidebar">
        <div class="main-content">
            <!-- Cabeçalho da Página -->
            <div class="page-header">
                <h1><i class="fas fa-flask me-2"></i>Página de Exemplo</h1>
                <p class="text-muted">Um modelo completo seguindo o design system Shell</p>
            </div>
            
            <!-- Conteúdo da Página -->
            <div class="page-content">
                <!-- Seção: Cards Informativos -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="stats-card fade-in">
                            <div class="stats-card-title">Total de Visitas</div>
                            <div class="stats-card-value">1,254</div>
                            <div class="stats-card-trend">
                                <i class="fas fa-arrow-up trend-up"></i> 5% desde ontem
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="stats-card fade-in" style="animation-delay: 0.1s;">
                            <div class="stats-card-title">Interações</div>
                            <div class="stats-card-value">342</div>
                            <div class="stats-card-trend">
                                <i class="fas fa-arrow-up trend-up"></i> 12% desde ontem
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="stats-card fade-in" style="animation-delay: 0.2s;">
                            <div class="stats-card-title">Taxa de Conversão</div>
                            <div class="stats-card-value">8.7%</div>
                            <div class="stats-card-trend">
                                <i class="fas fa-arrow-down trend-down"></i> 2% desde ontem
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Seção: Conteúdo Principal -->
                <div class="row">
                    <!-- Coluna da Esquerda -->
                    <div class="col-md-8">
                        <!-- Card com Tabela -->
                        <div class="card-shell mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h3 class="m-0">Dados Recentes</h3>
                                <div>
                                    <button class="btn-shell-yellow">
                                        <i class="fas fa-download me-2"></i>Exportar
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table-shell">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Nome</th>
                                                <th>Status</th>
                                                <th>Data</th>
                                                <th>Ações</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>001</td>
                                                <td>Item 1</td>
                                                <td><span class="badge bg-success">Ativo</span></td>
                                                <td>12/01/2023</td>
                                                <td>
                                                    <button class="btn-sm btn-shell-red">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>002</td>
                                                <td>Item 2</td>
                                                <td><span class="badge bg-warning">Pendente</span></td>
                                                <td>14/01/2023</td>
                                                <td>
                                                    <button class="btn-sm btn-shell-red">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>003</td>
                                                <td>Item 3</td>
                                                <td><span class="badge bg-danger">Inativo</span></td>
                                                <td>15/01/2023</td>
                                                <td>
                                                    <button class="btn-sm btn-shell-red">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Card com Gráfico -->
                        <div class="card-shell">
                            <div class="card-header">
                                <h3 class="m-0">Análise de Dados</h3>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="analyticsChart" height="250"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Coluna da Direita -->
                    <div class="col-md-4">
                        <!-- Card de Formulário -->
                        <div class="card-shell mb-4">
                            <div class="card-header">
                                <h3 class="m-0">Filtros</h3>
                            </div>
                            <div class="card-body">
                                <form class="form-shell">
                                    <div class="form-group">
                                        <label for="dataInicio">Data Inicial</label>
                                        <input type="date" id="dataInicio" class="form-control">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="dataFim">Data Final</label>
                                        <input type="date" id="dataFim" class="form-control">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="status">Status</label>
                                        <select id="status" class="form-control">
                                            <option value="">Todos</option>
                                            <option value="ativo">Ativo</option>
                                            <option value="pendente">Pendente</option>
                                            <option value="inativo">Inativo</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <button type="submit" class="btn-shell-red w-100">
                                            <i class="fas fa-search me-2"></i>Filtrar
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- Card de Notificações -->
                        <div class="card-shell">
                            <div class="card-header">
                                <h3 class="m-0">Notificações Recentes</h3>
                            </div>
                            <div class="card-body p-0">
                                <ul class="notifications-list">
                                    <li class="notification-item">
                                        <div class="notification-icon bg-primary">
                                            <i class="fas fa-bell"></i>
                                        </div>
                                        <div class="notification-content">
                                            <div class="notification-title">Novo alerta</div>
                                            <div class="notification-text">Você tem uma nova mensagem</div>
                                            <div class="notification-time">Há 5 minutos</div>
                                        </div>
                                    </li>
                                    <li class="notification-item">
                                        <div class="notification-icon bg-success">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="notification-content">
                                            <div class="notification-title">Tarefa concluída</div>
                                            <div class="notification-text">Relatório mensal finalizado</div>
                                            <div class="notification-time">Há 2 horas</div>
                                        </div>
                                    </li>
                                    <li class="notification-item">
                                        <div class="notification-icon bg-warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </div>
                                        <div class="notification-content">
                                            <div class="notification-title">Aviso</div>
                                            <div class="notification-text">Espaço em disco abaixo de 20%</div>
                                            <div class="notification-time">Há 1 dia</div>
                                        </div>
                                    </li>
                                </ul>
                                <div class="text-center p-3">
                                    <a href="#" class="btn-link">Ver todas as notificações</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts Base -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Scripts Custom -->
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/exemplo-page.js"></script>
    
    <!-- Script Específico da Página -->
    <script>
        // Inicialização do Gráfico
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('analyticsChart').getContext('2d');
            
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
                    datasets: [{
                        label: 'Dados 2023',
                        data: [12, 19, 3, 5, 2, 3],
                        borderColor: '#ED1C24',
                        backgroundColor: 'rgba(237, 28, 36, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Dados 2022',
                        data: [5, 12, 7, 8, 6, 9],
                        borderColor: '#FDB813',
                        backgroundColor: 'rgba(253, 184, 19, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
{{ end }} 