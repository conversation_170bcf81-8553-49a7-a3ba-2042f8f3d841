{{ define "relatorios/reports.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatórios - Shell</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&family=Share+Tech+Mono&display=swap" rel="stylesheet">
    <link href="/static/css/common.css" rel="stylesheet">
    <link href="/static/css/styles.css" rel="stylesheet">
    <link href="/static/css/reports.css" rel="stylesheet">
</head>
<body class="shell-theme">
    {{ template "sidebar" . }}
    <div class="content-with-sidebar">
        <div class="main-content">
            <!-- Cabeçalho da página com estilo diferente -->
            <div class="reports-header">
                <div class="reports-title-container">
                    <h1 class="reports-title">
                        <i class="fas fa-chart-line"></i> Central de Relatórios
                    </h1>
                    <div class="reports-subtitle">Análise de desempenho e métricas</div>
                </div>
                <div class="reports-actions">
                    <div class="date-range-selector">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                            <input type="text" class="form-control" id="dateRangePicker" placeholder="Selecione o período">
                        </div>
                    </div>
                    <button class="btn-shell-red ms-2" id="generateReportBtn">
                        <i class="fas fa-file-export"></i> Exportar
                    </button>
                </div>
            </div>
            
            <!-- Área de filtros específica para relatórios -->
            <div class="reports-filter-panel">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Tipo de Relatório</label>
                            <select class="form-control shell-select" id="reportTypeFilter">
                                <option value="performance">Desempenho</option>
                                <option value="financial">Financeiro</option>
                                <option value="maintenance">Manutenção</option>
                                <option value="operations">Operações</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Filial</label>
                            <select class="form-control shell-select" id="branchFilter">
                                <option value="all">Todas</option>
                                <option value="1">São Paulo</option>
                                <option value="2">Rio de Janeiro</option>
                                <option value="3">Belo Horizonte</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Agrupar por</label>
                            <select class="form-control shell-select" id="groupByFilter">
                                <option value="day">Dia</option>
                                <option value="week">Semana</option>
                                <option value="month">Mês</option>
                                <option value="quarter">Trimestre</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Visualização</label>
                            <div class="btn-group w-100 shell-btn-group" role="group">
                                <button type="button" class="btn btn-shell-yellow active" data-view="chart">
                                    <i class="fas fa-chart-bar"></i>
                                </button>
                                <button type="button" class="btn btn-shell-yellow" data-view="table">
                                    <i class="fas fa-table"></i>
                                </button>
                                <button type="button" class="btn btn-shell-yellow" data-view="cards">
                                    <i class="fas fa-th-large"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Indicadores principais - Layout específico para relatórios -->
            <div class="reports-kpi-container">
                <div class="row">
                    <div class="col-md-3">
                        <div class="kpi-card shell-card kpi-revenue">
                            <div class="kpi-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="kpi-content">
                                <div class="kpi-value">R$ 1.245.678</div>
                                <div class="kpi-label">Receita Total</div>
                                <div class="kpi-trend positive">
                                    <i class="fas fa-arrow-up"></i> 12.5%
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="kpi-card shell-card kpi-orders">
                            <div class="kpi-icon">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="kpi-content">
                                <div class="kpi-value">1.876</div>
                                <div class="kpi-label">Ordens Concluídas</div>
                                <div class="kpi-trend positive">
                                    <i class="fas fa-arrow-up"></i> 8.3%
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="kpi-card shell-card kpi-time">
                            <div class="kpi-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="kpi-content">
                                <div class="kpi-value">4.2h</div>
                                <div class="kpi-label">Tempo Médio</div>
                                <div class="kpi-trend negative">
                                    <i class="fas fa-arrow-up"></i> 2.1%
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="kpi-card shell-card kpi-satisfaction">
                            <div class="kpi-icon">
                                <i class="fas fa-smile"></i>
                            </div>
                            <div class="kpi-content">
                                <div class="kpi-value">94.7%</div>
                                <div class="kpi-label">Satisfação</div>
                                <div class="kpi-trend positive">
                                    <i class="fas fa-arrow-up"></i> 1.2%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Área de gráficos - Layout específico para relatórios -->
            <div class="reports-charts-container">
                <div class="row">
                    <div class="col-md-8">
                        <div class="chart-card shell-card">
                            <div class="chart-header shell-card-header">
                                <h3 class="chart-title shell-title">Desempenho por Período</h3>
                                <div class="chart-actions">
                                    <button class="btn btn-sm btn-shell-yellow">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-sm btn-shell-yellow">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <div class="chart-placeholder" id="performanceChart">
                                    <div class="chart-loading">
                                        <div class="shell-spinner"></div>
                                        <div class="loading-text">Carregando gráfico...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="chart-card shell-card">
                            <div class="chart-header shell-card-header">
                                <h3 class="chart-title shell-title">Distribuição por Status</h3>
                                <div class="chart-actions">
                                    <button class="btn btn-sm btn-shell-yellow">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <div class="chart-placeholder" id="statusDistributionChart">
                                    <div class="chart-loading">
                                        <div class="shell-spinner"></div>
                                        <div class="loading-text">Carregando gráfico...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="chart-card shell-card">
                            <div class="chart-header shell-card-header">
                                <h3 class="chart-title shell-title">Desempenho por Filial</h3>
                                <div class="chart-actions">
                                    <button class="btn btn-sm btn-shell-yellow">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <div class="chart-placeholder" id="branchPerformanceChart">
                                    <div class="chart-loading">
                                        <div class="shell-spinner"></div>
                                        <div class="loading-text">Carregando gráfico...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-card shell-card">
                            <div class="chart-header shell-card-header">
                                <h3 class="chart-title shell-title">Tendências de Manutenção</h3>
                                <div class="chart-actions">
                                    <button class="btn btn-sm btn-shell-yellow">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <div class="chart-placeholder" id="maintenanceTrendsChart">
                                    <div class="chart-loading">
                                        <div class="shell-spinner"></div>
                                        <div class="loading-text">Carregando gráfico...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Tabela de dados detalhados - Específica para relatórios -->
            <div class="reports-data-table-container mt-4">
                <div class="data-table-header">
                    <h3 class="data-table-title">Dados Detalhados</h3>
                    <div class="data-table-actions">
                        <button class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-filter"></i> Filtrar
                        </button>
                        <button class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-download"></i> Exportar
                        </button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table-shell">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Filial</th>
                                <th>Tipo</th>
                                <th>Data</th>
                                <th>Duração</th>
                                <th>Custo</th>
                                <th>Status</th>
                                <th>Satisfação</th>
                            </tr>
                        </thead>
                        <tbody id="reportsTableBody">
                            <tr>
                                <td>1001</td>
                                <td>São Paulo</td>
                                <td>Preventiva</td>
                                <td>10/04/2023</td>
                                <td>3.5h</td>
                                <td>R$ 1.250,00</td>
                                <td><span class="badge-shell-red">Concluído</span></td>
                                <td>95%</td>
                            </tr>
                            <tr>
                                <td>1002</td>
                                <td>Rio de Janeiro</td>
                                <td>Corretiva</td>
                                <td>12/04/2023</td>
                                <td>5.2h</td>
                                <td>R$ 2.150,00</td>
                                <td><span class="badge-shell">Em Progresso</span></td>
                                <td>--</td>
                            </tr>
                            <tr>
                                <td>1003</td>
                                <td>Belo Horizonte</td>
                                <td>Emergencial</td>
                                <td>15/04/2023</td>
                                <td>2.1h</td>
                                <td>R$ 3.450,00</td>
                                <td><span class="badge-shell-red">Concluído</span></td>
                                <td>92%</td>
                            </tr>
                            <tr>
                                <td>1004</td>
                                <td>São Paulo</td>
                                <td>Preventiva</td>
                                <td>18/04/2023</td>
                                <td>4.0h</td>
                                <td>R$ 1.850,00</td>
                                <td><span class="badge-shell-red">Concluído</span></td>
                                <td>97%</td>
                            </tr>
                            <tr>
                                <td>1005</td>
                                <td>Rio de Janeiro</td>
                                <td>Corretiva</td>
                                <td>20/04/2023</td>
                                <td>6.5h</td>
                                <td>R$ 4.250,00</td>
                                <td><span class="badge-shell-red">Concluído</span></td>
                                <td>89%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="data-table-pagination">
                    <div class="pagination-info">Mostrando 1-5 de 125 registros</div>
                    <div class="pagination-controls">
                        <button class="btn btn-sm btn-outline-secondary" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary active">1</button>
                        <button class="btn btn-sm btn-outline-secondary">2</button>
                        <button class="btn btn-sm btn-outline-secondary">3</button>
                        <button class="btn btn-sm btn-outline-secondary">...</button>
                        <button class="btn btn-sm btn-outline-secondary">25</button>
                        <button class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts comuns -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/reports.js"></script>
</body>
</html>
{{ end }}
