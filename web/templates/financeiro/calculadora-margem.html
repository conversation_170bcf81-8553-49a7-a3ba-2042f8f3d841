{{ define "financeiro/calculadora-margem.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculadora de Margem - Shell</title>
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <link rel="stylesheet" href="/static/css/fontawesome.min.css">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/calculadora-margem.css">
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&family=Share+Tech+Mono&display=swap" rel="stylesheet">
</head>
<body>
    <div class="content-with-sidebar">
        {{ template "sidebar" . }}
        <div class="main-content">
            <div class="page-header">
                <h1><i class="fas fa-calculator"></i> Calculadora de Margem</h1>
                <p>Calcule margens de lucro com base em variáveis personalizáveis</p>
            </div>
            
            <div class="page-content">
                <div class="calculadora-container">
                    <!-- Painel de configuração -->
                    <div class="config-panel">
                        <div class="panel-header">
                            <h3>Configurações</h3>
                            <button id="btn-salvar-config" class="shell-btn shell-btn-yellow">
                                <i class="fas fa-save"></i> Salvar Configuração
                            </button>
                        </div>
                        
                        <div class="componentes-disponiveis">
                            <h4>Componentes Disponíveis</h4>
                            <div class="componentes-lista">
                                <div class="componente-item" draggable="true" data-tipo="custo-fixo">
                                    <i class="fas fa-dollar-sign"></i>
                                    <span>Custo Fixo</span>
                                </div>
                                <div class="componente-item" draggable="true" data-tipo="custo-variavel">
                                    <i class="fas fa-percentage"></i>
                                    <span>Custo Variável</span>
                                </div>
                                <div class="componente-item" draggable="true" data-tipo="imposto">
                                    <i class="fas fa-file-invoice-dollar"></i>
                                    <span>Imposto</span>
                                </div>
                                <div class="componente-item" draggable="true" data-tipo="transporte">
                                    <i class="fas fa-truck"></i>
                                    <span>Transporte</span>
                                </div>
                                <div class="componente-item" draggable="true" data-tipo="combustivel">
                                    <i class="fas fa-gas-pump"></i>
                                    <span>Combustível</span>
                                </div>
                                <div class="componente-item" draggable="true" data-tipo="formula">
                                    <i class="fas fa-function"></i>
                                    <span>Fórmula Personalizada</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="configuracoes-salvas">
                            <h4>Configurações Salvas</h4>
                            <div class="configs-lista" id="configs-salvas">
                                <div class="config-item">
                                    <span>Configuração Padrão</span>
                                    <div class="config-actions">
                                        <button class="btn-icon"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon"><i class="fas fa-trash"></i></button>
                                    </div>
                                </div>
                                <div class="config-item">
                                    <span>Etanol Hidratado</span>
                                    <div class="config-actions">
                                        <button class="btn-icon"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon"><i class="fas fa-trash"></i></button>
                                    </div>
                                </div>
                                <div class="config-item">
                                    <span>Diesel S-10</span>
                                    <div class="config-actions">
                                        <button class="btn-icon"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon"><i class="fas fa-trash"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Calculadora principal -->
                    <div class="calculadora-principal">
                        <div class="calculadora-header">
                            <div class="combustivel-selector">
                                <label for="tipo-combustivel">Tipo de Combustível</label>
                                <select id="tipo-combustivel" class="form-select">
                                    <option value="gasolina">Gasolina Comum</option>
                                    <option value="gasolina-aditivada">Gasolina Aditivada</option>
                                    <option value="etanol">Etanol Hidratado</option>
                                    <option value="diesel-s500">Diesel S-500</option>
                                    <option value="diesel-s10">Diesel S-10</option>
                                </select>
                            </div>
                            
                            <div class="estado-selector">
                                <label for="estado">Estado</label>
                                <select id="estado" class="form-select">
                                    <option value="SP">São Paulo (SP)</option>
                                    <option value="RJ">Rio de Janeiro (RJ)</option>
                                    <option value="MG">Minas Gerais (MG)</option>
                                    <option value="PR">Paraná (PR)</option>
                                    <option value="SC">Santa Catarina (SC)</option>
                                    <option value="RS">Rio Grande do Sul (RS)</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="valores-principais">
                            <div class="valor-item">
                                <label for="preco-compra">Preço de Compra (R$/L)</label>
                                <input type="number" id="preco-compra" class="form-control" step="0.01" value="4.25">
                            </div>
                            
                            <div class="valor-item">
                                <label for="preco-venda">Preço de Venda (R$/L)</label>
                                <input type="number" id="preco-venda" class="form-control" step="0.01" value="6.79">
                            </div>
                            
                            <div class="valor-item">
                                <label for="volume-mensal">Volume Mensal (L)</label>
                                <input type="number" id="volume-mensal" class="form-control" step="1" value="30000">
                            </div>
                        </div>
                        
                        <div class="componentes-adicionados">
                            <h4>Componentes de Custo</h4>
                            <p class="instrucao">Arraste componentes da esquerda e solte aqui para adicionar</p>
                            
                            <div class="drop-zone" id="componentes-drop-zone">
                                <!-- Os componentes arrastados serão adicionados aqui -->
                                <div class="componente-container" data-id="imposto-icms">
                                    <div class="componente-header">
                                        <div class="componente-titulo">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                            <span>ICMS</span>
                                        </div>
                                        <div class="componente-acoes">
                                            <button class="btn-componente btn-editar"><i class="fas fa-edit"></i></button>
                                            <button class="btn-componente btn-remover"><i class="fas fa-times"></i></button>
                                        </div>
                                    </div>
                                    <div class="componente-body">
                                        <div class="campo-container">
                                            <label>Alíquota (%)</label>
                                            <input type="number" class="form-control custo-valor" step="0.01" value="18.00">
                                        </div>
                                        <div class="campo-info">
                                            <span class="info-label">Valor (R$):</span>
                                            <span class="info-valor">R$ 1.22</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="componente-container" data-id="custo-transporte">
                                    <div class="componente-header">
                                        <div class="componente-titulo">
                                            <i class="fas fa-truck"></i>
                                            <span>Transporte</span>
                                        </div>
                                        <div class="componente-acoes">
                                            <button class="btn-componente btn-editar"><i class="fas fa-edit"></i></button>
                                            <button class="btn-componente btn-remover"><i class="fas fa-times"></i></button>
                                        </div>
                                    </div>
                                    <div class="componente-body">
                                        <div class="campo-container">
                                            <label>Custo por Litro (R$)</label>
                                            <input type="number" class="form-control custo-valor" step="0.01" value="0.15">
                                        </div>
                                        <div class="campo-info">
                                            <span class="info-label">Valor Total (R$):</span>
                                            <span class="info-valor">R$ 0.15</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="componente-container" data-id="formula-custo-caminhao">
                                    <div class="componente-header">
                                        <div class="componente-titulo">
                                            <i class="fas fa-function"></i>
                                            <span>Custo Manutenção Caminhão</span>
                                        </div>
                                        <div class="componente-acoes">
                                            <button class="btn-componente btn-editar"><i class="fas fa-edit"></i></button>
                                            <button class="btn-componente btn-remover"><i class="fas fa-times"></i></button>
                                        </div>
                                    </div>
                                    <div class="componente-body">
                                        <div class="campo-container">
                                            <label>Fórmula</label>
                                            <input type="text" class="form-control formula-valor" value="volume * 0.05 / 1000">
                                        </div>
                                        <div class="campo-container">
                                            <label>Custo Mensal (R$)</label>
                                            <input type="number" class="form-control" step="0.01" value="1500">
                                        </div>
                                        <div class="campo-info">
                                            <span class="info-label">Valor por Litro (R$):</span>
                                            <span class="info-valor">R$ 0.08</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="acoes-calculadora">
                            <button id="btn-adicionar-componente" class="shell-btn shell-btn-grey">
                                <i class="fas fa-plus"></i> Adicionar Componente
                            </button>
                            <button id="btn-calcular" class="shell-btn shell-btn-red">
                                <i class="fas fa-calculator"></i> Calcular
                            </button>
                        </div>
                    </div>
                    
                    <!-- Resultados -->
                    <div class="resultados-container" id="resultados">
                        <div class="resultados-header">
                            <h3>Resultados</h3>
                            <div class="acoes-resultados">
                                <button id="btn-exportar" class="shell-btn shell-btn-grey">
                                    <i class="fas fa-file-export"></i> Exportar
                                </button>
                                <button id="btn-compartilhar" class="shell-btn shell-btn-yellow">
                                    <i class="fas fa-share-alt"></i> Compartilhar
                                </button>
                            </div>
                        </div>
                        
                        <div class="resultados-cards">
                            <div class="resultado-card margem-bruta">
                                <div class="resultado-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="resultado-info">
                                    <div class="resultado-valor">R$ 2.54</div>
                                    <div class="resultado-label">Margem Bruta</div>
                                    <div class="resultado-percentual">37.4%</div>
                                </div>
                            </div>
                            
                            <div class="resultado-card custos-totais">
                                <div class="resultado-icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="resultado-info">
                                    <div class="resultado-valor">R$ 1.45</div>
                                    <div class="resultado-label">Custos Totais</div>
                                    <div class="resultado-percentual">21.4%</div>
                                </div>
                            </div>
                            
                            <div class="resultado-card margem-liquida">
                                <div class="resultado-icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="resultado-info">
                                    <div class="resultado-valor">R$ 1.09</div>
                                    <div class="resultado-label">Margem Líquida</div>
                                    <div class="resultado-percentual">16.1%</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="resultados-detalhes">
                            <h4>Detalhamento</h4>
                            <table class="table table-shell">
                                <thead>
                                    <tr>
                                        <th>Componente</th>
                                        <th>Valor (R$/L)</th>
                                        <th>% s/ Venda</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Preço de Compra</td>
                                        <td>R$ 4.25</td>
                                        <td>62.6%</td>
                                    </tr>
                                    <tr>
                                        <td>ICMS</td>
                                        <td>R$ 1.22</td>
                                        <td>18.0%</td>
                                    </tr>
                                    <tr>
                                        <td>Transporte</td>
                                        <td>R$ 0.15</td>
                                        <td>2.2%</td>
                                    </tr>
                                    <tr>
                                        <td>Manutenção Caminhão</td>
                                        <td>R$ 0.08</td>
                                        <td>1.2%</td>
                                    </tr>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th>Preço de Venda</th>
                                        <th>R$ 6.79</th>
                                        <th>100.0%</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                        
                        <div class="resultados-grafico">
                            <h4>Análise Gráfica</h4>
                            <div class="grafico-container">
                                <canvas id="grafico-margem"></canvas>
                            </div>
                        </div>
                        
                        <div class="resultados-projecao">
                            <h4>Projeção Mensal</h4>
                            <div class="projecao-grid">
                                <div class="projecao-item">
                                    <div class="item-label">Volume Total</div>
                                    <div class="item-valor">30.000 L</div>
                                </div>
                                <div class="projecao-item">
                                    <div class="item-label">Receita Total</div>
                                    <div class="item-valor">R$ 203.700,00</div>
                                </div>
                                <div class="projecao-item">
                                    <div class="item-label">Custo Total</div>
                                    <div class="item-valor">R$ 171.000,00</div>
                                </div>
                                <div class="projecao-item">
                                    <div class="item-label">Lucro Líquido</div>
                                    <div class="item-valor destaque">R$ 32.700,00</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Template para novo componente -->
    <template id="template-componente">
        <div class="componente-container" data-id="">
            <div class="componente-header">
                <div class="componente-titulo">
                    <i class="fas fa-dollar-sign"></i>
                    <span>Novo Componente</span>
                </div>
                <div class="componente-acoes">
                    <button class="btn-componente btn-editar"><i class="fas fa-edit"></i></button>
                    <button class="btn-componente btn-remover"><i class="fas fa-times"></i></button>
                </div>
            </div>
            <div class="componente-body">
                <div class="campo-container">
                    <label>Nome</label>
                    <input type="text" class="form-control nome-componente" value="Novo Componente">
                </div>
                <div class="campo-container">
                    <label>Valor</label>
                    <input type="number" class="form-control custo-valor" step="0.01" value="0.00">
                </div>
                <div class="campo-info">
                    <span class="info-label">Valor Total:</span>
                    <span class="info-valor">R$ 0.00</span>
                </div>
            </div>
        </div>
    </template>

    <!-- Modal para edição de componente -->
    <div class="modal fade" id="modal-editar-componente" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Editar Componente</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="edit-nome-componente">Nome</label>
                        <input type="text" class="form-control" id="edit-nome-componente">
                    </div>
                    <div class="form-group">
                        <label for="edit-tipo-componente">Tipo</label>
                        <select class="form-select" id="edit-tipo-componente">
                            <option value="custo-fixo">Custo Fixo</option>
                            <option value="custo-variavel">Custo Variável</option>
                            <option value="imposto">Imposto</option>
                            <option value="transporte">Transporte</option>
                            <option value="formula">Fórmula Personalizada</option>
                        </select>
                    </div>
                    <div class="form-group" id="form-group-valor">
                        <label for="edit-valor-componente">Valor</label>
                        <input type="number" class="form-control" id="edit-valor-componente" step="0.01">
                    </div>
                    <div class="form-group" id="form-group-aliquota" style="display: none;">
                        <label for="edit-aliquota-componente">Alíquota (%)</label>
                        <input type="number" class="form-control" id="edit-aliquota-componente" step="0.01">
                    </div>
                    <div class="form-group" id="form-group-formula" style="display: none;">
                        <label for="edit-formula-componente">Fórmula</label>
                        <input type="text" class="form-control" id="edit-formula-componente">
                        <small class="form-text text-muted">Use variáveis: preco, volume, custo</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="shell-btn shell-btn-grey" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="shell-btn shell-btn-red" id="btn-salvar-componente">Salvar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/chart.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/calculadora-margem.js"></script>
</body>
</html>
{{ end }} 