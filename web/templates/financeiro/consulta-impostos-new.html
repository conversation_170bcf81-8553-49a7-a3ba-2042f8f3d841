{{ define "financeiro/consulta-impostos-new.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Consulta de Impostos - Shell</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&family=Share+Tech+Mono&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/consulta-impostos-new.css">
</head>
<body>
    <!-- Sidebar Navigation -->
    {{ template "sidebar" . }}

    <!-- Main Content -->
    <div class="content-with-sidebar">
        <div class="main-content">
            <div class="page-header">
                <h1><i class="fas fa-calculator me-2"></i> Consulta de Impostos</h1>
                <p>Análise e comparação de impostos sobre combustíveis por estado</p>
            </div>

            <div class="page-content">
                <!-- Formulário de Consulta -->
                <div class="row mb-4">
                    <div class="col-lg-8 mx-auto">
                        <div class="card-shell">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h3 class="m-0"><i class="fas fa-search me-2"></i> Consulta de Impostos</h3>
                            </div>
                            <div class="card-body">
                                <form id="form-impostos" class="row g-3">
                                    <div class="col-md-6">
                                        <label for="estado" class="form-label">Estado</label>
                                        <select id="estado" class="form-select">
                                            <option value="" selected disabled>Selecione um estado</option>
                                            <option value="RJ">Rio de Janeiro</option>
                                            <option value="SP">São Paulo</option>
                                            <option value="MG">Minas Gerais</option>
                                            <option value="ES">Espírito Santo</option>
                                            <option value="BA">Bahia</option>
                                            <option value="RS">Rio Grande do Sul</option>
                                            <!-- Outros estados serão adicionados via JavaScript -->
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="combustivel" class="form-label">Tipo de Combustível</label>
                                        <select id="combustivel" class="form-select">
                                            <option value="" selected disabled>Selecione um combustível</option>
                                            <option value="Gasolina C (comum ou aditivada)">Gasolina Comum</option>
                                            <option value="Gasolina Premium">Gasolina Premium</option>
                                            <option value="Etanol Hidratado">Etanol</option>
                                            <option value="Diesel S500">Diesel S500</option>
                                            <option value="Diesel S10">Diesel S10</option>
                                            <!-- Outros combustíveis serão adicionados via JavaScript -->
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                            <button type="submit" class="btn-shell-red">
                                                <i class="fas fa-search me-2"></i> Consultar
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Resultados da Consulta -->
                <div id="resultados" class="mt-4" style="display: none;">
                    <div class="card-shell mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h3 class="m-0"><i class="fas fa-chart-pie me-2"></i> Alíquotas de Impostos</h3>
                            <div>
                                <span id="info-consulta" class="badge bg-secondary me-2"></span>
                                <button id="btn-exportar" class="btn-shell-yellow btn-sm">
                                    <i class="fas fa-file-export me-1"></i> Exportar
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Cards de Impostos -->
                            <div class="row" id="impostos-container">
                                <!-- ICMS Card -->
                                <div class="col-md-4 mb-3">
                                    <div class="imposto-card imposto-icms">
                                        <div class="imposto-header">
                                            <h4>ICMS</h4>
                                            <div class="imposto-icon">
                                                <i class="fas fa-landmark"></i>
                                            </div>
                                        </div>
                                        <div class="imposto-body">
                                            <div class="imposto-valor" id="valor-icms">34%</div>
                                            <div class="imposto-info">
                                                <div class="imposto-descricao">Imposto Estadual</div>
                                                <div class="imposto-porcentagem" id="perc-icms">34%</div>
                                            </div>
                                            <div class="imposto-barra">
                                                <div class="imposto-progresso" id="barra-icms" style="width: 34%;"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- PIS Card -->
                                <div class="col-md-4 mb-3">
                                    <div class="imposto-card imposto-pis">
                                        <div class="imposto-header">
                                            <h4>PIS</h4>
                                            <div class="imposto-icon">
                                                <i class="fas fa-hand-holding-usd"></i>
                                            </div>
                                        </div>
                                        <div class="imposto-body">
                                            <div class="imposto-valor" id="valor-pis">1.65%</div>
                                            <div class="imposto-info">
                                                <div class="imposto-descricao">Programa de Integração Social</div>
                                                <div class="imposto-porcentagem" id="perc-pis">1.65%</div>
                                            </div>
                                            <div class="imposto-barra">
                                                <div class="imposto-progresso" id="barra-pis" style="width: 1.65%;"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- COFINS Card -->
                                <div class="col-md-4 mb-3">
                                    <div class="imposto-card imposto-cofins">
                                        <div class="imposto-header">
                                            <h4>COFINS</h4>
                                            <div class="imposto-icon">
                                                <i class="fas fa-coins"></i>
                                            </div>
                                        </div>
                                        <div class="imposto-body">
                                            <div class="imposto-valor" id="valor-cofins">7.6%</div>
                                            <div class="imposto-info">
                                                <div class="imposto-descricao">Contribuição para Financiamento da Seguridade Social</div>
                                                <div class="imposto-porcentagem" id="perc-cofins">7.6%</div>
                                            </div>
                                            <div class="imposto-barra">
                                                <div class="imposto-progresso" id="barra-cofins" style="width: 7.6%;"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- CIDE Card -->
                                <div class="col-md-4 mb-3">
                                    <div class="imposto-card imposto-cide">
                                        <div class="imposto-header">
                                            <h4>CIDE</h4>
                                            <div class="imposto-icon">
                                                <i class="fas fa-gas-pump"></i>
                                            </div>
                                        </div>
                                        <div class="imposto-body">
                                            <div class="imposto-valor" id="valor-cide">10%</div>
                                            <div class="imposto-info">
                                                <div class="imposto-descricao">Contribuição de Intervenção no Domínio Econômico</div>
                                                <div class="imposto-porcentagem" id="perc-cide">10%</div>
                                            </div>
                                            <div class="imposto-barra">
                                                <div class="imposto-progresso" id="barra-cide" style="width: 10%;"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Outros impostos serão adicionados dinamicamente via JavaScript -->
                                
                                <!-- Total Card -->
                                <div class="col-12 mt-3">
                                    <div class="imposto-card imposto-total">
                                        <div class="imposto-header">
                                            <h4>Total de Impostos</h4>
                                            <div class="imposto-icon">
                                                <i class="fas fa-calculator"></i>
                                            </div>
                                        </div>
                                        <div class="imposto-body">
                                            <div class="imposto-valor" id="valor-total">43.93%</div>
                                            <div class="imposto-info">
                                                <div class="imposto-descricao">Carga tributária total sobre o combustível</div>
                                                <div class="imposto-porcentagem" id="perc-total">43.93%</div>
                                            </div>
                                            <div class="imposto-barra">
                                                <div class="imposto-progresso" id="barra-total" style="width: 43.93%;"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Detalhamento dos Impostos -->
                    <div class="card-shell">
                        <div class="card-header">
                            <h3 class="m-0"><i class="fas fa-list-ul me-2"></i> Detalhamento dos Impostos</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-shell">
                                    <thead>
                                        <tr>
                                            <th>Imposto</th>
                                            <th>Descrição</th>
                                            <th>Alíquota</th>
                                            <th>Valor (R$)</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tabela-detalhamento">
                                        <!-- Será preenchido via JavaScript -->
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="2">Total de Impostos</th>
                                            <th id="total-aliquota">43.93%</th>
                                            <th id="total-valor">R$ 0,00</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Informações Adicionais -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card-shell h-100">
                            <div class="card-header">
                                <h3 class="m-0"><i class="fas fa-info-circle me-2"></i> Sobre os Impostos</h3>
                            </div>
                            <div class="card-body">
                                <div class="accordion" id="accordionImpostos">
                                    <div class="accordion-item bg-dark text-light border-secondary">
                                        <h2 class="accordion-header" id="headingICMS">
                                            <button class="accordion-button collapsed bg-dark text-light" type="button" data-bs-toggle="collapse" data-bs-target="#collapseICMS" aria-expanded="false" aria-controls="collapseICMS">
                                                ICMS - Imposto sobre Circulação de Mercadorias e Serviços
                                            </button>
                                        </h2>
                                        <div id="collapseICMS" class="accordion-collapse collapse" aria-labelledby="headingICMS" data-bs-parent="#accordionImpostos">
                                            <div class="accordion-body">
                                                <p>O ICMS é um imposto estadual que incide sobre a circulação de mercadorias e serviços. Cada estado define sua própria alíquota, que pode variar de acordo com o produto.</p>
                                                <p>No caso dos combustíveis, o ICMS é um dos principais componentes do preço final.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="accordion-item bg-dark text-light border-secondary">
                                        <h2 class="accordion-header" id="headingPIS">
                                            <button class="accordion-button collapsed bg-dark text-light" type="button" data-bs-toggle="collapse" data-bs-target="#collapsePIS" aria-expanded="false" aria-controls="collapsePIS">
                                                PIS - Programa de Integração Social
                                            </button>
                                        </h2>
                                        <div id="collapsePIS" class="accordion-collapse collapse" aria-labelledby="headingPIS" data-bs-parent="#accordionImpostos">
                                            <div class="accordion-body">
                                                <p>O PIS é uma contribuição social federal que incide sobre a receita bruta das empresas. Seu objetivo é financiar o programa de seguro-desemprego e o abono salarial.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="accordion-item bg-dark text-light border-secondary">
                                        <h2 class="accordion-header" id="headingCOFINS">
                                            <button class="accordion-button collapsed bg-dark text-light" type="button" data-bs-toggle="collapse" data-bs-target="#collapseCOFINS" aria-expanded="false" aria-controls="collapseCOFINS">
                                                COFINS - Contribuição para Financiamento da Seguridade Social
                                            </button>
                                        </h2>
                                        <div id="collapseCOFINS" class="accordion-collapse collapse" aria-labelledby="headingCOFINS" data-bs-parent="#accordionImpostos">
                                            <div class="accordion-body">
                                                <p>A COFINS é uma contribuição federal que incide sobre a receita bruta das empresas. Seu objetivo é financiar a seguridade social, que inclui saúde, previdência e assistência social.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="accordion-item bg-dark text-light border-secondary">
                                        <h2 class="accordion-header" id="headingCIDE">
                                            <button class="accordion-button collapsed bg-dark text-light" type="button" data-bs-toggle="collapse" data-bs-target="#collapseCIDE" aria-expanded="false" aria-controls="collapseCIDE">
                                                CIDE - Contribuição de Intervenção no Domínio Econômico
                                            </button>
                                        </h2>
                                        <div id="collapseCIDE" class="accordion-collapse collapse" aria-labelledby="headingCIDE" data-bs-parent="#accordionImpostos">
                                            <div class="accordion-body">
                                                <p>A CIDE-Combustíveis é um tributo federal que incide sobre a importação e a comercialização de petróleo e seus derivados, gás natural e álcool etílico combustível.</p>
                                                <p>Os recursos arrecadados são destinados a subsídios a preços ou transporte de combustíveis, projetos ambientais e financiamento de programas de infraestrutura de transportes.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card-shell h-100">
                            <div class="card-header">
                                <h3 class="m-0"><i class="fas fa-chart-line me-2"></i> Comparativo de Alíquotas por Estado</h3>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-shell table-sm">
                                        <thead>
                                            <tr>
                                                <th>Estado</th>
                                                <th>ICMS Gasolina</th>
                                                <th>ICMS Etanol</th>
                                                <th>ICMS Diesel</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>SP</td>
                                                <td>25%</td>
                                                <td>12%</td>
                                                <td>13.3%</td>
                                            </tr>
                                            <tr>
                                                <td>RJ</td>
                                                <td>34%</td>
                                                <td>24%</td>
                                                <td>16%</td>
                                            </tr>
                                            <tr>
                                                <td>MG</td>
                                                <td>31%</td>
                                                <td>16%</td>
                                                <td>15%</td>
                                            </tr>
                                            <tr>
                                                <td>BA</td>
                                                <td>28%</td>
                                                <td>19%</td>
                                                <td>18%</td>
                                            </tr>
                                            <tr>
                                                <td>RS</td>
                                                <td>30%</td>
                                                <td>17%</td>
                                                <td>17%</td>
                                            </tr>
                                            <!-- Mais estados serão adicionados via JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-center mt-3">
                                    <a href="#" class="btn-shell-yellow btn-sm" id="btn-comparar-estados">
                                        <i class="fas fa-exchange-alt me-1"></i> Comparar Estados
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/impostos.js"></script>
    <script src="/static/js/calculadora-impostos.js"></script>
    
    <!-- Script para a nova interface -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Referências aos elementos do DOM
        const formImpostos = document.getElementById('form-impostos');
        const resultadosDiv = document.getElementById('resultados');
        const infoConsulta = document.getElementById('info-consulta');
        const tabelaDetalhamento = document.getElementById('tabela-detalhamento');
        const totalAliquota = document.getElementById('total-aliquota');
        const totalValor = document.getElementById('total-valor');
        
        // Função para formatar valores monetários
        function formatarMoeda(valor) {
            return valor.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
        }
        
        // Função para formatar percentuais
        function formatarPercentual(valor) {
            return valor.toFixed(2) + '%';
        }
        
        // Função para atualizar os cards de impostos
        function atualizarCardsImpostos(impostos, valorBase) {
            // Atualizar ICMS
            if (impostos.icms) {
                document.getElementById('valor-icms').textContent = formatarMoeda(impostos.icms.valor);
                document.getElementById('perc-icms').textContent = formatarPercentual(impostos.icms.percentual);
                document.getElementById('barra-icms').style.width = impostos.icms.percentual + '%';
            }
            
            // Atualizar PIS
            if (impostos.pisCofinsProduto) {
                const valorPis = impostos.pisCofinsProduto.valor * 0.22; // 22% do PIS/COFINS é PIS
                document.getElementById('valor-pis').textContent = formatarMoeda(valorPis);
                document.getElementById('perc-pis').textContent = formatarPercentual(impostos.pisCofinsProduto.percentual * 0.22);
                document.getElementById('barra-pis').style.width = (impostos.pisCofinsProduto.percentual * 0.22) + '%';
            }
            
            // Atualizar COFINS
            if (impostos.pisCofinsProduto) {
                const valorCofins = impostos.pisCofinsProduto.valor * 0.78; // 78% do PIS/COFINS é COFINS
                document.getElementById('valor-cofins').textContent = formatarMoeda(valorCofins);
                document.getElementById('perc-cofins').textContent = formatarPercentual(impostos.pisCofinsProduto.percentual * 0.78);
                document.getElementById('barra-cofins').style.width = (impostos.pisCofinsProduto.percentual * 0.78) + '%';
            }
            
            // Atualizar CIDE
            if (impostos.cide) {
                document.getElementById('valor-cide').textContent = formatarMoeda(impostos.cide.valor);
                document.getElementById('perc-cide').textContent = formatarPercentual(impostos.cide.percentual);
                document.getElementById('barra-cide').style.width = impostos.cide.percentual + '%';
            }
            
            // Atualizar Total
            if (impostos.total) {
                document.getElementById('valor-total').textContent = formatarMoeda(impostos.total.valor);
                document.getElementById('perc-total').textContent = formatarPercentual(impostos.total.percentual);
                document.getElementById('barra-total').style.width = impostos.total.percentual + '%';
                
                totalAliquota.textContent = formatarPercentual(impostos.total.percentual);
                totalValor.textContent = formatarMoeda(impostos.total.valor);
            }
            
            // Preencher tabela de detalhamento
            tabelaDetalhamento.innerHTML = '';
            for (const [chave, imposto] of Object.entries(impostos)) {
                if (chave !== 'total') {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>${obterNomeImposto(chave)}</td>
                        <td>${obterDescricaoImposto(chave)}</td>
                        <td>${formatarPercentual(imposto.percentual)}</td>
                        <td>${formatarMoeda(imposto.valor)}</td>
                    `;
                    tabelaDetalhamento.appendChild(tr);
                }
            }
        }
        
        // Função para obter o nome amigável do imposto
        function obterNomeImposto(chave) {
            const nomes = {
                'cide': 'CIDE',
                'pisCofinsProduto': 'PIS/COFINS Produto',
                'pisCofinsAeac': 'PIS/COFINS AEAC',
                'mistura': 'Mistura',
                'cidePisCof': 'CIDE + PIS/COFINS',
                'icms': 'ICMS',
                'pisCofinsProd': 'PIS/COFINS Produto',
                'pisCofinsDistr': 'PIS/COFINS Distribuidor',
                'pisCofinsEtanol': 'PIS/COFINS Etanol',
                'pmpf': 'PMPF',
                'icmsTotal': 'ICMS Total',
                'pisCofinsDiesel': 'PIS/COFINS Diesel',
                'pisCofinsBio': 'PIS/COFINS Biodiesel'
            };
            return nomes[chave] || chave;
        }
        
        // Função para obter a descrição do imposto
        function obterDescricaoImposto(chave) {
            const descricoes = {
                'cide': 'Contribuição de Intervenção no Domínio Econômico',
                'pisCofinsProduto': 'Programa de Integração Social e Contribuição para Financiamento da Seguridade Social',
                'pisCofinsAeac': 'PIS/COFINS sobre Álcool Etílico Anidro Combustível',
                'mistura': 'Percentual de mistura de etanol na gasolina',
                'cidePisCof': 'Combinação de CIDE e PIS/COFINS',
                'icms': 'Imposto sobre Circulação de Mercadorias e Serviços',
                'pisCofinsProd': 'PIS/COFINS sobre o produto',
                'pisCofinsDistr': 'PIS/COFINS sobre a distribuição',
                'pisCofinsEtanol': 'PIS/COFINS sobre o etanol',
                'pmpf': 'Preço Médio Ponderado ao Consumidor Final',
                'icmsTotal': 'Total do ICMS aplicado',
                'pisCofinsDiesel': 'PIS/COFINS sobre o diesel',
                'pisCofinsBio': 'PIS/COFINS sobre o biodiesel'
            };
            return descricoes[chave] || '';
        }
        
        // Manipulador de evento para o formulário
        formImpostos.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const estado = document.getElementById('estado').value;
            const combustivel = document.getElementById('combustivel').value;
            
            if (!estado || !combustivel) {
                alert('Por favor, selecione o estado e o tipo de combustível.');
                return;
            }
            
            try {
                // Simulação de cálculo de impostos (em um ambiente real, isso viria do backend)
                const valorBase = 5.00; // Valor base para cálculo (R$ 5,00)
                const calculadora = new CalculadoraImpostos();
                const impostos = calculadora.calcularImpostos(combustivel, estado, valorBase);
                
                // Atualizar informações da consulta
                infoConsulta.textContent = `${combustivel} - ${estado}`;
                
                // Atualizar cards e tabela
                atualizarCardsImpostos(impostos, valorBase);
                
                // Mostrar resultados
                resultadosDiv.style.display = 'block';
                
                // Scroll suave até os resultados
                resultadosDiv.scrollIntoView({ behavior: 'smooth' });
            } catch (error) {
                alert('Erro ao calcular impostos: ' + error.message);
                console.error(error);
            }
        });
        
        // Botão para exportar
        document.getElementById('btn-exportar').addEventListener('click', function() {
            alert('Funcionalidade de exportação será implementada em breve!');
        });
        
        // Botão para comparar estados
        document.getElementById('btn-comparar-estados').addEventListener('click', function() {
            alert('Funcionalidade de comparação entre estados será implementada em breve!');
        });
    });
    </script>
</body>
</html>
{{ end }}
