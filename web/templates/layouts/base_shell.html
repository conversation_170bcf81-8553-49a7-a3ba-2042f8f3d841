{{ define "layouts/base_shell.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .title }}</title>

    <!-- CSS Base -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Fontes Shell -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&family=Share+Tech+Mono&display=swap" rel="stylesheet">

    <!-- CSS Shell -->
    <link href="/static/css/common.css" rel="stylesheet">
    <link href="/static/css/styles.css" rel="stylesheet">
    <link href="/static/css/shell-theme.css" rel="stylesheet">

    <!-- CSS Específico da Página -->
    {{ block "page_styles" . }}{{ end }}

    <!-- CSS específico para páginas de prestadoras -->
    {{ if eq .page "perfil_empresa" }}
        <link href="/static/css/prestadoras/perfil_empresa.css" rel="stylesheet">
    {{ end }}
</head>
<body class="shell-theme">
    <!-- Menu Lateral -->
    {{ template "sidebar" . }}

    <!-- Conteúdo Principal -->
    <div class="content-with-sidebar">
        <div class="main-content">
            {{ block "content" . }}{{ end }}
        </div>
    </div>

    <!-- JavaScript Base -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JavaScript Shell -->
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>

    <!-- JavaScript Específico da Página -->
    {{ block "page_scripts" . }}{{ end }}

    <!-- JavaScript específico para páginas de prestadoras -->
    {{ if eq .page "perfil_empresa" }}
        <script src="/static/js/prestadoras/perfil_empresa.js"></script>
        <script src="/static/js/prestadoras/upload_logo.js"></script>
    {{ end }}
</body>
</html>
{{ end }}