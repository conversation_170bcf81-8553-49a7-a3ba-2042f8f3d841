# Documentação Completa do Módulo "Minha Conta"

## Vis<PERSON> Geral

O módulo "Minha Conta" é um conjunto de páginas que permite aos usuários gerenciar suas informações pessoais, configurações de segurança e acompanhar atividades relacionadas ao seu perfil no sistema. O módulo é composto por três páginas principais:

1. **Minha Conta**: Página principal que exibe informações do usuário e cards com diferentes funcionalidades.
2. **Editar Perfil**: Página para edição das informações pessoais do usuário.
3. **Alterar Senha**: Página para alteração da senha do usuário com validação de segurança.

## Arquivos do Módulo

### Arquivos HTML (Templates)

| Arquivo | Caminho | Descrição |
|---------|---------|-----------|
| Minha Conta | `web/templates/minhaconta/minha_conta.html` | Template da página principal |
| Editar Perfil | `web/templates/minhaconta/editar_perfil.html` | Template da página de edição de perfil |
| Alterar Senha | `web/templates/minhaconta/alterar_senha.html` | Template da página de alteração de senha |

### Arquivos CSS

| Arquivo | Caminho | Descrição |
|---------|---------|-----------|
| Minha Conta | `web/static/css/minha_conta.css` | Estilos da página principal |
| Editar Perfil | `web/static/css/editar_perfil.css` | Estilos da página de edição de perfil |
| Alterar Senha | `web/static/css/alterar_senha.css` | Estilos da página de alteração de senha |

### Arquivos JavaScript

| Arquivo | Caminho | Descrição |
|---------|---------|-----------|
| Minha Conta | `web/static/js/minha_conta.js` | Lógica da página principal |
| Editar Perfil | `web/static/js/editar_perfil.js` | Lógica da página de edição de perfil |
| Alterar Senha | `web/static/js/alterar_senha.js` | Lógica da página de alteração de senha |

### Rotas da API

| Rota | Método | Descrição |
|------|--------|-----------|
| `/api/user/me` | GET | Obtém informações do usuário atual |
| `/api/user/profile` | PUT | Atualiza o perfil do usuário |
| `/api/user/change-password` | POST | Altera a senha do usuário |
| `/api/equipments` | GET | Lista todos os equipamentos da filial |
| `/api/equipments/:id` | GET | Obtém detalhes de um equipamento específico |
| `/api/equipments` | POST | Cria um novo equipamento |
| `/api/equipments/:id` | PUT | Atualiza um equipamento existente |
| `/api/equipments/:id` | DELETE | Exclui um equipamento |

## Página "Minha Conta"

### Descrição

A página "Minha Conta" é a página principal do módulo, exibindo informações do usuário e cards com diferentes funcionalidades. A página é adaptativa e exibe conteúdo específico com base no tipo de usuário (administrador, filial, técnico, etc.).

### Cards

1. **Card de Perfil**: 
   - Exibe informações básicas do usuário (nome, e-mail, função)
   - Botões para editar perfil, alterar senha e gerenciar equipamentos (para usuários do tipo "filial")

2. **Card de Atividades**: 
   - Para usuários do tipo "filial": Exibe timers ativos de manutenções em andamento
   - Para outros usuários: Exibe a última atividade e o total de ordens

3. **Card de Histórico**:
   - Para usuários do tipo "filial": Exibe histórico de timers recentes concluídos
   - Para outros usuários: Exibe ordens recentes

4. **Card de Segurança**: 
   - Exibe informações sobre login e alterações de perfil
   - Botão para acessar notificações

5. **Card de Notificações**: 
   - Exibe uma prévia das notificações recentes do usuário
   - Botões para ver todas as notificações e marcar como lidas

6. **Card de Equipamentos** (apenas para usuários do tipo "filial"): 
   - Exibe uma lista resumida dos equipamentos da filial
   - Botão para gerenciar equipamentos

### Modais

1. **Modal de Notificações**: 
   - Exibe todas as notificações do usuário
   - Permite marcar todas como lidas

2. **Modal de Equipamentos**: 
   - Exibe lista completa de equipamentos
   - Permite adicionar, visualizar e editar equipamentos

3. **Modal de Cadastro de Equipamento**: 
   - Formulário para cadastrar novo equipamento
   - Reutilizado para editar equipamentos existentes

### Funcionalidades JavaScript

- **Inicialização de Timers**: Atualiza os timers ativos a cada segundo
- **Exibição de Modais**: Função para exibir modais pelo ID
- **Gerenciamento de Equipamentos**: Funções para listar, visualizar, editar, excluir e salvar equipamentos

## Página "Editar Perfil"

### Descrição

A página "Editar Perfil" permite ao usuário atualizar suas informações pessoais e preferências de notificação.

### Seções

1. **Informações Pessoais**:
   - Nome completo
   - E-mail
   - Telefone
   - Cargo
   - Biografia

2. **Preferências de Notificação**:
   - Receber notificações por e-mail
   - Receber notificações push no navegador
   - Receber notificações por SMS

3. **Avatar**:
   - Exibe o avatar atual do usuário
   - Permite fazer upload de um novo avatar

4. **Segurança**:
   - Links para alterar senha e configurações de segurança

### Funcionalidades JavaScript

- **Validação de Formulário**: Valida os campos do formulário antes de enviar
- **Upload de Avatar**: Permite fazer upload de um novo avatar
- **Atualização de Perfil**: Envia os dados do formulário para a API

## Página "Alterar Senha"

### Descrição

A página "Alterar Senha" permite ao usuário alterar sua senha com validação de segurança.

### Seções

1. **Formulário de Alteração de Senha**:
   - Senha atual
   - Nova senha
   - Confirmação da nova senha

2. **Medidor de Força da Senha**:
   - Exibe a força da nova senha (fraca, média, forte, muito forte)

3. **Requisitos de Senha**:
   - Mínimo de 8 caracteres
   - Pelo menos uma letra maiúscula
   - Pelo menos uma letra minúscula
   - Pelo menos um número
   - Pelo menos um caractere especial

4. **Dicas de Segurança**:
   - Senhas únicas
   - Senhas complexas
   - Atualizações regulares
   - Evitar informações pessoais

### Funcionalidades JavaScript

- **Validação de Senha**: Valida a força da senha e os requisitos
- **Alternância de Visibilidade**: Permite alternar a visibilidade da senha
- **Atualização de Senha**: Envia os dados do formulário para a API

## Fluxo de Dados

### Obtenção de Dados do Usuário

1. O usuário acessa a página "Minha Conta"
2. O servidor renderiza o template com os dados básicos do usuário (ID, nome, e-mail, função)
3. O JavaScript da página faz uma requisição para `/api/user/me` para obter dados adicionais
4. Os dados são exibidos nos cards correspondentes

### Atualização de Perfil

1. O usuário acessa a página "Editar Perfil"
2. O servidor renderiza o template com os dados básicos do usuário
3. O usuário preenche o formulário e clica em "Salvar Alterações"
4. O JavaScript valida os dados e envia uma requisição PUT para `/api/user/profile`
5. A API atualiza os dados do usuário no banco de dados
6. O usuário é redirecionado para a página "Minha Conta" ou recebe uma mensagem de sucesso

### Alteração de Senha

1. O usuário acessa a página "Alterar Senha"
2. O usuário preenche o formulário com a senha atual, nova senha e confirmação
3. O JavaScript valida a força da senha e os requisitos
4. O JavaScript envia uma requisição POST para `/api/user/change-password`
5. A API valida a senha atual, verifica os requisitos e atualiza a senha no banco de dados
6. O usuário recebe uma mensagem de sucesso

### Gerenciamento de Equipamentos (para usuários do tipo "filial")

1. O usuário clica no botão "Gerenciar Equipamentos" na página "Minha Conta"
2. O modal de equipamentos é exibido
3. O JavaScript faz uma requisição GET para `/api/equipments` para obter a lista de equipamentos
4. Os equipamentos são exibidos na tabela
5. O usuário pode adicionar, editar ou excluir equipamentos
6. As alterações são enviadas para a API correspondente

## Personalização e Manutenção

### Adicionando Novos Cards

Para adicionar um novo card à página "Minha Conta":

1. Adicione um novo elemento `<div class="col-lg-4 col-md-6">` dentro da div `row g-4`.
2. Dentro desse elemento, crie um card seguindo a estrutura:
   ```html
   <div class="card-shell nome-do-card">
       <div class="card-header-shell">
           <i class="fas fa-icone text-shell-yellow me-2"></i>
           <h2 class="card-title">Título do Card</h2>
       </div>
       <div class="card-body-shell">
           <!-- Conteúdo do card -->
       </div>
   </div>
   ```
3. Adicione estilos específicos para o novo card no arquivo CSS, se necessário.

### Adicionando Novos Modais

Para adicionar um novo modal à página:

1. Adicione um novo elemento `<div class="modal fade">` no final do body.
2. Dentro desse elemento, crie um modal seguindo a estrutura:
   ```html
   <div class="modal fade" id="nomeDoModal" tabindex="-1" aria-labelledby="nomeDoModalLabel" aria-hidden="true">
       <div class="modal-dialog modal-dialog-centered">
           <div class="modal-content modal-shell">
               <div class="modal-header">
                   <h5 class="modal-title" id="nomeDoModalLabel">
                       <i class="fas fa-icone text-shell-yellow me-2"></i>
                       Título do Modal
                   </h5>
                   <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
               </div>
               <div class="modal-body">
                   <!-- Conteúdo do modal -->
               </div>
               <div class="modal-footer">
                   <button type="button" class="btn-shell" data-bs-dismiss="modal">Fechar</button>
                   <button type="button" class="btn-shell-red" onclick="funcaoDeAcao()">Ação</button>
               </div>
           </div>
       </div>
   </div>
   ```
3. Adicione uma função no arquivo JavaScript para exibir o modal:
   ```javascript
   function funcaoDeAcao() {
       // Implementação da ação
   }
   ```

### Adaptação para Diferentes Tipos de Usuário

Para exibir conteúdo específico para diferentes tipos de usuário:

```html
{{ if and .User (eq .User.Role "filial") }}
    <!-- Conteúdo específico para filiais -->
{{ else if and .User (eq .User.Role "admin") }}
    <!-- Conteúdo específico para administradores -->
{{ else }}
    <!-- Conteúdo para outros tipos de usuário -->
{{ end }}
```

## Troubleshooting

### Problemas Comuns e Soluções

- **Cards não aparecem**: 
  - Verifique se a estrutura HTML está correta
  - Verifique se os dados estão sendo passados corretamente para o template

- **Timers não atualizam**: 
  - Verifique se a função `initializeTimers()` está sendo chamada
  - Verifique se os elementos HTML têm os atributos `data-start-time` e `data-active` corretos

- **Modais não abrem**: 
  - Verifique se a função `showModal()` está sendo chamada corretamente
  - Verifique se o ID do modal está correto

- **Requisições à API falham**: 
  - Verifique se as rotas da API estão corretas
  - Verifique se o servidor está respondendo corretamente
  - Verifique os logs do servidor para identificar erros

- **Upload de avatar falha**:
  - Verifique se o tipo de arquivo é válido (imagem)
  - Verifique se o tamanho do arquivo não excede o limite (2MB)
  - Verifique se a rota da API está correta

- **Validação de senha falha**:
  - Verifique se a senha atende a todos os requisitos
  - Verifique se a senha atual está correta
  - Verifique se a nova senha e a confirmação coincidem

## Conclusão

O módulo "Minha Conta" fornece uma interface completa para que os usuários gerenciem suas informações pessoais, configurações de segurança e acompanhem atividades relacionadas ao seu perfil no sistema. As páginas são responsivas, interativas e personalizadas com base no tipo de usuário, fornecendo uma experiência completa ao usuário.
