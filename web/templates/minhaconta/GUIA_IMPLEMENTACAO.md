# Guia de Implementação do Módulo "Minha Conta"

Este guia detalha o processo de implementação do módulo "Minha Conta", incluindo todos os componentes necessários e suas configurações.

## 1. Estrutura de Arquivos

### 1.1. Arquivos HTML (Templates)

- **<PERSON>a Conta**: `web/templates/minhaconta/minha_conta.html`
- **Editar Perfil**: `web/templates/minhaconta/editar_perfil.html`
- **Alterar <PERSON>**: `web/templates/minhaconta/alterar_senha.html`
- **Documentação**: `web/templates/minhaconta/DOCUMENTACAO_COMPLETA.md`

### 1.2. Arquivos CSS

- **Minha Conta**: `web/static/css/minha_conta.css`
- **Editar Perfil**: `web/static/css/editar_perfil.css`
- **Alterar Senha**: `web/static/css/alterar_senha.css`

### 1.3. Arquivos JavaScript

- **<PERSON>a Conta**: `web/static/js/minha_conta.js`
- **Editar Perfil**: `web/static/js/editar_perfil.js`
- **Alterar Senha**: `web/static/js/alterar_senha.js`

### 1.4. Arquivos de Backend

- **Modelo de Equipamento**: `internal/models/equipment.go`
- **Repositório de Equipamento**: `internal/repository/equipment_repository.go`
- **Serviço de Equipamento**: `internal/services/equipment_service.go`
- **Handler de API de Equipamento**: `internal/handlers/equipment_api_handler.go`
- **Handler de API de Usuário**: `internal/handlers/user_api_handler.go`

## 2. Implementação do Backend

### 2.1. Modelo de Equipamento

O modelo de equipamento define a estrutura de dados para equipamentos:

```go
// Equipment representa um equipamento de uma filial
type Equipment struct {
    ID             int       `json:"id" db:"id"`
    BranchID       int       `json:"branch_id" db:"branch_id"`
    Name           string    `json:"name" db:"name"`
    Type           string    `json:"type" db:"type"`
    Model          string    `json:"model" db:"model"`
    Brand          string    `json:"brand" db:"brand"`
    SerialNumber   string    `json:"serial_number" db:"serial_number"`
    Status         string    `json:"status" db:"status"`
    AcquisitionDate *time.Time `json:"acquisition_date,omitempty" db:"acquisition_date"`
    Notes          string    `json:"notes" db:"notes"`
    CreatedAt      time.Time `json:"created_at" db:"created_at"`
    UpdatedAt      time.Time `json:"updated_at" db:"updated_at"`
}
```

### 2.2. Repositório de Equipamento

O repositório de equipamento implementa a interface `IEquipmentRepository` e fornece métodos para acessar os dados de equipamentos:

```go
// EquipmentRepository gerencia o acesso aos dados de equipamentos
type EquipmentRepository struct {
    db *gorm.DB
}

// Métodos implementados:
// - GetAllEquipment
// - GetEquipmentByID
// - GetEquipmentsByType
// - CreateEquipment
// - UpdateEquipment
// - DeleteEquipment
// - EquipmentExists
// - GetByStationID
```

### 2.3. Serviço de Equipamento

O serviço de equipamento implementa a lógica de negócios para equipamentos:

```go
// EquipmentService gerencia a lógica de negócios para equipamentos
type EquipmentService struct {
    repo *repository.EquipmentRepository
}

// Métodos implementados:
// - GetAll
// - GetByID
// - GetByStationID
// - Create
// - Update
// - Delete
// - GetEquipmentTypes
```

### 2.4. Handler de API de Equipamento

O handler de API de equipamento processa as requisições HTTP relacionadas a equipamentos:

```go
// EquipmentAPIHandler gerencia as requisições da API relacionadas a equipamentos
type EquipmentAPIHandler struct {
    service *services.EquipmentService
}

// Métodos implementados:
// - GetAllEquipments
// - GetEquipmentByID
// - CreateEquipment
// - UpdateEquipment
// - DeleteEquipment
// - GetEquipmentTypes
```

### 2.5. Handler de API de Usuário

O handler de API de usuário processa as requisições HTTP relacionadas a usuários:

```go
// UserAPIHandler gerencia as requisições da API relacionadas a usuários
type UserAPIHandler struct {
    userService *services.UserService
}

// Métodos implementados:
// - GetCurrentUser
// - UpdateUserProfile
// - ChangePassword
```

### 2.6. Configuração das Rotas

As rotas são configuradas no arquivo `cmd/main.go`:

```go
// Rotas de usuário
user := api.Group("/user")
{
    user.GET("/me", userAPIHandler.GetCurrentUser)
    user.PUT("/profile", userAPIHandler.UpdateUserProfile)
    user.POST("/change-password", userAPIHandler.ChangePassword)
}

// Rotas de equipamentos
equipments := api.Group("/equipments")
{
    equipments.GET("", equipmentAPIHandler.GetAllEquipments)
    equipments.GET("/:id", equipmentAPIHandler.GetEquipmentByID)
    equipments.POST("", equipmentAPIHandler.CreateEquipment)
    equipments.PUT("/:id", equipmentAPIHandler.UpdateEquipment)
    equipments.DELETE("/:id", equipmentAPIHandler.DeleteEquipment)
    equipments.GET("/types", equipmentAPIHandler.GetEquipmentTypes)
}

// Rotas de páginas
protected.GET("/minha-conta", func(c *gin.Context) {
    c.HTML(http.StatusOK, "minhaconta/minha_conta.html", gin.H{
        "title":      "Minha Conta - Rede Tradição",
        "page":       "minha-conta",
        "ActivePage": "minha-conta",
        "User": gin.H{
            "ID":    c.GetInt("userID"),
            "Name":  c.GetString("userName"),
            "Email": c.GetString("userEmail"),
            "Role":  c.GetString("userRole"),
        },
        "now": time.Now(),
    })
})

protected.GET("/editar-perfil", func(c *gin.Context) {
    c.HTML(http.StatusOK, "minhaconta/editar_perfil.html", gin.H{
        "title":      "Editar Perfil - Rede Tradição",
        "page":       "editar-perfil",
        "ActivePage": "minha-conta",
        "User": gin.H{
            "ID":       c.GetInt("userID"),
            "Name":     c.GetString("userName"),
            "Email":    c.GetString("userEmail"),
            "Role":     c.GetString("userRole"),
            "Phone":    c.GetString("userPhone"),
            "Position": c.GetString("userPosition"),
            "Bio":      c.GetString("userBio"),
            "Preferences": gin.H{
                "EmailNotifications": true,
                "PushNotifications":  false,
                "SmsNotifications":   false,
            },
        },
    })
})

protected.GET("/minha-conta/alterar-senha", func(c *gin.Context) {
    c.HTML(http.StatusOK, "minhaconta/alterar_senha.html", gin.H{
        "title":      "Alterar Senha - Rede Tradição",
        "page":       "alterar-senha",
        "ActivePage": "minha-conta",
        "User": gin.H{
            "ID":    c.GetInt("userID"),
            "Name":  c.GetString("userName"),
            "Email": c.GetString("userEmail"),
            "Role":  c.GetString("userRole"),
        },
    })
})
```

## 3. Implementação do Frontend

### 3.1. Página "Minha Conta"

A página "Minha Conta" é composta por vários cards que exibem diferentes tipos de informações:

1. **Card de Perfil**: Exibe informações básicas do usuário
2. **Card de Atividades**: Exibe atividades recentes ou timers ativos
3. **Card de Histórico**: Exibe histórico de atividades ou timers concluídos
4. **Card de Segurança**: Exibe informações de segurança
5. **Card de Notificações**: Exibe notificações recentes
6. **Card de Equipamentos**: Exibe equipamentos da filial (apenas para usuários do tipo "filial")

A página também inclui modais para notificações, equipamentos e cadastro de equipamentos.

### 3.2. Página "Editar Perfil"

A página "Editar Perfil" permite ao usuário atualizar suas informações pessoais e preferências de notificação:

1. **Informações Pessoais**: Nome, e-mail, telefone, cargo, biografia
2. **Preferências de Notificação**: E-mail, push, SMS
3. **Avatar**: Upload de avatar
4. **Segurança**: Links para alterar senha e configurações de segurança

### 3.3. Página "Alterar Senha"

A página "Alterar Senha" permite ao usuário alterar sua senha com validação de segurança:

1. **Formulário de Alteração de Senha**: Senha atual, nova senha, confirmação
2. **Medidor de Força da Senha**: Exibe a força da nova senha
3. **Requisitos de Senha**: Lista de requisitos de segurança
4. **Dicas de Segurança**: Dicas para criar senhas seguras

### 3.4. JavaScript

O JavaScript implementa a lógica interativa das páginas:

1. **Minha Conta**:
   - Inicialização de timers
   - Exibição de modais
   - Gerenciamento de equipamentos

2. **Editar Perfil**:
   - Validação de formulário
   - Upload de avatar
   - Atualização de perfil

3. **Alterar Senha**:
   - Validação de senha
   - Alternância de visibilidade
   - Atualização de senha

### 3.5. CSS

O CSS implementa os estilos visuais das páginas:

1. **Minha Conta**:
   - Estilos para cards
   - Estilos para modais
   - Estilos para timers e atividades

2. **Editar Perfil**:
   - Estilos para formulário
   - Estilos para avatar
   - Estilos para opções de segurança

3. **Alterar Senha**:
   - Estilos para formulário
   - Estilos para medidor de força
   - Estilos para requisitos de senha
   - Estilos para dicas de segurança

## 4. Fluxo de Implementação

### 4.1. Preparação

1. Criar os modelos de dados necessários
2. Criar os repositórios para acesso aos dados
3. Criar os serviços para lógica de negócios
4. Criar os handlers para processar requisições HTTP

### 4.2. Implementação do Backend

1. Configurar as rotas da API
2. Implementar os handlers da API
3. Configurar as rotas das páginas
4. Implementar os handlers das páginas

### 4.3. Implementação do Frontend

1. Criar os templates HTML
2. Criar os arquivos CSS
3. Criar os arquivos JavaScript
4. Integrar os templates com os dados do backend

### 4.4. Testes

1. Testar as rotas da API
2. Testar as páginas
3. Testar a integração entre frontend e backend
4. Testar a responsividade em diferentes dispositivos

## 5. Considerações Finais

### 5.1. Segurança

- Validar dados de entrada tanto no cliente quanto no servidor
- Proteger rotas sensíveis com autenticação
- Implementar validação de senha segura
- Implementar proteção contra CSRF

### 5.2. Desempenho

- Minimizar o número de requisições à API
- Otimizar consultas ao banco de dados
- Implementar cache quando apropriado
- Otimizar carregamento de recursos estáticos

### 5.3. Manutenção

- Documentar o código
- Seguir padrões de codificação consistentes
- Implementar testes automatizados
- Manter a documentação atualizada

## 6. Próximos Passos

Para completar a implementação, seria necessário:

1. Implementar a funcionalidade de upload de avatar
2. Implementar a funcionalidade de notificações
3. Implementar a funcionalidade de timers para filiais
4. Implementar a funcionalidade de histórico de atividades
5. Implementar a funcionalidade de configurações de segurança
