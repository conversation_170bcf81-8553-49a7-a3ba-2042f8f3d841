# Documentação do Sistema de Alteração de Senha

## Visão Geral

O sistema de alteração de senha permite que os usuários alterem suas senhas seguindo políticas de segurança definidas. Este documento detalha a implementação, os componentes envolvidos e as regras de negócio aplicadas.

## Arquitetura

O sistema de alteração de senha é composto por:

1. **Interface do Usuário (Frontend)**
   - Página HTML: `web/templates/minhaconta/alterar_senha.html`
   - JavaScript: `web/static/js/alterar_senha.js`
   - CSS: `web/static/css/alterar_senha.css`

2. **API (Backend)**
   - Handler: `internal/handlers/user_api_handler.go` (método `ChangePassword`)
   - Serviço: `internal/services/user_service.go` (método `ChangePassword`)
   - Política de Senha: `internal/services/password_policy.go`
   - Repositório: `internal/repository/gorm_user_repository.go` e `internal/repository/sql_user_repository.go`

3. **Modelo de Dados**
   - Modelo de Usuário: `internal/models/models.go` (struct `User`)
   - Modelo de Política de Segurança: `internal/models/models.go` (struct `SecurityPolicy`)

4. **Banco de Dados**
   - Tabela `users` com colunas:
     - `password`: Armazena o hash da senha
     - `last_password_change`: Armazena a data da última alteração de senha
     - `force_password_change`: Flag que indica se o usuário deve alterar a senha no próximo login

## Fluxo de Funcionamento

1. **Acesso à Página**
   - O usuário acessa a rota `/minha-conta/alterar-senha`
   - O middleware de autenticação verifica se o usuário está logado
   - A página de alteração de senha é renderizada

2. **Preenchimento do Formulário**
   - O usuário preenche os campos:
     - Senha atual
     - Nova senha
     - Confirmação da nova senha
   - O JavaScript valida em tempo real:
     - Força da senha (comprimento, letras maiúsculas/minúsculas, números, caracteres especiais)
     - Correspondência entre nova senha e confirmação

3. **Envio do Formulário**
   - O JavaScript envia uma requisição POST para `/api/user/change-password`
   - O token CSRF é incluído no cabeçalho da requisição

4. **Processamento no Backend**
   - O handler recebe a requisição e extrai os dados
   - O serviço de usuário é chamado para processar a alteração de senha
   - Validações são realizadas:
     - Verificação da senha atual
     - Validação da força da nova senha
     - Verificação de informações pessoais na senha
     - Verificação do histórico de senhas
   - Se todas as validações passarem, a senha é atualizada no banco de dados

5. **Resposta ao Usuário**
   - Em caso de sucesso, uma resposta 200 OK é retornada
   - Em caso de erro, uma resposta 400 Bad Request com a mensagem de erro é retornada
   - O frontend exibe uma mensagem de sucesso ou erro ao usuário

## Regras de Negócio

### Validação de Senha

1. **Força da Senha**
   - Comprimento mínimo: 8 caracteres
   - Deve conter pelo menos uma letra maiúscula
   - Deve conter pelo menos uma letra minúscula
   - Deve conter pelo menos um número
   - Deve conter pelo menos um caractere especial

2. **Restrições**
   - A nova senha não pode ser igual à senha atual
   - A nova senha não pode conter informações pessoais (nome, email)
   - A nova senha não pode ser igual a uma das últimas 5 senhas utilizadas

3. **Expiração de Senha**
   - A senha expira após o número de dias definido na política de segurança
   - Se a senha expirar, o usuário é forçado a alterá-la no próximo login

## Implementação Técnica

### Modelo de Dados

```go
// User representa um usuário no sistema
type User struct {
    ID                  uint       `json:"id" gorm:"primaryKey"`
    Name                string     `json:"name"`
    Email               string     `json:"email" gorm:"unique"`
    Password            string     `json:"-"`
    Role                UserRole   `json:"role" gorm:"column:type"`
    BranchID            *uint      `json:"branch_id,omitempty"`
    FailedAttempts      int        `json:"failed_attempts" gorm:"default:0"`
    Blocked             bool       `json:"blocked" gorm:"default:false"`
    TOTPSecret          string     `json:"-" gorm:"column:totp_secret"`
    TOTPEnabled         bool       `json:"totp_enabled" gorm:"default:false"`
    LastPasswordChange  *time.Time `json:"last_password_change,omitempty" gorm:"column:last_password_change"`
    CreatedAt           time.Time  `json:"created_at"`
    UpdatedAt           time.Time  `json:"updated_at"`
    ForcePasswordChange bool       `json:"force_password_change" gorm:"default:false"`
    
    Branch *Branch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
}

// SecurityPolicy representa as políticas de segurança do sistema
type SecurityPolicy struct {
    ID                         uint      `json:"id" gorm:"primaryKey"`
    Name                       string    `json:"name"`
    PasswordMinLength          int       `json:"password_min_length" gorm:"default:8"`
    PasswordRequireUppercase   bool      `json:"password_require_uppercase" gorm:"default:true"`
    PasswordRequireNumber      bool      `json:"password_require_number" gorm:"default:true"`
    PasswordRequireSpecialChar bool      `json:"password_require_special_char" gorm:"default:true"`
    PasswordExpiryDays         int       `json:"password_expiry_days" gorm:"default:90"`
    MaxLoginAttempts           int       `json:"max_login_attempts" gorm:"default:5"`
    LockoutDurationMinutes     int       `json:"lockout_duration_minutes" gorm:"default:30"`
    Enable2FA                  bool      `json:"enable_2fa" gorm:"default:false"`
    SessionTimeoutMinutes      int       `json:"session_timeout_minutes" gorm:"default:60"`
    CreatedAt                  time.Time `json:"created_at"`
    UpdatedAt                  time.Time `json:"updated_at"`
}
```

### Serviço de Alteração de Senha

```go
// ChangePassword altera a senha de um usuário seguindo a política de senhas
func (s *UserService) ChangePassword(userID uint, currentPassword, newPassword string) error {
    // Buscar usuário
    user, err := s.userRepo.FindByID(userID)
    if err != nil {
        return fmt.Errorf("erro ao buscar usuário: %v", err)
    }

    // Verificar senha atual
    err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(currentPassword))
    if err != nil {
        return errors.New("senha atual incorreta")
    }

    // Validar nova senha
    passwordPolicy := NewPasswordPolicyService(s.userRepo)
    strength, validationErr := passwordPolicy.ValidatePasswordStrength(newPassword)
    if validationErr != nil {
        return validationErr
    }

    if strength < MediumPassword {
        return errors.New("a nova senha não atende aos requisitos mínimos de segurança")
    }

    // Verificar informações pessoais
    if passwordPolicy.containsPersonalInfo(newPassword, user) {
        return errors.New("a senha não deve conter informações pessoais como nome ou email")
    }

    // Verificar se é igual à senha atual
    if currentPassword == newPassword {
        return errors.New("a nova senha não pode ser igual à senha atual")
    }

    // Gerar hash da nova senha
    hashedPassword, err := s.HashPassword(newPassword)
    if err != nil {
        return err
    }

    // Verificar histórico de senhas
    inHistory, err := s.userRepo.IsPasswordInHistory(userID, hashedPassword)
    if err != nil {
        return fmt.Errorf("erro ao verificar histórico de senhas: %v", err)
    }

    if inHistory {
        return errors.New("a nova senha não pode ser igual a uma das últimas 5 senhas utilizadas")
    }

    // Adicionar senha atual ao histórico
    if err := s.userRepo.AddPasswordToHistory(userID, user.Password); err != nil {
        return fmt.Errorf("erro ao atualizar histórico de senhas: %v", err)
    }

    // Atualizar senha
    if err := s.userRepo.UpdatePassword(userID, hashedPassword); err != nil {
        return fmt.Errorf("erro ao atualizar senha: %v", err)
    }

    // Desativar flag de troca obrigatória
    err = s.userRepo.DisableForcePasswordChange(userID)
    if err != nil {
        // Apenas logar o erro, mas não falhar a operação
        fmt.Printf("[ALTERAR SENHA] Aviso: Não foi possível atualizar o status de troca obrigatória: %v\n", err)
    }

    return nil
}
```

### Repositório de Usuário

```go
// UpdatePassword atualiza a senha do usuário
func (r *GormUserRepository) UpdatePassword(id uint, password string) error {
    // Usar um ponteiro para time.Time para ser consistente com o modelo
    now := time.Now()

    result := r.db.Model(&models.User{}).Where("id = ?", id).
        Updates(map[string]interface{}{
            "password":             password,
            "last_password_change": &now,
        })

    if result.Error != nil {
        return result.Error
    }

    if result.RowsAffected == 0 {
        return fmt.Errorf("nenhum usuário encontrado com ID: %d", id)
    }

    return nil
}
```

### Frontend (JavaScript)

```javascript
// Função para alterar a senha
function changePassword(passwordData) {
    // Obter token CSRF
    const csrfToken = getCsrfToken();

    // Fazer requisição para a API
    fetch('/api/user/change-password', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': csrfToken
        },
        body: JSON.stringify(passwordData),
        credentials: 'same-origin'
    })
    .then(response => {
        // Processar resposta
        return response.text().then(text => {
            try {
                if (text) {
                    return { json: JSON.parse(text), status: response.status, ok: response.ok };
                }
                return { json: {}, status: response.status, ok: response.ok };
            } catch (e) {
                return { text: text, status: response.status, ok: response.ok };
            }
        });
    })
    .then(result => {
        if (!result.ok) {
            // Tratar erro
            if (result.json && result.json.error) {
                throw new Error(result.json.error);
            } else if (result.text) {
                throw new Error('Resposta do servidor: ' + result.text);
            } else {
                throw new Error('Falha ao alterar senha: ' + result.status);
            }
        }

        // Sucesso
        showSuccess();
    })
    .catch(error => {
        // Exibir mensagem de erro
        showError('Erro ao alterar senha: ' + error.message);
    });
}
```

## Migrações de Banco de Dados

Para garantir que o sistema funcione corretamente, as seguintes migrações devem ser executadas:

1. **Adicionar coluna `last_password_change` à tabela `users`**
   ```go
   // AddLastPasswordChangeToUsers adiciona a coluna last_password_change à tabela users
   func AddLastPasswordChangeToUsers(db *gorm.DB) error {
       // Verificar se a coluna já existe
       var count int64
       db.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'last_password_change'").Count(&count)
       
       // Se a coluna não existir, adicioná-la
       if count == 0 {
           return db.Exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS last_password_change TIMESTAMP").Error
       }
       
       return nil
   }
   ```

2. **Adicionar coluna `force_password_change` à tabela `users`**
   ```go
   // AddForcePasswordChangeToUsers adiciona a coluna force_password_change à tabela users
   func AddForcePasswordChangeToUsers(db *gorm.DB) error {
       // Verificar se a coluna já existe
       var count int64
       db.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'force_password_change'").Count(&count)
       
       // Se a coluna não existir, adicioná-la
       if count == 0 {
           return db.Exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS force_password_change BOOLEAN DEFAULT FALSE").Error
       }
       
       return nil
   }
   ```



## Conclusão

O sistema de alteração de senha implementa boas práticas de segurança e oferece uma experiência de usuário intuitiva. A arquitetura modular facilita a manutenção e extensão do sistema no futuro.

Para garantir o funcionamento correto, certifique-se de que todas as migrações necessárias foram executadas e que as políticas de segurança estão configuradas adequadamente.
    