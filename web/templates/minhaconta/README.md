# Documentação da Página "Minha Conta"

## Visão Geral

A página "Minha Conta" é uma interface centralizada para que os usuários gerenciem suas informações pessoais e acompanhem atividades relacionadas ao seu perfil no sistema. A página apresenta diferentes informações e funcionalidades dependendo do tipo de usuário (administrador, filial, técnico, etc.).

## Arquivos Relacionados

- **Template HTML**: `web/templates/minhaconta/minha_conta.html`
- **CSS**: `web/static/css/minha_conta.css`
- **JavaScript**: `web/static/js/minha_conta.js`
- **Rota**: Definida em `cmd/main.go`

## Estrutura da Página

A página "Minha Conta" é composta por vários cards que exibem diferentes tipos de informações:

1. **Card de Perfil**: Exibe informações básicas do usuário (nome, e-mail, função) e botões para editar perfil, alterar senha e gerenciar equipamentos (para usuários do tipo "filial").

2. **Card de Atividades**: 
   - Para usuários do tipo "filial": Exibe timers ativos de manutenções em andamento.
   - Para outros usuários: Exibe a última atividade e o total de ordens.

3. **Card de Histórico**:
   - Para usuários do tipo "filial": Exibe histórico de timers recentes concluídos.
   - Para outros usuários: Exibe ordens recentes.

4. **Card de Segurança**: Exibe informações sobre login e alterações de perfil, além de um botão para acessar notificações.

5. **Card de Notificações**: Exibe uma prévia das notificações recentes do usuário.

6. **Card de Equipamentos** (apenas para usuários do tipo "filial"): Exibe uma lista resumida dos equipamentos da filial.

## Modais

A página inclui os seguintes modais:

1. **Modal de Notificações**: Exibe todas as notificações do usuário.
2. **Modal de Equipamentos**: Permite visualizar e gerenciar todos os equipamentos da filial.
3. **Modal de Cadastro de Equipamento**: Permite cadastrar um novo equipamento ou editar um existente.

## Funcionalidades JavaScript

O arquivo `minha_conta.js` implementa as seguintes funcionalidades:

1. **Inicialização de Timers**: Atualiza os timers ativos a cada segundo.
2. **Exibição de Modais**: Função para exibir modais pelo ID.
3. **Gerenciamento de Equipamentos**:
   - Carregamento da lista de equipamentos
   - Visualização de detalhes de um equipamento
   - Edição de um equipamento
   - Exclusão de um equipamento
   - Salvamento de um novo equipamento ou atualização de um existente

## Personalização dos Cards

Para adicionar ou modificar cards na página:

1. Adicione um novo elemento `<div class="col-lg-4 col-md-6">` dentro da div `row g-4`.
2. Dentro desse elemento, crie um card seguindo a estrutura:
   ```html
   <div class="card-shell nome-do-card">
       <div class="card-header-shell">
           <i class="fas fa-icone text-shell-yellow me-2"></i>
           <h2 class="card-title">Título do Card</h2>
       </div>
       <div class="card-body-shell">
           <!-- Conteúdo do card -->
       </div>
   </div>
   ```

3. Adicione estilos específicos para o novo card no arquivo CSS, se necessário.

## Adaptação para Diferentes Tipos de Usuário

A página usa condicionais para exibir conteúdo específico para cada tipo de usuário:

```html
{{ if and .User (eq .User.Role "filial") }}
    <!-- Conteúdo específico para filiais -->
{{ else }}
    <!-- Conteúdo para outros tipos de usuário -->
{{ end }}
```

## Integração com a API

A página faz requisições para as seguintes rotas da API:

- `/api/equipments`: Para listar equipamentos
- `/api/equipments/:id`: Para obter detalhes de um equipamento específico
- `/api/equipments/:id`: Para atualizar um equipamento (PUT)
- `/api/equipments/:id`: Para excluir um equipamento (DELETE)
- `/api/equipments`: Para criar um novo equipamento (POST)

## Estilização

Os estilos da página estão definidos no arquivo `minha_conta.css`, que inclui:

- Estilos para cards e seus componentes
- Estilos para badges e botões
- Estilos para timers e atividades
- Estilos para modais
- Estilos responsivos para diferentes tamanhos de tela

## Responsividade

A página é responsiva e se adapta a diferentes tamanhos de tela:

- Em telas grandes (lg): Os cards são exibidos em 3 colunas
- Em telas médias (md): Os cards são exibidos em 2 colunas
- Em telas pequenas (sm): Os cards são exibidos em 1 coluna

## Manutenção e Extensão

Para adicionar novas funcionalidades à página:

1. **Novos Cards**: Adicione novos cards seguindo a estrutura existente.
2. **Novos Modais**: Adicione novos modais seguindo a estrutura existente.
3. **Novas Funcionalidades JavaScript**: Adicione novas funções ao arquivo `minha_conta.js`.
4. **Novos Estilos**: Adicione novos estilos ao arquivo `minha_conta.css`.

## Troubleshooting

Problemas comuns e suas soluções:

1. **Cards não aparecem**: Verifique se a estrutura HTML está correta e se os dados estão sendo passados corretamente para o template.
2. **Timers não atualizam**: Verifique se a função `initializeTimers()` está sendo chamada e se os elementos HTML têm os atributos `data-start-time` e `data-active` corretos.
3. **Modais não abrem**: Verifique se a função `showModal()` está sendo chamada corretamente e se o ID do modal está correto.
4. **Requisições à API falham**: Verifique se as rotas da API estão corretas e se o servidor está respondendo corretamente.
