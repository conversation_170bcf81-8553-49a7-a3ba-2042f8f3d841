{{ define "minhaconta/editar_perfil.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .title }}</title>
    <link rel="icon" href="/static/images/favicon.ico" type="image/x-icon">

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">

    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">

    <!-- CSS Específico -->
    <link rel="stylesheet" href="/static/css/sidebar.css">
    <link rel="stylesheet" href="/static/css/editar_perfil.css">
</head>
<body class="bg-dark">
    <!-- Sidebar -->
    {{ template "sidebar" . }}

    <!-- Conteúdo principal -->
    <div class="content-with-sidebar">
        <div class="container-fluid py-4">
            <!-- Cabeçalho da página -->
            <div class="page-header mb-4">
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-user-edit fa-2x text-shell-red me-3"></i>
                    <h1 class="page-title m-0">Editar Perfil</h1>
                </div>
                <p class="page-subtitle">
                    Atualize suas informações pessoais e preferências de conta.
                </p>
            </div>

            <!-- Conteúdo da página -->
            <div class="row">
                <!-- Coluna do formulário -->
                <div class="col-lg-8">
                    <div class="card-shell">
                        <div class="card-header-shell">
                            <i class="fas fa-id-card text-shell-yellow me-2"></i>
                            <h2 class="card-title">Informações Pessoais</h2>
                        </div>
                        <div class="card-body-shell">
                            <form id="profileForm" class="profile-form">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="name" class="form-label">Nome Completo *</label>
                                        <input type="text" class="form-control" id="name" name="name" value="{{ if .User }}{{ .User.Name }}{{ end }}" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="email" class="form-label">E-mail *</label>
                                        <input type="email" class="form-control" id="email" name="email" value="{{ if .User }}{{ .User.Email }}{{ end }}" required>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="phone" class="form-label">Telefone</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" value="{{ if .User }}{{ .User.Phone }}{{ end }}">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="position" class="form-label">Cargo</label>
                                        <input type="text" class="form-control" id="position" name="position" value="{{ if .User }}{{ .User.Position }}{{ end }}">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="bio" class="form-label">Biografia</label>
                                    <textarea class="form-control" id="bio" name="bio" rows="3">{{ if .User }}{{ .User.Bio }}{{ end }}</textarea>
                                    <small class="form-text text-muted">Uma breve descrição sobre você (máximo 200 caracteres).</small>
                                </div>

                                <hr class="my-4">

                                <h3 class="section-title">Preferências de Notificação</h3>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="emailNotifications" name="emailNotifications" {{ if and .User .User.Preferences.EmailNotifications }}checked{{ end }}>
                                        <label class="form-check-label" for="emailNotifications">Receber notificações por e-mail</label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="pushNotifications" name="pushNotifications" {{ if and .User .User.Preferences.PushNotifications }}checked{{ end }}>
                                        <label class="form-check-label" for="pushNotifications">Receber notificações push no navegador</label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="smsNotifications" name="smsNotifications" {{ if and .User .User.Preferences.SmsNotifications }}checked{{ end }}>
                                        <label class="form-check-label" for="smsNotifications">Receber notificações por SMS</label>
                                    </div>
                                </div>

                                <div class="form-actions mt-4">
                                    <button type="submit" class="btn-shell-red">
                                        <i class="fas fa-save me-2"></i> Salvar Alterações
                                    </button>
                                    <a href="/minha-conta" class="btn-shell ms-2">
                                        <i class="fas fa-times me-2"></i> Cancelar
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Coluna lateral -->
                <div class="col-lg-4">
                    <!-- Card de Avatar -->
                    <div class="card-shell mb-4">
                        <div class="card-header-shell">
                            <i class="fas fa-user-circle text-shell-yellow me-2"></i>
                            <h2 class="card-title">Avatar</h2>
                        </div>
                        <div class="card-body-shell text-center">
                            <div class="avatar-container">
                                <div class="profile-avatar-large">
                                    {{ if and .User .User.Name (gt (len .User.Name) 0) }}{{ slice .User.Name 0 1 }}{{ else }}U{{ end }}
                                </div>
                                <div class="avatar-overlay">
                                    <button type="button" class="btn-avatar-edit" onclick="document.getElementById('avatarUpload').click()">
                                        <i class="fas fa-camera"></i>
                                    </button>
                                </div>
                            </div>
                            <input type="file" id="avatarUpload" name="avatarUpload" accept="image/*" style="display: none;">
                            <p class="avatar-help mt-3">Clique no ícone da câmera para alterar seu avatar.</p>
                        </div>
                    </div>

                    <!-- Card de Segurança -->
                    <div class="card-shell">
                        <div class="card-header-shell">
                            <i class="fas fa-shield-alt text-shell-yellow me-2"></i>
                            <h2 class="card-title">Segurança</h2>
                        </div>
                        <div class="card-body-shell">
                            <div class="security-options">
                                <a href="/minha-conta/alterar-senha" class="security-option">
                                    <div class="security-icon">
                                        <i class="fas fa-key"></i>
                                    </div>
                                    <div class="security-content">
                                        <h4>Alterar Senha</h4>
                                        <p>Atualize sua senha de acesso</p>
                                    </div>
                                    <i class="fas fa-chevron-right"></i>
                                </a>

                                <a href="/security-settings" class="security-option">
                                    <div class="security-icon">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                    <div class="security-content">
                                        <h4>Configurações de Segurança</h4>
                                        <p>Gerencie autenticação de dois fatores</p>
                                    </div>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Sucesso -->
    <div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modal-shell">
                <div class="modal-header">
                    <h5 class="modal-title" id="successModalLabel">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        Sucesso
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <p>Suas informações foram atualizadas com sucesso!</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-shell" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Erro -->
    <div class="modal fade" id="errorModal" tabindex="-1" aria-labelledby="errorModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modal-shell">
                <div class="modal-header">
                    <h5 class="modal-title" id="errorModalLabel">
                        <i class="fas fa-exclamation-circle text-danger me-2"></i>
                        Erro
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <p id="errorMessage">Ocorreu um erro ao atualizar suas informações. Por favor, tente novamente.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-shell" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/sidebar.js"></script>
    <script src="/static/js/editar_perfil.js"></script>
</body>
</html>
{{ end }}
