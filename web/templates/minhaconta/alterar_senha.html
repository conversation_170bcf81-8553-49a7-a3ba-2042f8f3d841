{{ define "minhaconta/alterar_senha.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ .csrfToken }}">
    <title>{{ .title }}</title>
    <link rel="icon" href="/static/images/favicon.ico" type="image/x-icon">

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">

    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">

    <!-- CSS Específico -->
    <link rel="stylesheet" href="/static/css/sidebar.css">
    <link rel="stylesheet" href="/static/css/alterar_senha.css">
</head>
<body class="bg-dark">
    <!-- Sidebar -->
    {{ template "sidebar" . }}

    <!-- Conteúdo principal -->
    <div class="content-with-sidebar">
        <div class="container-fluid py-4">
            <!-- Cabeçalho da página -->
            <div class="page-header mb-4">
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-key fa-2x text-shell-red me-3"></i>
                    <h1 class="page-title m-0">Alterar Senha</h1>
                </div>
                <p class="page-subtitle">
                    Atualize sua senha para manter sua conta segura.
                </p>
            </div>

            <!-- Conteúdo da página -->
            <div class="row justify-content-center">
                <div class="col-lg-6 col-md-8">
                    <div class="card-shell">
                        <div class="card-header-shell">
                            <i class="fas fa-lock text-shell-yellow me-2"></i>
                            <h2 class="card-title">Alterar Senha</h2>
                        </div>
                        <div class="card-body-shell">
                            <form id="passwordForm" class="password-form">
                                <div class="mb-3">
                                    <label for="currentPassword" class="form-label">Senha Atual *</label>
                                    <div class="password-input-group">
                                        <input type="password" class="form-control" id="currentPassword" name="currentPassword" required>
                                        <button type="button" class="password-toggle" onclick="togglePasswordVisibility('currentPassword')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="newPassword" class="form-label">Nova Senha *</label>
                                    <div class="password-input-group">
                                        <input type="password" class="form-control" id="newPassword" name="newPassword" required>
                                        <button type="button" class="password-toggle" onclick="togglePasswordVisibility('newPassword')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="password-strength mt-2">
                                        <div class="strength-meter">
                                            <div class="strength-meter-fill" id="strengthMeter"></div>
                                        </div>
                                        <div class="strength-text" id="strengthText">Força da senha</div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="confirmPassword" class="form-label">Confirmar Nova Senha *</label>
                                    <div class="password-input-group">
                                        <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
                                        <button type="button" class="password-toggle" onclick="togglePasswordVisibility('confirmPassword')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="password-requirements mb-4">
                                    <h3 class="requirements-title">Requisitos de Senha</h3>
                                    <ul class="requirements-list">
                                        <li id="req-length"><i class="fas fa-circle"></i> Mínimo de 8 caracteres</li>
                                        <li id="req-uppercase"><i class="fas fa-circle"></i> Pelo menos uma letra maiúscula</li>
                                        <li id="req-lowercase"><i class="fas fa-circle"></i> Pelo menos uma letra minúscula</li>
                                        <li id="req-number"><i class="fas fa-circle"></i> Pelo menos um número</li>
                                        <li id="req-special"><i class="fas fa-circle"></i> Pelo menos um caractere especial</li>
                                    </ul>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn-shell-red">
                                        <i class="fas fa-save me-2"></i> Alterar Senha
                                    </button>
                                    <a href="/minha-conta" class="btn-shell ms-2">
                                        <i class="fas fa-times me-2"></i> Cancelar
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Coluna de dicas de segurança -->
                <div class="col-lg-4 col-md-8 mt-4 mt-lg-0">
                    <div class="card-shell security-tips-card">
                        <div class="card-header-shell">
                            <i class="fas fa-shield-alt text-shell-yellow me-2"></i>
                            <h2 class="card-title">Dicas de Segurança</h2>
                        </div>
                        <div class="card-body-shell">
                            <div class="security-tips">
                                <div class="security-tip">
                                    <div class="tip-icon">
                                        <i class="fas fa-fingerprint"></i>
                                    </div>
                                    <div class="tip-content">
                                        <h4>Senhas Únicas</h4>
                                        <p>Use senhas diferentes para cada conta que você possui.</p>
                                    </div>
                                </div>

                                <div class="security-tip">
                                    <div class="tip-icon">
                                        <i class="fas fa-random"></i>
                                    </div>
                                    <div class="tip-content">
                                        <h4>Senhas Complexas</h4>
                                        <p>Combine letras, números e símbolos para criar senhas fortes.</p>
                                    </div>
                                </div>

                                <div class="security-tip">
                                    <div class="tip-icon">
                                        <i class="fas fa-sync-alt"></i>
                                    </div>
                                    <div class="tip-content">
                                        <h4>Atualizações Regulares</h4>
                                        <p>Altere suas senhas a cada 3-6 meses para maior segurança.</p>
                                    </div>
                                </div>

                                <div class="security-tip">
                                    <div class="tip-icon">
                                        <i class="fas fa-user-secret"></i>
                                    </div>
                                    <div class="tip-content">
                                        <h4>Evite Informações Pessoais</h4>
                                        <p>Não use nomes, datas de nascimento ou informações facilmente descobertas.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Sucesso -->
    <div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modal-shell">
                <div class="modal-header">
                    <h5 class="modal-title" id="successModalLabel">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        Sucesso
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <p>Sua senha foi alterada com sucesso!</p>
                </div>
                <div class="modal-footer">
                    <a href="/minha-conta" class="btn-shell">Voltar para Minha Conta</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Erro -->
    <div class="modal fade" id="errorModal" tabindex="-1" aria-labelledby="errorModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modal-shell">
                <div class="modal-header">
                    <h5 class="modal-title" id="errorModalLabel">
                        <i class="fas fa-exclamation-circle text-danger me-2"></i>
                        Erro
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <p id="errorMessage">Ocorreu um erro ao alterar sua senha. Por favor, tente novamente.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-shell" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/sidebar.js"></script>
    <script src="/static/js/alterar_senha.js"></script>
</body>
</html>
{{ end }}
