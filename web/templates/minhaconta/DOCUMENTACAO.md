# Documentação da Página "Minha Conta"

## Visão Geral do Processo de Criação

Este documento detalha o processo completo de criação da página "Minha Conta" no sistema, incluindo todos os componentes necessários e suas configurações.

## 1. Criação dos Arquivos Principais

### 1.1. Arquivo HTML (Template)
- **Caminho**: `web/templates/minhaconta/minha_conta.html`
- **Função**: Define a estrutura visual da página, incluindo cards, modais e elementos interativos.
- **Estrutura**:
  - Definição do template com nome correspondente ao caminho do arquivo
  - Estrutura HTML5 completa com head e body
  - Inclusão do template da sidebar
  - Estrutura de cards para diferentes tipos de informações
  - Modais para funcionalidades específicas

### 1.2. Arquivo CSS
- **Caminho**: `web/static/css/minha_conta.css`
- **Função**: Define os estilos visuais específicos da página.
- **Estrutura**:
  - Variáveis CSS para cores e outros valores reutilizáveis
  - Estilos para os cards e seus componentes
  - Estilos para badges, botões e outros elementos interativos
  - Estilos para modais
  - Media queries para responsividade

### 1.3. Arquivo JavaScript
- **Caminho**: `web/static/js/minha_conta.js`
- **Função**: Implementa a lógica interativa da página.
- **Estrutura**:
  - Inicialização de componentes quando o documento é carregado
  - Funções para gerenciar timers ativos
  - Funções para exibir modais
  - Funções para gerenciar equipamentos (listar, visualizar, editar, excluir, salvar)
  - Funções auxiliares para formatação e manipulação de dados

## 2. Configuração da Rota no Servidor

### 2.1. Atualização do Arquivo de Rotas
- **Caminho**: `cmd/main.go`
- **Função**: Define a rota HTTP para a página e configura os dados a serem passados para o template.
- **Implementação**:
  ```go
  // Minha Conta
  protected.GET("/minha-conta", func(c *gin.Context) {
      c.HTML(http.StatusOK, "minhaconta/minha_conta.html", gin.H{
          "title":      "Minha Conta - Rede Tradição",
          "page":       "minha-conta",
          "ActivePage": "minha-conta",
          "User": gin.H{
              "ID":    c.GetInt("userID"),
              "Name":  c.GetString("userName"),
              "Email": c.GetString("userEmail"),
              "Role":  c.GetString("userRole"),
          },
          "now": time.Now(),
      })
  })
  ```

## 3. Integração com o Sistema Existente

### 3.1. Integração com a Sidebar
- Utilização do template existente da sidebar: `{{ template "sidebar" . }}`
- Passagem dos dados necessários para a sidebar, como a página ativa

### 3.2. Integração com o Sistema de Autenticação
- Utilização dos dados do usuário autenticado para personalizar a página
- Exibição de conteúdo específico com base no tipo de usuário (filial, admin, etc.)

### 3.3. Integração com o Sistema de Estilização Global
- Importação dos estilos globais do sistema
- Utilização de classes CSS consistentes com o restante do sistema
- Manutenção da identidade visual do sistema

## 4. Implementação de Funcionalidades Específicas

### 4.1. Cards Informativos
- **Card de Perfil**:
  - Exibe informações básicas do usuário (nome, e-mail, função)
  - Botões para editar perfil, alterar senha e gerenciar equipamentos

- **Card de Atividades**:
  - Para usuários do tipo "filial": Exibe timers ativos de manutenções
  - Para outros usuários: Exibe última atividade e total de ordens

- **Card de Histórico**:
  - Para usuários do tipo "filial": Exibe histórico de timers concluídos
  - Para outros usuários: Exibe ordens recentes

- **Card de Segurança**:
  - Exibe informações sobre login e alterações de perfil
  - Botão para acessar notificações

- **Card de Notificações**:
  - Exibe prévia das notificações recentes
  - Botões para ver todas as notificações e marcar como lidas

- **Card de Equipamentos** (apenas para usuários do tipo "filial"):
  - Exibe lista resumida dos equipamentos da filial
  - Botão para gerenciar equipamentos

### 4.2. Modais Interativos
- **Modal de Notificações**:
  - Exibe todas as notificações do usuário
  - Permite marcar todas como lidas

- **Modal de Equipamentos**:
  - Exibe lista completa de equipamentos
  - Permite adicionar, visualizar e editar equipamentos

- **Modal de Cadastro de Equipamento**:
  - Formulário para cadastrar novo equipamento
  - Reutilizado para editar equipamentos existentes

### 4.3. Funcionalidades JavaScript
- **Inicialização de Timers**:
  - Atualiza os timers ativos a cada segundo
  - Formata a exibição em horas, minutos e segundos

- **Gerenciamento de Modais**:
  - Funções para exibir e fechar modais
  - Limpeza de formulários ao fechar modais

- **Gerenciamento de Equipamentos**:
  - Carregamento da lista de equipamentos via API
  - Visualização de detalhes de um equipamento
  - Edição de um equipamento existente
  - Exclusão de um equipamento
  - Salvamento de um novo equipamento

## 5. Estrutura de Dados

### 5.1. Dados do Usuário
```json
{
  "ID": 10,
  "Name": "Administrador",
  "Email": "<EMAIL>",
  "Role": "admin"
}
```

### 5.2. Dados de Equipamentos
```json
{
  "id": 1,
  "name": "Bomba de Combustível 1",
  "type": "bomba",
  "model": "Shell Pro 2000",
  "serial_number": "BP2000-123",
  "status": "ativo",
  "brand": "Shell",
  "acquisition_date": "2023-01-15",
  "notes": "Instalada na entrada principal"
}
```

### 5.3. Dados de Timers
```json
{
  "ID": 1,
  "EquipmentType": "Bomba de Combustível",
  "MaintenanceOrderID": 123,
  "StartTime": "2025-04-17T10:30:00Z",
  "EndTime": null,
  "IsActive": true
}
```

### 5.4. Dados de Notificações
```json
{
  "ID": 1,
  "Title": "Nova ordem de serviço",
  "Message": "Você recebeu uma nova ordem de serviço #123",
  "Icon": "fa-clipboard-list",
  "CreatedAt": "2025-04-16T15:20:00Z",
  "IsRead": false
}
```

## 6. Personalização e Manutenção

### 6.1. Adicionando Novos Cards
Para adicionar um novo card à página:

1. Adicione um novo elemento `<div class="col-lg-4 col-md-6">` dentro da div `row g-4`.
2. Dentro desse elemento, crie um card seguindo a estrutura:
   ```html
   <div class="card-shell nome-do-card">
       <div class="card-header-shell">
           <i class="fas fa-icone text-shell-yellow me-2"></i>
           <h2 class="card-title">Título do Card</h2>
       </div>
       <div class="card-body-shell">
           <!-- Conteúdo do card -->
       </div>
   </div>
   ```
3. Adicione estilos específicos para o novo card no arquivo CSS, se necessário.

### 6.2. Adicionando Novos Modais
Para adicionar um novo modal à página:

1. Adicione um novo elemento `<div class="modal fade">` no final do body.
2. Dentro desse elemento, crie um modal seguindo a estrutura:
   ```html
   <div class="modal fade" id="nomeDoModal" tabindex="-1" aria-labelledby="nomeDoModalLabel" aria-hidden="true">
       <div class="modal-dialog modal-dialog-centered">
           <div class="modal-content modal-shell">
               <div class="modal-header">
                   <h5 class="modal-title" id="nomeDoModalLabel">
                       <i class="fas fa-icone text-shell-yellow me-2"></i>
                       Título do Modal
                   </h5>
                   <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
               </div>
               <div class="modal-body">
                   <!-- Conteúdo do modal -->
               </div>
               <div class="modal-footer">
                   <button type="button" class="btn-shell" data-bs-dismiss="modal">Fechar</button>
                   <button type="button" class="btn-shell-red" onclick="funcaoDeAcao()">Ação</button>
               </div>
           </div>
       </div>
   </div>
   ```
3. Adicione uma função no arquivo JavaScript para exibir o modal:
   ```javascript
   function funcaoDeAcao() {
       // Implementação da ação
   }
   ```

### 6.3. Adaptação para Diferentes Tipos de Usuário
Para exibir conteúdo específico para diferentes tipos de usuário:

```html
{{ if and .User (eq .User.Role "filial") }}
    <!-- Conteúdo específico para filiais -->
{{ else if and .User (eq .User.Role "admin") }}
    <!-- Conteúdo específico para administradores -->
{{ else }}
    <!-- Conteúdo para outros tipos de usuário -->
{{ end }}
```

## 7. Considerações para Implementação Completa

### 7.1. Implementação de APIs Necessárias
Para que a página funcione completamente, é necessário implementar as seguintes APIs:

- **GET /api/equipments**: Para listar equipamentos
- **GET /api/equipments/:id**: Para obter detalhes de um equipamento
- **POST /api/equipments**: Para criar um novo equipamento
- **PUT /api/equipments/:id**: Para atualizar um equipamento
- **DELETE /api/equipments/:id**: Para excluir um equipamento
- **GET /api/user/me**: Para obter informações detalhadas do usuário atual

### 7.2. Implementação de Páginas Relacionadas
Para completar o fluxo de navegação, é necessário implementar:

- **Página de edição de perfil** (`/editar-perfil`)
- **Página de alteração de senha** (`/minha-conta/alterar-senha`)

## 8. Troubleshooting

### 8.1. Problemas Comuns e Soluções

- **Cards não aparecem**:
  - Verifique se a estrutura HTML está correta
  - Verifique se os dados estão sendo passados corretamente para o template

- **Timers não atualizam**:
  - Verifique se a função `initializeTimers()` está sendo chamada
  - Verifique se os elementos HTML têm os atributos `data-start-time` e `data-active` corretos

- **Modais não abrem**:
  - Verifique se a função `showModal()` está sendo chamada corretamente
  - Verifique se o ID do modal está correto

- **Requisições à API falham**:
  - Verifique se as rotas da API estão corretas
  - Verifique se o servidor está respondendo corretamente

## Conclusão

A página "Minha Conta" é uma interface centralizada para que os usuários gerenciem suas informações pessoais e acompanhem atividades relacionadas ao seu perfil no sistema. A página é responsiva, interativa e personalizada com base no tipo de usuário, fornecendo uma experiência completa ao usuário.

Para manter a página atualizada e funcionando corretamente, é importante seguir as diretrizes de personalização e manutenção descritas neste documento, bem como implementar as APIs e páginas relacionadas necessárias.
