# Guia do Desenvolvedor - <PERSON><PERSON><PERSON><PERSON> "Minha Conta"

Este guia contém instruções específicas para desenvolvedores que precisem modificar ou estender a página "Minha Conta".

## Arquivos Principais

| Arquivo | Caminho | Descrição |
|---------|---------|-----------|
| Template HTML | `web/templates/minhaconta/minha_conta.html` | Estrutura visual da página |
| CSS | `web/static/css/minha_conta.css` | Estilos específicos da página |
| JavaScript | `web/static/js/minha_conta.js` | Lógica interativa da página |
| Rota | `cmd/main.go` | Configuração da rota HTTP |

## Modificando a Página

### 1. Adicionando um Novo Card

1. Abra o arquivo `web/templates/minhaconta/minha_conta.html`
2. Localize a seção `<!-- Grid de cards -->`
3. Adicione um novo card seguindo o padrão:

```html
<!-- Card Personalizado -->
<div class="col-lg-4 col-md-6">
    <div class="card-shell nome-do-card">
        <div class="card-header-shell">
            <i class="fas fa-icone text-shell-yellow me-2"></i>
            <h2 class="card-title">Título do Card</h2>
        </div>
        <div class="card-body-shell">
            <!-- Conteúdo do card -->
            <div class="conteudo-personalizado">
                <!-- Seu conteúdo aqui -->
            </div>
        </div>
    </div>
</div>
```

4. Abra o arquivo `web/static/css/minha_conta.css`
5. Adicione estilos específicos para o novo card:

```css
/* Estilos para o card personalizado */
.nome-do-card {
    /* Seus estilos aqui */
}

.conteudo-personalizado {
    /* Seus estilos aqui */
}
```

### 2. Adicionando um Novo Modal

1. Abra o arquivo `web/templates/minhaconta/minha_conta.html`
2. Adicione um novo modal no final do body:

```html
<!-- Modal Personalizado -->
<div class="modal fade" id="modalPersonalizado" tabindex="-1" aria-labelledby="modalPersonalizadoLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modal-shell">
            <div class="modal-header">
                <h5 class="modal-title" id="modalPersonalizadoLabel">
                    <i class="fas fa-icone text-shell-yellow me-2"></i>
                    Título do Modal
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
            </div>
            <div class="modal-body">
                <!-- Conteúdo do modal -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-shell" data-bs-dismiss="modal">Fechar</button>
                <button type="button" class="btn-shell-red" onclick="acaoPersonalizada()">Ação</button>
            </div>
        </div>
    </div>
</div>
```

3. Abra o arquivo `web/static/js/minha_conta.js`
4. Adicione uma função para a ação do modal:

```javascript
/**
 * Função personalizada para o modal
 */
function acaoPersonalizada() {
    // Sua implementação aqui
    console.log('Ação personalizada executada');
    
    // Fechar o modal após a ação
    const modal = bootstrap.Modal.getInstance(document.getElementById('modalPersonalizado'));
    if (modal) {
        modal.hide();
    }
}
```

### 3. Adicionando uma Nova Funcionalidade JavaScript

1. Abra o arquivo `web/static/js/minha_conta.js`
2. Adicione uma nova função:

```javascript
/**
 * Função personalizada
 * @param {type} param - Descrição do parâmetro
 * @returns {type} - Descrição do retorno
 */
function minhaFuncaoPersonalizada(param) {
    // Sua implementação aqui
    return resultado;
}
```

3. Se a função deve ser executada quando a página carrega, adicione-a ao evento `DOMContentLoaded`:

```javascript
document.addEventListener('DOMContentLoaded', function() {
    // Código existente...
    
    // Sua inicialização personalizada
    minhaFuncaoPersonalizada('valor inicial');
});
```

### 4. Modificando a Rota

1. Abra o arquivo `cmd/main.go`
2. Localize a rota `/minha-conta`
3. Modifique os dados passados para o template:

```go
// Minha Conta
protected.GET("/minha-conta", func(c *gin.Context) {
    c.HTML(http.StatusOK, "minhaconta/minha_conta.html", gin.H{
        "title":      "Minha Conta - Rede Tradição",
        "page":       "minha-conta",
        "ActivePage": "minha-conta",
        "User": gin.H{
            "ID":    c.GetInt("userID"),
            "Name":  c.GetString("userName"),
            "Email": c.GetString("userEmail"),
            "Role":  c.GetString("userRole"),
        },
        "now": time.Now(),
        // Adicione seus dados personalizados aqui
        "dadosPersonalizados": obterDadosPersonalizados(),
    })
})

// Função para obter dados personalizados
func obterDadosPersonalizados() interface{} {
    // Sua implementação aqui
    return dados
}
```

## Implementando APIs Necessárias

### 1. API de Equipamentos

Crie um novo arquivo `internal/handlers/equipment_handler.go`:

```go
package handlers

import (
    "net/http"
    "strconv"

    "github.com/gin-gonic/gin"
    "tradicao/internal/models"
    "tradicao/internal/services"
)

// EquipmentHandler gerencia as requisições relacionadas a equipamentos
type EquipmentHandler struct {
    service *services.EquipmentService
}

// NewEquipmentHandler cria um novo handler de equipamentos
func NewEquipmentHandler(service *services.EquipmentService) *EquipmentHandler {
    return &EquipmentHandler{
        service: service,
    }
}

// GetAll retorna todos os equipamentos
func (h *EquipmentHandler) GetAll(c *gin.Context) {
    // Obter ID da filial do usuário autenticado
    branchID := c.GetInt("branchID")
    
    equipments, err := h.service.GetAllByBranch(branchID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, equipments)
}

// GetByID retorna um equipamento pelo ID
func (h *EquipmentHandler) GetByID(c *gin.Context) {
    id, err := strconv.Atoi(c.Param("id"))
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
        return
    }
    
    equipment, err := h.service.GetByID(id)
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": "Equipamento não encontrado"})
        return
    }
    
    c.JSON(http.StatusOK, equipment)
}

// Create cria um novo equipamento
func (h *EquipmentHandler) Create(c *gin.Context) {
    var equipment models.Equipment
    if err := c.ShouldBindJSON(&equipment); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // Definir a filial do usuário autenticado
    equipment.BranchID = c.GetInt("branchID")
    
    id, err := h.service.Create(equipment)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusCreated, gin.H{"id": id, "message": "Equipamento criado com sucesso"})
}

// Update atualiza um equipamento existente
func (h *EquipmentHandler) Update(c *gin.Context) {
    id, err := strconv.Atoi(c.Param("id"))
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
        return
    }
    
    var equipment models.Equipment
    if err := c.ShouldBindJSON(&equipment); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    equipment.ID = id
    
    err = h.service.Update(equipment)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"message": "Equipamento atualizado com sucesso"})
}

// Delete exclui um equipamento
func (h *EquipmentHandler) Delete(c *gin.Context) {
    id, err := strconv.Atoi(c.Param("id"))
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
        return
    }
    
    err = h.service.Delete(id)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"message": "Equipamento excluído com sucesso"})
}
```

Registre as rotas no arquivo `cmd/main.go`:

```go
// Inicializar serviço e handler de equipamentos
equipmentRepo := repository.NewEquipmentRepository(db)
equipmentService := services.NewEquipmentService(equipmentRepo)
equipmentHandler := handlers.NewEquipmentHandler(equipmentService)

// Rotas de equipamentos
api.GET("/equipments", equipmentHandler.GetAll)
api.GET("/equipments/:id", equipmentHandler.GetByID)
api.POST("/equipments", equipmentHandler.Create)
api.PUT("/equipments/:id", equipmentHandler.Update)
api.DELETE("/equipments/:id", equipmentHandler.Delete)
```

### 2. API de Usuário

Adicione um novo endpoint ao `AuthHandler` existente:

```go
// GetMe retorna informações do usuário atual
func (h *AuthHandler) GetMe(c *gin.Context) {
    userID := c.GetInt("userID")
    
    user, err := h.service.GetByID(userID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    // Remover informações sensíveis
    user.Password = ""
    
    c.JSON(http.StatusOK, user)
}
```

Registre a rota no arquivo `cmd/main.go`:

```go
// Rotas de autenticação protegidas
auth := api.Group("/auth")
{
    // Rotas existentes...
    auth.GET("/me", authHandler.GetMe)
}

// Adicionar alias para compatibilidade
api.GET("/user/me", authHandler.GetMe)
```

## Boas Práticas

1. **Mantenha a consistência visual**: Siga o padrão de design existente para novos elementos.
2. **Documente seu código**: Adicione comentários explicativos para novas funções e componentes.
3. **Teste em diferentes dispositivos**: Verifique se suas alterações são responsivas.
4. **Otimize o desempenho**: Minimize o número de requisições à API e operações DOM.
5. **Mantenha a segurança**: Valide dados de entrada tanto no cliente quanto no servidor.
6. **Atualize a documentação**: Mantenha os arquivos README.md e DOCUMENTACAO.md atualizados.

## Fluxo de Trabalho Recomendado

1. **Planeje suas alterações**: Defina claramente o que precisa ser modificado.
2. **Faça backup dos arquivos**: Antes de fazer alterações significativas.
3. **Implemente as alterações**: Siga as instruções deste guia.
4. **Teste localmente**: Verifique se tudo funciona como esperado.
5. **Documente suas alterações**: Atualize a documentação conforme necessário.
6. **Implante em produção**: Siga o processo de implantação do projeto.

## Solução de Problemas

### Erros Comuns

1. **Erro 404 ao acessar a página**: Verifique se a rota está configurada corretamente.
2. **Erro 500 ao acessar a página**: Verifique os logs do servidor para identificar o erro.
3. **Elementos não aparecem**: Verifique se os dados estão sendo passados corretamente para o template.
4. **JavaScript não funciona**: Verifique o console do navegador para identificar erros.
5. **Estilos não são aplicados**: Verifique se o arquivo CSS está sendo carregado corretamente.

### Ferramentas de Depuração

1. **Console do navegador**: Para depurar erros JavaScript.
2. **Inspetor de elementos**: Para verificar a estrutura HTML e estilos aplicados.
3. **Rede**: Para verificar requisições à API e carregamento de recursos.
4. **Logs do servidor**: Para identificar erros no servidor.

## Contato

Se você tiver dúvidas ou precisar de ajuda, entre em contato com a equipe de desenvolvimento:

- **Email**: <EMAIL>
- **Slack**: #canal-desenvolvimento
