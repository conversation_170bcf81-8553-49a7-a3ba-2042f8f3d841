# Guia de Diagnóstico e Resolução de Erros - Sistema de Alteração de Senha

## Visão Geral

Este documento fornece um guia completo para identificar, diagnosticar e resolver erros relacionados ao sistema de alteração de senha. Ele é destinado a desenvolvedores e administradores de sistema responsáveis pela manutenção da aplicação.

## Categorias de Erros

Os erros no sistema de alteração de senha podem ser categorizados em:

1. **Erros de Validação de Entrada**
2. **Erros de Banco de Dados**
3. **Erros de Autenticação**
4. **Erros de Infraestrutura**
5. **Erros de Frontend**

## Erros de Validação de Entrada

### 1. Senha atual incorreta

**Mensagem de erro:** "Senha atual incorreta"

**Causa:** O usuário forneceu uma senha atual que não corresponde à senha armazenada no banco de dados.

**Solução:**
- Verificar se o usuário está digitando a senha corretamente
- Verificar se o teclado está com a configuração correta (Caps Lock, layout)
- Em casos extremos, um administrador pode redefinir a senha do usuário

**Código de diagnóstico:**
```go
// Verificar senha atual
err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(currentPassword))
if err != nil {
    return errors.New("senha atual incorreta")
}
```

### 2. Nova senha não atende aos requisitos mínimos

**Mensagem de erro:** "A nova senha não atende aos requisitos mínimos de segurança"

**Causa:** A senha fornecida não atende aos critérios de força definidos na política de segurança.

**Solução:**
- Orientar o usuário sobre os requisitos de senha
- Verificar se a interface está exibindo corretamente os requisitos
- Verificar se a política de segurança está configurada corretamente

**Código de diagnóstico:**
```go
strength, validationErr := passwordPolicy.ValidatePasswordStrength(newPassword)
if validationErr != nil {
    return validationErr
}

if strength < MediumPassword {
    return errors.New("a nova senha não atende aos requisitos mínimos de segurança")
}
```

### 3. Senha contém informações pessoais

**Mensagem de erro:** "A senha não deve conter informações pessoais como nome ou email"

**Causa:** A nova senha contém partes do nome ou email do usuário.

**Solução:**
- Orientar o usuário a escolher uma senha que não contenha informações pessoais
- Verificar a implementação do método `containsPersonalInfo`

**Código de diagnóstico:**
```go
if passwordPolicy.containsPersonalInfo(newPassword, user) {
    return errors.New("a senha não deve conter informações pessoais como nome ou email")
}
```

### 4. Nova senha igual à senha atual

**Mensagem de erro:** "A nova senha não pode ser igual à senha atual"

**Causa:** O usuário tentou definir a mesma senha que já está usando.

**Solução:**
- Orientar o usuário a escolher uma senha diferente da atual

**Código de diagnóstico:**
```go
if currentPassword == newPassword {
    return errors.New("a nova senha não pode ser igual à senha atual")
}
```

### 5. Senha já utilizada anteriormente

**Mensagem de erro:** "A nova senha não pode ser igual a uma das últimas 5 senhas utilizadas"

**Causa:** A nova senha está no histórico de senhas recentes do usuário.

**Solução:**
- Orientar o usuário a escolher uma senha que não tenha sido usada recentemente
- Verificar a implementação do método `IsPasswordInHistory`

**Código de diagnóstico:**
```go
inHistory, err := s.userRepo.IsPasswordInHistory(userID, hashedPassword)
if err != nil {
    return fmt.Errorf("erro ao verificar histórico de senhas: %v", err)
}

if inHistory {
    return errors.New("a nova senha não pode ser igual a uma das últimas 5 senhas utilizadas")
}
```

### 6. Confirmação de senha não corresponde

**Mensagem de erro:** "A nova senha e a confirmação não coincidem"

**Causa:** Os campos "Nova senha" e "Confirmar nova senha" contêm valores diferentes.

**Solução:**
- Orientar o usuário a digitar a mesma senha em ambos os campos

**Código de diagnóstico (JavaScript):**
```javascript
if (newPassword !== confirmPassword) {
    showError('A nova senha e a confirmação não coincidem.');
    return false;
}
```

## Erros de Banco de Dados

### 1. Coluna `force_password_change` não existe

**Mensagem de erro:** "ERROR: column 'force_password_change' of relation 'users' does not exist (SQLSTATE 42703)"

**Causa:** A coluna `force_password_change` não foi adicionada à tabela `users`.

**Solução:**
- Executar a migração `AddForcePasswordChangeToUsers`
- Verificar se a migração foi executada com sucesso
- Verificar se o banco de dados está acessível

**Código de diagnóstico:**
```go
// Desativar flag de troca obrigatória
err = s.userRepo.DisableForcePasswordChange(userID)
if err != nil {
    // Apenas logar o erro, mas não falhar a operação
    fmt.Printf("[ALTERAR SENHA] Aviso: Não foi possível atualizar o status de troca obrigatória: %v\n", err)
    // Verificar se o erro é relacionado à coluna inexistente
    if err.Error() != "ERROR: column \"force_password_change\" of relation \"users\" does not exist (SQLSTATE 42703)" {
        // Se for outro tipo de erro, pode ser mais grave
        fmt.Printf("[ALTERAR SENHA] Aviso: Erro não esperado ao atualizar status: %v\n", err)
    }
}
```

### 2. Coluna `last_password_change` não existe

**Mensagem de erro:** "ERROR: column 'last_password_change' of relation 'users' does not exist (SQLSTATE 42703)"

**Causa:** A coluna `last_password_change` não foi adicionada à tabela `users`.

**Solução:**
- Executar a migração `AddLastPasswordChangeToUsers`
- Verificar se a migração foi executada com sucesso
- Verificar se o banco de dados está acessível

**Código de diagnóstico:**
```go
// Atualizar senha
result := r.db.Model(&models.User{}).Where("id = ?", id).
    Updates(map[string]interface{}{
        "password":             password,
        "last_password_change": &now,
    })

if result.Error != nil {
    fmt.Printf("[ALTERAR SENHA] Repositório: Erro ao atualizar senha: %v\n", result.Error)
    return result.Error
}
```

### 3. Usuário não encontrado

**Mensagem de erro:** "Erro ao buscar usuário: record not found" ou "Nenhum usuário encontrado com ID: X"

**Causa:** O ID do usuário não existe no banco de dados ou o usuário foi excluído.

**Solução:**
- Verificar se o ID do usuário está correto
- Verificar se o usuário existe no banco de dados
- Verificar se o usuário está autenticado corretamente

**Código de diagnóstico:**
```go
user, err := s.userRepo.FindByID(userID)
if err != nil {
    fmt.Printf("[ALTERAR SENHA] Erro ao buscar usuário: %v\n", err)
    return fmt.Errorf("erro ao buscar usuário: %v", err)
}

// Ou no repositório:
if result.RowsAffected == 0 {
    fmt.Printf("[ALTERAR SENHA] Repositório: Nenhuma linha afetada para ID: %d\n", id)
    return fmt.Errorf("nenhum usuário encontrado com ID: %d", id)
}
```

### 4. Erro ao conectar ao banco de dados

**Mensagem de erro:** "Erro ao conectar ao banco de dados: dial tcp: lookup database: no such host"

**Causa:** O servidor de banco de dados não está acessível ou as credenciais estão incorretas.

**Solução:**
- Verificar se o servidor de banco de dados está em execução
- Verificar as configurações de conexão no arquivo `.env`
- Verificar se o firewall está permitindo a conexão
- Verificar se as credenciais estão corretas

**Código de diagnóstico:**
```go
db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
if err != nil {
    log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
}
```

## Erros de Autenticação

### 1. Token de sessão inválido ou expirado

**Mensagem de erro:** "Acesso não autorizado" ou redirecionamento para a página de login

**Causa:** O token JWT expirou ou é inválido.

**Solução:**
- Orientar o usuário a fazer login novamente
- Verificar a configuração de expiração do token JWT
- Verificar se o relógio do servidor está sincronizado

**Código de diagnóstico:**
```go
// No middleware de autenticação
token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
    return JWTSecret, nil
})

if err != nil || !token.Valid {
    c.Redirect(http.StatusFound, "/login")
    c.Abort()
    return
}
```

### 2. Token CSRF ausente ou inválido

**Mensagem de erro:** "CSRF token mismatch" ou "Forbidden"

**Causa:** O token CSRF não foi incluído na requisição ou é inválido.

**Solução:**
- Verificar se o JavaScript está incluindo o token CSRF no cabeçalho da requisição
- Verificar se o token CSRF está sendo gerado corretamente
- Limpar o cache do navegador

**Código de diagnóstico (JavaScript):**
```javascript
const csrfToken = getCsrfToken();
fetch('/api/user/change-password', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken
    },
    body: JSON.stringify(passwordData),
    credentials: 'same-origin'
})
```

## Erros de Infraestrutura

### 1. Servidor não responde

**Mensagem de erro:** "Failed to fetch" ou "Network error"

**Causa:** O servidor está indisponível ou a conexão de rede está instável.

**Solução:**
- Verificar se o servidor está em execução
- Verificar a conexão de rede
- Verificar os logs do servidor para identificar possíveis erros

**Código de diagnóstico (JavaScript):**
```javascript
fetch('/api/user/change-password', {
    // ...
})
.catch(error => {
    console.error('[ALTERAR SENHA] Erro ao alterar senha:', error);
    showError('Erro ao alterar senha: ' + error.message);
});
```

### 2. Timeout na requisição

**Mensagem de erro:** "Request timeout" ou "The operation timed out"

**Causa:** O servidor está demorando muito para responder.

**Solução:**
- Verificar a carga do servidor
- Verificar se há operações de banco de dados lentas
- Aumentar o timeout da requisição

**Código de diagnóstico (JavaScript):**
```javascript
// Implementar um timeout para a requisição
const controller = new AbortController();
const timeoutId = setTimeout(() => controller.abort(), 10000);

fetch('/api/user/change-password', {
    method: 'POST',
    // ...
    signal: controller.signal
})
.then(response => {
    clearTimeout(timeoutId);
    // ...
})
.catch(error => {
    clearTimeout(timeoutId);
    if (error.name === 'AbortError') {
        showError('A requisição excedeu o tempo limite. Por favor, tente novamente.');
    } else {
        showError('Erro ao alterar senha: ' + error.message);
    }
});
```

## Erros de Frontend

### 1. Validação de JavaScript desativada

**Sintoma:** O usuário consegue enviar o formulário com dados inválidos.

**Causa:** O JavaScript está desativado no navegador ou há um erro no código JavaScript.

**Solução:**
- Verificar se o JavaScript está ativado no navegador
- Verificar se há erros no console do navegador
- Implementar validação no servidor como backup

**Código de diagnóstico:**
```javascript
// Verificar se o JavaScript está ativado
if (!window.addEventListener) {
    // Exibir mensagem de aviso
    document.getElementById('jsWarning').style.display = 'block';
}
```

### 2. Incompatibilidade de navegador

**Sintoma:** A interface não funciona corretamente em determinados navegadores.

**Causa:** O código JavaScript ou CSS usa recursos não suportados por todos os navegadores.

**Solução:**
- Verificar a compatibilidade do código com diferentes navegadores
- Usar polyfills para recursos não suportados
- Testar em diferentes navegadores

**Código de diagnóstico:**
```javascript
// Verificar suporte a recursos modernos
if (!window.fetch) {
    // Carregar polyfill para fetch
    const script = document.createElement('script');
    script.src = '/static/js/polyfills/fetch.js';
    document.head.appendChild(script);
}
```

## Logs e Monitoramento

### Logs do Servidor

O sistema registra logs detalhados para facilitar a depuração:

```
[ALTERAR SENHA] Iniciando alteração de senha para usuário ID: X
[ALTERAR SENHA] Usuário encontrado: Nome (ID: X)
[ALTERAR SENHA] Senha atual validada com sucesso
[ALTERAR SENHA] Validando força da nova senha
[ALTERAR SENHA] Gerando hash da nova senha
[ALTERAR SENHA] Verificando histórico de senhas
[ALTERAR SENHA] Adicionando senha atual ao histórico
[ALTERAR SENHA] Atualizando senha no banco de dados
[ALTERAR SENHA] Repositório: Atualizando senha para usuário ID: X
[ALTERAR SENHA] Repositório: Senha atualizada com sucesso para ID: X
[ALTERAR SENHA] Desativando flag de troca obrigatória
[ALTERAR SENHA] Senha alterada com sucesso
```

### Logs do Cliente

O JavaScript também registra logs no console do navegador:

```javascript
console.log('[ALTERAR SENHA] Iniciando processo de alteração de senha');
console.log('[ALTERAR SENHA] Dados enviados:', JSON.stringify(passwordData));
console.log('[ALTERAR SENHA] Token CSRF obtido:', csrfToken ? 'Sim' : 'Não');
console.log('[ALTERAR SENHA] Status da resposta:', response.status);
console.log('[ALTERAR SENHA] Headers da resposta:', [...response.headers.entries()]);
console.log('[ALTERAR SENHA] Texto da resposta:', text);
console.log('[ALTERAR SENHA] Resultado processado:', result);
console.log('[ALTERAR SENHA] Dados de sucesso:', data);
console.error('[ALTERAR SENHA] Erro ao alterar senha:', error);
```

## Fluxograma de Diagnóstico

Para facilitar o diagnóstico de problemas, siga este fluxograma:

1. **O usuário consegue acessar a página de alteração de senha?**
   - **Não**: Verificar autenticação e permissões
   - **Sim**: Continuar para o próximo passo

2. **O formulário é exibido corretamente?**
   - **Não**: Verificar HTML, CSS e JavaScript
   - **Sim**: Continuar para o próximo passo

3. **O usuário consegue preencher e enviar o formulário?**
   - **Não**: Verificar validação de JavaScript
   - **Sim**: Continuar para o próximo passo

4. **A requisição é enviada para o servidor?**
   - **Não**: Verificar JavaScript e conexão de rede
   - **Sim**: Continuar para o próximo passo

5. **O servidor recebe a requisição?**
   - **Não**: Verificar logs do servidor e configuração de rede
   - **Sim**: Continuar para o próximo passo

6. **A validação no servidor passa?**
   - **Não**: Verificar mensagens de erro e logs
   - **Sim**: Continuar para o próximo passo

7. **A senha é atualizada no banco de dados?**
   - **Não**: Verificar logs do servidor e conexão com o banco de dados
   - **Sim**: Continuar para o próximo passo

8. **O usuário recebe uma resposta de sucesso?**
   - **Não**: Verificar logs do servidor e do cliente
   - **Sim**: Problema resolvido

## Prevenção de Erros

Para prevenir erros no sistema de alteração de senha:

1. **Manutenção Regular**
   - Executar todas as migrações necessárias
   - Verificar regularmente os logs do sistema
   - Manter as dependências atualizadas

2. **Testes Automatizados**
   - Implementar testes unitários para o serviço de alteração de senha
   - Implementar testes de integração para o fluxo completo
   - Implementar testes de interface para o formulário

3. **Monitoramento**
   - Configurar alertas para erros frequentes
   - Monitorar o tempo de resposta das requisições
   - Monitorar a taxa de sucesso das alterações de senha

## Conclusão

Este guia de diagnóstico e resolução de erros fornece uma referência abrangente para identificar e resolver problemas no sistema de alteração de senha. Seguindo as orientações deste documento, os desenvolvedores e administradores de sistema podem garantir que o sistema funcione de maneira confiável e segura.

Para problemas não cobertos por este guia, consulte a documentação completa do sistema ou entre em contato com a equipe de desenvolvimento.
