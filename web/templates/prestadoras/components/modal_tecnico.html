<!-- Modal Editar Técnico -->
<div class="modal fade" id="modalEditTecnico" tabindex="-1" aria-labelledby="modalEditTecnicoLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalEditTecnicoLabel">Editar Técnico</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="formEditTecnico">
                    <input type="hidden" id="editTecnicoId" name="id">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="editTecnicoNome" class="form-label">Nome Comple<PERSON></label>
                                <input type="text" class="form-control" id="editTecnicoNome" name="name" required>
                            </div>
                            <div class="mb-3">
                                <label for="editTecnicoEmail" class="form-label">Email</label>
                                <input type="email" class="form-control" id="editTecnicoEmail" name="email" required readonly>
                                <div class="form-text">O email não pode ser alterado pois é usado para login no sistema.</div>
                            </div>
                            <div class="mb-3">
                                <label for="editTecnicoTelefone" class="form-label">Telefone</label>
                                <input type="tel" class="form-control" id="editTecnicoTelefone" name="phone">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3 text-center">
                                <label class="form-label d-block">Foto do Técnico</label>
                                <div class="avatar-upload">
                                    <div class="avatar-preview">
                                        <div id="editImagePreview" style="background-image: url('/static/img/default-avatar.png');"></div>
                                    </div>
                                    <div class="avatar-edit">
                                        <input type='file' id="editTecnicoAvatar" name="avatar" accept=".png, .jpg, .jpeg" />
                                        <label for="editTecnicoAvatar"><i class="fas fa-pencil-alt"></i></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Especialidades</label>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="Elétrica" id="editEspEletrica" name="specialties">
                                    <label class="form-check-label" for="editEspEletrica">Elétrica</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="Hidráulica" id="editEspHidraulica" name="specialties">
                                    <label class="form-check-label" for="editEspHidraulica">Hidráulica</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="Mecânica" id="editEspMecanica" name="specialties">
                                    <label class="form-check-label" for="editEspMecanica">Mecânica</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="Refrigeração" id="editEspRefrigeracao" name="specialties">
                                    <label class="form-check-label" for="editEspRefrigeracao">Refrigeração</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="Automação" id="editEspAutomacao" name="specialties">
                                    <label class="form-check-label" for="editEspAutomacao">Automação</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="Informática" id="editEspInformatica" name="specialties">
                                    <label class="form-check-label" for="editEspInformatica">Informática</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="editTecnicoStatus" name="active" checked>
                            <label class="form-check-label" for="editTecnicoStatus">Ativo</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="btnSalvarEditTecnico">Salvar</button>
            </div>
        </div>
    </div>
</div>
