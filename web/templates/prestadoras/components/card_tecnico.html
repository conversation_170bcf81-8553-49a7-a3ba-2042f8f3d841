<!-- Card de Técnico -->
<div class="col-xl-3 col-md-6 mb-4 tecnico-card" data-id="{{.ID}}">
    <div class="card shadow h-100">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary tecnico-nome">{{.Name}}</h6>
            <div class="dropdown no-arrow">
                <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in">
                    <a class="dropdown-item btn-editar-tecnico" href="#" data-id="{{.ID}}">
                        <i class="fas fa-user-edit fa-sm fa-fw mr-2 text-gray-400"></i>Editar
                    </a>
                    <a class="dropdown-item btn-ver-ordens" href="#" data-id="{{.ID}}">
                        <i class="fas fa-clipboard-list fa-sm fa-fw mr-2 text-gray-400"></i>Ver Ordens
                    </a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item btn-remover-tecnico" href="#" data-id="{{.ID}}">
                        <i class="fas fa-trash fa-sm fa-fw mr-2 text-gray-400"></i>Remover
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body text-center">
            <div class="tecnico-avatar mb-3">
                {{if .AvatarURL}}
                <img class="img-profile rounded-circle" src="{{.AvatarURL}}" alt="Avatar do técnico">
                {{else}}
                <img class="img-profile rounded-circle" src="/static/img/default-avatar.png" alt="Avatar do técnico">
                {{end}}
            </div>
            <div class="tecnico-email mb-1">{{.Email}}</div>
            <div class="tecnico-telefone mb-2">{{.Phone}}</div>
            <div class="tecnico-especialidades">
                {{range .Specialties}}
                <span class="badge bg-primary me-1">{{.}}</span>
                {{end}}
            </div>
        </div>
        <div class="card-footer">
            <div class="row">
                <div class="col-6 text-center">
                    <div class="small font-weight-bold">Ordens Pendentes</div>
                    <span class="ordens-pendentes">{{.PendingOrders}}</span>
                </div>
                <div class="col-6 text-center">
                    <div class="small font-weight-bold">Ordens Concluídas</div>
                    <span class="ordens-concluidas">{{.CompletedOrders}}</span>
                </div>
            </div>
        </div>
    </div>
</div>
