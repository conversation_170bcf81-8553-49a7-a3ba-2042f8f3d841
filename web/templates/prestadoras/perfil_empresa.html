{{define "prestadoras/perfil_empresa.html"}}
{{ template "layouts/base_shell.html" . }}
{{end}}

{{define "content"}}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Perfil da Empresa</h1>
    </div>

    <div class="row">
        <div class="col-xl-4">
            <!-- Perfil Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Logomarca da Empresa</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="logo-upload">
                            <div class="logo-preview">
                                <img id="logoPreview" src="/static/img/default-logo.png" alt="Logomarca da empresa" class="img-fluid mb-3">
                            </div>
                            <div class="logo-edit">
                                <input type='file' id="logoInput" name="logo" accept=".png, .jpg, .jpeg" />
                                <label for="logoInput" class="btn btn-primary btn-sm">
                                    <i class="fas fa-upload me-2"></i>Alterar Logomarca
                                </label>
                            </div>
                        </div>
                        <div class="small text-gray-500 mt-2">Formatos aceitos: JPG, PNG. Tamanho máximo: 2MB.</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Status da Empresa</label>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="statusEmpresa" checked>
                            <label class="form-check-label" for="statusEmpresa">Ativo</label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Avaliação Média</label>
                        <div class="rating-stars">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star-half-alt text-warning"></i>
                            <span class="ms-2">4.5/5.0</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Data de Cadastro</label>
                        <p class="mb-0" id="dataCadastro">01/01/2023</p>
                    </div>
                </div>
            </div>

            <!-- Estatísticas Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Estatísticas</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-6">
                            <div class="small font-weight-bold">Total de Técnicos</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="estatTecnicos">0</div>
                        </div>
                        <div class="col-6">
                            <div class="small font-weight-bold">Ordens Atendidas</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="estatOrdens">0</div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="small font-weight-bold">Tempo Médio</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="estatTempo">0h</div>
                        </div>
                        <div class="col-6">
                            <div class="small font-weight-bold">Taxa de Conclusão</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="estatTaxa">0%</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Botão Salvar Alterações -->
            <div class="actions-container">
                <button class="btn btn-primary btn-save-profile" id="btnSalvarPerfil">
                    <i class="fas fa-save me-2"></i>Salvar Alterações
                </button>
            </div>
        </div>

        <div class="col-xl-8">
            <!-- Informações da Empresa Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informações da Empresa</h6>
                </div>
                <div class="card-body">
                    <form id="formPerfilEmpresa">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="nomeFantasia" class="form-label">Nome Fantasia</label>
                                <input type="text" class="form-control" id="nomeFantasia" name="name" required>
                            </div>
                            <div class="col-md-6">
                                <label for="razaoSocial" class="form-label">Razão Social</label>
                                <input type="text" class="form-control" id="razaoSocial" name="company_name" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="cnpj" class="form-label">CNPJ</label>
                                <input type="text" class="form-control" id="cnpj" name="cnpj" required>
                            </div>
                            <div class="col-md-6">
                                <label for="areaAtuacao" class="form-label">Área de Atuação</label>
                                <input type="text" class="form-control" id="areaAtuacao" name="area_of_expertise">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="endereco" class="form-label">Endereço</label>
                            <input type="text" class="form-control" id="endereco" name="address">
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="cidade" class="form-label">Cidade</label>
                                <input type="text" class="form-control" id="cidade" name="city">
                            </div>
                            <div class="col-md-3">
                                <label for="estado" class="form-label">Estado</label>
                                <select class="form-select" id="estado" name="state">
                                    <option value="">Selecione...</option>
                                    <option value="AC">AC</option>
                                    <option value="AL">AL</option>
                                    <option value="AP">AP</option>
                                    <option value="AM">AM</option>
                                    <option value="BA">BA</option>
                                    <option value="CE">CE</option>
                                    <option value="DF">DF</option>
                                    <option value="ES">ES</option>
                                    <option value="GO">GO</option>
                                    <option value="MA">MA</option>
                                    <option value="MT">MT</option>
                                    <option value="MS">MS</option>
                                    <option value="MG">MG</option>
                                    <option value="PA">PA</option>
                                    <option value="PB">PB</option>
                                    <option value="PR">PR</option>
                                    <option value="PE">PE</option>
                                    <option value="PI">PI</option>
                                    <option value="RJ">RJ</option>
                                    <option value="RN">RN</option>
                                    <option value="RS">RS</option>
                                    <option value="RO">RO</option>
                                    <option value="RR">RR</option>
                                    <option value="SC">SC</option>
                                    <option value="SP">SP</option>
                                    <option value="SE">SE</option>
                                    <option value="TO">TO</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="cep" class="form-label">CEP</label>
                                <input type="text" class="form-control" id="cep" name="zip_code">
                            </div>
                        </div>

                        <h6 class="section-title mt-4 mb-3">Informações de Contato</h6>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="nomeContato" class="form-label">Nome do Contato</label>
                                <input type="text" class="form-control" id="nomeContato" name="contact_name">
                            </div>
                            <div class="col-md-6">
                                <label for="emailContato" class="form-label">Email de Contato</label>
                                <input type="email" class="form-control" id="emailContato" name="contact_email">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="telefoneContato" class="form-label">Telefone de Contato</label>
                            <input type="tel" class="form-control" id="telefoneContato" name="contact_phone">
                        </div>

                        <h6 class="section-title mt-4 mb-3">Especialidades</h6>

                        <div class="mb-3">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="Elétrica" id="espEletrica" name="specialties">
                                        <label class="form-check-label" for="espEletrica">Elétrica</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="Hidráulica" id="espHidraulica" name="specialties">
                                        <label class="form-check-label" for="espHidraulica">Hidráulica</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="Mecânica" id="espMecanica" name="specialties">
                                        <label class="form-check-label" for="espMecanica">Mecânica</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="Refrigeração" id="espRefrigeracao" name="specialties">
                                        <label class="form-check-label" for="espRefrigeracao">Refrigeração</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="Automação" id="espAutomacao" name="specialties">
                                        <label class="form-check-label" for="espAutomacao">Automação</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="Informática" id="espInformatica" name="specialties">
                                        <label class="form-check-label" for="espInformatica">Informática</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Sucesso -->
<div class="modal fade" id="modalSucesso" tabindex="-1" aria-labelledby="modalSucessoLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalSucessoLabel">Perfil Atualizado</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="fas fa-check-circle text-success fa-4x"></i>
                </div>
                <p class="text-center">As informações da empresa foram atualizadas com sucesso!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "page_scripts"}}
<!-- Scripts já incluídos no template base quando page="perfil_empresa" -->
{{end}}
