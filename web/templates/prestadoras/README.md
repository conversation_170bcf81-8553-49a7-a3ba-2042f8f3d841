# Documentação da Página de Prestadoras

## Visão Geral

A página de Prestadoras é uma interface dedicada para que empresas prestadoras de serviço gerenciem seus técnicos e informações empresariais. Esta página implementa o conceito de que prestadoras são empresas que prestam serviço para a rede Tradição, funcionando como uma matriz que cadastra e gerencia seus próprios técnicos.

## Estrutura de Arquivos

### Templates HTML

- `web/templates/prestadoras/minha_equipe.html` - Página principal para gerenciamento de técnicos
- `web/templates/prestadoras/perfil_empresa.html` - Página para gerenciamento de informações da empresa
- `web/templates/prestadoras/components/modal_tecnico.html` - Componente modal para adicionar/editar técnicos
- `web/templates/prestadoras/components/card_tecnico.html` - Componente card para exibir informações de técnicos

### Arquivos CSS

- `web/static/css/prestadoras/minha_equipe.css` - Estilos para a página de gerenciamento de técnicos
- `web/static/css/prestadoras/perfil_empresa.css` - Estilos para a página de perfil da empresa

### Arquivos JavaScript

- `web/static/js/prestadoras/minha_equipe.js` - Funcionalidades para a página de gerenciamento de técnicos
- `web/static/js/prestadoras/perfil_empresa.js` - Funcionalidades para a página de perfil da empresa
- `web/static/js/prestadoras/upload_logo.js` - Funcionalidades para upload de logomarca

## Funcionalidades Principais

### Gerenciamento de Técnicos

A página "Minha Equipe" permite que prestadoras:

1. Visualizem todos os técnicos vinculados à empresa
2. Adicionem novos técnicos com informações básicas e foto
3. Editem informações de técnicos existentes
4. Removam técnicos da empresa
5. Visualizem ordens de serviço atribuídas a cada técnico

### Perfil da Empresa

A página "Perfil da Empresa" permite que prestadoras:

1. Visualizem e editem informações empresariais (nome, CNPJ, endereço, etc.)
2. Façam upload da logomarca da empresa
3. Gerenciem especialidades oferecidas
4. Visualizem estatísticas de desempenho

## Fluxos de Usuário

### Fluxo de Cadastro de Técnico

1. Prestadora acessa a página "Minha Equipe"
2. Clica no botão "Adicionar Técnico"
3. Preenche formulário com informações do técnico (nome, email, telefone, especialidades)
4. Faz upload da foto do técnico (opcional)
5. Confirma o cadastro
6. Sistema gera senha temporária e envia email para o técnico
7. Técnico aparece na lista de técnicos da empresa

### Fluxo de Upload de Logomarca

1. Prestadora acessa a página "Perfil da Empresa"
2. Clica no botão "Alterar Logomarca"
3. Seleciona imagem do computador
4. Visualiza prévia da imagem
5. Confirma o upload
6. Logomarca é atualizada no sistema e exibida no perfil e sidebar

## Integração com o Sistema

### Sidebar

A página de prestadoras é acessível através do sidebar, com um item de menu específico para "Minha Equipe".

### Dashboard

O dashboard para prestadoras inclui um card com resumo da equipe e link direto para a página "Minha Equipe".

### Permissões

A página de prestadoras é acessível apenas para usuários com o perfil "prestadores".

## Design e Estilo Visual

### Componentes Visuais

- **Cards de Técnicos**: Exibem foto, nome, especialidades e status do técnico
- **Tabela de Técnicos**: Exibe informações detalhadas em formato de tabela
- **Modal de Cadastro**: Interface para adicionar ou editar técnicos
- **Área de Upload**: Componente para upload de logomarca com prévia

### Responsividade

A interface é totalmente responsiva:

- **Desktop**: Layout completo com todas as funcionalidades
- **Tablet**: Adaptação dos cards e formulários
- **Mobile**: Reorganização dos elementos para melhor visualização em telas pequenas

## Implementação

Para implementar esta página, siga a documentação detalhada em:

- `docs/guias/Prestadoras/README.md` - Visão geral da implementação
- `docs/guias/Prestadoras/07-Interface-Usuario.md` - Detalhes da implementação da interface

## Manutenção e Extensão

Para adicionar novas funcionalidades à página:

1. Siga o padrão de organização de arquivos
2. Mantenha a consistência visual com o restante do sistema
3. Documente as alterações
4. Atualize as permissões se necessário
