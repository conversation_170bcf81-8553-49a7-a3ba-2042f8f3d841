{{ define "impostos/consulta.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Consulta de Impostos - Shell</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/impostos.css">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&family=Share+Tech+Mono&display=swap" rel="stylesheet">
</head>
<body>
    {{ template "sidebar" . }}
    
    <div class="content-with-sidebar">
        <div class="main-content">
            <div class="page-header">
                <h1><i class="fas fa-calculator"></i> Consulta de Impostos de Combustíveis</h1>
            </div>
            
            <div class="page-content">
                <div class="card-shell">
                    <div class="card-header">
                        <h2>Calculadora de Impostos por Estado</h2>
                    </div>
                    <div class="card-body">
                        <form id="calculadora-impostos-form">
                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="estado">Estado</label>
                                        <select id="estado" class="form-control" required>
                                            <option value="">Selecione o estado</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="combustivel">Tipo de Combustível</label>
                                        <select id="combustivel" class="form-control" required>
                                            <option value="">Selecione o combustível</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="valor-base">Valor Base (R$)</label>
                                        <input type="text" id="valor-base" class="form-control" placeholder="0,00" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-shell-red">
                                    <i class="fas fa-calculator"></i> Calcular Impostos
                                </button>
                            </div>
                            
                            <div class="alert alert-shell-danger mt-3 d-none" id="mensagem-erro">
                                Preencha todos os campos antes de calcular.
                            </div>
                        </form>
                        
                        <div id="resultados" class="mt-4 d-none">
                            <h3 class="mb-3">Resultado do Cálculo</h3>
                            
                            <div class="table-responsive">
                                <table class="table table-shell">
                                    <thead>
                                        <tr>
                                            <th>Imposto</th>
                                            <th>Alíquota</th>
                                            <th>Valor (R$)</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tabela-resultados">
                                        <!-- Os resultados serão inseridos aqui via JavaScript -->
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-active font-weight-bold">
                                            <td colspan="2" class="text-end">Total de Impostos:</td>
                                            <td id="total-impostos">R$ 0,00</td>
                                        </tr>
                                        <tr class="table-active font-weight-bold">
                                            <td colspan="2" class="text-end">Valor Final com Impostos:</td>
                                            <td id="valor-final">R$ 0,00</td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/impostos.js"></script>
    <script src="/static/js/calculadora-impostos.js"></script>
</body>
</html>
{{ end }} 