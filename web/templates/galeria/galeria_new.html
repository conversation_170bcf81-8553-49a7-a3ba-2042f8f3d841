{{ define "galeria/galeria_new.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Galeria de Equipamentos - Rede Tradição</title>
    <link rel="icon" href="/static/images/favicon.ico" type="image/x-icon">

    <!-- Bootstrap e estilos base -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/sidebar.css">
    <link rel="stylesheet" href="/static/css/sidebar-profile.css">
    <link rel="stylesheet" href="/static/css/galeria.css">
    <link rel="stylesheet" href="/static/css/galeria_new.css">

    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">

    <!-- Lightbox para visualização de imagens -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/css/lightbox.min.css">
</head>
<body class="dark-theme">
    <!-- Incluindo o menu lateral -->
    {{ template "layouts/sidebar.html" . }}

    <!-- Conteúdo principal -->
    <div class="content-with-sidebar">
        <div class="main-content">
            <!-- Cabeçalho da página -->
            <div class="page-header d-flex justify-content-between align-items-center mb-4">
                <h1 class="page-title">
                    <i class="fas fa-tools text-shell-yellow me-2"></i> Galeria de Equipamentos
                </h1>
                <div class="page-actions">
                    <button class="btn btn-shell-yellow" id="refreshBtn">
                        <i class="fas fa-sync-alt me-2"></i> Atualizar
                    </button>
                </div>
            </div>

            <!-- Filtros -->
            <div class="filter-container mb-4">
                <div class="card card-dark">
                    <div class="card-header d-flex align-items-center">
                        <i class="fas fa-filter text-shell-yellow me-2"></i>
                        <h5 class="card-title mb-0">Filtros</h5>
                        <button class="btn btn-sm btn-link ms-auto" id="toggleFilters">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    <div class="card-body" id="filterBody">
                        <div class="row g-3">
                            <div class="col-md-6 col-lg-3">
                                <div class="form-group">
                                    <label for="filialSelect" class="form-label">Filial</label>
                                    <select class="form-select form-select-sm" id="filialSelect">
                                        <option value="">Todas as Filiais</option>
                                        {{ range .Branches }}
                                        <option value="{{ .ID }}">{{ .Name }}</option>
                                        {{ end }}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3">
                                <div class="form-group">
                                    <label for="equipmentTypeSelect" class="form-label">Tipo de Equipamento</label>
                                    <select class="form-select form-select-sm" id="equipmentTypeSelect">
                                        <option value="">Todos os Tipos</option>
                                        {{ range .EquipmentTypes }}
                                        <option value="{{ .Value }}">{{ .Label }}</option>
                                        {{ end }}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3">
                                <div class="form-group">
                                    <label for="statusSelect" class="form-label">Status</label>
                                    <select class="form-select form-select-sm" id="statusSelect">
                                        <option value="">Todos os Status</option>
                                        <option value="ativo">Ativo</option>
                                        <option value="manutencao">Em Manutenção</option>
                                        <option value="inativo">Inativo</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3">
                                <div class="form-group">
                                    <label for="searchInput" class="form-label">Buscar</label>
                                    <div class="input-group input-group-sm">
                                        <input type="text" class="form-control" id="searchInput" placeholder="Nome, modelo, série...">
                                        <button class="btn btn-shell-yellow" type="button" id="searchBtn">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Informações da filial selecionada -->
            <div id="filialInfoSection" class="mb-4 d-none">
                <div class="card card-dark">
                    <div class="card-header d-flex align-items-center">
                        <i class="fas fa-building text-shell-yellow me-2"></i>
                        <h5 class="card-title mb-0">Informações da Filial</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h4 id="filialName" class="text-shell-yellow mb-3">Nome da Filial</h4>
                                <p><i class="fas fa-map-marker-alt me-2 text-shell-yellow"></i> <span id="filialAddress">Endereço da Filial</span></p>
                                <p><i class="fas fa-phone me-2 text-shell-yellow"></i> <span id="filialPhone">Telefone da Filial</span></p>
                                <p><i class="fas fa-envelope me-2 text-shell-yellow"></i> <span id="filialEmail">Email da Filial</span></p>
                            </div>
                            <div class="col-md-6">
                                <div class="stats-container">
                                    <div class="stat-card">
                                        <div class="stat-icon"><i class="fas fa-tools"></i></div>
                                        <div class="stat-value" id="totalEquipments">0</div>
                                        <div class="stat-label">Total</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                                        <div class="stat-value" id="activeEquipments">0</div>
                                        <div class="stat-label">Ativos</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-icon"><i class="fas fa-wrench"></i></div>
                                        <div class="stat-value" id="maintenanceEquipments">0</div>
                                        <div class="stat-label">Em Manutenção</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Galeria de Equipamentos -->
            <div class="gallery-container">
                <div class="card card-dark">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-tools text-shell-yellow me-2"></i>
                            <h5 class="card-title mb-0">Equipamentos</h5>
                        </div>
                        <div class="view-options">
                            <button class="btn btn-sm btn-dark active" id="gridViewBtn" title="Visualização em Grade">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="btn btn-sm btn-dark" id="listViewBtn" title="Visualização em Lista">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <!-- Visualização em Grade (padrão) -->
                        <div id="gridView" class="equipment-grid">
                            <!-- Preenchido via JavaScript -->
                            <div class="text-center py-5" id="loadingIndicator" style="grid-column: 1 / -1;">
                                <div class="shell-spinner"></div>
                                <p class="mt-3">Carregando equipamentos...</p>
                            </div>
                            <div class="text-center py-5 d-none" id="noEquipmentsMessage" style="grid-column: 1 / -1;">
                                <div class="empty-state">
                                    <i class="fas fa-exclamation-circle empty-state-icon"></i>
                                    <p>Nenhum equipamento encontrado com os filtros selecionados.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Visualização em Lista (alternativa) -->
                        <div id="listView" class="equipment-list d-none">
                            <div class="table-responsive">
                                <table class="table table-dark table-hover">
                                    <thead>
                                        <tr>
                                            <th>Nome</th>
                                            <th>Tipo</th>
                                            <th>Modelo</th>
                                            <th>Nº Série</th>
                                            <th>Status</th>
                                            <th>Filial</th>
                                            <th>Mídias</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody id="equipmentTableBody">
                                        <!-- Preenchido via JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Detalhes do Equipamento -->
    <div class="modal fade" id="equipmentDetailModal" tabindex="-1" aria-labelledby="equipmentDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="equipmentDetailModalLabel">
                        <i class="fas fa-tools text-shell-yellow me-2"></i>
                        Detalhes do Equipamento
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="equipment-info">
                                <h3 id="equipmentName" class="text-shell-yellow mb-3">Nome do Equipamento</h3>
                                <div class="equipment-id mb-3">
                                    <span class="badge bg-dark border border-shell-yellow text-shell-yellow">
                                        <i class="fas fa-hashtag me-1"></i> ID: <span id="equipmentId">0</span>
                                    </span>
                                </div>
                                <div class="info-grid">
                                    <div class="info-item">
                                        <div class="info-label"><i class="fas fa-tag me-2"></i> Tipo:</div>
                                        <div class="info-value" id="equipmentType">Tipo do Equipamento</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label"><i class="fas fa-cube me-2"></i> Modelo:</div>
                                        <div class="info-value" id="equipmentModel">Modelo do Equipamento</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label"><i class="fas fa-copyright me-2"></i> Marca:</div>
                                        <div class="info-value" id="equipmentBrand">Marca do Equipamento</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label"><i class="fas fa-barcode me-2"></i> Número de Série:</div>
                                        <div class="info-value" id="equipmentSerialNumber">Número de Série</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label"><i class="fas fa-info-circle me-2"></i> Status:</div>
                                        <div class="info-value"><span id="equipmentStatus" class="badge badge-shell">Status</span></div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label"><i class="fas fa-calendar-check me-2"></i> Data de Instalação:</div>
                                        <div class="info-value" id="equipmentInstallationDate">Data de Instalação</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label"><i class="fas fa-history me-2"></i> Última Manutenção:</div>
                                        <div class="info-value" id="equipmentLastMaintenance">Última Manutenção</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label"><i class="fas fa-calendar-alt me-2"></i> Próxima Manutenção:</div>
                                        <div class="info-value" id="equipmentNextMaintenance">Próxima Manutenção</div>
                                    </div>
                                </div>
                                <div class="mt-4">
                                    <h5 class="text-shell-yellow"><i class="fas fa-sticky-note me-2"></i> Observações:</h5>
                                    <div id="equipmentNotes" class="equipment-notes">Observações do Equipamento</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="equipment-media">
                                <h5 class="text-shell-yellow mb-3"><i class="fas fa-photo-video me-2"></i> Mídias do Equipamento</h5>
                                <div id="mediaGallery" class="media-gallery">
                                    <!-- Preenchido via JavaScript -->
                                    <div class="text-center py-4" id="mediaLoadingIndicator">
                                        <div class="shell-spinner"></div>
                                        <p class="mt-3">Carregando mídias...</p>
                                    </div>
                                    <div class="text-center py-4 d-none" id="noMediaMessage">
                                        <div class="empty-state">
                                            <i class="fas fa-photo-video empty-state-icon"></i>
                                            <p>Nenhuma mídia cadastrada para este equipamento.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-shell" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/sidebar.js"></script>
    <script src="/static/js/galeria_new.js"></script>

    <script>
        // Objeto User para uso no JavaScript
        const User = {
            ID: {{ .User.ID }},
            Name: "{{ .User.Name }}",
            Email: "{{ .User.Email }}",
            Role: "{{ .User.Role }}",
            BranchID: {{ if .User.BranchID }}{{ .User.BranchID }}{{ else }}0{{ end }}
        };
    </script>
</body>
</html>
{{ end }}
