# Documentação da Galeria de Equipamentos

## Visão Geral
A Galeria de Equipamentos é um componente que permite visualizar, filtrar e obter detalhes sobre os equipamentos cadastrados no sistema. O componente segue os padrões visuais do projeto, utilizando o tema escuro (dark-theme) e os componentes visuais padronizados como cards, spinners e estados vazios.

## Estrutura de Arquivos
- `web/templates/galeria/galeria.html` - Template HTML principal da galeria
- `web/static/css/galeria.css` - Estilos específicos da galeria
- `web/static/js/galeria.js` - Funcionalidades JavaScript da galeria

## Componentes Principais

### 1. Filtros
Permite filtrar os equipamentos por:
- Filial
- Tipo de Equipamento
- Status
- Busca por texto (nome, modelo, número de série)

### 2. Informações da Filial
Exibe detalhes sobre a filial selecionada:
- Nome, endereço, telefone e email
- Estatísticas de equipamentos (total, ativos, em manutenção)

### 3. Visualização de Equipamentos
Oferece duas formas de visualização:
- **Grade (Grid)**: Exibe cards com imagem e informações básicas
- **Lista (Table)**: Exibe uma tabela com informações detalhadas

### 4. Modal de Detalhes
Exibe informações detalhadas sobre o equipamento selecionado:
- Dados técnicos (tipo, modelo, marca, número de série)
- Status e datas importantes
- Observações
- Galeria de mídias relacionadas ao equipamento

## Estilos e Padrões Visuais

### Cards
Os cards seguem o padrão visual do projeto:
- Classe `card-shell` para o container
- Gradiente de fundo escuro
- Bordas arredondadas
- Efeitos de hover com elevação e borda amarela

### Estados de Carregamento e Vazios
- `shell-spinner` para indicadores de carregamento
- `empty-state` para mensagens de "nenhum item encontrado"

### Cores e Temas
- Tema escuro (`dark-theme`) aplicado ao body
- Cores padrão do projeto (shell-yellow, shell-red)
- Gradientes e efeitos de sombra para profundidade

## Responsividade
O layout é totalmente responsivo:
- Grid adaptável com `minmax` para diferentes tamanhos de tela
- Ajustes específicos para dispositivos móveis
- Reorganização de elementos em telas menores

## Como Utilizar

### Inicialização
A galeria é inicializada automaticamente quando a página é carregada. O JavaScript busca os equipamentos da API e preenche a visualização.

### Filtros
1. Selecione uma filial para ver apenas equipamentos daquela unidade
2. Escolha um tipo de equipamento para filtrar por categoria
3. Selecione um status para ver equipamentos ativos, em manutenção ou inativos
4. Use a busca para encontrar equipamentos por nome, modelo ou número de série

### Visualização
- Clique nos ícones de grade/lista para alternar entre os modos de visualização
- Clique em um equipamento para ver detalhes completos no modal

## Personalização
Para personalizar a galeria:

1. **Adicionar novos filtros**:
   - Adicione novos campos de filtro no HTML
   - Atualize a função de filtro no JavaScript

2. **Modificar o layout dos cards**:
   - Edite a estrutura HTML gerada no JavaScript
   - Ajuste os estilos em `galeria.css`

3. **Adicionar novas funcionalidades**:
   - Implemente novas funções no arquivo `galeria.js`
   - Adicione os elementos de interface correspondentes no HTML

## Integração com a API
A galeria consome os seguintes endpoints:
- `GET /api/equipments` - Lista todos os equipamentos
- `GET /api/equipments/:id` - Obtém detalhes de um equipamento específico
- `GET /api/branches/:id` - Obtém detalhes de uma filial específica
