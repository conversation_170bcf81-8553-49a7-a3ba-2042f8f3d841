{{ define "galeria/galeria.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Postos - Rede Tradição</title>
    <link rel="icon" href="/static/images/favicon.ico" type="image/x-icon">

    <!-- Bootstrap e estilos base -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/sidebar.css">
    <link rel="stylesheet" href="/static/css/sidebar-profile.css">
    <link rel="stylesheet" href="/static/css/galeria.css">

    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">

    <!-- Lightbox para visualização de imagens -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/css/lightbox.min.css">
</head>
<body class="dark-theme">
    <!-- Incluindo o menu lateral -->
    {{ template "layouts/sidebar.html" . }}

    <!-- Conteúdo principal -->
    <div class="content-with-sidebar">
        <div class="main-content">
            <!-- Cabeçalho da página -->
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-gas-pump text-shell-yellow me-2"></i> Postos
                </h1>
                <div class="page-actions">
                    <button class="btn btn-shell-yellow" id="refreshBtn">
                        <i class="fas fa-sync-alt me-2"></i> Atualizar
                    </button>
                </div>
            </div>

            <!-- Filtros e seleção de filial -->
            <div class="filter-section mb-4">
                <div class="card card-shell">
                    <div class="card-header">
                        <i class="fas fa-filter text-shell-yellow me-2"></i>
                        <h2 class="card-title">Filtros</h2>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="filialSelect" class="form-label">Selecione a Filial</label>
                                    <select class="form-select" id="filialSelect">
                                        <option value="">Todas as Filiais</option>
                                        {{ range .Branches }}
                                        <option value="{{ .ID }}">{{ .Name }}</option>
                                        {{ end }}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="equipmentTypeSelect" class="form-label">Tipo de Equipamento</label>
                                    <select class="form-select" id="equipmentTypeSelect">
                                        <option value="">Todos os Tipos</option>
                                        {{ range .EquipmentTypes }}
                                        <option value="{{ .Value }}">{{ .Label }}</option>
                                        {{ end }}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="statusSelect" class="form-label">Status</label>
                                    <select class="form-select" id="statusSelect">
                                        <option value="">Todos os Status</option>
                                        <option value="ativo">Ativo</option>
                                        <option value="manutencao">Em Manutenção</option>
                                        <option value="inativo">Inativo</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="searchInput" class="form-label">Buscar</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="searchInput" placeholder="Nome, modelo, número de série...">
                                        <button class="btn btn-shell-yellow" type="button" id="searchBtn">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Informações da filial selecionada -->
            <div id="filialInfoSection" class="mb-4 d-none">
                <div class="card card-shell">
                    <div class="card-header">
                        <i class="fas fa-building text-shell-yellow me-2"></i>
                        <h2 class="card-title">Informações da Filial</h2>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="filial-info">
                                    <h3 id="filialName">Nome da Filial</h3>
                                    <p><i class="fas fa-map-marker-alt me-2"></i> <span id="filialAddress">Endereço da Filial</span></p>
                                    <p><i class="fas fa-phone me-2"></i> <span id="filialPhone">Telefone da Filial</span></p>
                                    <p><i class="fas fa-envelope me-2"></i> <span id="filialEmail">Email da Filial</span></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="filial-stats">
                                    <div class="stat-item">
                                        <div class="stat-label">Total de Equipamentos</div>
                                        <div class="stat-value" id="totalEquipments">0</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-label">Equipamentos Ativos</div>
                                        <div class="stat-value" id="activeEquipments">0</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-label">Em Manutenção</div>
                                        <div class="stat-value" id="maintenanceEquipments">0</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Galeria de Equipamentos -->
            <div class="gallery-section">
                <div class="card card-shell">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-tools text-shell-yellow me-2"></i>
                                <h2 class="card-title mb-0">Equipamentos do Posto</h2>
                            </div>
                            <div class="card-header-actions">
                                <button class="btn btn-icon" id="gridViewBtn" title="Visualização em Grade" data-bs-toggle="tooltip">
                                    <i class="fas fa-th"></i>
                                </button>
                                <button class="btn btn-icon" id="listViewBtn" title="Visualização em Lista" data-bs-toggle="tooltip">
                                    <i class="fas fa-list"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Visualização em Grade (padrão) -->
                        <div id="gridView" class="equipment-grid">
                            <!-- Preenchido via JavaScript -->
                            <div class="text-center py-5" id="loadingIndicator" style="grid-column: 1 / -1;">
                                <div class="shell-spinner"></div>
                                <p class="mt-3">Carregando equipamentos...</p>
                            </div>
                            <div class="text-center py-5 d-none" id="noEquipmentsMessage" style="grid-column: 1 / -1;">
                                <div class="empty-state">
                                    <i class="fas fa-exclamation-circle empty-state-icon"></i>
                                    <p>Nenhum equipamento encontrado com os filtros selecionados.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Visualização em Lista (alternativa) -->
                        <div id="listView" class="equipment-list d-none">
                            <div class="table-responsive">
                                <table class="table table-shell">
                                    <thead>
                                        <tr>
                                            <th>Nome</th>
                                            <th>Tipo</th>
                                            <th>Modelo</th>
                                            <th>Nº Série</th>
                                            <th>Status</th>
                                            <th>Filial</th>
                                            <th>Mídias</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody id="equipmentTableBody">
                                        <!-- Preenchido via JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Detalhes do Equipamento -->
    <div class="modal fade" id="equipmentDetailModal" tabindex="-1" aria-labelledby="equipmentDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="equipmentDetailModalLabel">
                        <i class="fas fa-tools text-shell-yellow me-2"></i>
                        Detalhes do Equipamento do Posto
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="equipment-info">
                                <h3 id="equipmentName">Nome do Equipamento</h3>
                                <p><strong>Tipo:</strong> <span id="equipmentType">Tipo do Equipamento</span></p>
                                <p><strong>Modelo:</strong> <span id="equipmentModel">Modelo do Equipamento</span></p>
                                <p><strong>Marca:</strong> <span id="equipmentBrand">Marca do Equipamento</span></p>
                                <p><strong>Número de Série:</strong> <span id="equipmentSerialNumber">Número de Série</span></p>
                                <p><strong>Status:</strong> <span id="equipmentStatus" class="badge badge-shell">Status</span></p>
                                <p><strong>Data de Instalação:</strong> <span id="equipmentInstallationDate">Data de Instalação</span></p>
                                <p><strong>Última Manutenção:</strong> <span id="equipmentLastMaintenance">Última Manutenção</span></p>
                                <p><strong>Próxima Manutenção:</strong> <span id="equipmentNextMaintenance">Próxima Manutenção</span></p>
                                <p><strong>Observações:</strong></p>
                                <div id="equipmentNotes" class="equipment-notes">Observações do Equipamento</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="equipment-media">
                                <h4 class="mb-3">Mídias do Equipamento</h4>
                                <div id="mediaGallery" class="media-gallery">
                                    <!-- Preenchido via JavaScript -->
                                    <div class="text-center py-4" id="mediaLoadingIndicator">
                                        <div class="shell-spinner"></div>
                                        <p class="mt-3">Carregando mídias...</p>
                                    </div>
                                    <div class="text-center py-4 d-none" id="noMediaMessage">
                                        <div class="empty-state">
                                            <i class="fas fa-photo-video empty-state-icon"></i>
                                            <p>Nenhuma mídia cadastrada para este equipamento.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-shell" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/sidebar.js"></script>
    <script src="/static/js/galeria.js"></script>

    <script>
        // Objeto User para uso no JavaScript
        const User = {
            ID: {{ .User.ID }},
            Name: "{{ .User.Name }}",
            Email: "{{ .User.Email }}",
            Role: "{{ .User.Role }}",
            BranchID: {{ if .User.BranchID }}{{ .User.BranchID }}{{ else }}0{{ end }}
        };
    </script>
</body>
</html>
{{ end }}
