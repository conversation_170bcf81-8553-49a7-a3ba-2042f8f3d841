{{ define "galeria/galeria_tecnico_new.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Postos Cadastrados - Rede Tradição</title>
    <link rel="icon" href="/static/images/favicon.ico" type="image/x-icon">

    <!-- Bootstrap e estilos base -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/sidebar.css">
    <link rel="stylesheet" href="/static/css/sidebar-profile.css">
    <link rel="stylesheet" href="/static/css/galeria.css">
    <link rel="stylesheet" href="/static/css/galeria_new.css">

    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">

    <style>
        .branch-card {
            background: linear-gradient(145deg, #252525, #1a1a1a);
            border-radius: 12px;
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            border: 1px solid #333;
            transition: all 0.3s ease;
        }

        .branch-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3);
            border-color: var(--shell-yellow);
        }

        .branch-card .card-img-top {
            height: 160px;
            background-color: #151515;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .branch-card .card-img-top .branch-icon {
            font-size: 4rem;
            color: var(--shell-yellow);
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .branch-card:hover .card-img-top .branch-icon {
            transform: scale(1.1);
            opacity: 1;
        }

        .branch-card .card-img-top::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(to right, var(--shell-red), var(--shell-yellow));
            opacity: 0.7;
        }

        .branch-card .card-body {
            padding: 1.5rem;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .branch-card .card-title {
            color: var(--shell-yellow);
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 1.25rem;
            text-align: center;
        }

        .branch-card .card-text {
            color: #ddd;
            margin-bottom: 0.75rem;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
        }

        .branch-card .card-text i {
            color: var(--shell-yellow);
            width: 20px;
            margin-right: 0.75rem;
        }

        .branch-card .card-footer {
            background-color: #1a1a1a;
            border-top: 1px solid #333;
            padding: 1rem;
            display: flex;
            justify-content: center;
        }

        .branch-card .btn-shell {
            background-color: var(--shell-yellow);
            color: #222;
            font-weight: 500;
            padding: 0.5rem 1.5rem;
            border: none;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .branch-card .btn-shell:hover {
            background-color: #e6a700;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .page-title-container {
            text-align: center;
            margin-bottom: 3rem;
        }

        .page-title {
            color: var(--shell-yellow);
            font-weight: 700;
            font-size: 2rem;
            margin-bottom: 1rem;
            display: inline-block;
            position: relative;
        }

        .page-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(to right, var(--shell-red), var(--shell-yellow));
            border-radius: 3px;
        }

        .branches-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
            padding: 1rem;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            grid-column: 1 / -1;
        }

        .empty-state-icon {
            font-size: 4rem;
            color: var(--shell-yellow);
            opacity: 0.7;
            margin-bottom: 1.5rem;
        }

        .empty-state-text {
            color: #adb5bd;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .branches-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 1.5rem;
            }

            .page-title {
                font-size: 1.75rem;
            }
        }

        @media (max-width: 576px) {
            .branches-grid {
                grid-template-columns: 1fr;
                gap: 1.25rem;
            }

            .page-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body class="dark-theme">
    <!-- Incluindo o menu lateral -->
    {{ template "layouts/sidebar.html" . }}

    <!-- Conteúdo principal -->
    <div class="content-with-sidebar">
        <div class="main-content">
            <!-- Título centralizado -->
            <div class="page-title-container">
                <h1 class="page-title">
                    <i class="fas fa-gas-pump me-2"></i> Postos Cadastrados
                </h1>
                <!-- Informação de diagnóstico -->
                <p class="text-muted small">Usuário ID: {{ .UserID }}</p>
            </div>

            <!-- Grid de cards de postos -->
            <div class="branches-grid">
                {{ if eq (len .Branches) 0 }}
                <div class="empty-state">
                    <i class="fas fa-exclamation-circle empty-state-icon"></i>
                    <p class="empty-state-text">Nenhum posto encontrado para o seu perfil.</p>
                </div>
                {{ else }}
                {{ range .Branches }}
                <div class="branch-item">
                    <div class="branch-card">
                        <div class="card-img-top">
                            <div class="branch-icon">
                                <i class="fas fa-gas-pump"></i>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">{{ .Name }}</h5>
                            {{ if .Address }}
                            <p class="card-text">
                                <i class="fas fa-map-marker-alt"></i>
                                {{ .Address }}
                            </p>
                            {{ end }}
                            {{ if .City }}
                            <p class="card-text">
                                <i class="fas fa-city"></i>
                                {{ .City }}{{ if .State }}, {{ .State }}{{ end }}
                            </p>
                            {{ end }}
                            {{ if .Phone }}
                            <p class="card-text">
                                <i class="fas fa-phone"></i>
                                {{ .Phone }}
                            </p>
                            {{ end }}
                        </div>
                        <div class="card-footer">
                            <button type="button" class="btn btn-shell view-branch-equipment" data-branch-id="{{ .ID }}" data-branch-name="{{ .Name }}">
                                <i class="fas fa-tools me-2"></i> Ver Equipamentos
                            </button>
                        </div>
                    </div>
                </div>
                {{ end }}
                {{ end }}
            </div>
        </div>
    </div>

    <!-- Modal para exibir equipamentos da filial -->
    <div class="modal fade" id="branchEquipmentModal" tabindex="-1" aria-labelledby="branchEquipmentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content modal-shell">
                <div class="modal-header">
                    <h5 class="modal-title" id="branchEquipmentModalLabel">
                        <i class="fas fa-tools me-2 text-shell-yellow"></i>
                        Equipamentos do Posto <span id="branchNameInModal"></span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <div id="equipmentLoading" class="text-center py-5">
                        <div class="spinner-border text-shell-yellow" role="status">
                            <span class="visually-hidden">Carregando...</span>
                        </div>
                        <p class="mt-3 text-light">Carregando equipamentos...</p>
                    </div>

                    <div id="equipmentList" class="row g-4" style="display: none;">
                        <!-- Preenchido via JavaScript -->
                    </div>

                    <div id="noEquipmentMessage" class="text-center py-5" style="display: none;">
                        <i class="fas fa-exclamation-circle fa-3x text-shell-yellow mb-3"></i>
                        <p class="text-light">Nenhum equipamento encontrado para este posto.</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-shell" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para exibir detalhes do equipamento -->
    <div class="modal fade" id="equipmentDetailModal" tabindex="-1" aria-labelledby="equipmentDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content modal-shell">
                <div class="modal-header">
                    <h5 class="modal-title" id="equipmentDetailModalLabel">
                        <i class="fas fa-info-circle me-2 text-shell-yellow"></i>
                        Detalhes do Equipamento
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <div id="equipmentDetailLoading" class="text-center py-5">
                        <div class="spinner-border text-shell-yellow" role="status">
                            <span class="visually-hidden">Carregando...</span>
                        </div>
                        <p class="mt-3 text-light">Carregando detalhes...</p>
                    </div>

                    <div id="equipmentDetailContent" style="display: none;">
                        <!-- Preenchido via JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-shell" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/sidebar.js"></script>

    <script>
        // Script para gerenciar a visualização de equipamentos por filial
        document.addEventListener('DOMContentLoaded', function() {
            // Referências aos elementos do DOM
            const branchEquipmentModal = document.getElementById('branchEquipmentModal');
            const branchNameInModal = document.getElementById('branchNameInModal');
            const equipmentLoading = document.getElementById('equipmentLoading');
            const equipmentList = document.getElementById('equipmentList');
            const noEquipmentMessage = document.getElementById('noEquipmentMessage');

            // Referências aos elementos do modal de detalhes
            const equipmentDetailModal = document.getElementById('equipmentDetailModal');
            const equipmentDetailLoading = document.getElementById('equipmentDetailLoading');
            const equipmentDetailContent = document.getElementById('equipmentDetailContent');

            // Instanciar os modais do Bootstrap
            const branchModal = new bootstrap.Modal(branchEquipmentModal);
            const detailModal = new bootstrap.Modal(equipmentDetailModal);

            // Adicionar event listeners aos botões de visualização de equipamentos
            document.querySelectorAll('.view-branch-equipment').forEach(button => {
                button.addEventListener('click', function() {
                    const branchId = this.getAttribute('data-branch-id');
                    const branchName = this.getAttribute('data-branch-name');

                    // Atualizar o título do modal
                    branchNameInModal.textContent = branchName;

                    // Mostrar o indicador de carregamento
                    equipmentLoading.style.display = 'block';
                    equipmentList.style.display = 'none';
                    noEquipmentMessage.style.display = 'none';

                    // Abrir o modal
                    branchModal.show();

                    // Carregar os equipamentos da filial
                    console.log(`Carregando equipamentos para a filial ID: ${branchId}`);
                    fetch(`/api/equipments/filial/${branchId}`)
                        .then(response => {
                            console.log(`Resposta da API: status ${response.status}`);
                            if (!response.ok) {
                                throw new Error(`Erro ao carregar equipamentos: ${response.status} ${response.statusText}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log(`Dados recebidos: ${JSON.stringify(data)}`);
                            // Ocultar o indicador de carregamento
                            equipmentLoading.style.display = 'none';

                            if (data && data.length > 0) {
                                console.log(`Encontrados ${data.length} equipamentos para a filial ID: ${branchId}`);
                                // Limpar a lista de equipamentos
                                equipmentList.innerHTML = '';

                                // Preencher a lista com os equipamentos
                                data.forEach((equipment, index) => {
                                    console.log(`Processando equipamento #${index+1}: ID=${equipment.id}, Nome=${equipment.name}`);

                                    const statusClass = equipment.status === 'ativo' ? 'success' :
                                                       (equipment.status === 'manutencao' ? 'warning' : 'danger');
                                    const statusText = equipment.status === 'ativo' ? 'Ativo' :
                                                      (equipment.status === 'manutencao' ? 'Em Manutenção' : 'Inativo');

                                    const equipmentCard = document.createElement('div');
                                    equipmentCard.className = 'col-md-4';
                                    equipmentCard.innerHTML = `
                                        <div class="card h-100 bg-dark border-secondary">
                                            <div class="card-body">
                                                <h5 class="card-title text-shell-yellow">${equipment.name}</h5>
                                                <p class="card-text text-light">
                                                    <strong>Tipo:</strong> ${formatEquipmentType(equipment.type)}
                                                </p>
                                                <p class="card-text text-light">
                                                    <strong>Modelo:</strong> ${equipment.model || 'N/A'}
                                                </p>
                                                <p class="card-text text-light">
                                                    <strong>Status:</strong>
                                                    <span class="badge bg-${statusClass}">${statusText}</span>
                                                </p>
                                            </div>
                                            <div class="card-footer bg-dark border-secondary">
                                                <button type="button" class="btn btn-sm btn-shell view-equipment-detail"
                                                        data-equipment-id="${equipment.id}">
                                                    <i class="fas fa-info-circle me-1"></i> Detalhes
                                                </button>
                                            </div>
                                        </div>
                                    `;

                                    equipmentList.appendChild(equipmentCard);
                                });

                                // Mostrar a lista de equipamentos
                                equipmentList.style.display = 'flex';
                                console.log('Lista de equipamentos exibida com sucesso');

                                // Adicionar event listeners aos botões de detalhes
                                document.querySelectorAll('.view-equipment-detail').forEach(button => {
                                    button.addEventListener('click', function() {
                                        const equipmentId = this.getAttribute('data-equipment-id');
                                        console.log(`Clique no botão de detalhes para o equipamento ID: ${equipmentId}`);
                                        showEquipmentDetails(equipmentId);
                                    });
                                });
                            } else {
                                console.log(`Nenhum equipamento encontrado para a filial ID: ${branchId}`);
                                // Mostrar mensagem de nenhum equipamento
                                noEquipmentMessage.style.display = 'block';
                            }
                        })
                        .catch(error => {
                            console.error(`Erro ao carregar equipamentos para a filial ID: ${branchId}:`, error);
                            equipmentLoading.style.display = 'none';
                            noEquipmentMessage.style.display = 'block';
                            noEquipmentMessage.querySelector('p').textContent = `Erro ao carregar equipamentos: ${error.message}`;
                        });
                });
            });

            // Função para mostrar detalhes do equipamento
            function showEquipmentDetails(equipmentId) {
                // Mostrar o indicador de carregamento
                equipmentDetailLoading.style.display = 'block';
                equipmentDetailContent.style.display = 'none';

                // Abrir o modal
                detailModal.show();

                // Carregar os detalhes do equipamento
                fetch(`/api/equipments/${equipmentId}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Erro ao carregar detalhes do equipamento');
                        }
                        return response.json();
                    })
                    .then(equipment => {
                        // Ocultar o indicador de carregamento
                        equipmentDetailLoading.style.display = 'none';

                        // Formatar datas
                        const lastMaintenance = equipment.last_maintenance_date ?
                            new Date(equipment.last_maintenance_date).toLocaleDateString('pt-BR') : 'N/A';
                        const installationDate = equipment.installation_date ?
                            new Date(equipment.installation_date).toLocaleDateString('pt-BR') : 'N/A';

                        // Status do equipamento
                        const statusClass = equipment.status === 'ativo' ? 'success' :
                                           (equipment.status === 'manutencao' ? 'warning' : 'danger');
                        const statusText = equipment.status === 'ativo' ? 'Ativo' :
                                          (equipment.status === 'manutencao' ? 'Em Manutenção' : 'Inativo');

                        // Preencher o conteúdo do modal
                        equipmentDetailContent.innerHTML = `
                            <div class="equipment-detail-header mb-4">
                                <h3 class="equipment-detail-title text-shell-yellow">${equipment.name}</h3>
                                <span class="badge bg-${statusClass}">${statusText}</span>
                            </div>

                            <div class="row">
                                <div class="col-md-7">
                                    <div class="equipment-detail-info">
                                        <div class="equipment-detail-item mb-2">
                                            <span class="equipment-detail-label text-light">Tipo:</span>
                                            <span class="equipment-detail-value text-light">${formatEquipmentType(equipment.type)}</span>
                                        </div>
                                        <div class="equipment-detail-item mb-2">
                                            <span class="equipment-detail-label text-light">Modelo:</span>
                                            <span class="equipment-detail-value text-light">${equipment.model || 'N/A'}</span>
                                        </div>
                                        <div class="equipment-detail-item mb-2">
                                            <span class="equipment-detail-label text-light">Número de Série:</span>
                                            <span class="equipment-detail-value text-light">${equipment.serial_number || 'N/A'}</span>
                                        </div>
                                        <div class="equipment-detail-item mb-2">
                                            <span class="equipment-detail-label text-light">Data de Instalação:</span>
                                            <span class="equipment-detail-value text-light">${installationDate}</span>
                                        </div>
                                        <div class="equipment-detail-item mb-2">
                                            <span class="equipment-detail-label text-light">Última Manutenção:</span>
                                            <span class="equipment-detail-value text-light">${lastMaintenance}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-5 text-center">
                                    <img src="/static/images/shell-pump.svg" alt="${equipment.name}" class="img-fluid" style="max-height: 200px;">
                                </div>
                            </div>

                            ${equipment.notes ? `
                            <div class="equipment-detail-notes mt-3">
                                <span class="equipment-detail-label text-light">Observações:</span>
                                <p class="equipment-detail-value text-light">${equipment.notes}</p>
                            </div>
                            ` : ''}
                        `;

                        // Mostrar o conteúdo
                        equipmentDetailContent.style.display = 'block';
                    })
                    .catch(error => {
                        console.error('Erro:', error);
                        equipmentDetailLoading.style.display = 'none';
                        equipmentDetailContent.innerHTML = `
                            <div class="alert alert-danger">
                                Erro ao carregar detalhes do equipamento. Por favor, tente novamente.
                            </div>
                        `;
                        equipmentDetailContent.style.display = 'block';
                    });
            }

            // Função para formatar o tipo de equipamento
            function formatEquipmentType(type) {
                const types = {
                    'bomba': 'Bomba de Combustível',
                    'tanque': 'Tanque de Armazenamento',
                    'compressor': 'Compressor de Ar',
                    'gerador': 'Gerador de Energia',
                    'sistema_pagamento': 'Sistema de Pagamento',
                    'outro': 'Outro'
                };

                return types[type] || type;
            }
        });
    </script>
</body>
</html>
{{ end }}
