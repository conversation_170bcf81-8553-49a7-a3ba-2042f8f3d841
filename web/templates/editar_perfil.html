{{ define "web/templates/editar_perfil.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Editar Perfil - Sistema de Manutenção Shell</title>
    <link rel="stylesheet" href="/static/css/editar_perfil.css" />
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@600;700&family=Share+Tech+Mono&display=swap" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
    {{ template "sidebar" . }}

    <div class="content-with-sidebar">
        <div class="main-content">
            <div class="page-header">
                <h1 class="page-title">Editar Perfil</h1>
                <p class="page-subtitle">Atualize suas informações pessoais e avatar</p>
            </div>

            <div class="page-content">
                <div class="card-shell">
                    <div class="card-header-shell">
                        <h2 class="card-title">Informações Pessoais</h2>
                    </div>
                    <div class="card-body-shell">
                        <form id="profileForm" class="profile-form" enctype="multipart/form-data">
                            <div class="avatar-container">
                                <div class="profile-avatar-large">
                                    {{ if .AvatarURL }}
                                    <img src="{{ .AvatarURL }}" alt="Avatar" class="avatar-image" />
                                    {{ else }}
                                    <span>{{ index .Name 0 | upper }}</span>
                                    {{ end }}
                                </div>
                                <div class="avatar-overlay">
                                    <label for="avatarUpload" class="btn-avatar-edit" title="Alterar Avatar">
                                        <i class="fas fa-camera"></i>
                                    </label>
                                    <input type="file" id="avatarUpload" name="avatar" accept="image/*" style="display:none" />
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="name" class="form-label">Nome</label>
                                <input type="text" id="name" name="name" class="form-control" value="{{ .Name }}" required />
                            </div>

                            <div class="form-group">
                                <label for="email" class="form-label">E-mail</label>
                                <input type="email" id="email" name="email" class="form-control" value="{{ .Email }}" required />
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn-shell-red">Salvar Alterações</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de sucesso -->
    <div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-shell">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="successModalLabel">Sucesso</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    Suas informações foram atualizadas com sucesso!
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-shell-red" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de erro -->
    <div class="modal fade" id="errorModal" tabindex="-1" aria-labelledby="errorModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-shell">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="errorModalLabel">Erro</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body" id="errorMessage">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-shell-red" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/editar_perfil.js"></script>
</body>
</html>
{{ end }}
