{{ define "meuperfil/meuperfil.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Meu Perfil - Rede Tradição</title>
    <link rel="stylesheet" href="/static/css/common.css" />
    <link rel="stylesheet" href="/static/css/styles.css" />
    <link rel="stylesheet" href="/static/css/pagina.css" />
    <link rel="stylesheet" href="/static/css/meuperfil.css" />
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@600;700&family=Share+Tech+Mono&display=swap" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
</head>
<body>
    {{ template "sidebar" . }}

    <div class="content-with-sidebar">
        <main class="main-content" role="main" aria-label="Meu Perfil">
            <header class="page-header">
                <h1 class="page-title">Meu Perfil</h1>
                <p class="page-subtitle">Gerencie suas informações, equipamentos e notificações</p>
            </header>

            <div class="page-content">
                <section class="profile-section card-shell">
                    <h2 class="card-shell-title">Informações Pessoais</h2>
                    <div class="profile-box">
                        <div class="profile-avatar-large" aria-label="Avatar do usuário">
                            <img id="photoPreview" src="/static/images/default-avatar.png" alt="Avatar do usuário" />
                        </div>
                        <form id="profileForm" novalidate>
                            <div class="form-group">
                                <label for="name" class="form-label">Nome</label>
                                <input type="text" id="name" name="name" required aria-required="true" class="form-control" />
                            </div>
                            <div class="form-group">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" id="email" name="email" required aria-required="true" class="form-control" />
                            </div>
                            <div class="form-group">
                                <label for="phone" class="form-label">Telefone</label>
                                <input type="tel" id="phone" name="phone" class="form-control" />
                            </div>
                            <div class="form-group">
                                <label for="department" class="form-label">Departamento</label>
                                <input type="text" id="department" name="department" class="form-control" />
                            </div>
                            <div class="form-group action-buttons">
                                <input type="file" id="photoUpload" accept="image/*" aria-label="Enviar nova foto de perfil" />
                                <button type="button" class="btn-shell" onclick="updatePhoto()">Atualizar Foto</button>
                                <button type="button" class="btn-shell-yellow" onclick="saveProfile()">Salvar Perfil</button>
                            </div>
                        </form>
                    </div>
                </section>

                <section class="security-section card-shell">
                    <h2 class="card-shell-title">Segurança</h2>
                    <form id="passwordForm" novalidate>
                        <div class="form-group">
                            <label for="currentPassword" class="form-label">Senha Atual</label>
                            <div class="input-group">
                                <input type="password" id="currentPassword" name="currentPassword" required aria-required="true" class="form-control" />
                                <button type="button" class="btn-icon" onclick="togglePasswordVisibility('currentPassword')" aria-label="Mostrar ou ocultar senha atual">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="newPassword" class="form-label">Nova Senha</label>
                            <div class="input-group">
                                <input type="password" id="newPassword" name="newPassword" required aria-required="true" class="form-control" />
                                <button type="button" class="btn-icon" onclick="togglePasswordVisibility('newPassword')" aria-label="Mostrar ou ocultar nova senha">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="confirmPassword" class="form-label">Confirmar Nova Senha</label>
                            <div class="input-group">
                                <input type="password" id="confirmPassword" name="confirmPassword" required aria-required="true" class="form-control" />
                                <button type="button" class="btn-icon" onclick="togglePasswordVisibility('confirmPassword')" aria-label="Mostrar ou ocultar confirmação de senha">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <button type="submit" class="btn-shell-red">Alterar Senha</button>
                    </form>
                </section>

                <section class="preferences-section card-shell">
                    <h2 class="card-shell-title">Preferências</h2>
                    <div class="preference-item">
                        <label>
                            <input type="checkbox" data-preference="emailNotifications" />
                            Receber notificações por email
                        </label>
                    </div>
                    <div class="preference-item">
                        <label>
                            <input type="checkbox" data-preference="smsNotifications" />
                            Receber notificações por SMS
                        </label>
                    </div>
                    <div class="preference-item">
                        <label>
                            <input type="checkbox" data-preference="darkMode" />
                            Ativar modo escuro
                        </label>
                    </div>
                    <button type="button" class="btn-shell-yellow" onclick="savePreferences()">Salvar Preferências</button>
                </section>

                <section class="notifications-section card-shell">
                    <h2 class="card-shell-title">Notificações</h2>
                    <div class="notification-list" aria-live="polite" aria-relevant="additions">
                        <!-- Notificações carregadas dinamicamente -->
                    </div>
                </section>

                <section class="equipment-section card-shell">
                    <h2 class="card-shell-title">Equipamentos da Filial</h2>
                    <button type="button" class="btn-shell-yellow" onclick="showModal('equipmentModal')">Ver Equipamentos</button>
                </section>

                <section class="logout-section card-shell">
                    <button type="button" class="btn-shell-red" id="logoutBtn" onclick="logout()">Logout</button>
                </section>
            </div>
        </main>
    </div>

    <!-- Modal Equipamentos -->
    <div class="modal fade modal-shell" id="equipmentModal" tabindex="-1" aria-labelledby="equipmentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="equipmentModalLabel">Equipamentos da Filial</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <div class="equipment-table-container table-responsive">
                        <table class="equipment-table table-dark table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nome</th>
                                    <th>Tipo</th>
                                    <th>Modelo</th>
                                    <th>Número de Série</th>
                                    <th>Status</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Conteúdo carregado dinamicamente -->
                            </tbody>
                        </table>
                    </div>
                    <button type="button" class="btn-shell-yellow" onclick="showModal('newEquipmentModal')">Adicionar Equipamento</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Novo Equipamento -->
    <div class="modal fade modal-shell" id="newEquipmentModal" tabindex="-1" aria-labelledby="newEquipmentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-md modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="newEquipmentModalLabel">Novo Equipamento</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form id="equipmentForm" novalidate>
                        <label for="equipmentName" class="form-label">Nome</label>
                        <input type="text" id="equipmentName" name="equipmentName" required class="form-control" />
                        <label for="equipmentType" class="form-label">Tipo</label>
                        <select id="equipmentType" name="equipmentType" required class="form-select">
                            <option value="">Selecione</option>
                            <option value="bomba">Bomba</option>
                            <option value="tanque">Tanque</option>
                            <option value="compressor">Compressor</option>
                            <option value="sistema_pagamento">Sistema de Pagamento</option>
                        </select>
                        <label for="equipmentStatus" class="form-label">Status</label>
                        <select id="equipmentStatus" name="equipmentStatus" required class="form-select">
                            <option value="">Selecione</option>
                            <option value="ativo">Ativo</option>
                            <option value="manutencao">Em Manutenção</option>
                            <option value="inativo">Inativo</option>
                        </select>
                        <button type="button" class="btn-shell-yellow" onclick="saveEquipment()">Salvar Equipamento</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast de Notificações -->
    <div id="toastNotification" class="toast align-items-center text-white bg-light border-0" role="alert" aria-live="assertive" aria-atomic="true" style="position: fixed; bottom: 1rem; right: 1rem; min-width: 250px; z-index: 1080;">
        <div class="d-flex">
            <div class="toast-body">
                <strong id="toastTitle"></strong><br />
                <span id="toastMessage"></span>
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Fechar"></button>
        </div>
    </div>

    <script src="/static/js/minha-conta.js" defer></script>
    <script src="/static/js/minha-conta-novo.js" defer></script>
</body>
</html>
{{ end }}
