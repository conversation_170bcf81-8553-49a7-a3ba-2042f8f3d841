{{ define "modals/order_invoice_modal.html" }}
<!-- <PERSON><PERSON> de Nota Fiscal -->
<div class="modal fade" id="invoiceModal" tabindex="-1" aria-labelledby="invoiceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content bg-dark text-light">
            <div class="modal-header border-secondary">
                <h5 class="modal-title" id="invoiceModalLabel">Anexar Nota Fiscal - Ordem <span id="invoiceModalOrderId"></span></h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fechar"></button>
            </div>
            <div class="modal-body">
                <form id="addInvoiceForm">
                    <input type="hidden" id="invoiceOrderId" name="orderId">
                    
                    <div class="mb-3">
                        <label for="invoiceNumber" class="form-label">Número da Nota Fiscal</label>
                        <input type="text" class="form-control bg-dark text-light border-secondary" id="invoiceNumber" name="invoiceNumber" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="invoiceAmount" class="form-label">Valor Total (R$)</label>
                        <input type="number" step="0.01" min="0" class="form-control bg-dark text-light border-secondary" id="invoiceAmount" name="invoiceAmount" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="invoiceDate" class="form-label">Data de Emissão</label>
                        <input type="date" class="form-control bg-dark text-light border-secondary" id="invoiceDate" name="invoiceDate" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="invoiceFile" class="form-label">Arquivo da Nota Fiscal (PDF)</label>
                        <input type="file" class="form-control bg-dark text-light border-secondary" id="invoiceFile" name="invoiceFile" accept=".pdf">
                        <div class="form-text text-muted">Opcional. Tamanho máximo: 5MB</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="invoiceNotes" class="form-label">Observações</label>
                        <textarea class="form-control bg-dark text-light border-secondary" id="invoiceNotes" name="invoiceNotes" rows="3"></textarea>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-warning">Enviar Nota Fiscal</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const addInvoiceForm = document.getElementById('addInvoiceForm');
        if (addInvoiceForm) {
            addInvoiceForm.addEventListener('submit', async function(event) {
                event.preventDefault();
                
                const orderId = document.getElementById('invoiceOrderId').value;
                const invoiceNumber = document.getElementById('invoiceNumber').value;
                const invoiceAmount = document.getElementById('invoiceAmount').value;
                const invoiceDate = document.getElementById('invoiceDate').value;
                const invoiceNotes = document.getElementById('invoiceNotes').value;
                
                if (!invoiceNumber || !invoiceAmount || !invoiceDate) {
                    showToast('Por favor, preencha todos os campos obrigatórios.', 'warning');
                    return;
                }
                
                try {
                    const formData = new FormData();
                    formData.append('invoice_number', invoiceNumber);
                    formData.append('amount', invoiceAmount);
                    formData.append('issue_date', invoiceDate);
                    formData.append('notes', invoiceNotes);
                    
                    const invoiceFile = document.getElementById('invoiceFile').files[0];
                    if (invoiceFile) {
                        formData.append('file', invoiceFile);
                    }
                    
                    const response = await fetch(`/api/orders/${orderId}/invoice`, {
                        method: 'POST',
                        body: formData
                    });
                    
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.error || `Erro HTTP ${response.status}`);
                    }
                    
                    // Fechar modal e recarregar página
                    const invoiceModal = bootstrap.Modal.getInstance(document.getElementById('invoiceModal'));
                    if (invoiceModal) {
                        invoiceModal.hide();
                    }
                    
                    showToast('Nota fiscal anexada com sucesso!', 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } catch (error) {
                    console.error('Erro ao anexar nota fiscal:', error);
                    showToast(`Erro ao anexar nota fiscal: ${error.message}`, 'error');
                }
            });
        }
    });
</script>
{{ end }}
