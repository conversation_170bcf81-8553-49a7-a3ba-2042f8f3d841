{{ define "modals/calculadora_margem_modal.html" }}
<!-- Modal para Calculadora de Margem -->
<div class="modal fade" id="calculadoraMargemModal" tabindex="-1" aria-labelledby="calculadoraMargemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark text-light">
            <div class="modal-header border-bottom border-danger">
                <h5 class="modal-title text-warning" id="calculadoraMargemModalLabel">
                    <i class="fas fa-calculator me-2"></i>Calculadora de Margem de Lucro
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fechar"></button>
            </div>
            <div class="modal-body">
                <form id="calculadoraMargemForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="precoCusto" class="form-label text-warning">Preço de Custo (R$)</label>
                            <div class="input-group">
                                <span class="input-group-text bg-dark text-light border-secondary">R$</span>
                                <input type="number" class="form-control bg-dark text-light border-secondary" id="precoCusto" min="0.01" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="precoVenda" class="form-label text-warning">Preço de Venda (R$)</label>
                            <div class="input-group">
                                <span class="input-group-text bg-dark text-light border-secondary">R$</span>
                                <input type="number" class="form-control bg-dark text-light border-secondary" id="precoVenda" min="0.01" step="0.01" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label text-warning">Impostos</label>
                            <div class="input-group">
                                <input type="number" class="form-control bg-dark text-light border-secondary" id="impostos" min="0" max="100" step="0.01" value="17.00">
                                <span class="input-group-text bg-dark text-light border-secondary">%</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label text-warning">Custos Operacionais</label>
                            <div class="input-group">
                                <input type="number" class="form-control bg-dark text-light border-secondary" id="custosOperacionais" min="0" max="100" step="0.01" value="8.50">
                                <span class="input-group-text bg-dark text-light border-secondary">%</span>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn-shell-red">
                            <i class="fas fa-calculator me-2"></i>Calcular Margem
                        </button>
                    </div>
                </form>

                <div id="resultadosMargem" class="mt-4" style="display: none;">
                    <h4 class="text-center text-warning mb-3">Resultados da Análise</h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="result-card">
                                <div class="result-icon text-warning">
                                    <i class="fas fa-percentage"></i>
                                </div>
                                <div class="result-title">Margem Bruta</div>
                                <div class="result-value" id="margemBruta">35.00%</div>
                                <div class="result-bar">
                                    <div class="progress-bar bg-warning" id="margemBrutaBar" style="width: 35%"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="result-card">
                                <div class="result-icon text-danger">
                                    <i class="fas fa-percentage"></i>
                                </div>
                                <div class="result-title">Margem Líquida</div>
                                <div class="result-value" id="margemLiquida">19.50%</div>
                                <div class="result-bar">
                                    <div class="progress-bar bg-danger" id="margemLiquidaBar" style="width: 19.5%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="table-responsive mt-4">
                        <table class="table table-dark table-bordered">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Valor (R$)</th>
                                    <th>Porcentagem</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Preço de Venda</td>
                                    <td id="resultVenda">R$ 0,00</td>
                                    <td>100%</td>
                                </tr>
                                <tr>
                                    <td>Preço de Custo</td>
                                    <td id="resultCusto">R$ 0,00</td>
                                    <td id="resultCustoPct">65.00%</td>
                                </tr>
                                <tr>
                                    <td>Impostos</td>
                                    <td id="resultImpostos">R$ 0,00</td>
                                    <td id="resultImpostosPct">17.00%</td>
                                </tr>
                                <tr>
                                    <td>Custos Operacionais</td>
                                    <td id="resultCustosOp">R$ 0,00</td>
                                    <td id="resultCustosOpPct">8.50%</td>
                                </tr>
                                <tr class="table-success">
                                    <td><strong>Lucro Líquido</strong></td>
                                    <td id="resultLucro"><strong>R$ 0,00</strong></td>
                                    <td id="resultLucroPct"><strong>9.50%</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-top border-secondary">
                <button type="button" class="btn-outline-shell" data-bs-dismiss="modal">Fechar</button>
                <button type="button" class="btn-shell-yellow" id="btnNovoCalculo">Novo Cálculo</button>
            </div>
        </div>
    </div>
</div>

<style>
.result-card {
    background-color: rgba(40, 40, 40, 0.8);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.result-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.result-title {
    font-size: 1rem;
    color: #ccc;
    margin-bottom: 5px;
}

.result-value {
    font-family: 'Share Tech Mono', monospace;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.result-bar {
    height: 6px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.btn-shell-red {
    background-color: #ED1C24;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-shell-red:hover {
    background-color: #CE181F;
    transform: translateY(-2px);
}

.btn-shell-yellow {
    background-color: #FDB813;
    color: #333;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-shell-yellow:hover {
    background-color: #e0a100;
    transform: translateY(-2px);
}

.btn-outline-shell {
    background-color: transparent;
    color: #ccc;
    border: 1px solid #555;
    padding: 8px 16px;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-shell:hover {
    color: #FDB813;
    border-color: #FDB813;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculadoraForm = document.getElementById('calculadoraMargemForm');
    const resultadosMargem = document.getElementById('resultadosMargem');
    const btnNovoCalculo = document.getElementById('btnNovoCalculo');
    
    calculadoraForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Obter valores
        const precoCusto = parseFloat(document.getElementById('precoCusto').value);
        const precoVenda = parseFloat(document.getElementById('precoVenda').value);
        const impostos = parseFloat(document.getElementById('impostos').value) / 100;
        const custosOp = parseFloat(document.getElementById('custosOperacionais').value) / 100;
        
        // Calcular resultados
        const margemBruta = ((precoVenda - precoCusto) / precoVenda) * 100;
        const valorImpostos = precoVenda * impostos;
        const valorCustosOp = precoVenda * custosOp;
        const lucroLiquido = precoVenda - precoCusto - valorImpostos - valorCustosOp;
        const margemLiquida = (lucroLiquido / precoVenda) * 100;
        
        // Atualizar UI
        document.getElementById('margemBruta').textContent = margemBruta.toFixed(2) + '%';
        document.getElementById('margemBrutaBar').style.width = Math.min(100, margemBruta) + '%';
        
        document.getElementById('margemLiquida').textContent = margemLiquida.toFixed(2) + '%';
        document.getElementById('margemLiquidaBar').style.width = Math.min(100, margemLiquida) + '%';
        
        document.getElementById('resultVenda').textContent = 'R$ ' + precoVenda.toFixed(2);
        document.getElementById('resultCusto').textContent = 'R$ ' + precoCusto.toFixed(2);
        document.getElementById('resultCustoPct').textContent = ((precoCusto / precoVenda) * 100).toFixed(2) + '%';
        
        document.getElementById('resultImpostos').textContent = 'R$ ' + valorImpostos.toFixed(2);
        document.getElementById('resultImpostosPct').textContent = (impostos * 100).toFixed(2) + '%';
        
        document.getElementById('resultCustosOp').textContent = 'R$ ' + valorCustosOp.toFixed(2);
        document.getElementById('resultCustosOpPct').textContent = (custosOp * 100).toFixed(2) + '%';
        
        document.getElementById('resultLucro').textContent = 'R$ ' + lucroLiquido.toFixed(2);
        document.getElementById('resultLucroPct').textContent = margemLiquida.toFixed(2) + '%';
        
        // Mostrar resultados
        resultadosMargem.style.display = 'block';
        calculadoraForm.style.display = 'none';
    });
    
    btnNovoCalculo.addEventListener('click', function() {
        calculadoraForm.reset();
        calculadoraForm.style.display = 'block';
        resultadosMargem.style.display = 'none';
    });
});
</script>
{{ end }} 