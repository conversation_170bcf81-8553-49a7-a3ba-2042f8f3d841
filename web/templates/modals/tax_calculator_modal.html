{{ define "modals/tax_calculator_modal.html" }}
<div class="modal fade" id="taxCalculatorModal" tabindex="-1" aria-labelledby="taxCalculatorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content bg-dark text-white">
            <div class="modal-header border-secondary">
                <h5 class="modal-title" id="taxCalculatorModalLabel">
                    <i class="fas fa-file-invoice-dollar me-2"></i>Calculadora de Impostos
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="taxCalculatorForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="taxState" class="form-label">Estado</label>
                            <select class="form-select form-select-sm bg-secondary text-white border-dark" id="taxState" required>
                                <option value="">Selecione o estado...</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="taxFuelType" class="form-label">Tipo de Combustível</label>
                            <select class="form-select form-select-sm bg-secondary text-white border-dark" id="taxFuelType" required>
                                <option value="">Selecione o combustível...</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="taxBaseValue" class="form-label">Valor Base (R$)</label>
                        <input type="number" step="0.01" min="0" class="form-control form-control-sm bg-secondary text-white border-dark" id="taxBaseValue" required>
                    </div>

                    <div class="text-end mb-4">
                        <button type="submit" class="shell-btn shell-btn-yellow">
                            <i class="fas fa-calculator me-2"></i>Calcular Impostos
                        </button>
                    </div>
                </form>

                <div id="taxResultContainer" style="display: none;">
                    <h6 class="mb-3">Resultado do Cálculo</h6>
                    <div class="table-responsive">
                        <table class="table table-dark table-hover table-sm">
                            <thead>
                                <tr>
                                    <th>Imposto</th>
                                    <th>Alíquota</th>
                                    <th>Valor (R$)</th>
                                </tr>
                            </thead>
                            <tbody id="taxResultTable">
                            </tbody>
                            <tfoot>
                                <tr class="table-warning text-dark fw-bold">
                                    <td>Total de Impostos</td>
                                    <td id="totalTaxRate">-</td>
                                    <td id="totalTaxValue">-</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="shell-btn shell-btn-grey" data-bs-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>
{{ end }} 