{{ define "modals/order_edit_modal.html" }}
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content modal-shell">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalLabel">Editar Ordem</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
            </div>
            <div class="modal-body">
                <form id="editForm" role="form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="clientName" class="form-label">Cliente</label>
                                <input type="text" class="form-control form-control-shell" 
                                       id="clientName" name="clientName" required 
                                       aria-required="true" aria-describedby="clientNameHelp">
                                <small id="clientNameHelp" class="form-text text-muted">
                                    Nome completo do cliente
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date" class="form-label">Data</label>
                                <input type="date" class="form-control form-control-shell" 
                                       id="date" name="date" required 
                                       aria-required="true" aria-describedby="dateHelp">
                                <small id="dateHelp" class="form-text text-muted">
                                    Data da ordem de serviço
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-control form-control-shell" 
                                        id="status" name="status" required 
                                        aria-required="true" aria-describedby="statusHelp">
                                    <option value="aberta">Aberta</option>
                                    <option value="confirmada">Confirmada</option>
                                    <option value="em_analise">Em Análise</option>
                                    <option value="em_andamento">Em Andamento</option>
                                    <option value="aguardando_peca">Aguardando Peças</option>
                                    <option value="concluida_tecnico">Conclusão Técnica</option>
                                    <option value="validada_filial">Validada pela Filial</option>
                                    <option value="aprovada_gerencia">Aprovada pela Gerência</option>
                                    <option value="concluida">Concluída</option>
                                    <option value="cancelada">Cancelada</option>
                                    <option value="rejeitada">Rejeitada</option>
                                </select>
                                <small id="statusHelp" class="form-text text-muted">
                                    Status atual da ordem
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="priority" class="form-label">Prioridade</label>
                                <select class="form-control form-control-shell" 
                                        id="priority" name="priority" required 
                                        aria-required="true" aria-describedby="priorityHelp">
                                    <option value="baixa">Baixa</option>
                                    <option value="media">Média</option>
                                    <option value="alta">Alta</option>
                                    <option value="urgente">Urgente</option>
                                </select>
                                <small id="priorityHelp" class="form-text text-muted">
                                    Nível de prioridade da ordem
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Descrição do Problema</label>
                        <textarea class="form-control form-control-shell" 
                                id="description" name="description" rows="3" required 
                                aria-required="true" aria-describedby="descriptionHelp"></textarea>
                        <small id="descriptionHelp" class="form-text text-muted">
                            Descreva detalhadamente o problema relatado
                        </small>
                    </div>

                    <div class="mb-3">
                        <label for="solution" class="form-label">Solução</label>
                        <textarea class="form-control form-control-shell" 
                                id="solution" name="solution" rows="3"
                                aria-describedby="solutionHelp"></textarea>
                        <small id="solutionHelp" class="form-text text-muted">
                            Descreva a solução aplicada (se houver)
                        </small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="shell-btn shell-btn-secondary" 
                        onclick="cancelEdit()" aria-label="Cancelar edição">
                    Cancelar
                </button>
                <button type="button" class="shell-btn shell-btn-primary" 
                        onclick="saveEdit()" aria-label="Salvar alterações">
                    Salvar
                </button>
            </div>
        </div>
    </div>
</div>
{{ end }} 