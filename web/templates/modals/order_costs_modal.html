{{ define "modals/order_costs_modal.html" }}
<div class="modal fade" id="costsModal" tabindex="-1" aria-labelledby="costsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content bg-dark text-white">
            <div class="modal-header border-secondary">
                <h5 class="modal-title" id="costsModalLabel">Custos da Ordem <span id="costModalOrderId"></span></h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="costOrderId">

                <h6>Custos Registrados</h6>
                <div id="costsContainer" class="mb-4" style="max-height: 250px; overflow-y: auto;">
                    <p class="text-muted">Carregando custos...</p>
                </div>
                <hr class="border-secondary">

                <h6>Adicionar Novo Item de Custo</h6>
                <form id="addCostForm">
                    <div class="mb-3">
                        <label for="costDescription" class="form-label">Descrição</label>
                        <input type="text" class="form-control form-control-sm bg-dark text-white border-secondary" id="costDescription" required>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="costQuantity" class="form-label">Quantidade</label>
                            <input type="number" step="0.01" class="form-control form-control-sm bg-dark text-white border-secondary" id="costQuantity" required value="1">
                        </div>
                        <div class="col-md-6">
                            <label for="costUnitPrice" class="form-label">Valor Unitário (R$)</label>
                            <input type="number" step="0.01" class="form-control form-control-sm bg-dark text-white border-secondary" id="costUnitPrice" required>
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="submit" class="shell-btn shell-btn-yellow">
                            <i class="fas fa-plus"></i> Adicionar Item
                        </button>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="shell-btn shell-btn-grey" data-bs-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>
{{ end }}