{{/* Template para uma nova página */}}

{{ define "novapagina/novapagina.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .Title }}</title>
    <link rel="icon" href="/static/images/favicon.ico" type="image/x-icon">

    <!-- Bootstrap e estilos base -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/base_layout.css">

    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">

    <style>
        .novapagina-container {
            padding: 20px;
        }

        .novapagina-header {
            margin-bottom: 30px;
        }

        .novapagina-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .novapagina-card {
            background: rgba(0, 0, 0, 0.5);
            border: 2px solid var(--shell-yellow);
            border-radius: 10px;
            padding: 20px;
            transition: transform 0.3s ease;
        }

        .novapagina-card:hover {
            transform: translateY(-5px);
        }

        .novapagina-card h3 {
            color: var(--shell-yellow);
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <!-- Incluindo o menu lateral independente -->
    {{ template "sidebar" . }}

    <!-- Conteúdo principal -->
    <div class="content-with-sidebar">
        <div class="main-content">
            <div class="novapagina-container">
                <div class="novapagina-header">
                    <h1 class="page-title">Nova Página</h1>
                    <p>Bem-vindo à sua nova página personalizada</p>
                </div>

                <div class="novapagina-content">
                    <div class="novapagina-card">
                        <h3>Card 1</h3>
                        <p>Conteúdo do primeiro card</p>
                    </div>

                    <div class="novapagina-card">
                        <h3>Card 2</h3>
                        <p>Conteúdo do segundo card</p>
                    </div>

                    <div class="novapagina-card">
                        <h3>Card 3</h3>
                        <p>Conteúdo do terceiro card</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts comuns -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Nova página carregada!');
        });
    </script>
</body>
</html>
{{ end }}