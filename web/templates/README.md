# Regras de Templates do Gin

## Estrutura de Diretórios
```
web/
├── static/
│   ├── css/
│   │   ├── common.css
│   │   └── [modulo].css
│   ├── js/
│   │   ├── common.js
│   │   └── [modulo].js
│   └── images/
└── templates/
    ├── layouts/
    │   └── partials/
    │       ├── header.html
    │       ├── footer.html
    │       └── sidebar.html
    └── [modulos]/
        └── [pagina].html
```

## Regras Obrigatórias

1. **Definição de Templates**
```html
{{ define "modulo/pagina.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tí<PERSON><PERSON> da Página</title>
    <!-- CSS e JS em arquivos separados -->
</head>
<body>
    <!-- Conteúdo -->
</body>
</html>
{{ end }}
```

2. **Inclusão de Componentes**
```html
{{ template "sidebar" . }}
```

3. **Arquivos Estáticos**
- CSS em `/static/css/`
- JavaScript em `/static/js/`
- Imagens em `/static/images/`

4. **Checklist de Template**
- [ ] Nome único e completo
- [ ] HTML completo com DOCTYPE
- [ ] Meta tags viewport e charset
- [ ] CSS em arquivos separados
- [ ] JavaScript em arquivos separados
- [ ] Componentes incluídos corretamente

## Proibições
1. NÃO usar herança de templates (não existe extends)
2. NÃO definir templates aninhados
3. NÃO misturar CSS/JS inline com o HTML
4. NÃO omitir DOCTYPE ou meta tags

## Renderização no Gin
```go
c.HTML(http.StatusOK, "modulo/pagina.html", gin.H{
    "title": "Título",
    "data": dados,
})
```