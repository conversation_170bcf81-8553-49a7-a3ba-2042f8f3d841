package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// SetupTaxCalculatorRoutes configura as rotas da calculadora de impostos
func SetupTaxCalculatorRoutes(router *gin.Engine) {
	taxCalculator := router.Group("/calculadora-impostos")
	{
		taxCalculator.GET("/", func(c *gin.Context) {
			c.HTML(http.StatusOK, "tax-calculator.html", gin.H{
				"title": "Calculadora de Impostos - Shell",
			})
		})
	}
}
