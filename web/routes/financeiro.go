package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// SetupFinanceiroCalculadoraRoutes configura as rotas da calculadora do módulo financeiro
func SetupFinanceiroCalculadoraRoutes(router *gin.Engine) {
	financeiro := router.Group("/financeiro")
	{
		// Painel principal
		financeiro.GET("/", func(c *gin.Context) {
			c.HTML(http.StatusOK, "financeiro/painel.html", gin.H{
				"title": "Painel Financeiro - Shell",
			})
		})

		// Calculadora de Impostos
		financeiro.GET("/calculadora-impostos", func(c *gin.Context) {
			c.HTML(http.StatusOK, "financeiro/calculadora-impostos.html", gin.H{
				"title": "Calculadora de Impostos - Shell",
			})
		})

		// Calculadora de Margem
		financeiro.GET("/calculadora-margem", func(c *gin.Context) {
			c.HTML(http.StatusOK, "financeiro/calculadora-margem.html", gin.H{
				"title": "Calculadora de Margem - Shell",
			})
		})

		// Rota alternativa para calculadora de impostos usando a função CalculadoraImpostos
		router.GET("/financeiro/calculadora-impostos", CalculadoraImpostos)
	}
}

// CalculadoraImpostos renderiza a página da calculadora de impostos
func CalculadoraImpostos(c *gin.Context) {
	c.HTML(http.StatusOK, "financeiro/calculadora-impostos.html", gin.H{
		"title": "Calculadora de Impostos",
	})
}

// Comentário: A rota para CalculadoraImpostos já foi adicionada dentro da função SetupFinanceiroRoutes
