package routes

import (
	"tradicao/internal/controllers"

	"github.com/gin-gonic/gin"
)

// SetupFinanceiroRoutes configura as rotas relacionadas ao financeiro
func SetupFinanceiroRoutes(router *gin.Engine) {
	financeiroController := controllers.NewFinanceiroController()

	financeiroGroup := router.Group("/financeiro")
	{
		financeiroGroup.GET("/", financeiroController.Painel)
		financeiroGroup.GET("/calculadora", financeiroController.Calculadora)
	}
}
