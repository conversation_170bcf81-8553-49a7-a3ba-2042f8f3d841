package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// SetupExemploRoutes configura a rota para a página de exemplo
func SetupExemploRoutes(router *gin.Engine) {
	// Rota para a página de exemplo
	router.GET("/exemplo", func(c *gin.Context) {
		c.HTML(http.StatusOK, "exemplos/exemplo-page.html", gin.H{
			"title":      "Página de Exemplo - Rede Tradição Shell",
			"ActivePage": "exemplo", // Para destacar o item correto no sidebar
		})
	})
}
