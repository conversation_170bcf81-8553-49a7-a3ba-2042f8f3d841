package routes

import (
	"net/http"

	"tradicao/internal/controllers"
	"tradicao/internal/middlewares"
	"tradicao/internal/repository"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// SetupRoutes configura todas as rotas web
func SetupRoutes(router *gin.Engine) {
	// Rota para a página de exemplo
	router.GET("/exemplo", func(c *gin.Context) {
		c.HTML(http.StatusOK, "exemplos/exemplo-page.html", gin.H{
			"title": "Página de Exemplo - Rede Tradição Shell",
		})
	})

	// Criar serviços necessários
	userRepo := repository.NewGormUserRepository()
	authService := services.NewAuthService(userRepo)

	// Controladores
	// Armazenar em variáveis para uso posterior
	_ = controllers.NewAuthController(authService)
	_ = controllers.NewDashboardController()
	_ = controllers.NewUsuarioController()
	_ = controllers.NewClienteController()
	financeiroController := controllers.NewFinanceiroController()
	impostosController := controllers.NewImpostosController()

	// Grupo de rotas autenticadas
	autenticadas := router.Group("/")
	autenticadas.Use(middlewares.AuthMiddleware())
	{
		// Rotas Financeiras
		autenticadas.GET("/financeiro", financeiroController.Painel)
		autenticadas.GET("/financeiro/calculadora", financeiroController.Calculadora)
		autenticadas.GET("/financeiro/impostos", impostosController.ConsultaImpostos)
	}
}
