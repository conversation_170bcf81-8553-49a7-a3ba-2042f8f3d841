// Código para integrar o sistema de gerenciamento de vínculos
// Adicione este código ao arquivo cmd/main.go após a inicialização dos outros serviços e antes da configuração das rotas

// Inicializar repositório de branch (se ainda não estiver inicializado)
branchRepo := repository.NewBranchRepository(db)

// Inicializar repositório de service provider
serviceProviderRepo := repository.NewServiceProviderRepository(db)

// Inicializar repositório de técnicos (se ainda não estiver inicializado)
technicianRepo := repository.NewTechnicianRepository(db)

// Inicializar serviço de gerenciamento de vínculos
linkManagementService := services.NewLinkManagementService(
    db,
    branchRepo,
    technicianRepo,
    serviceProviderRepo,
)

// Inicializar controlador de gerenciamento de vínculos
linkManagementController := controllers.NewLinkManagementController(linkManagementService)

// Configurar rotas de gerenciamento de vínculos
routes.SetupLinkManagementRoutes(router, linkManagementController, technicianOrderService)
