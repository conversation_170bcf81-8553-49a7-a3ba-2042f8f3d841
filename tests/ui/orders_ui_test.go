package ui

import (
	"fmt"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/tebeka/selenium"
	"github.com/tebeka/selenium/chrome"
)

// TestOrdersUI testa a interface de usuário da página de ordens
func TestOrdersUI(t *testing.T) {
	// Verificar se os testes de UI devem ser executados
	if os.Getenv("RUN_UI_TESTS") != "true" {
		t.<PERSON><PERSON>("Testes de UI desativados. Defina RUN_UI_TESTS=true para executá-los.")
	}

	// Configurações do Selenium
	const (
		seleniumPath     = "/usr/local/bin/selenium-server.jar"
		chromeDriverPath = "/usr/local/bin/chromedriver"
		port             = 8080
	)

	opts := []selenium.ServiceOption{
		selenium.ChromeDriver(chromeDriverPath),
		selenium.Output(os.Stderr),
	}

	selenium.SetDebug(true)
	service, err := selenium.NewSeleniumService(seleniumPath, port, opts...)
	if err != nil {
		t.Fatalf("Erro ao iniciar o serviço Selenium: %v", err)
	}
	defer service.Stop()

	// Configurar capacidades do navegador
	caps := selenium.Capabilities{
		"browserName": "chrome",
	}

	// Configurar opções do Chrome
	chromeCaps := chrome.Capabilities{
		Args: []string{
			"--headless",
			"--no-sandbox",
			"--disable-dev-shm-usage",
			"--disable-gpu",
			"--window-size=1920,1080",
		},
	}
	caps.AddChrome(chromeCaps)

	// Criar uma nova instância do WebDriver
	wd, err := selenium.NewRemote(caps, fmt.Sprintf("http://localhost:%d/wd/hub", port))
	if err != nil {
		t.Fatalf("Erro ao criar sessão remota: %v", err)
	}
	defer wd.Quit()

	// Definir timeout implícito
	wd.SetImplicitWaitTimeout(10 * time.Second)

	// Teste 1: Acessar a página de login
	t.Run("Acessar página de login", func(t *testing.T) {
		err = wd.Get("http://localhost:5000/login")
		if err != nil {
			t.Fatalf("Erro ao acessar a página de login: %v", err)
		}

		// Verificar título da página
		title, err := wd.Title()
		if err != nil {
			t.Fatalf("Erro ao obter título da página: %v", err)
		}
		if title != "Login - Shell Tradição" {
			t.Errorf("Título da página incorreto. Esperado: 'Login - Shell Tradição', Obtido: '%s'", title)
		}
	})

	// Teste 2: Fazer login
	t.Run("Fazer login", func(t *testing.T) {
		// Encontrar campo de email
		emailField, err := wd.FindElement(selenium.ByCSSSelector, "input[name='email']")
		if err != nil {
			t.Fatalf("Erro ao encontrar campo de email: %v", err)
		}
		err = emailField.SendKeys("<EMAIL>")
		if err != nil {
			t.Fatalf("Erro ao preencher campo de email: %v", err)
		}

		// Encontrar campo de senha
		passwordField, err := wd.FindElement(selenium.ByCSSSelector, "input[name='password']")
		if err != nil {
			t.Fatalf("Erro ao encontrar campo de senha: %v", err)
		}
		err = passwordField.SendKeys("senha123")
		if err != nil {
			t.Fatalf("Erro ao preencher campo de senha: %v", err)
		}

		// Clicar no botão de login
		loginButton, err := wd.FindElement(selenium.ByCSSSelector, "button[type='submit']")
		if err != nil {
			t.Fatalf("Erro ao encontrar botão de login: %v", err)
		}
		err = loginButton.Click()
		if err != nil {
			t.Fatalf("Erro ao clicar no botão de login: %v", err)
		}

		// Aguardar redirecionamento para a dashboard
		time.Sleep(2 * time.Second)

		// Verificar se foi redirecionado para a dashboard
		currentURL, err := wd.CurrentURL()
		if err != nil {
			t.Fatalf("Erro ao obter URL atual: %v", err)
		}
		if currentURL != "http://localhost:5000/dashboard" {
			t.Errorf("Redirecionamento incorreto. Esperado: 'http://localhost:5000/dashboard', Obtido: '%s'", currentURL)
		}
	})

	// Teste 3: Acessar página de ordens
	t.Run("Acessar página de ordens", func(t *testing.T) {
		// Clicar no link de ordens no menu lateral
		ordersLink, err := wd.FindElement(selenium.ByCSSSelector, "a[href='/orders']")
		if err != nil {
			t.Fatalf("Erro ao encontrar link de ordens: %v", err)
		}
		err = ordersLink.Click()
		if err != nil {
			t.Fatalf("Erro ao clicar no link de ordens: %v", err)
		}

		// Aguardar carregamento da página
		time.Sleep(2 * time.Second)

		// Verificar se está na página de ordens
		currentURL, err := wd.CurrentURL()
		if err != nil {
			t.Fatalf("Erro ao obter URL atual: %v", err)
		}
		if currentURL != "http://localhost:5000/orders" {
			t.Errorf("URL incorreta. Esperado: 'http://localhost:5000/orders', Obtido: '%s'", currentURL)
		}

		// Verificar se a tabela de ordens está presente
		_, err = wd.FindElement(selenium.ByCSSSelector, "#ordersTable")
		if err != nil {
			t.Fatalf("Tabela de ordens não encontrada: %v", err)
		}
	})

	// Teste 4: Criar nova ordem
	t.Run("Criar nova ordem", func(t *testing.T) {
		// Clicar no botão "Nova Ordem"
		newOrderButton, err := wd.FindElement(selenium.ByCSSSelector, "button.shell-btn-primary")
		if err != nil {
			t.Fatalf("Erro ao encontrar botão 'Nova Ordem': %v", err)
		}
		err = newOrderButton.Click()
		if err != nil {
			t.Fatalf("Erro ao clicar no botão 'Nova Ordem': %v", err)
		}

		// Aguardar carregamento do formulário
		time.Sleep(2 * time.Second)

		// Verificar se está na página de criação de ordem
		currentURL, err := wd.CurrentURL()
		if err != nil {
			t.Fatalf("Erro ao obter URL atual: %v", err)
		}
		if currentURL != "http://localhost:5000/orders/new" {
			t.Errorf("URL incorreta. Esperado: 'http://localhost:5000/orders/new', Obtido: '%s'", currentURL)
		}

		// Preencher formulário
		// Cliente
		clientField, err := wd.FindElement(selenium.ByCSSSelector, "input[name='client_name']")
		if err != nil {
			t.Fatalf("Erro ao encontrar campo de cliente: %v", err)
		}
		err = clientField.SendKeys("Cliente de Teste Automatizado")
		if err != nil {
			t.Fatalf("Erro ao preencher campo de cliente: %v", err)
		}

		// Descrição
		descField, err := wd.FindElement(selenium.ByCSSSelector, "textarea[name='description']")
		if err != nil {
			t.Fatalf("Erro ao encontrar campo de descrição: %v", err)
		}
		err = descField.SendKeys("Descrição de teste criada por teste automatizado")
		if err != nil {
			t.Fatalf("Erro ao preencher campo de descrição: %v", err)
		}

		// Selecionar status
		statusSelect, err := wd.FindElement(selenium.ByCSSSelector, "select[name='status']")
		if err != nil {
			t.Fatalf("Erro ao encontrar campo de status: %v", err)
		}
		statusSelect.Click()
		pendingOption, err := wd.FindElement(selenium.ByCSSSelector, "option[value='pending']")
		if err != nil {
			t.Fatalf("Erro ao encontrar opção 'pending': %v", err)
		}
		pendingOption.Click()

		// Enviar formulário
		submitButton, err := wd.FindElement(selenium.ByCSSSelector, "button[type='submit']")
		if err != nil {
			t.Fatalf("Erro ao encontrar botão de envio: %v", err)
		}
		err = submitButton.Click()
		if err != nil {
			t.Fatalf("Erro ao clicar no botão de envio: %v", err)
		}

		// Aguardar redirecionamento
		time.Sleep(2 * time.Second)

		// Verificar se voltou para a lista de ordens
		currentURL, err = wd.CurrentURL()
		if err != nil {
			t.Fatalf("Erro ao obter URL atual: %v", err)
		}
		if currentURL != "http://localhost:5000/orders" {
			t.Errorf("URL incorreta após criação. Esperado: 'http://localhost:5000/orders', Obtido: '%s'", currentURL)
		}

		// Verificar se a ordem foi criada (deve aparecer na tabela)
		pageSource, err := wd.PageSource()
		if err != nil {
			t.Fatalf("Erro ao obter código-fonte da página: %v", err)
		}
		if !strings.Contains(pageSource, "Cliente de Teste Automatizado") {
			t.Errorf("Nova ordem não encontrada na tabela")
		}
	})

	// Teste 5: Editar ordem
	t.Run("Editar ordem", func(t *testing.T) {
		// Encontrar botão de edição da primeira ordem
		editButton, err := wd.FindElement(selenium.ByCSSSelector, "button.shell-btn-info")
		if err != nil {
			t.Fatalf("Erro ao encontrar botão de edição: %v", err)
		}
		err = editButton.Click()
		if err != nil {
			t.Fatalf("Erro ao clicar no botão de edição: %v", err)
		}

		// Aguardar abertura do modal
		time.Sleep(2 * time.Second)

		// Verificar se o modal está aberto
		modal, err := wd.FindElement(selenium.ByCSSSelector, ".modal.show")
		if err != nil {
			t.Fatalf("Modal de edição não encontrado: %v", err)
		}

		// Modificar descrição
		descField, err := modal.FindElement(selenium.ByCSSSelector, "textarea[name='description']")
		if err != nil {
			t.Fatalf("Erro ao encontrar campo de descrição no modal: %v", err)
		}
		descField.Clear()
		err = descField.SendKeys("Descrição atualizada pelo teste automatizado")
		if err != nil {
			t.Fatalf("Erro ao atualizar campo de descrição: %v", err)
		}

		// Salvar alterações
		saveButton, err := modal.FindElement(selenium.ByCSSSelector, "button.btn-primary")
		if err != nil {
			t.Fatalf("Erro ao encontrar botão de salvar: %v", err)
		}
		err = saveButton.Click()
		if err != nil {
			t.Fatalf("Erro ao clicar no botão de salvar: %v", err)
		}

		// Aguardar fechamento do modal
		time.Sleep(2 * time.Second)

		// Verificar se a ordem foi atualizada
		pageSource, err := wd.PageSource()
		if err != nil {
			t.Fatalf("Erro ao obter código-fonte da página: %v", err)
		}
		if !strings.Contains(pageSource, "Descrição atualizada pelo teste automatizado") {
			t.Errorf("Descrição atualizada não encontrada na tabela")
		}
	})

	// Teste 6: Excluir ordem
	t.Run("Excluir ordem", func(t *testing.T) {
		// Encontrar botão de exclusão da primeira ordem
		deleteButton, err := wd.FindElement(selenium.ByCSSSelector, "button.shell-btn-danger")
		if err != nil {
			t.Fatalf("Erro ao encontrar botão de exclusão: %v", err)
		}
		err = deleteButton.Click()
		if err != nil {
			t.Fatalf("Erro ao clicar no botão de exclusão: %v", err)
		}

		// Aguardar abertura do modal de confirmação
		time.Sleep(2 * time.Second)

		// Verificar se o modal está aberto
		confirmModal, err := wd.FindElement(selenium.ByCSSSelector, ".modal.show")
		if err != nil {
			t.Fatalf("Modal de confirmação não encontrado: %v", err)
		}

		// Confirmar exclusão
		confirmButton, err := confirmModal.FindElement(selenium.ByCSSSelector, "button.btn-danger")
		if err != nil {
			t.Fatalf("Erro ao encontrar botão de confirmação: %v", err)
		}
		err = confirmButton.Click()
		if err != nil {
			t.Fatalf("Erro ao clicar no botão de confirmação: %v", err)
		}

		// Aguardar processamento
		time.Sleep(2 * time.Second)

		// Verificar se a ordem foi removida
		pageSource, err := wd.PageSource()
		if err != nil {
			t.Fatalf("Erro ao obter código-fonte da página: %v", err)
		}
		if strings.Contains(pageSource, "Cliente de Teste Automatizado") {
			t.Errorf("Ordem ainda presente na tabela após exclusão")
		}
	})
}
