# Testes Automatizados - Projeto Shell Tradição

Este diretório contém os testes automatizados para o projeto Shell Tradição. Os testes são organizados em diferentes categorias para garantir a qualidade e o funcionamento correto do sistema.

## Estrutura de Testes

- **handlers/**: Testes de API e endpoints HTTP
- **integration/**: Testes de integração entre componentes
- **ui/**: Testes de interface de usuário (Selenium)

## Pré-requisitos

- Go 1.16 ou superior
- PostgreSQL (para testes que usam banco de dados)
- Selenium WebDriver (para testes de UI)
- ChromeDriver (para testes de UI com Chrome)

## Como Executar os Testes

### Todos os Testes

Para executar todos os testes do projeto, use o script:

```bash
./run_tests_all.sh
```

Este script executa testes unitários, de integração e de API.

### Testes de API

Para executar apenas os testes de API:

```bash
cd tests/handlers
go test -v ./...
```

### Testes de Integração

Para executar apenas os testes de integração:

```bash
cd tests/integration
go test -v ./...
```

### Testes de UI

Para executar os testes de interface de usuário:

1. Certifique-se de que o servidor está rodando:
   ```bash
   go run cmd/main.go
   ```

2. Em outro terminal, execute:
   ```bash
   ./run_ui_tests.sh
   ```

## Configuração de Ambiente de Teste

Os testes usam as seguintes variáveis de ambiente:

- `GIN_MODE=test`: Define o modo de teste para o Gin
- `DB_TEST=true`: Usa banco de dados de teste
- `RUN_UI_TESTS=true`: Habilita testes de UI (opcional)

## Escrevendo Novos Testes

### Testes de API

1. Crie um arquivo `*_test.go` em `tests/handlers/`
2. Use o pacote `handlers_test`
3. Utilize o framework Gin para simular requisições HTTP
4. Use `assert` para verificações

Exemplo:

```go
package handlers_test

import (
    "net/http"
    "net/http/httptest"
    "testing"

    "github.com/gin-gonic/gin"
    "github.com/stretchr/testify/assert"
)

func TestEndpoint(t *testing.T) {
    gin.SetMode(gin.TestMode)
    router := gin.Default()

    // Configurar rotas

    req, _ := http.NewRequest("GET", "/api/endpoint", nil)
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)

    assert.Equal(t, http.StatusOK, w.Code)
}
```

### Testes de Integração

1. Crie um arquivo `*_test.go` em `tests/integration/`
2. Teste fluxos completos que envolvem múltiplos componentes
3. Use mocks quando necessário

### Testes de UI

1. Crie um arquivo `*_test.go` em `tests/ui/`
2. Use o pacote Selenium para Go
3. Teste interações do usuário com a interface

## Melhores Práticas

1. Mantenha os testes independentes
2. Use nomes descritivos para funções de teste
3. Organize os testes em subgrupos com `t.Run()`
4. Limpe dados de teste após a execução
5. Use mocks para isolar componentes
6. Verifique tanto casos de sucesso quanto de erro

## Testes de Performance

Para garantir que o sistema atenda aos requisitos de desempenho, também implementamos testes de performance:

1. Use a ferramenta `go-wrk` para testes de carga:
   ```bash
   go-wrk -d 30 -c 100 http://localhost:8080/api/endpoint
   ```

2. Monitore o tempo de resposta e uso de recursos durante os testes
3. Compare resultados com benchmarks estabelecidos
4. Execute testes de performance após mudanças significativas no código
