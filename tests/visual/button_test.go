package visual

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/chromedp/chromedp"
)

// TestButtonsFunctionality verifica se os botões de uma página estão funcionando corretamente
// Esta função testa se os botões são clicáveis e se produzem algum efeito na página
func TestButtonsFunctionality(t *testing.T) {
	// Criar diretório para screenshots se não existir
	screenshotDir := "screenshots"
	if _, err := os.Stat(screenshotDir); os.IsNotExist(err) {
		os.Mkdir(screenshotDir, 0755)
	}

	// Configurar opções do Chrome
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", true),
		chromedp.Flag("disable-gpu", true),
		chromedp.Flag("no-sandbox", true),
		chromedp.Flag("disable-dev-shm-usage", true),
		chromedp.WindowSize(1280, 800),
	)

	// Criar contexto do navegador
	allocCtx, cancel := chromedp.NewExecAllocator(context.Background(), opts...)
	defer cancel()

	// Criar um logger personalizado que filtra mensagens de erro de cookies
	logger := log.New(os.Stdout, "", log.LstdFlags)
	customLogger := func(format string, args ...interface{}) {
		msg := fmt.Sprintf(format, args...)
		if !strings.Contains(msg, "could not unmarshal event") &&
			!strings.Contains(msg, "cookiePart") {
			logger.Printf(format, args...)
		}
	}

	ctx, cancel := chromedp.NewContext(allocCtx, chromedp.WithLogf(customLogger))
	defer cancel()

	// Definir timeout mais longo para execução lenta
	ctx, cancel = context.WithTimeout(ctx, 180*time.Second)
	defer cancel()

	// Fazer login no sistema
	t.Log("=========================================")
	t.Log("ETAPA 1: Fazendo login no sistema...")
	t.Log("=========================================")
	// Obter credenciais das variáveis de ambiente ou usar valores padrão
	email := os.Getenv("VISUAL_TEST_EMAIL")
	if email == "" {
		email = "<EMAIL>" // Valor padrão
	}

	senha := os.Getenv("VISUAL_TEST_PASSWORD")
	if senha == "" {
		senha = "i1t2@3l4O5" // Valor padrão
	}

	t.Logf("Tentando login com usuário: %s", email)
	err := login(ctx, "http://localhost:8080/login", email, senha)
	if err != nil {
		t.Fatalf("Erro ao fazer login: %v", err)
	}
	t.Log("Login realizado com sucesso!")

	// Obter a página a ser testada a partir da variável de ambiente
	pagePath := os.Getenv("VISUAL_TEST_PAGE")
	if pagePath == "" {
		pagePath = "/financeiro" // Valor padrão para teste de botões
	}

	// Testar os botões da página especificada
	t.Log("=========================================")
	t.Logf("ETAPA 2: Testando botões da página: %s", pagePath)
	t.Log("=========================================")
	testPageButtons(t, ctx, screenshotDir, pagePath)
}

// testPageButtons testa os botões de uma página
func testPageButtons(t *testing.T, ctx context.Context, screenshotDir string, pagePath string) {
	// Navegar para a página especificada
	pageURL := fmt.Sprintf("http://localhost:8080%s", pagePath)
	t.Logf("Navegando para: %s", pageURL)
	t.Log("Aguardando 5 segundos para carregamento completo...")

	var buf []byte
	err := chromedp.Run(ctx,
		chromedp.Navigate(pageURL),
		chromedp.Sleep(5*time.Second), // Aguardar carregamento completo (tempo aumentado)
		chromedp.CaptureScreenshot(&buf),
	)

	if err != nil {
		t.Fatalf("Erro ao navegar para a página %s: %v", pagePath, err)
	}

	// Salvar screenshot inicial
	filename := filepath.Join(screenshotDir, fmt.Sprintf("buttons_test_initial_%s.png", strings.ReplaceAll(strings.Trim(pagePath, "/"), "/", "_")))
	if err := os.WriteFile(filename, buf, 0644); err != nil {
		t.Fatalf("Erro ao salvar screenshot: %v", err)
	}
	t.Logf("Screenshot inicial salvo em: %s", filename)

	// Encontrar todos os botões na página
	t.Log("Procurando botões na página...")
	var buttonSelectors []string
	err = chromedp.Run(ctx,
		chromedp.Evaluate(`
			Array.from(document.querySelectorAll('button, .btn, .shell-btn, [role="button"], a.btn, input[type="button"], input[type="submit"]'))
				.filter(el => {
					const style = window.getComputedStyle(el);
					return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
				})
				.map(el => {
					// Criar um seletor único para cada botão
					if (el.id) return '#' + el.id;
					if (el.className) {
						const classes = Array.from(el.classList).join('.');
						return el.tagName.toLowerCase() + '.' + classes;
					}
					return el.tagName.toLowerCase();
				})
		`, &buttonSelectors),
	)

	if err != nil {
		t.Fatalf("Erro ao encontrar botões na página: %v", err)
	}

	t.Logf("Encontrados %d botões na página", len(buttonSelectors))
	t.Log("=========================================")

	if len(buttonSelectors) == 0 {
		t.Logf("Nenhum botão encontrado na página. Verificando elementos clicáveis...")

		// Tentar encontrar elementos clicáveis mais genéricos
		err = chromedp.Run(ctx,
			chromedp.Evaluate(`
				Array.from(document.querySelectorAll('*'))
					.filter(el => {
						const style = window.getComputedStyle(el);
						return style.cursor === 'pointer' &&
							style.display !== 'none' &&
							style.visibility !== 'hidden' &&
							style.opacity !== '0';
					})
					.map(el => {
						if (el.id) return '#' + el.id;
						if (el.className) {
							const classes = Array.from(el.classList).join('.');
							return el.tagName.toLowerCase() + '.' + classes;
						}
						return el.tagName.toLowerCase();
					})
			`, &buttonSelectors),
		)

		if err != nil {
			t.Fatalf("Erro ao encontrar elementos clicáveis na página: %v", err)
		}

		t.Logf("Encontrados %d elementos clicáveis na página", len(buttonSelectors))
	}

	// Testar cada botão
	t.Log("Iniciando teste de cada botão...")
	for i, selector := range buttonSelectors {
		t.Logf("Testando botão %d/%d: %s", i+1, len(buttonSelectors), selector)

		// Verificar se o botão existe e está visível
		var visible bool
		err := chromedp.Run(ctx,
			chromedp.Evaluate(fmt.Sprintf(`
				const el = document.querySelector('%s');
				if (!el) return false;
				const rect = el.getBoundingClientRect();
				const style = window.getComputedStyle(el);
				return rect.width > 0 && rect.height > 0 &&
					style.display !== 'none' &&
					style.visibility !== 'hidden' &&
					style.opacity !== '0';
			`, selector), &visible),
		)

		if err != nil || !visible {
			t.Logf("Botão '%s' não encontrado ou não está visível, pulando...", selector)
			continue
		}

		// Capturar estado da página antes do clique
		var beforeHTML string
		err = chromedp.Run(ctx,
			chromedp.OuterHTML("html", &beforeHTML),
		)
		if err != nil {
			t.Logf("Erro ao capturar HTML antes do clique: %v", err)
			continue
		}

		// Tentar clicar no botão
		t.Logf("Clicando no botão '%s'...", selector)
		err = chromedp.Run(ctx,
			chromedp.Click(selector, chromedp.NodeVisible),
			chromedp.Sleep(3*time.Second), // Aguardar possível efeito do clique (tempo aumentado)
		)
		t.Log("Aguardando 3 segundos após o clique...")

		if err != nil {
			t.Logf("Erro ao clicar no botão '%s': %v", selector, err)
			continue
		}

		// Capturar screenshot após o clique
		err = chromedp.Run(ctx,
			chromedp.CaptureScreenshot(&buf),
		)
		if err != nil {
			t.Logf("Erro ao capturar screenshot após clique: %v", err)
		} else {
			afterClickFilename := filepath.Join(screenshotDir, fmt.Sprintf("buttons_test_after_click_%s_button%d.png",
				strings.ReplaceAll(strings.Trim(pagePath, "/"), "/", "_"), i+1))
			if err := os.WriteFile(afterClickFilename, buf, 0644); err != nil {
				t.Logf("Erro ao salvar screenshot após clique: %v", err)
			} else {
				t.Logf("Screenshot após clique salvo em: %s", afterClickFilename)
			}
		}

		// Capturar estado da página após o clique
		var afterHTML string
		err = chromedp.Run(ctx,
			chromedp.OuterHTML("html", &afterHTML),
		)
		if err != nil {
			t.Logf("Erro ao capturar HTML após o clique: %v", err)
			continue
		}

		// Verificar se houve alguma mudança na página
		t.Log("Verificando mudanças na página após o clique...")
		if beforeHTML == afterHTML {
			t.Logf("Aviso: Nenhuma mudança visível no HTML após clicar no botão '%s'", selector)

			// Verificar se há algum modal ou popup visível
			var modalVisible bool
			err = chromedp.Run(ctx,
				chromedp.Evaluate(`
					document.querySelector('.modal.show, .popup.show, .dialog.show, [role="dialog"].show, .dropdown.show') !== null
				`, &modalVisible),
			)

			if err != nil {
				t.Logf("Erro ao verificar modais: %v", err)
			} else if modalVisible {
				t.Logf("Um modal ou popup foi aberto após o clique")

				// Fechar o modal se possível
				err = chromedp.Run(ctx,
					chromedp.Evaluate(`
						const closeBtn = document.querySelector('.modal.show .close, .modal.show .btn-close, .popup.show .close');
						if (closeBtn) closeBtn.click();
						true;
					`, nil),
					chromedp.Sleep(2*time.Second), // Tempo aumentado
				)

				if err != nil {
					t.Logf("Erro ao tentar fechar o modal: %v", err)
				}
			} else {
				// Verificar se houve mudança de URL
				var currentURL string
				err = chromedp.Run(ctx,
					chromedp.Evaluate(`window.location.href`, &currentURL),
				)

				if err != nil {
					t.Logf("Erro ao verificar URL atual: %v", err)
				} else if currentURL != pageURL {
					t.Logf("A URL mudou para: %s", currentURL)

					// Voltar para a página original
					err = chromedp.Run(ctx,
						chromedp.Navigate(pageURL),
						chromedp.Sleep(5*time.Second), // Tempo aumentado
					)

					if err != nil {
						t.Fatalf("Erro ao voltar para a página original: %v", err)
					}
				} else {
					// Verificar se houve alguma mudança no console (pode indicar ação JavaScript)
					var consoleMessages []string
					err = chromedp.Run(ctx,
						chromedp.Evaluate(`
							// Esta é uma simulação, pois não podemos acessar o console diretamente
							// Verificar se há algum elemento de notificação ou toast
							const notifications = document.querySelectorAll('.toast, .notification, .alert');
							Array.from(notifications).map(n => n.textContent);
						`, &consoleMessages),
					)

					if err != nil {
						t.Logf("Erro ao verificar notificações: %v", err)
					} else if len(consoleMessages) > 0 {
						t.Logf("Notificações encontradas após o clique: %v", consoleMessages)
					}
				}
			}
		} else {
			t.Logf("O botão '%s' causou mudanças na página", selector)
		}

		// Aguardar mais tempo antes de testar o próximo botão
		t.Log("Aguardando 2 segundos antes de testar o próximo botão...")
		time.Sleep(2 * time.Second)
		t.Log("----------------------------------------")
	}

	// Capturar screenshot final
	err = chromedp.Run(ctx,
		chromedp.CaptureScreenshot(&buf),
	)
	if err != nil {
		t.Logf("Erro ao capturar screenshot final: %v", err)
	} else {
		finalFilename := filepath.Join(screenshotDir, fmt.Sprintf("buttons_test_final_%s.png",
			strings.ReplaceAll(strings.Trim(pagePath, "/"), "/", "_")))
		if err := os.WriteFile(finalFilename, buf, 0644); err != nil {
			t.Logf("Erro ao salvar screenshot final: %v", err)
		} else {
			t.Logf("Screenshot final salvo em: %s", finalFilename)
		}
	}

	t.Log("=========================================")
	t.Logf("Teste de botões concluído para a página %s", pagePath)
	t.Log("=========================================")
}
