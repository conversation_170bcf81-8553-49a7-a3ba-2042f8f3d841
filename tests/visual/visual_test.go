package visual

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/chromedp/chromedp"
)

// TestPageVisual verifica automaticamente se o visual de uma página está correto
// Esta função pode ser usada para testar qualquer página do sistema
func TestPageVisual(t *testing.T) {
	// Criar diretório para screenshots se não existir
	screenshotDir := "screenshots"
	if _, err := os.Stat(screenshotDir); os.IsNotExist(err) {
		os.Mkdir(screenshotDir, 0755)
	}

	// Configurar opções do Chrome
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", true),
		chromedp.Flag("disable-gpu", true),
		chromedp.Flag("no-sandbox", true),
		chromedp.Flag("disable-dev-shm-usage", true),
		chromedp.WindowSize(1280, 800),
	)

	// Criar contexto do navegador
	allocCtx, cancel := chromedp.NewExecAllocator(context.Background(), opts...)
	defer cancel()

	// Criar um logger personalizado que filtra mensagens de erro de cookies
	logger := log.New(os.Stdout, "", log.LstdFlags)
	customLogger := func(format string, args ...interface{}) {
		msg := fmt.Sprintf(format, args...)
		if !strings.Contains(msg, "could not unmarshal event") &&
			!strings.Contains(msg, "cookiePart") {
			logger.Printf(format, args...)
		}
	}

	ctx, cancel := chromedp.NewContext(allocCtx, chromedp.WithLogf(customLogger))
	defer cancel()

	// Definir timeout
	ctx, cancel = context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// Fazer login no sistema
	t.Log("Fazendo login no sistema...")
	// Obter credenciais das variáveis de ambiente ou usar valores padrão
	email := os.Getenv("VISUAL_TEST_EMAIL")
	if email == "" {
		email = "<EMAIL>" // Valor padrão
	}

	senha := os.Getenv("VISUAL_TEST_PASSWORD")
	if senha == "" {
		senha = "i1t2@3l4O5" // Valor padrão
	}

	t.Logf("Tentando login com usuário: %s", email)
	err := login(ctx, "http://localhost:8080/login", email, senha)
	if err != nil {
		t.Fatalf("Erro ao fazer login: %v", err)
	}

	// Obter a página a ser testada a partir da variável de ambiente
	pagePath := os.Getenv("VISUAL_TEST_PAGE")
	if pagePath == "" {
		pagePath = "/galeria" // Valor padrão para compatibilidade
	}

	// Testar a página especificada
	t.Logf("Testando a página: %s", pagePath)
	verifyPage(t, ctx, screenshotDir, pagePath)
}

// verifyPage verifica se uma página está visualmente correta
func verifyPage(t *testing.T, ctx context.Context, screenshotDir string, pagePath string) {
	var buf []byte
	var title string
	var html string
	var bodyClasses string
	var cssProperties map[string]string = make(map[string]string)
	var criticalElements []string
	var optionalElements []string

	// Definir elementos críticos e opcionais com base no tipo de página
	pageName := strings.Trim(pagePath, "/")
	switch pageName {
	case "galeria":
		criticalElements = []string{
			"body",                  // Corpo da página
			".content-with-sidebar", // Container principal com sidebar
			".main-content",         // Conteúdo principal
			".page-header",          // Cabeçalho da página
		}
		optionalElements = []string{
			"#gridView",        // Container principal da galeria
			".filter-section",  // Seção de filtros
			".gallery-section", // Seção da galeria
			".equipment-grid",  // Grid de equipamentos
		}
	case "dashboard":
		criticalElements = []string{
			"body",              // Corpo da página
			".tradicio-sidebar", // Sidebar
			".sidebar-header",   // Cabeçalho da sidebar
			".row",              // Linhas do Bootstrap
		}
		optionalElements = []string{
			".sidebar-profile",  // Perfil na sidebar
			".sidebar-logo",     // Logo na sidebar
			".dashboard-card",   // Cards do dashboard
			".dashboard-metric", // Métricas do dashboard
		}
	default:
		// Elementos genéricos para qualquer página
		criticalElements = []string{
			"body",                  // Corpo da página
			".content-with-sidebar", // Container principal com sidebar
			".main-content",         // Conteúdo principal
		}
		optionalElements = []string{
			".page-header", // Cabeçalho da página
			".card-shell",  // Card shell (container)
			".row",         // Linha do Bootstrap
			".card",        // Card do Bootstrap
		}
	}

	// Navegar para a página especificada
	pageURL := fmt.Sprintf("http://localhost:8080%s", pagePath)
	t.Logf("Navegando para: %s", pageURL)
	err := chromedp.Run(ctx,
		chromedp.Navigate(pageURL),
		chromedp.Sleep(2*time.Second), // Aguardar carregamento completo
		chromedp.Title(&title),
		chromedp.OuterHTML("html", &html),
		chromedp.Evaluate(`document.body.className`, &bodyClasses),
		chromedp.CaptureScreenshot(&buf),
	)

	if err != nil {
		t.Fatalf("Erro ao navegar para a página da galeria: %v", err)
	}

	// Verificar título da página
	// Extrair o nome da página do caminho (ex: "/galeria" -> "galeria")
	if pageName == "" {
		pageName = "inicio" // Página inicial
	}
	t.Logf("Nome da página: %s", pageName)

	titleCorrect := !strings.EqualFold(title, "") // Título não deve estar vazio
	if !titleCorrect {
		t.Errorf("Título da página está vazio")
	} else {
		t.Logf("Título da página: %s", title)
	}

	// Verificar se a página contém a classe de tema escuro (dark mode)
	if !strings.Contains(bodyClasses, "dark-mode") && !strings.Contains(bodyClasses, "dark-theme") {
		t.Logf("Aviso: Página não parece estar usando tema escuro. Classes do body: %s", bodyClasses)
	}

	// Verificar elementos críticos (obrigatórios)
	criticalElementsFound := true
	for _, selector := range criticalElements {
		var visible bool
		var exists bool

		// Verificar se o elemento existe e está visível
		err := chromedp.Run(ctx,
			chromedp.Evaluate(fmt.Sprintf(`document.querySelector('%s') !== null`, selector), &exists),
			chromedp.Evaluate(fmt.Sprintf(`
				const el = document.querySelector('%s');
				if (!el) return false;
				const style = window.getComputedStyle(el);
				return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
			`, selector), &visible),
		)

		if err != nil || !exists || !visible {
			t.Errorf("Elemento crítico '%s' não encontrado ou não está visível", selector)
			criticalElementsFound = false
		} else {
			t.Logf("Elemento crítico '%s' encontrado e está visível", selector)
		}
	}

	// Verificar elementos opcionais (bom ter)
	optionalElementsFound := 0
	for _, selector := range optionalElements {
		var visible bool
		var exists bool

		// Verificar se o elemento existe e está visível
		err := chromedp.Run(ctx,
			chromedp.Evaluate(fmt.Sprintf(`document.querySelector('%s') !== null`, selector), &exists),
			chromedp.Evaluate(fmt.Sprintf(`
				const el = document.querySelector('%s');
				if (!el) return false;
				const style = window.getComputedStyle(el);
				return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
			`, selector), &visible),
		)

		if err != nil || !exists || !visible {
			t.Logf("Elemento opcional '%s' não encontrado ou não está visível", selector)
		} else {
			t.Logf("Elemento opcional '%s' encontrado e está visível", selector)
			optionalElementsFound++

			// Verificar propriedades CSS importantes para este elemento
			if selector == "#gridView" {
				getComputedStyle(ctx, selector, "display", &cssProperties)
				getComputedStyle(ctx, selector, "margin", &cssProperties)
				getComputedStyle(ctx, selector, "padding", &cssProperties)
			}

			if selector == ".container" {
				getComputedStyle(ctx, selector, "max-width", &cssProperties)
				getComputedStyle(ctx, selector, "padding-left", &cssProperties)
				getComputedStyle(ctx, selector, "padding-right", &cssProperties)
			}

			if selector == ".row" {
				getComputedStyle(ctx, selector, "display", &cssProperties)
				getComputedStyle(ctx, selector, "flex-wrap", &cssProperties)
				getComputedStyle(ctx, selector, "margin-left", &cssProperties)
				getComputedStyle(ctx, selector, "margin-right", &cssProperties)

				// Verificar se está usando flex com wrap
				if !strings.Contains(cssProperties["display"], "flex") {
					t.Logf("Aviso: .row não está usando display flex. Display atual: %s", cssProperties["display"])
				}
			}

			if selector == ".card" {
				getComputedStyle(ctx, selector, "border-radius", &cssProperties)
				getComputedStyle(ctx, selector, "overflow", &cssProperties)
				getComputedStyle(ctx, selector, "box-shadow", &cssProperties)
				getComputedStyle(ctx, selector, "background-color", &cssProperties)

				// Verificar se os cards têm estilo moderno
				if cssProperties["border-radius"] == "0px" {
					t.Logf("Aviso: .card não tem border-radius. Valor atual: %s", cssProperties["border-radius"])
				}
			}
		}
	}

	// Verificar itens específicos com base no tipo de página
	if pageName == "galeria" {
		// Contar quantos itens de galeria existem
		var itemCount int
		err = chromedp.Run(ctx,
			chromedp.Evaluate(`document.querySelectorAll('#gridView > div:not(#loadingIndicator):not(#noEquipmentsMessage)').length`, &itemCount),
		)

		if err != nil {
			t.Logf("Erro ao contar itens da galeria: %v", err)
		} else {
			if itemCount == 0 {
				t.Errorf("Nenhum item encontrado na galeria")
				criticalElementsFound = false
			} else {
				t.Logf("Galeria contém %d itens", itemCount)
			}
		}
	} else if pageName == "dashboard" {
		// Verificar elementos específicos do dashboard
		var cardCount int
		err = chromedp.Run(ctx,
			chromedp.Evaluate(`document.querySelectorAll('.dashboard-card, .card').length`, &cardCount),
		)

		if err != nil {
			t.Logf("Erro ao contar cards do dashboard: %v", err)
		} else {
			if cardCount == 0 {
				t.Logf("Aviso: Nenhum card encontrado no dashboard")
			} else {
				t.Logf("Dashboard contém %d cards", cardCount)
			}
		}
	}

	// Salvar screenshot com o nome da página
	screenshotName := fmt.Sprintf("%s.png", pageName)
	filename := filepath.Join(screenshotDir, screenshotName)
	if err := os.WriteFile(filename, buf, 0644); err != nil {
		t.Fatalf("Erro ao salvar screenshot: %v", err)
	}

	// Avaliar resultados do teste
	t.Logf("Resumo do teste visual:")
	if criticalElementsFound {
		t.Logf("- Elementos críticos: TODOS ENCONTRADOS")
	} else {
		t.Logf("- Elementos críticos: ALGUNS FALTANDO")
	}
	t.Logf("- Elementos opcionais: %d/%d encontrados", optionalElementsFound, len(optionalElements))

	// Salvar screenshot para referência
	t.Logf("Screenshot salvo em: %s", filename)

	// Resultado final
	if criticalElementsFound {
		t.Logf("SUCESSO: Página '%s' contém todos os elementos críticos!", pageName)
		if optionalElementsFound < len(optionalElements) {
			t.Logf("AVISO: Alguns elementos opcionais não foram encontrados. Isso pode ser normal se a página foi redesenhada.")
		}
	} else {
		t.Errorf("FALHA: Página '%s' não contém todos os elementos críticos. Verifique o screenshot em: %s", pageName, filename)
	}
}

// getComputedStyle obtém uma propriedade CSS computada de um elemento
func getComputedStyle(ctx context.Context, selector, property string, result *map[string]string) {
	var value string
	err := chromedp.Run(ctx,
		chromedp.Evaluate(fmt.Sprintf(`
			const el = document.querySelector('%s');
			if (!el) return '';
			return window.getComputedStyle(el).getPropertyValue('%s');
		`, selector, property), &value),
	)

	if err == nil && value != "" {
		(*result)[property] = value
	} else {
		(*result)[property] = "unknown"
	}
}

// login faz login no sistema
func login(ctx context.Context, url, email, password string) error {
	// Primeiro, navegar para a página inicial
	if err := chromedp.Run(ctx, chromedp.Navigate("http://localhost:8080/")); err != nil {
		return fmt.Errorf("erro ao navegar para a página inicial: %v", err)
	}

	// Aguardar a página inicial carregar
	if err := chromedp.Run(ctx, chromedp.Sleep(2*time.Second)); err != nil {
		return fmt.Errorf("erro ao aguardar carregamento da página inicial: %v", err)
	}

	// Agora navegar para a página de login
	if err := chromedp.Run(ctx, chromedp.Navigate(url)); err != nil {
		return fmt.Errorf("erro ao navegar para a página de login: %v", err)
	}

	// Aguardar a página de login carregar
	if err := chromedp.Run(ctx, chromedp.Sleep(2*time.Second)); err != nil {
		return fmt.Errorf("erro ao aguardar carregamento da página de login: %v", err)
	}

	// Verificar se estamos na página de login
	var title string
	if err := chromedp.Run(ctx, chromedp.Title(&title)); err != nil {
		return fmt.Errorf("erro ao obter título da página: %v", err)
	}

	// Verificar se o título contém "Login"
	if !strings.Contains(title, "Login") {
		return fmt.Errorf("não estamos na página de login. Título atual: %s", title)
	}

	// Aguardar o formulário de login aparecer
	if err := chromedp.Run(ctx, chromedp.WaitVisible(`form`, chromedp.ByQuery)); err != nil {
		return fmt.Errorf("erro ao aguardar formulário de login: %v", err)
	}

	// Verificar quais campos existem no formulário
	var formHTML string
	if err := chromedp.Run(ctx, chromedp.OuterHTML(`form`, &formHTML, chromedp.ByQuery)); err != nil {
		return fmt.Errorf("erro ao obter HTML do formulário: %v", err)
	}

	// Determinar os seletores corretos com base no HTML do formulário
	emailSelector := `input[name="email"]`
	passwordSelector := `input[name="password"]`
	submitSelector := `button[type="submit"]`

	// Se o formulário usar outros nomes de campos, ajustar os seletores
	if !strings.Contains(formHTML, `name="email"`) {
		// Tentar outros nomes comuns para o campo de email/usuário
		if strings.Contains(formHTML, `name="username"`) {
			emailSelector = `input[name="username"]`
		} else if strings.Contains(formHTML, `name="user"`) {
			emailSelector = `input[name="user"]`
		} else if strings.Contains(formHTML, `name="login"`) {
			emailSelector = `input[name="login"]`
		}
	}

	if !strings.Contains(formHTML, `name="password"`) {
		// Tentar outros nomes comuns para o campo de senha
		if strings.Contains(formHTML, `name="pass"`) {
			passwordSelector = `input[name="pass"]`
		} else if strings.Contains(formHTML, `name="pwd"`) {
			passwordSelector = `input[name="pwd"]`
		}
	}

	// Preencher o formulário e enviar
	err := chromedp.Run(ctx,
		// Limpar os campos primeiro
		chromedp.Clear(emailSelector, chromedp.ByQuery),
		chromedp.Clear(passwordSelector, chromedp.ByQuery),

		// Preencher os campos
		chromedp.SendKeys(emailSelector, email, chromedp.ByQuery),
		chromedp.SendKeys(passwordSelector, password, chromedp.ByQuery),

		// Clicar no botão de envio
		chromedp.Click(submitSelector, chromedp.ByQuery),

		// Aguardar redirecionamento após login
		chromedp.Sleep(5*time.Second),

		// Verificar se o login foi bem-sucedido (verificar se não estamos mais na página de login)
		chromedp.ActionFunc(func(ctx context.Context) error {
			var currentURL string
			if err := chromedp.Evaluate(`window.location.href`, &currentURL).Do(ctx); err != nil {
				return err
			}

			if strings.Contains(currentURL, "/login") {
				return fmt.Errorf("login falhou, ainda estamos na página de login: %s", currentURL)
			}

			return nil
		}),
	)

	if err != nil {
		return err
	}

	// Verificar se conseguimos acessar a página dashboard após o login
	var dashboardHTML string
	err = chromedp.Run(ctx,
		chromedp.Navigate("http://localhost:8080/dashboard"),
		chromedp.Sleep(2*time.Second),
		chromedp.OuterHTML("html", &dashboardHTML),
	)

	if err != nil {
		return fmt.Errorf("erro ao acessar dashboard após login: %v", err)
	}

	// Verificar se a página dashboard contém conteúdo válido
	if strings.Contains(dashboardHTML, "Token de autenticação não fornecido ou inválido") {
		return fmt.Errorf("login falhou: token de autenticação inválido")
	}

	return nil
}
