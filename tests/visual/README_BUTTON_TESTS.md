# Testes de Funcionalidade de Botões

Este documento explica como usar os testes automatizados para verificar se os botões das páginas do sistema estão funcionando corretamente.

## O que estes testes fazem?

Os testes de botões realizam as seguintes verificações:

1. Identificam todos os botões e elementos clicáveis em uma página
2. Tentam clicar em cada botão
3. Verificam se o clique produziu algum efeito (mudanças na página, abertura de modais, navegação, etc.)
4. Capturam screenshots antes e depois de cada clique para análise visual
5. Geram um relatório detalhado sobre o funcionamento de cada botão

## Pré-requisitos

- Go 1.16 ou superior
- Chrome ou Chromium instalado
- Servidor do projeto em execução (ou será iniciado automaticamente pelo script)

## Como executar os testes

### No Linux/Mac

```bash
# Executar teste na página padrão (/financeiro)
./scripts/run_button_tests.sh

# Executar teste em uma página específica
./scripts/run_button_tests.sh /dashboard

# Executar teste com credenciais específicas
./scripts/run_button_tests.sh /dashboard <EMAIL> senha123
```

### No Windows

```batch
# Executar teste na página padrão (/financeiro)
scripts\run_button_tests.bat

# Executar teste em uma página específica
scripts\run_button_tests.bat /dashboard

# Executar teste com credenciais específicas
scripts\run_button_tests.bat /dashboard <EMAIL> senha123
```

## Interpretando os resultados

Após a execução dos testes, você encontrará os seguintes resultados:

1. **Log detalhado no terminal**: Mostra cada botão testado e o resultado do teste
2. **Screenshots na pasta `tests/visual/screenshots/`**:
   - `buttons_test_initial_[pagina].png`: Estado inicial da página
   - `buttons_test_after_click_[pagina]_button[N].png`: Estado após clicar no botão N
   - `buttons_test_final_[pagina].png`: Estado final da página após todos os testes

## Como os botões são identificados

O teste identifica os seguintes elementos como botões:

- Elementos `<button>`
- Elementos com classe `.btn`, `.shell-btn`
- Elementos com atributo `role="button"`
- Links `<a>` com classe `.btn`
- Inputs do tipo `button` ou `submit`
- Qualquer elemento com estilo CSS `cursor: pointer`

## Limitações

- Alguns botões podem executar ações JavaScript que não causam mudanças visíveis no DOM
- Botões que abrem novas abas ou janelas podem não ser detectados corretamente
- Botões que requerem preenchimento prévio de formulários podem falhar
- Botões que dependem de estado específico da aplicação podem não funcionar como esperado

## Personalizando os testes

Você pode modificar o arquivo `tests/visual/button_test.go` para:

- Adicionar seletores específicos para botões importantes
- Ignorar certos botões que não devem ser testados
- Adicionar verificações específicas para botões críticos
- Modificar o comportamento de detecção de mudanças na página

## Solução de problemas

### Teste falha ao iniciar

Verifique se:
- O Chrome/Chromium está instalado
- O servidor está rodando na porta 8080
- As credenciais de login estão corretas

### Botões não são detectados

Verifique se:
- Os botões estão visíveis na página (não ocultos por CSS)
- Os botões têm seletores CSS válidos
- A página carregou completamente antes do teste

### Falsos positivos/negativos

- Aumente o tempo de espera após os cliques (`chromedp.Sleep`)
- Verifique os screenshots para entender o que aconteceu
- Adicione verificações específicas para botões problemáticos
