package handlers_test

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// TestGetMaintenanceOrders testa a listagem de ordens de manutenção
func TestGetMaintenanceOrders(t *testing.T) {
	// Configurar modo de teste para o Gin
	gin.SetMode(gin.TestMode)

	// Criar um router de teste
	router := gin.Default()

	// Simular o endpoint de listagem de ordens
	router.GET("/api/maintenance", func(c *gin.Context) {
		// Simular dados de resposta para ordens de manutenção
		orders := []map[string]interface{}{
			{
				"id":          1,
				"title":       "Manutenção preventiva - Bomba #2",
				"description": "Verificação de mangueiras e filtros de combustível",
				"status":      "scheduled",
				"priority":    "medium",
				"location":    "Posto Shell Avenida Paulista",
				"date":        "2025-04-15",
			},
			{
				"id":          2,
				"title":       "Reparo emergencial - Sistema elétrico",
				"description": "Falha no sistema elétrico da ilha de abastecimento",
				"status":      "urgent",
				"priority":    "high",
				"location":    "Posto Shell Morumbi",
				"date":        "2025-04-02",
			},
		}

		// Retornar resposta
		c.JSON(http.StatusOK, gin.H{
			"status":  "success",
			"message": "Ordens de manutenção encontradas",
			"data": gin.H{
				"orders": orders,
				"total":  len(orders),
			},
		})
	})

	// Teste: Listagem de ordens de manutenção
	t.Run("Get Maintenance Orders", func(t *testing.T) {
		// Criar requisição de teste
		req, _ := http.NewRequest("GET", "/api/maintenance", nil)

		// Incluir possível token de autenticação
		req.Header.Set("Authorization", "Bearer test_token")

		// Executar a requisição
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verificar o código de status
		assert.Equal(t, http.StatusOK, w.Code)

		// Analisar resposta
		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)

		// Verificações específicas
		assert.Equal(t, "success", response["status"])

		// Verificar dados retornados
		data, _ := response["data"].(map[string]interface{})
		orders, _ := data["orders"].([]interface{})

		// Verificar quantidade de ordens retornadas
		assert.Equal(t, 2, len(orders))

		// Verificar primeira ordem
		firstOrder, _ := orders[0].(map[string]interface{})
		assert.Equal(t, float64(1), firstOrder["id"])
		assert.Equal(t, "scheduled", firstOrder["status"])
	})
}
