package handlers_test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// TestMaintenanceOrderEndpoints testa os endpoints relacionados a ordens de manutenção
func TestMaintenanceOrderEndpoints(t *testing.T) {
	// Configurar modo de teste para o Gin
	gin.SetMode(gin.TestMode)

	// Criar um router de teste
	router := gin.Default()

	// Configurar rotas para teste
	setupMaintenanceOrderRoutes(router)

	// Teste 1: Listar ordens de manutenção
	t.Run("Listar ordens de manutenção", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/maintenance/orders", nil)
		req.Header.Set("Authorization", "Bearer test_token")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)

		assert.Equal(t, "success", response["status"])
		data, _ := response["data"].(map[string]interface{})
		orders, _ := data["orders"].([]interface{})
		assert.NotEmpty(t, orders, "A lista de ordens não deve estar vazia")
	})

	// Teste 2: Obter detalhes de uma ordem específica
	t.Run("Obter detalhes de uma ordem", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/maintenance/orders/1", nil)
		req.Header.Set("Authorization", "Bearer test_token")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)

		assert.Equal(t, "success", response["status"])
		data, _ := response["data"].(map[string]interface{})
		order, _ := data["order"].(map[string]interface{})
		assert.Equal(t, float64(1), order["id"])
	})

	// Teste 3: Criar uma nova ordem
	t.Run("Criar nova ordem", func(t *testing.T) {
		orderData := map[string]interface{}{
			"branch_id":    1,
			"equipment_id": 2,
			"description":  "Teste de criação de ordem",
			"priority":     "high",
		}
		jsonData, _ := json.Marshal(orderData)

		req, _ := http.NewRequest("POST", "/api/maintenance/orders", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer test_token")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)

		assert.Equal(t, "success", response["status"])
		assert.Contains(t, response["message"], "criada")
	})

	// Teste 4: Atualizar uma ordem existente
	t.Run("Atualizar ordem existente", func(t *testing.T) {
		updateData := map[string]interface{}{
			"description": "Descrição atualizada",
			"status":      "in_progress",
		}
		jsonData, _ := json.Marshal(updateData)

		req, _ := http.NewRequest("PUT", "/api/maintenance/orders/1", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer test_token")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)

		assert.Equal(t, "success", response["status"])
	})

	// Teste 5: Excluir uma ordem
	t.Run("Excluir ordem", func(t *testing.T) {
		req, _ := http.NewRequest("DELETE", "/api/maintenance/orders/1", nil)
		req.Header.Set("Authorization", "Bearer test_token")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)

		assert.Equal(t, "success", response["status"])
	})
}

// setupMaintenanceOrderRoutes configura as rotas para testes
func setupMaintenanceOrderRoutes(router *gin.Engine) {
	// Grupo de rotas para manutenção
	maintenance := router.Group("/api/maintenance")
	{
		// Listar todas as ordens
		maintenance.GET("/orders", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"status": "success",
				"data": gin.H{
					"orders": []gin.H{
						{
							"id":          1,
							"branch_id":   1,
							"description": "Manutenção preventiva",
							"status":      "pending",
							"created_at":  "2023-10-01T10:00:00Z",
						},
						{
							"id":          2,
							"branch_id":   1,
							"description": "Reparo de equipamento",
							"status":      "in_progress",
							"created_at":  "2023-10-02T14:30:00Z",
						},
					},
				},
			})
		})

		// Obter uma ordem específica
		maintenance.GET("/orders/:id", func(c *gin.Context) {
			id := c.Param("id")
			c.JSON(http.StatusOK, gin.H{
				"status": "success",
				"data": gin.H{
					"order": gin.H{
						"id":          id,
						"branch_id":   1,
						"description": "Manutenção preventiva",
						"status":      "pending",
						"created_at":  "2023-10-01T10:00:00Z",
					},
				},
			})
		})

		// Criar uma nova ordem
		maintenance.POST("/orders", func(c *gin.Context) {
			var orderRequest struct {
				BranchID    int    `json:"branch_id"`
				EquipmentID int    `json:"equipment_id"`
				Description string `json:"description"`
				Priority    string `json:"priority"`
			}

			if err := c.ShouldBindJSON(&orderRequest); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"status":  "error",
					"message": "Formato de requisição inválido",
				})
				return
			}

			c.JSON(http.StatusCreated, gin.H{
				"status":  "success",
				"message": "Ordem de manutenção criada com sucesso",
				"data": gin.H{
					"order_id": 3,
				},
			})
		})

		// Atualizar uma ordem existente
		maintenance.PUT("/orders/:id", func(c *gin.Context) {
			id := c.Param("id")
			var updateRequest struct {
				Description string `json:"description"`
				Status      string `json:"status"`
			}

			if err := c.ShouldBindJSON(&updateRequest); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"status":  "error",
					"message": "Formato de requisição inválido",
				})
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"status":  "success",
				"message": "Ordem de manutenção atualizada com sucesso",
				"data": gin.H{
					"order_id": id,
				},
			})
		})

		// Excluir uma ordem
		maintenance.DELETE("/orders/:id", func(c *gin.Context) {
			id := c.Param("id")
			c.JSON(http.StatusOK, gin.H{
				"status":  "success",
				"message": "Ordem de manutenção excluída com sucesso",
				"data": gin.H{
					"order_id": id,
				},
			})
		})
	}
}
