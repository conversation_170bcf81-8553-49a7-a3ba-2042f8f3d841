package handlers_test

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// TestOrderDetailEndpoint testa o endpoint de detalhes de uma ordem
func TestOrderDetailEndpoint(t *testing.T) {
	// Configurar modo de teste para o Gin
	gin.SetMode(gin.TestMode)

	// Criar um router de teste
	router := gin.Default()

	// Configurar rota para teste
	router.GET("/api/orders/:id/details", func(c *gin.Context) {
		id := c.Param("id")

		// Simular resposta com detalhes da ordem
		c.JSON(http.StatusOK, gin.H{
			"status": "success",
			"data": gin.H{
				"order": gin.H{
					"id":          id,
					"client_name": "Cliente Exemplo",
					"description": "Descrição detalhada da ordem",
					"status":      "in_progress",
					"created_at":  "2023-10-15T14:30:00Z",
					"updated_at":  "2023-10-16T09:15:00Z",
					"assigned_to": "Técnico Responsável",
					"priority":    "high",
					"location":    "Posto Shell Centro",
					"equipment":   "Bomba de Combustível #3",
					"photos": []gin.H{
						{
							"id":        1,
							"url":       "/static/images/orders/photo1.jpg",
							"thumbnail": "/static/images/orders/thumbnails/photo1.jpg",
							"caption":   "Foto inicial",
						},
						{
							"id":        2,
							"url":       "/static/images/orders/photo2.jpg",
							"thumbnail": "/static/images/orders/thumbnails/photo2.jpg",
							"caption":   "Detalhe do problema",
						},
					},
					"history": []gin.H{
						{
							"date":        "2023-10-15T14:30:00Z",
							"user":        "Admin",
							"action":      "created",
							"description": "Ordem criada",
						},
						{
							"date":        "2023-10-15T15:45:00Z",
							"user":        "Supervisor",
							"action":      "assigned",
							"description": "Ordem atribuída ao técnico",
						},
						{
							"date":        "2023-10-16T09:15:00Z",
							"user":        "Técnico",
							"action":      "status_update",
							"description": "Status atualizado para 'em andamento'",
						},
					},
				},
			},
		})
	})

	// Teste: Obter detalhes de uma ordem
	t.Run("Obter detalhes de uma ordem", func(t *testing.T) {
		// Criar requisição de teste
		req, _ := http.NewRequest("GET", "/api/orders/123/details", nil)
		req.Header.Set("Authorization", "Bearer test_token")

		// Executar a requisição
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verificar o código de status
		assert.Equal(t, http.StatusOK, w.Code)

		// Verificar conteúdo da resposta
		assert.Contains(t, w.Body.String(), "Cliente Exemplo")
		assert.Contains(t, w.Body.String(), "Descrição detalhada da ordem")
		assert.Contains(t, w.Body.String(), "in_progress")
		assert.Contains(t, w.Body.String(), "Técnico Responsável")
		assert.Contains(t, w.Body.String(), "Bomba de Combustível #3")

		// Verificar se contém fotos
		assert.Contains(t, w.Body.String(), "photos")
		assert.Contains(t, w.Body.String(), "photo1.jpg")

		// Verificar se contém histórico
		assert.Contains(t, w.Body.String(), "history")
		assert.Contains(t, w.Body.String(), "Ordem criada")
		assert.Contains(t, w.Body.String(), "Status atualizado")
	})
}
