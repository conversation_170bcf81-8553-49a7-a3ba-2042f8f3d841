package handlers_test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// TestLoginEndpoint testa o funcionamento básico do endpoint de login
func TestLoginEndpoint(t *testing.T) {
	// Configurar modo de teste para o Gin
	gin.SetMode(gin.TestMode)

	// Criar um router de teste
	router := gin.Default()

	// Simular o endpoint de login
	router.POST("/api/login", func(c *gin.Context) {
		var loginRequest struct {
			Email    string `json:"email"`
			Password string `json:"password"`
		}

		if err := c.ShouldBind<PERSON>(&loginRequest); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
			return
		}

		// Simular uma autenticação simples para teste
		if loginRequest.Email == "<EMAIL>" && loginRequest.Password == "password123" {
			c.<PERSON>(http.StatusOK, gin.H{
				"status":  "success",
				"message": "Login successful",
				"data": gin.H{
					"user": gin.H{
						"id":    1,
						"email": loginRequest.Email,
						"name":  "Test User",
					},
					"token": "test_token_for_simulation",
				},
			})
		} else {
			c.JSON(http.StatusUnauthorized, gin.H{
				"status":  "error",
				"message": "Invalid credentials",
			})
		}
	})

	// Teste 1: Credenciais válidas
	t.Run("Valid Credentials", func(t *testing.T) {
		// Preparar dados de teste
		loginData := map[string]string{
			"email":    "<EMAIL>",
			"password": "password123",
		}
		jsonData, _ := json.Marshal(loginData)

		// Criar uma requisição de teste
		req, _ := http.NewRequest("POST", "/api/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")

		// Executar a requisição
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verificar o resultado
		assert.Equal(t, http.StatusOK, w.Code)

		// Analisar a resposta
		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)

		// Verificações adicionais
		assert.Equal(t, "success", response["status"])
		assert.Equal(t, "Login successful", response["message"])
	})

	// Teste 2: Credenciais inválidas
	t.Run("Invalid Credentials", func(t *testing.T) {
		// Preparar dados de teste inválidos
		loginData := map[string]string{
			"email":    "<EMAIL>",
			"password": "wrongpassword",
		}
		jsonData, _ := json.Marshal(loginData)

		// Criar uma requisição de teste
		req, _ := http.NewRequest("POST", "/api/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")

		// Executar a requisição
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verificar o resultado
		assert.Equal(t, http.StatusUnauthorized, w.Code)

		// Analisar a resposta
		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)

		// Verificações adicionais
		assert.Equal(t, "error", response["status"])
		assert.Equal(t, "Invalid credentials", response["message"])
	})
}
