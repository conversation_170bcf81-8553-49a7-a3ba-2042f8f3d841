package integration

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// TestAuthenticationFlow testa o fluxo completo de autenticação
// Este é um teste de integração que verifica o fluxo de login, acesso a recursos protegidos e logout
func TestAuthenticationFlow(t *testing.T) {
	// Configurar modo de teste para o Gin
	gin.SetMode(gin.TestMode)

	// Criar um router de teste
	router := setupTestRouter()

	// Teste 1: Login com credenciais válidas
	t.Run("Login com credenciais válidas", func(t *testing.T) {
		// Preparar dados de teste
		loginData := map[string]string{
			"email":    "<EMAIL>",
			"password": "password123",
		}
		jsonData, _ := json.Marshal(loginData)

		// Criar uma requisição de teste
		req, _ := http.NewRequest("POST", "/api/auth/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")

		// Executar a requisição
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verificar o resultado
		assert.Equal(t, http.StatusOK, w.Code)

		// Analisar a resposta
		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)

		// Verificações adicionais
		assert.Equal(t, "success", response["status"])

		// Extrair token para usar nos próximos testes
		data, _ := response["data"].(map[string]interface{})
		token, _ := data["token"].(string)
		assert.NotEmpty(t, token, "Token não deve estar vazio")

		// Teste 2: Acessar recurso protegido com token válido
		t.Run("Acessar recurso protegido com token válido", func(t *testing.T) {
			req, _ := http.NewRequest("GET", "/api/user/profile", nil)
			req.Header.Set("Authorization", "Bearer "+token)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)
		})

		// Teste 3: Logout
		t.Run("Logout", func(t *testing.T) {
			req, _ := http.NewRequest("POST", "/api/auth/logout", nil)
			req.Header.Set("Authorization", "Bearer "+token)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)
		})

		// Teste 4: Tentar acessar recurso protegido após logout
		t.Run("Acessar recurso protegido após logout", func(t *testing.T) {
			req, _ := http.NewRequest("GET", "/api/user/profile", nil)
			req.Header.Set("Authorization", "Bearer "+token)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, http.StatusUnauthorized, w.Code)
		})
	})
}

// setupTestRouter configura um router para testes de integração
// Esta função deve ser adaptada para configurar o router real da aplicação
func setupTestRouter() *gin.Engine {
	router := gin.Default()

	// Configurar rotas de autenticação
	router.POST("/api/auth/login", func(c *gin.Context) {
		var loginRequest struct {
			Email    string `json:"email"`
			Password string `json:"password"`
		}

		if err := c.ShouldBindJSON(&loginRequest); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
			return
		}

		// Simular autenticação para teste
		if loginRequest.Email == "<EMAIL>" && loginRequest.Password == "password123" {
			c.JSON(http.StatusOK, gin.H{
				"status":  "success",
				"message": "Login successful",
				"data": gin.H{
					"user": gin.H{
						"id":    1,
						"email": loginRequest.Email,
						"name":  "Test User",
					},
					"token": "test_token_for_simulation",
				},
			})
		} else {
			c.JSON(http.StatusUnauthorized, gin.H{
				"status":  "error",
				"message": "Invalid credentials",
			})
		}
	})

	// Configurar rota de logout
	router.POST("/api/auth/logout", func(c *gin.Context) {
		// Simular logout para teste
		c.JSON(http.StatusOK, gin.H{
			"status":  "success",
			"message": "Logout successful",
		})
	})

	// Configurar rota protegida
	router.GET("/api/user/profile", func(c *gin.Context) {
		// Simular verificação de autenticação
		token := c.GetHeader("Authorization")
		if token != "Bearer test_token_for_simulation" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"status":  "error",
				"message": "Unauthorized",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"status": "success",
			"data": gin.H{
				"user": gin.H{
					"id":    1,
					"email": "<EMAIL>",
					"name":  "Test User",
				},
			},
		})
	})

	return router
}
