package main

import (
	"fmt"
)

type Role struct {
	Name        string
	Description string
	Pages       []string
	APIs        []string
}

func main() {
	// Definir os papéis do sistema
	roles := []Role{
		{
			Name:        "admin",
			Description: "Administrador do Sistema",
			Pages: []string{
				"/dashboard", "/dashboard-enhanced", "/dashboard-calendario", "/calendario-avancado", "/calendario-flip",
				"/manutencao", "/nova-manutencao", "/financeiro", "/settings", "/minha-conta", "/relatorios",
				"/orders", "/maintenance/new", "/maintenance/edit", "/maintenance/view", "/dashboard/orders",
				"/dashboard/approval", "/configuracoes-notificacoes", "/calendario/stats",
			},
			APIs: []string{
				"/api/user/me", "/api/auth/logout", "/api/maintenance", "/api/maintenance/:id",
				"/api/maintenance/:id/status", "/api/maintenance/:id/priority", "/api/stations",
				"/api/dashboard/metrics", "/api/dashboard/recent", "/api/dashboard/status",
				"/api/reports", "/api/reports/download", "/api/financial/summary", "/api/financial/transactions",
				"/api/notifications", "/api/notifications/settings", "/api/notifications/subscribe",
			},
		},
		{
			Name:        "gerente",
			Description: "Gerente / Gestor de Manutenção",
			Pages: []string{
				"/dashboard", "/dashboard-enhanced", "/dashboard-calendario", "/calendario-avancado", "/calendario-flip",
				"/manutencao", "/nova-manutencao", "/financeiro", "/settings", "/minha-conta", "/relatorios",
				"/orders", "/maintenance/new", "/maintenance/edit", "/maintenance/view", "/dashboard/orders",
				"/dashboard/approval", "/calendario/stats",
			},
			APIs: []string{
				"/api/user/me", "/api/auth/logout", "/api/maintenance", "/api/maintenance/:id",
				"/api/maintenance/:id/status", "/api/maintenance/:id/priority", "/api/stations",
				"/api/dashboard/metrics", "/api/dashboard/recent", "/api/dashboard/status",
				"/api/reports", "/api/reports/download", "/api/financial/summary", "/api/financial/transactions",
				"/api/notifications/settings", "/api/notifications/subscribe",
			},
		},
		{
			Name:        "financeiro",
			Description: "Analista Financeiro",
			Pages: []string{
				"/dashboard", "/dashboard-calendario", "/calendario-avancado", "/calendario-flip",
				"/manutencao", "/financeiro", "/settings", "/minha-conta", "/relatorios",
				"/orders", "/maintenance/view", "/dashboard/orders",
			},
			APIs: []string{
				"/api/user/me", "/api/auth/logout", "/api/maintenance", "/api/maintenance/:id",
				"/api/dashboard/metrics", "/api/dashboard/recent", "/api/dashboard/status",
				"/api/reports", "/api/reports/download", "/api/financial/summary", "/api/financial/transactions",
				"/api/notifications/settings", "/api/notifications/subscribe",
			},
		},
		{
			Name:        "tecnico",
			Description: "Técnico de Manutenção",
			Pages: []string{
				"/dashboard", "/calendario-flip", "/ordemtecnica", "/manutencao", "/minha-conta",
				"/maintenance/view",
			},
			APIs: []string{
				"/api/user/me", "/api/auth/logout", "/api/maintenance", "/api/maintenance/:id",
				"/api/notifications/settings", "/api/notifications/subscribe",
			},
		},
		{
			Name:        "filial",
			Description: "Gestor de Filial/Posto",
			Pages: []string{
				"/dashboard", "/calendario-flip", "/manutencao", "/nova-manutencao",
				"/minha-conta", "/orders", "/maintenance/view",
			},
			APIs: []string{
				"/api/user/me", "/api/auth/logout", "/api/maintenance", "/api/maintenance/:id",
				"/api/stations", "/api/notifications/settings", "/api/notifications/subscribe",
			},
		},
		{
			Name:        "prestadores",
			Description: "Prestador de Serviço",
			Pages: []string{
				"/dashboard", "/calendario-flip", "/minha-conta", "/maintenance/view",
			},
			APIs: []string{
				"/api/user/me", "/api/auth/logout", "/api/maintenance/:id",
				"/api/notifications/settings", "/api/notifications/subscribe",
			},
		},
	}

	// Imprimir o cabeçalho do relatório
	fmt.Println("# RELATÓRIO DE PERMISSÕES DE USUÁRIOS")
	fmt.Println("## Sistema de Manutenção Shell Tradição")
	fmt.Println("Data: 31/03/2025")
	fmt.Println("Versão: 1.0")
	fmt.Println("---")

	// Imprimir permissões por papel
	fmt.Println("## Permissões por Papel (Role)")
	for _, role := range roles {
		fmt.Printf("\n### %s (%s)\n\n", role.Name, role.Description)

		fmt.Println("#### Páginas Permitidas")
		fmt.Println("```")
		for _, page := range role.Pages {
			fmt.Println(page)
		}
		fmt.Println("```")

		fmt.Println("\n#### APIs Permitidas")
		fmt.Println("```")
		for _, api := range role.APIs {
			fmt.Println(api)
		}
		fmt.Println("```")
	}

	// Imprimir permissões por página
	fmt.Println("\n## Permissões por Página")
	allPages := make(map[string][]string)

	for _, role := range roles {
		for _, page := range role.Pages {
			allPages[page] = append(allPages[page], role.Name)
		}
	}

	pageNames := make([]string, 0, len(allPages))
	for page := range allPages {
		pageNames = append(pageNames, page)
	}
	// sort.Strings(pageNames)

	for _, page := range pageNames {
		fmt.Printf("\n### %s\n\n", page)
		fmt.Println("Papéis com acesso:")
		fmt.Println("```")
		for _, role := range allPages[page] {
			fmt.Println(role)
		}
		fmt.Println("```")
	}

	// Diagnóstico do problema de acesso
	fmt.Println("\n## Diagnóstico de Problemas de Acesso")
	fmt.Println("\n### Análise do Problema com '/minha-conta'")
	fmt.Println("O problema com o acesso à página '/minha-conta' pode ser devido a:")
	fmt.Println("1. A sessão não tem o token JWT válido - o middleware de autenticação rejeita o acesso")
	fmt.Println("2. O token JWT tem assinatura inválida - vimos logs indicando 'token signature is invalid: signature is invalid'")
	fmt.Println("3. O token JWT tem uma role/papel diferente do esperado (não é 'admin')")
	fmt.Println("4. A middleware de autenticação está usando uma chave JWT diferente da usada para gerar o token")

	fmt.Println("\n### Solução Implementada")
	fmt.Println("1. A rota '/minha-conta' foi temporariamente movida para o grupo público (sem autenticação)")
	fmt.Println("2. Isso permite diagnóstico sem o bloqueio pela middleware de autenticação")
	fmt.Println("3. A mudança é temporária para permitir acesso e diagnóstico mais profundo do problema")
	fmt.Println("\n*Quando o problema for identificado e corrigido, a rota deve ser movida de volta para o grupo protegido*")

	// Instruções para login
	fmt.Println("\n## Instruções para Login")
	fmt.Println("\nPara acessar o sistema com privilégios de administrador:")
	fmt.Println("1. Use o email: <EMAIL>")
	fmt.Println("2. Use a senha: i1t2@3l4O5")
	fmt.Println("\nOBS: Observamos em logs que a senha 'admin123' foi tentada mas resultou em 'Senha incorreta para usuário: <EMAIL>'")
}
