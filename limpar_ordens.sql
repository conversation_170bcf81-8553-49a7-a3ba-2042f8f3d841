-- <PERSON><PERSON>, vamos listar todas as ordens existentes
SELECT id, branch_id, equipment_id, technician_id, service_provider_id, number, status 
FROM maintenance_orders;

-- Listar ordens associadas ao técnico específico (ID 94)
SELECT id, branch_id, equipment_id, technician_id, service_provider_id, number, status 
FROM maintenance_orders
WHERE technician_id = 94 OR service_provider_id IN (SELECT service_provider_id FROM users WHERE id = 94);

-- Selecionar uma ordem para manter (a mais recente do técnico 94)
SELECT id, branch_id, equipment_id, technician_id, service_provider_id, number, status 
FROM maintenance_orders
WHERE technician_id = 94
ORDER BY id DESC
LIMIT 1;

-- Excluir todas as ordens exceto a selecionada
-- ATENÇÃO: Descomente a linha abaixo para executar a exclusão
-- DELETE FROM maintenance_orders WHERE id NOT IN (SELECT id FROM maintenance_orders WHERE technician_id = 94 ORDER BY id DESC LIMIT 1);
