package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	_ "github.com/lib/pq"
	"golang.org/x/crypto/bcrypt"
)

func main() {
	// Obter a string de conexão da variável de ambiente
	connStr := os.Getenv("DATABASE_URL")
	if connStr == "" {
		// Construir manualmente se DATABASE_URL não estiver disponível
		host := os.Getenv("DB_HOST")
		port := os.Getenv("DB_PORT")
		user := os.Getenv("DB_USER")
		password := os.Getenv("DB_PASS")
		dbname := os.Getenv("DB_NAME")

		if host == "" || port == "" || user == "" || password == "" || dbname == "" {
			// Usar valores padrão se não houver variáveis de ambiente
			host = "postgres-ag-br1-03.conteige.cloud"
			port = "54243"
			user = "fcobdj_tradicao"
			password = "67573962"
			dbname = "fcobdj_tradicao"
		}

		connStr = fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable",
			user, password, host, port, dbname)
	}

	fmt.Println("Conectando ao banco de dados...")

	// Conecta ao banco de dados
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}
	defer db.Close()

	// Verifica a conexão
	err = db.Ping()
	if err != nil {
		log.Fatalf("Erro ao verificar conexão com o banco: %v", err)
	}
	fmt.Println("Conexão com o banco de dados estabelecida com sucesso")

	// Gera o hash da nova senha
	password := "i1t2@3l4O5"
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		log.Fatalf("Erro ao gerar hash da senha: %v", err)
	}

	// Atualiza a senha do administrador
	query := `
                UPDATE users
                SET password = $1
                WHERE email = $2
        `

	result, err := db.Exec(query, string(hashedPassword), "<EMAIL>")
	if err != nil {
		log.Fatalf("Erro ao atualizar senha: %v", err)
	}

	// Atualiza os timestamps
	queryTimestamps := `
                UPDATE users
                SET updated_at = NOW(), last_password_change = NOW()
                WHERE email = $1
        `

	_, err = db.Exec(queryTimestamps, "<EMAIL>")
	if err != nil {
		log.Fatalf("Erro ao atualizar timestamps: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.Fatalf("Erro ao obter linhas afetadas: %v", err)
	}

	if rowsAffected == 0 {
		fmt.Println("Nenhum usuário com <NAME_EMAIL> foi encontrado.")
	} else {
		fmt.Println("Senha do administrador redefinida com sucesso para 'i1t2@3l4O5'")
	}
}
