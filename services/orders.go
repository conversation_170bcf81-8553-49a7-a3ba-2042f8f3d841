package services

import (
	"encoding/json"
	"fmt" // Add this import
	"math/rand"
	"net/http"
	"time"
)

// ServiceOrder representa uma ordem de serviço
type ServiceOrder struct {
	ID          string    `json:"id"`
	Date        time.Time `json:"date"`
	BranchName  string    `json:"branchName"`
	Priority    string    `json:"priority"`
	Description string    `json:"description"`
	Status      string    `json:"status"`
	Technician  string    `json:"technician"`
}

// Branches representa as filiais disponíveis
var Branches = []string{
	"Posto Shell - Centro",
	"Posto Shell - Zona Sul",
	"Posto Shell - Zona Norte",
	"Posto Shell - Rodovia",
	"Posto Shell - Litoral",
}

// GetServiceOrders retorna as ordens de serviço
func GetServiceOrders(w http.ResponseWriter, r *http.Request) {
	// Gera dados de exemplo
	orders := generateSampleOrders()

	w.<PERSON>er().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(orders)
}

// Gera ordens de serviço de exemplo
func generateSampleOrders() []ServiceOrder {
	rand.Seed(time.Now().UnixNano())

	priorities := []string{"high", "medium", "low"}
	descriptions := []string{
		"Manutenção preventiva - Bomba",
		"Calibração de bicos injetores",
		"Troca de filtros",
		"Verificação de vazamentos",
		"Atualização de software",
		"Limpeza de tanques",
	}
	statuses := []string{"pending", "in_progress", "completed"}
	technicians := []string{"Carlos Silva", "Ana Oliveira", "Roberto Santos", "Juliana Lima"}

	var orders []ServiceOrder

	// Gera ordens para o mês atual
	currentDate := time.Now()
	year, month, _ := currentDate.Date()

	// Gera entre 15-25 ordens para o mês
	numOrders := rand.Intn(10) + 15

	for i := 0; i < numOrders; i++ {
		day := rand.Intn(28) + 1 // Dias 1-28 para evitar problemas com meses curtos

		order := ServiceOrder{
			ID:          fmt.Sprintf("OS%04d", rand.Intn(9000)+1000),
			Date:        time.Date(year, month, day, 9, 0, 0, 0, time.Local),
			BranchName:  Branches[rand.Intn(len(Branches))],
			Priority:    priorities[rand.Intn(len(priorities))],
			Description: descriptions[rand.Intn(len(descriptions))],
			Status:      statuses[rand.Intn(len(statuses))],
			Technician:  technicians[rand.Intn(len(technicians))],
		}

		orders = append(orders, order)
	}

	return orders
}
