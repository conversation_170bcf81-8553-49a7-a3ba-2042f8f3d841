package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"entgo.io/ent/dialect/sql"
	_ "github.com/lib/pq"
	"tradicao/internal/ent"
	"tradicao/internal/ent/user"
	"tradicao/internal/ent/technician"
	"tradicao/internal/ent/serviceprovider"
)

func main() {
	// Configurações do banco de dados
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		"postgres-ag-br1-03.conteige.cloud",
		"54243",
		"fcobdj_tradicao",
		"67573962",
		"fcobdj_tradicao",
	)

	// Inicializar cliente ENT
	client, err := ent.Open("postgres", dsn)
	if err != nil {
		log.Fatalf("Falha ao conectar ao banco de dados: %v", err)
	}
	defer client.Close()

	// Verificar conexão
	ctx := context.Background()
	if err := client.Schema.Verify(ctx); err != nil {
		log.Fatalf("Falha ao verificar schema: %v", err)
	}

	fmt.Println("Conexão com o banco de dados estabelecida com sucesso!")

	// Email do usuário técnico
	email := "<EMAIL>"

	// Buscar usuário pelo email
	u, err := client.User.Query().
		Where(user.EmailEQ(email)).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			log.Fatalf("Usuário com email %s não encontrado", email)
		}
		log.Fatalf("Erro ao buscar usuário: %v", err)
	}

	fmt.Println("=== Informações do Usuário ===")
	fmt.Printf("ID: %d\n", u.ID)
	fmt.Printf("Nome: %s\n", u.Name)
	fmt.Printf("Email: %s\n", u.Email)
	fmt.Printf("Tipo/Perfil: %s\n", u.Type)
	fmt.Printf("Bloqueado: %t\n", u.Blocked)
	fmt.Printf("Branch ID: %d\n", u.BranchID)

	// Verificar se o usuário é um técnico
	tech, err := client.Technician.Query().
		Where(technician.UserIDEQ(u.ID)).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			fmt.Println("\n⚠️ PROBLEMA DETECTADO: Usuário não está associado a nenhum registro na tabela technicians")
			fmt.Println("Isso explica o problema de login, pois o sistema espera que um usuário do tipo 'tecnico' tenha um registro correspondente na tabela technicians")
		} else {
			fmt.Printf("Erro ao buscar técnico: %v\n", err)
		}
	} else {
		fmt.Println("\n=== Informações do Técnico ===")
		fmt.Printf("ID do Técnico: %d\n", tech.ID)
		fmt.Printf("ID do Usuário: %d\n", tech.UserID)
		fmt.Printf("ID do Prestador: %d\n", tech.ProviderID)

		// Buscar informações do prestador de serviço
		provider, err := client.ServiceProvider.Query().
			Where(serviceprovider.IDEQ(tech.ProviderID)).
			Only(ctx)

		if err != nil {
			if ent.IsNotFound(err) {
				fmt.Printf("\n⚠️ PROBLEMA DETECTADO: Prestador com ID %d não encontrado\n", tech.ProviderID)
				fmt.Println("Isso pode causar problemas de login, pois o sistema espera que o prestador exista")
			} else {
				fmt.Printf("Erro ao buscar prestador: %v\n", err)
			}
		} else {
			fmt.Println("\n=== Informações do Prestador de Serviço ===")
			fmt.Printf("ID: %d\n", provider.ID)
			fmt.Printf("Nome: %s\n", provider.Name)
			fmt.Printf("Status: %s\n", provider.Status)
		}
	}

	// Verificar se existem outros usuários do tipo técnico que conseguem fazer login
	otherTechs, err := client.User.Query().
		Where(
			user.TypeEQ("tecnico"),
			user.EmailNEQ(email),
		).
		Limit(5).
		All(ctx)

	if err != nil {
		fmt.Printf("Erro ao buscar outros técnicos: %v\n", err)
	} else {
		fmt.Println("\n=== Outros Usuários Técnicos para Comparação ===")
		for i, otherTech := range otherTechs {
			fmt.Printf("%d. ID: %d, Nome: %s, Email: %s\n", i+1, otherTech.ID, otherTech.Name, otherTech.Email)

			// Verificar se este técnico tem registro na tabela technicians
			hasTechRecord, err := client.Technician.Query().
				Where(technician.UserIDEQ(otherTech.ID)).
				Exist(ctx)

			if err != nil {
				fmt.Printf("   Erro ao verificar registro de técnico: %v\n", err)
			} else if hasTechRecord {
				fmt.Println("   ✅ Tem registro na tabela technicians")
			} else {
				fmt.Println("   ❌ Não tem registro na tabela technicians")
			}
		}
	}

	fmt.Println("\n=== Conclusão do Diagnóstico ===")
	fmt.Println("Baseado na análise, o problema de login provavelmente está relacionado a:")
	fmt.Println("1. Falta de registro na tabela technicians para este usuário")
	fmt.Println("2. Ou registro existente mas com provider_id inválido")
	fmt.Println("3. Ou inconsistência entre o tipo de usuário e os registros relacionados")
}
