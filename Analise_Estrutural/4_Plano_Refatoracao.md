# Plano de Refatoração para Padronização das Relações no Sistema de Gestão Tradição

## 1. Visão Geral da Estratégia

A refatoração será realizada em fases incrementais para minimizar o impacto no sistema em produção. Cada fase será implementada, testada e implantada separadamente, garantindo que o sistema continue funcionando corretamente durante todo o processo.

### Princípios Orientadores

1. **Compatibilidade Retroativa**: Manter compatibilidade com código existente durante a transição
2. **Testes Abrangentes**: Implementar testes automatizados para cada mudança
3. **Implantação Gradual**: Implantar mudanças incrementalmente
4. **Documentação Clara**: Documentar todas as mudanças e decisões de design
5. **Migrações Seguras**: Implementar migrações de banco de dados com possibilidade de rollback

## 2. Fases de Refatoração

### Fase 1: Padronização de Modelos e Nomenclatura

#### 1.1 Consolidação do Modelo de Filial

1. **Criar Modelo Unificado**:
   - Implementar o modelo `Branch` unificado em `internal/models/branch.go`
   - Adicionar todos os campos e relações necessários
   - Manter compatibilidade com código existente

2. **Implementar Adaptadores**:
   - Criar adaptadores para converter entre o modelo unificado e os modelos legados
   - Implementar métodos de conversão em ambas as direções
   - Documentar o uso dos adaptadores

3. **Atualizar Repositórios**:
   - Refatorar repositórios para usar o modelo unificado
   - Implementar camada de compatibilidade para código legado
   - Atualizar consultas SQL conforme necessário

#### 1.2 Padronização de Nomenclatura

1. **Criar Guia de Nomenclatura**:
   - Documentar a nomenclatura padronizada para todos os conceitos
   - Distribuir o guia para a equipe de desenvolvimento

2. **Atualizar Comentários e Documentação**:
   - Atualizar comentários no código para usar a nomenclatura padronizada
   - Atualizar documentação técnica e de usuário

3. **Refatorar Variáveis e Métodos**:
   - Renomear variáveis e métodos para seguir a nomenclatura padronizada
   - Manter compatibilidade através de aliases quando necessário

### Fase 2: Padronização das Relações entre Entidades

#### 2.1 Relação Filial-Prestadora

1. **Padronizar Tabela de Relacionamento**:
   - Verificar se a tabela `provider_branches` existe e tem a estrutura correta
   - Adicionar campos faltantes através de migração
   - Criar índices para otimizar consultas

2. **Migrar Dados Existentes**:
   - Identificar relações existentes em outras tabelas ou formatos
   - Migrar dados para a tabela padronizada
   - Verificar integridade dos dados após migração

3. **Atualizar Código**:
   - Refatorar código para usar a tabela padronizada
   - Implementar novos métodos no repositório
   - Atualizar serviços que utilizam estas relações

#### 2.2 Relação Técnico-Prestadora

1. **Padronizar Modelo de Usuário**:
   - Verificar se o modelo `User` tem o campo `ServiceProviderID`
   - Adicionar campo e relação se necessário
   - Criar índice para otimizar consultas

2. **Migrar Dados Existentes**:
   - Identificar técnicos vinculados a prestadoras em outros formatos
   - Migrar dados para o formato padronizado
   - Verificar integridade dos dados após migração

3. **Atualizar Código**:
   - Refatorar código para usar o modelo padronizado
   - Implementar novos métodos no repositório
   - Atualizar serviços que utilizam estas relações

#### 2.3 Relação Técnico-Filial

1. **Padronizar Tabela de Relacionamento**:
   - Verificar se a tabela `technician_branches` existe e tem a estrutura correta
   - Adicionar campos faltantes através de migração
   - Criar índices para otimizar consultas

2. **Migrar Dados Existentes**:
   - Identificar relações existentes em outras tabelas ou formatos
   - Migrar dados para a tabela padronizada
   - Verificar integridade dos dados após migração

3. **Atualizar Código**:
   - Refatorar código para usar a tabela padronizada
   - Implementar novos métodos no repositório
   - Atualizar serviços que utilizam estas relações

#### 2.4 Relação Técnico-Ordem

1. **Padronizar Tabela de Relacionamento**:
   - Verificar se a tabela `technician_orders` existe e tem a estrutura correta
   - Adicionar campos faltantes através de migração
   - Criar índices para otimizar consultas

2. **Migrar Dados Existentes**:
   - Identificar atribuições existentes através do campo `TechnicianID` na tabela `maintenance_orders`
   - Migrar dados para a tabela padronizada
   - Verificar integridade dos dados após migração

3. **Atualizar Código**:
   - Refatorar código para usar a tabela padronizada
   - Implementar novos métodos no repositório
   - Atualizar serviços que utilizam estas relações

### Fase 3: Padronização do Sistema de Atribuição de Ordens

#### 3.1 Implementação do Serviço de Atribuição Centralizado

1. **Criar Serviço Centralizado**:
   - Implementar `OrderAssignmentService` em `internal/services/order_assignment_service.go`
   - Implementar todos os métodos necessários
   - Adicionar testes unitários abrangentes

2. **Migrar Código Existente**:
   - Identificar código de atribuição em diferentes partes do sistema
   - Refatorar para usar o serviço centralizado
   - Manter compatibilidade com código legado durante a transição

3. **Atualizar Handlers e Controllers**:
   - Refatorar handlers e controllers para usar o serviço centralizado
   - Implementar novos endpoints se necessário
   - Atualizar documentação da API

#### 3.2 Padronização do Fluxo de Atribuição

1. **Documentar Fluxo Padronizado**:
   - Criar documentação detalhada do fluxo de atribuição
   - Incluir diagramas de sequência e fluxogramas
   - Distribuir para a equipe de desenvolvimento

2. **Implementar Hooks e Eventos**:
   - Implementar sistema de hooks para eventos de atribuição
   - Integrar com o sistema de notificações
   - Adicionar logging detalhado para rastreabilidade

3. **Atualizar Interface do Usuário**:
   - Refatorar interface para refletir o fluxo padronizado
   - Implementar novas telas ou componentes se necessário
   - Atualizar mensagens e tooltips

### Fase 4: Padronização do Sistema de Permissões

#### 4.1 Consolidação do Sistema de Permissões

1. **Finalizar Implementação do Sistema Centralizado**:
   - Completar a implementação do `ResourcePermissionService`
   - Implementar cache para otimizar verificações frequentes
   - Adicionar testes unitários abrangentes

2. **Migrar Verificações Existentes**:
   - Identificar verificações de permissão em diferentes partes do sistema
   - Refatorar para usar o sistema centralizado
   - Remover código duplicado e verificações hardcoded

3. **Atualizar Configurações de Permissões**:
   - Revisar e atualizar configurações YAML
   - Adicionar novas permissões para recursos específicos
   - Documentar todas as permissões disponíveis

#### 4.2 Padronização dos Middlewares

1. **Consolidar Middlewares**:
   - Padronizar implementação do `ResourcePermissionMiddleware`
   - Depreciar middlewares legados
   - Adicionar testes de integração

2. **Atualizar Rotas**:
   - Refatorar rotas para usar os middlewares padronizados
   - Verificar cobertura de todas as rotas
   - Testar exaustivamente diferentes cenários de permissão

3. **Implementar Auditoria**:
   - Adicionar logging detalhado para verificações de permissão
   - Implementar sistema de auditoria para ações sensíveis
   - Criar dashboard para monitoramento de permissões

## 3. Plano de Migração de Dados

### 3.1 Migração de Dados de Filiais

1. **Análise de Dados Existentes**:
   - Identificar todas as tabelas que contêm dados de filiais
   - Mapear campos e relações
   - Identificar possíveis duplicações ou inconsistências

2. **Script de Migração**:
   - Implementar script para migrar dados para o modelo unificado
   - Incluir validações e verificações de integridade
   - Adicionar logging detalhado para rastreabilidade

3. **Teste e Validação**:
   - Testar script em ambiente de desenvolvimento
   - Validar dados migrados
   - Corrigir problemas identificados

### 3.2 Migração de Relações Técnico-Ordem

1. **Análise de Dados Existentes**:
   - Identificar ordens com técnicos atribuídos através do campo `TechnicianID`
   - Mapear relações existentes
   - Identificar possíveis duplicações ou inconsistências

2. **Script de Migração**:
   - Implementar script para migrar dados para a tabela `technician_orders`
   - Incluir validações e verificações de integridade
   - Adicionar logging detalhado para rastreabilidade

3. **Teste e Validação**:
   - Testar script em ambiente de desenvolvimento
   - Validar dados migrados
   - Corrigir problemas identificados

### 3.3 Migração de Outras Relações

1. **Análise de Dados Existentes**:
   - Identificar relações existentes em formatos não padronizados
   - Mapear dados e relações
   - Identificar possíveis duplicações ou inconsistências

2. **Script de Migração**:
   - Implementar script para migrar dados para as tabelas padronizadas
   - Incluir validações e verificações de integridade
   - Adicionar logging detalhado para rastreabilidade

3. **Teste e Validação**:
   - Testar script em ambiente de desenvolvimento
   - Validar dados migrados
   - Corrigir problemas identificados

## 4. Plano de Testes

### 4.1 Testes Unitários

1. **Cobertura de Modelos**:
   - Implementar testes para todos os modelos padronizados
   - Testar métodos de conversão e adaptadores
   - Verificar validações e constraints

2. **Cobertura de Serviços**:
   - Implementar testes para o serviço de atribuição centralizado
   - Testar diferentes cenários de atribuição
   - Verificar tratamento de erros

3. **Cobertura de Permissões**:
   - Implementar testes para o sistema de permissões
   - Testar diferentes cenários de verificação
   - Verificar cache e otimizações

### 4.2 Testes de Integração

1. **Fluxos de Atribuição**:
   - Testar fluxo completo de atribuição de ordens
   - Verificar consistência dos dados em todas as tabelas
   - Testar cenários de erro e recuperação

2. **Verificação de Permissões**:
   - Testar middleware de permissões em diferentes rotas
   - Verificar acesso para diferentes perfis de usuário
   - Testar cenários de negação de acesso

3. **Migração de Dados**:
   - Testar scripts de migração em ambiente controlado
   - Verificar integridade dos dados após migração
   - Testar rollback em caso de falha

### 4.3 Testes de Aceitação

1. **Validação de Interface**:
   - Testar interface do usuário após refatoração
   - Verificar fluxos de trabalho para diferentes perfis
   - Coletar feedback dos usuários

2. **Validação de Desempenho**:
   - Medir desempenho antes e depois da refatoração
   - Identificar possíveis gargalos
   - Otimizar consultas e operações críticas

3. **Validação de Segurança**:
   - Verificar controle de acesso após refatoração
   - Testar cenários de tentativa de acesso não autorizado
   - Validar auditoria e logging

## 5. Cronograma de Implementação

### 5.1 Fase 1: Padronização de Modelos e Nomenclatura
- **Duração**: 2 semanas
- **Dependências**: Nenhuma
- **Riscos**: Impacto em código existente, possíveis regressões

### 5.2 Fase 2: Padronização das Relações entre Entidades
- **Duração**: 3 semanas
- **Dependências**: Fase 1
- **Riscos**: Migração de dados, integridade referencial

### 5.3 Fase 3: Padronização do Sistema de Atribuição de Ordens
- **Duração**: 2 semanas
- **Dependências**: Fase 2
- **Riscos**: Mudanças no fluxo de trabalho, treinamento de usuários

### 5.4 Fase 4: Padronização do Sistema de Permissões
- **Duração**: 2 semanas
- **Dependências**: Fase 3
- **Riscos**: Segurança, controle de acesso

### 5.5 Testes e Implantação Final
- **Duração**: 1 semana
- **Dependências**: Todas as fases anteriores
- **Riscos**: Integração, desempenho, aceitação do usuário
