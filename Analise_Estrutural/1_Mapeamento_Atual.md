# Mapeamento Atual das Relações no Sistema de Gestão Tradição

## 1. Relação entre Filiais e Prestadoras/Técnicos

### 1.1 Estruturas de Dados

#### Tabelas Principais
- `branches`: Armazena informações sobre filiais/postos
- `service_providers`: Armazena informações sobre prestadoras de serviço
- `users`: Armazena usuários, incluindo técnicos
- `provider_branches`: Tabela de relacionamento entre prestadoras e filiais
- `technician_branches`: Tabela de relacionamento entre técnicos e filiais

#### Modelos Relevantes

**Branch (Filial)**
```go
type Branch struct {
    ID              uint           `gorm:"primaryKey" json:"id"`
    Name            string         `gorm:"size:100;not null" json:"name"`
    Code            string         `gorm:"size:20;unique;not null" json:"code"`
    Address         string         `gorm:"size:255" json:"address"`
    City            string         `gorm:"size:100" json:"city"`
    State           string         `gorm:"size:2" json:"state"`
    ZipCode         string         `gorm:"size:20" json:"zip_code"`
    Phone           string         `gorm:"size:20" json:"phone"`
    Email           string         `gorm:"size:100" json:"email"`
    IsActive        bool           `gorm:"default:true" json:"is_active"`
    BranchType      string         `gorm:"size:20;default:'urbano'" json:"branch_type"`
    ResponsibleName string         `gorm:"size:100" json:"responsible_name"`
    Notes           string         `gorm:"size:500" json:"notes"`
    ContactInfo     string         `gorm:"size:500" json:"contact_info"`
    CreatedAt       time.Time      `json:"created_at"`
    UpdatedAt       time.Time      `json:"updated_at"`
    DeletedAt       gorm.DeletedAt `gorm:"index" json:"-"`
}
```

**ServiceProvider (Prestadora)**
```go
type ServiceProvider struct {
    ID              uint      `json:"id" gorm:"primaryKey"`
    Name            string    `json:"name" gorm:"not null"`
    CompanyName     string    `json:"company_name" gorm:"not null"`
    CNPJ            string    `json:"cnpj" gorm:"not null;unique"`
    Address         string    `json:"address"`
    City            string    `json:"city"`
    State           string    `json:"state"`
    ZipCode         string    `json:"zip_code"`
    ContactName     string    `json:"contact_name"`
    ContactEmail    string    `json:"contact_email"`
    ContactPhone    string    `json:"contact_phone"`
    Specialties     string    `json:"specialties"`
    AreaOfExpertise string    `json:"area_of_expertise"`
    AverageRating   float64   `json:"average_rating"`
    Status          string    `json:"status" gorm:"default:active"`
    LogoURL         string    `json:"logo_url"`
    CreatedAt       time.Time `json:"created_at"`
    UpdatedAt       time.Time `json:"updated_at"`
    
    // Relações
    Technicians []User `json:"technicians,omitempty" gorm:"foreignKey:ServiceProviderID"`
}
```

**ProviderBranch (Relação Prestadora-Filial)**
```go
type ProviderBranch struct {
    ID                uint      `json:"id" gorm:"primaryKey"`
    ServiceProviderID uint      `json:"service_provider_id" gorm:"not null"`
    BranchID          uint      `json:"branch_id" gorm:"not null"`
    CreatedAt         time.Time `json:"created_at"`
    UpdatedAt         time.Time `json:"updated_at"`
    
    // Relações
    ServiceProvider   ServiceProvider `json:"service_provider,omitempty" gorm:"foreignKey:ServiceProviderID"`
    Branch            Branch          `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
}
```

**TechnicianBranch (Relação Técnico-Filial)**
```go
type TechnicianBranch struct {
    TechnicianID uint      `gorm:"primaryKey" json:"technician_id"`
    BranchID     uint      `gorm:"primaryKey" json:"branch_id"`
    SpecialtyID  uint      `gorm:"primaryKey" json:"specialty_id"`
    CreatedAt    time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
    UpdatedAt    time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`

    // Relacionamentos
    Technician User                `gorm:"foreignKey:TechnicianID" json:"technician"`
    Branch     BranchModel         `gorm:"foreignKey:BranchID" json:"branch"`
    Specialty  TechnicianSpecialty `gorm:"foreignKey:SpecialtyID" json:"specialty"`
}
```

### 1.2 Fluxo de Dados e Lógica de Negócio

1. **Vinculação de Prestadoras a Filiais**:
   - Uma prestadora pode ser vinculada a múltiplas filiais através da tabela `provider_branches`
   - A vinculação é feita através do endpoint `AssignProviderToBranch` no arquivo `internal/handlers/provider_assignment_handlers.go`
   - O serviço `AutoAssignmentService` gerencia estas atribuições

2. **Vinculação de Técnicos a Filiais**:
   - Técnicos podem ser vinculados a filiais através da tabela `technician_branches`
   - Esta vinculação inclui uma especialidade específica para o técnico naquela filial
   - A consulta de filiais atribuídas a um técnico é feita em `internal/services/filial_filter_service.go`

## 2. Relação entre Técnicos e Prestadoras

### 2.1 Estruturas de Dados

#### Tabelas Principais
- `users`: Armazena usuários, incluindo técnicos
- `service_providers`: Armazena informações sobre prestadoras de serviço
- `service_provider_managers`: Armazena gestores de prestadoras

#### Modelos Relevantes

**User (Usuário/Técnico)**
```go
type User struct {
    ID                  uint             `json:"id" gorm:"primaryKey"`
    Name                string           `json:"name"`
    Email               string           `json:"email" gorm:"unique"`
    Password            string           `json:"-"`
    Role                UserRole         `json:"role" gorm:"column:type"`
    BranchID            *uint            `json:"branch_id,omitempty"`
    ServiceProviderID   *uint            `json:"service_provider_id,omitempty" gorm:"index"`
    // Outros campos...
    Branch              *Branch          `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
    ServiceProvider     *ServiceProvider `json:"service_provider,omitempty" gorm:"foreignKey:ServiceProviderID"`
}
```

**ServiceProviderManager (Gestor de Prestadora)**
```go
type ServiceProviderManager struct {
    ID                uint      `json:"id" gorm:"primaryKey"`
    UserID            uint      `json:"user_id" gorm:"not null"`
    ServiceProviderID uint      `json:"service_provider_id" gorm:"not null"`
    Role              string    `json:"role" gorm:"not null"` // 'owner', 'manager', 'admin'
    CreatedAt         time.Time `json:"created_at"`
    UpdatedAt         time.Time `json:"updated_at"`
    
    // Relações
    User              User              `json:"user" gorm:"foreignKey:UserID"`
    ServiceProvider   ServiceProvider   `json:"service_provider" gorm:"foreignKey:ServiceProviderID"`
}
```

### 2.2 Fluxo de Dados e Lógica de Negócio

1. **Vinculação de Técnicos a Prestadoras**:
   - Técnicos são vinculados a prestadoras através do campo `ServiceProviderID` na tabela `users`
   - A prestadora pode acessar todos os seus técnicos através da relação `Technicians` no modelo `ServiceProvider`
   - O repositório `GormServiceProviderRepository` fornece métodos para gerenciar técnicos:
     - `GetTechnicians`: Retorna todos os técnicos de uma prestadora
     - `AddTechnician`: Adiciona um técnico a uma prestadora
     - `RemoveTechnician`: Remove um técnico de uma prestadora

2. **Gestão de Prestadoras**:
   - Usuários podem ser designados como gestores de prestadoras através da tabela `service_provider_managers`
   - Um gestor pode ter diferentes papéis dentro da prestadora (proprietário, gerente, administrador)

## 3. Relação entre Ordens de Serviço, Filiais e Prestadoras

### 3.1 Estruturas de Dados

#### Tabelas Principais
- `maintenance_orders`: Armazena ordens de manutenção
- `branches`: Armazena informações sobre filiais
- `service_providers`: Armazena informações sobre prestadoras
- `provider_branches`: Tabela de relacionamento entre prestadoras e filiais

#### Modelos Relevantes

**MaintenanceOrder (Ordem de Manutenção)**
```go
type MaintenanceOrder struct {
    ID                 uint          `gorm:"primaryKey" json:"id"`
    BranchID           uint          `gorm:"column:branch_id" json:"branch_id"`
    BranchName         string        `gorm:"-" json:"branch_name"`
    EquipmentID        uint          `gorm:"column:equipment_id" json:"equipment_id"`
    ServiceProviderID  *uint         `gorm:"column:service_provider_id" json:"service_provider_id,omitempty"`
    TechnicianID       *uint         `json:"technician_id" gorm:"column:technician_id"`
    CreatedByUserID    uint          `gorm:"column:created_by_user_id" json:"created_by_user_id"`
    // Outros campos...
}
```

### 3.2 Fluxo de Dados e Lógica de Negócio

1. **Criação de Ordens de Manutenção**:
   - Uma filial cria uma ordem de manutenção para um equipamento
   - A ordem é registrada com o `BranchID` da filial que a criou
   - O sistema pode atribuir automaticamente a ordem a uma prestadora através do serviço `AutoAssignmentService`

2. **Atribuição Automática de Prestadoras**:
   - Quando uma ordem é criada, o sistema tenta atribuí-la automaticamente a uma prestadora elegível
   - A elegibilidade é determinada com base no tipo de equipamento e na filial
   - O serviço `AutoAssignmentService` implementa esta lógica em `internal/services/auto_assignment_service.go`

3. **Fluxo de Atribuição**:
   - Filial cria ordem → Sistema busca prestadoras elegíveis → Sistema atribui ordem a uma prestadora → Sistema notifica a prestadora
   - A prestadora pode então atribuir a ordem a um de seus técnicos

## 4. Relação entre Técnicos e Ordens de Serviço

### 4.1 Estruturas de Dados

#### Tabelas Principais
- `maintenance_orders`: Armazena ordens de manutenção
- `users`: Armazena usuários, incluindo técnicos
- `technician_orders`: Tabela de relacionamento entre técnicos e ordens

#### Modelos Relevantes

**TechnicianOrder (Relação Técnico-Ordem)**
```go
type TechnicianOrder struct {
    ID           uint      `json:"id" gorm:"primaryKey"`
    TechnicianID uint      `json:"technician_id" gorm:"column:technician_id;not null"`
    OrderID      uint      `json:"order_id" gorm:"column:order_id;not null"`
    CreatedAt    time.Time `json:"created_at" gorm:"column:created_at;default:CURRENT_TIMESTAMP"`
    CreatedBy    *uint     `json:"created_by,omitempty" gorm:"column:created_by"`
    Notes        string    `json:"notes,omitempty" gorm:"column:notes"`

    // Relacionamentos
    Technician *User             `json:"technician,omitempty" gorm:"foreignKey:TechnicianID"`
    Order      *MaintenanceOrder `json:"order,omitempty" gorm:"foreignKey:OrderID"`
    Creator    *User             `json:"creator,omitempty" gorm:"foreignKey:CreatedBy"`
}
```

### 4.2 Fluxo de Dados e Lógica de Negócio

1. **Atribuição de Ordens a Técnicos**:
   - Uma ordem pode ser atribuída a um técnico através da tabela `technician_orders`
   - A atribuição pode ser feita por um gestor da prestadora ou automaticamente pelo sistema
   - O serviço `TechnicianOrderService` gerencia estas atribuições

2. **Verificação de Permissões**:
   - O sistema verifica se um técnico tem permissão para acessar uma ordem através do middleware `VerificarPermissaoOrdem`
   - Um técnico pode acessar ordens atribuídas diretamente a ele ou ao seu prestador de serviço
   - O sistema de permissões centralizado também é utilizado para controlar o acesso

3. **Fluxo de Trabalho**:
   - Prestadora recebe ordem → Prestadora atribui ordem a um técnico → Sistema registra atribuição na tabela `technician_orders` → Sistema notifica o técnico
   - O técnico pode então visualizar e atualizar a ordem conforme necessário
