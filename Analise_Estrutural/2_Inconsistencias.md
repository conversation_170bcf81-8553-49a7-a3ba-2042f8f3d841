# Inconsistências nas Relações do Sistema de Gestão Tradição

## 1. Duplicidade de Modelos e Tabelas

### 1.1 Modelos Duplicados para Filiais

O sistema possui múltiplos modelos que representam o mesmo conceito de filial:

- `Branch`: Modelo principal para filiais (`internal/models/branch.go`)
- `Filial`: Modelo alternativo que mapeia para a tabela `stations` (`internal/models/filial.go`)
- `Station`: Outro modelo que também mapeia para a tabela `stations` (`internal/models/station.go`)
- `BranchModel`: Versão simplificada do modelo `Branch` (`internal/models/user.go`)

Esta duplicidade causa confusão e aumenta a complexidade do código, pois diferentes partes do sistema utilizam modelos diferentes para representar o mesmo conceito.

### 1.2 Tabelas Duplicadas para Relações

Existem múltiplas tabelas que armazenam relações semelhantes:

- `branch_station_links`: Relaciona `branches` com `stations`
- `technician_branches`: Relaciona técnicos com filiais
- `provider_branches`: Relaciona prestadoras com filiais

Estas tabelas têm estruturas diferentes e são utilizadas em contextos diferentes, o que dificulta a manutenção e compreensão do sistema.

## 2. Inconsistências na Atribuição de Ordens

### 2.1 Múltiplos Mecanismos de Atribuição

O sistema possui múltiplos mecanismos para atribuir ordens a técnicos:

1. **Campo `TechnicianID` na tabela `maintenance_orders`**:
   - Algumas partes do código atribuem ordens diretamente através deste campo
   - Exemplo: `db.Exec("UPDATE maintenance_orders SET technician_id = ? WHERE id = ?", tecnicoIDUint, ordem.ID)`

2. **Tabela `technician_orders`**:
   - Sistema mais recente que cria um relacionamento explícito entre técnicos e ordens
   - Exemplo: `technicianOrderRepo.Create(technicianID, orderID, createdBy, notes)`

Esta duplicidade causa problemas de consistência, pois uma ordem pode estar atribuída a um técnico em uma tabela mas não na outra.

### 2.2 Verificações Hardcoded para Ordens Específicas

O código contém verificações hardcoded para ordens específicas, como a ordem #18:

```go
// Bloquear acesso à ordem #18 que está causando problemas
if id == 18 {
    ctx.JSON(http.StatusForbidden, gin.H{"error": "Esta ordem não está disponível para atualização. Por favor, contate o suporte."})
    return
}
```

Estas verificações são frágeis e dificultam a manutenção do sistema.

## 3. Inconsistências no Sistema de Permissões

### 3.1 Múltiplos Sistemas de Verificação de Permissões

O sistema possui múltiplos mecanismos para verificar permissões:

1. **Middleware `VerificarPermissaoOrdem`**:
   - Implementa lógica específica para verificar permissões de acesso a ordens
   - Contém verificações hardcoded para diferentes perfis de usuário

2. **Sistema de Permissões Centralizado**:
   - Implementado em `internal/permissions`
   - Utiliza configurações baseadas em YAML para definir permissões

3. **Middleware `ResourcePermissionMiddleware`**:
   - Implementação mais recente que utiliza o sistema de permissões centralizado
   - Mais flexível e extensível

A coexistência destes sistemas causa confusão e pode levar a comportamentos inconsistentes.

### 3.2 Verificações de Permissão Duplicadas

Existem verificações de permissão duplicadas em diferentes partes do código:

1. **Verificações no Controlador**:
   ```go
   if userRole != "admin" && userRole != "gerente" {
       // Verificar permissões específicas
   }
   ```

2. **Verificações no Middleware**:
   ```go
   if userRoleStr == string(models.RoleAdmin) || userRoleStr == string(models.RoleGerente) {
       // Permitir acesso
   }
   ```

3. **Verificações no Serviço de Permissões**:
   ```go
   if role == "admin" {
       return true
   }
   ```

Esta duplicidade aumenta a complexidade do código e dificulta a manutenção.

## 4. Inconsistências na Nomenclatura

### 4.1 Mistura de Termos em Português e Inglês

O sistema mistura termos em português e inglês para os mesmos conceitos:

- `Branch` vs `Filial`
- `Technician` vs `Técnico`
- `ServiceProvider` vs `Prestadora`

Esta mistura dificulta a compreensão do código e pode levar a erros.

### 4.2 Múltiplos Termos para o Mesmo Conceito

O sistema utiliza múltiplos termos para representar o mesmo conceito:

- `Branch`, `Filial`, `Station`, `Posto`
- `ServiceProvider`, `Prestadora`, `Prestador de Serviço`
- `Technician`, `Técnico`, `Prestador`

Esta inconsistência dificulta a busca e compreensão do código.

## 5. Problemas na Estrutura de Dados

### 5.1 Relações Incompletas ou Redundantes

Algumas relações são implementadas de forma incompleta ou redundante:

1. **Relação Técnico-Prestadora**:
   - Implementada através do campo `ServiceProviderID` na tabela `users`
   - Também implementada através da relação `Technicians` no modelo `ServiceProvider`
   - Não há garantia de consistência entre estas duas implementações

2. **Relação Técnico-Filial**:
   - Implementada através da tabela `technician_branches`
   - Também implementada parcialmente através do campo `BranchID` na tabela `users`
   - Não há garantia de consistência entre estas duas implementações

### 5.2 Campos Virtuais e Mapeamentos Manuais

O sistema utiliza campos virtuais e mapeamentos manuais em vários lugares:

```go
// Campo virtual para nome da filial
BranchName string `gorm:"-" json:"branch_name"`

// Campos adicionais para compatibilidade
Type         string   `json:"type" gorm:"-"`
PostalCode   string   `json:"postal_code" gorm:"-"`
PhoneNumber  string   `json:"phone_number" gorm:"-"`
```

Estes campos aumentam a complexidade do código e podem levar a inconsistências se não forem preenchidos corretamente.

## 6. Problemas na Implementação de Funcionalidades

### 6.1 Atribuição Automática Inconsistente

O sistema de atribuição automática de ordens a prestadoras é implementado de forma inconsistente:

1. **Em `internal/services/ordens_service.go`**:
   ```go
   autoAssignmentService := NewAutoAssignmentService(tx)
   if err := autoAssignmentService.AssignOrderToProvider(&ordemParaSalvar); err != nil {
       // Apenas logar o erro, não impedir a criação da ordem
       log.Printf("Erro ao atribuir automaticamente a ordem a um prestador: %s", err.Error())
   }
   ```

2. **Em `internal/services/order_service_adapter.go`**:
   ```go
   if order.ServiceProviderID == nil || *order.ServiceProviderID == 0 {
       autoAssignmentService := NewAutoAssignmentService(tx)
       if err := autoAssignmentService.AssignOrderToProvider(order); err != nil {
           log.Printf("Aviso: Não foi possível atribuir automaticamente a ordem %d a um prestador: %v",
               order.ID, err)
           // Continuar mesmo sem atribuição automática
       }
   }
   ```

Estas implementações duplicadas podem levar a comportamentos inconsistentes.

### 6.2 Notificações Duplicadas

O sistema envia notificações em múltiplos pontos do código:

1. **Em `internal/handlers/atribuir_ordem_handler.go`**:
   ```go
   notificationService := notifications.NewService()
   if err := notificationService.NotifyOrderAssigned(ordem.ID, tecnicoIDUint); err != nil {
       // Apenas logar o erro, não impedir a atribuição
       fmt.Printf("Erro ao enviar notificação para o técnico %d: %s\n", tecnicoIDUint, err.Error())
   }
   ```

2. **Em `internal/services/auto_assignment_service.go`**:
   ```go
   if err := s.notificationService.NotifyProviderOrderAssigned(order.ID, provider.ID); err != nil {
       // Apenas logar o erro, não impedir a atribuição
       log.Printf("Erro ao enviar notificação para o prestador %d: %s", provider.ID, err.Error())
   }
   ```

Esta duplicidade pode levar a notificações duplicadas ou inconsistentes.
