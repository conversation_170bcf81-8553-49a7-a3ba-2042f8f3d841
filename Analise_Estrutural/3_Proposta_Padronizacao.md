# Proposta de Padronização das Relações no Sistema de Gestão Tradição

## 1. Padronização de Modelos e Nomenclatura

### 1.1 Modelo Unificado para Filiais

Propomos a adoção de um único modelo para representar filiais, eliminando a duplicidade atual:

```go
// Branch representa uma filial/posto no sistema
type Branch struct {
    ID              uint           `gorm:"primaryKey" json:"id"`
    Name            string         `gorm:"size:100;not null" json:"name"`
    Code            string         `gorm:"size:20;unique;not null" json:"code"`
    Address         string         `gorm:"size:255" json:"address"`
    City            string         `gorm:"size:100" json:"city"`
    State           string         `gorm:"size:2" json:"state"`
    ZipCode         string         `gorm:"size:20" json:"zip_code"`
    Phone           string         `gorm:"size:20" json:"phone"`
    Email           string         `gorm:"size:100" json:"email"`
    Type            string         `gorm:"size:20;default:'urban'" json:"type"`
    IsActive        bool           `gorm:"default:true" json:"is_active"`
    ManagerID       *uint          `gorm:"index" json:"manager_id,omitempty"`
    Latitude        *float64       `json:"latitude,omitempty"`
    Longitude       *float64       `json:"longitude,omitempty"`
    OpeningHours    string         `gorm:"size:255" json:"opening_hours,omitempty"`
    CreatedAt       time.Time      `json:"created_at"`
    UpdatedAt       time.Time      `json:"updated_at"`
    DeletedAt       gorm.DeletedAt `gorm:"index" json:"-"`
    
    // Relações
    Manager         *User          `json:"manager,omitempty" gorm:"foreignKey:ManagerID"`
    Equipment       []Equipment    `json:"equipment,omitempty" gorm:"foreignKey:BranchID"`
    MaintenanceOrders []MaintenanceOrder `json:"maintenance_orders,omitempty" gorm:"foreignKey:BranchID"`
    ServiceProviders []ServiceProvider `json:"service_providers,omitempty" gorm:"many2many:provider_branches"`
    Technicians     []User         `json:"technicians,omitempty" gorm:"many2many:technician_branches"`
}
```

Este modelo unificado:
- Combina todos os campos relevantes dos modelos atuais
- Utiliza nomenclatura consistente em inglês
- Define claramente todas as relações com outras entidades

### 1.2 Padronização de Nomenclatura

Propomos a adoção de uma nomenclatura consistente em todo o sistema:

| Conceito | Termo Padronizado (Código) | Termo na Interface |
|----------|----------------------------|-------------------|
| Filial   | Branch                     | Filial/Posto      |
| Prestadora | ServiceProvider          | Prestadora de Serviço |
| Técnico  | Technician (User com role="technician") | Técnico |
| Ordem    | MaintenanceOrder           | Ordem de Manutenção |

Esta padronização deve ser aplicada em:
- Nomes de modelos e tabelas
- Nomes de variáveis e métodos
- Comentários e documentação
- Mensagens de log

## 2. Padronização das Relações entre Entidades

### 2.1 Relação Filial-Prestadora

Propomos a padronização da relação entre filiais e prestadoras através de uma tabela de relacionamento clara:

```go
// ProviderBranch representa a relação entre prestadora e filial
type ProviderBranch struct {
    ID                uint      `json:"id" gorm:"primaryKey"`
    ServiceProviderID uint      `json:"service_provider_id" gorm:"not null;index"`
    BranchID          uint      `json:"branch_id" gorm:"not null;index"`
    CreatedAt         time.Time `json:"created_at"`
    UpdatedAt         time.Time `json:"updated_at"`
    CreatedByID       uint      `json:"created_by_id" gorm:"index"`
    
    // Relações
    ServiceProvider   ServiceProvider `json:"service_provider,omitempty" gorm:"foreignKey:ServiceProviderID"`
    Branch            Branch          `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
    CreatedBy         User            `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID"`
}
```

Esta tabela:
- Define claramente a relação muitos-para-muitos entre filiais e prestadoras
- Inclui metadados importantes como quem criou a relação e quando
- Possui índices para otimizar consultas

### 2.2 Relação Técnico-Prestadora

Propomos a padronização da relação entre técnicos e prestadoras:

```go
// User (com role="technician") representa um técnico no sistema
type User struct {
    // Campos existentes...
    ServiceProviderID *uint            `json:"service_provider_id,omitempty" gorm:"index"`
    ServiceProvider   *ServiceProvider `json:"service_provider,omitempty" gorm:"foreignKey:ServiceProviderID"`
}

// ServiceProvider representa uma prestadora de serviço
type ServiceProvider struct {
    // Campos existentes...
    
    // Relações
    Technicians []User `json:"technicians,omitempty" gorm:"foreignKey:ServiceProviderID;constraint:OnDelete:SET NULL"`
    Managers    []ServiceProviderManager `json:"managers,omitempty" gorm:"foreignKey:ServiceProviderID"`
    Branches    []Branch `json:"branches,omitempty" gorm:"many2many:provider_branches"`
}
```

Esta abordagem:
- Mantém a relação direta entre técnicos e prestadoras através do campo `ServiceProviderID`
- Define claramente a relação inversa através da propriedade `Technicians`
- Adiciona uma constraint para lidar com a exclusão de prestadoras

### 2.3 Relação Técnico-Filial

Propomos a padronização da relação entre técnicos e filiais:

```go
// TechnicianBranch representa a relação entre técnico e filial
type TechnicianBranch struct {
    TechnicianID uint      `gorm:"primaryKey;index" json:"technician_id"`
    BranchID     uint      `gorm:"primaryKey;index" json:"branch_id"`
    SpecialtyID  uint      `gorm:"primaryKey;index" json:"specialty_id"`
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
    CreatedByID  uint      `json:"created_by_id" gorm:"index"`

    // Relacionamentos
    Technician   User                `json:"technician,omitempty" gorm:"foreignKey:TechnicianID"`
    Branch       Branch              `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
    Specialty    TechnicianSpecialty `json:"specialty,omitempty" gorm:"foreignKey:SpecialtyID"`
    CreatedBy    User                `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID"`
}
```

Esta tabela:
- Define claramente a relação muitos-para-muitos entre técnicos e filiais
- Inclui a especialidade do técnico para aquela filial
- Inclui metadados importantes como quem criou a relação e quando
- Possui índices para otimizar consultas

### 2.4 Relação Técnico-Ordem

Propomos a padronização da relação entre técnicos e ordens:

```go
// TechnicianOrder representa a relação entre técnico e ordem
type TechnicianOrder struct {
    ID           uint      `json:"id" gorm:"primaryKey"`
    TechnicianID uint      `json:"technician_id" gorm:"not null;index"`
    OrderID      uint      `json:"order_id" gorm:"not null;index"`
    CreatedAt    time.Time `json:"created_at"`
    CreatedByID  uint      `json:"created_by_id" gorm:"index"`
    Notes        string    `json:"notes,omitempty"`
    Status       string    `json:"status" gorm:"default:'assigned'"` // assigned, accepted, rejected, completed

    // Relacionamentos
    Technician   *User             `json:"technician,omitempty" gorm:"foreignKey:TechnicianID"`
    Order        *MaintenanceOrder `json:"order,omitempty" gorm:"foreignKey:OrderID"`
    CreatedBy    *User             `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID"`
}
```

Esta tabela:
- Define claramente a relação entre técnicos e ordens
- Inclui metadados importantes como quem criou a relação e quando
- Adiciona um campo de status para rastrear o progresso da atribuição
- Possui índices para otimizar consultas

## 3. Padronização do Sistema de Atribuição de Ordens

### 3.1 Fluxo de Atribuição Padronizado

Propomos um fluxo de atribuição padronizado para ordens de manutenção:

1. **Criação da Ordem**:
   - Uma filial cria uma ordem de manutenção
   - A ordem é registrada com o `BranchID` da filial que a criou

2. **Atribuição a Prestadora**:
   - O sistema atribui automaticamente a ordem a uma prestadora elegível
   - A prestadora é registrada no campo `ServiceProviderID` da ordem
   - Um registro é criado no histórico da ordem

3. **Atribuição a Técnico**:
   - A prestadora ou o sistema atribui a ordem a um técnico
   - Um registro é criado na tabela `technician_orders`
   - O campo `TechnicianID` da ordem é atualizado para manter compatibilidade
   - Um registro é criado no histórico da ordem

4. **Notificações**:
   - O sistema envia notificações em pontos específicos do fluxo
   - As notificações são centralizadas no serviço de notificações

### 3.2 Serviço de Atribuição Centralizado

Propomos a criação de um serviço centralizado para gerenciar atribuições:

```go
// OrderAssignmentService gerencia a atribuição de ordens
type OrderAssignmentService struct {
    db                  *gorm.DB
    technicianOrderRepo TechnicianOrderRepository
    notificationService NotificationService
}

// AssignOrderToProvider atribui uma ordem a uma prestadora
func (s *OrderAssignmentService) AssignOrderToProvider(orderID, providerID, assignedByID uint) error {
    // Implementação...
}

// AssignOrderToTechnician atribui uma ordem a um técnico
func (s *OrderAssignmentService) AssignOrderToTechnician(orderID, technicianID, assignedByID uint, notes string) error {
    // Implementação...
}

// AutoAssignOrderToProvider atribui automaticamente uma ordem a uma prestadora
func (s *OrderAssignmentService) AutoAssignOrderToProvider(orderID uint) error {
    // Implementação...
}

// GetEligibleProviders retorna prestadoras elegíveis para uma ordem
func (s *OrderAssignmentService) GetEligibleProviders(orderID uint) ([]ServiceProvider, error) {
    // Implementação...
}

// GetEligibleTechnicians retorna técnicos elegíveis para uma ordem
func (s *OrderAssignmentService) GetEligibleTechnicians(orderID uint) ([]User, error) {
    // Implementação...
}
```

Este serviço:
- Centraliza toda a lógica de atribuição de ordens
- Garante consistência nas atribuições
- Facilita a manutenção e evolução do sistema

## 4. Padronização do Sistema de Permissões

### 4.1 Sistema de Permissões Centralizado

Propomos a adoção completa do sistema de permissões centralizado:

```go
// ResourcePermissionService gerencia permissões de recursos
type ResourcePermissionService struct {
    db *gorm.DB
    cache map[string]bool
}

// HasOrderPermission verifica se um usuário tem permissão para acessar uma ordem
func (s *ResourcePermissionService) HasOrderPermission(userID uint, orderID uint, action Action) bool {
    // Implementação...
}

// HasBranchPermission verifica se um usuário tem permissão para acessar uma filial
func (s *ResourcePermissionService) HasBranchPermission(userID uint, branchID uint, action Action) bool {
    // Implementação...
}

// HasServiceProviderPermission verifica se um usuário tem permissão para acessar uma prestadora
func (s *ResourcePermissionService) HasServiceProviderPermission(userID uint, providerID uint, action Action) bool {
    // Implementação...
}
```

Este serviço:
- Centraliza toda a lógica de verificação de permissões
- Implementa regras de negócio específicas para cada tipo de recurso
- Utiliza cache para otimizar verificações frequentes

### 4.2 Middleware Padronizado

Propomos a adoção de um middleware padronizado para verificação de permissões:

```go
// ResourcePermissionMiddleware verifica permissões de recursos
func ResourcePermissionMiddleware(resourceType ResourceType, action Action) gin.HandlerFunc {
    return func(c *gin.Context) {
        // Implementação...
    }
}
```

Este middleware:
- Utiliza o sistema de permissões centralizado
- Padroniza a verificação de permissões em todas as rotas
- Facilita a manutenção e evolução do sistema
