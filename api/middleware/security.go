package middleware

import (
	"net/http"

	"os"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/csrf"
)

func SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.<PERSON><PERSON>("X-Content-Type-Options", "nosniff")
		c.<PERSON>("X-Frame-Options", "DENY")
		c<PERSON>("X-XSS-Protection", "1; mode=block")
		c<PERSON><PERSON>("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		c<PERSON><PERSON><PERSON>("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Next()
	}
}

func CSRFProtection() gin.HandlerFunc {
	csrfMiddleware := csrf.Protect(
		[]byte(os.Getenv("CSRF_KEY")),
		csrf.Secure(true),
		csrf.Path("/"),
	)

	// Adaptador para converter o middleware do Gorilla CSRF para o formato do Gin
	return func(c *gin.Context) {
		csrfMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Armazenar o token CSRF no contexto do Gin para uso nos templates
			c.Set("csrf_token", csrf.Token(r))

			// Continuar com a próxima função na cadeia de middleware
			c.Request = r
		})).ServeHTTP(c.Writer, c.Request)

		c.Next()
	}
}

func CacheControl() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Para recursos estáticos
		if c.Request.URL.Path[:8] == "/static/" {
			c.Header("Cache-Control", "public, max-age=31536000")
		} else {
			// Para conteúdo dinâmico
			c.Header("Cache-Control", "private, max-age=300")
		}
		c.Next()
	}
}
