package api

import (
	"log"
	"time"

	"github.com/gin-gonic/gin"
)

type Metrics struct {
	PageLoads   map[string]PageLoad `json:"pageLoads"`
	Errors      []ErrorMetric       `json:"errors"`
	Performance Performance         `json:"performance"`
}

type PageLoad struct {
	Timestamp string `json:"timestamp"`
	LoadTime  int    `json:"loadTime"`
	URL       string `json:"url"`
}

type ErrorMetric struct {
	Timestamp string                 `json:"timestamp"`
	Error     string                 `json:"error"`
	Stack     string                 `json:"stack"`
	Context   map[string]interface{} `json:"context"`
	URL       string                 `json:"url"`
}

type Performance struct {
	Timestamp   string `json:"timestamp"`
	Memory      int    `json:"memory"`
	TotalMemory int    `json:"totalMemory"`
	URL         string `json:"url"`
}

func HandleMetrics(c *gin.Context) {
	var metrics Metrics
	if err := c.Bind<PERSON>(&metrics); err != nil {
		c.J<PERSON>(400, gin.H{"error": "Invalid metrics data"})
		return
	}

	// Log métricas para análise
	log.Printf("[METRICS] Page Loads: %d, Errors: %d, Time: %s\n",
		len(metrics.PageLoads),
		len(metrics.Errors),
		time.Now().Format(time.RFC3339),
	)

	// Aqui você pode adicionar código para:
	// 1. Salvar métricas no banco de dados
	// 2. Enviar para um serviço de monitoramento (ex: Prometheus)
	// 3. Gerar alertas baseados em thresholds

	c.JSON(200, gin.H{"status": "ok"})
}
