# Projeto Tradição (projeto_linux)

## Visão Geral

O Projeto Tradição é um sistema de gerenciamento de manutenção, monitorando dados de equipamentos e suas manutenções com custos, além de administrar os equipamentos em cada filial.

## Estrutura do Projeto

- **cmd/**: Contém o ponto de entrada da aplicação
- **internal/**: Contém o código interno da aplicação
  - **config/**: Configurações da aplicação
  - **database/**: Conexão com o banco de dados
  - **handlers/**: Handlers HTTP
  - **middleware/**: Middlewares HTTP
  - **models/**: Modelos de dados
  - **repository/**: Repositórios para acesso a dados
  - **services/**: Serviços de negócio
  - **utils/**: Utilitários
- **migrations/**: Migrações de banco de dados
- **web/**: Arquivos web (HTML, CSS, JS)
  - **static/**: Arquivos estáticos
  - **templates/**: Templates HTML
- **docs/**: Documentação
- **tools/**: Ferramentas de desenvolvimento

## Requisitos

- Go 1.16 ou superior
- PostgreSQL 12 ou superior
- Node.js 14 ou superior (para desenvolvimento frontend)

## Instalação

1. Clone o repositório:
   ```bash
   git clone https://github.com/projetotradicao/projeto_linux.git
   cd projeto_linux
   ```

2. Instale as dependências:
   ```bash
   go mod download
   ```

3. Configure o banco de dados:
   ```bash
   cp .env.example .env
   # Edite o arquivo .env com suas configurações
   ```

4. Execute as migrações:
   ```bash
   go run cmd/migrate/main.go
   ```

5. Compile e execute a aplicação:
   ```bash
   go build -o bin/app ./cmd/main.go
   ./bin/app
   ```

## Migração de Banco de Dados

Recentemente, o projeto passou por uma migração de banco de dados de GORM para Ent + Atlas. Para mais informações, consulte a [documentação de migração](docs/migration/README.md).

### Ferramentas de Migração

As ferramentas de migração estão disponíveis no diretório `tools/db_migration/`:

1. **analyze_db.go**: Analisa o banco de dados atual e gera um relatório detalhado
2. **generate_atlas_schema.go**: Gera o esquema Atlas a partir do novo esquema proposto
3. **migrate_data.go**: Migra os dados do banco de dados atual para o novo esquema

Para mais informações sobre como usar essas ferramentas, consulte o [README](tools/db_migration/README.md) do diretório `tools/db_migration/`.

## Desenvolvimento

### Compilação

```bash
# Compilar o projeto
go build -o bin/app ./cmd/main.go

# Compilar com otimizações
go build -ldflags="-s -w" -o bin/app ./cmd/main.go
```

### Execução

```bash
# Executar o projeto
go run cmd/main.go
```

### Testes

```bash
# Executar todos os testes
go test ./...

# Executar testes com cobertura
go test -cover ./...
```

## Documentação

A documentação do projeto está disponível no diretório `docs/`:

- [Visão Geral da Migração](docs/migration/migration_overview.md)
- [Correção de Problemas no Banco de Dados](docs/migration/database_issues_correction.md)
- [Migração de GORM para Ent + Atlas](docs/migration/gorm_to_ent_atlas.md)
- [Otimização para Códigos Grandes](docs/migration/code_optimization.md)

## Contribuição

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/nova-feature`)
3. Faça commit das suas alterações (`git commit -am 'Adiciona nova feature'`)
4. Faça push para a branch (`git push origin feature/nova-feature`)
5. Abra um Pull Request

## Licença

Este projeto está licenciado sob a licença MIT - veja o arquivo [LICENSE](LICENSE) para mais detalhes.