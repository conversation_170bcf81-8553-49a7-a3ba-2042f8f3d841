#!/bin/bash

# Cores para saída
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} Iniciando servidor rapidamente..."

# Verificar se estamos no diretório raiz do projeto
if [ ! -d "web/templates" ]; then
    # Se estamos no diretório cmd, voltar para o diretório raiz
    if [ "$(basename $(pwd))" = "cmd" ]; then
        echo -e "${YELLOW}[!] Detectado diretório cmd, voltando para o diretório raiz${NC}"
        cd ..
    fi

    # Verificar novamente se estamos no diretório raiz
    if [ ! -d "web/templates" ]; then
        echo -e "${RED}[✗] Erro: Não foi possível encontrar o diretório web/templates${NC}"
        echo -e "${RED}[✗] Por favor, execute este script do diretório raiz do projeto${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}[✓] Diretório de trabalho correto: $(pwd)${NC}"

# Verificar se as variáveis de ambiente estão configuradas
if [ -f .env ]; then
    echo -e "${GREEN}[✓] Arquivo .env encontrado, carregando configurações...${NC}"
    source .env
else
    echo -e "${RED}[✗] Arquivo .env não encontrado!${NC}"
    echo -e "${YELLOW}[!] Por favor, crie o arquivo .env com as configurações do banco de dados.${NC}"
    exit 1
fi

# Verificar se as variáveis obrigatórias estão definidas
if [ -z "$DB_HOST" ] || [ -z "$DB_PORT" ] || [ -z "$DB_USER" ] || [ -z "$DB_PASS" ] || [ -z "$DB_NAME" ]; then
    echo -e "${RED}[✗] Variáveis de ambiente obrigatórias não encontradas!${NC}"
    echo -e "${YELLOW}[!] Configure: DB_HOST, DB_PORT, DB_USER, DB_PASS, DB_NAME no arquivo .env${NC}"
    exit 1
fi

export DB_PERSISTENT_CONNECTION="true"

# Configurar para escutar em IPv4 e IPv6
export GIN_MODE="debug"
export HOST="0.0.0.0"

# Definir a porta padrão como 8080
export PORT="8080"

# Função para encerrar processos em uma porta específica
encerrar_processo_na_porta() {
    local porta=$1

    if netstat -tuln | grep -q ":$porta "; then
        echo -e "${YELLOW}[!] Porta $porta já está em uso. Tentando encerrar o processo...${NC}"

        # Obter o PID do processo que está usando a porta
        local PID=$(netstat -tlnp 2>/dev/null | grep ":$porta" | awk '{print $7}' | cut -d'/' -f1)

        if [ ! -z "$PID" ]; then
            echo -e "${YELLOW}[!] Encerrando processo com PID $PID que está usando a porta $porta${NC}"
            kill -9 $PID 2>/dev/null
            sleep 1

            # Verificar se a porta foi liberada
            if netstat -tuln | grep -q ":$porta "; then
                echo -e "${RED}[✗] Não foi possível liberar a porta $porta.${NC}"
                return 1
            else
                echo -e "${GREEN}[✓] Porta $porta liberada com sucesso${NC}"
                return 0
            fi
        else
            echo -e "${RED}[✗] Não foi possível identificar o processo que está usando a porta $porta${NC}"
            return 1
        fi
    else
        # Porta já está livre
        return 0
    fi
}

# Encerrar processos nas portas 8080, 8081 e 8082
echo -e "${YELLOW}[!] Verificando e liberando portas 8080, 8081 e 8082...${NC}"

# Tentar encerrar processos em todas as portas
encerrar_processo_na_porta 8080
encerrar_processo_na_porta 8081
encerrar_processo_na_porta 8082

# Verificar novamente se a porta 8080 está livre
if netstat -tuln | grep -q ":8080 "; then
    echo -e "${RED}[✗] Não foi possível liberar a porta 8080. Por favor, encerre o processo manualmente.${NC}"
    exit 1
fi

echo -e "${GREEN}[✓] Configurado para usar banco de dados remoto${NC}"
echo -e "${GREEN}[✓] Servidor será iniciado na porta $PORT${NC}"

# Matar processos Go anteriores que possam estar rodando
if pgrep -f "go run cmd/main.go" > /dev/null; then
    echo -e "${YELLOW}[!] Processos Go anteriores encontrados. Finalizando...${NC}"
    pkill -f "go run cmd/main.go" || true
    pkill -f "/tmp/go-build" || true
    sleep 2
fi

echo -e "\n${YELLOW}==============================================${NC}"
echo -e "${GREEN}Servidor iniciando em http://localhost:$PORT${NC}"
echo -e "${YELLOW}==============================================${NC}\n"

# Iniciar o servidor em primeiro plano
go run cmd/main.go
