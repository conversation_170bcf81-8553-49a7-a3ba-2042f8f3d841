package main

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func mainCheckMaintenanceOrders() {
	// Carregar variáveis de ambiente
	err := godotenv.Load()
	if err != nil {
		log.Println("Arquivo .env não encontrado, usando variáveis de ambiente do sistema")
	}

	// Obter diretório de trabalho
	workDir, err := os.Getwd()
	if err != nil {
		log.Fatalf("Erro ao obter diretório de trabalho: %v", err)
	}
	log.Printf("Usando diretório de trabalho: %s", workDir)

	// Configurar conexão com o banco de dados
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASS")
	dbName := os.Getenv("DB_NAME")
	dbSSLMode := "disable"

	// Usar valores padrão se não estiverem definidos
	if dbHost == "" {
		dbHost = "localhost"
	}
	if dbPort == "" {
		dbPort = "5432"
	}
	if dbUser == "" {
		dbUser = "postgres"
	}
	if dbPassword == "" {
		dbPassword = "postgres"
	}
	if dbName == "" {
		dbName = "tradicao"
	}
	if dbSSLMode == "" {
		dbSSLMode = "disable"
	}

	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		dbHost, dbPort, dbUser, dbPassword, dbName, dbSSLMode)

	// Configurar logger do GORM
	gormLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  true,
		},
	)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	log.Println("Conexão com banco de dados PostgreSQL via GORM estabelecida com sucesso")

	// Verificar estrutura da tabela maintenance_orders
	var columns []struct {
		ColumnName string `gorm:"column:column_name"`
		DataType   string `gorm:"column:data_type"`
	}

	db.Raw(`
		SELECT column_name, data_type
		FROM information_schema.columns
		WHERE table_name = 'maintenance_orders'
		ORDER BY ordinal_position
	`).Scan(&columns)

	fmt.Println("\nEstrutura da tabela 'maintenance_orders':")
	fmt.Println("----------------------------------------")
	for _, col := range columns {
		fmt.Printf("%-25s %s\n", col.ColumnName, col.DataType)
	}

	// Verificar se há ordens no banco de dados
	var count int64
	db.Table("maintenance_orders").Count(&count)
	fmt.Printf("\nTotal de ordens no banco de dados: %d\n", count)
}

// Para compilar este arquivo individualmente, descomente a função main abaixo
// e comente a função main em outros arquivos do mesmo pacote
/*
func main() {
	mainCheckMaintenanceOrders()
}
*/
