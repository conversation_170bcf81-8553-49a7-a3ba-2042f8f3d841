package main

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func mainDbInfo() {
	// Carregar variáveis de ambiente
	err := godotenv.Load()
	if err != nil {
		log.Println("Arquivo .env não encontrado, usando variáveis de ambiente do sistema")
	}

	// Obter diretório de trabalho
	workDir, err := os.Getwd()
	if err != nil {
		log.Fatalf("Erro ao obter diretório de trabalho: %v", err)
	}
	log.Printf("Usando diretório de trabalho: %s", workDir)

	// Configurar conexão com o banco de dados
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASS")
	dbName := os.Getenv("DB_NAME")
	dbSSLMode := "disable"

	// Usar valores padrão se não estiverem definidos
	if dbHost == "" {
		dbHost = "localhost"
	}
	if dbPort == "" {
		dbPort = "5432"
	}
	if dbUser == "" {
		dbUser = "postgres"
	}
	if dbPassword == "" {
		dbPassword = "postgres"
	}
	if dbName == "" {
		dbName = "tradicao"
	}
	if dbSSLMode == "" {
		dbSSLMode = "disable"
	}

	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		dbHost, dbPort, dbUser, dbPassword, dbName, dbSSLMode)

	// Configurar logger do GORM
	gormLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  true,
		},
	)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	log.Println("Conexão com banco de dados PostgreSQL via GORM estabelecida com sucesso")

	// Verificar estrutura da tabela de usuários
	var columns []struct {
		ColumnName string `gorm:"column:column_name"`
		DataType   string `gorm:"column:data_type"`
	}

	db.Raw(`
		SELECT column_name, data_type
		FROM information_schema.columns
		WHERE table_name = 'users'
		ORDER BY ordinal_position
	`).Scan(&columns)

	fmt.Println("\nEstrutura da tabela 'users':")
	fmt.Println("-----------------------------")
	for _, col := range columns {
		fmt.Printf("%-20s %s\n", col.ColumnName, col.DataType)
	}

	// Verificar se há usuários no banco de dados
	var count int64
	db.Table("users").Count(&count)
	fmt.Printf("\nTotal de usuários no banco de dados: %d\n", count)

	// Listar os primeiros 5 usuários
	type User struct {
		ID       int    `gorm:"column:id"`
		Name     string `gorm:"column:name"`
		Email    string `gorm:"column:email"`
		Type     string `gorm:"column:type"`
		Password string `gorm:"column:password"`
	}

	var users []User
	db.Table("users").Limit(5).Find(&users)

	fmt.Println("\nPrimeiros 5 usuários:")
	fmt.Println("--------------------")
	for _, user := range users {
		fmt.Printf("ID: %d, Nome: %s, Email: %s, Tipo: %s\n", user.ID, user.Name, user.Email, user.Type)
	}
}

// Função principal para este arquivo
func mainCmdTools() {
	mainDbInfo()
}
