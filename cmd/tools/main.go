package main

import (
	"fmt"
	"os"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Uso: go run cmd/tools/main.go <comando>")
		fmt.Println("Comandos disponíveis:")
		fmt.Println("  check-maintenance-orders - Verifica ordens de manutenção")
		fmt.Println("  db-info                 - Exibe informações do banco de dados")
		os.Exit(1)
	}

	switch os.Args[1] {
	case "check-maintenance-orders":
		mainCheckMaintenanceOrders()
	case "db-info":
		mainDbInfo()
	default:
		fmt.Printf("Comando desconhecido: %s\n", os.Args[1])
		os.Exit(1)
	}
}
