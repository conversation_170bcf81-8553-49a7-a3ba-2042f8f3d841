package main

import (
	"log"
	"tradicao/internal/database"
	"tradicao/internal/database/migrations"

	"github.com/joho/godotenv"
)

func main() {
	// Carrega as variáveis de ambiente do arquivo .env
	if err := godotenv.Load(); err != nil {
		log.Fatalf("Erro ao carregar .env: %v", err)
	}

	// Define o diretório de trabalho para a raiz do projeto
	// Comentado para funcionar em ambiente Windows
	// if err := os.Chdir("/home/<USER>"); err != nil {
	//     log.Fatalf("Erro ao definir diretório de trabalho: %v", err)
	// }

	log.Println("Conectando ao banco de dados...")
	db, err := database.Connect()
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}
	log.Println("Conexão com o banco de dados estabelecida com sucesso!")

	// Executar migrações
	log.Println("Executando migração: CreateNotificationsTable")
	if err := migrations.CreateNotificationsTable(db); err != nil {
		log.Fatalf("Erro ao executar migração CreateNotificationsTable: %v", err)
	}

	log.Println("Executando migração: CreateSecurityPoliciesTable")
	if err := migrations.CreateSecurityPoliciesTable(db); err != nil {
		log.Fatalf("Erro ao executar migração CreateSecurityPoliciesTable: %v", err)
	}

	log.Println("Executando migração: AddLastPasswordChangeToUsers")
	if err := migrations.AddLastPasswordChangeToUsers(db); err != nil {
		log.Fatalf("Erro ao executar migração AddLastPasswordChangeToUsers: %v", err)
	}

	log.Println("Executando migração: AddForcePasswordChangeToUsers")
	if err := migrations.AddForcePasswordChangeToUsers(db); err != nil {
		log.Fatalf("Erro ao executar migração AddForcePasswordChangeToUsers: %v", err)
	}

	log.Println("Todas as migrações foram executadas com sucesso!")
}
