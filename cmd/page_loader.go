package main

import (
	"bufio"
	"log"
	"os"
	"strings"

	"github.com/gin-gonic/gin"
)

// Carrega as páginas listadas no arquivo de permissões e registra no log
func loadPagesFromPermissionsFile(router *gin.Engine) {
	log.Println("Registrando páginas do arquivo data/permissoes_paginas.md")

	file, err := os.Open("data/permissoes_paginas.md")
	if err != nil {
		log.Printf("Erro ao abrir arquivo de permissões: %v", err)
		return
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	inTable := false
	totalPages := 0

	log.Println("Páginas registradas:")

	for scanner.Scan() {
		line := scanner.Text()

		// Verificar se estamos na seção da tabela
		if strings.Contains(line, "| Página | Perfis com acesso |") {
			inTable = true
			continue
		}

		// Pular a linha de separação da tabela
		if strings.Contains(line, "|---") {
			continue
		}

		if inTable && strings.HasPrefix(line, "|") {
			parts := strings.Split(line, "|")
			if len(parts) >= 3 {
				pagePath := strings.TrimSpace(parts[1])
				rolesStr := strings.TrimSpace(parts[2])

				// Remover as aspas e formatação markdown
				pagePath = strings.Trim(pagePath, "`")

				// Log da página e seus perfis permitidos
				log.Printf("Página carregada: %s -> Perfis: %s", pagePath, rolesStr)
				totalPages++
			}
		}

		// Sair do processamento da tabela quando encontrar uma linha em branco
		if inTable && strings.TrimSpace(line) == "" {
			inTable = false
		}
	}

	if err := scanner.Err(); err != nil {
		log.Printf("Erro ao ler arquivo de permissões: %v", err)
		return
	}

	log.Printf("Total de páginas registradas: %d", totalPages)
}
