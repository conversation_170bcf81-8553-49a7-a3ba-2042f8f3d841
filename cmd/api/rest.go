package main

import (
	"log"
	"tradicao/internal/database"
	"tradicao/internal/handlers"
	"tradicao/internal/middleware"
	"tradicao/internal/repository"
	"tradicao/internal/routes"
	"tradicao/internal/services"
	"tradicao/internal/setup"

	"github.com/gin-gonic/gin"
)

func main() {
	// Configurar conexão com o banco de dados
	db, err := database.InitGorm()
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Configurar router
	router := gin.Default()

	// Configurar componentes unificados para filiais, estações e branches
	setup.SetupUnifiedComponents(router, db)

	// Obter o handler unificado para uso em outras partes do código
	// Este handler já implementa todas as funcionalidades relacionadas a filiais, estações e branches
	unifiedHandler := setup.GetUnifiedHandler()

	// Inicializar repositório de ordens
	orderRepo := repository.NewMaintenanceOrderRepository(db)

	// Criar adaptador para o repositório de ordens
	orderRepoAdapter := repository.NewMaintenanceOrderRepositoryAdapter(orderRepo)

	// Inicializar serviço de ordens
	orderService := services.NewOrderService(orderRepoAdapter)

	// Criar adaptador para o serviço de ordens
	orderServiceAdapter := services.NewOrderServiceAdapter(orderService, db)

	// Inicializar handler de ordens
	// Usar o adaptador como OrderServiceInterface
	orderHandler := handlers.NewOrderHandler(orderServiceAdapter)

	// Configurar rotas de ordens
	routes.SetupOrderRoutes(router, orderHandler, middleware.AuthMiddleware())

	// Usar o handler unificado para rotas adicionais que não foram configuradas em SetupUnifiedRoutes
	// Por exemplo, para rotas de equipamentos por filial
	equipmentRoutes := router.Group("/api/equipments")
	equipmentRoutes.Use(middleware.AuthMiddleware())
	equipmentRoutes.GET("/filial/:id", unifiedHandler.GetEquipmentsByFilial)

	// Iniciar servidor
	if err := router.Run(":8080"); err != nil {
		log.Fatalf("Erro ao iniciar servidor: %v", err)
	}
}
