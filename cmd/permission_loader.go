package main

import (
	"bufio"
	"log"
	"os"
	"strings"
)

// Carregar e registrar as permissões das páginas a partir do arquivo
func loadPermissionsFromFile() {
	log.Println("Carregando permissões de páginas do arquivo data/permissoes_paginas.md")

	file, err := os.Open("data/permissoes_paginas.md")
	if err != nil {
		log.Printf("Erro ao abrir arquivo de permissões: %v", err)
		return
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	inTable := false
	totalPages := 0

	log.Println("Permissões de páginas:")

	for scanner.Scan() {
		line := scanner.Text()

		// Verificar se estamos na seção da tabela
		if strings.Contains(line, "| Página | Perfis com acesso |") {
			inTable = true
			continue
		}

		if inTable && strings.HasPrefix(line, "|") && !strings.HasPrefix(line, "|---") {
			parts := strings.Split(line, "|")
			if len(parts) >= 3 {
				pagePath := strings.TrimSpace(parts[1])
				rolesStr := strings.TrimSpace(parts[2])

				// Remover as aspas e barras do nome da página
				pagePath = strings.Trim(pagePath, "`/")

				// Registrar no log
				log.Printf("Página: %s -> Roles: %s", pagePath, rolesStr)
				totalPages++
			}
		}

		// Sair do processamento da tabela quando encontrar uma linha em branco
		if inTable && strings.TrimSpace(line) == "" {
			inTable = false
		}
	}

	if err := scanner.Err(); err != nil {
		log.Printf("Erro ao ler arquivo de permissões: %v", err)
		return
	}

	log.Printf("Total de páginas com permissões definidas: %d", totalPages)
}
