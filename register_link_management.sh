#!/bin/bash

# Script para registrar as rotas de gerenciamento de vínculos

# Diretório do projeto
PROJECT_DIR="/root/projeto_linux"

# Verificar se o diretório existe
if [ ! -d "$PROJECT_DIR" ]; then
    echo "Diretório do projeto não encontrado: $PROJECT_DIR"
    exit 1
fi

# Ir para o diretório do projeto
cd "$PROJECT_DIR"

# Criar um arquivo temporário para registrar as rotas
cat > register_links.go << 'EOF'
package main

import (
	"log"
	"tradicao/internal/controllers"
	"tradicao/internal/repository"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// RegisterLinkManagement registra as rotas de gerenciamento de vínculos
func RegisterLinkManagement(router *gin.Engine, db *gorm.DB, technicianOrderService *services.TechnicianOrderService) {
	// Inicializar repositórios
	branchRepo := repository.NewBranchRepository(db)
	technicianRepo := repository.NewTechnicianRepository(db)
	serviceProviderRepo := repository.NewServiceProviderRepository(db)

	// Inicializar serviço
	linkManagementService := services.NewLinkManagementService(
		db,
		branchRepo,
		technicianRepo,
		serviceProviderRepo,
	)

	// Inicializar controlador
	linkManagementController := controllers.NewLinkManagementController(linkManagementService)

	// Configurar rotas
	routes.SetupLinkManagementRoutes(router, linkManagementController, technicianOrderService)

	log.Println("Sistema de gerenciamento de vínculos configurado com sucesso")
}
EOF

# Compilar o projeto para verificar se há erros
go build -o /dev/null

# Remover o arquivo temporário
rm register_links.go

# Criar um script para iniciar o servidor com as rotas de gerenciamento de vínculos
cat > iniciar_com_vinculos.sh << 'EOF'
#!/bin/bash

# Script para iniciar o servidor com as rotas de gerenciamento de vínculos

# Diretório do projeto
PROJECT_DIR="/root/projeto_linux"

# Verificar se o diretório existe
if [ ! -d "$PROJECT_DIR" ]; then
    echo "Diretório do projeto não encontrado: $PROJECT_DIR"
    exit 1
fi

# Ir para o diretório do projeto
cd "$PROJECT_DIR"

# Iniciar o servidor
go run cmd/main.go internal/routes/setup_link_management.go
EOF

# Tornar o script executável
chmod +x iniciar_com_vinculos.sh

echo "Script de inicialização com vínculos criado: iniciar_com_vinculos.sh"
echo "Execute ./iniciar_com_vinculos.sh para iniciar o servidor com as rotas de gerenciamento de vínculos"
