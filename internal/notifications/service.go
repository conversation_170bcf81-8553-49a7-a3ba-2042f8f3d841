package notifications

import (
	"fmt"
	"log"
	"time"
	"tradicao/internal/database"
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// NotificationType representa o tipo de notificação
type NotificationType string

const (
	// NotificationOrderAssigned representa uma notificação de ordem atribuída
	NotificationOrderAssigned NotificationType = "order_assigned"

	// NotificationOrderUpdated representa uma notificação de ordem atualizada
	NotificationOrderUpdated NotificationType = "order_updated"

	// NotificationOrderCompleted representa uma notificação de ordem concluída
	NotificationOrderCompleted NotificationType = "order_completed"

	// NotificationOrderCancelled representa uma notificação de ordem cancelada
	NotificationOrderCancelled NotificationType = "order_cancelled"

	// NotificationOrderComment representa uma notificação de comentário em ordem
	NotificationOrderComment NotificationType = "order_comment"
)

// Service é o serviço de notificações
type Service struct {
	db *gorm.DB
}

// NewService cria um novo serviço de notificações
func NewService() *Service {
	return &Service{
		db: database.GetGormDB(),
	}
}

// Notification representa uma notificação
type Notification struct {
	ID          uint             `json:"id" gorm:"primaryKey"`
	UserID      uint             `json:"user_id" gorm:"index"`
	Title       string           `json:"title"`
	Message     string           `json:"message"`
	Type        NotificationType `json:"type"`
	ReferenceID uint             `json:"reference_id"`
	IsRead      bool             `json:"is_read"`
	CreatedAt   time.Time        `json:"created_at"`
	UpdatedAt   time.Time        `json:"updated_at"`
}

// TableName retorna o nome da tabela
func (Notification) TableName() string {
	return "notifications"
}

// NotifyOrderAssigned notifica um técnico ou prestador quando uma ordem é atribuída
func (s *Service) NotifyOrderAssigned(orderID uint, userID uint) error {
	// Obter a ordem
	var order models.MaintenanceOrder
	if err := s.db.First(&order, orderID).Error; err != nil {
		log.Printf("[NOTIFICATION-SERVICE] Erro ao buscar ordem %d: %v", orderID, err)
		return err
	}

	// Obter o usuário
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		log.Printf("[NOTIFICATION-SERVICE] Erro ao buscar usuário %d: %v", userID, err)
		return err
	}

	// Criar a notificação
	notification := Notification{
		UserID:      userID,
		Title:       "Nova Ordem Atribuída",
		Message:     "Você foi atribuído à ordem #" + fmt.Sprintf("%d", order.ID),
		Type:        NotificationOrderAssigned,
		ReferenceID: orderID,
		IsRead:      false,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Salvar a notificação
	if err := s.db.Create(&notification).Error; err != nil {
		log.Printf("[NOTIFICATION-SERVICE] Erro ao criar notificação: %v", err)
		return err
	}

	log.Printf("[NOTIFICATION-SERVICE] Notificação criada para usuário %d sobre ordem %d", userID, orderID)
	return nil
}

// NotifyProviderOrderAssigned notifica um prestador quando uma ordem é atribuída automaticamente
func (s *Service) NotifyProviderOrderAssigned(orderID uint, providerID uint) error {
	// Obter a ordem
	var order models.MaintenanceOrder
	if err := s.db.First(&order, orderID).Error; err != nil {
		log.Printf("[NOTIFICATION-SERVICE] Erro ao buscar ordem %d: %v", orderID, err)
		return err
	}

	// Obter o prestador
	var provider models.ServiceProvider
	if err := s.db.First(&provider, providerID).Error; err != nil {
		log.Printf("[NOTIFICATION-SERVICE] Erro ao buscar prestador %d: %v", providerID, err)
		return err
	}

	// Buscar filial
	var branch models.Branch
	if err := s.db.First(&branch, order.BranchID).Error; err != nil {
		log.Printf("[NOTIFICATION-SERVICE] Erro ao buscar filial %d: %v", order.BranchID, err)
		return err
	}

	// Buscar equipamento
	var equipment models.Equipment
	if err := s.db.First(&equipment, order.EquipmentID).Error; err != nil {
		log.Printf("[NOTIFICATION-SERVICE] Erro ao buscar equipamento %d: %v", order.EquipmentID, err)
		return err
	}

	// Buscar usuários do prestador
	var users []models.User
	if err := s.db.Where("service_provider_id = ?", providerID).Find(&users).Error; err != nil {
		log.Printf("[NOTIFICATION-SERVICE] Erro ao buscar usuários do prestador %d: %v", providerID, err)
		return err
	}

	// Se não houver usuários, buscar o usuário administrador do prestador
	if len(users) == 0 {
		if err := s.db.Where("role = ? AND service_provider_id = ?", "prestadores", providerID).First(&users).Error; err != nil {
			// Se não encontrar, apenas logar o erro e continuar
			log.Printf("[NOTIFICATION-SERVICE] Não foi possível encontrar usuários para o prestador %d: %v", providerID, err)
		}
	}

	// Criar notificação para cada usuário do prestador
	for _, user := range users {
		notification := Notification{
			UserID:      user.ID,
			Title:       "Nova Ordem de Manutenção",
			Message:     "Uma nova ordem de manutenção foi atribuída à sua empresa para o equipamento " + equipment.Name + " na filial " + branch.Name,
			Type:        NotificationOrderAssigned,
			ReferenceID: orderID,
			IsRead:      false,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}

		if err := s.db.Create(&notification).Error; err != nil {
			// Apenas logar o erro e continuar
			log.Printf("[NOTIFICATION-SERVICE] Erro ao criar notificação para o usuário %d: %v", user.ID, err)
		} else {
			log.Printf("[NOTIFICATION-SERVICE] Notificação criada para usuário %d do prestador %d sobre ordem %d", user.ID, providerID, orderID)
		}
	}

	// Buscar técnicos do prestador
	var technicians []models.Technician
	if err := s.db.Where("service_provider_id = ?", providerID).Find(&technicians).Error; err != nil {
		// Apenas logar o erro e continuar
		log.Printf("[NOTIFICATION-SERVICE] Erro ao buscar técnicos do prestador %d: %v", providerID, err)
	}

	// Criar notificação para cada técnico do prestador
	for _, technician := range technicians {
		// Buscar usuário do técnico
		var user models.User
		if err := s.db.First(&user, technician.UserID).Error; err != nil {
			// Apenas logar o erro e continuar
			log.Printf("[NOTIFICATION-SERVICE] Erro ao buscar usuário do técnico %d: %v", technician.ID, err)
			continue
		}

		notification := Notification{
			UserID:      user.ID,
			Title:       "Nova Ordem de Manutenção",
			Message:     "Uma nova ordem de manutenção foi atribuída à sua empresa para o equipamento " + equipment.Name + " na filial " + branch.Name,
			Type:        NotificationOrderAssigned,
			ReferenceID: orderID,
			IsRead:      false,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}

		if err := s.db.Create(&notification).Error; err != nil {
			// Apenas logar o erro e continuar
			log.Printf("[NOTIFICATION-SERVICE] Erro ao criar notificação para o técnico %d: %v", technician.ID, err)
		} else {
			log.Printf("[NOTIFICATION-SERVICE] Notificação criada para técnico %d do prestador %d sobre ordem %d", technician.ID, providerID, orderID)
		}
	}

	return nil
}

// NotifyOrderUpdated notifica usuários relevantes quando uma ordem é atualizada
func (s *Service) NotifyOrderUpdated(orderID uint) error {
	// Obter a ordem
	var order models.MaintenanceOrder
	if err := s.db.First(&order, orderID).Error; err != nil {
		log.Printf("[NOTIFICATION-SERVICE] Erro ao buscar ordem %d: %v", orderID, err)
		return err
	}

	// Lista de usuários a notificar
	usersToNotify := make([]uint, 0)

	// Adicionar o técnico, se houver
	if order.TechnicianID != nil {
		usersToNotify = append(usersToNotify, *order.TechnicianID)
	}

	// Adicionar o prestador de serviço, se houver
	if order.ServiceProviderID != nil {
		usersToNotify = append(usersToNotify, *order.ServiceProviderID)
	}

	// Adicionar o criador da ordem
	usersToNotify = append(usersToNotify, order.CreatedByUserID)

	// Buscar usuários da filial
	var filialUsers []models.User
	if err := s.db.Where("branch_id = ?", order.BranchID).Find(&filialUsers).Error; err != nil {
		log.Printf("[NOTIFICATION-SERVICE] Erro ao buscar usuários da filial %d: %v", order.BranchID, err)
	} else {
		for _, user := range filialUsers {
			usersToNotify = append(usersToNotify, user.ID)
		}
	}

	// Criar notificações para cada usuário
	for _, userID := range usersToNotify {
		notification := Notification{
			UserID:      userID,
			Title:       "Ordem Atualizada",
			Message:     "A ordem #" + fmt.Sprintf("%d", order.ID) + " foi atualizada",
			Type:        NotificationOrderUpdated,
			ReferenceID: orderID,
			IsRead:      false,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}

		if err := s.db.Create(&notification).Error; err != nil {
			log.Printf("[NOTIFICATION-SERVICE] Erro ao criar notificação para usuário %d: %v", userID, err)
		}
	}

	return nil
}

// GetUserNotifications retorna as notificações de um usuário
func (s *Service) GetUserNotifications(userID uint, limit int, offset int) ([]Notification, error) {
	var notifications []Notification

	result := s.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&notifications)

	if result.Error != nil {
		log.Printf("[NOTIFICATION-SERVICE] Erro ao buscar notificações do usuário %d: %v", userID, result.Error)
		return nil, result.Error
	}

	return notifications, nil
}

// MarkAsRead marca uma notificação como lida
func (s *Service) MarkAsRead(notificationID uint, userID uint) error {
	result := s.db.Model(&Notification{}).
		Where("id = ? AND user_id = ?", notificationID, userID).
		Update("is_read", true)

	if result.Error != nil {
		log.Printf("[NOTIFICATION-SERVICE] Erro ao marcar notificação %d como lida: %v", notificationID, result.Error)
		return result.Error
	}

	return nil
}

// MarkAllAsRead marca todas as notificações de um usuário como lidas
func (s *Service) MarkAllAsRead(userID uint) error {
	result := s.db.Model(&Notification{}).
		Where("user_id = ?", userID).
		Update("is_read", true)

	if result.Error != nil {
		log.Printf("[NOTIFICATION-SERVICE] Erro ao marcar todas as notificações do usuário %d como lidas: %v", userID, result.Error)
		return result.Error
	}

	return nil
}

// DeleteNotification exclui uma notificação
func (s *Service) DeleteNotification(notificationID uint, userID uint) error {
	result := s.db.Where("id = ? AND user_id = ?", notificationID, userID).
		Delete(&Notification{})

	if result.Error != nil {
		log.Printf("[NOTIFICATION-SERVICE] Erro ao excluir notificação %d: %v", notificationID, result.Error)
		return result.Error
	}

	return nil
}

// GetUnreadCount retorna o número de notificações não lidas de um usuário
func (s *Service) GetUnreadCount(userID uint) (int64, error) {
	var count int64

	result := s.db.Model(&Notification{}).
		Where("user_id = ? AND is_read = ?", userID, false).
		Count(&count)

	if result.Error != nil {
		log.Printf("[NOTIFICATION-SERVICE] Erro ao contar notificações não lidas do usuário %d: %v", userID, result.Error)
		return 0, result.Error
	}

	return count, nil
}
