package services

import (
	"errors"
	"os"
	"time"

	"tradicao/internal/models"

	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
)

// Claims representa os dados armazenados no token JWT
type Claims struct {
	UserID uint   `json:"user_id"`
	Email  string `json:"email"`
	Admin  bool   `json:"admin"`
	jwt.RegisteredClaims
}

// GerarHash cria um hash seguro para a senha
func GerarHash(senha string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(senha), 14)
	return string(bytes), err
}

// VerificarSenha compara a senha com o hash armazenado
// Keep this implementation and remove the one in auth_service.go
func VerificarSenha(senhaHash, senha string) bool {
	// Implementation
	err := bcrypt.CompareHashAndPassword([]byte(senhaHash), []byte(senha))
	return err == nil
}

// GerarToken cria um novo token JWT para o usuário
func GerarToken(usuario models.User) (string, error) {
	// Obter a chave secreta do ambiente
	chaveSecreta := os.Getenv("JWT_SECRET")
	if chaveSecreta == "" {
		chaveSecreta = "chave-secreta-padrao-nao-use-em-producao"
	}

	// Definir o tempo de expiração do token (24 horas)
	expiracao := time.Now().Add(24 * time.Hour)

	// Criar claims com os dados do usuário
	claims := &Claims{
		UserID: usuario.ID,
		Email:  usuario.Email,
		Admin:  usuario.Role == models.RoleAdmin,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiracao),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "tradicao-api",
			Subject:   usuario.Email,
		},
	}

	// Criar token com os claims
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Assinar o token com a chave secreta
	tokenString, err := token.SignedString([]byte(chaveSecreta))
	if err != nil {
		return "", err
	}

	return tokenString, nil
}

// ValidarToken verifica se um token JWT é válido
func ValidarToken(tokenString string) (*Claims, error) {
	// Obter a chave secreta do ambiente
	chaveSecreta := os.Getenv("JWT_SECRET")
	if chaveSecreta == "" {
		return nil, errors.New("JWT_SECRET environment variable not set")
	}

	// Analisar o token
	claims := &Claims{}
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(chaveSecreta), nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, errors.New("token inválido")
	}

	return claims, nil
}
