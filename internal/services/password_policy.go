package services

import (
	"errors"
	"fmt"
	"log"
	"regexp"
	"strings"
	"time"
	"unicode"

	"tradicao/internal/models"
	"tradicao/internal/repository"

	"golang.org/x/crypto/bcrypt"
)

type PasswordStrength int

const (
	WeakPassword PasswordStrength = iota
	MediumPassword
	StrongPassword
	VeryStrongPassword
)

type PasswordPolicyService struct {
	userRepo repository.UserRepository
}

func NewPasswordPolicyService(userRepo repository.UserRepository) *PasswordPolicyService {
	return &PasswordPolicyService{
		userRepo: userRepo,
	}
}

func (s *PasswordPolicyService) ValidatePasswordStrength(password string) (PasswordStrength, error) {
	policy, err := s.userRepo.GetSecurityPolicy()
	if err != nil {
		return WeakPassword, fmt.Errorf("erro ao carregar política de segurança: %v", err)
	}

	if len(password) < policy.PasswordMinLength {
		return WeakPassword, fmt.Errorf("a senha deve ter pelo menos %d caracteres", policy.PasswordMinLength)
	}

	var hasUpper, hasLower, hasNumber, hasSpecial bool
	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsNumber(char):
			hasNumber = true
		case unicode.IsPunct(char) || unicode.IsSymbol(char):
			hasSpecial = true
		}
	}

	if policy.PasswordRequireUppercase && !hasUpper {
		return WeakPassword, errors.New("a senha deve conter pelo menos uma letra maiúscula")
	}

	if policy.PasswordRequireNumber && !hasNumber {
		return WeakPassword, errors.New("a senha deve conter pelo menos um número")
	}

	if policy.PasswordRequireSpecialChar && !hasSpecial {
		return WeakPassword, errors.New("a senha deve conter pelo menos um caractere especial")
	}

	var strength PasswordStrength = WeakPassword
	if hasUpper && hasLower && hasNumber && hasSpecial && len(password) >= 10 {
		strength = VeryStrongPassword
	} else if hasUpper && hasLower && hasNumber && len(password) >= 8 {
		strength = StrongPassword
	} else if (hasUpper || hasLower) && (hasNumber || hasSpecial) && len(password) >= policy.PasswordMinLength {
		strength = MediumPassword
	}

	return strength, nil
}

func (s *PasswordPolicyService) containsPersonalInfo(password string, user models.User) bool {
	pwd := strings.ToLower(password)

	if user.Name != "" {
		names := strings.Split(strings.ToLower(user.Name), " ")
		for _, name := range names {
			if len(name) >= 3 && strings.Contains(pwd, name) {
				return true
			}
		}
	}

	if user.Email != "" {
		emailParts := strings.Split(strings.ToLower(user.Email), "@")
		if len(emailParts) > 0 && len(emailParts[0]) >= 3 {
			if strings.Contains(pwd, emailParts[0]) {
				return true
			}
		}
	}

	return false
}

func (s *PasswordPolicyService) IsPasswordExpired(user models.User) (bool, error) {
	// Obter a política de segurança
	policy, err := s.userRepo.GetSecurityPolicy()
	if err != nil {
		return false, fmt.Errorf("erro ao carregar política de segurança: %v", err)
	}

	// Se a política não exige expiração de senha, retorna false
	if policy.PasswordExpiryDays <= 0 {
		return false, nil
	}

	// Se o campo LastPasswordChange for nulo, consideramos que a senha nunca foi alterada
	if user.LastPasswordChange == nil {
		// Se a política exige expiração, consideramos que a senha expirou
		return true, nil
	}

	// Verificar se a senha expirou com base na data da última alteração
	expiryDuration := time.Duration(policy.PasswordExpiryDays) * 24 * time.Hour
	expiryDate := user.LastPasswordChange.Add(expiryDuration)

	// Se a data de expiração for anterior à data atual, a senha expirou
	return time.Now().After(expiryDate), nil
}

func (s *PasswordPolicyService) IsPasswordComplexEnough(password string, user models.User) error {
	policy, err := s.userRepo.GetSecurityPolicy()
	if err != nil {
		return fmt.Errorf("erro ao carregar política de segurança: %v", err)
	}

	if len(password) < policy.PasswordMinLength {
		return fmt.Errorf("a senha deve ter pelo menos %d caracteres", policy.PasswordMinLength)
	}

	if policy.PasswordRequireUppercase {
		match, _ := regexp.MatchString(`[A-Z]`, password)
		if !match {
			return errors.New("a senha deve conter pelo menos uma letra maiúscula")
		}
	}

	if policy.PasswordRequireNumber {
		match, _ := regexp.MatchString(`[0-9]`, password)
		if !match {
			return errors.New("a senha deve conter pelo menos um número")
		}
	}

	if policy.PasswordRequireSpecialChar {
		match, _ := regexp.MatchString(`[^a-zA-Z0-9]`, password)
		if !match {
			return errors.New("a senha deve conter pelo menos um caractere especial")
		}
	}

	lowerPassword := strings.ToLower(password)
	if user.Name != "" && len(user.Name) >= 3 {
		if strings.Contains(lowerPassword, strings.ToLower(user.Name)) {
			return errors.New("a senha não pode conter seu nome")
		}
	}
	if user.Email != "" && len(user.Email) >= 3 {
		if strings.Contains(lowerPassword, strings.ToLower(user.Email)) {
			return errors.New("a senha não pode conter seu email")
		}
	}

	return nil
}

func (s *PasswordPolicyService) GeneratePasswordPolicy() (string, error) {
	policy, err := s.userRepo.GetSecurityPolicy()
	if err != nil {
		return "", fmt.Errorf("erro ao carregar política de segurança: %v", err)
	}

	var requirements []string
	requirements = append(requirements, fmt.Sprintf("- Mínimo de %d caracteres", policy.PasswordMinLength))

	if policy.PasswordRequireUppercase {
		requirements = append(requirements, "- Pelo menos uma letra maiúscula")
	}

	if policy.PasswordRequireNumber {
		requirements = append(requirements, "- Pelo menos um número")
	}

	if policy.PasswordRequireSpecialChar {
		requirements = append(requirements, "- Pelo menos um caractere especial")
	}

	if policy.PasswordExpiryDays > 0 {
		requirements = append(requirements, fmt.Sprintf("- A senha expira após %d dias", policy.PasswordExpiryDays))
	}

	return fmt.Sprintf("Requisitos de senha:\n%s", strings.Join(requirements, "\n")), nil
}

func (s *PasswordPolicyService) CheckPasswordHistory(userID uint, password string) error {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		log.Printf("Erro ao gerar hash para verificar histórico: %v", err)
		return errors.New("erro interno ao verificar histórico de senha")
	}

	inHistory, err := s.userRepo.IsPasswordInHistory(userID, string(hashedPassword))
	if err != nil {
		log.Printf("Erro ao verificar histórico de senha no repositório: %v", err)
		return nil
	}

	if inHistory {
		return errors.New("senha já utilizada recentemente")
	}

	return nil
}

func (s *PasswordPolicyService) CheckPasswordExpiry(user models.User) error {
	return nil
}

func (s *PasswordPolicyService) AddPasswordToHistory(userID uint, password string) error {
	return nil
}
