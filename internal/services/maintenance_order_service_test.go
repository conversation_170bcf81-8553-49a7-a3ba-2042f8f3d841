package services

import (
	"testing"

	"tradicao/internal/repository"

	"github.com/stretchr/testify/assert"
)

// TestNewMaintenanceOrderService testa a criação do serviço
func TestNewMaintenanceOrderService(t *testing.T) {
	// Arrange
	repo := &repository.MockMaintenanceOrderRepository{}

	// Act
	service := NewMaintenanceOrderService(repo)

	// Assert
	assert.NotNil(t, service)
	assert.Equal(t, repo, service.repo)
}
