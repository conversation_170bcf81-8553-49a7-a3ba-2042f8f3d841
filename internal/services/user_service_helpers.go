package services

import (
	"errors"
	"log"

	"tradicao/internal/models"
	"tradicao/internal/repository"

	"golang.org/x/crypto/bcrypt"
)

// GetAllUsers retorna todos os usuários do sistema
func GetAllUsers() ([]models.User, error) {
	// Obter repositório de usuários
	userRepo := repository.NewGormUserRepository()

	// Buscar todos os usuários
	users, err := userRepo.FindAll()
	if err != nil {
		log.Printf("Erro ao buscar usuários: %v", err)
		return nil, errors.New("erro ao buscar usuários")
	}

	// Limpar senhas por segurança
	for i := range users {
		users[i].Password = ""
	}

	return users, nil
}

// GetUserByID busca um usuário pelo ID
func GetUserByID(id uint) (models.User, error) {
	// Obter repositório de usuários
	userRepo := repository.NewGormUserRepository()

	// Buscar usuário pelo ID
	user, err := userRepo.GetUserByID(int64(id))
	if err != nil {
		log.Printf("Erro ao buscar usuário %d: %v", id, err)
		return models.User{}, errors.New("usuário não encontrado")
	}

	return *user, nil
}

// CreateUser cria um novo usuário
func CreateUser(user models.User, password string) (models.User, error) {
	// Obter repositório de usuários
	userRepo := repository.NewGormUserRepository()

	// Verificar se já existe usuário com o mesmo email
	existingUser, err := userRepo.FindByEmail(user.Email)
	if err == nil && existingUser.ID > 0 {
		return models.User{}, errors.New("já existe um usuário com este email")
	}

	// Hash da senha
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		log.Printf("Erro ao gerar hash da senha: %v", err)
		return models.User{}, errors.New("erro ao processar senha")
	}

	// Definir senha hasheada
	user.Password = string(hashedPassword)

	// Criar usuário
	createdUser, err := userRepo.Create(user)
	if err != nil {
		log.Printf("Erro ao criar usuário: %v", err)
		return models.User{}, errors.New("erro ao criar usuário")
	}

	return createdUser, nil
}

// UpdateUser atualiza um usuário existente
func UpdateUser(user models.User) (models.User, error) {
	// Obter repositório de usuários
	userRepo := repository.NewGormUserRepository()

	// Verificar se o usuário existe
	existingUser, err := userRepo.FindByID(user.ID)
	if err != nil {
		return models.User{}, errors.New("usuário não encontrado")
	}

	// Manter a senha original
	user.Password = existingUser.Password

	// Atualizar usuário
	updatedUser, err := userRepo.Update(user)
	if err != nil {
		log.Printf("Erro ao atualizar usuário %d: %v", user.ID, err)
		return models.User{}, errors.New("erro ao atualizar usuário")
	}

	return updatedUser, nil
}

// DeleteUser remove um usuário
func DeleteUser(id uint) error {
	// Obter repositório de usuários
	userRepo := repository.NewGormUserRepository()

	// Verificar se o usuário existe
	_, err := userRepo.FindByID(id)
	if err != nil {
		return errors.New("usuário não encontrado")
	}

	// Remover usuário
	err = userRepo.Delete(id)
	if err != nil {
		log.Printf("Erro ao remover usuário %d: %v", id, err)
		return errors.New("erro ao remover usuário")
	}

	return nil
}
