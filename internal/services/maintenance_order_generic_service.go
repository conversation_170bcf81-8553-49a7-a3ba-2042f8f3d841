package services

import (
	"context"
	"errors"
	"tradicao/internal/models"
	"tradicao/internal/repository"
)

// Erros comuns
var (
	ErrNotFound     = errors.New("ordem não encontrada")
	ErrInvalidInput = errors.New("entrada inválida")
	ErrUnauthorized = errors.New("não autorizado")
)

// MaintenanceOrderGenericService implementa um serviço para ordens de manutenção
// usando o repositório genérico
type MaintenanceOrderGenericService struct {
	repo repository.GenericRepository[models.MaintenanceOrder]
}

// NewMaintenanceOrderGenericService cria um novo serviço para ordens de manutenção
func NewMaintenanceOrderGenericService(
	repo repository.GenericRepository[models.MaintenanceOrder],
) *MaintenanceOrderGenericService {
	return &MaintenanceOrderGenericService{
		repo: repo,
	}
}

// GetOrderByID retorna uma ordem pelo ID
func (s *MaintenanceOrderGenericService) GetOrderByID(ctx context.Context, idStr string) (*models.MaintenanceOrder, error) {
	// Converter string para ID
	id, err := models.ParseID(idStr)
	if err != nil {
		return nil, ErrInvalidInput
	}

	// Buscar ordem pelo ID
	order, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			return nil, ErrNotFound
		}
		return nil, err
	}

	return order, nil
}

// CreateOrder cria uma nova ordem
func (s *MaintenanceOrderGenericService) CreateOrder(ctx context.Context, order *models.MaintenanceOrder) error {
	// Validar ordem
	if order.Problem == "" {
		return ErrInvalidInput
	}

	// Criar ordem
	return s.repo.Create(ctx, order)
}

// UpdateOrder atualiza uma ordem existente
func (s *MaintenanceOrderGenericService) UpdateOrder(ctx context.Context, idStr string, order *models.MaintenanceOrder) error {
	// Converter string para ID
	id, err := models.ParseID(idStr)
	if err != nil {
		return ErrInvalidInput
	}

	// Verificar se a ordem existe
	_, err = s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			return ErrNotFound
		}
		return err
	}

	// Atualizar ordem
	return s.repo.Update(ctx, id, order)
}

// DeleteOrder remove uma ordem
func (s *MaintenanceOrderGenericService) DeleteOrder(ctx context.Context, idStr string) error {
	// Converter string para ID
	id, err := models.ParseID(idStr)
	if err != nil {
		return ErrInvalidInput
	}

	// Verificar se a ordem existe
	_, err = s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			return ErrNotFound
		}
		return err
	}

	// Remover ordem
	return s.repo.Delete(ctx, id)
}

// ListOrders retorna uma lista de ordens com paginação
func (s *MaintenanceOrderGenericService) ListOrders(ctx context.Context, page, pageSize int) ([]models.MaintenanceOrder, int, error) {
	// Validar parâmetros
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// Calcular offset
	offset := (page - 1) * pageSize

	// Buscar ordens
	return s.repo.List(ctx, offset, pageSize)
}
