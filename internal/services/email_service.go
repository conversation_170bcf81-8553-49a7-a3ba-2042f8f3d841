package services

import (
	"fmt"
	"log"
	"net/smtp"
)

// EmailService define a interface para serviços de email
type EmailService interface {
	SendTechnicianInvitation(email, name, tempPassword string) error
}

// SimulatedEmailService implementa EmailService para desenvolvimento
// Apenas registra as mensagens no log em vez de enviar emails reais
type SimulatedEmailService struct{}

// NewSimulatedEmailService cria uma nova instância de SimulatedEmailService
func NewSimulatedEmailService() EmailService {
	return &SimulatedEmailService{}
}

// SendTechnicianInvitation simula o envio de um convite para um técnico
func (s *SimulatedEmailService) SendTechnicianInvitation(email, name, tempPassword string) error {
	log.Printf("[EMAIL SIMULADO] Convite enviado para técnico %s (%s) com senha temporária: %s",
		name, email, tempPassword)
	return nil
}

// SMTPEmailService implementa EmailService usando SMTP
type SMTPEmailService struct {
	host     string
	port     string
	username string
	password string
	from     string
}

// NewSMTPEmailService cria um novo SMTPEmailService
func NewSMTPEmailService(host, port, username, password, from string) *SMTPEmailService {
	return &SMTPEmailService{
		host:     host,
		port:     port,
		username: username,
		password: password,
		from:     from,
	}
}

// SendTechnicianInvitation envia email de convite para um técnico
func (s *SMTPEmailService) SendTechnicianInvitation(email, name, tempPassword string) error {
	// Configurar template de email
	subject := "Bem-vindo ao Sistema de Gestão Tradição - Acesso de Técnico"

	// Corpo do email em HTML
	htmlBody := fmt.Sprintf(`
	<html>
	<head>
		<style>
			body { font-family: Arial, sans-serif; line-height: 1.6; }
			.container { max-width: 600px; margin: 0 auto; padding: 20px; }
			.header { background-color: #f8f9fa; padding: 20px; text-align: center; }
			.content { padding: 20px; }
			.footer { background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; }
			.btn { display: inline-block; padding: 10px 20px; background-color: #ED1C24; color: white; text-decoration: none; border-radius: 5px; }
			.credentials { background-color: #f8f9fa; padding: 15px; margin: 15px 0; border-left: 4px solid #ED1C24; }
		</style>
	</head>
	<body>
		<div class="container">
			<div class="header">
				<h2>Bem-vindo ao Sistema de Gestão Tradição</h2>
			</div>
			<div class="content">
				<p>Olá, <strong>%s</strong>!</p>
				<p>Você foi cadastrado como técnico no Sistema de Gestão Tradição. Abaixo estão suas credenciais temporárias para acesso:</p>

				<div class="credentials">
					<p><strong>Email:</strong> %s</p>
					<p><strong>Senha temporária:</strong> %s</p>
				</div>

				<p>Por motivos de segurança, você será solicitado a alterar sua senha no primeiro acesso.</p>

				<p style="text-align: center; margin: 30px 0;">
					<a href="https://sistema.tradicao.com/login" class="btn">Acessar o Sistema</a>
				</p>

				<p>Se você tiver alguma dúvida, entre em contato com o suporte.</p>

				<p>Atenciosamente,<br>Equipe Tradição</p>
			</div>
			<div class="footer">
				<p>Este é um email automático. Por favor, não responda.</p>
			</div>
		</div>
	</body>
	</html>
	`, name, email, tempPassword)

	// Configurar cabeçalhos do email
	headers := make(map[string]string)
	headers["From"] = s.from
	headers["To"] = email
	headers["Subject"] = subject
	headers["MIME-Version"] = "1.0"
	headers["Content-Type"] = "text/html; charset=UTF-8"

	// Construir mensagem completa
	message := ""
	for k, v := range headers {
		message += fmt.Sprintf("%s: %s\r\n", k, v)
	}
	message += "\r\n" + htmlBody

	// Enviar email
	auth := smtp.PlainAuth("", s.username, s.password, s.host)
	return smtp.SendMail(
		s.host+":"+s.port,
		auth,
		s.from,
		[]string{email},
		[]byte(message),
	)
}
