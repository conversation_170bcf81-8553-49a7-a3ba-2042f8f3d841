package services

import (
	"fmt"
	"log"
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// PermissionAssignmentService é responsável por atribuir permissões automaticamente
// quando uma ordem de manutenção é criada ou atualizada
type PermissionAssignmentService struct {
	db                     *gorm.DB
	technicianOrderRepo    models.TechnicianOrderRepository
	technicianOrderService *TechnicianOrderService
}

// NewPermissionAssignmentService cria uma nova instância do serviço
func NewPermissionAssignmentService(
	db *gorm.DB,
	technicianOrderRepo models.TechnicianOrderRepository,
	technicianOrderService *TechnicianOrderService,
) *PermissionAssignmentService {
	return &PermissionAssignmentService{
		db:                     db,
		technicianOrderRepo:    technicianOrderRepo,
		technicianOrderService: technicianOrderService,
	}
}

// AssignPermissionsForNewOrder atribui permissões automaticamente quando uma nova ordem é criada
func (s *PermissionAssignmentService) AssignPermissionsForNewOrder(order *models.MaintenanceOrder) error {
	log.Printf("[PERMISSION-ASSIGNMENT] Iniciando atribuição automática de permissões para ordem #%d", order.ID)

	// Verificar se a ordem tem um técnico atribuído
	if order.TechnicianID == nil || *order.TechnicianID == 0 {
		log.Printf("[PERMISSION-ASSIGNMENT] Ordem #%d não tem técnico atribuído, pulando atribuição de permissões", order.ID)
		return nil
	}

	// Verificar se a ordem tem uma prestadora atribuída
	if order.ServiceProviderID == nil || *order.ServiceProviderID == 0 {
		log.Printf("[PERMISSION-ASSIGNMENT] Ordem #%d não tem prestadora atribuída, pulando atribuição de permissões", order.ID)
		return nil
	}

	// 1. Atribuir permissão ao técnico designado
	technicianID := *order.TechnicianID
	err := s.assignPermissionToTechnician(technicianID, order.ID, "Atribuição automática ao técnico designado")
	if err != nil {
		log.Printf("[PERMISSION-ASSIGNMENT] Erro ao atribuir permissão ao técnico %d para ordem #%d: %v",
			technicianID, order.ID, err)
		// Continuar mesmo com erro para tentar as outras atribuições
	}

	// 2. Buscar todos os técnicos da mesma prestadora
	providerID := *order.ServiceProviderID
	technicians, err := s.getTechniciansFromProvider(providerID)
	if err != nil {
		log.Printf("[PERMISSION-ASSIGNMENT] Erro ao buscar técnicos da prestadora %d: %v", providerID, err)
		return err
	}

	// 3. Atribuir permissão a todos os técnicos da prestadora
	successCount := 0
	for _, tech := range technicians {
		// Pular o técnico que já foi atribuído diretamente
		if tech.ID == technicianID {
			continue
		}

		err := s.assignPermissionToTechnician(tech.ID, order.ID,
			fmt.Sprintf("Atribuição automática por vínculo com prestadora %d", providerID))
		if err != nil {
			log.Printf("[PERMISSION-ASSIGNMENT] Erro ao atribuir permissão ao técnico %d da prestadora %d para ordem #%d: %v",
				tech.ID, providerID, order.ID, err)
			// Continuar mesmo com erro para tentar as outras atribuições
		} else {
			successCount++
		}
	}

	log.Printf("[PERMISSION-ASSIGNMENT] Atribuição automática de permissões concluída para ordem #%d. "+
		"Atribuída ao técnico designado e a %d técnicos adicionais da prestadora %d",
		order.ID, successCount, providerID)

	return nil
}

// AssignPermissionsForUpdatedOrder atribui permissões automaticamente quando uma ordem é atualizada
func (s *PermissionAssignmentService) AssignPermissionsForUpdatedOrder(
	oldOrder *models.MaintenanceOrder,
	newOrder *models.MaintenanceOrder,
) error {
	log.Printf("[PERMISSION-ASSIGNMENT] Iniciando atualização de permissões para ordem #%d", newOrder.ID)

	// Verificar se houve mudança no técnico atribuído
	if (oldOrder.TechnicianID == nil && newOrder.TechnicianID != nil) ||
		(oldOrder.TechnicianID != nil && newOrder.TechnicianID == nil) ||
		(oldOrder.TechnicianID != nil && newOrder.TechnicianID != nil && *oldOrder.TechnicianID != *newOrder.TechnicianID) {

		// Se havia um técnico anterior, manter suas permissões
		if oldOrder.TechnicianID != nil && *oldOrder.TechnicianID > 0 {
			log.Printf("[PERMISSION-ASSIGNMENT] Mantendo permissões do técnico anterior %d para ordem #%d",
				*oldOrder.TechnicianID, newOrder.ID)
		}

		// Se há um novo técnico, atribuir permissões
		if newOrder.TechnicianID != nil && *newOrder.TechnicianID > 0 {
			err := s.assignPermissionToTechnician(*newOrder.TechnicianID, newOrder.ID,
				"Atribuição automática ao novo técnico designado")
			if err != nil {
				log.Printf("[PERMISSION-ASSIGNMENT] Erro ao atribuir permissão ao novo técnico %d para ordem #%d: %v",
					*newOrder.TechnicianID, newOrder.ID, err)
				// Continuar mesmo com erro
			}
		}
	}

	// Verificar se houve mudança na prestadora atribuída
	if (oldOrder.ServiceProviderID == nil && newOrder.ServiceProviderID != nil) ||
		(oldOrder.ServiceProviderID != nil && newOrder.ServiceProviderID == nil) ||
		(oldOrder.ServiceProviderID != nil && newOrder.ServiceProviderID != nil &&
			*oldOrder.ServiceProviderID != *newOrder.ServiceProviderID) {

		// Se há uma nova prestadora, atribuir permissões aos técnicos dela
		if newOrder.ServiceProviderID != nil && *newOrder.ServiceProviderID > 0 {
			providerID := *newOrder.ServiceProviderID
			technicians, err := s.getTechniciansFromProvider(providerID)
			if err != nil {
				log.Printf("[PERMISSION-ASSIGNMENT] Erro ao buscar técnicos da nova prestadora %d: %v",
					providerID, err)
				return err
			}

			// Atribuir permissão a todos os técnicos da nova prestadora
			successCount := 0
			for _, tech := range technicians {
				err := s.assignPermissionToTechnician(tech.ID, newOrder.ID,
					fmt.Sprintf("Atribuição automática por vínculo com nova prestadora %d", providerID))
				if err != nil {
					log.Printf("[PERMISSION-ASSIGNMENT] Erro ao atribuir permissão ao técnico %d da nova prestadora %d para ordem #%d: %v",
						tech.ID, providerID, newOrder.ID, err)
					// Continuar mesmo com erro
				} else {
					successCount++
				}
			}

			log.Printf("[PERMISSION-ASSIGNMENT] Atribuídas permissões a %d técnicos da nova prestadora %d para ordem #%d",
				successCount, providerID, newOrder.ID)
		}
	}

	return nil
}

// assignPermissionToTechnician atribui permissão a um técnico para acessar uma ordem
func (s *PermissionAssignmentService) assignPermissionToTechnician(technicianID uint, orderID uint, notes string) error {
	// Verificar se a atribuição já existe
	exists, err := s.technicianOrderRepo.Exists(technicianID, orderID)
	if err != nil {
		return fmt.Errorf("erro ao verificar existência de atribuição: %w", err)
	}

	if exists {
		log.Printf("[PERMISSION-ASSIGNMENT] Técnico %d já tem permissão para acessar a ordem #%d",
			technicianID, orderID)
		return nil
	}

	// Criar a atribuição
	err = s.technicianOrderRepo.Create(technicianID, orderID, 1, notes) // 1 = sistema
	if err != nil {
		return fmt.Errorf("erro ao criar atribuição: %w", err)
	}

	log.Printf("[PERMISSION-ASSIGNMENT] Permissão atribuída com sucesso: Técnico %d -> Ordem #%d",
		technicianID, orderID)
	return nil
}

// getTechniciansFromProvider retorna todos os técnicos vinculados a uma prestadora
func (s *PermissionAssignmentService) getTechniciansFromProvider(providerID uint) ([]models.User, error) {
	var technicians []models.User

	// Buscar técnicos vinculados à prestadora
	err := s.db.Where("service_provider_id = ? AND type = ?", providerID, "technician").
		Find(&technicians).Error

	if err != nil {
		return nil, fmt.Errorf("erro ao buscar técnicos da prestadora %d: %w", providerID, err)
	}

	log.Printf("[PERMISSION-ASSIGNMENT] Encontrados %d técnicos vinculados à prestadora %d",
		len(technicians), providerID)
	return technicians, nil
}
