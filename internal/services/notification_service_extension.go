package services

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"tradicao/internal/models"
)

// EnviarNotificacaoPorFilial envia uma notificação para todos os usuários de uma filial específica
func (s *NotificationService) EnviarNotificacaoPorFilial(branchID uint, titulo, mensagem string, orderID int64, url string) error {
	// Buscar usuários da filial
	users, err := s.userRepo.GetUsersByBranchID(int64(branchID))
	if err != nil {
		return fmt.Errorf("erro ao buscar usuários da filial %d: %w", branchID, err)
	}

	if len(users) == 0 {
		log.Printf("Nenhum usuário encontrado para a filial %d", branchID)
		return nil
	}

	// Preparar o ID da ordem, se houver
	var orderIDUint *uint
	if orderID > 0 {
		u := uint(orderID)
		orderIDUint = &u
	}

	// Enviar notificação para cada usuário da filial
	for _, user := range users {
		s.enviarNotificacaoParaUsuario(user.ID, titulo, mensagem, orderIDUint, url)
	}

	return nil
}

// enviarNotificacaoParaUsuario envia uma notificação para um usuário específico
func (s *NotificationService) enviarNotificacaoParaUsuario(userID uint, titulo, mensagem string, orderID *uint, url string) {
	// Criar a notificação
	notificacao := models.Notification{
		UserID:    userID,
		Title:     titulo,
		Body:      mensagem,
		OrderID:   orderID,
		URL:       url,
		Type:      models.NotificationTypeInfo,
		Read:      false,
		CreatedAt: time.Now(),
	}

	// Salvar a notificação no banco de dados
	if err := s.repo.SaveNotification(notificacao); err != nil {
		log.Printf("Erro ao salvar notificação para usuário %d: %v", userID, err)
		return
	}

	// Enviar notificação push
	s.enviarNotificacaoPush(userID, notificacao)

	// Enviar notificação via WebSocket
	s.enviarNotificacaoWebSocket(userID, notificacao)
}

// enviarNotificacaoPush envia uma notificação push para um usuário
func (s *NotificationService) enviarNotificacaoPush(userID uint, notificacao models.Notification) {
	// Buscar as assinaturas do usuário
	subscriptions, err := s.repo.GetSubscriptionsByUserID(int64(userID))
	if err != nil {
		log.Printf("Erro ao buscar assinaturas do usuário %d: %v", userID, err)
		return
	}

	// Enviar para cada assinatura do usuário
	for _, sub := range subscriptions {
		if err := s.sendPushNotification(sub.Subscription, notificacao); err != nil {
			log.Printf("Erro ao enviar notificação push para %s: %v", sub.Subscription.Endpoint, err)
			// Continuar para as próximas assinaturas mesmo que uma falhe
		}
	}
}

// enviarNotificacaoWebSocket envia uma notificação via WebSocket para um usuário
func (s *NotificationService) enviarNotificacaoWebSocket(userID uint, notificacao models.Notification) {
	// Verificar se há clientes WebSocket
	if s.clients == nil {
		return
	}

	// Criar mensagem para WebSocket
	wsMessage := map[string]interface{}{
		"type":         "notification",
		"notification": notificacao,
	}

	// Converter para JSON
	messageBytes, err := json.Marshal(wsMessage)
	if err != nil {
		log.Printf("Erro ao converter notificação para JSON: %v", err)
		return
	}

	// Enviar para o cliente específico
	for client := range s.clients {
		if client.UserID == int64(userID) {
			select {
			case client.Send <- messageBytes:
				// Mensagem enviada com sucesso
			default:
				// Se o canal estiver cheio ou fechado, remover o cliente
				s.RemoveClient(client)
			}
		}
	}
}

// NotifyEquipmentTransfer envia notificação sobre transferência de equipamento
func (s *NotificationService) NotifyEquipmentTransfer(
	transferID uint,
	equipmentName string,
	sourceBranchID uint,
	sourceBranchName string,
	destinationBranchID uint,
	destinationBranchName string,
	action string,
) error {
	var titulo, mensagem string
	var targetBranchID uint
	var url string

	url = fmt.Sprintf("/minha-conta?transfer=%d", transferID)

	switch action {
	case "request":
		titulo = "Nova solicitação de transferência de equipamento"
		mensagem = fmt.Sprintf("A filial %s solicitou a transferência do equipamento %s para sua filial.",
			sourceBranchName, equipmentName)
		targetBranchID = destinationBranchID

	case "approve":
		titulo = "Transferência de equipamento aprovada"
		mensagem = fmt.Sprintf("A transferência do equipamento %s foi aprovada pela filial %s.",
			equipmentName, destinationBranchName)
		targetBranchID = sourceBranchID

	case "reject":
		titulo = "Transferência de equipamento rejeitada"
		mensagem = fmt.Sprintf("A transferência do equipamento %s foi rejeitada pela filial %s.",
			equipmentName, destinationBranchName)
		targetBranchID = sourceBranchID

	case "cancel":
		titulo = "Transferência de equipamento cancelada"
		mensagem = fmt.Sprintf("A transferência do equipamento %s foi cancelada pela filial %s.",
			equipmentName, sourceBranchName)
		targetBranchID = destinationBranchID

	default:
		return fmt.Errorf("ação de transferência desconhecida: %s", action)
	}

	// Enviar notificação para a filial alvo
	return s.EnviarNotificacaoPorFilial(targetBranchID, titulo, mensagem, 0, url)
}
