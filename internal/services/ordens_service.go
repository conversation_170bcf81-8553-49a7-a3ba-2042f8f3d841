package services

import (
	"errors"
	"log"
	"time"

	"tradicao/internal/db"
	"tradicao/internal/models"
	"tradicao/internal/repository"
)

// ListarOrdens retorna uma lista paginada de ordens de manutenção com filtros
func ListarOrdens(pagina, tamanhoPagina int, filtros map[string]interface{}) ([]models.OrdemManutencao, int64, error) {
	// Obter conexão com o banco
	database := db.GetDB()

	// Iniciar query
	query := database.Model(&models.OrdemManutencao{})

	// Aplicar filtros
	if filtros != nil {
		// Filtro de status
		if status, ok := filtros["status"].(string); ok {
			query = query.Where("status = ?", status)
		}

		// Filtro de prioridade
		if prioridade, ok := filtros["prioridade"].(string); ok {
			query = query.Where("prioridade = ?", prioridade)
		}

		// Filtro de filial
		if filialID, ok := filtros["filial_id"].(uint); ok {
			query = query.Where("filial_id = ?", filialID)
		}

		// Filtro de equipamento
		if equipamentoID, ok := filtros["equipamento_id"].(uint); ok {
			query = query.Where("equipamento_id = ?", equipamentoID)
		}

		// Filtro de técnico
		if tecnicoID, ok := filtros["tecnico_id"].(uint); ok {
			query = query.Where("tecnico_id = ?", tecnicoID)
		}

		// Filtros de data
		if dataInicio, ok := filtros["data_inicio"].(string); ok {
			query = query.Where("data_abertura >= ?", dataInicio)
		}

		if dataFim, ok := filtros["data_fim"].(string); ok {
			query = query.Where("data_abertura <= ?", dataFim)
		}

		// Filtro de busca por texto
		if busca, ok := filtros["busca"].(string); ok && busca != "" {
			busca = "%" + busca + "%"
			query = query.Where("titulo LIKE ? OR descricao LIKE ? OR observacoes LIKE ?", busca, busca, busca)
		}
	}

	// Contar total de resultados sem paginação
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Aplicar ordenação e paginação
	var ordens []models.OrdemManutencao
	offset := (pagina - 1) * tamanhoPagina

	if err := query.Order("created_at DESC").
		Limit(tamanhoPagina).
		Offset(offset).
		Find(&ordens).Error; err != nil {
		return nil, 0, err
	}

	return ordens, total, nil
}

// ObterOrdem retorna uma ordem específica pelo ID
func ObterOrdem(id uint) (models.OrdemManutencaoExpandida, error) {
	database := db.GetDB()

	// Buscar ordem básica
	var ordem models.OrdemManutencao
	if err := database.First(&ordem, id).Error; err != nil {
		return models.OrdemManutencaoExpandida{}, err
	}

	// Converter para modelo expandido
	var ordemExp models.OrdemManutencaoExpandida
	ordemExp.ID = ordem.ID
	ordemExp.Titulo = ordem.Title
	ordemExp.Status = models.StatusOrdem(ordem.Status)
	ordemExp.CreatedAt = ordem.CreatedAt
	ordemExp.UpdatedAt = ordem.UpdatedAt

	// Buscar histórico (exemplo, precisa ajustar)
	// if err := database.Where("ordem_id = ?", id).Order("data_evento DESC").Find(&ordemExp.Historico).Error; err != nil {
	// 	 log.Printf("Erro ao buscar histórico para ordem %d: %v", id, err)
	// }

	// Buscar fotos (exemplo, precisa ajustar)
	// if err := database.Where("ordem_id = ?", id).Order("data_upload DESC").Find(&ordemExp.Fotos).Error; err != nil {
	// 	 log.Printf("Erro ao buscar fotos para ordem %d: %v", id, err)
	// }

	return ordemExp, nil
}

// CriarOrdem cria uma nova ordem de manutenção
func CriarOrdem(ordem models.OrdemManutencaoExpandida) (models.OrdemManutencaoExpandida, error) {
	database := db.GetDB()

	// Iniciar transação
	tx := database.Begin()

	// Converter para modelo MaintenanceOrder para salvar no banco
	ordemParaSalvar := models.MaintenanceOrder{
		Problem:     ordem.Titulo + " - " + ordem.Descricao,
		BranchID:    ordem.FilialID,
		Number:      ordem.EquipamentoNome,
		Status:      models.OrderStatus(ordem.Status),
		Priority:    models.PriorityLevel(ordem.Prioridade),
		EquipmentID: ordem.EquipamentoID,
	}

	// Salvar ordem
	if err := tx.Create(&ordemParaSalvar).Error; err != nil {
		tx.Rollback()
		return models.OrdemManutencaoExpandida{}, err
	}

	// Criar histórico inicial
	historico := models.HistoricoOrdem{
		OrdemID:       ordemParaSalvar.GetIDAsUint(),
		UsuarioID:     ordem.SolicitanteID,
		UsuarioNome:   ordem.SolicitanteNome,
		DataEvento:    time.Now(),
		ValorAnterior: "",
		ValorNovo:     string(ordem.Status),
		Observacao:    "Ordem de manutenção criada",
		CreatedAt:     time.Now(),
	}

	if err := tx.Create(&historico).Error; err != nil {
		tx.Rollback()
		return models.OrdemManutencaoExpandida{}, err
	}

	// Salvar fotos se existirem
	if len(ordem.Fotos) > 0 {
		for i := range ordem.Fotos {
			ordem.Fotos[i].OrdemID = ordemParaSalvar.GetIDAsUint()
			ordem.Fotos[i].DataUpload = time.Now()
			ordem.Fotos[i].CreatedAt = time.Now()

			if err := tx.Create(&ordem.Fotos[i]).Error; err != nil {
				tx.Rollback()
				return models.OrdemManutencaoExpandida{}, err
			}
		}
	}

	// Atribuir automaticamente a ordem a um prestador
	autoAssignmentService := NewAutoAssignmentService(tx)
	if err := autoAssignmentService.AssignOrderToProvider(&ordemParaSalvar); err != nil {
		// Apenas logar o erro, não impedir a criação da ordem
		log.Printf("Erro ao atribuir automaticamente a ordem a um prestador: %s", err.Error())
	} else {
		// Adicionar histórico de atribuição automática
		if ordemParaSalvar.ServiceProviderID != nil && *ordemParaSalvar.ServiceProviderID > 0 {
			// Buscar nome do prestador
			var provider models.ServiceProvider
			err := tx.First(&provider, *ordemParaSalvar.ServiceProviderID).Error
			if err == nil {
				historicoAtribuicao := models.HistoricoOrdem{
					OrdemID:       ordemParaSalvar.GetIDAsUint(),
					UsuarioID:     ordem.SolicitanteID,
					UsuarioNome:   ordem.SolicitanteNome,
					DataEvento:    time.Now(),
					ValorAnterior: "",
					ValorNovo:     provider.Name,
					Observacao:    "Ordem atribuída automaticamente ao prestador " + provider.Name,
					CreatedAt:     time.Now(),
				}

				if err := tx.Create(&historicoAtribuicao).Error; err != nil {
					log.Printf("Erro ao criar histórico de atribuição automática: %s", err.Error())
				}
			}
		}
	}

	// Commit transação
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return models.OrdemManutencaoExpandida{}, err
	}

	// Retornar ordem expandida buscando novamente (garante dados atualizados)
	return ObterOrdem(ordemParaSalvar.GetIDAsUint())
}

// AtualizarStatusOrdem atualiza o status de uma ordem e adiciona ao histórico
func AtualizarStatusOrdem(id uint, novoStatus models.StatusOrdem, observacao string, usuarioID uint) (models.OrdemManutencaoExpandida, error) {
	database := db.GetDB()

	// Buscar ordem atual (usando OrdemManutencao pois é o que a assinatura espera)
	var ordem models.OrdemManutencao
	if err := database.First(&ordem, id).Error; err != nil {
		return models.OrdemManutencaoExpandida{}, err
	}

	// Verificar se a transição de status é válida
	statusAtual := models.StatusOrdem(ordem.Status)
	if !isValidStatusTransition(statusAtual, novoStatus, usuarioID) {
		return models.OrdemManutencaoExpandida{}, errors.New("transição de status inválida")
	}

	// Buscar nome do usuário
	userRepo := repository.NewGormUserRepository()
	usuario, err := userRepo.FindByID(uint(usuarioID))
	if err != nil {
		return models.OrdemManutencaoExpandida{}, err
	}

	// Iniciar transação
	tx := database.Begin()

	// Salvar status anterior para o histórico
	statusAnterior := ordem.Status

	// Atualizar status na ordem
	ordem.Status = string(novoStatus)

	// Atualizar ordem
	if err := tx.Save(&ordem).Error; err != nil {
		tx.Rollback()
		return models.OrdemManutencaoExpandida{}, err
	}

	// Criar histórico
	historico := models.HistoricoOrdem{
		OrdemID:       id,
		UsuarioID:     usuarioID,
		UsuarioNome:   usuario.Name,
		DataEvento:    time.Now(),
		ValorAnterior: statusAnterior,
		ValorNovo:     string(novoStatus),
		Observacao:    observacao,
		CreatedAt:     time.Now(),
	}

	if err := tx.Create(&historico).Error; err != nil {
		tx.Rollback()
		return models.OrdemManutencaoExpandida{}, err
	}

	// Commit transação
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return models.OrdemManutencaoExpandida{}, err
	}

	// Retornar ordem expandida
	return ObterOrdem(id)
}

// ListarEquipamentos retorna a lista de equipamentos
func ListarEquipamentos(filialID uint) ([]models.Equipamento, error) {
	database := db.GetDB()

	var equipamentos []models.Equipamento
	query := database.Model(&models.Equipamento{})

	// Filtrar por filial se fornecida
	if filialID > 0 {
		query = query.Where("filial_id = ?", filialID)
	}

	if err := query.Order("nome").Find(&equipamentos).Error; err != nil {
		return nil, err
	}

	return equipamentos, nil
}

// ListarFiliais retorna a lista de filiais
func ListarFiliais() ([]models.Branch, error) {
	database := db.GetDB()

	var filiais []models.Branch
	if err := database.Order("name").Find(&filiais).Error; err != nil {
		return nil, err
	}

	return filiais, nil
}

/*
// Função auxiliar para converter modelo básico para expandido
// ATENÇÃO: Esta função pode não preencher todos os campos corretamente
// devido às limitações do tipo de entrada models.OrdemManutencao
// TEMPORARIAMENTE COMENTADA PARA RESOLVER ERROS DE LINTER
func convertToExpandedOrder(ordem models.OrdemManutencao) models.OrdemManutencaoExpandida {
	return models.OrdemManutencaoExpandida{
		ID:        ordem.ID,
		// Titulo:          ordem.Title, // Campo existe mas comentado para evitar erro de linter transitório?
		// Descricao:       ordem.Descricao,       // Campo não existe em models.OrdemManutencao
		// FilialID:        ordem.FilialID,        // Campo não existe em models.OrdemManutencao
		// FilialNome:      ordem.FilialNome,      // Campo não existe em models.OrdemManutencao
		// EquipamentoID:   ordem.EquipamentoID,   // Campo não existe em models.OrdemManutencao
		// EquipamentoNome: ordem.EquipamentoNome, // Campo não existe em models.OrdemManutencao
		// Status:          string(ordem.Status), // Campo existe mas comentado para evitar erro de linter transitório?
		// Prioridade:      ordem.Prioridade,      // Campo não existe em models.OrdemManutencao
		// Tipo:            ordem.Tipo,            // Campo não existe em models.OrdemManutencao
		// SolicitanteID:   ordem.SolicitanteID,   // Campo não existe em models.OrdemManutencao
		// SolicitanteNome: ordem.SolicitanteNome, // Campo não existe em models.OrdemManutencao
		// TecnicoID:       ordem.TecnicoID,       // Campo não existe em models.OrdemManutencao
		// TecnicoNome:     ordem.TecnicoNome,     // Campo não existe em models.OrdemManutencao
		// DataAbertura:    ordem.DataAbertura,    // Campo não existe em models.OrdemManutencao
		// DataDesejada:    ordem.DataDesejada,    // Campo não existe em models.OrdemManutencao
		// DataInicio:      ordem.DataInicio,      // Campo não existe em models.OrdemManutencao
		// DataConclusao:   ordem.DataConclusao,   // Campo não existe em models.OrdemManutencao
		// Observacoes:     ordem.Observacoes,     // Campo não existe em models.OrdemManutencao
		CreatedAt: ordem.CreatedAt,
		UpdatedAt: ordem.UpdatedAt,
		// Historico:       []models.HistoricoOrdem{}, // Campo não existe em models.OrdemManutencao
		// Fotos:           []models.FotoOrdem{},      // Campo não existe em models.OrdemManutencao
	}
}
*/

// Verifica se a transição de status é válida com base em regras de negócio
// Esta função usa as constantes padronizadas e funções de compatibilidade para verificar transições
func isValidStatusTransition(atual, novo models.StatusOrdem, _ uint) bool {
	// Normalizar os status para garantir compatibilidade com valores legados
	atualNormalizado := models.NormalizeOrderStatus(string(atual))
	novoNormalizado := models.NormalizeOrderStatus(string(novo))

	// Por enquanto, implementa uma versão simplificada do fluxo usando constantes padronizadas
	switch atualNormalizado {
	case models.StatusInProgress:
		// De em atendimento pode ir para pendente ou concluída
		return novoNormalizado == models.StatusPending ||
			novoNormalizado == models.StatusCompleted
	case models.StatusPending:
		// De pendente pode ir para aprovada, rejeitada ou cancelada
		return novoNormalizado == models.StatusApproved ||
			novoNormalizado == models.StatusRejected ||
			novoNormalizado == models.StatusCancelled // Nota: Usamos StatusCancelled em vez de StatusCanceled
	case models.StatusRejected:
		// De rejeitada pode ir para em atendimento (para revisar) ou cancelada
		return novoNormalizado == models.StatusInProgress ||
			novoNormalizado == models.StatusCancelled // Nota: Usamos StatusCancelled em vez de StatusCanceled
	case models.StatusApproved:
		// De aprovada pode ir para em atendimento
		return novoNormalizado == models.StatusInProgress
	case models.StatusCompleted:
		return false
	case models.StatusCancelled: // Nota: Usamos StatusCancelled em vez de StatusCanceled
		return false
	default:
		log.Printf("Transição de status inválida ou não mapeada: de %s para %s", atual, novo)
		return false
	}
}
