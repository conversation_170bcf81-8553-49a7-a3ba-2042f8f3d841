package services

import (
	"fmt"
	"tradicao/internal/models"
	"tradicao/internal/repository"
)

// FilialService encapsula a lógica de negócios relacionada a filiais
type FilialService struct {
	filialRepo repository.IFilialRepository
}

// NewFilialService cria uma nova instância do serviço de filiais
func NewFilialService(filialRepo repository.IFilialRepository) *FilialService {
	return &FilialService{
		filialRepo: filialRepo,
	}
}

// GetAllFiliais retorna todas as filiais
func (s *FilialService) GetAllFiliais() ([]models.Filial, error) {
	return s.filialRepo.GetAllFiliais()
}

// GetFilialByID retorna uma filial pelo ID
func (s *FilialService) GetFilialByID(id int64) (*models.Filial, error) {
	return s.filialRepo.GetFilialByID(uint(id))
}

// GetFiliaisByUserID retorna todas as filiais de um usuário
func (s *FilialService) GetFiliaisByUserID(userID int64) ([]models.Filial, error) {
	return s.filialRepo.GetFiliaisByUserID(userID)
}

// CreateFilial cria uma nova filial
func (s *FilialService) CreateFilial(filial *models.Filial) error {
	// Verifica se já existe uma filial com o mesmo código
	filiais, err := s.filialRepo.GetAllFiliais()
	if err != nil {
		return err
	}

	for _, f := range filiais {
		if f.Code == filial.Code {
			return fmt.Errorf("já existe uma filial com o código %s", filial.Code)
		}
	}

	return s.filialRepo.CreateFilial(filial)
}

// UpdateFilial atualiza uma filial existente
func (s *FilialService) UpdateFilial(filial *models.Filial) error {
	// Verifica se a filial existe
	existingFilial, err := s.filialRepo.GetFilialByID(filial.ID)
	if err != nil {
		return err
	}

	if existingFilial == nil {
		return fmt.Errorf("filial não encontrada")
	}

	return s.filialRepo.UpdateFilial(filial)
}

// DeleteFilial remove uma filial
func (s *FilialService) DeleteFilial(id int64) error {
	return s.filialRepo.DeleteFilial(uint(id))
}

// GetFiliaisByRegion retorna filiais por região
func (s *FilialService) GetFiliaisByRegion(region string) ([]models.Filial, error) {
	return s.filialRepo.GetFiliaisByRegion(region)
}

// GetActiveFiliais retorna todas as filiais ativas
func (s *FilialService) GetActiveFiliais() ([]models.Filial, error) {
	return s.filialRepo.GetActiveFiliais()
}

// GetFilialMetrics retorna métricas das filiais
func (s *FilialService) GetFilialMetrics() (*models.FilialMetrics, error) {
	return s.filialRepo.GetFilialMetrics()
}
