package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"tradicao/internal/models"
	"tradicao/internal/repository"
)

const (
	errUpdateTransferStatus = "erro ao atualizar status da transferência: %w"
	errTransferNotPending  = "transferência não está pendente"
)

// EquipmentTransferService gerencia a lógica de negócios relacionada a transferências de equipamentos
type EquipmentTransferService struct {
	transferRepo  repository.IEquipmentTransferRepository
	equipmentRepo repository.IEquipmentRepository
	branchRepo    interface {
		GetBranchByID(id uint) (*models.Branch, error)
	}
	notificationSvc *NotificationService
}

// NewEquipmentTransferService cria uma nova instância do serviço de transferências de equipamentos
func NewEquipmentTransferService(
	transferRepo repository.IEquipmentTransferRepository,
	equipmentRepo repository.IEquipmentRepository,
	branchRepo interface {
		GetBranchByID(id uint) (*models.Branch, error)
	},
	notificationSvc *NotificationService,
) *EquipmentTransferService {
	return &EquipmentTransferService{
		transferRepo:    transferRepo,
		equipmentRepo:   equipmentRepo,
		branchRepo:      branchRepo,
		notificationSvc: notificationSvc,
	}
}

// RequestTransfer solicita uma transferência de equipamento
func (s *EquipmentTransferService) RequestTransfer(
	ctx context.Context,
	equipmentID uint,
	sourceBranchID uint,
	destinationBranchID uint,
	requestedByUserID uint,
	justification string,
	authorizedBy string,
) (*models.EquipmentTransfer, error) {
	// Verificar se o equipamento existe
	equipment, err := s.equipmentRepo.FindByID(ctx, equipmentID)
	if err != nil {
		return nil, fmt.Errorf("erro ao buscar equipamento: %w", err)
	}
	if equipment == nil {
		return nil, errors.New("equipamento não encontrado")
	}

	// Verificar se o equipamento pertence à filial de origem
	if equipment.BranchID != sourceBranchID {
		return nil, errors.New("equipamento não pertence à filial de origem")
	}

	// Verificar se as filiais existem
	sourceFilial, err := s.branchRepo.GetBranchByID(sourceBranchID)
	if err != nil {
		return nil, fmt.Errorf("erro ao buscar filial de origem: %w", err)
	}

	// Verificar se a filial de destino existe
	destFilial, err := s.branchRepo.GetBranchByID(destinationBranchID)
	if err != nil {
		return nil, fmt.Errorf("erro ao buscar filial de destino: %w", err)
	}

	// Criar a transferência
	transfer := &models.EquipmentTransfer{
		EquipmentID:         equipmentID,
		SourceBranchID:      sourceBranchID,
		DestinationBranchID: destinationBranchID,
		RequestedByUserID:   requestedByUserID,
		Justification:       justification,
		AuthorizedBy:        authorizedBy,
		Status:              models.StatusTransferenciaPendente,
		RequestedAt:         time.Now(),
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
	}

	// Salvar a transferência
	if err := s.transferRepo.Create(ctx, transfer); err != nil {
		return nil, fmt.Errorf("erro ao criar transferência: %w", err)
	}

	// Enviar notificação para a filial de destino
	if s.notificationSvc != nil {
		// Enviar notificação
		s.notificationSvc.NotifyEquipmentTransfer(
			transfer.ID,
			equipment.Name,
			sourceBranchID,
			sourceFilial.Name,
			destinationBranchID,
			destFilial.Name,
			"request",
		)
	}

	return transfer, nil
}

// GetTransferByID busca uma transferência pelo ID
func (s *EquipmentTransferService) GetTransferByID(ctx context.Context, id uint) (*models.EquipmentTransfer, error) {
	return s.transferRepo.FindByID(ctx, id)
}

// GetTransfersByEquipment busca transferências de um equipamento
func (s *EquipmentTransferService) GetTransfersByEquipment(ctx context.Context, equipmentID uint) ([]models.EquipmentTransfer, error) {
	return s.transferRepo.FindByEquipmentID(ctx, equipmentID)
}

// GetPendingTransfersBySourceBranch busca transferências pendentes de uma filial de origem
func (s *EquipmentTransferService) GetPendingTransfersBySourceBranch(ctx context.Context, branchID uint) ([]models.EquipmentTransfer, error) {
	return s.transferRepo.FindPendingBySourceBranch(ctx, branchID)
}

// GetPendingTransfersByDestinationBranch busca transferências pendentes para uma filial de destino
func (s *EquipmentTransferService) GetPendingTransfersByDestinationBranch(ctx context.Context, branchID uint) ([]models.EquipmentTransfer, error) {
	return s.transferRepo.FindPendingByDestinationBranch(ctx, branchID)
}

// ApproveTransfer aprova uma transferência
func (s *EquipmentTransferService) ApproveTransfer(ctx context.Context, id uint, approvedByUserID uint, notes string) error {
	// Verificar se a transferência existe
	transfer, err := s.transferRepo.FindByID(ctx, id)
	if err != nil {
		return fmt.Errorf("erro ao buscar transferência: %w", err)
	}
	if transfer == nil {
		return errors.New("transferência não encontrada")
	}

	// Verificar se a transferência está pendente
	if transfer.Status != models.StatusTransferenciaPendente {
		return errors.New(errTransferNotPending)
	}

	// Atualizar o status da transferência
	if err := s.transferRepo.UpdateStatus(ctx, id, models.StatusTransferenciaAprovada, &approvedByUserID, notes); err != nil {
		return fmt.Errorf(errUpdateTransferStatus, err)
	}

	// Completar a transferência (atualizar o branch_id do equipamento)
	if err := s.transferRepo.CompleteTransfer(ctx, id); err != nil {
		return fmt.Errorf("erro ao completar transferência: %w", err)
	}

	// Enviar notificação para a filial de origem
	if s.notificationSvc != nil && transfer.Equipment != nil {
		// Buscar nomes das filiais
		sourceFilial, _ := s.branchRepo.GetBranchByID(transfer.SourceBranchID)
		destFilial, _ := s.branchRepo.GetBranchByID(transfer.DestinationBranchID)

		// Enviar notificação
		s.notificationSvc.NotifyEquipmentTransfer(
			transfer.ID,
			transfer.Equipment.Name,
			transfer.SourceBranchID,
			sourceFilial.Name,
			transfer.DestinationBranchID,
			destFilial.Name,
			"approve",
		)
	}

	return nil
}

// RejectTransfer rejeita uma transferência
func (s *EquipmentTransferService) RejectTransfer(ctx context.Context, id uint, rejectedByUserID uint, notes string) error {
	// Verificar se a transferência existe
	transfer, err := s.transferRepo.FindByID(ctx, id)
	if err != nil {
		return fmt.Errorf("erro ao buscar transferência: %w", err)
	}
	if transfer == nil {
		return errors.New("transferência não encontrada")
	}

	// Verificar se a transferência está pendente
	if transfer.Status != models.StatusTransferenciaPendente {
		return errors.New(errTransferNotPending)
	}

	// Atualizar o status da transferência
	if err := s.transferRepo.UpdateStatus(ctx, id, models.StatusTransferenciaRejeitada, &rejectedByUserID, notes); err != nil {
		return fmt.Errorf(errUpdateTransferStatus, err)
	}

	// Enviar notificação para a filial de origem
	if s.notificationSvc != nil && transfer.Equipment != nil {
		// Buscar nomes das filiais
		sourceFilial, _ := s.branchRepo.GetBranchByID(transfer.SourceBranchID)
		destFilial, _ := s.branchRepo.GetBranchByID(transfer.DestinationBranchID)

		// Enviar notificação
		s.notificationSvc.NotifyEquipmentTransfer(
			transfer.ID,
			transfer.Equipment.Name,
			transfer.SourceBranchID,
			sourceFilial.Name,
			transfer.DestinationBranchID,
			destFilial.Name,
			"reject",
		)
	}

	return nil
}

// CancelTransfer cancela uma transferência
func (s *EquipmentTransferService) CancelTransfer(ctx context.Context, id uint, canceledByUserID uint, notes string) error {
	// Verificar se a transferência existe
	transfer, err := s.transferRepo.FindByID(ctx, id)
	if err != nil {
		return fmt.Errorf("erro ao buscar transferência: %w", err)
	}
	if transfer == nil {
		return errors.New("transferência não encontrada")
	}

	// Verificar se a transferência está pendente
	if transfer.Status != models.StatusTransferenciaPendente {
		return errors.New(errTransferNotPending)
	}

	// Atualizar o status da transferência
	if err := s.transferRepo.UpdateStatus(ctx, id, models.StatusTransferenciaCancelada, &canceledByUserID, notes); err != nil {
		return fmt.Errorf(errUpdateTransferStatus, err)
	}

	// Enviar notificação para a filial de destino
	if s.notificationSvc != nil && transfer.Equipment != nil {
		// Buscar nomes das filiais
		sourceFilial, _ := s.branchRepo.GetBranchByID(transfer.SourceBranchID)
		destFilial, _ := s.branchRepo.GetBranchByID(transfer.DestinationBranchID)

		// Enviar notificação
		s.notificationSvc.NotifyEquipmentTransfer(
			transfer.ID,
			transfer.Equipment.Name,
			transfer.SourceBranchID,
			sourceFilial.Name,
			transfer.DestinationBranchID,
			destFilial.Name,
			"cancel",
		)
	}

	return nil
}

// ListAllTransfers lista todas as transferências
func (s *EquipmentTransferService) ListAllTransfers(ctx context.Context) ([]models.EquipmentTransfer, error) {
	return s.transferRepo.List(ctx)
}
