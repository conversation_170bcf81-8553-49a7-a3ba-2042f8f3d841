package services

import (
	"tradicao/internal/models"
	"tradicao/internal/repository"
)

// BranchService encapsula a lógica de negócios relacionada a filiais
type BranchService struct {
	branchRepo repository.IBranchRepository
}

// NewBranchService cria uma nova instância do serviço de filiais
func NewBranchService(branchRepo repository.IBranchRepository) *BranchService {
	return &BranchService{
		branchRepo: branchRepo,
	}
}

// GetAllBranches retorna todas as filiais com paginação e filtros
func (s *BranchService) GetAllBranches(page, limit int, search string, isActive *bool) ([]models.Branch, int64, error) {
	return s.branchRepo.GetAllBranches(page, limit, search, isActive)
}

// GetBranchByID retorna uma filial pelo ID
func (s *BranchService) GetBranchByID(id uint) (*models.Branch, error) {
	return s.branchRepo.GetBranchByID(id)
}

// GetBranchesByUserID retorna filiais associadas a um usuário
func (s *BranchService) GetBranchesByUserID(userID int64) ([]models.Branch, error) {
	return s.branchRepo.GetBranchesByUserID(userID)
}

// GetBranchesByTechnicianID retorna filiais onde um técnico está associado a ordens
func (s *BranchService) GetBranchesByTechnicianID(technicianID int64) ([]models.Branch, error) {
	return s.branchRepo.GetBranchesByTechnicianID(technicianID)
}

// CreateBranch cria uma nova filial
func (s *BranchService) CreateBranch(branch *models.Branch) error {
	return s.branchRepo.CreateBranch(branch)
}

// UpdateBranch atualiza uma filial existente
func (s *BranchService) UpdateBranch(branch *models.Branch) error {
	// Verificar se a filial existe antes de atualizar
	exists, err := s.branchRepo.BranchExists(branch.ID)
	if err != nil {
		return err
	}

	if !exists {
		return models.ErrNotFound
	}

	return s.branchRepo.UpdateBranch(branch)
}

// DeleteBranch remove uma filial
func (s *BranchService) DeleteBranch(id uint) error {
	// Verificar se a filial existe antes de excluir
	exists, err := s.branchRepo.BranchExists(id)
	if err != nil {
		return err
	}

	if !exists {
		return models.ErrNotFound
	}

	return s.branchRepo.DeleteBranch(id)
}

// LinkProvider vincula um prestador a uma filial
func (s *BranchService) LinkProvider(branchID, providerID uint) error {
	return s.branchRepo.LinkProvider(branchID, providerID)
}

// UnlinkProvider remove o vínculo entre um prestador e uma filial
func (s *BranchService) UnlinkProvider(branchID, providerID uint) error {
	return s.branchRepo.UnlinkProvider(branchID, providerID)
}

// GetLinkedProviders retorna os IDs dos prestadores vinculados a uma filial
func (s *BranchService) GetLinkedProviders(branchID uint) ([]uint, error) {
	return s.branchRepo.GetLinkedProviders(branchID)
}

// GenerateAuthToken gera um token de autenticação para uma filial
func (s *BranchService) GenerateAuthToken(branchID uint) (string, error) {
	return s.branchRepo.GenerateAuthToken(branchID)
}

// ValidateAuthToken valida um token de autenticação de uma filial
func (s *BranchService) ValidateAuthToken(token string) (uint, error) {
	return s.branchRepo.ValidateAuthToken(token)
}

// UseAuthToken utiliza um token de autenticação, invalidando-o após o uso
func (s *BranchService) UseAuthToken(token string) error {
	return s.branchRepo.UseAuthToken(token)
}

// GetBranchesByRegion retorna filiais por região (estado)
func (s *BranchService) GetBranchesByRegion(region string) ([]models.Branch, error) {
	return s.branchRepo.GetBranchesByRegion(region)
}

// GetActiveBranches retorna todas as filiais ativas
func (s *BranchService) GetActiveBranches() ([]models.Branch, error) {
	return s.branchRepo.GetActiveBranches()
}

// GetBranchMetrics retorna métricas das filiais
func (s *BranchService) GetBranchMetrics() (*models.BranchMetrics, error) {
	return s.branchRepo.GetBranchMetrics()
}

// GetBranchSummaries retorna resumos de todas as filiais
func (s *BranchService) GetBranchSummaries() ([]models.BranchSummary, error) {
	return s.branchRepo.GetBranchSummaries()
}

// GetBranchSummaryByID retorna o resumo de uma filial pelo ID
func (s *BranchService) GetBranchSummaryByID(id uint) (*models.BranchSummary, error) {
	return s.branchRepo.GetBranchSummaryByID(id)
}
