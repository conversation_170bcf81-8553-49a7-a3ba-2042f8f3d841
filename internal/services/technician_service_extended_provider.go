package services

import (
	"errors"
	"tradicao/internal/models"
	"tradicao/internal/repository"
)

// TechnicianProviderService gerencia a lógica de negócios para técnicos relacionados a prestadores
type TechnicianProviderService struct {
	userRepo      repository.UserRepository
	providerRepo  repository.ServiceProviderRepository
	orderRepo     repository.MaintenanceOrderRepository
	uploadService *FileUploadService
}

// NewTechnicianProviderService cria um novo TechnicianProviderService
func NewTechnicianProviderService(
	userRepo repository.UserRepository,
	providerRepo repository.ServiceProviderRepository,
	orderRepo repository.MaintenanceOrderRepository,
	uploadService *FileUploadService,
) *TechnicianProviderService {
	return &TechnicianProviderService{
		userRepo:      userRepo,
		providerRepo:  providerRepo,
		orderRepo:     orderRepo,
		uploadService: uploadService,
	}
}

// GetServiceProvider retorna o prestador de um técnico
func (s *TechnicianProviderService) GetServiceProvider(technicianID uint) (models.ServiceProvider, error) {
	user, err := s.userRepo.FindByID(technicianID)
	if err != nil {
		return models.ServiceProvider{}, err
	}
	
	if user.ServiceProviderID == nil || *user.ServiceProviderID == 0 {
		return models.ServiceProvider{}, errors.New("técnico não está vinculado a nenhum prestador")
	}
	
	return s.providerRepo.FindByID(*user.ServiceProviderID)
}

// GetOrdersByTechnician retorna ordens de serviço atribuídas a um técnico
func (s *TechnicianProviderService) GetOrdersByTechnician(technicianID uint) ([]models.MaintenanceOrder, error) {
	return s.orderRepo.FindByTechnician(technicianID)
}

// UpdateTechnicianAvatar atualiza o avatar de um técnico
func (s *TechnicianProviderService) UpdateTechnicianAvatar(technicianID uint, avatarURL string) error {
	return s.userRepo.UpdateAvatar(technicianID, avatarURL)
}
