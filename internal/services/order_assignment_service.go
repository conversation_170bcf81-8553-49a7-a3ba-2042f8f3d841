package services

import (
	"fmt"
	"log"
	"tradicao/internal/models"
	"tradicao/internal/notifications"

	"gorm.io/gorm"
)

// OrderAssignmentService é o serviço centralizado para atribuição de ordens
// Este serviço gerencia todas as operações relacionadas à atribuição de ordens a técnicos e prestadores
type OrderAssignmentService struct {
	db                     *gorm.DB
	technicianOrderRepo    models.TechnicianOrderRepository
	notificationService    *notifications.Service
	permissionService      *PermissionAssignmentService
}

// NewOrderAssignmentService cria uma nova instância do serviço de atribuição de ordens
func NewOrderAssignmentService(
	db *gorm.DB,
	technicianOrderRepo models.TechnicianOrderRepository,
	permissionService *PermissionAssignmentService,
) *OrderAssignmentService {
	return &OrderAssignmentService{
		db:                     db,
		technicianOrderRepo:    technicianOrderRepo,
		notificationService:    notifications.NewService(),
		permissionService:      permissionService,
	}
}

// AssignOrderToTechnician atribui uma ordem a um técnico
// Esta função é a principal para atribuir ordens a técnicos e deve ser usada em vez de atualizar diretamente o campo technician_id
func (s *OrderAssignmentService) AssignOrderToTechnician(orderID, technicianID, assignedByID uint) error {
	// Iniciar uma transação
	tx := s.db.Begin()
	if tx.Error != nil {
		return fmt.Errorf("erro ao iniciar transação: %w", tx.Error)
	}

	// Garantir que a transação seja revertida em caso de erro
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Buscar a ordem
	var order models.MaintenanceOrder
	if err := tx.First(&order, orderID).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("erro ao buscar ordem: %w", err)
	}

	// Buscar o técnico
	var technician models.User
	if err := tx.First(&technician, technicianID).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("erro ao buscar técnico: %w", err)
	}

	// Verificar se o técnico tem o perfil correto
	if !models.IsTechnician(string(technician.Role)) {
		tx.Rollback()
		return fmt.Errorf("usuário %d não é um técnico", technicianID)
	}

	// Atualizar o campo technician_id da ordem (para compatibilidade com código existente)
	if err := tx.Model(&order).Update("technician_id", technicianID).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("erro ao atualizar technician_id: %w", err)
	}

	// Atualizar o campo assigned_to_user_id da ordem (para compatibilidade com código existente)
	if err := tx.Model(&order).Update("assigned_to_user_id", technicianID).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("erro ao atualizar assigned_to_user_id: %w", err)
	}

	// Criar registro na tabela technician_orders
	notes := fmt.Sprintf("Atribuição manual por usuário ID %d", assignedByID)
	if err := s.technicianOrderRepo.Create(technicianID, orderID, assignedByID, notes); err != nil {
		tx.Rollback()
		return fmt.Errorf("erro ao criar registro na tabela technician_orders: %w", err)
	}

	// Atualizar o campo service_provider_id se o técnico tiver um prestador associado
	if technician.ServiceProviderID != nil && *technician.ServiceProviderID > 0 {
		if err := tx.Model(&order).Update("service_provider_id", *technician.ServiceProviderID).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("erro ao atualizar service_provider_id: %w", err)
		}
	}

	// Confirmar a transação
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("erro ao confirmar transação: %w", err)
	}

	// Enviar notificação ao técnico
	if err := s.notificationService.NotifyOrderAssigned(orderID, technicianID); err != nil {
		// Apenas logar o erro, não impedir a atribuição
		log.Printf("Erro ao enviar notificação para o técnico %d: %s", technicianID, err.Error())
	}

	log.Printf("Ordem %d atribuída com sucesso ao técnico %d", orderID, technicianID)
	return nil
}

// AssignOrderToProvider atribui uma ordem a um prestador de serviço
// Esta função deve ser usada em vez de atualizar diretamente o campo service_provider_id
func (s *OrderAssignmentService) AssignOrderToProvider(orderID, providerID, assignedByID uint) error {
	// Iniciar uma transação
	tx := s.db.Begin()
	if tx.Error != nil {
		return fmt.Errorf("erro ao iniciar transação: %w", tx.Error)
	}

	// Garantir que a transação seja revertida em caso de erro
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Buscar a ordem
	var order models.MaintenanceOrder
	if err := tx.First(&order, orderID).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("erro ao buscar ordem: %w", err)
	}

	// Buscar o prestador
	var provider models.ServiceProvider
	if err := tx.First(&provider, providerID).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("erro ao buscar prestador: %w", err)
	}

	// Atualizar o campo service_provider_id da ordem
	if err := tx.Model(&order).Update("service_provider_id", providerID).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("erro ao atualizar service_provider_id: %w", err)
	}

	// Buscar todos os técnicos associados ao prestador
	var technicians []models.User
	if err := tx.Where("service_provider_id = ?", providerID).Find(&technicians).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("erro ao buscar técnicos do prestador: %w", err)
	}

	// Atribuir a ordem a todos os técnicos do prestador
	for _, technician := range technicians {
		notes := fmt.Sprintf("Atribuição automática via prestador ID %d por usuário ID %d", providerID, assignedByID)
		if err := s.technicianOrderRepo.Create(technician.ID, orderID, assignedByID, notes); err != nil {
			// Apenas logar o erro, não impedir a atribuição
			log.Printf("Erro ao criar registro na tabela technician_orders para técnico %d: %s", technician.ID, err.Error())
		}
	}

	// Confirmar a transação
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("erro ao confirmar transação: %w", err)
	}

	// Enviar notificação ao prestador
	if err := s.notificationService.NotifyProviderOrderAssigned(orderID, providerID); err != nil {
		// Apenas logar o erro, não impedir a atribuição
		log.Printf("Erro ao enviar notificação para o prestador %d: %s", providerID, err.Error())
	}

	log.Printf("Ordem %d atribuída com sucesso ao prestador %d", orderID, providerID)
	return nil
}

// UnassignOrderFromTechnician remove a atribuição de uma ordem a um técnico
func (s *OrderAssignmentService) UnassignOrderFromTechnician(orderID, technicianID uint) error {
	// Iniciar uma transação
	tx := s.db.Begin()
	if tx.Error != nil {
		return fmt.Errorf("erro ao iniciar transação: %w", tx.Error)
	}

	// Garantir que a transação seja revertida em caso de erro
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Buscar a ordem
	var order models.MaintenanceOrder
	if err := tx.First(&order, orderID).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("erro ao buscar ordem: %w", err)
	}

	// Verificar se o técnico está atribuído à ordem
	if order.TechnicianID == nil || *order.TechnicianID != technicianID {
		tx.Rollback()
		return fmt.Errorf("técnico %d não está atribuído à ordem %d", technicianID, orderID)
	}

	// Remover o técnico da ordem
	if err := tx.Model(&order).Update("technician_id", nil).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("erro ao remover technician_id: %w", err)
	}

	// Remover o registro da tabela technician_orders
	if err := s.technicianOrderRepo.Delete(technicianID, orderID); err != nil {
		tx.Rollback()
		return fmt.Errorf("erro ao remover registro da tabela technician_orders: %w", err)
	}

	// Confirmar a transação
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("erro ao confirmar transação: %w", err)
	}

	log.Printf("Atribuição da ordem %d ao técnico %d removida com sucesso", orderID, technicianID)
	return nil
}

// HasAccessToOrder verifica se um técnico tem acesso a uma ordem
// Esta função deve ser usada em vez de verificações diretas nos campos
func (s *OrderAssignmentService) HasAccessToOrder(technicianID, orderID uint) (bool, error) {
	// Verificar se existe um registro na tabela technician_orders
	exists, err := s.technicianOrderRepo.Exists(technicianID, orderID)
	if err != nil {
		return false, fmt.Errorf("erro ao verificar existência de atribuição: %w", err)
	}

	if exists {
		return true, nil
	}

	// Verificar se o técnico está atribuído diretamente à ordem (para compatibilidade)
	var order models.MaintenanceOrder
	if err := s.db.First(&order, orderID).Error; err != nil {
		return false, fmt.Errorf("erro ao buscar ordem: %w", err)
	}

	if order.TechnicianID != nil && *order.TechnicianID == technicianID {
		return true, nil
	}

	// Verificar se o técnico pertence ao prestador atribuído à ordem (para compatibilidade)
	if order.ServiceProviderID != nil && *order.ServiceProviderID > 0 {
		var technician models.User
		if err := s.db.First(&technician, technicianID).Error; err != nil {
			return false, fmt.Errorf("erro ao buscar técnico: %w", err)
		}

		if technician.ServiceProviderID != nil && *technician.ServiceProviderID == *order.ServiceProviderID {
			return true, nil
		}
	}

	return false, nil
}
