package services

import (
	"errors"
	"fmt"
	"log"
	"time"

	"golang.org/x/crypto/bcrypt"

	"tradicao/internal/middleware"
	"tradicao/internal/models"
	"tradicao/internal/repository"

	"github.com/golang-jwt/jwt/v5"
)

// AuthService gerencia autenticação e autorização
type AuthService struct {
	userRepo repository.UserRepository
}

// NewAuthService cria um novo serviço de autenticação
func NewAuthService(userRepo repository.UserRepository) *AuthService {
	return &AuthService{
		userRepo: userRepo,
	}
}

// Login autentica um usuário e retorna um token JWT
func (s *AuthService) Login(email, password string) (string, models.User, error) {
	var emptyUser models.User

	// Busca o usuário pelo email
	user, err := s.userRepo.FindByEmail(email)
	if err != nil {
		log.Printf("Erro ao buscar usuário %s: %v", email, err)
		return "", emptyUser, errors.New("credenciais inválidas")
	}

	// Verifica se a senha está correta
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password))
	if err != nil {
		log.Printf("Senha incorreta para usuário %s", email)
		return "", emptyUser, errors.New("credenciais inválidas")
	}

	// Verifica se a conta está bloqueada
	if user.Blocked {
		log.Printf("Tentativa de login em conta bloqueada: %s", email)
		return "", emptyUser, errors.New("conta bloqueada, entre em contato com o administrador")
	}

	// Gera o token JWT
	token, err := middleware.GenerateToken(user.ID, user.Email, string(user.Role))
	if err != nil {
		log.Printf("Erro ao gerar token para usuário %s: %v", email, err)
		return "", emptyUser, errors.New("erro ao gerar token de autenticação")
	}

	// Registra o login bem-sucedido
	s.userRepo.Update(user)

	// Limpa a senha antes de retornar o usuário
	user.Password = ""

	return token, user, nil
}

// Register registra um novo usuário
func (s *AuthService) Register(user models.User, password string) (models.User, error) {
	// Verifica se o email já está em uso
	existingUser, err := s.userRepo.FindByEmail(user.Email)
	if err == nil && existingUser.ID != 0 {
		return models.User{}, errors.New("email já está em uso")
	}

	// Criptografa a senha
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		log.Printf("Erro ao criptografar senha: %v", err)
		return models.User{}, errors.New("erro ao processar senha")
	}

	// Define valores padrão
	user.Password = string(hashedPassword)
	user.CreatedAt = time.Now()
	user.UpdatedAt = time.Now()

	// Se não houver role definida, define como "user"
	if user.Role == "" {
		user.Role = "user"
	}

	// Salva o usuário
	newUser, err := s.userRepo.Create(user)
	if err != nil {
		log.Printf("Erro ao criar usuário: %v", err)
		return models.User{}, errors.New("erro ao criar usuário")
	}

	// Limpa a senha antes de retornar
	newUser.Password = ""
	return newUser, nil
}

// RequestPasswordReset solicita um reset de senha
func (s *AuthService) RequestPasswordReset(email string) (string, error) {
	// Busca o usuário pelo email
	user, err := s.userRepo.FindByEmail(email)
	if err != nil {
		// Não revelamos se o email existe ou não para evitar enumeração
		log.Printf("Solicitação de reset para email não cadastrado: %s", email)
		return "", nil
	}

	// Gera token de reset com validade de 1 hora
	expirationTime := time.Now().Add(1 * time.Hour)
	claims := &jwt.RegisteredClaims{
		Subject:   user.Email,
		ExpiresAt: jwt.NewNumericDate(expirationTime),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(middleware.JWTSecret)
	if err != nil {
		log.Printf("Erro ao gerar token de reset para %s: %v", email, err)
		return "", errors.New("erro ao gerar token de reset")
	}

	// Aqui seria implementado o envio do email com o link de reset
	// Esta implementação depende do serviço de email que você usar

	return tokenString, nil
}

// ResetPassword altera a senha de um usuário usando token de reset
func (s *AuthService) ResetPassword(token, newPassword string) error {
	// Valida o token
	claims := &jwt.RegisteredClaims{}
	parsedToken, err := jwt.ParseWithClaims(token, claims, func(token *jwt.Token) (interface{}, error) {
		return middleware.JWTSecret, nil
	})

	if err != nil || !parsedToken.Valid {
		log.Printf("Token de reset inválido: %v", err)
		return errors.New("token inválido ou expirado")
	}

	// Busca o usuário pelo email no token
	email := claims.Subject
	user, err := s.userRepo.FindByEmail(email)
	if err != nil {
		log.Printf("Usuário não encontrado para reset de senha: %s", email)
		return errors.New("usuário não encontrado")
	}

	// Criptografa a nova senha
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		log.Printf("Erro ao criptografar nova senha: %v", err)
		return errors.New("erro ao processar nova senha")
	}

	// Atualiza a senha
	user.Password = string(hashedPassword)
	user.UpdatedAt = time.Now()
	_, err = s.userRepo.Update(user)
	if err != nil {
		log.Printf("Erro ao atualizar senha: %v", err)
		return errors.New("erro ao atualizar senha")
	}

	return nil
}

// ChangePassword altera a senha de um usuário autenticado
func (s *AuthService) ChangePassword(userID uint, currentPassword, newPassword string) error {
	// Busca o usuário
	user, err := s.userRepo.FindByID(userID)
	if err != nil {
		return errors.New("usuário não encontrado")
	}

	// Verifica a senha atual
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(currentPassword))
	if err != nil {
		log.Printf("Senha atual incorreta para usuário ID %d", userID)
		return errors.New("senha atual incorreta")
	}

	// Criptografa a nova senha
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		log.Printf("Erro ao criptografar nova senha: %v", err)
		return errors.New("erro ao processar nova senha")
	}

	// Atualiza a senha
	user.Password = string(hashedPassword)
	user.UpdatedAt = time.Now()
	_, err = s.userRepo.Update(user)
	if err != nil {
		log.Printf("Erro ao atualizar senha: %v", err)
		return errors.New("erro ao atualizar senha")
	}

	return nil
}

// ValidateToken valida um token JWT
func (s *AuthService) ValidateToken(tokenString string) (*middleware.Claims, error) {
	claims := &middleware.Claims{}

	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		return middleware.JWTSecret, nil
	})

	if err != nil || !token.Valid {
		return nil, errors.New("token inválido ou expirado")
	}

	return claims, nil
}

// --- Métodos para Status de Senha e 2FA (Placeholders) ---

// GetPasswordStatus verifica o status da senha do usuário (expirada, etc.)
// Placeholder: Implementação futura depende da reativação da lógica de expiração
func (s *AuthService) GetPasswordStatus(userID uint) (string, error) {
	log.Printf("AVISO: GetPasswordStatus chamado para userID %d, mas a funcionalidade está desativada.", userID)
	// Aqui verificaríamos user.ForcePasswordChange ou chamaríamos passwordPolicyService.CheckPasswordExpiry
	return "ok", nil // Retorna "ok" temporariamente
}

// SetupTOTP inicia a configuração do 2FA para um usuário
// Placeholder: Implementação real gerará segredo e URL
func (s *AuthService) SetupTOTP(userID uint) (secret string, qrCodeURL string, err error) {
	log.Printf("AVISO: SetupTOTP chamado para userID %d, placeholder ativo.", userID)
	// TODO: Implementar geração de segredo TOTP (ex: pquerna/otp)
	// TODO: Gerar URL otpauth://
	// TODO: Armazenar segredo temporariamente ou associado ao usuário com status 'pending'
	secret = "PLACEHOLDER_SECRET"
	qrCodeURL = "otpauth://totp/PlaceholderApp:<EMAIL>?secret=" + secret + "&issuer=PlaceholderApp"
	// Exemplo: err = s.userRepo.SetTOTPSecret(userID, secret) // Salva o segredo
	return secret, qrCodeURL, nil
}

// VerifyAndEnableTOTP verifica um código TOTP e ativa o 2FA
// Placeholder: Implementação real validará o código
func (s *AuthService) VerifyAndEnableTOTP(userID uint, code string) error {
	log.Printf("AVISO: VerifyAndEnableTOTP chamado para userID %d com código %s, placeholder ativo.", userID, code)
	// TODO: Obter segredo TOTP do usuário (do banco)
	// TODO: Validar o código TOTP (ex: pquerna/otp.Validate)
	// if !valid {
	// 	 return errors.New("código TOTP inválido")
	// }
	// TODO: Chamar s.userRepo.EnableTOTP(userID, secret) para ativar
	log.Printf("Simulando ativação de TOTP para userID %d", userID)
	return nil
}

// DisableTOTP desativa o 2FA para um usuário
// Placeholder: Implementação real chamará o repositório
func (s *AuthService) DisableTOTP(userID uint) error {
	log.Printf("AVISO: DisableTOTP chamado para userID %d, placeholder ativo.", userID)
	// TODO: Chamar s.userRepo.DisableTOTP(userID)
	log.Printf("Simulando desativação de TOTP para userID %d", userID)
	return nil
}

// GetQRCodeData retorna os dados necessários para gerar um QR Code para o setup TOTP
// (Alternativa/Complemento a SetupTOTP retornar a URL diretamente)
// Placeholder: Implementação real buscaria/geraria os dados
func (s *AuthService) GetQRCodeData(userID uint) (qrData string, err error) {
	log.Printf("AVISO: GetQRCodeData chamado para userID %d, placeholder ativo.", userID)
	// TODO: Obter segredo TOTP do usuário (pode ser o mesmo passo do SetupTOTP)
	// TODO: Formatar a string otpauth:// para o QR Code
	secret := "PLACEHOLDER_SECRET_FOR_QR"
	email := "<EMAIL>" // Buscar email real do usuário
	issuer := "SuaApp"
	qrData = fmt.Sprintf("otpauth://totp/%s:%s?secret=%s&issuer=%s", issuer, email, secret, issuer)
	return qrData, nil
}
