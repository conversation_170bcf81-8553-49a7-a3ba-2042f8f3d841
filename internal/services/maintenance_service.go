package services

import (
	"errors"
	"time"
	"tradicao/internal/models"
	"tradicao/internal/repository"
)

// MaintenanceService implementa a lógica de negócios para ordens de manutenção
type MaintenanceService struct {
	repo                repository.MaintenanceRepository
	userRepo            repository.UserRepository
	notificationService *NotificationService
}

// validateOrder valida os dados de uma ordem de manutenção
func (s *MaintenanceService) validateOrder(order *models.MaintenanceOrder) error {
	if order == nil {
		return errors.New("ordem não pode ser nula")
	}

	if order.BranchID == 0 {
		return errors.New("filial é obrigatória")
	}

	if order.Description == "" {
		return errors.New("descrição é obrigatória")
	}

	if order.Status == "" {
		order.Status = models.StatusPending
	}

	return nil
}

// NewMaintenanceService cria uma nova instância do serviço
func NewMaintenanceService(
	repo repository.MaintenanceRepository,
	userRepo repository.UserRepository,
	notificationService *NotificationService,
) *MaintenanceService {
	return &MaintenanceService{
		repo:                repo,
		userRepo:            userRepo,
		notificationService: notificationService,
	}
}

// CreateOrder cria uma nova ordem
func (s *MaintenanceService) CreateOrder(order *models.MaintenanceOrder) error {
	// Validar a ordem
	if err := s.validateOrder(order); err != nil {
		return err
	}

	// Criar a ordem
	if err := s.repo.Create(order); err != nil {
		return err
	}

	// Remover notificação WhatsApp - será implementada com API oficial no futuro
	return nil
}

// GetByID busca uma ordem pelo ID
func (s *MaintenanceService) GetByID(id uint) (*models.MaintenanceOrder, error) {
	return s.repo.GetByID(id)
}

// Update atualiza uma ordem existente
func (s *MaintenanceService) Update(order *models.MaintenanceOrder) error {
	if order.ID == 0 {
		return errors.New("ID não especificado")
	}

	// Verificar se a ordem existe
	existing, err := s.repo.GetByID(order.ID)
	if err != nil {
		return err
	}

	// Preservar campos que não devem ser alterados
	order.CreatedAt = existing.CreatedAt
	order.UpdatedAt = time.Now()

	return s.repo.Update(order)
}

// Delete remove uma ordem
func (s *MaintenanceService) Delete(id uint) error {
	return s.repo.Delete(id)
}

// GetAll retorna todas as ordens com filtros
func (s *MaintenanceService) GetAll(filters *models.MaintenanceOrderFilters) ([]models.MaintenanceOrder, int, error) {
	return s.repo.GetAll(filters)
}

// UpdateStatus atualiza o status de uma ordem
func (s *MaintenanceService) UpdateStatus(id uint, status string, userID uint) error {
	// Validar status
	if !isValidStatus(status) {
		return errors.New("status inválido")
	}

	return s.repo.UpdateStatus(id, status, userID)
}

// UpdateApprovalStatus atualiza o status de aprovação
func (s *MaintenanceService) UpdateApprovalStatus(id uint, approved bool, approvedBy uint, notes string) error {
	return s.repo.UpdateApprovalStatus(id, approved, approvedBy, notes)
}

// UpdatePaymentStatus atualiza o status de pagamento
func (s *MaintenanceService) UpdatePaymentStatus(id uint, paid bool, paidBy uint, notes string) error {
	return s.repo.UpdatePaymentStatus(id, paid, paidBy, notes)
}

// AddNoteOld adiciona uma nota à ordem (implementação antiga)
func (s *MaintenanceService) AddNoteOld(orderID uint, userID uint, content string) error {
	if content == "" {
		return errors.New("conteúdo da nota não pode estar vazio")
	}
	return s.repo.AddNote(orderID, userID, content)
}

// AddNote adiciona uma nota à ordem (implementação nova para compatibilidade com a interface)
func (s *MaintenanceService) AddNote(orderID int64, content string, userID int64) error {
	if content == "" {
		return errors.New("conteúdo da nota não pode estar vazio")
	}
	return s.repo.AddNote(uint(orderID), uint(userID), content)
}

// AddMaterial adiciona um material à ordem (implementação antiga)
func (s *MaintenanceService) AddMaterialOld(orderID uint, material *models.Material) error {
	if material.Name == "" || material.Quantity <= 0 {
		return errors.New("nome e quantidade do material são obrigatórios")
	}
	return s.repo.AddMaterial(orderID, material)
}

// AddMaterial adiciona um material à ordem (implementação nova para compatibilidade com a interface)
func (s *MaintenanceService) AddMaterial(orderID int64, material models.MaterialRequest, userID int64) error {
	// Converter MaterialRequest para Material
	newMaterial := &models.Material{
		Name:               material.Name,
		Quantity:           material.Quantity,
		UnitCost:           material.UnitCost,
		MaintenanceOrderID: uint(orderID),
		AddedByUserID:      uint(userID),
	}

	// Validar
	if newMaterial.Name == "" || newMaterial.Quantity <= 0 {
		return errors.New("nome e quantidade do material são obrigatórios")
	}

	// Chamar o repositório
	return s.repo.AddMaterial(uint(orderID), newMaterial)
}

// GetMetrics retorna métricas das ordens
func (s *MaintenanceService) GetMetrics() (*models.MaintenanceMetrics, error) {
	return s.repo.GetMetrics()
}

// GetExtendedMetrics retorna métricas detalhadas
func (s *MaintenanceService) GetExtendedMetrics() (*models.MaintenanceMetrics, error) {
	return s.repo.GetExtendedMetrics()
}

// isValidStatus verifica se o status é válido
// Usa a função IsOrderStatusInSet para verificar se o status está no conjunto de status válidos
// Esta implementação suporta tanto os valores padronizados quanto os valores legados
func isValidStatus(status string) bool {
	return models.IsOrderStatusInSet(status,
		models.StatusPending,
		models.StatusScheduled,
		models.StatusInProgress,
		models.StatusCompleted,
		models.StatusCancelled, // Nota: Usamos StatusCancelled em vez de StatusCanceled
		models.StatusVerified,
		models.StatusRejected,
		models.StatusApproved,
		models.StatusConfirmed,
	)
}

// NotifyTechnicianAssigned notifica um técnico quando ele é designado para uma ordem
func (s *MaintenanceService) NotifyTechnicianAssigned(orderID uint, technicianID uint) error {
	// Implementação será substituída pela API oficial de WhatsApp no futuro
	return nil
}

// NotifyStatusChange notifica as partes interessadas sobre mudança de status
func (s *MaintenanceService) NotifyStatusChange(orderID uint, newStatus string, userID uint) error {
	// Implementação será substituída pela API oficial de WhatsApp no futuro
	return nil
}

// NotifyOrderApproval notifica sobre aprovação/rejeição da ordem
func (s *MaintenanceService) NotifyOrderApproval(orderID uint, approved bool, approverID uint) error {
	// Implementação será substituída pela API oficial de WhatsApp no futuro
	return nil
}

// NotifyMaterialAdded notifica sobre adição de material
func (s *MaintenanceService) NotifyMaterialAdded(orderID uint, materialID uint, addedByID uint) error {
	// Implementação será substituída pela API oficial de WhatsApp no futuro
	return nil
}

// NotifyNoteAdded notifica sobre adição de nota/comentário
func (s *MaintenanceService) NotifyNoteAdded(orderID uint, noteID uint, addedByID uint) error {
	// Implementação será substituída pela API oficial de WhatsApp no futuro
	return nil
}
