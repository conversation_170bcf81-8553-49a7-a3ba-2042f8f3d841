Manual de Boas Práticas para o Sistema de Manutenção Shell Tradição
1. Estrutura e Arquitetura do Projeto
O que fazer:
Usar a estrutura modular organizada por funcionalidades (maintenance, dashboard, reports, etc.)
Manter o padrão de nomenclatura consistente (português para interfaces de usuário, inglês para código)
Seguir o padrão de roteamento com registerControlPanelRoute ou registerSideMenuRoute para novas páginas
O que evitar:
Não criar rotas manualmente sem usar as funções de registro existentes
Evitar misturar idiomas em uma única interface (manter coerência)
Não duplicar templates que podem ser reaproveitados
2. Templates e Renderização
O que fazer:
Para conteúdo estático ou muito complexo, incorpore diretamente o HTML na rota (c.HTML()) com template.HTML()
Use gin.H{} para passar dados para os templates
Mantenha os arquivos CSS e JS em pastas dedicadas (/static/css, /static/js)
O que evitar:
Evitar usar renderTemplate() para conteúdo complexo com muitos caracteres especiais
Não usar template strings do JavaScript (${var}) em strings literais de Go
Evitar modificar diretamente os arquivos base (base_controlpanel.html, base_sidemenu.html)
3. Erros Comuns e Soluções
Problema: Caracteres de Escape em Strings JavaScript
Solução: Use concatenação de strings simples (+) em vez de template strings (${...}) quando estiver incluindo JavaScript em strings Go.

Problema: Template "Undefined"
Solução: Em vez de tentar renderizar um template separado e incluí-lo, incorpore diretamente o HTML no handler da rota.

Problema: Layout com Menu Lateral Quebrado
Solução: Não inclua container-fluid extras no template, já que o base_controlpanel.html já possui essa estrutura.

4. Estilo Visual e Identidade
O que fazer:
Seguir as cores da Shell: vermelho (#ED1C24), amarelo (#f4b31d)
Usar os componentes visuais temáticos: pump-border, fuel-gauge, shell-btn
Implementar efeitos visuais relacionados a bombas de combustível e postos Shell
O que evitar:
Não alterar o esquema de cores corporativas
Evitar remover a identidade Shell (logotipos, cores, elementos temáticos)
5. JavaScript e Interatividade
O que fazer:
Carregue scripts no final da página para melhor performance
Use document.addEventListener('DOMContentLoaded', function() {...}) para inicializar scripts
Mantenha funcionalidades em módulos JavaScript separados
O que evitar:
Evitar código JavaScript inline quando possível
Não misturar jQuery com JavaScript puro em um mesmo módulo
Evitar usar template strings JS complexas em strings Go
6. Backend e API
O que fazer:
Usar a estrutura de APIs RESTful /api/resource para novas funcionalidades
Implementar manipuladores de dados no lado do servidor em Go
Manter a autenticação consistente em todas as rotas protegidas
O que evitar:
Não acessar o banco de dados diretamente pelo frontend
Evitar duplicar lógica de negócio entre rotas
Não misturar diferentes mecanismos de autenticação
7. Controle de Acesso e Roles
O que fazer:
Seguir o modelo de roles (admin, gerente, financeiro, tecnico, filial, prestadores, funcionario)
Implementar verificação de permissões baseada em roles
Mostrar apenas os itens de menu permitidos para cada role
O que evitar:
Não deixar funcionalidades sensíveis disponíveis a todos os usuários
Evitar hard-coding de permissões em múltiplos lugares
8. Considerações Específicas da Linguagem Go
O que fazer:
Usar gin.H{} para criar mapas de dados
Inicializar rotas e grupos com nomes claros (api, protected, router)
Passar interfaces como template.HTML quando quiser incluir HTML diretamente
O que evitar:
Não usar aspas simples para strings em Go (apenas aspas duplas)
Evitar caracteres de escape desnecessários em strings Go
Não usar sintaxe de template ou expressões de outras linguagens em strings Go
9. Integrações e Funcionalidades Específicas
Calendário:
Usar o objeto calendar com seus métodos de inicialização
Implementar o carregamento de dados reais de eventos via API
Manter a estética temática Shell com cores e bordas apropriadas
Relatórios:
Seguir o padrão de geração de relatórios via API
Implementar opções de download em diferentes formatos
Manter a estrutura de filtros consistente