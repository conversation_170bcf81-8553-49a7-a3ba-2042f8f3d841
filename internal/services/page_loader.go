package services

import (
	"bufio"
	"log"
	"os"
	"strings"
)

// Estrutura para armazenar informações de uma página
type PageInfo struct {
	Path  string   // Caminho da rota
	Roles []string // Perfis que podem acessar
}

// Carrega informações de páginas do arquivo markdown
func LoadPagesFromFile(filePath string) ([]PageInfo, error) {
	log.Printf("Carregando páginas do arquivo: %s", filePath)

	file, err := os.Open(filePath)
	if err != nil {
		log.Printf("Erro ao abrir arquivo de páginas: %v", err)
		return nil, err
	}
	defer file.Close()

	var pages []PageInfo
	scanner := bufio.NewScanner(file)
	inTable := false

	for scanner.Scan() {
		line := scanner.Text()

		// Verificar se estamos na seção da tabela
		if strings.Contains(line, "| Página | Perfis com acesso |") {
			inTable = true
			continue
		}

		if inTable && strings.HasPrefix(line, "|") && !strings.HasPrefix(line, "|---") {
			parts := strings.Split(line, "|")
			if len(parts) >= 3 {
				pagePath := strings.TrimSpace(parts[1])
				rolesStr := strings.TrimSpace(parts[2])

				// Remover as aspas e barras do nome da página
				pagePath = strings.Trim(pagePath, "`/")

				// Dividir as roles em uma lista
				rolesRaw := strings.Split(rolesStr, ",")
				roles := make([]string, 0)

				for _, role := range rolesRaw {
					roleStr := strings.TrimSpace(role)
					if roleStr != "" {
						roles = append(roles, roleStr)
					}
				}

				// Adicionar a página à lista
				pages = append(pages, PageInfo{
					Path:  pagePath,
					Roles: roles,
				})

				log.Printf("Página carregada: '%s' acessível por: %v", pagePath, roles)
			}
		}

		// Sair do processamento da tabela quando encontrar uma linha em branco
		if inTable && strings.TrimSpace(line) == "" {
			inTable = false
		}
	}

	if err := scanner.Err(); err != nil {
		log.Printf("Erro ao ler arquivo de páginas: %v", err)
		return nil, err
	}

	log.Printf("Carregamento de páginas concluído. %d páginas configuradas.", len(pages))
	return pages, nil
}
