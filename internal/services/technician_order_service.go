package services

import (
	"log"
	"tradicao/internal/models"
)

// TechnicianOrderService é o serviço para gerenciar relacionamentos entre técnicos e ordens
type TechnicianOrderService struct {
	repo models.TechnicianOrderRepository
}

// NewTechnicianOrderService cria uma nova instância do serviço
func NewTechnicianOrderService(repo models.TechnicianOrderRepository) *TechnicianOrderService {
	return &TechnicianOrderService{repo: repo}
}

// AssignOrderToTechnician atribui uma ordem a um técnico
func (s *TechnicianOrderService) AssignOrderToTechnician(technicianID, orderID, createdBy uint, notes string) error {
	log.Printf("Atribuindo ordem %d ao técnico %d", orderID, technicianID)
	return s.repo.Create(technicianID, orderID, createdBy, notes)
}

// UnassignOrderFromTechnician remove uma ordem de um técnico
func (s *TechnicianOrderService) UnassignOrderFromTechnician(technicianID, orderID uint) error {
	log.Printf("Removendo ordem %d do técnico %d", orderID, technicianID)
	return s.repo.Delete(technicianID, orderID)
}

// GetOrdersByTechnician retorna todas as ordens associadas a um técnico
func (s *TechnicianOrderService) GetOrdersByTechnician(technicianID uint) ([]models.TechnicianOrder, error) {
	return s.repo.GetByTechnician(technicianID)
}

// GetTechniciansByOrder retorna todos os técnicos associados a uma ordem
func (s *TechnicianOrderService) GetTechniciansByOrder(orderID uint) ([]models.TechnicianOrder, error) {
	return s.repo.GetByOrder(orderID)
}

// HasAccessToOrder verifica se um técnico tem acesso a uma ordem
func (s *TechnicianOrderService) HasAccessToOrder(technicianID, orderID uint) (bool, error) {
	return s.repo.Exists(technicianID, orderID)
}

// GetAllAssignments retorna todos os relacionamentos entre técnicos e ordens
func (s *TechnicianOrderService) GetAllAssignments() ([]models.TechnicianOrder, error) {
	return s.repo.GetAll()
}

// BatchAssignOrders atribui várias ordens a um técnico
func (s *TechnicianOrderService) BatchAssignOrders(technicianID uint, orderIDs []uint, createdBy uint, notes string) (int, error) {
	successCount := 0

	for _, orderID := range orderIDs {
		err := s.AssignOrderToTechnician(technicianID, orderID, createdBy, notes)
		if err != nil {
			log.Printf("Erro ao atribuir ordem %d ao técnico %d: %v", orderID, technicianID, err)
			continue
		}
		successCount++
	}

	return successCount, nil
}

// BatchAssignTechnicians atribui vários técnicos a uma ordem
func (s *TechnicianOrderService) BatchAssignTechnicians(technicianIDs []uint, orderID uint, createdBy uint, notes string) (int, error) {
	successCount := 0

	for _, technicianID := range technicianIDs {
		err := s.AssignOrderToTechnician(technicianID, orderID, createdBy, notes)
		if err != nil {
			log.Printf("Erro ao atribuir técnico %d à ordem %d: %v", technicianID, orderID, err)
			continue
		}
		successCount++
	}

	return successCount, nil
}
