package services

import (
	"context"
	"errors"
	"time"

	"tradicao/internal/models"
	"tradicao/internal/repository"
)

// FinanceService define a interface para operações financeiras
type FinanceService interface {
	// Dashboard
	GetDashboardData(ctx context.Context) (*models.DashboardData, error)

	// Pagamentos
	ListPayments(ctx context.Context, filters *models.PaymentFilters) ([]*models.Payment, error)
	GetPayment(ctx context.Context, id int64) (*models.Payment, error)
	CreatePayment(ctx context.Context, payment *models.Payment) error
	UpdatePayment(ctx context.Context, payment *models.Payment) error
	DeletePayment(ctx context.Context, id int64) error

	// Notas Fiscais
	ListInvoices(ctx context.Context, filters *models.InvoiceFilters) ([]*models.Invoice, error)
	GetInvoice(ctx context.Context, id int64) (*models.Invoice, error)
	CreateInvoice(ctx context.Context, invoice *models.Invoice) error
	UpdateInvoice(ctx context.Context, invoice *models.Invoice) error
	DeleteInvoice(ctx context.Context, id int64) error

	// Orçamentos
	ListBudgets(ctx context.Context, filters *models.BudgetFilters) ([]*models.Budget, error)
	GetBudget(ctx context.Context, id int64) (*models.Budget, error)
	CreateBudget(ctx context.Context, budget *models.Budget) error
	UpdateBudget(ctx context.Context, budget *models.Budget) error
	DeleteBudget(ctx context.Context, id int64) error

	// Relatórios
	ListReports(ctx context.Context, filters *models.ReportFilters) ([]*models.Report, error)
	GetReport(ctx context.Context, id int64) (*models.Report, error)
	CreateReport(ctx context.Context, report *models.Report) error
	DeleteReport(ctx context.Context, id int64) error

	// Cotações
	ListQuotes(ctx context.Context) ([]*models.Quote, error)
	GetQuote(ctx context.Context, symbol string) (*models.Quote, error)
	RefreshQuotes(ctx context.Context) error

	// Notícias
	ListNews(ctx context.Context) ([]*models.News, error)
	GetNews(ctx context.Context, id int64) (*models.News, error)

	// Configurações
	GetSettings(ctx context.Context) (*models.FinanceSettings, error)
	UpdateSettings(ctx context.Context, settings *models.FinanceSettings) error
}

// FinanceService gerencia a lógica de negócios da área financeira
type FinanceServiceImpl struct {
	repo repository.FinanceRepository
}

// NewFinanceService cria uma nova instância do FinanceService
func NewFinanceService(repo repository.FinanceRepository) *FinanceServiceImpl {
	return &FinanceServiceImpl{
		repo: repo,
	}
}

// GetDashboardData retorna os dados necessários para o painel financeiro
func (s *FinanceServiceImpl) GetDashboardData(ctx context.Context) (*models.DashboardData, error) {
	// Obtém o período atual (mês atual)
	now := time.Now()
	startDate := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	endDate := startDate.AddDate(0, 1, 0).Add(-time.Second)

	// Obtém os dados financeiros do período
	payments, err := s.repo.ListPayments(ctx, &models.PaymentFilters{
		StartDate: startDate,
		EndDate:   endDate,
	})
	if err != nil {
		return nil, err
	}

	invoices, err := s.repo.ListInvoices(ctx, &models.InvoiceFilters{
		StartDate: startDate,
		EndDate:   endDate,
	})
	if err != nil {
		return nil, err
	}

	// Calcula os totais
	var totalRevenue, totalExpenses float64
	for _, invoice := range invoices {
		if invoice.Status == "PAID" {
			totalRevenue += invoice.Amount
		}
	}

	for _, payment := range payments {
		if payment.Status == "PAID" {
			totalExpenses += payment.Amount
		}
	}

	// Obtém as cotações mais recentes
	quotes, err := s.repo.ListQuotes(ctx)
	if err != nil {
		return nil, err
	}

	// Obtém as notícias financeiras mais recentes
	news, err := s.repo.ListNews(ctx)
	if err != nil {
		return nil, err
	}

	return &models.DashboardData{
		TotalRevenue:  totalRevenue,
		TotalExpenses: totalExpenses,
		Profit:        totalRevenue - totalExpenses,
		Balance:       totalRevenue - totalExpenses,
		Quotes:        quotes,
		News:          news,
	}, nil
}

// ListPayments lista os pagamentos com base nos filtros fornecidos
func (s *FinanceServiceImpl) ListPayments(ctx context.Context, filters *models.PaymentFilters) ([]*models.Payment, error) {
	return s.repo.ListPayments(ctx, filters)
}

// CreatePayment cria um novo pagamento
func (s *FinanceServiceImpl) CreatePayment(ctx context.Context, payment *models.Payment) error {
	if payment.Amount <= 0 {
		return errors.New("valor do pagamento deve ser maior que zero")
	}

	if payment.DueDate.Before(time.Now()) {
		return errors.New("data de vencimento não pode ser no passado")
	}

	return s.repo.CreatePayment(ctx, payment)
}

// GetPayment obtém um pagamento específico
func (s *FinanceServiceImpl) GetPayment(ctx context.Context, id int64) (*models.Payment, error) {
	return s.repo.GetPayment(ctx, id)
}

// UpdatePayment atualiza um pagamento existente
func (s *FinanceServiceImpl) UpdatePayment(ctx context.Context, payment *models.Payment) error {
	if payment.Amount <= 0 {
		return errors.New("valor do pagamento deve ser maior que zero")
	}

	if payment.DueDate.Before(time.Now()) {
		return errors.New("data de vencimento não pode ser no passado")
	}

	return s.repo.UpdatePayment(ctx, payment)
}

// DeletePayment remove um pagamento
func (s *FinanceServiceImpl) DeletePayment(ctx context.Context, id int64) error {
	return s.repo.DeletePayment(ctx, id)
}

// ListInvoices lista as notas fiscais com base nos filtros fornecidos
func (s *FinanceServiceImpl) ListInvoices(ctx context.Context, filters *models.InvoiceFilters) ([]*models.Invoice, error) {
	return s.repo.ListInvoices(ctx, filters)
}

// CreateInvoice cria uma nova nota fiscal
func (s *FinanceServiceImpl) CreateInvoice(ctx context.Context, invoice *models.Invoice) error {
	if invoice.Amount <= 0 {
		return errors.New("valor da nota fiscal deve ser maior que zero")
	}

	if invoice.DueDate.Before(time.Now()) {
		return errors.New("data de vencimento não pode ser no passado")
	}

	return s.repo.CreateInvoice(ctx, invoice)
}

// GetInvoice obtém uma nota fiscal específica
func (s *FinanceServiceImpl) GetInvoice(ctx context.Context, id int64) (*models.Invoice, error) {
	return s.repo.GetInvoice(ctx, id)
}

// UpdateInvoice atualiza uma nota fiscal existente
func (s *FinanceServiceImpl) UpdateInvoice(ctx context.Context, invoice *models.Invoice) error {
	if invoice.Amount <= 0 {
		return errors.New("valor da nota fiscal deve ser maior que zero")
	}

	if invoice.DueDate.Before(time.Now()) {
		return errors.New("data de vencimento não pode ser no passado")
	}

	return s.repo.UpdateInvoice(ctx, invoice)
}

// DeleteInvoice remove uma nota fiscal
func (s *FinanceServiceImpl) DeleteInvoice(ctx context.Context, id int64) error {
	return s.repo.DeleteInvoice(ctx, id)
}

// ListBudgets lista os orçamentos com base nos filtros fornecidos
func (s *FinanceServiceImpl) ListBudgets(ctx context.Context, filters *models.BudgetFilters) ([]*models.Budget, error) {
	return s.repo.ListBudgets(ctx, filters)
}

// CreateBudget cria um novo orçamento
func (s *FinanceServiceImpl) CreateBudget(ctx context.Context, budget *models.Budget) error {
	if budget.Amount <= 0 {
		return errors.New("valor do orçamento deve ser maior que zero")
	}

	return s.repo.CreateBudget(ctx, budget)
}

// GetBudget obtém um orçamento específico
func (s *FinanceServiceImpl) GetBudget(ctx context.Context, id int64) (*models.Budget, error) {
	return s.repo.GetBudget(ctx, id)
}

// UpdateBudget atualiza um orçamento existente
func (s *FinanceServiceImpl) UpdateBudget(ctx context.Context, budget *models.Budget) error {
	if budget.Amount <= 0 {
		return errors.New("valor do orçamento deve ser maior que zero")
	}

	return s.repo.UpdateBudget(ctx, budget)
}

// DeleteBudget remove um orçamento
func (s *FinanceServiceImpl) DeleteBudget(ctx context.Context, id int64) error {
	return s.repo.DeleteBudget(ctx, id)
}

// ListReports lista os relatórios com base nos filtros fornecidos
func (s *FinanceServiceImpl) ListReports(ctx context.Context, filters *models.ReportFilters) ([]*models.Report, error) {
	return s.repo.ListReports(ctx, filters)
}

// CreateReport cria um novo relatório
func (s *FinanceServiceImpl) CreateReport(ctx context.Context, report *models.Report) error {
	if report.Type == "" {
		return errors.New("tipo do relatório é obrigatório")
	}

	return s.repo.CreateReport(ctx, report)
}

// GetReport obtém um relatório específico
func (s *FinanceServiceImpl) GetReport(ctx context.Context, id int64) (*models.Report, error) {
	return s.repo.GetReport(ctx, id)
}

// DeleteReport remove um relatório
func (s *FinanceServiceImpl) DeleteReport(ctx context.Context, id int64) error {
	return s.repo.DeleteReport(ctx, id)
}

// ListQuotes lista as cotações
func (s *FinanceServiceImpl) ListQuotes(ctx context.Context) ([]*models.Quote, error) {
	return s.repo.ListQuotes(ctx)
}

// GetQuote obtém uma cotação específica
func (s *FinanceServiceImpl) GetQuote(ctx context.Context, symbol string) (*models.Quote, error) {
	return s.repo.GetQuote(ctx, symbol)
}

// RefreshQuotes atualiza as cotações
func (s *FinanceServiceImpl) RefreshQuotes(ctx context.Context) error {
	return s.repo.RefreshQuotes(ctx)
}

// ListNews lista as notícias financeiras
func (s *FinanceServiceImpl) ListNews(ctx context.Context) ([]*models.News, error) {
	return s.repo.ListNews(ctx)
}

// GetNews obtém uma notícia específica
func (s *FinanceServiceImpl) GetNews(ctx context.Context, id int64) (*models.News, error) {
	return s.repo.GetNews(ctx, id)
}

// GetSettings obtém as configurações financeiras
func (s *FinanceServiceImpl) GetSettings(ctx context.Context) (*models.FinanceSettings, error) {
	return s.repo.GetSettings(ctx)
}

// UpdateSettings atualiza as configurações financeiras
func (s *FinanceServiceImpl) UpdateSettings(ctx context.Context, settings *models.FinanceSettings) error {
	return s.repo.UpdateSettings(ctx, settings)
}
