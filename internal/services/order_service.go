package services

import (
	"fmt"
	"log"
	"sync"
	"time"
	"tradicao/internal/models"
	"tradicao/internal/repository"
)

// OrderService representa o serviço de ordens
type OrderService struct {
	repo repository.OrderRepository
	// Cache para métricas
	metricsCache struct {
		sync.RWMutex
		lastUpdate time.Time
		data       struct {
			pendingCount    int
			inProgressCount int
			completedCount  int
		}
	}
}

// NewOrderService cria uma nova instância do serviço de ordens
func NewOrderService(repo repository.OrderRepository) *OrderService {
	service := &OrderService{
		repo: repo,
	}
	// Inicializar cache
	service.metricsCache.lastUpdate = time.Now()
	return service
}

// GetOrderDetails retorna os detalhes de uma ordem
func (s *OrderService) GetOrderDetails(orderID uint) (*models.MaintenanceOrder, *models.Branch, *models.Equipment, *models.User, *models.User, *models.User, error) {
	order, err := s.repo.GetOrderByID(orderID)
	if err != nil {
		return nil, nil, nil, nil, nil, nil, err
	}

	// Implementação temporária - retorna apenas a ordem
	return order, nil, nil, nil, nil, nil, nil
}

// GetCostsByOrderID retorna os custos de uma ordem
func (s *OrderService) GetCostsByOrderID(orderID uint) ([]models.CostItem, error) {
	// Implementação temporária
	return []models.CostItem{}, nil
}

// AddCost adiciona um custo a uma ordem
func (s *OrderService) AddCost(orderID uint, userID uint, cost models.CostItem) (*models.CostItem, error) {
	// Implementação temporária
	return &models.CostItem{}, nil
}

// UpdateStatus atualiza o status de uma ordem
func (s *OrderService) UpdateStatus(orderID uint, userID uint, newStatus models.OrderStatus, reason string) (*models.MaintenanceOrder, error) {
	// Implementação temporária
	return &models.MaintenanceOrder{}, nil
}

// AssignProvider atribui um fornecedor a uma ordem
func (s *OrderService) AssignProvider(orderID uint, userID uint, providerID uint) (*models.MaintenanceOrder, error) {
	// Implementação temporária
	return &models.MaintenanceOrder{}, nil
}

// SubmitForApproval envia uma ordem para aprovação
func (s *OrderService) SubmitForApproval(orderID uint, userID uint) (*models.MaintenanceOrder, error) {
	// Implementação temporária
	return &models.MaintenanceOrder{}, nil
}

// Approve aprova uma ordem
func (s *OrderService) Approve(orderID uint, userID uint) (*models.MaintenanceOrder, error) {
	// Implementação temporária
	return &models.MaintenanceOrder{}, nil
}

// Reject rejeita uma ordem
func (s *OrderService) Reject(orderID uint, userID uint, reason string) (*models.MaintenanceOrder, error) {
	// Implementação temporária
	return &models.MaintenanceOrder{}, nil
}

// Cancel cancela uma ordem
func (s *OrderService) Cancel(orderID uint, userID uint, reason string) (*models.MaintenanceOrder, error) {
	// Implementação temporária
	return &models.MaintenanceOrder{}, nil
}

// AddInteraction adiciona uma interação a uma ordem
func (s *OrderService) AddInteraction(orderID uint, userID uint, message string) (*models.Interaction, error) {
	// Implementação temporária
	return &models.Interaction{}, nil
}

// UploadInvoice faz upload de uma nota fiscal
func (s *OrderService) UploadInvoice(orderID uint, userID uint, invoiceData models.Invoice, attachment models.Attachment) (*models.Invoice, error) {
	// Implementação temporária
	return &models.Invoice{}, nil
}

// GetOrdersWithPagination retorna as ordens com paginação
func (s *OrderService) GetOrdersWithPagination(offset, limit int, statusStr, branchIDStr, startDateStr, endDateStr string) ([]models.MaintenanceOrder, int, error) {
	// Converter parâmetros
	var status models.OrderStatus
	if statusStr != "" {
		status = models.OrderStatus(statusStr)
	}

	var branchID uint
	if branchIDStr != "" {
		var err error
		var id int
		_, err = fmt.Sscanf(branchIDStr, "%d", &id)
		if err != nil {
			return nil, 0, fmt.Errorf("ID da filial inválido: %v", err)
		}
		branchID = uint(id)
	}

	var startDate, endDate time.Time
	var err error
	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			return nil, 0, fmt.Errorf("data inicial inválida: %v", err)
		}
	}
	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			return nil, 0, fmt.Errorf("data final inválida: %v", err)
		}
	}

	// Calcular página
	page := offset/limit + 1
	return s.repo.GetOrdersWithPagination(page, limit, status, branchID, startDate, endDate)
}

// GetOrderCountsByStatus retorna a contagem de ordens por status com cache
func (s *OrderService) GetOrderCountsByStatus() (pendingCount, inProgressCount, completedCount int) {
	// Verificar cache
	s.metricsCache.RLock()
	if time.Since(s.metricsCache.lastUpdate) < 5*time.Minute {
		defer s.metricsCache.RUnlock()
		return s.metricsCache.data.pendingCount,
			s.metricsCache.data.inProgressCount,
			s.metricsCache.data.completedCount
	}
	s.metricsCache.RUnlock()

	// Atualizar cache
	s.metricsCache.Lock()
	defer s.metricsCache.Unlock()

	counts, err := s.repo.GetOrderCountsByStatus()
	if err != nil {
		log.Printf("Erro ao buscar contagem de status: %v", err)
		return 0, 0, 0
	}

	s.metricsCache.data.pendingCount = counts[models.StatusPending]
	s.metricsCache.data.inProgressCount = counts[models.StatusInProgress]
	s.metricsCache.data.completedCount = counts[models.StatusCompleted]
	s.metricsCache.lastUpdate = time.Now()

	return s.metricsCache.data.pendingCount,
		s.metricsCache.data.inProgressCount,
		s.metricsCache.data.completedCount
}

// GetOrderInteractions retorna as interações de uma ordem
func (s *OrderService) GetOrderInteractions(orderID, offset, limit uint) ([]models.Interaction, error) {
	return s.repo.GetOrderInteractions(orderID)
}

// GetOrderCosts retorna os custos de uma ordem
func (s *OrderService) GetOrderCosts(orderID uint) ([]models.CostItem, error) {
	return s.repo.GetOrderCosts(orderID)
}
