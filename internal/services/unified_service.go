package services

import (
	"tradicao/internal/models"
	"tradicao/internal/repository"
)

// UnifiedFilialService é um serviço unificado que implementa todas as interfaces
// relacionadas a filiais (FilialService, StationService, BranchService)
type UnifiedFilialService struct {
	repo *repository.UnifiedFilialRepository
}

// NewUnifiedFilialService cria um novo serviço unificado
func NewUnifiedFilialService(repo *repository.UnifiedFilialRepository) *UnifiedFilialService {
	return &UnifiedFilialService{
		repo: repo,
	}
}

// Implementação de FilialService

func (s *UnifiedFilialService) GetAllFiliais() ([]models.Filial, error) {
	return s.repo.GetAllFiliais()
}

func (s *UnifiedFilialService) GetFilialByID(id int64) (*models.Filial, error) {
	return s.repo.GetFilialByID(uint(id))
}

func (s *UnifiedFilialService) GetFiliaisByUserID(userID int64) ([]models.Filial, error) {
	return s.repo.GetFiliaisByUserID(userID)
}

func (s *UnifiedFilialService) CreateFilial(filial *models.Filial) error {
	return s.repo.CreateFilial(filial)
}

func (s *UnifiedFilialService) UpdateFilial(filial *models.Filial) error {
	return s.repo.UpdateFilial(filial)
}

func (s *UnifiedFilialService) DeleteFilial(id int64) error {
	return s.repo.DeleteFilial(uint(id))
}

func (s *UnifiedFilialService) GetFiliaisByRegion(region string) ([]models.Filial, error) {
	return s.repo.GetFiliaisByRegion(region)
}

// Implementação de StationService

func (s *UnifiedFilialService) GetAllStations() ([]models.Station, error) {
	return s.repo.GetAllStations()
}

func (s *UnifiedFilialService) GetStationByID(id int64) (*models.Station, error) {
	return s.repo.GetStationByID(uint(id))
}

func (s *UnifiedFilialService) GetStationsByBranchID(branchID int64) ([]models.Station, error) {
	// Implementar conforme necessário
	return nil, nil
}

func (s *UnifiedFilialService) CreateStation(station *models.Station) error {
	return s.repo.CreateStation(station)
}

func (s *UnifiedFilialService) UpdateStation(station *models.Station) error {
	return s.repo.UpdateStation(station)
}

func (s *UnifiedFilialService) DeleteStation(id int64) error {
	return s.repo.DeleteStation(uint(id))
}

// Implementação de BranchService

func (s *UnifiedFilialService) GetAllBranches(page, limit int, search string, isActive *bool) ([]models.Branch, int64, error) {
	return s.repo.GetAllBranches(page, limit, search, isActive)
}

func (s *UnifiedFilialService) GetBranchByID(id uint) (*models.Branch, error) {
	return s.repo.GetBranchByID(id)
}

func (s *UnifiedFilialService) GetBranchesByUserID(userID int64) ([]models.Branch, error) {
	return s.repo.GetBranchesByUserID(userID)
}

func (s *UnifiedFilialService) GetBranchesByTechnicianID(technicianID int64) ([]models.Branch, error) {
	return s.repo.GetBranchesByTechnicianID(technicianID)
}

func (s *UnifiedFilialService) CreateBranch(branch *models.Branch) error {
	return s.repo.CreateBranch(branch)
}

func (s *UnifiedFilialService) UpdateBranch(branch *models.Branch) error {
	return s.repo.UpdateBranch(branch)
}

func (s *UnifiedFilialService) DeleteBranch(id uint) error {
	return s.repo.DeleteBranch(id)
}

func (s *UnifiedFilialService) LinkProvider(branchID, providerID uint) error {
	return s.repo.LinkProvider(branchID, providerID)
}

func (s *UnifiedFilialService) UnlinkProvider(branchID, providerID uint) error {
	return s.repo.UnlinkProvider(branchID, providerID)
}

func (s *UnifiedFilialService) GetLinkedProviders(branchID uint) ([]uint, error) {
	return s.repo.GetLinkedProviders(branchID)
}

func (s *UnifiedFilialService) GenerateAuthToken(branchID uint) (string, error) {
	return s.repo.GenerateAuthToken(branchID)
}

func (s *UnifiedFilialService) ValidateAuthToken(token string) (uint, error) {
	return s.repo.ValidateAuthToken(token)
}

func (s *UnifiedFilialService) UseAuthToken(token string) error {
	return s.repo.UseAuthToken(token)
}
