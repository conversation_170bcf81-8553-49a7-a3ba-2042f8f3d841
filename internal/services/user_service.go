package services

import (
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/dgrijalva/jwt-go"
	"golang.org/x/crypto/bcrypt"

	"tradicao/internal/config"
	"tradicao/internal/middleware"
	"tradicao/internal/models"
	"tradicao/internal/repository"
)

// UserService gerencia a lógica de negócios relacionada a usuários
type UserService struct {
	userRepo    repository.UserRepository
	config      *config.Config
	TotpService *TOTPService // Serviço para gerenciar autenticação de dois fatores
}

// GetUserRepo retorna o repositório de usuários
func (s *UserService) GetUserRepo() repository.UserRepository {
	return s.userRepo
}

// NewUserService cria um novo serviço de usuário
func NewUserService(userRepo repository.UserRepository, config *config.Config) *UserService {
	return &UserService{
		userRepo:    userRepo,
		config:      config,
		TotpService: NewTOTPService(),
	}
}

// Authenticate autentica um usuário e retorna um token JWT se bem-sucedido
// Se o usuário tem 2FA ativado, não retorna o token até que o código TOTP seja validado
func (s *UserService) Authenticate(email, password string, totpCode ...string) (*models.LoginResponse, error) {
	fmt.Printf("Tentativa de autenticação para email: %s\n", email)

	// Busca o usuário pelo e-mail
	user, err := s.userRepo.FindByEmail(email)
	if err != nil {
		fmt.Printf("Erro ao buscar usuário por email: %v\n", err)
		return nil, errors.New("credenciais inválidas")
	}

	fmt.Printf("Usuário encontrado: %s (ID: %d)\n", user.Name, user.ID)

	// Compara a senha
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password))
	if err != nil {
		fmt.Printf("Senha incorreta para usuário: %s\n", email)

		// Registra tentativa falha
		s.userRepo.UpdateLoginAttempts(user.ID, true)

		return nil, errors.New("credenciais inválidas")
	}

	fmt.Printf("Senha validada com sucesso para: %s\n", email)

	// Verifica se o usuário tem 2FA ativado
	if user.TOTPEnabled {
		// Se o 2FA está ativado e nenhum código foi fornecido
		if len(totpCode) == 0 {
			return &models.LoginResponse{
				RequiresTOTP: true,
				User:         user.ToResponse(),
			}, nil
		}

		// Valida o código TOTP fornecido
		valid, err := s.ValidateTOTP(user.ID, totpCode[0])
		if err != nil || !valid {
			// Registra tentativa falha
			s.userRepo.UpdateLoginAttempts(user.ID, true)

			return nil, errors.New("código de verificação inválido")
		}
	}

	// Reseta tentativas de login após autenticação bem-sucedida
	s.userRepo.UpdateLoginAttempts(user.ID, false)

	// Gera o token JWT
	token, _, err := s.generateJWT(user)
	if err != nil {
		fmt.Printf("Erro ao gerar JWT: %v\n", err)
		return nil, fmt.Errorf("erro ao gerar token: %v", err)
	}

	fmt.Printf("JWT gerado com sucesso para usuário: %s\n", email)

	// Cria a resposta
	userResponse := models.NewUserResponse{
		User: user.ToResponse(),
	}

	response := &models.LoginResponse{
		Token:        token,
		User:         userResponse.User,
		RequiresTOTP: false,
	}

	return response, nil
}

// GetUserByID busca um usuário pelo ID
func (s *UserService) GetUserByID(id int64) (*models.UserResponse, error) {
	user, err := s.userRepo.FindByID(uint(id))
	if err != nil {
		return nil, err
	}

	response := user.ToResponse()
	return &response, nil
}

// GetTechnicians retorna todos os técnicos disponíveis
func (s *UserService) GetTechnicians() ([]models.UserResponse, error) {
	users, err := s.userRepo.FindAll()
	if err != nil {
		return nil, err
	}

	var technicians []models.UserResponse
	for _, user := range users {
		if user.Role == models.RoleTechnician { // Usando a constante padronizada
			technicians = append(technicians, user.ToResponse())
		}
	}

	return technicians, nil
}

// generateJWT gera um token JWT para o usuário
func (s *UserService) generateJWT(user models.User) (string, time.Time, error) {
	// Define tempo de expiração com base no perfil do usuário
	var expirationTime time.Time

	// Perfis privilegiados têm tokens com duração de 24 horas
	if user.Role == models.RoleAdmin || user.Role == models.RoleGerente || user.Role == models.RoleFilial {
		expirationTime = time.Now().Add(24 * time.Hour)
		fmt.Printf("[AUTH] Gerando token de longa duração (24h) para usuário %s com perfil %s\n", user.Email, user.Role)
	} else {
		// Outros perfis têm tokens com duração de 10 minutos
		expirationTime = time.Now().Add(10 * time.Minute)
		fmt.Printf("[AUTH] Gerando token de curta duração (10min) para usuário %s com perfil %s\n", user.Email, user.Role)
	}

	// Gera um ID único para o token (JTI)
	tokenID, err := middleware.GenerateUniqueTokenID()
	if err != nil {
		fmt.Printf("[AUTH-ERROR] Erro ao gerar ID único para token: %v\n", err)
		return "", time.Time{}, err
	}

	claims := jwt.MapClaims{
		"user_id": user.ID,
		"name":    user.Name,
		"email":   user.Email,
		"role":    user.Role,
		"exp":     expirationTime.Unix(),
		"iat":     time.Now().Unix(),
		"nbf":     time.Now().Unix(),
		"jti":     tokenID, // JWT ID - identificador único para este token
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	tokenString, err := token.SignedString(middleware.JWTSecret)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("erro ao assinar o token: %v", err)
	}

	return tokenString, expirationTime, nil
}

// HashPassword cria um hash da senha
func (s *UserService) HashPassword(password string) (string, error) {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", fmt.Errorf("erro ao criar hash de senha: %v", err)
	}
	return string(hashedPassword), nil
}

// ChangePassword altera a senha de um usuário seguindo a política de senhas
func (s *UserService) ChangePassword(userID uint, currentPassword, newPassword string) error {
	fmt.Printf("[ALTERAR SENHA] Iniciando alteração de senha para usuário ID: %d\n", userID)

	user, err := s.userRepo.FindByID(userID)
	if err != nil {
		fmt.Printf("[ALTERAR SENHA] Erro ao buscar usuário: %v\n", err)
		return fmt.Errorf("erro ao buscar usuário: %v", err)
	}

	fmt.Printf("[ALTERAR SENHA] Usuário encontrado: %s (ID: %d)\n", user.Name, user.ID)

	// Verificar senha atual
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(currentPassword))
	if err != nil {
		fmt.Println("[ALTERAR SENHA] Erro: Senha atual incorreta")
		return errors.New("senha atual incorreta")
	}

	fmt.Println("[ALTERAR SENHA] Senha atual validada com sucesso")

	// Validar nova senha
	passwordPolicy := NewPasswordPolicyService(s.userRepo)

	fmt.Println("[ALTERAR SENHA] Validando força da nova senha")
	strength, validationErr := passwordPolicy.ValidatePasswordStrength(newPassword)
	if validationErr != nil {
		fmt.Printf("[ALTERAR SENHA] Erro na validação da senha: %v\n", validationErr)
		return validationErr
	}

	if strength < MediumPassword {
		fmt.Println("[ALTERAR SENHA] Erro: Senha muito fraca")
		return errors.New("a nova senha não atende aos requisitos mínimos de segurança")
	}

	// Verificar informações pessoais
	if passwordPolicy.containsPersonalInfo(newPassword, user) {
		fmt.Println("[ALTERAR SENHA] Erro: Senha contém informações pessoais")
		return errors.New("a senha não deve conter informações pessoais como nome ou email")
	}

	// Verificar se é igual à senha atual
	if currentPassword == newPassword {
		fmt.Println("[ALTERAR SENHA] Erro: Nova senha igual à senha atual")
		return errors.New("a nova senha não pode ser igual à senha atual")
	}

	// Gerar hash da nova senha
	fmt.Println("[ALTERAR SENHA] Gerando hash da nova senha")
	hashedPassword, err := s.HashPassword(newPassword)
	if err != nil {
		fmt.Printf("[ALTERAR SENHA] Erro ao gerar hash: %v\n", err)
		return err
	}

	// Verificar histórico de senhas
	fmt.Println("[ALTERAR SENHA] Verificando histórico de senhas")
	inHistory, err := s.userRepo.IsPasswordInHistory(userID, hashedPassword)
	if err != nil {
		fmt.Printf("[ALTERAR SENHA] Erro ao verificar histórico: %v\n", err)
		return fmt.Errorf("erro ao verificar histórico de senhas: %v", err)
	}

	if inHistory {
		fmt.Println("[ALTERAR SENHA] Erro: Senha já utilizada anteriormente")
		return errors.New("a nova senha não pode ser igual a uma das últimas 5 senhas utilizadas")
	}

	// Adicionar senha atual ao histórico
	fmt.Println("[ALTERAR SENHA] Adicionando senha atual ao histórico")
	if err := s.userRepo.AddPasswordToHistory(userID, user.Password); err != nil {
		fmt.Printf("[ALTERAR SENHA] Erro ao atualizar histórico: %v\n", err)
		return fmt.Errorf("erro ao atualizar histórico de senhas: %v", err)
	}

	// Atualizar senha
	fmt.Println("[ALTERAR SENHA] Atualizando senha no banco de dados")
	if err := s.userRepo.UpdatePassword(userID, hashedPassword); err != nil {
		fmt.Printf("[ALTERAR SENHA] Erro ao atualizar senha: %v\n", err)
		return fmt.Errorf("erro ao atualizar senha: %v", err)
	}

	// Desativar flag de troca obrigatória
	fmt.Println("[ALTERAR SENHA] Desativando flag de troca obrigatória")
	err = s.userRepo.DisableForcePasswordChange(userID)
	if err != nil {
		// Apenas logar o erro, mas não falhar a operação
		fmt.Printf("[ALTERAR SENHA] Aviso: Não foi possível atualizar o status de troca obrigatória: %v\n", err)
		// Verificar se o erro é relacionado à coluna inexistente
		if err.Error() != "ERROR: column \"force_password_change\" of relation \"users\" does not exist (SQLSTATE 42703)" {
			// Se for outro tipo de erro, pode ser mais grave
			fmt.Printf("[ALTERAR SENHA] Aviso: Erro não esperado ao atualizar status: %v\n", err)
		}
		// Não retornar erro para não impedir a alteração de senha
	}

	fmt.Println("[ALTERAR SENHA] Senha alterada com sucesso")
	return nil
}

// IsPasswordChangeRequired verifica se o usuário precisa trocar a senha
func (s *UserService) IsPasswordChangeRequired(userID uint) (bool, error) {
	user, err := s.userRepo.FindByID(userID)
	if err != nil {
		return false, fmt.Errorf("erro ao buscar usuário: %v", err)
	}

	if user.ID == 0 {
		return false, fmt.Errorf("usuário não encontrado: ID %d", userID)
	}

	// Verificar se o usuário precisa trocar a senha (se a coluna existir)
	// Nota: Se a coluna não existir, o valor padrão é false
	if user.ForcePasswordChange {
		return true, nil
	}

	passwordPolicy := NewPasswordPolicyService(s.userRepo)
	expired, err := passwordPolicy.IsPasswordExpired(user) // Corrigindo a chamada para passar o objeto User
	if err != nil {
		log.Printf("Erro ao verificar expiração de senha: %v", err)
		return false, nil
	}

	return expired, nil
}

// ResetUserPassword redefine a senha de um usuário e força a troca no próximo login
func (s *UserService) ResetUserPassword(userID uint, newPassword string) error {
	_, err := s.userRepo.FindByID(userID)
	if err != nil {
		return fmt.Errorf("erro ao buscar usuário: %v", err)
	}

	hashedPassword, err := s.HashPassword(newPassword)
	if err != nil {
		return err
	}

	if err := s.userRepo.UpdatePassword(userID, hashedPassword); err != nil {
		return fmt.Errorf("erro ao atualizar senha: %v", err)
	}

	err = s.userRepo.EnableForcePasswordChange(userID)
	if err != nil {
		// Apenas logar o erro, mas não falhar a operação
		fmt.Printf("[RESET SENHA] Aviso: Não foi possível configurar troca obrigatória de senha: %v\n", err)
		// Verificar se o erro é relacionado à coluna inexistente
		if err.Error() != "ERROR: column \"force_password_change\" of relation \"users\" does not exist (SQLSTATE 42703)" {
			// Se for outro tipo de erro, pode ser mais grave
			fmt.Printf("[RESET SENHA] Aviso: Erro não esperado ao configurar troca obrigatória: %v\n", err)
		}
		// Não retornar erro para não impedir o reset de senha
	}

	return nil
}

// SetupTOTP configura o TOTP para um usuário
func (s *UserService) SetupTOTP(userID uint) (string, string, error) {
	user, err := s.userRepo.FindByID(userID)
	if err != nil {
		return "", "", fmt.Errorf("erro ao buscar usuário: %v", err)
	}

	secret, qrURL, err := s.TotpService.GenerateSecret(user.Email)
	if err != nil {
		return "", "", fmt.Errorf("erro ao gerar segredo TOTP: %v", err)
	}

	if err := s.userRepo.SetTOTPSecret(userID, secret); err != nil {
		return "", "", fmt.Errorf("erro ao salvar segredo TOTP: %v", err)
	}

	go s.TotpService.CleanupOldQRCodes()

	return secret, qrURL, nil
}

// EnableTOTP ativa o TOTP para um usuário após validação do código
func (s *UserService) EnableTOTP(userID uint, code string) error {
	secret, enabled, err := s.userRepo.GetTOTPSecret(userID)
	if err != nil {
		return fmt.Errorf("erro ao buscar segredo TOTP: %v", err)
	}

	if enabled {
		return errors.New("2FA já está ativado para este usuário")
	}

	valid, err := s.TotpService.ValidateCode(secret, code)
	if err != nil {
		return fmt.Errorf("erro ao validar código: %v", err)
	}

	if !valid {
		return errors.New("código de verificação inválido")
	}

	if err := s.userRepo.EnableTOTP(userID, secret); err != nil {
		return fmt.Errorf("erro ao ativar TOTP: %v", err)
	}

	return nil
}

// DisableTOTP desativa o TOTP para um usuário
func (s *UserService) DisableTOTP(userID uint, password string) error {
	user, err := s.userRepo.FindByID(userID)
	if err != nil {
		return fmt.Errorf("erro ao buscar usuário: %v", err)
	}

	if !user.TOTPEnabled {
		return errors.New("2FA não está ativado para este usuário")
	}

	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		return errors.New("senha incorreta")
	}

	if err := s.userRepo.DisableTOTP(userID); err != nil {
		return fmt.Errorf("erro ao desativar TOTP: %v", err)
	}

	return nil
}

// ValidateTOTP valida um código TOTP
func (s *UserService) ValidateTOTP(userID uint, code string) (bool, error) {
	secret, enabled, err := s.userRepo.GetTOTPSecret(userID)
	if err != nil {
		return false, fmt.Errorf("erro ao buscar segredo TOTP: %v", err)
	}

	if !enabled {
		return false, errors.New("2FA não está ativado para este usuário")
	}

	return s.TotpService.ValidateCode(secret, code)
}

// CreateAuditLog registra uma ação no log de auditoria
func (s *UserService) CreateAuditLog(log models.AuditLog) error {
	return s.userRepo.CreateAuditLog(log)
}
