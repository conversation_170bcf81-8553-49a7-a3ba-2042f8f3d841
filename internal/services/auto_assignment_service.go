package services

import (
	"fmt"
	"log"
	"tradicao/internal/models"
	"tradicao/internal/notifications"

	"gorm.io/gorm"
)

// AutoAssignmentService é o serviço responsável pela atribuição automática de ordens
type AutoAssignmentService struct {
	db                  *gorm.DB
	notificationService *notifications.Service
}

// NewAutoAssignmentService cria uma nova instância do serviço de atribuição automática
func NewAutoAssignmentService(db *gorm.DB) *AutoAssignmentService {
	return &AutoAssignmentService{
		db:                  db,
		notificationService: notifications.NewService(),
	}
}

// AssignOrderToProvider atribui automaticamente uma ordem a um prestador de serviço
func (s *AutoAssignmentService) AssignOrderToProvider(order *models.MaintenanceOrder) error {
	// Verificar se a ordem já tem um prestador atribuído
	if order.ServiceProviderID != nil && *order.ServiceProviderID > 0 {
		return nil
	}

	// Obter o equipamento
	var equipment models.Equipment
	if err := s.db.First(&equipment, order.EquipmentID).Error; err != nil {
		return fmt.Errorf("erro ao buscar equipamento: %w", err)
	}

	// Se o equipamento não tiver um tipo padronizado, tentar encontrar um com base no campo Type
	if equipment.EquipmentTypeID == nil || *equipment.EquipmentTypeID == 0 {
		var equipmentType models.EquipmentType
		if err := s.db.Where("name LIKE ?", "%"+equipment.Type+"%").First(&equipmentType).Error; err != nil {
			log.Printf("Não foi possível encontrar um tipo padronizado para o equipamento %d (tipo: %s): %v",
				equipment.ID, equipment.Type, err)
		} else {
			typeID := equipmentType.ID
			equipment.EquipmentTypeID = &typeID
			s.db.Save(&equipment)
		}
	}

	// Buscar prestadores elegíveis
	providers, err := s.GetEligibleProviders(order.BranchID, equipment.ID)
	if err != nil {
		return fmt.Errorf("erro ao buscar prestadores elegíveis: %w", err)
	}

	// Se não houver prestadores elegíveis, retornar erro
	if len(providers) == 0 {
		return fmt.Errorf("nenhum prestador elegível encontrado para a ordem")
	}

	// Selecionar o primeiro prestador (poderia implementar lógica mais sofisticada aqui)
	provider := providers[0]

	// Atribuir o prestador à ordem
	order.ServiceProviderID = &provider.ID

	// Salvar a ordem
	if err := s.db.Save(order).Error; err != nil {
		return fmt.Errorf("erro ao salvar ordem: %w", err)
	}

	// Enviar notificação ao prestador
	if err := s.notificationService.NotifyProviderOrderAssigned(order.ID, provider.ID); err != nil {
		// Apenas logar o erro, não impedir a atribuição
		log.Printf("Erro ao enviar notificação para o prestador %d: %s", provider.ID, err.Error())
	}

	log.Printf("Ordem %d atribuída automaticamente ao prestador %d", order.ID, provider.ID)

	return nil
}

// GetEligibleProviders retorna os prestadores elegíveis para uma ordem
func (s *AutoAssignmentService) GetEligibleProviders(branchID uint, equipmentID uint) ([]models.ServiceProvider, error) {
	// Obter o equipamento
	var equipment models.Equipment
	if err := s.db.First(&equipment, equipmentID).Error; err != nil {
		return nil, fmt.Errorf("erro ao buscar equipamento: %w", err)
	}

	var providers []models.ServiceProvider

	// Se o equipamento tiver um tipo padronizado, buscar prestadores que atendem a esse tipo
	if equipment.EquipmentTypeID != nil && *equipment.EquipmentTypeID > 0 {
		// Consulta SQL para encontrar prestadores elegíveis
		query := `
			SELECT sp.* FROM service_providers sp
			JOIN provider_branches pb ON sp.id = pb.service_provider_id
			JOIN provider_equipment_types pet ON sp.id = pet.service_provider_id
			WHERE pb.branch_id = ? AND pet.equipment_type_id = ?
			AND sp.status = 'active'
		`

		if err := s.db.Raw(query, branchID, *equipment.EquipmentTypeID).Scan(&providers).Error; err != nil {
			return nil, fmt.Errorf("erro ao buscar prestadores por tipo padronizado: %w", err)
		}
	} else {
		// Se não tiver tipo padronizado, buscar prestadores que atendem à filial
		query := `
			SELECT sp.* FROM service_providers sp
			JOIN provider_branches pb ON sp.id = pb.service_provider_id
			WHERE pb.branch_id = ? AND sp.status = 'active'
		`

		if err := s.db.Raw(query, branchID).Scan(&providers).Error; err != nil {
			return nil, fmt.Errorf("erro ao buscar prestadores por filial: %w", err)
		}
	}

	return providers, nil
}

// AssignProviderToBranch atribui um prestador a uma filial
func (s *AutoAssignmentService) AssignProviderToBranch(providerID uint, branchID uint) error {
	// Verificar se o prestador existe
	var provider models.ServiceProvider
	if err := s.db.First(&provider, providerID).Error; err != nil {
		return fmt.Errorf("prestador não encontrado: %w", err)
	}

	// Verificar se a filial existe
	var branch models.Branch
	if err := s.db.First(&branch, branchID).Error; err != nil {
		return fmt.Errorf("filial não encontrada: %w", err)
	}

	// Verificar se a atribuição já existe
	var count int64
	s.db.Model(&models.ProviderBranch{}).
		Where("service_provider_id = ? AND branch_id = ?", providerID, branchID).
		Count(&count)

	if count > 0 {
		return fmt.Errorf("esta atribuição já existe")
	}

	// Criar a atribuição
	assignment := models.ProviderBranch{
		ServiceProviderID: providerID,
		BranchID:          branchID,
	}

	if err := s.db.Create(&assignment).Error; err != nil {
		return fmt.Errorf("erro ao criar atribuição: %w", err)
	}

	return nil
}

// AssignProviderToEquipmentType atribui um prestador a um tipo de equipamento
func (s *AutoAssignmentService) AssignProviderToEquipmentType(providerID uint, equipmentTypeID uint) error {
	// Verificar se o prestador existe
	var provider models.ServiceProvider
	if err := s.db.First(&provider, providerID).Error; err != nil {
		return fmt.Errorf("prestador não encontrado: %w", err)
	}

	// Verificar se o tipo de equipamento existe
	var equipmentType models.EquipmentType
	if err := s.db.First(&equipmentType, equipmentTypeID).Error; err != nil {
		return fmt.Errorf("tipo de equipamento não encontrado: %w", err)
	}

	// Verificar se a atribuição já existe
	var count int64
	s.db.Model(&models.ProviderEquipmentType{}).
		Where("service_provider_id = ? AND equipment_type_id = ?", providerID, equipmentTypeID).
		Count(&count)

	if count > 0 {
		return fmt.Errorf("esta atribuição já existe")
	}

	// Criar a atribuição
	assignment := models.ProviderEquipmentType{
		ServiceProviderID: providerID,
		EquipmentTypeID:   equipmentTypeID,
	}

	if err := s.db.Create(&assignment).Error; err != nil {
		return fmt.Errorf("erro ao criar atribuição: %w", err)
	}

	return nil
}

// RemoveProviderFromBranch remove um prestador de uma filial
func (s *AutoAssignmentService) RemoveProviderFromBranch(providerID uint, branchID uint) error {
	result := s.db.Where("service_provider_id = ? AND branch_id = ?", providerID, branchID).
		Delete(&models.ProviderBranch{})

	if result.Error != nil {
		return fmt.Errorf("erro ao remover atribuição: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("atribuição não encontrada")
	}

	return nil
}

// RemoveProviderFromEquipmentType remove um prestador de um tipo de equipamento
func (s *AutoAssignmentService) RemoveProviderFromEquipmentType(providerID uint, equipmentTypeID uint) error {
	result := s.db.Where("service_provider_id = ? AND equipment_type_id = ?", providerID, equipmentTypeID).
		Delete(&models.ProviderEquipmentType{})

	if result.Error != nil {
		return fmt.Errorf("erro ao remover atribuição: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("atribuição não encontrada")
	}

	return nil
}
