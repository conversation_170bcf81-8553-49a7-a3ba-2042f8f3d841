package services

import (
	"fmt"
	"tradicao/internal/models"
	"tradicao/internal/repository"
)

// StationService encapsula a lógica de negócios relacionada a estações/postos
// Deprecated: Use FilialService em vez disso
type StationService struct {
	stationRepo   repository.IStationRepository
	filialService *FilialService // Para delegar operações ao FilialService quando possível
}

// NewStationService cria uma nova instância do serviço de estações
// Deprecated: Use NewFilialService em vez disso
func NewStationService(stationRepo repository.IStationRepository) *StationService {
	// Criar um adaptador para converter IStationRepository para IFilialRepository
	filialRepo := repository.StationToFilialRepository(stationRepo)

	// Criar um FilialService usando o adaptador
	filialService := NewFilialService(filialRepo)

	return &StationService{
		stationRepo:   stationRepo,
		filialService: filialService,
	}
}

// GetAllStations retorna todas as estações
// Deprecated: Use FilialService.GetAllFiliais em vez disso
func (s *StationService) GetAllStations() ([]models.Station, error) {
	// Usar o FilialService quando possível
	if s.filialService != nil {
		filiais, err := s.filialService.GetAllFiliais()
		if err != nil {
			return nil, err
		}

		// Converter Filial para Station
		stations := make([]models.Station, len(filiais))
		for i, filial := range filiais {
			station := filial.ToStation()
			stations[i] = *station
		}

		return stations, nil
	}

	// Fallback para o repositório original
	return s.stationRepo.GetAllStations()
}

// GetStationByID retorna uma estação pelo ID
// Deprecated: Use FilialService.GetFilialByID em vez disso
func (s *StationService) GetStationByID(id int64) (*models.Station, error) {
	// Usar o FilialService quando possível
	if s.filialService != nil {
		filial, err := s.filialService.GetFilialByID(id)
		if err != nil {
			return nil, err
		}

		return filial.ToStation(), nil
	}

	// Fallback para o repositório original
	return s.stationRepo.GetStationByID(uint(id))
}

// GetStationsByBranchID retorna todas as estações de uma filial
// Deprecated: Use FilialService.GetFiliaisByUserID em vez disso
func (s *StationService) GetStationsByBranchID(branchID int64) ([]models.Station, error) {
	// Usar o FilialService quando possível
	if s.filialService != nil {
		// Não há um método direto para obter filiais por branchID no FilialService
		// Então vamos obter todas as filiais e filtrar
		filiais, err := s.filialService.GetAllFiliais()
		if err != nil {
			return nil, err
		}

		// Filtrar filiais pelo branchID
		var filiaisFiltradas []models.Filial
		for _, filial := range filiais {
			if filial.BranchID != nil && *filial.BranchID == uint(branchID) {
				filiaisFiltradas = append(filiaisFiltradas, filial)
			}
		}

		// Converter Filial para Station
		stations := make([]models.Station, len(filiaisFiltradas))
		for i, filial := range filiaisFiltradas {
			station := filial.ToStation()
			stations[i] = *station
		}

		return stations, nil
	}

	// Fallback para o repositório original
	return s.stationRepo.GetAllStations()
}

// CreateStation cria uma nova estação
// Deprecated: Use FilialService.CreateFilial em vez disso
func (s *StationService) CreateStation(station *models.Station) error {
	// Usar o FilialService quando possível
	if s.filialService != nil {
		// Converter Station para Filial
		filial := models.FromStation(station)

		// Criar a filial usando o FilialService
		err := s.filialService.CreateFilial(filial)
		if err != nil {
			return err
		}

		// Atualizar o objeto Station com os dados da filial criada
		*station = *filial.ToStation()

		return nil
	}

	// Fallback para o código original
	// Verifica se já existe uma estação com o mesmo código
	exists := false
	stations, err := s.stationRepo.GetAllStations()
	if err != nil {
		return err
	}
	for _, s := range stations {
		if s.Code == station.Code {
			exists = true
			break
		}
	}
	if exists {
		return fmt.Errorf("já existe uma estação com o código %s", station.Code)
	}

	return s.stationRepo.CreateStation(station)
}

// UpdateStation atualiza uma estação existente
// Deprecated: Use FilialService.UpdateFilial em vez disso
func (s *StationService) UpdateStation(station *models.Station) error {
	// Usar o FilialService quando possível
	if s.filialService != nil {
		// Converter Station para Filial
		filial := models.FromStation(station)

		// Atualizar a filial usando o FilialService
		return s.filialService.UpdateFilial(filial)
	}

	// Fallback para o código original
	// Verifica se a estação existe
	exists := false
	stations, err := s.stationRepo.GetAllStations()
	if err != nil {
		return err
	}
	for _, s := range stations {
		if s.ID == station.ID {
			exists = true
			break
		}
	}
	if !exists {
		return fmt.Errorf("estação não encontrada")
	}

	return s.stationRepo.UpdateStation(station)
}

// DeleteStation remove uma estação
// Deprecated: Use FilialService.DeleteFilial em vez disso
func (s *StationService) DeleteStation(id int64) error {
	// Usar o FilialService quando possível
	if s.filialService != nil {
		return s.filialService.DeleteFilial(id)
	}

	// Fallback para o repositório original
	return s.stationRepo.DeleteStation(uint(id))
}
