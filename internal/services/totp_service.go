package services

import (
	"crypto/rand"
	"encoding/base32"
	"errors"
	"fmt"
	"image/png"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/boombuler/barcode"
	"github.com/boombuler/barcode/qr"
	"github.com/pquerna/otp"
	"github.com/pquerna/otp/totp"
)

// TOTPService fornece funcionalidades relacionadas a autenticação TOTP
type TOTPService struct {
	appName string
	issuer  string
}

// NewTOTPService cria uma nova instância do serviço TOTP
func NewTOTPService() *TOTPService {
	return &TOTPService{
		appName: "ShellTradição",
		issuer:  "RedeTradição",
	}
}

// GenerateSecret cria um novo segredo TOTP para um usuário
func (s *TOTPService) GenerateSecret(username string) (string, string, error) {
	// Gerar chave
	key, err := totp.Generate(totp.GenerateOpts{
		Issuer:      s.issuer,
		AccountName: username,
		SecretSize:  20, // 160 bits como recomendado
	})
	if err != nil {
		return "", "", fmt.Errorf("erro ao gerar segredo TOTP: %w", err)
	}

	// Obter URL para QR Code
	qrURL, err := s.GenerateQRCode(key)
	if err != nil {
		return "", "", fmt.Errorf("erro ao gerar QR code: %w", err)
	}

	return key.Secret(), qrURL, nil
}

// GenerateQRCode cria uma imagem QR code para configuração do TOTP
func (s *TOTPService) GenerateQRCode(key *otp.Key) (string, error) {
	// Criar diretório para armazenar QR codes temporários
	qrDir := "./data/temp/qrcodes"
	if err := os.MkdirAll(qrDir, 0755); err != nil {
		return "", fmt.Errorf("erro ao criar diretório para QR codes: %w", err)
	}

	// Gerar QR code
	qrCode, err := qr.Encode(key.URL(), qr.M, qr.Auto)
	if err != nil {
		return "", fmt.Errorf("erro ao codificar QR code: %w", err)
	}

	// Redimensionar para 256x256
	qrCode, err = barcode.Scale(qrCode, 256, 256)
	if err != nil {
		return "", fmt.Errorf("erro ao redimensionar QR code: %w", err)
	}

	// Gerar nome único de arquivo
	randBytes := make([]byte, 8)
	if _, err := rand.Read(randBytes); err != nil {
		return "", fmt.Errorf("erro ao gerar bytes aleatórios: %w", err)
	}

	filename := fmt.Sprintf("qr_%s.png", base32.StdEncoding.EncodeToString(randBytes))
	filepath := filepath.Join(qrDir, filename)

	// Criar arquivo
	file, err := os.Create(filepath)
	if err != nil {
		return "", fmt.Errorf("erro ao criar arquivo QR code: %w", err)
	}
	defer file.Close()

	// Salvar imagem
	if err := png.Encode(file, qrCode); err != nil {
		return "", fmt.Errorf("erro ao salvar QR code: %w", err)
	}

	// Retornar URL para o QR code
	return fmt.Sprintf("/api/auth/security/qrcode/%s", filename), nil
}

// ValidateCode valida um código TOTP com base no segredo do usuário
func (s *TOTPService) ValidateCode(secret, code string) (bool, error) {
	if secret == "" {
		return false, errors.New("segredo TOTP não configurado")
	}

	if code == "" {
		return false, errors.New("código TOTP não fornecido")
	}

	// Corrigir o secret se necessário
	secret = strings.TrimSpace(secret)

	// Validar o código
	valid := totp.Validate(code, secret)
	if !valid {
		return false, nil
	}

	return true, nil
}

// GenerateRecoveryCodes cria códigos de recuperação para situações onde o usuário perde acesso ao dispositivo
func (s *TOTPService) GenerateRecoveryCodes() ([]string, error) {
	// Gerar 10 códigos de recuperação
	const numCodes = 10
	const codeLength = 10

	codes := make([]string, numCodes)

	for i := 0; i < numCodes; i++ {
		// Gerar bytes aleatórios para cada código
		randBytes := make([]byte, codeLength)
		if _, err := rand.Read(randBytes); err != nil {
			return nil, fmt.Errorf("erro ao gerar códigos de recuperação: %w", err)
		}

		// Codificar em base32 e formatar
		code := base32.StdEncoding.EncodeToString(randBytes)
		code = code[:codeLength] // Limitar para o tamanho desejado

		// Agrupar em dois grupos para facilitar leitura (Ex: ABCDE-FGHIJ)
		halfway := codeLength / 2
		codes[i] = fmt.Sprintf("%s-%s", code[:halfway], code[halfway:])
	}

	return codes, nil
}

// CleanupOldQRCodes remove códigos QR antigos para evitar acúmulo de arquivos
func (s *TOTPService) CleanupOldQRCodes() error {
	qrDir := "./data/temp/qrcodes"

	// Verificar se o diretório existe
	if _, err := os.Stat(qrDir); os.IsNotExist(err) {
		return nil // Nada a limpar se o diretório não existe
	}

	// Obter lista de arquivos
	files, err := os.ReadDir(qrDir)
	if err != nil {
		return fmt.Errorf("erro ao ler diretório de QR codes: %w", err)
	}

	// Obter a hora atual
	now := time.Now()

	// Verificar cada arquivo
	for _, file := range files {
		if file.IsDir() {
			continue // Pular diretórios
		}

		// Verificar nome do arquivo
		if !strings.HasPrefix(file.Name(), "qr_") || !strings.HasSuffix(file.Name(), ".png") {
			continue // Pular arquivos que não são QR codes
		}

		// Obter informações do arquivo
		info, err := file.Info()
		if err != nil {
			continue // Ignorar erro e prosseguir para o próximo
		}

		// Se o arquivo tiver mais de 1 hora, remover
		if now.Sub(info.ModTime()) > time.Hour {
			fullPath := filepath.Join(qrDir, file.Name())
			os.Remove(fullPath) // Ignora erro de remoção
		}
	}

	return nil
}

// GenerateSecurePassword gera uma senha segura aleatória com o comprimento especificado
func (s *TOTPService) GenerateSecurePassword(length int) string {
	if length < 8 {
		length = 8 // Garante um comprimento mínimo de segurança
	}

	// Caracteres que podem ser usados na senha
	const (
		uppercaseChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
		lowercaseChars = "abcdefghijklmnopqrstuvwxyz"
		numberChars    = "0123456789"
		specialChars   = "!@#$%^&*()-_=+[]{}|;:,.<>?"
	)

	allChars := uppercaseChars + lowercaseChars + numberChars + specialChars

	// Inicializa o buffer para a senha
	password := make([]byte, length)

	// Gera bytes aleatórios
	if _, err := rand.Read(password); err != nil {
		// Fallback simplificado se falhar a geração de bytes aleatórios
		for i := 0; i < length; i++ {
			password[i] = allChars[time.Now().Nanosecond()%len(allChars)]
		}
		return string(password)
	}

	// Converte cada byte em um caractere do conjunto permitido
	for i := 0; i < length; i++ {
		password[i] = allChars[int(password[i])%len(allChars)]
	}

	// Garante que a senha contém pelo menos uma de cada classe de caracteres
	if length >= 4 {
		password[0] = uppercaseChars[int(password[0])%len(uppercaseChars)]
		password[1] = lowercaseChars[int(password[1])%len(lowercaseChars)]
		password[2] = numberChars[int(password[2])%len(numberChars)]
		password[3] = specialChars[int(password[3])%len(specialChars)]
	}

	return string(password)
}
