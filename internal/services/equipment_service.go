package services

import (
	"context"
	"errors"

	"tradicao/internal/models"
	"tradicao/internal/repository"
)

// EquipmentService gerencia a lógica de negócios relacionada a equipamentos
type EquipmentService struct {
	equipmentRepo repository.IEquipmentRepository
}

// NewEquipmentService cria uma nova instância do serviço de equipamentos
func NewEquipmentService(equipmentRepo repository.IEquipmentRepository) *EquipmentService {
	return &EquipmentService{
		equipmentRepo: equipmentRepo,
	}
}

// GetAllEquipment retorna todos os equipamentos
func (s *EquipmentService) GetAllEquipment(ctx context.Context) ([]models.Equipment, error) {
	return s.equipmentRepo.List(ctx)
}

// GetEquipmentByID retorna um equipamento pelo ID
func (s *EquipmentService) GetEquipmentByID(ctx context.Context, id uint) (*models.Equipment, error) {
	return s.equipmentRepo.FindByID(ctx, id)
}

// GetEquipmentsByType retorna equipamentos filtrados por tipo
func (s *EquipmentService) GetEquipmentsByType(ctx context.Context, equipmentType string) ([]models.Equipment, error) {
	equipments, err := s.equipmentRepo.List(ctx)
	if err != nil {
		return nil, err
	}

	var filteredEquipments []models.Equipment
	for _, equipment := range equipments {
		if equipment.Type == equipmentType {
			filteredEquipments = append(filteredEquipments, equipment)
		}
	}

	return filteredEquipments, nil
}

// CreateEquipment cria um novo equipamento
func (s *EquipmentService) CreateEquipment(ctx context.Context, equipment *models.Equipment) error {
	// Validar dados do equipamento
	if equipment.Name == "" {
		return errors.New("nome do equipamento é obrigatório")
	}

	if equipment.Type == "" {
		return errors.New("tipo do equipamento é obrigatório")
	}

	if equipment.BranchID == 0 {
		return errors.New("filial é obrigatória")
	}

	// Criar o equipamento
	return s.equipmentRepo.Create(ctx, equipment)
}

// UpdateEquipment atualiza um equipamento existente
func (s *EquipmentService) UpdateEquipment(ctx context.Context, equipment *models.Equipment) error {
	// Verificar se o equipamento existe
	existingEquipment, err := s.equipmentRepo.FindByID(ctx, equipment.ID)
	if err != nil {
		return err
	}

	if existingEquipment == nil {
		return errors.New("equipamento não encontrado")
	}

	// Validar dados do equipamento
	if equipment.Name == "" {
		return errors.New("nome do equipamento é obrigatório")
	}

	if equipment.Type == "" {
		return errors.New("tipo do equipamento é obrigatório")
	}

	if equipment.BranchID == 0 {
		return errors.New("filial é obrigatória")
	}

	// Atualizar o equipamento
	return s.equipmentRepo.Update(ctx, equipment)
}

// DeleteEquipment remove um equipamento
func (s *EquipmentService) DeleteEquipment(ctx context.Context, id uint) error {
	// Verificar se o equipamento existe
	equipment, err := s.equipmentRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}

	if equipment == nil {
		return errors.New("equipamento não encontrado")
	}

	// Remover o equipamento
	return s.equipmentRepo.Delete(ctx, id)
}

// GetEquipmentsByFilial retorna equipamentos de uma filial específica
func (s *EquipmentService) GetEquipmentsByFilial(ctx context.Context, filialID uint) ([]models.Equipment, error) {
	return s.equipmentRepo.FindByBranch(ctx, filialID)
}

// GetEquipmentsNeedingMaintenance retorna equipamentos que precisam de manutenção
func (s *EquipmentService) GetEquipmentsNeedingMaintenance(ctx context.Context) ([]models.Equipment, error) {
	return s.equipmentRepo.FindNeedingMaintenance(ctx)
}
