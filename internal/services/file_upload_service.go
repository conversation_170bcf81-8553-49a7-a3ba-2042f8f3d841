package services

import (
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"

	"github.com/google/uuid"
)

// UploadType define o tipo de upload
type UploadType string

const (
	UploadTypeAvatar UploadType = "avatars"
	UploadTypeLogo   UploadType = "logos"
)

// FileUploadService gerencia uploads de arquivos
type FileUploadService struct {
	basePath string
}

// NewFileUploadService cria uma nova instância do serviço de upload
func NewFileUploadService(basePath string) *FileUploadService {
	// Garantir que o diretório base existe
	os.MkdirAll(basePath, 0755)
	
	// Criar subdiretórios para diferentes tipos de uploads
	os.MkdirAll(filepath.Join(basePath, string(UploadTypeAvatar)), 0755)
	os.MkdirAll(filepath.Join(basePath, string(UploadTypeLogo)), 0755)
	
	return &FileUploadService{
		basePath: basePath,
	}
}

// UploadFile faz upload de um arquivo e retorna o caminho relativo
func (s *FileUploadService) UploadFile(file *multipart.FileHeader, uploadType UploadType) (string, error) {
	// Verificar tipo de arquivo
	if !isValidImageFile(file.Filename) {
		return "", errors.New("tipo de arquivo não permitido, apenas imagens (jpg, jpeg, png, gif)")
	}
	
	// Gerar nome único para o arquivo
	ext := filepath.Ext(file.Filename)
	newFilename := fmt.Sprintf("%s%s", uuid.New().String(), ext)
	
	// Definir caminho completo
	uploadDir := filepath.Join(s.basePath, string(uploadType))
	filePath := filepath.Join(uploadDir, newFilename)
	
	// Abrir arquivo de origem
	src, err := file.Open()
	if err != nil {
		return "", err
	}
	defer src.Close()
	
	// Criar arquivo de destino
	dst, err := os.Create(filePath)
	if err != nil {
		return "", err
	}
	defer dst.Close()
	
	// Copiar conteúdo
	if _, err = io.Copy(dst, src); err != nil {
		return "", err
	}
	
	// Retornar caminho relativo para armazenar no banco de dados
	return fmt.Sprintf("/uploads/%s/%s", uploadType, newFilename), nil
}

// DeleteFile remove um arquivo existente
func (s *FileUploadService) DeleteFile(filePath string) error {
	// Extrair o caminho relativo se for uma URL completa
	relativePath := strings.TrimPrefix(filePath, "/uploads/")
	
	// Construir caminho completo
	fullPath := filepath.Join(s.basePath, relativePath)
	
	// Verificar se o arquivo existe
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		return nil // Arquivo já não existe, não é erro
	}
	
	// Remover arquivo
	return os.Remove(fullPath)
}

// isValidImageFile verifica se o arquivo é uma imagem válida
func isValidImageFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	validExts := []string{".jpg", ".jpeg", ".png", ".gif"}
	
	for _, validExt := range validExts {
		if ext == validExt {
			return true
		}
	}
	
	return false
}
