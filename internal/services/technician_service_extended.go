package services

import (
	"time"
	"tradicao/internal/models"
)

// Métodos adicionais para TechnicianService

// GetSpecialtyByID obtém uma especialidade pelo ID
func (s *TechnicianService) GetSpecialtyByID(id uint) (*models.TechnicianSpecialty, error) {
	return s.GetSpecialty(id)
}

// CreateTechnicianBranch cria uma associação entre técnico e filial
func (s *TechnicianService) CreateTechnicianBranch(branch *models.TechnicianBranch) error {
	return s.CreateBranchAssociation(branch)
}

// GetTechnicianBranches obtém as filiais associadas a um técnico
func (s *TechnicianService) GetTechnicianBranches(technicianID uint) ([]models.TechnicianBranch, error) {
	return s.ListBranchAssociations(technicianID)
}

// GetBranchTechnicians obtém os técnicos associados a uma filial
// Esta é uma implementação simplificada que retorna uma lista vazia
func (s *TechnicianService) GetBranchTechnicians(branchID uint) ([]models.Technician, error) {
	// Implementação simplificada
	return []models.Technician{}, nil
}

// DeleteTechnicianBranch remove uma associação entre técnico e filial
func (s *TechnicianService) DeleteTechnicianBranch(technicianID, branchID, specialtyID uint) error {
	return s.DeleteBranchAssociation(technicianID, branchID)
}

// GetTechnicianHistory obtém o histórico de manutenções de um técnico
func (s *TechnicianService) GetTechnicianHistory(technicianID uint) ([]models.MaintenanceHistory, error) {
	// Implementação simplificada
	startDate := time.Now().AddDate(-1, 0, 0) // 1 ano atrás
	endDate := time.Now()
	return s.ListMaintenanceHistory(technicianID, startDate, endDate)
}

// GetEquipmentHistory obtém o histórico de manutenções de um equipamento
// Esta é uma implementação simplificada que retorna uma lista vazia
func (s *TechnicianService) GetEquipmentHistory(equipmentID uint) ([]models.MaintenanceHistory, error) {
	// Implementação simplificada
	return []models.MaintenanceHistory{}, nil
}

// GetOrderHistory obtém o histórico de manutenções de uma ordem
// Esta é uma implementação simplificada que retorna uma lista vazia
func (s *TechnicianService) GetOrderHistory(orderID uint) ([]models.MaintenanceHistory, error) {
	// Implementação simplificada
	return []models.MaintenanceHistory{}, nil
}
