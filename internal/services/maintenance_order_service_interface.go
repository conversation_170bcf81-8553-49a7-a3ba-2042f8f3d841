package services

import (
	"context"
	"tradicao/internal/models"
)

// IMaintenanceOrderService define a interface para o serviço de ordens de manutenção
type IMaintenanceOrderService interface {
	// Métodos existentes
	CreateOrder(ctx context.Context, order *models.MaintenanceOrder) error
	UpdateOrder(ctx context.Context, order *models.MaintenanceOrder) error
	GetAllOrders() ([]models.MaintenanceOrder, error)
	UpdateStatus(ctx context.Context, orderID uint, status models.OrderStatus, userID uint, reason string) error
	GetOrderDetails(orderID uint) (*models.MaintenanceOrder, *models.Branch, *models.Equipment, *models.User, *models.User, *models.User, error)
	GetOrderDetailsByID(orderID uint) (*models.MaintenanceOrder, *models.Branch, *models.Equipment, *models.User, *models.User, *models.User, error)
	GetCostsByOrderID(orderID uint) ([]models.CostItem, error)
	AddCost(orderID uint, userID uint, cost models.CostItem) (*models.CostItem, error)
	AssignProvider(orderID uint, userID uint, providerID uint) (*models.MaintenanceOrder, error)
	SubmitForApproval(orderID uint, userID uint) (*models.MaintenanceOrder, error)
	Approve(orderID uint, userID uint) (*models.MaintenanceOrder, error)
	Reject(orderID uint, userID uint, reason string) (*models.MaintenanceOrder, error)
	Cancel(orderID uint, userID uint, reason string) (*models.MaintenanceOrder, error)
	AddInteraction(orderID uint, userID uint, message string) (*models.Interaction, error)
	UploadInvoice(orderID uint, userID uint, invoiceData models.Invoice, attachment models.Attachment) (*models.Invoice, error)

	// Novos métodos
	GetOrdersWithPagination(offset, limit int, status, branchID, startDate, endDate string) ([]models.MaintenanceOrder, int, error)
	GetOrderCountsByStatus() (pendingCount, inProgressCount, completedCount int)
	GetOrderInteractions(orderID, offset, limit uint) ([]models.Interaction, error)
	GetOrderCosts(orderID uint) ([]models.CostItem, error)

	// Novos métodos para notificações
	NotifyTechnicianAssigned(orderID uint, technicianID uint) error
	NotifyStatusChange(orderID uint, newStatus string, userID uint) error
	NotifyOrderApproval(orderID uint, approved bool, approverID uint) error
	NotifyMaterialAdded(orderID uint, materialID uint, addedByID uint) error
	NotifyNoteAdded(orderID uint, noteID uint, addedByID uint) error
}
