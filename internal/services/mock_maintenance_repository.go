package services

import (
	"github.com/stretchr/testify/mock"
	"tradicao/internal/models"
)

// MockMaintenanceRepository é um mock do repositório de manutenção
type MockMaintenanceRepository struct {
	mock.Mock
}

// FindByBranchID implementa o método FindByBranchID da interface
func (m *MockMaintenanceRepository) FindByBranchID(branchID uint) ([]models.MaintenanceOrder, error) {
	args := m.Called(branchID)
	return args.Get(0).([]models.MaintenanceOrder), args.Error(1)
}
