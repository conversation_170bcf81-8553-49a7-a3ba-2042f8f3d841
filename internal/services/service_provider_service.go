package services

import (
	"crypto/rand"
	"errors"
	"math/big"
	"time"

	"tradicao/internal/models"
	"tradicao/internal/repository"

	"golang.org/x/crypto/bcrypt"
)

// ServiceProviderService gerencia a lógica de negócios para prestadores
type ServiceProviderService struct {
	providerRepo repository.ServiceProviderRepository
	userRepo     repository.UserRepository
	managerRepo  repository.ServiceProviderManagerRepository
	emailService EmailService
	uploadService *FileUploadService
}

// NewServiceProviderService cria um novo ServiceProviderService
func NewServiceProviderService(
	providerRepo repository.ServiceProviderRepository,
	userRepo repository.UserRepository,
	managerRepo repository.ServiceProviderManagerRepository,
	emailService EmailService,
	uploadService *FileUploadService,
) *ServiceProviderService {
	return &ServiceProviderService{
		providerRepo:  providerRepo,
		userRepo:      userRepo,
		managerRepo:   managerRepo,
		emailService:  emailService,
		uploadService: uploadService,
	}
}

// FindAll retorna todos os prestadores
func (s *ServiceProviderService) FindAll() ([]models.ServiceProvider, error) {
	return s.providerRepo.FindAll()
}

// FindByID retorna um prestador pelo ID
func (s *ServiceProviderService) FindByID(id uint) (models.ServiceProvider, error) {
	return s.providerRepo.FindByID(id)
}

// Create cria um novo prestador
func (s *ServiceProviderService) Create(provider *models.ServiceProvider) (models.ServiceProvider, error) {
	return s.providerRepo.Create(*provider)
}

// Update atualiza um prestador existente
func (s *ServiceProviderService) Update(provider *models.ServiceProvider) (models.ServiceProvider, error) {
	return s.providerRepo.Update(*provider)
}

// Delete remove um prestador
func (s *ServiceProviderService) Delete(id uint) error {
	return s.providerRepo.Delete(id)
}

// GetTechnicians retorna todos os técnicos de um prestador
func (s *ServiceProviderService) GetTechnicians(providerID uint) ([]models.User, error) {
	return s.providerRepo.GetTechnicians(providerID)
}

// AddTechnician adiciona um técnico a um prestador com geração de senha temporária
func (s *ServiceProviderService) AddTechnician(providerID uint, technicianData models.TechnicianRegistration) (*models.User, string, error) {
	// Verificar se o email já está em uso
	existingUser, err := s.userRepo.FindByEmail(technicianData.Email)
	if err == nil && existingUser.ID > 0 {
		return nil, "", errors.New("email já está em uso")
	}
	
	// Gerar senha temporária aleatória
	tempPassword := generateSecurePassword(12) // 12 caracteres aleatórios
	
	// Hash da senha para armazenamento
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(tempPassword), bcrypt.DefaultCost)
	if err != nil {
		return nil, "", err
	}
	
	// Criar novo usuário
	now := time.Now()
	role := "technician"
	newUser := models.User{
		Name:                technicianData.Name,
		Email:               technicianData.Email,
		Password:            string(hashedPassword),
		Role:                models.UserRole(role),
		ServiceProviderID:   &providerID,
		Phone:               technicianData.Phone,
		ForcePasswordChange: true,
		LastPasswordChange:  &now,
		CreatedAt:           now,
		UpdatedAt:           now,
	}
	
	// Salvar no banco de dados
	createdUser, err := s.userRepo.Create(newUser)
	if err != nil {
		return nil, "", err
	}
	
	// Enviar email com credenciais temporárias
	if err := s.emailService.SendTechnicianInvitation(newUser.Email, newUser.Name, tempPassword); err != nil {
		// Não falhar a operação se apenas o email falhar
		// Apenas registrar o erro
	}
	
	return &createdUser, tempPassword, nil
}

// RemoveTechnician remove um técnico de um prestador
func (s *ServiceProviderService) RemoveTechnician(providerID uint, userID uint) error {
	// Verificar se o técnico pertence a este prestador
	user, err := s.userRepo.FindByID(userID)
	if err != nil {
		return err
	}
	
	if user.ServiceProviderID == nil || *user.ServiceProviderID != providerID {
		return errors.New("técnico não pertence a este prestador")
	}
	
	return s.providerRepo.RemoveTechnician(userID)
}

// GetManagers retorna todos os gestores de um prestador
func (s *ServiceProviderService) GetManagers(providerID uint) ([]models.ServiceProviderManager, error) {
	return s.managerRepo.FindByProviderID(providerID)
}

// AddManager adiciona um gestor a um prestador
func (s *ServiceProviderService) AddManager(manager *models.ServiceProviderManager) error {
	// Verificar se o usuário existe
	_, err := s.userRepo.FindByID(manager.UserID)
	if err != nil {
		return err
	}
	
	// Verificar se o prestador existe
	_, err = s.providerRepo.FindByID(manager.ServiceProviderID)
	if err != nil {
		return err
	}
	
	// Verificar se já existe um gestor com este usuário e prestador
	isManager, err := s.managerRepo.IsProviderManager(manager.ServiceProviderID, manager.UserID)
	if err != nil {
		return err
	}
	
	if isManager {
		return errors.New("usuário já é gestor deste prestador")
	}
	
	// Definir timestamps
	manager.CreatedAt = time.Now()
	manager.UpdatedAt = time.Now()
	
	return s.managerRepo.Create(manager)
}

// RemoveManager remove um gestor de um prestador
func (s *ServiceProviderService) RemoveManager(managerID uint) error {
	return s.managerRepo.Delete(managerID)
}

// UpdateProviderLogo atualiza a URL da logo de um prestador
func (s *ServiceProviderService) UpdateProviderLogo(providerID uint, logoURL string) error {
	return s.providerRepo.UpdateLogo(providerID, logoURL)
}

// IsProviderManager verifica se um usuário é gestor de um prestador
func (s *ServiceProviderService) IsProviderManager(providerID uint, userID uint) (bool, error) {
	return s.managerRepo.IsProviderManager(providerID, userID)
}

// Função auxiliar para gerar senha segura
func generateSecurePassword(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()-_=+"
	password := make([]byte, length)
	for i := range password {
		n, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		password[i] = charset[n.Int64()]
	}
	return string(password)
}
