package handlers

import (
	"net/http"
	"strconv"
	"tradicao/internal/database"
	"tradicao/internal/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Estrutura para resposta da API
type DocumentoResponse struct {
	Success     bool        `json:"success"`
	Message     string      `json:"message,omitempty"`
	Data        interface{} `json:"data,omitempty"`
	Title       string      `json:"title,omitempty"`
	Description string      `json:"description,omitempty"`
	Content     string      `json:"content,omitempty"`
}

// GetDocumentoHandler retorna os detalhes de um documento específico
func GetDocumentoHandler(c *gin.Context) {
	// Obter ID do documento
	documentoID := c.Param("id")
	id, err := strconv.Atoi(documentoID)
	if err != nil {
		c.JSON(http.StatusBadRequest, DocumentoResponse{
			Success: false,
			Message: "ID de documento inválido",
		})
		return
	}

	// Obter conexão com o banco de dados
	db := database.GetGormDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, DocumentoResponse{
			Success: false,
			Message: "Erro ao conectar ao banco de dados",
		})
		return
	}

	// Buscar documento no banco de dados
	var documento models.Documento
	result := db.First(&documento, id)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, DocumentoResponse{
				Success: false,
				Message: "Documento não encontrado",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, DocumentoResponse{
			Success: false,
			Message: "Erro ao buscar documento: " + result.Error.Error(),
		})
		return
	}

	// Verificar permissões do usuário para acessar este documento
	// Aqui você pode implementar lógica de verificação de permissões
	// Por exemplo, verificar se o usuário tem acesso à ordem de serviço relacionada ao documento

	// Retornar documento
	c.JSON(http.StatusOK, DocumentoResponse{
		Success:     true,
		Title:       documento.Title,
		Description: documento.Description,
		Content:     documento.Content,
	})
}
