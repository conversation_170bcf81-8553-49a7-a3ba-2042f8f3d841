package handlers

import (
	"log"
	"net/http"
	"strconv"

	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// FilialFilteredAPIHandler gerencia as requisições da API relacionadas a filiais com filtro de permissões
type FilialFilteredAPIHandler struct {
	branchService       *services.BranchService
	filialFilterService *services.FilialFilterService
}

// NewFilialFilteredAPIHandler cria um novo handler para a API de filiais com filtro de permissões
func NewFilialFilteredAPIHandler(branchService *services.BranchService, database *gorm.DB) *FilialFilteredAPIHandler {
	return &FilialFilteredAPIHandler{
		branchService:       branchService,
		filialFilterService: services.NewFilialFilterService(database),
	}
}

// GetAllFiliais retorna todas as filiais que o usuário tem permissão para acessar
func (h *FilialFilteredAPIHandler) GetAllFiliais(c *gin.Context) {
	// Obter informações do usuário do contexto
	userID, _ := c.Get("userID")
	userRole, _ := c.Get("userRole")

	// Converter userID para uint
	var userIDUint uint
	if id, ok := userID.(uint); ok {
		userIDUint = id
	} else if id, ok := userID.(int); ok {
		userIDUint = uint(id)
	}

	// Converter userRole para string
	var userRoleStr string
	if role, ok := userRole.(string); ok {
		userRoleStr = role
	}

	// Usar o serviço de filtragem para obter filiais autorizadas
	var filiais []models.Branch
	var err error

	if h.filialFilterService != nil {
		filiais, err = h.filialFilterService.GetAuthorizedFiliais(userIDUint, userRoleStr)
		if err != nil {
			log.Printf("Erro ao buscar filiais autorizadas: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar filiais: " + err.Error()})
			return
		}
	} else {
		// Fallback para o método antigo se o serviço não estiver disponível
		// Usar parâmetros padrão para GetAllBranches
		branches, _, errAntigo := h.branchService.GetAllBranches(1, 1000, "", nil)
		if errAntigo != nil {
			log.Printf("Erro ao buscar filiais: %v", errAntigo)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar filiais: " + errAntigo.Error()})
			return
		}

		// Usar diretamente as branches
		filiais = branches
	}

	c.JSON(http.StatusOK, filiais)
}

// GetFilialByID retorna uma filial pelo ID se o usuário tiver permissão para acessá-la
func (h *FilialFilteredAPIHandler) GetFilialByID(c *gin.Context) {
	// Obter informações do usuário do contexto
	userID, _ := c.Get("userID")
	userRole, _ := c.Get("userRole")

	// Converter userID para uint
	var userIDUint uint
	if id, ok := userID.(uint); ok {
		userIDUint = id
	} else if id, ok := userID.(int); ok {
		userIDUint = uint(id)
	}

	// Converter userRole para string
	var userRoleStr string
	if role, ok := userRole.(string); ok {
		userRoleStr = role
	}

	// Obter ID da filial da URL
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Verificar se o usuário tem permissão para acessar esta filial
	if h.filialFilterService != nil {
		authorized, err := h.filialFilterService.IsFilialAuthorized(userIDUint, userRoleStr, uint(id))
		if err != nil {
			log.Printf("Erro ao verificar permissão para filial: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao verificar permissões: " + err.Error()})
			return
		}

		if !authorized {
			c.JSON(http.StatusForbidden, gin.H{"error": "Sem permissão para acessar esta filial"})
			return
		}
	}

	// Buscar a filial
	filial, err := h.branchService.GetBranchByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar filial: " + err.Error()})
		return
	}
	if filial == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Filial não encontrada"})
		return
	}

	c.JSON(http.StatusOK, filial)
}

// GetEquipmentsByFilial retorna os equipamentos de uma filial se o usuário tiver permissão para acessá-la
func (h *FilialFilteredAPIHandler) GetEquipmentsByFilial(c *gin.Context) {
	// Obter informações do usuário do contexto
	userID, _ := c.Get("userID")
	userRole, _ := c.Get("userRole")

	// Converter userID para uint
	var userIDUint uint
	if id, ok := userID.(uint); ok {
		userIDUint = id
	} else if id, ok := userID.(int); ok {
		userIDUint = uint(id)
	}

	// Converter userRole para string
	var userRoleStr string
	if role, ok := userRole.(string); ok {
		userRoleStr = role
	}

	// Obter ID da filial da URL
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Verificar se o usuário tem permissão para acessar esta filial
	if h.filialFilterService != nil {
		authorized, err := h.filialFilterService.IsFilialAuthorized(userIDUint, userRoleStr, uint(id))
		if err != nil {
			log.Printf("Erro ao verificar permissão para filial: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao verificar permissões: " + err.Error()})
			return
		}

		if !authorized {
			c.JSON(http.StatusForbidden, gin.H{"error": "Sem permissão para acessar esta filial"})
			return
		}
	}

	// Buscar os equipamentos da filial
	// Aqui você chamaria o serviço de equipamentos para buscar os equipamentos da filial
	// Como não temos acesso direto a esse serviço, vamos retornar um array vazio
	c.JSON(http.StatusOK, []gin.H{})
}
