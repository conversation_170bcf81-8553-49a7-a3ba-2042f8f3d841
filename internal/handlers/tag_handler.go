package handlers

import (
	"net/http"
	"strconv"
	"tradicao/internal/models"
	"tradicao/internal/repository"

	"github.com/gin-gonic/gin"
)

type TagHandler struct {
	tagRepo repository.TagRepository
}

func NewTagHandler(tagRepo repository.TagRepository) *TagHandler {
	return &TagHandler{
		tagRepo: tagRepo,
	}
}

// RenderTagPage renderiza a página principal de tags
func (h *TagHandler) RenderTagPage(c *gin.Context) {
	categories, err := h.tagRepo.ListCategories(c.Request.Context())
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Erro ao carregar categorias",
		})
		return
	}

	tags, err := h.tagRepo.ListTags(c.Request.Context(), nil)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Erro ao carregar tags",
		})
		return
	}

	c.HTML(http.StatusOK, "tags/index.html", gin.H{
		"Categories": categories,
		"Tags":       tags,
	})
}

// ListCategories lista todas as categorias de tags
func (h *TagHandler) ListCategories(c *gin.Context) {
	categories, err := h.tagRepo.ListCategories(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, categories)
}

// CreateCategory cria uma nova categoria
func (h *TagHandler) CreateCategory(c *gin.Context) {
	var category models.TagCategory
	if err := c.ShouldBindJSON(&category); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.tagRepo.CreateCategory(c.Request.Context(), &category); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao criar categoria"})
		return
	}

	c.JSON(http.StatusCreated, category)
}

// UpdateCategory atualiza uma categoria existente
func (h *TagHandler) UpdateCategory(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var category models.TagCategory
	if err := c.ShouldBindJSON(&category); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	category.ID = id
	if err := h.tagRepo.UpdateCategory(c.Request.Context(), &category); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar categoria"})
		return
	}

	c.JSON(http.StatusOK, category)
}

// DeleteCategory remove uma categoria
func (h *TagHandler) DeleteCategory(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	if err := h.tagRepo.DeleteCategory(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao excluir categoria"})
		return
	}

	c.Status(http.StatusNoContent)
}

// CreateTag cria uma nova tag
func (h *TagHandler) CreateTag(c *gin.Context) {
	var tag models.Tag
	if err := c.ShouldBindJSON(&tag); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.tagRepo.CreateTag(c.Request.Context(), &tag); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao criar tag"})
		return
	}

	c.JSON(http.StatusCreated, tag)
}

// UpdateTag atualiza uma tag existente
func (h *TagHandler) UpdateTag(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var tag models.Tag
	if err := c.ShouldBindJSON(&tag); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	tag.ID = id
	if err := h.tagRepo.UpdateTag(c.Request.Context(), &tag); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar tag"})
		return
	}

	c.JSON(http.StatusOK, tag)
}

// DeleteTag remove uma tag
func (h *TagHandler) DeleteTag(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	if err := h.tagRepo.DeleteTag(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao excluir tag"})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetTag retorna uma tag específica
func (h *TagHandler) GetTag(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	tag, err := h.tagRepo.GetTag(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Tag não encontrada"})
		return
	}

	c.JSON(http.StatusOK, tag)
}

// ListTags retorna uma lista de tags, opcionalmente filtrada por categoria
func (h *TagHandler) ListTags(c *gin.Context) {
	categoryID := c.Query("category_id")
	var categoryIDPtr *int64

	if categoryID != "" {
		id, err := strconv.ParseInt(categoryID, 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "ID de categoria inválido"})
			return
		}
		categoryIDPtr = &id
	}

	tags, err := h.tagRepo.ListTags(c.Request.Context(), categoryIDPtr)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao listar tags"})
		return
	}

	c.JSON(http.StatusOK, tags)
}
