package handlers

import (
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// MaintenanceTimerHandler maneja operações relacionadas aos timers de manutenção
type MaintenanceTimerHandler struct {
	DB *sql.DB
}

// TimerStartRequest representa o corpo do request para iniciar um timer
type TimerStartRequest struct {
	OrderID       int    `json:"order_id" binding:"required"`
	EquipmentType string `json:"equipment_type" binding:"required"`
	BranchID      int    `json:"branch_id" binding:"required"`
}

// MaintenanceTimer representa um timer de manutenção
type MaintenanceTimer struct {
	ID                 int
	BranchID           int
	EquipmentType      string
	MaintenanceOrderID int
	MaintenanceTitle   string
	StartTime          time.Time
	EndTime            *time.Time
	TotalSeconds       int
	IsActive           bool
	CreatedAt          time.Time
	UpdatedAt          time.Time
	FormattedDuration  string // Campo calculado
}

// BranchMaintenanceStats representa estatísticas de manutenção de uma filial
type BranchMaintenanceStats struct {
	BranchID              int
	EquipmentType         string
	TotalMaintenanceTime  int
	TotalMaintenanceCount int
	AvgMaintenanceTime    int
	LastUpdated           time.Time
	FormattedTotalTime    string // Campo calculado
	FormattedAvgTime      string // Campo calculado
}

// GetActiveTimersByBranch retorna todos os timers ativos para uma filial
func (h *MaintenanceTimerHandler) GetActiveTimersByBranch(c *gin.Context) {
	branchID, err := strconv.Atoi(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de filial inválido"})
		return
	}

	query := `
        SELECT 
            mt.id, 
            mt.branch_id, 
            mt.equipment_type,
            mt.maintenance_order_id,
            mo.title AS maintenance_title,
            mt.start_time,
            mt.end_time,
            mt.total_seconds,
            mt.is_active,
            mt.created_at,
            mt.updated_at
        FROM 
            maintenance_timers mt
        JOIN 
            maintenance_orders mo ON mt.maintenance_order_id = mo.id
        WHERE 
            mt.branch_id = $1 AND mt.is_active = true
        ORDER BY 
            mt.start_time DESC
    `

	rows, err := h.DB.Query(query, branchID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao consultar timers: " + err.Error()})
		return
	}
	defer rows.Close()

	var timers []MaintenanceTimer
	for rows.Next() {
		var timer MaintenanceTimer
		var endTime sql.NullTime

		err := rows.Scan(
			&timer.ID,
			&timer.BranchID,
			&timer.EquipmentType,
			&timer.MaintenanceOrderID,
			&timer.MaintenanceTitle,
			&timer.StartTime,
			&endTime,
			&timer.TotalSeconds,
			&timer.IsActive,
			&timer.CreatedAt,
			&timer.UpdatedAt,
		)

		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao processar dados: " + err.Error()})
			return
		}

		if endTime.Valid {
			timer.EndTime = &endTime.Time

			// Calcular a duração formatada
			duration := timer.TotalSeconds
			hours := duration / 3600
			minutes := (duration % 3600) / 60
			seconds := duration % 60

			timer.FormattedDuration = fmt.Sprintf("%02d:%02d:%02d", hours, minutes, seconds)
		} else {
			timer.FormattedDuration = "Em andamento"
		}

		timers = append(timers, timer)
	}

	c.JSON(http.StatusOK, gin.H{"timers": timers})
}

// GetRecentTimersByBranch retorna os timers recentes completados para uma filial
func (h *MaintenanceTimerHandler) GetRecentTimersByBranch(c *gin.Context) {
	branchID, err := strconv.Atoi(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de filial inválido"})
		return
	}

	query := `
        SELECT 
            mt.id, 
            mt.branch_id, 
            mt.equipment_type,
            mt.maintenance_order_id,
            mo.title AS maintenance_title,
            mt.start_time,
            mt.end_time,
            mt.total_seconds,
            mt.is_active,
            mt.created_at,
            mt.updated_at
        FROM 
            maintenance_timers mt
        JOIN 
            maintenance_orders mo ON mt.maintenance_order_id = mo.id
        WHERE 
            mt.branch_id = $1 AND mt.is_active = false
        ORDER BY 
            mt.end_time DESC
        LIMIT 5
    `

	rows, err := h.DB.Query(query, branchID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao consultar timers: " + err.Error()})
		return
	}
	defer rows.Close()

	var timers []MaintenanceTimer
	for rows.Next() {
		var timer MaintenanceTimer
		var endTime sql.NullTime

		err := rows.Scan(
			&timer.ID,
			&timer.BranchID,
			&timer.EquipmentType,
			&timer.MaintenanceOrderID,
			&timer.MaintenanceTitle,
			&timer.StartTime,
			&endTime,
			&timer.TotalSeconds,
			&timer.IsActive,
			&timer.CreatedAt,
			&timer.UpdatedAt,
		)

		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao processar dados: " + err.Error()})
			return
		}

		if endTime.Valid {
			timer.EndTime = &endTime.Time

			// Calcular a duração formatada
			duration := timer.TotalSeconds
			hours := duration / 3600
			minutes := (duration % 3600) / 60
			seconds := duration % 60

			timer.FormattedDuration = fmt.Sprintf("%02d:%02d:%02d", hours, minutes, seconds)
		} else {
			timer.FormattedDuration = "Em andamento"
		}

		timers = append(timers, timer)
	}

	c.JSON(http.StatusOK, gin.H{"timers": timers})
}

// GetBranchMaintenanceStats retorna estatísticas de manutenção para uma filial
func (h *MaintenanceTimerHandler) GetBranchMaintenanceStats(c *gin.Context) {
	branchID, err := strconv.Atoi(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de filial inválido"})
		return
	}

	query := `
        SELECT 
            id,
            branch_id,
            equipment_type,
            total_maintenance_time,
            total_maintenance_count,
            avg_maintenance_time,
            last_updated
        FROM 
            branch_maintenance_stats
        WHERE 
            branch_id = $1
        ORDER BY 
            last_updated DESC
    `

	rows, err := h.DB.Query(query, branchID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao consultar estatísticas: " + err.Error()})
		return
	}
	defer rows.Close()

	var stats []BranchMaintenanceStats
	for rows.Next() {
		var stat BranchMaintenanceStats

		err := rows.Scan(
			&stat.BranchID,
			&stat.EquipmentType,
			&stat.TotalMaintenanceTime,
			&stat.TotalMaintenanceCount,
			&stat.AvgMaintenanceTime,
			&stat.LastUpdated,
		)

		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao processar dados: " + err.Error()})
			return
		}

		// Formatar tempos para exibição
		totalHours := stat.TotalMaintenanceTime / 3600
		totalMinutes := (stat.TotalMaintenanceTime % 3600) / 60

		avgHours := stat.AvgMaintenanceTime / 3600
		avgMinutes := (stat.AvgMaintenanceTime % 3600) / 60

		stat.FormattedTotalTime = fmt.Sprintf("%dh %dm", totalHours, totalMinutes)
		stat.FormattedAvgTime = fmt.Sprintf("%dh %dm", avgHours, avgMinutes)

		stats = append(stats, stat)
	}

	c.JSON(http.StatusOK, gin.H{"stats": stats})
}

// StartTimer inicia um novo timer de manutenção
func (h *MaintenanceTimerHandler) StartTimer(c *gin.Context) {
	var request TimerStartRequest

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos: " + err.Error()})
		return
	}

	// Verificar se já existe um timer ativo para esta ordem
	var existingTimerID int
	query := `SELECT id FROM maintenance_timers WHERE maintenance_order_id = $1 AND is_active = true`
	err := h.DB.QueryRow(query, request.OrderID).Scan(&existingTimerID)

	if err == nil {
		// Já existe um timer ativo
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Já existe um timer ativo para esta ordem de manutenção",
		})
		return
	} else if err != sql.ErrNoRows {
		// Erro na consulta
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao verificar timers existentes: " + err.Error(),
		})
		return
	}

	// Criar novo timer
	now := time.Now()

	query = `
		INSERT INTO maintenance_timers 
		(branch_id, equipment_type, maintenance_order_id, start_time, is_active, created_at, updated_at)
		VALUES ($1, $2, $3, $4, true, $5, $5)
		RETURNING id
	`

	var timerID int
	err = h.DB.QueryRow(
		query,
		request.BranchID,
		request.EquipmentType,
		request.OrderID,
		now,
		now,
	).Scan(&timerID)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao criar timer: " + err.Error(),
		})
		return
	}

	// Atualizar o status da filial para "maintenance"
	updateQuery := `
		UPDATE branches SET status = 'maintenance', updated_at = $1 WHERE id = $2
	`
	_, err = h.DB.Exec(updateQuery, now, request.BranchID)

	if err != nil {
		log.Printf("Erro ao atualizar status da filial: %v", err)
		// Continuar mesmo com erro (não crítico)
	}

	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"message":  "Timer iniciado com sucesso",
		"timer_id": timerID,
	})
}

// StopTimer para um timer de manutenção ativo
func (h *MaintenanceTimerHandler) StopTimer(c *gin.Context) {
	timerID, err := strconv.Atoi(c.Param("timerId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de timer inválido"})
		return
	}

	// Verificar se o timer existe e está ativo
	var branchID int
	var equipmentType string
	var startTime time.Time

	query := `
		SELECT branch_id, equipment_type, start_time 
		FROM maintenance_timers 
		WHERE id = $1 AND is_active = true
	`

	err = h.DB.QueryRow(query, timerID).Scan(&branchID, &equipmentType, &startTime)

	if err == sql.ErrNoRows {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Timer não encontrado ou já finalizado",
		})
		return
	} else if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao verificar timer: " + err.Error(),
		})
		return
	}

	// Calcular tempo total
	now := time.Now()
	totalSeconds := int(now.Sub(startTime).Seconds())

	// Atualizar o timer
	updateQuery := `
		UPDATE maintenance_timers
		SET end_time = $1, total_seconds = $2, is_active = false, updated_at = $1
		WHERE id = $3
	`

	_, err = h.DB.Exec(updateQuery, now, totalSeconds, timerID)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao finalizar timer: " + err.Error(),
		})
		return
	}

	// Atualizar estatísticas de manutenção
	// Primeiro, verificar se já existe estatística para este tipo de equipamento
	var statsID int
	var currentTotal, currentCount int

	statsQuery := `
		SELECT id, total_maintenance_time, total_maintenance_count
		FROM branch_maintenance_stats
		WHERE branch_id = $1 AND equipment_type = $2
	`

	err = h.DB.QueryRow(statsQuery, branchID, equipmentType).Scan(&statsID, &currentTotal, &currentCount)

	if err == sql.ErrNoRows {
		// Criar novo registro de estatísticas
		insertStatsQuery := `
			INSERT INTO branch_maintenance_stats
			(branch_id, equipment_type, total_maintenance_time, total_maintenance_count, avg_maintenance_time, last_updated)
			VALUES ($1, $2, $3, 1, $3, $4)
		`

		_, err = h.DB.Exec(insertStatsQuery, branchID, equipmentType, totalSeconds, now)

		if err != nil {
			log.Printf("Erro ao criar estatísticas: %v", err)
			// Continuar mesmo com erro (não crítico)
		}
	} else if err == nil {
		// Atualizar estatísticas existentes
		newTotal := currentTotal + totalSeconds
		newCount := currentCount + 1
		newAvg := newTotal / newCount

		updateStatsQuery := `
			UPDATE branch_maintenance_stats
			SET total_maintenance_time = $1, 
				total_maintenance_count = $2,
				avg_maintenance_time = $3,
				last_updated = $4
			WHERE id = $5
		`

		_, err = h.DB.Exec(updateStatsQuery, newTotal, newCount, newAvg, now, statsID)

		if err != nil {
			log.Printf("Erro ao atualizar estatísticas: %v", err)
			// Continuar mesmo com erro (não crítico)
		}
	} else {
		log.Printf("Erro ao consultar estatísticas: %v", err)
		// Continuar mesmo com erro (não crítico)
	}

	// Verificar se ainda há timers ativos para esta filial
	var activeTimerCount int
	countQuery := `
		SELECT COUNT(*) FROM maintenance_timers 
		WHERE branch_id = $1 AND is_active = true
	`

	err = h.DB.QueryRow(countQuery, branchID).Scan(&activeTimerCount)

	if err != nil {
		log.Printf("Erro ao contar timers ativos: %v", err)
		// Continuar mesmo com erro (não crítico)
	} else if activeTimerCount == 0 {
		// Se não houver mais timers ativos, atualizar o status da filial de volta para "active"
		updateBranchQuery := `
			UPDATE branches SET status = 'active', updated_at = $1 WHERE id = $2
		`
		_, err = h.DB.Exec(updateBranchQuery, now, branchID)

		if err != nil {
			log.Printf("Erro ao atualizar status da filial: %v", err)
			// Continuar mesmo com erro (não crítico)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success":        true,
		"message":        "Timer finalizado com sucesso",
		"total_seconds":  totalSeconds,
		"formatted_time": formatDuration(totalSeconds),
	})
}

// GetBranchStatus retorna o status atual de uma filial
func (h *MaintenanceTimerHandler) GetBranchStatus(c *gin.Context) {
	branchID, err := strconv.Atoi(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de filial inválido"})
		return
	}

	var status string
	query := `SELECT status FROM branches WHERE id = $1`

	err = h.DB.QueryRow(query, branchID).Scan(&status)

	if err == sql.ErrNoRows {
		c.JSON(http.StatusNotFound, gin.H{"error": "Filial não encontrada"})
		return
	} else if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao consultar status: " + err.Error()})
		return
	}

	if status == "" {
		status = "active" // Status padrão se não estiver definido
	}

	c.JSON(http.StatusOK, gin.H{"status": status})
}

// Função utilitária para formatar duração em segundos para HH:MM:SS
func formatDuration(seconds int) string {
	hours := seconds / 3600
	minutes := (seconds % 3600) / 60
	secs := seconds % 60

	return fmt.Sprintf("%02d:%02d:%02d", hours, minutes, secs)
}
