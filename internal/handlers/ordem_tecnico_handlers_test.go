package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"

	"tradicao/internal/database"
)

// MockDB é um mock para o banco de dados
type MockDB struct {
	mock.Mock
}

// Create é um mock para o método Create do GORM
func (m *MockDB) Create(value interface{}) *gorm.DB {
	m.Called(value)
	return &gorm.DB{Error: nil}
}

// First é um mock para o método First do GORM
func (m *MockDB) First(dest interface{}, conds ...interface{}) *gorm.DB {
	m.Called(dest, conds)
	return &gorm.DB{Error: nil}
}

// Where é um mock para o método Where do GORM
func (m *MockDB) Where(query interface{}, args ...interface{}) *gorm.DB {
	m.Called(query, args)
	return &gorm.DB{Error: nil}
}

// Order é um mock para o método Order do GORM
func (m *MockDB) Order(value interface{}) *gorm.DB {
	m.Called(value)
	return &gorm.DB{Error: nil}
}

// Find é um mock para o método Find do GORM
func (m *MockDB) Find(dest interface{}, conds ...interface{}) *gorm.DB {
	m.Called(dest, conds)
	return &gorm.DB{Error: nil}
}

// Configurar o mock do banco de dados
func setupMockDB() *MockDB {
	mockDB := new(MockDB)
	database.SetTestDB(mockDB)
	return mockDB
}

// Configurar o router para testes
func setupRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	return router
}

// Teste para validação de dados de manutenção
func TestValidacaoManutencaoData(t *testing.T) {
	// Dados válidos
	validData := ManutencaoData{
		OrdemID:         1,
		Descricao:       "Teste de manutenção",
		PecasUtilizadas: "Peças de teste",
		Observacoes:     "Observações de teste",
		TecnicoID:       1,
		TecnicoNome:     "Técnico Teste",
	}

	// Verificar se os dados são válidos
	assert.NotEmpty(t, validData.OrdemID)
	assert.NotEmpty(t, validData.Descricao)
	assert.NotEmpty(t, validData.TecnicoID)
	assert.NotEmpty(t, validData.TecnicoNome)

	// Dados inválidos
	invalidData := ManutencaoData{
		OrdemID:   0,
		Descricao: "",
	}

	// Verificar se os dados são inválidos
	assert.Empty(t, invalidData.Descricao)
	assert.Zero(t, invalidData.OrdemID)
}

// Teste para validação de dados de custos
func TestValidacaoCustosData(t *testing.T) {
	// Dados válidos
	validData := CustosData{
		OrdemID:      1,
		Pecas:        100.0,
		MaoObra:      200.0,
		Deslocamento: 50.0,
		TecnicoID:    1,
		TecnicoNome:  "Técnico Teste",
	}

	// Verificar se os dados são válidos
	assert.NotEmpty(t, validData.OrdemID)
	assert.NotZero(t, validData.Pecas)
	assert.NotZero(t, validData.MaoObra)
	assert.NotZero(t, validData.Deslocamento)
	assert.NotEmpty(t, validData.TecnicoID)
	assert.NotEmpty(t, validData.TecnicoNome)

	// Verificar cálculo do total
	total := validData.Pecas + validData.MaoObra + validData.Deslocamento
	assert.Equal(t, 350.0, total)

	// Dados inválidos
	invalidData := CustosData{
		OrdemID: 0,
		Pecas:   -10.0,
	}

	// Verificar se os dados são inválidos
	assert.Zero(t, invalidData.OrdemID)
	assert.True(t, invalidData.Pecas < 0)
}

// Teste para SaveCronogramaHandler
func TestSaveCronogramaHandler(t *testing.T) {
	// Configurar o mock do banco de dados
	mockDB := setupMockDB()

	// Configurar o router
	router := setupRouter()
	router.POST("/api/ordens/:id/cronograma", func(c *gin.Context) {
		// Simular autenticação
		c.Set("userID", "1")
		c.Set("userName", "Técnico Teste")
		c.Set("userRole", "tecnico")
		SaveCronogramaHandler(c)
	})

	// Dados para o teste
	cronogramaData := CronogramaData{
		OrdemID:     1,
		DataInicio:  "2025-01-01",
		HoraInicio:  "08:00",
		DataFim:     "2025-01-01",
		HoraFim:     "12:00",
		Status:      "Em andamento",
		TecnicoID:   1,
		TecnicoNome: "Técnico Teste",
	}

	// Converter para JSON
	jsonData, _ := json.Marshal(cronogramaData)

	// Configurar o mock para retornar sucesso
	mockDB.On("Create", mock.AnythingOfType("*models.CronogramaData")).Return(&gorm.DB{Error: nil})
	mockDB.On("First", mock.AnythingOfType("*models.User"), mock.Anything).Return(&gorm.DB{Error: nil})
	mockDB.On("Create", mock.AnythingOfType("*models.HistoricoOrdem")).Return(&gorm.DB{Error: nil})

	// Criar requisição
	req, _ := http.NewRequest("POST", "/api/ordens/1/cronograma", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	// Executar a requisição
	router.ServeHTTP(w, req)

	// Verificar o resultado
	assert.Equal(t, http.StatusOK, w.Code)

	// Verificar a resposta
	var response APIResponse
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.True(t, response.Success)
	assert.Equal(t, "Dados de cronograma salvos com sucesso", response.Message)

	// Verificar se o mock foi chamado corretamente
	mockDB.AssertExpectations(t)
}

// Teste para SaveChatMessageHandler
func TestSaveChatMessageHandler(t *testing.T) {
	// Configurar o mock do banco de dados
	mockDB := setupMockDB()

	// Configurar o router
	router := setupRouter()
	router.POST("/api/ordens/:id/chat", func(c *gin.Context) {
		// Simular autenticação
		c.Set("userID", "1")
		c.Set("userName", "Técnico Teste")
		c.Set("userRole", "tecnico")
		SaveChatMessageHandler(c)
	})

	// Dados para o teste
	mensagemData := MensagemChat{
		OrdemID:     1,
		Mensagem:    "Mensagem de teste",
		Remetente:   "Técnico Teste",
		RemetenteID: 1,
		DataEnvio:   time.Now(),
	}

	// Converter para JSON
	jsonData, _ := json.Marshal(mensagemData)

	// Configurar o mock para retornar sucesso
	mockDB.On("Create", mock.AnythingOfType("*models.MensagemChat")).Return(&gorm.DB{Error: nil})

	// Criar requisição
	req, _ := http.NewRequest("POST", "/api/ordens/1/chat", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	// Executar a requisição
	router.ServeHTTP(w, req)

	// Verificar o resultado
	assert.Equal(t, http.StatusOK, w.Code)

	// Verificar a resposta
	var response APIResponse
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.True(t, response.Success)
	assert.Equal(t, "Mensagem enviada com sucesso", response.Message)

	// Verificar se o mock foi chamado corretamente
	mockDB.AssertExpectations(t)
}

// Teste para GetOrdemHandler
func TestGetOrdemHandler(t *testing.T) {
	// Configurar o mock do banco de dados
	mockDB := setupMockDB()

	// Configurar o router
	router := setupRouter()
	router.GET("/api/ordens/:id", func(c *gin.Context) {
		// Simular autenticação
		c.Set("userID", "1")
		c.Set("userName", "Técnico Teste")
		c.Set("userRole", "tecnico")
		GetOrdemHandler(c)
	})

	// Configurar o mock para retornar uma ordem
	mockDB.On("First", mock.AnythingOfType("*models.MaintenanceOrder"), mock.Anything).Return(&gorm.DB{Error: nil})
	mockDB.On("Where", "ordem_id = ?", mock.Anything).Return(mockDB)
	mockDB.On("Order", "created_at DESC").Return(mockDB)
	mockDB.On("First", mock.AnythingOfType("*models.ManutencaoData"), mock.Anything).Return(&gorm.DB{Error: nil})
	mockDB.On("First", mock.AnythingOfType("*models.CustosData"), mock.Anything).Return(&gorm.DB{Error: nil})
	mockDB.On("First", mock.AnythingOfType("*models.CronogramaData"), mock.Anything).Return(&gorm.DB{Error: nil})
	mockDB.On("Where", "ordem_id = ?", mock.Anything).Return(mockDB)
	mockDB.On("Order", "data_envio ASC").Return(mockDB)
	mockDB.On("Find", mock.AnythingOfType("*[]models.MensagemChat"), mock.Anything).Return(&gorm.DB{Error: nil})

	// Criar requisição
	req, _ := http.NewRequest("GET", "/api/ordens/1", nil)
	w := httptest.NewRecorder()

	// Executar a requisição
	router.ServeHTTP(w, req)

	// Verificar o resultado
	assert.Equal(t, http.StatusOK, w.Code)

	// Verificar a resposta
	var response APIResponse
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.True(t, response.Success)
	assert.Equal(t, "Dados da ordem obtidos com sucesso", response.Message)

	// Verificar se o mock foi chamado corretamente
	mockDB.AssertExpectations(t)
}
