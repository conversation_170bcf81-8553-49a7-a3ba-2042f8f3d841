package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"tradicao/internal/database"
	"tradicao/internal/models"
	"tradicao/internal/notifications"

	"github.com/gin-gonic/gin"
)

// AtribuirOrdemHandler atribui uma ordem específica a um técnico específico
func AtribuirOrdemHandler(c *gin.Context) {
	// Obter parâmetros da requisição
	ordemIDStr := c.<PERSON>("ordem_id")
	tecnicoIDStr := c.Query("tecnico_id")

	// Validar parâmetros
	if ordemIDStr == "" || tecnicoIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Parâmetros ordem_id e tecnico_id são obrigatórios",
		})
		return
	}

	// Converter para inteiros
	ordemID, err := strconv.ParseUint(ordemIDStr, 10, 32)
	if err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "ID de ordem inválido: " + err.<PERSON>(),
		})
		return
	}

	tecnicoID, err := strconv.ParseUint(tecnicoIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "ID de técnico inválido: " + err.Error(),
		})
		return
	}

	// Obter conexão com o banco de dados
	db := database.GetGormDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao conectar ao banco de dados",
		})
		return
	}

	// Verificar se a ordem existe
	var ordem models.MaintenanceOrder
	if err := db.First(&ordem, uint(ordemID)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": fmt.Sprintf("Ordem com ID %d não encontrada: %s", ordemID, err.Error()),
		})
		return
	}

	// Verificar se o técnico existe
	var tecnico models.User
	if err := db.First(&tecnico, uint(tecnicoID)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": fmt.Sprintf("Técnico com ID %d não encontrado: %s", tecnicoID, err.Error()),
		})
		return
	}

	// Verificar se o usuário existe
	var tecnicoUser models.User
	if err := db.First(&tecnicoUser, uint(tecnicoID)).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": fmt.Sprintf("Usuário com ID %d não foi encontrado: %s", tecnicoID, err.Error()),
		})
		return
	}

	// Apenas para log
	fmt.Printf("Atribuindo ordem %d ao usuário %d (%s)\n", ordemID, tecnicoUser.ID, tecnicoUser.Name)

	// Atribuir o técnico à ordem usando SQL direto
	tecnicoIDUint := uint(tecnicoID)

	// Usar SQL direto para atualizar a ordem
	// Primeiro, verificar quais colunas existem na tabela
	var colunas []string
	if err := db.Raw("SELECT column_name FROM information_schema.columns WHERE table_name = 'maintenance_orders'").Pluck("column_name", &colunas).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao verificar colunas da tabela: " + err.Error(),
		})
		return
	}

	// Mapear as colunas para facilitar a verificação
	colunasMap := make(map[string]bool)
	for _, coluna := range colunas {
		colunasMap[coluna] = true
	}

	// Verificar se a coluna technician_id existe
	if colunasMap["technician_id"] {
		// Usar SQL direto para atualizar a coluna technician_id
		result := db.Exec("UPDATE maintenance_orders SET technician_id = ? WHERE id = ?", tecnicoIDUint, ordem.ID)
		if result.Error != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Erro ao atualizar technician_id: " + result.Error.Error(),
			})
			return
		}
		fmt.Printf("Atualizada coluna technician_id para ordem %d com técnico %d\n", ordem.ID, tecnicoIDUint)
	} else if colunasMap["assigned_to_user_id"] {
		// Usar SQL direto para atualizar a coluna assigned_to_user_id
		result := db.Exec("UPDATE maintenance_orders SET assigned_to_user_id = ? WHERE id = ?", tecnicoIDUint, ordem.ID)
		if result.Error != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Erro ao atualizar assigned_to_user_id: " + result.Error.Error(),
			})
			return
		}
		fmt.Printf("Atualizada coluna assigned_to_user_id para ordem %d com técnico %d\n", ordem.ID, tecnicoIDUint)
	} else {
		// Tentar criar a coluna technician_id
		if err := db.Exec("ALTER TABLE maintenance_orders ADD COLUMN IF NOT EXISTS technician_id INTEGER").Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Erro ao criar coluna technician_id: " + err.Error(),
			})
			return
		}

		// Agora atualizar a coluna recém-criada
		result := db.Exec("UPDATE maintenance_orders SET technician_id = ? WHERE id = ?", tecnicoIDUint, ordem.ID)
		if result.Error != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Erro ao atualizar technician_id após criação: " + result.Error.Error(),
			})
			return
		}
		fmt.Printf("Criada e atualizada coluna technician_id para ordem %d com técnico %d\n", ordem.ID, tecnicoIDUint)
	}

	// Verificar se a coluna service_provider_id existe e atualizar se o técnico tiver um prestador associado
	if colunasMap["service_provider_id"] && tecnicoUser.ServiceProviderID != nil && *tecnicoUser.ServiceProviderID > 0 {
		db.Exec("UPDATE maintenance_orders SET service_provider_id = ? WHERE id = ?", *tecnicoUser.ServiceProviderID, ordem.ID)
		fmt.Printf("Atualizada coluna service_provider_id para ordem %d com prestador %d\n", ordem.ID, *tecnicoUser.ServiceProviderID)
	}

	// Log de sucesso
	fmt.Printf("Ordem %d atribuída com sucesso ao técnico %d (%s)\n", ordem.ID, tecnicoIDUint, tecnicoUser.Name)

	// Enviar notificação ao técnico
	notificationService := notifications.NewService()
	if err := notificationService.NotifyOrderAssigned(ordem.ID, tecnicoIDUint); err != nil {
		// Apenas logar o erro, não impedir a atribuição
		fmt.Printf("Erro ao enviar notificação para o técnico %d: %s\n", tecnicoIDUint, err.Error())
	} else {
		fmt.Printf("Notificação enviada com sucesso para o técnico %d\n", tecnicoIDUint)
	}

	// Se a ordem tiver um prestador, enviar notificação também
	if ordem.ServiceProviderID != nil && *ordem.ServiceProviderID > 0 {
		if err := notificationService.NotifyProviderOrderAssigned(ordem.ID, *ordem.ServiceProviderID); err != nil {
			// Apenas logar o erro, não impedir a atribuição
			fmt.Printf("Erro ao enviar notificação para o prestador %d: %s\n", *ordem.ServiceProviderID, err.Error())
		} else {
			fmt.Printf("Notificação enviada com sucesso para o prestador %d\n", *ordem.ServiceProviderID)
		}
	}

	// Retornar sucesso
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("Ordem %d atribuída com sucesso ao técnico %d", ordemID, tecnicoID),
		"data": gin.H{
			"ordem_id":   ordemID,
			"tecnico_id": tecnicoID,
		},
	})
}
