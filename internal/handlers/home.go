package handlers

import (
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

func HomePage(c *gin.Context) {
	c.HTML(http.StatusOK, "index.html", gin.H{
		"title": "TRADICAO - Sistema de Gestão de Manutenção",
	})
}

// IndexHandler renderiza a página inicial
func IndexHandler(c *gin.Context) {
	log.Println("Tentando renderizar index.html")
	c.HTML(http.StatusOK, "index.html", gin.H{
		"title":      "Rede Tradição",
		"page":       "index",
		"ActivePage": "home",
	})
}

// LoginHandler renderiza a página de login
func LoginHandler(c *gin.Context) {
	log.Println("Tentando renderizar login/login.html")
	// Renderiza diretamente o HTML para evitar problemas com o template
	c.Writer.Header().Set("Content-Type", "text/html; charset=utf-8")
	c.Writer.WriteHeader(http.StatusOK)
	c.Writer.Write([]byte(`<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Rede Tradição Shell</title>
    <link rel="icon" href="/static/images/favicon.ico" type="image/x-icon">

    <!-- Bootstrap e estilos base -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <!-- Fontes do Design System -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&family=Share+Tech+Mono&display=swap" rel="stylesheet">

    <style>
        :root {
            --shell-red: #ED1C24;
            --shell-yellow: #FDB813;
            --shell-dark: #231F20;
            --shell-light: #FFFFFF;
        }

            body {
                background: url("/static/images/image.jpeg") no-repeat center center fixed;
                background-size: cover;
                min-height: 100vh;
                color: white;
                font-family: "Rajdhani", sans-serif;
                display: flex;
                align-items: center;
                justify-content: center;
                overflow-x: hidden;
            }

            .login-container {
                width: 100%;
                max-width: 420px;
                padding: 0 20px;
            }

            .login-card {
                background: rgba(35, 31, 32, 0.85);
                border-radius: 15px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                padding: 40px 30px;
                position: relative;
                overflow: hidden;
                border: 1px solid rgba(253, 184, 19, 0.3);
            }

            .login-card::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 5px;
                background: linear-gradient(
                    90deg,
                    var(--shell-red),
                    var(--shell-yellow)
                );
            }

            .logo-container {
                text-align: center;
                margin-bottom: 30px;
            }

            .logo {
                width: 180px;
                height: auto;
                margin-bottom: 15px;
                filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.3));
            }

            .subtitle {
                font-size: 1.1rem;
                color: rgba(255, 255, 255, 0.8);
                margin-bottom: 25px;
                text-align: center;
                font-weight: 300;
            }

            .form-floating {
                margin-bottom: 20px;
            }

            .form-control {
                background-color: rgba(35, 31, 32, 0.5);
                border: 1px solid rgba(255, 255, 255, 0.2);
                color: white;
                height: 55px;
                padding: 1.2rem 1rem 0.2rem;
            }

            .form-control:focus {
                background-color: rgba(35, 31, 32, 0.7);
                border-color: var(--shell-yellow);
                box-shadow: 0 0 0 0.25rem rgba(253, 184, 19, 0.25);
                color: white;
            }

            .form-floating label {
                color: rgba(255, 255, 255, 0.7);
                padding: 1rem 1rem;
                background-color: transparent !important;
            }

            /* Estilo personalizado dos inputs */
            .input-group {
                border-radius: 8px;
                overflow: hidden;
                border: 1px solid rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
            }

            .input-group:focus-within {
                border-color: var(--shell-yellow);
                box-shadow: 0 0 0 2px rgba(253, 184, 19, 0.2);
            }

            .input-group-text {
                border: none;
                border-radius: 0;
                color: rgba(255, 255, 255, 0.8);
                padding: 0.6rem 1rem;
            }

            .form-control {
                border: none;
                border-radius: 0;
                padding: 0.6rem 1rem;
                height: auto;
                font-size: 1rem;
            }

            .form-control:focus {
                box-shadow: none;
                outline: none;
            }

            .form-control::placeholder {
                color: rgba(255, 255, 255, 0.5);
            }

            .form-floating > .form-control:focus ~ label,
            .form-floating > .form-control:not(:placeholder-shown) ~ label {
                color: rgba(255, 255, 255, 0.9);
                transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
                background-color: transparent;
            }

            .btn-shell {
                background: linear-gradient(
                    45deg,
                    var(--shell-red),
                    var(--shell-yellow)
                );
                color: white;
                border: none;
                padding: 12px 30px;
                border-radius: 28px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                transition: all 0.3s ease;
                width: 100%;
                position: relative;
                overflow: hidden;
                margin-top: 10px;
                box-shadow: 0 4px 15px rgba(237, 28, 36, 0.3);
            }

            .btn-shell:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 18px rgba(237, 28, 36, 0.4);
                color: white;
            }

            .btn-shell:active {
                transform: translateY(1px);
            }

            .btn-shell::after {
                content: "";
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                background: linear-gradient(
                    45deg,
                    var(--shell-yellow),
                    var(--shell-red)
                );
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .btn-shell:hover::after {
                opacity: 1;
            }

            .btn-text {
                position: relative;
                z-index: 1;
            }

            .forgot-password {
                text-align: center;
                margin-top: 20px;
            }

            .forgot-password a {
                color: rgba(255, 255, 255, 0.7);
                text-decoration: none;
                font-size: 0.9rem;
                transition: all 0.3s ease;
            }

            .forgot-password a:hover {
                color: var(--shell-yellow);
            }

            .login-footer {
                text-align: center;
                margin-top: 30px;
                color: rgba(255, 255, 255, 0.5);
                font-size: 0.8rem;
            }

            /* Top corner art */
            .corner-art {
                position: absolute;
                top: -50px;
                right: -50px;
                width: 150px;
                height: 150px;
                background: linear-gradient(
                    135deg,
                    transparent,
                    transparent 50%,
                    var(--shell-red) 50%,
                    var(--shell-yellow)
                );
                transform: rotate(45deg);
                z-index: -1;
                opacity: 0.7;
            }

            /* Shell-themed checkbox */
            .form-check-input {
                background-color: rgba(255, 255, 255, 0.1);
                border-color: rgba(255, 255, 255, 0.3);
            }

            .form-check-input:checked {
                background-color: var(--shell-yellow);
                border-color: var(--shell-yellow);
            }

            .form-check-label {
                color: rgba(255, 255, 255, 0.8);
                font-size: 0.9rem;
            }

            /* Animated highlight effect */
            @keyframes highlight {
                0% {
                    background-position: 0% 50%;
                }
                50% {
                    background-position: 100% 50%;
                }
                100% {
                    background-position: 0% 50%;
                }
            }

            .animated-highlight {
                background: linear-gradient(
                    90deg,
                    var(--shell-red),
                    var(--shell-yellow),
                    var(--shell-red)
                );
                background-size: 200% 200%;
                -webkit-background-clip: text;
                background-clip: text;
                color: transparent;
                font-weight: 700;
                font-size: 2rem;
                animation: highlight 5s ease infinite;
                text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            }

            /* Efeito de rotação para login */
            @keyframes rotate-card {
                0% {
                    transform: perspective(1200px) rotateY(0);
                }
                100% {
                    transform: perspective(1200px) rotateY(360deg);
                }
            }

            .rotate-card {
                animation: rotate-card 1.2s ease-in-out;
                transform-style: preserve-3d;
            }

            .error-message {
                background-color: rgba(237, 28, 36, 0.2);
                border-left: 3px solid var(--shell-red);
                color: white;
                padding: 12px;
                margin-bottom: 20px;
                border-radius: 5px;
                display: none;
            }

            .error-message.show {
                display: block;
            }

            /* Efeito de shake para erros */
            @keyframes shake {
                0%,
                100% {
                    transform: translateX(0);
                }
                10%,
                30%,
                50%,
                70%,
                90% {
                    transform: translateX(-10px);
                }
                20%,
                40%,
                60%,
                80% {
                    transform: translateX(10px);
                }
            }

            .shake {
                animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
            }

            /* Efeito de animação para a logo */
            .animated-logo-container {
                position: relative;
                display: inline-block;
                margin-bottom: 15px;
            }

            .animated-logo {
                position: relative;
                filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.3));
                animation: logoRotate 6s ease-in-out infinite alternate;
                transition: filter 0.3s ease;
            }

            .animated-logo:hover {
                filter: drop-shadow(0 8px 15px rgba(253, 184, 19, 0.7));
                animation: logoRotate 2s ease-in-out infinite alternate;
            }

            @keyframes logoRotate {
                0% {
                    transform: rotate(-5deg) scale(1);
                }
                50% {
                    transform: rotate(5deg) scale(1.05);
                }
                100% {
                    transform: rotate(-5deg) scale(1);
                }
            }
        </style>
    </head>
    <body>
        <div class="login-container">
            <div class="login-card">
                <div class="corner-art"></div>

                <div class="logo-container">
                    <div class="animated-logo-container">
                        <img
                            src="/static/images/logo.png"
                            alt="Shell Logo"
                            class="logo animated-logo"
                        />
                    </div>
                    <p class="subtitle">Sistema de Gestão de Manutenção</p>
                </div>

                <div class="error-message" id="error-message">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <span id="error-text"
                        >Credenciais inválidas. Tente novamente.</span
                    >
                </div>

                <form id="login-form" action="/api/auth/login" method="POST">
                    <div class="mb-4">
                        <div class="input-group">
                            <span
                                class="input-group-text bg-transparent text-light border-end-0"
                            >
                                <i class="fas fa-envelope"></i>
                            </span>
                            <input
                                type="email"
                                class="form-control bg-transparent text-light border-start-0"
                                id="email"
                                name="email"
                                placeholder="Email"
                                required
                            />
                        </div>
                    </div>

                    <div class="mb-4">
                        <div class="input-group">
                            <span
                                class="input-group-text bg-transparent text-light border-end-0"
                            >
                                <i class="fas fa-lock"></i>
                            </span>
                            <input
                                type="password"
                                class="form-control bg-transparent text-light border-start-0"
                                id="password"
                                name="password"
                                placeholder="Senha"
                                required
                            />
                        </div>
                    </div>

                    <button type="submit" class="btn btn-shell">
                        <span class="btn-text">
                            <i class="fas fa-sign-in-alt me-2"></i> Entrar
                        </span>
                    </button>
                </form>

                <div class="mt-3 d-flex justify-content-center">
                    <button type="button" id="auto-login-btn" class="btn btn-sm btn-outline-light">
                        <i class="fas fa-user-shield me-1"></i> Login Automático (Ambiente de Dev)
                    </button>
                </div>

                <div class="login-footer">
                    &copy; 2025 - Rede de Postos Tradição Todos os direitos
                    reservados
                </div>
            </div>
        </div>

        <!-- Bootstrap JS -->
        <script src="/static/js/vendor/bootstrap.bundle.min.js"></script>

        <script>
            document.addEventListener("DOMContentLoaded", function () {
                const loginForm = document.getElementById("login-form");
                const errorMessage = document.getElementById("error-message");
                const errorText = document.getElementById("error-text");
                const loginCard = document.querySelector(".login-card");

                // Adiciona o efeito de rotação ao carregar a página
                loginCard.classList.add("rotate-card");

                loginForm.addEventListener("submit", function (e) {
                    e.preventDefault();

                    const email = document.getElementById("email").value;
                    const password = document.getElementById("password").value;

                    // Fazer a chamada AJAX para autenticar
                    const loginData = {
                        email: email,
                        password: password
                    };

                    fetch("/api/auth/login", {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                            "Accept": "application/json"
                        },
                        body: JSON.stringify(loginData)
                    })
                    .then((response) => {
                        // Primeiro verificamos se a resposta está OK
                        if (!response.ok) {
                            return response.json().then(data => {
                                throw new Error(data.message || "Erro ao fazer login");
                            });
                        }
                        return response.json();
                    })
                    .then((data) => {
                        // Login bem-sucedido, redireciona para a página inicial
                        window.location.href = "/dashboard";
                    })
                    .catch((error) => {
                        // Exibe mensagem de erro
                        errorText.textContent = error.message || "Credenciais inválidas. Tente novamente.";
                        errorMessage.classList.add("show");
                        loginCard.classList.add("shake");

                        // Remove a classe shake após a animação terminar
                        setTimeout(() => {
                            loginCard.classList.remove("shake");
                        }, 500);
                    });
                });

                // Botão de login automático para ambiente de desenvolvimento
                const autoLoginBtn = document.getElementById("auto-login-btn");
                if (autoLoginBtn) {
                    autoLoginBtn.addEventListener("click", function() {
                        const loginData = {
                            email: "<EMAIL>",
                            password: "i1t2@3l4O5"
                        };

                        fetch("/api/auth/login", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                "Accept": "application/json"
                            },
                            body: JSON.stringify(loginData)
                        })
                        .then((response) => {
                            if (!response.ok) {
                                return response.json().then(data => {
                                    throw new Error(data.message || "Erro ao fazer login automático");
                                });
                            }
                            return response.json();
                        })
                        .then((data) => {
                            window.location.href = "/dashboard";
                        })
                        .catch((error) => {
                            errorText.textContent = error.message || "Erro no login automático. Tente manualmente.";
                            errorMessage.classList.add("show");
                        });
                    });
                }
            });
        </script>
    </body>
</html>`))
}
