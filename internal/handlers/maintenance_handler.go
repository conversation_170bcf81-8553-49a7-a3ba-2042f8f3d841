package handlers

import (
	"context"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"tradicao/internal/models"
	"tradicao/internal/services"
)

// MaintenanceHandler representa o handler de manutenção
type MaintenanceHandler struct {
	maintenanceService  services.MaintenanceOrderService
	notificationService *services.NotificationService
	userService         services.UserService
}

// NewMaintenanceHandler cria uma nova instância do handler
func NewMaintenanceHandler(
	maintenanceService services.MaintenanceOrderService,
	notificationService *services.NotificationService,
	userService services.UserService,
) *MaintenanceHandler {
	return &MaintenanceHandler{
		maintenanceService:  maintenanceService,
		notificationService: notificationService,
		userService:         userService,
	}
}

// GetAll retorna todas as ordens de manutenção
func (h *MaintenanceHandler) GetAll(c *gin.Context) {
	// Obtém informações do usuário do contexto
	userID := c.GetInt64("userID")
	userRole := c.GetString("userRole")

	// Prepara o filtro
	filters := make(map[string]interface{})

	// Parâmetros de consulta
	if status := c.Query("status"); status != "" {
		filters["status"] = models.OrderStatus(status)
	}

	if priority := c.Query("priority"); priority != "" {
		filters["priority"] = models.PriorityLevel(priority)
	}

	if orderType := c.Query("type"); orderType != "" {
		filters["type"] = models.MaintenanceType(orderType)
	}

	if assignedToStr := c.Query("assigned_to"); assignedToStr != "" {
		assignedTo, err := strconv.ParseInt(assignedToStr, 10, 64)
		if err == nil {
			filters["assigned_to"] = assignedTo
		}
	}

	if search := c.Query("search"); search != "" {
		filters["search"] = search
	}

	// Parâmetros de data
	if startDateStr := c.Query("start_date"); startDateStr != "" {
		startDate, err := time.Parse("2006-01-02", startDateStr)
		if err == nil {
			filters["start_date"] = startDate
		}
	}

	if endDateStr := c.Query("end_date"); endDateStr != "" {
		endDate, err := time.Parse("2006-01-02", endDateStr)
		if err == nil {
			// Configura para o final do dia
			endDate = endDate.Add(24*time.Hour - 1*time.Second)
			filters["end_date"] = endDate
		}
	}

	// Paginação
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 10
	}

	// Busca as ordens de manutenção
	orders, total, err := h.maintenanceService.GetAll(context.Background(), filters, userID, userRole, page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar ordens: " + err.Error()})
		return
	}

	// Retorna os resultados
	c.JSON(http.StatusOK, gin.H{
		"data": orders,
		"meta": gin.H{
			"total": total,
			"page":  page,
			"limit": limit,
			"pages": (total + limit - 1) / limit,
		},
	})
}

// GetByID retorna uma ordem de manutenção pelo ID
func (h *MaintenanceHandler) GetByID(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	order, err := h.maintenanceService.GetOrderByID(context.Background(), uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, order)
}

// Create cria uma nova ordem de manutenção
func (h *MaintenanceHandler) Create(c *gin.Context) {
	// Obtém informações do usuário do contexto
	userID := c.GetInt64("userID")

	var request models.MaintenanceOrderRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Converter a requisição para uma ordem de manutenção
	order := &models.MaintenanceOrder{
		Title:              request.Title,
		Description:        request.Description,
		Problem:            request.Problem,
		Priority:           request.Priority,
		Status:             models.StatusPending,
		BranchID:           uint(request.BranchID),
		EquipmentID:        uint(request.EquipmentID),
		CreatedByUserID:    uint(userID),
		PartialFunctioning: request.PartialFunctioning,
		ExtraEquipment:     request.ExtraEquipment,
		SameDay:            request.SameDay,
		PartReplacement:    request.PartReplacement,
		OpenDate:           time.Now(),
	}

	if request.ServiceProviderID != nil {
		providerID := uint(*request.ServiceProviderID)
		order.ServiceProviderID = &providerID
	}

	if request.EstimatedCost != nil {
		order.EstimatedCost = *request.EstimatedCost
	}

	if request.DueDate != nil {
		order.DueDate = *request.DueDate
	}

	err := h.maintenanceService.CreateOrder(context.Background(), order)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, order)
}

// Update atualiza uma ordem de manutenção existente
func (h *MaintenanceHandler) Update(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var request models.MaintenanceOrderRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Buscar a ordem existente
	existingOrder, err := h.maintenanceService.GetOrderByID(context.Background(), uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Ordem não encontrada"})
		return
	}

	// Atualizar os campos da ordem
	existingOrder.Title = request.Title
	existingOrder.Description = request.Description
	existingOrder.Problem = request.Problem
	existingOrder.Priority = request.Priority
	existingOrder.BranchID = uint(request.BranchID)
	existingOrder.EquipmentID = uint(request.EquipmentID)
	existingOrder.PartialFunctioning = request.PartialFunctioning
	existingOrder.ExtraEquipment = request.ExtraEquipment
	existingOrder.SameDay = request.SameDay
	existingOrder.PartReplacement = request.PartReplacement

	if request.ServiceProviderID != nil {
		providerID := uint(*request.ServiceProviderID)
		existingOrder.ServiceProviderID = &providerID
	}

	if request.EstimatedCost != nil {
		existingOrder.EstimatedCost = *request.EstimatedCost
	}

	if request.DueDate != nil {
		existingOrder.DueDate = *request.DueDate
	}

	err = h.maintenanceService.UpdateOrder(context.Background(), existingOrder)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, existingOrder)
}

// UpdateStatus atualiza o status de uma ordem de manutenção
func (h *MaintenanceHandler) UpdateStatus(c *gin.Context) {
	// Obtém informações do usuário do contexto
	userID := c.GetInt64("userID")

	// Obtém o ID da ordem da URL
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Valida os dados recebidos
	var request models.StatusUpdateRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos: " + err.Error()})
		return
	}

	// Verifica se a ordem existe
	_, err = h.maintenanceService.GetOrderByID(context.Background(), uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Ordem não encontrada"})
		return
	}

	// Atualiza o status
	err = h.maintenanceService.UpdateStatus(context.Background(), uint(id), request.Status, uint(userID), request.Notes)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Buscar a ordem atualizada
	updatedOrder, err := h.maintenanceService.GetOrderByID(context.Background(), uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Ordem atualizada mas não pode ser recuperada"})
		return
	}

	c.JSON(http.StatusOK, updatedOrder)
}

// Delete remove uma ordem de manutenção
func (h *MaintenanceHandler) Delete(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	err = h.maintenanceService.DeleteOrder(context.Background(), uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Ordem removida com sucesso"})
}

// UpdatePriority atualiza a prioridade de uma ordem de manutenção
func (h *MaintenanceHandler) UpdatePriority(c *gin.Context) {
	// Obtém informações do usuário do contexto
	userID := c.GetInt64("userID")

	// Obtém o ID da ordem da URL
	orderID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Valida os dados recebidos
	var req models.PriorityUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos: " + err.Error()})
		return
	}

	// Verifica se a ordem existe
	_, err = h.maintenanceService.GetOrderByID(context.Background(), uint(orderID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Ordem não encontrada"})
		return
	}

	// Atualiza a prioridade
	err = h.maintenanceService.UpdatePriority(context.Background(), uint(orderID), req.Priority, uint(userID), req.Notes)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Buscar a ordem atualizada
	updatedOrder, err := h.maintenanceService.GetOrderByID(context.Background(), uint(orderID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Ordem atualizada mas não pode ser recuperada"})
		return
	}

	c.JSON(http.StatusOK, updatedOrder)
}

// AddNote adiciona uma nota a uma ordem de manutenção
func (h *MaintenanceHandler) AddNote(c *gin.Context) {
	// Obtém informações do usuário do contexto
	userID := c.GetInt64("userID")

	// Obtém o ID da ordem da URL
	orderID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Valida os dados recebidos
	var req models.NoteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos: " + err.Error()})
		return
	}

	// Adiciona a nota
	err = h.maintenanceService.AddNote(context.Background(), orderID, req.Content, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Busca a ordem atualizada
	order, err := h.maintenanceService.GetOrderByID(context.Background(), uint(orderID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, order)
}

// AddMaterial adiciona um material a uma ordem de manutenção
func (h *MaintenanceHandler) AddMaterial(c *gin.Context) {
	// Obtém informações do usuário do contexto
	userID := c.GetInt64("userID")

	// Obtém o ID da ordem da URL
	orderID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Valida os dados recebidos
	var req models.MaterialRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos: " + err.Error()})
		return
	}

	// Adiciona o material
	err = h.maintenanceService.AddMaterial(context.Background(), orderID, req, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Busca a ordem atualizada
	order, err := h.maintenanceService.GetOrderByID(context.Background(), uint(orderID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, order)
}

// GetStations retorna os postos disponíveis para o usuário
func (h *MaintenanceHandler) GetStations(c *gin.Context) {
	// Obtém informações do usuário do contexto
	userID := c.GetInt64("userID")
	userRole := c.GetString("userRole")

	// Busca as estações
	stations, err := h.maintenanceService.GetStations(context.Background(), userID, userRole)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, stations)
}

// GetFiliais retorna as filiais disponíveis para o usuário
func (h *MaintenanceHandler) GetFiliais(c *gin.Context) {
	// Obtém informações do usuário do contexto
	userID := c.GetInt64("userID")
	userRole := c.GetString("userRole")

	// Busca os postos (que são na verdade filiais)
	stations, err := h.maintenanceService.GetStations(context.Background(), userID, userRole)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, stations)
}
