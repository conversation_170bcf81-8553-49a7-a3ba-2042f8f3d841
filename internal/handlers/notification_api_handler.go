package handlers

import (
	"net/http"
	"strconv"

	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// NotificationAPIHandler gerencia as requisições HTTP relacionadas a notificações
type NotificationAPIHandler struct {
	service *services.NotificationService
}

// NewNotificationAPIHandler cria uma nova instância do handler de notificações
func NewNotificationAPIHandler(service *services.NotificationService) *NotificationAPIHandler {
	return &NotificationAPIHandler{
		service: service,
	}
}

// GetMyNotifications retorna as notificações do usuário atual
func (h *NotificationAPIHandler) GetMyNotifications(c *gin.Context) {
	// Obter o ID do usuário atual
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}

	// Converter para uint
	userIDUint := uint(userID.(int64))

	// Buscar notificações
	notifications, err := h.service.GetNotifications(userIDUint)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar notificações"})
		return
	}

	c.JSON(http.StatusOK, notifications)
}

// GetUnreadCount retorna o número de notificações não lidas do usuário atual
func (h *NotificationAPIHandler) GetUnreadCount(c *gin.Context) {
	// Obter o ID do usuário atual
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}

	// Converter para uint
	userIDUint := uint(userID.(int64))

	// Buscar contagem
	count, err := h.service.GetUnreadCount(userIDUint)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar contagem de notificações"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"count": count})
}

// MarkAsRead marca uma notificação como lida
func (h *NotificationAPIHandler) MarkAsRead(c *gin.Context) {
	// Obter o ID da notificação
	notificationID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de notificação inválido"})
		return
	}

	// Marcar como lida
	if err := h.service.MarkAsRead(uint(notificationID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao marcar notificação como lida"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Notificação marcada como lida"})
}

// MarkAllAsRead marca todas as notificações do usuário como lidas
func (h *NotificationAPIHandler) MarkAllAsRead(c *gin.Context) {
	// Obter o ID do usuário atual
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}

	// Converter para uint
	userIDUint := uint(userID.(int64))

	// Marcar todas as notificações como lidas
	if err := h.service.MarkAllAsRead(userIDUint); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao marcar notificações como lidas"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Todas as notificações marcadas como lidas"})
}

// RegisterRoutes registra as rotas da API de notificações
func (h *NotificationAPIHandler) RegisterRoutes(router *gin.Engine) {
	api := router.Group("/api/notifications")
	api.Use(AuthMiddleware()) // Middleware de autenticação

	api.GET("/me", h.GetMyNotifications)
	api.GET("/unread-count", h.GetUnreadCount)
	api.PUT("/:id/read", h.MarkAsRead)
	api.POST("/mark-all-read", h.MarkAllAsRead)
}
