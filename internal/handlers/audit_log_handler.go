package handlers

import (
	"net/http"
	"strconv"
	"tradicao/internal/database"
	"tradicao/internal/permissions"

	"github.com/gin-gonic/gin"
)

// AuditLogHandler é o handler para consulta de logs de auditoria
type AuditLogHandler struct {
	auditService *permissions.PermissionAuditService
}

// NewAuditLogHandler cria um novo handler para consulta de logs de auditoria
func NewAuditLogHandler() *AuditLogHandler {
	db := database.GetGormDB()
	auditService := permissions.NewPermissionAuditService(db)
	
	return &AuditLogHandler{
		auditService: auditService,
	}
}

// GetAuditLogs retorna os logs de auditoria
func (h *AuditLogHandler) GetAuditLogs(c *gin.Context) {
	// Obter parâmetros de paginação
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.<PERSON>fault<PERSON>("page_size", "20")
	
	// Converter para inteiros
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}
	
	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}
	
	// Obter filtros
	filters := make(map[string]interface{})
	
	// Filtro por usuário
	if userIDStr := c.Query("user_id"); userIDStr != "" {
		userID, err := strconv.ParseUint(userIDStr, 10, 32)
		if err == nil {
			filters["user_id"] = uint(userID)
		}
	}
	
	// Filtro por ação
	if action := c.Query("action"); action != "" {
		filters["action"] = action
	}
	
	// Filtro por recurso
	if resource := c.Query("resource"); resource != "" {
		filters["resource"] = resource
	}
	
	// Obter logs de auditoria
	logs, total, err := h.auditService.GetAuditLogs(filters, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao obter logs de auditoria: " + err.Error(),
		})
		return
	}
	
	// Calcular informações de paginação
	totalPages := (int(total) + pageSize - 1) / pageSize
	hasNextPage := page < totalPages
	hasPrevPage := page > 1
	
	// Retornar logs de auditoria
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"logs":        logs,
			"total":       total,
			"page":        page,
			"page_size":   pageSize,
			"total_pages": totalPages,
			"has_next":    hasNextPage,
			"has_prev":    hasPrevPage,
		},
	})
}

// GetAuditConfig retorna a configuração atual da auditoria
func (h *AuditLogHandler) GetAuditConfig(c *gin.Context) {
	// Obter configuração da auditoria
	config := h.auditService.GetConfig()
	
	// Retornar configuração da auditoria
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config,
	})
}

// UpdateAuditConfig atualiza a configuração da auditoria
func (h *AuditLogHandler) UpdateAuditConfig(c *gin.Context) {
	// Obter configuração da auditoria do corpo da requisição
	var config permissions.AuditConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Erro ao decodificar configuração da auditoria: " + err.Error(),
		})
		return
	}
	
	// Atualizar configuração da auditoria
	h.auditService.SetConfig(config)
	
	// Retornar sucesso
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Configuração da auditoria atualizada com sucesso",
		"data":    config,
	})
}
