package handlers

import (
	"log"
	"net/http"
	"strconv"

	"tradicao/internal/models"
	"tradicao/internal/repository"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// ServiceProviderHandler gerencia requisições relacionadas a prestadores
type ServiceProviderHandler struct {
	service       *services.ServiceProviderService
	userService   *services.UserService
	uploadService *services.FileUploadService
	emailService  services.EmailService
}

// NewServiceProviderHandler cria um novo ServiceProviderHandler
func NewServiceProviderHandler(
	service *services.ServiceProviderService,
	userService *services.UserService,
	uploadService *services.FileUploadService,
	emailService services.EmailService,
) *ServiceProviderHandler {
	return &ServiceProviderHandler{
		service:       service,
		userService:   userService,
		uploadService: uploadService,
		emailService:  emailService,
	}
}

// ListProviders lista todos os prestadores
func (h *ServiceProviderHandler) ListProviders(c *gin.Context) {
	providers, err := h.service.FindAll()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao listar prestadores", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, providers)
}

// GetProviderByID retorna um prestador pelo ID
func (h *ServiceProviderHandler) GetProviderByID(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	provider, err := h.service.FindByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Prestador não encontrado", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, provider)
}

// CreateProvider cria um novo prestador
func (h *ServiceProviderHandler) CreateProvider(c *gin.Context) {
	var provider models.ServiceProvider
	if err := c.ShouldBindJSON(&provider); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
		return
	}

	createdProvider, err := h.service.Create(&provider)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao criar prestador", "details": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, createdProvider)
}

// UpdateProvider atualiza um prestador
func (h *ServiceProviderHandler) UpdateProvider(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var provider models.ServiceProvider
	if err := c.ShouldBindJSON(&provider); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
		return
	}

	provider.ID = uint(id)

	updatedProvider, err := h.service.Update(&provider)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar prestador", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedProvider)
}

// DeleteProvider remove um prestador
func (h *ServiceProviderHandler) DeleteProvider(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	if err := h.service.Delete(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao remover prestador", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Prestador removido com sucesso"})
}

// GetProviderTechnicians retorna os técnicos de um prestador
func (h *ServiceProviderHandler) GetProviderTechnicians(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Verificar se o usuário logado tem permissão para este prestador
	userID := c.GetUint("userID")
	isManager, err := h.service.IsProviderManager(uint(id), userID)
	if err != nil || !isManager {
		c.JSON(http.StatusForbidden, gin.H{"error": "Sem permissão para gerenciar este prestador"})
		return
	}

	technicians, err := h.service.GetTechnicians(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao listar técnicos", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, technicians)
}

// AddTechnician adiciona um técnico a um prestador
func (h *ServiceProviderHandler) AddTechnician(c *gin.Context) {
	// Obter ID do prestador
	providerID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do prestador inválido"})
		return
	}

	// Verificar se o usuário logado tem permissão para este prestador
	userID := c.GetUint("userID")
	isManager, err := h.service.IsProviderManager(uint(providerID), userID)
	if err != nil || !isManager {
		c.JSON(http.StatusForbidden, gin.H{"error": "Sem permissão para gerenciar este prestador"})
		return
	}

	// Obter dados do técnico
	var technicianData models.TechnicianRegistration
	if err := c.ShouldBindJSON(&technicianData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
		return
	}

	// Validar dados
	if technicianData.Name == "" || technicianData.Email == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Nome e email são obrigatórios"})
		return
	}

	// Adicionar técnico
	newUser, tempPassword, err := h.service.AddTechnician(uint(providerID), technicianData)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao adicionar técnico", "details": err.Error()})
		return
	}

	// Retornar dados do técnico e senha temporária
	c.JSON(http.StatusCreated, gin.H{
		"message":       "Técnico adicionado com sucesso",
		"technician":    newUser,
		"temp_password": tempPassword, // Apenas para ambiente de desenvolvimento
	})
}

// RemoveTechnician remove um técnico de um prestador
func (h *ServiceProviderHandler) RemoveTechnician(c *gin.Context) {
	// Obter ID do prestador
	providerID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do prestador inválido"})
		return
	}

	// Obter ID do técnico
	technicianID, err := strconv.ParseUint(c.Param("technicianId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do técnico inválido"})
		return
	}

	// Verificar se o usuário logado tem permissão para este prestador
	userID := c.GetUint("userID")
	isManager, err := h.service.IsProviderManager(uint(providerID), userID)
	if err != nil || !isManager {
		c.JSON(http.StatusForbidden, gin.H{"error": "Sem permissão para gerenciar este prestador"})
		return
	}

	// Remover técnico
	if err := h.service.RemoveTechnician(uint(providerID), uint(technicianID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao remover técnico", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Técnico removido com sucesso"})
}

// UploadProviderLogo faz upload da logomarca de um prestador
func (h *ServiceProviderHandler) UploadProviderLogo(c *gin.Context) {
	// Obter ID do prestador
	providerID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do prestador inválido"})
		return
	}

	// Verificar se o usuário logado tem permissão para este prestador
	userID := c.GetUint("userID")
	isManager, err := h.service.IsProviderManager(uint(providerID), userID)
	if err != nil || !isManager {
		c.JSON(http.StatusForbidden, gin.H{"error": "Sem permissão para gerenciar este prestador"})
		return
	}

	// Obter arquivo do formulário
	file, err := c.FormFile("logo")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Arquivo não fornecido ou inválido"})
		return
	}

	// Verificar tamanho máximo (2MB)
	if file.Size > 2*1024*1024 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tamanho máximo de arquivo excedido (2MB)"})
		return
	}

	// Fazer upload do arquivo
	logoURL, err := h.uploadService.UploadFile(file, services.UploadTypeLogo)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao fazer upload do arquivo", "details": err.Error()})
		return
	}

	// Obter prestador atual para verificar se já tem logo
	provider, err := h.service.FindByID(uint(providerID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao obter dados do prestador"})
		return
	}

	// Se já existir uma logo, remover o arquivo antigo
	if provider.LogoURL != "" {
		h.uploadService.DeleteFile(provider.LogoURL)
	}

	// Atualizar URL da logo no banco de dados
	if err := h.service.UpdateProviderLogo(uint(providerID), logoURL); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar logo do prestador"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":  "Logo atualizada com sucesso",
		"logo_url": logoURL,
	})
}

// CheckProviderExists verifica se um prestador existe (usado para validação)
func (h *ServiceProviderHandler) CheckProviderExists(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.Status(http.StatusBadRequest)
		return
	}

	provider, err := h.service.FindByID(uint(id))
	if err != nil || provider.ID == 0 {
		c.Status(http.StatusNotFound)
		return
	}

	// Se o prestador existe, retornar 200 OK sem corpo
	c.Status(http.StatusOK)
}

// GetProvidersByBranch retorna prestadores vinculados a uma filial
func (h *ServiceProviderHandler) GetProvidersByBranch(c *gin.Context) {
	// Obter ID da filial da query string
	branchIDStr := c.Query("branch_id")
	if branchIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Parâmetro branch_id é obrigatório"})
		return
	}

	branchID, err := strconv.ParseUint(branchIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}

	// Criar repositório de prestadores
	providerRepo := repository.NewGormServiceProviderRepository()

	// Buscar prestadores vinculados à filial
	providers, err := providerRepo.FindByBranch(uint(branchID))
	if err != nil {
		log.Printf("Erro ao buscar prestadores para a filial %d: %v", branchID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar prestadores", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, providers)
}
