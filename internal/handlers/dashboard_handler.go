package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"tradicao/internal/models"
	"tradicao/internal/repository"
	"tradicao/internal/services"
)

// DashboardHandler gerencia as rotas do dashboard
type DashboardHandler struct {
	maintenanceService services.MaintenanceOrderService
	stationRepo        repository.IStationRepository
	filialRepo         repository.IFilialRepository
}

// NewDashboardHandler cria um novo manipulador de dashboard
func NewDashboardHandler(maintenanceService services.MaintenanceOrderService, stationRepo repository.IStationRepository) *DashboardHandler {
	// Criar um adaptador para converter IStationRepository para IFilialRepository
	filialRepo := repository.StationToFilialRepository(stationRepo)

	return &DashboardHandler{
		maintenanceService: maintenanceService,
		stationRepo:        stationRepo,
		filialRepo:         filialRepo,
	}
}

// GetMetrics retorna métricas para o dashboard
func (h *DashboardHandler) GetMetrics(c *gin.Context) {
	// Obtém informações do usuário do contexto
	userID := c.GetInt64("userID")
	userRole := c.GetString("userRole")

	// Obtém métricas por status
	statusCounts, err := h.maintenanceService.GetMetricsByStatus(userID, userRole)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao obter métricas de status: " + err.Error()})
		return
	}

	// Obtém métricas por prioridade
	priorityCounts, err := h.maintenanceService.GetOrdersByPriority(userID, userRole)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao obter métricas de prioridade: " + err.Error()})
		return
	}

	// Retorna as métricas
	c.JSON(http.StatusOK, gin.H{
		"status_counts":   statusCounts,
		"priority_counts": priorityCounts,
	})
}

// GetRecentOrders retorna as ordens mais recentes
func (h *DashboardHandler) GetRecentOrders(c *gin.Context) {
	// Obtém informações do usuário do contexto
	userID := c.GetInt64("userID")
	userRole := c.GetString("userRole")

	// Limite de ordens
	limit, err := strconv.Atoi(c.DefaultQuery("limit", "5"))
	if err != nil || limit < 1 || limit > 20 {
		limit = 5
	}

	// Busca as ordens recentes
	orders, err := h.maintenanceService.GetRecentOrders(userID, userRole, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar ordens recentes: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, orders)
}

// GetStatusCounts retorna a contagem de ordens por status
func (h *DashboardHandler) GetStatusCounts(c *gin.Context) {
	// Obtém informações do usuário do contexto
	userID := c.GetInt64("userID")
	userRole := c.GetString("userRole")

	// Obtém contagens por status
	statusCounts, err := h.maintenanceService.GetMetricsByStatus(userID, userRole)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao obter contagens por status: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, statusCounts)
}

// GetFinancialSummary retorna um resumo financeiro para o dashboard
func (h *DashboardHandler) GetFinancialSummary(c *gin.Context) {
	// Obtém informações do usuário do contexto (será usado futuramente)
	// userID := c.GetInt64("userID")
	// userRole := models.UserRole(c.GetString("userRole"))

	// Por enquanto, retornaremos dados simulados
	// Em produção, isso viria de um serviço financeiro real
	c.JSON(http.StatusOK, gin.H{
		"total_income":  56789.00,
		"total_expense": 34567.00,
		"balance":       22222.00,
		"expense_breakdown": []gin.H{
			{"category": "Combustível", "amount": 12350.00, "percentage": 35},
			{"category": "Manutenção", "amount": 8641.75, "percentage": 25},
			{"category": "Salários", "amount": 6913.40, "percentage": 20},
			{"category": "Suprimentos", "amount": 5185.05, "percentage": 15},
			{"category": "Outros", "amount": 1728.35, "percentage": 5},
		},
		"monthly_data": []gin.H{
			{"month": "Jan", "income": 41200.00, "expense": 32800.00},
			{"month": "Fev", "income": 38600.00, "expense": 29700.00},
			{"month": "Mar", "income": 42900.00, "expense": 31300.00},
			{"month": "Abr", "income": 45700.00, "expense": 33200.00},
			{"month": "Mai", "income": 46800.00, "expense": 34500.00},
			{"month": "Jun", "income": 48300.00, "expense": 35600.00},
			{"month": "Jul", "income": 50200.00, "expense": 36700.00},
			{"month": "Ago", "income": 51600.00, "expense": 37100.00},
			{"month": "Set", "income": 53200.00, "expense": 37900.00},
			{"month": "Out", "income": 54700.00, "expense": 38500.00},
			{"month": "Nov", "income": 55900.00, "expense": 38200.00},
			{"month": "Dez", "income": 56789.00, "expense": 34567.00},
		},
	})
}

// GetTransactions retorna transações financeiras
func (h *DashboardHandler) GetTransactions(c *gin.Context) {
	// Obtém informações do usuário do contexto (será usado futuramente)
	// userID := c.GetInt64("userID")
	// userRole := models.UserRole(c.GetString("userRole"))

	// Limite de transações
	limit, err := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if err != nil || limit < 1 || limit > 50 {
		limit = 10
	}

	// Paginação
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	// Por enquanto, retornaremos dados simulados
	// Em produção, isso viria de um serviço financeiro real
	c.JSON(http.StatusOK, gin.H{
		"transactions": []gin.H{
			{"id": 1, "date": "2023-12-15", "description": "Compra de combustível", "category": "fuel", "filial_id": 1, "filial_name": "Posto Shell Central", "amount": 12350.00, "type": "expense"},
			{"id": 2, "date": "2023-12-14", "description": "Venda de combustível", "category": "fuel", "filial_id": 1, "filial_name": "Posto Shell Central", "amount": 28500.00, "type": "income"},
			{"id": 3, "date": "2023-12-13", "description": "Manutenção de bombas", "category": "maintenance", "filial_id": 2, "filial_name": "Posto Shell Norte", "amount": 3500.00, "type": "expense"},
			{"id": 4, "date": "2023-12-12", "description": "Pagamento de salários", "category": "salary", "filial_id": 3, "filial_name": "Posto Shell Sul", "amount": 6913.40, "type": "expense"},
			{"id": 5, "date": "2023-12-11", "description": "Venda de produtos loja", "category": "store", "filial_id": 2, "filial_name": "Posto Shell Norte", "amount": 8750.00, "type": "income"},
			{"id": 6, "date": "2023-12-10", "description": "Compra de suprimentos", "category": "supplies", "filial_id": 1, "filial_name": "Posto Shell Central", "amount": 2185.05, "type": "expense"},
			{"id": 7, "date": "2023-12-09", "description": "Serviços de lavagem", "category": "services", "filial_id": 3, "filial_name": "Posto Shell Sul", "amount": 4250.00, "type": "income"},
			{"id": 8, "date": "2023-12-08", "description": "Manutenção predial", "category": "maintenance", "filial_id": 1, "filial_name": "Posto Shell Central", "amount": 5141.75, "type": "expense"},
			{"id": 9, "date": "2023-12-07", "description": "Venda de combustível", "category": "fuel", "filial_id": 2, "filial_name": "Posto Shell Norte", "amount": 15289.00, "type": "income"},
			{"id": 10, "date": "2023-12-06", "description": "Despesas diversas", "category": "other", "filial_id": 3, "filial_name": "Posto Shell Sul", "amount": 1728.35, "type": "expense"},
		},
		"pagination": gin.H{
			"current_page": page,
			"total_pages":  3,
			"total_items":  27,
			"per_page":     limit,
		},
	})
}

// CreateTransaction cria uma nova transação financeira
func (h *DashboardHandler) CreateTransaction(c *gin.Context) {
	// Obtém informações do usuário do contexto
	userID := c.GetInt64("userID")
	userRole := c.GetString("userRole")

	// Verificar permissões
	if userRole != string(models.RoleAdmin) && userRole != string(models.RoleFinanceiro) && userRole != string(models.RoleGerente) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permissão negada. Apenas administradores, gerentes e financeiros podem criar transações."})
		return
	}

	// Estrutura para bind da requisição
	var transaction struct {
		Description string  `json:"description" binding:"required"`
		Category    string  `json:"category" binding:"required"`
		FilialID    int64   `json:"filial_id" binding:"required"`
		Amount      float64 `json:"amount" binding:"required"`
		Type        string  `json:"type" binding:"required,oneof=income expense"`
		Date        string  `json:"date" binding:"required"`
		Notes       string  `json:"notes"`
	}

	// Bind dos dados da requisição
	if err := c.ShouldBindJSON(&transaction); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos: " + err.Error()})
		return
	}

	// Em produção, aqui salvaríamos a transação no banco de dados
	// Por enquanto, simulamos uma resposta de sucesso
	c.JSON(http.StatusCreated, gin.H{
		"id":          11, // ID simulado
		"description": transaction.Description,
		"category":    transaction.Category,
		"filial_id":   transaction.FilialID,
		"amount":      transaction.Amount,
		"type":        transaction.Type,
		"date":        transaction.Date,
		"notes":       transaction.Notes,
		"created_by":  userID,
		"created_at":  "2023-12-16T14:22:34Z",
		"message":     "Transação criada com sucesso",
	})
}

// UpdateTransaction atualiza uma transação existente
func (h *DashboardHandler) UpdateTransaction(c *gin.Context) {
	// Obtém informações do usuário do contexto
	userID := c.GetInt64("userID")
	userRole := c.GetString("userRole")

	// Verificar permissões
	if userRole != string(models.RoleAdmin) && userRole != string(models.RoleFinanceiro) && userRole != string(models.RoleGerente) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permissão negada. Apenas administradores, gerentes e financeiros podem atualizar transações."})
		return
	}

	// Obtém o ID da transação
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Estrutura para bind da requisição
	var transaction struct {
		Description string  `json:"description" binding:"required"`
		Category    string  `json:"category" binding:"required"`
		FilialID    int64   `json:"filial_id" binding:"required"`
		Amount      float64 `json:"amount" binding:"required"`
		Type        string  `json:"type" binding:"required,oneof=income expense"`
		Date        string  `json:"date" binding:"required"`
		Notes       string  `json:"notes"`
	}

	// Bind dos dados da requisição
	if err := c.ShouldBindJSON(&transaction); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos: " + err.Error()})
		return
	}

	// Em produção, aqui atualizaríamos a transação no banco de dados
	// Por enquanto, simulamos uma resposta de sucesso
	c.JSON(http.StatusOK, gin.H{
		"id":          id,
		"description": transaction.Description,
		"category":    transaction.Category,
		"filial_id":   transaction.FilialID,
		"amount":      transaction.Amount,
		"type":        transaction.Type,
		"date":        transaction.Date,
		"notes":       transaction.Notes,
		"updated_by":  userID,
		"updated_at":  "2023-12-16T14:30:22Z",
		"message":     "Transação atualizada com sucesso",
	})
}

// DeleteTransaction remove uma transação financeira
func (h *DashboardHandler) DeleteTransaction(c *gin.Context) {
	// Obtém informações do usuário do contexto
	_ = c.GetInt64("userID") // Ignora userID pois não é usado aqui
	userRole := c.GetString("userRole")

	// Verificar permissões
	if userRole != string(models.RoleAdmin) && userRole != string(models.RoleFinanceiro) && userRole != string(models.RoleGerente) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permissão negada. Apenas administradores, gerentes e financeiros podem excluir transações."})
		return
	}

	// Obtém o ID da transação
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Em produção, aqui excluiríamos a transação do banco de dados
	// Por enquanto, simulamos uma resposta de sucesso
	c.JSON(http.StatusOK, gin.H{
		"id":      id,
		"deleted": true,
		"message": "Transação excluída com sucesso",
	})
}
