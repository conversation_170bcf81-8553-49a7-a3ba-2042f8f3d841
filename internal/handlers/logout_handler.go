package handlers

import (
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"

	"tradicao/internal/middleware"
)

// LogoutHandler handles user logout for GET requests (direct browser access)
func LogoutHandler(c *gin.Context) {
	// Obter informações para auditoria
	clientIP := c.ClientIP()
	userAgent := c.Request.UserAgent()

	// Obter token do cookie
	tokenString, err := c.<PERSON>("session_token")
	if err == nil && tokenString != "" {
		invalidateToken(tokenString, "GET", clientIP, userAgent)
	}

	// Registrar a ação de logout com informações detalhadas
	userID, exists := c.Get("userID")
	if exists {
		log.Printf("[AUDIT][LOGOUT] Usuário ID %v realizando logout via GET. IP: %s, User-Agent: %s",
			userID, clientIP, userAgent)
	} else {
		log.Printf("[AUDIT][LOGOUT] Usuário desconhecido realizando logout via GET. IP: %s, User-Agent: %s",
			clientIP, userAgent)
	}

	// Limpar o cookie de sessão
	c.SetCookie("session_token", "", -1, "/", "", false, true)

	// Limpar outros cookies relacionados à autenticação, se existirem
	c.SetCookie("auth_token", "", -1, "/", "", false, true)
	c.SetCookie("user_data", "", -1, "/", "", false, true)

	// Redirecionar para a página de login
	c.Redirect(http.StatusFound, "/login")
}

// LogoutAPIHandler handles user logout for API requests (AJAX calls)
func LogoutAPIHandler(c *gin.Context) {
	// Obter informações para auditoria
	clientIP := c.ClientIP()
	userAgent := c.Request.UserAgent()

	// Obter token do cabeçalho Authorization ou do cookie
	tokenString := extractToken(c)
	if tokenString != "" {
		invalidateToken(tokenString, "API", clientIP, userAgent)
	}

	// Registrar a ação de logout com informações detalhadas
	userID, exists := c.Get("userID")
	if exists {
		log.Printf("[AUDIT][LOGOUT] Usuário ID %v realizando logout via API. IP: %s, User-Agent: %s",
			userID, clientIP, userAgent)
	} else {
		log.Printf("[AUDIT][LOGOUT] Usuário desconhecido realizando logout via API. IP: %s, User-Agent: %s",
			clientIP, userAgent)
	}

	// Limpar o cookie de sessão
	c.SetCookie("session_token", "", -1, "/", "", false, true)

	// Limpar outros cookies relacionados à autenticação, se existirem
	c.SetCookie("auth_token", "", -1, "/", "", false, true)
	c.SetCookie("user_data", "", -1, "/", "", false, true)

	// Responder com sucesso
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Logout realizado com sucesso",
	})
}

// extractToken extrai o token JWT do cabeçalho Authorization ou do cookie
func extractToken(c *gin.Context) string {
	// Tentar obter do cabeçalho Authorization
	bearerToken := c.Request.Header.Get("Authorization")
	if len(bearerToken) > 7 && strings.HasPrefix(bearerToken, "Bearer ") {
		return bearerToken[7:]
	}

	// Tentar obter do cookie
	tokenString, err := c.Cookie("session_token")
	if err == nil {
		return tokenString
	}

	return ""
}

// invalidateToken adiciona um token à blacklist e registra a ação
func invalidateToken(tokenString, method, clientIP, userAgent string) {
	// Verificar se a blacklist está configurada
	if middleware.GlobalTokenBlacklist == nil {
		log.Printf("[ERRO][LOGOUT] Blacklist de tokens não configurada")
		return
	}

	// Analisar o token para obter a data de expiração
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// Usar a mesma chave secreta que é usada para gerar os tokens
		return middleware.JWTSecret, nil
	})

	if err != nil {
		log.Printf("[ERRO][LOGOUT] Erro ao analisar token: %v", err)
		return
	}

	// Obter claims do token
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		log.Printf("[ERRO][LOGOUT] Não foi possível extrair claims do token")
		return
	}

	// Obter data de expiração
	var expiry time.Time
	if exp, ok := claims["exp"].(float64); ok {
		expiry = time.Unix(int64(exp), 0)
	} else {
		// Se não houver expiração, definir para 24 horas a partir de agora
		expiry = time.Now().Add(24 * time.Hour)
	}

	// Adicionar token à blacklist
	middleware.GlobalTokenBlacklist.AddToken(tokenString, expiry)

	// Registrar ação
	log.Printf("[AUDIT][LOGOUT] Token invalidado via %s. IP: %s, User-Agent: %s, Expira em: %v",
		method, clientIP, userAgent, expiry)
}
