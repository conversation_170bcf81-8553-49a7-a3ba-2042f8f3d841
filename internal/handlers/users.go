package handlers

import (
	"net/http"

	"tradicao/internal/database"
	"tradicao/internal/models"

	"github.com/gin-gonic/gin"
)

// GetUsers retorna a lista de usuários
func GetUsers(c *gin.Context) {
	var users []models.User
	result := database.DB.Find(&users)

	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar usuários"})
		return
	}

	c.JSO<PERSON>(http.StatusOK, users)
}
