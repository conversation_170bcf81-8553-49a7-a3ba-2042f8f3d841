package handlers

import (
	"database/sql"
	"net/http"
	"time"

	"tradicao/internal/database"
	"tradicao/internal/models"

	"github.com/gin-gonic/gin"
)

// DBTestHandler é um handler para testar a conexão com o banco de dados
type DBTestHandler struct{}

// NewDBTestHandler cria uma nova instância do handler de teste de banco de dados
func NewDBTestHandler() *DBTestHandler {
	return &DBTestHandler{}
}

// TestConnection testa a conexão com o banco de dados e retorna informações básicas
func (h *DBTestHandler) TestConnection(c *gin.Context) {
	result := make(map[string]interface{})

	// Tentar obter o DB do GORM
	db := database.GetGormDB()
	if db == nil {
		var err error
		db, err = database.InitGorm()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Falha ao conectar com o banco de dados GORM",
				"error":   err.Error(),
			})
			return
		}
	}

	// Verificar a conexão fazendo uma consulta simples
	var count int64
	if err := db.Model(&models.User{}).Count(&count).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Banco de dados conectado, mas falha ao executar consulta",
			"error":   err.Error(),
		})
		return
	}

	// Coletar estatísticas básicas
	result["user_count"] = count

	// Contagem de filiais
	var branchCount int64
	if err := db.Model(&models.Branch{}).Count(&branchCount).Error; err == nil {
		result["branch_count"] = branchCount
	}

	// Contagem de equipamentos
	var equipmentCount int64
	if err := db.Model(&models.Equipment{}).Count(&equipmentCount).Error; err == nil {
		result["equipment_count"] = equipmentCount
	}

	// Contagem de ordens de manutenção
	var orderCount int64
	if err := db.Model(&models.MaintenanceOrder{}).Count(&orderCount).Error; err == nil {
		result["maintenance_order_count"] = orderCount
	}

	// Informações sobre a conexão
	sqlDB, err := db.DB()
	if err == nil {
		result["max_open_connections"] = sqlDB.Stats().MaxOpenConnections
		result["open_connections"] = sqlDB.Stats().OpenConnections
		result["in_use_connections"] = sqlDB.Stats().InUse
		result["idle_connections"] = sqlDB.Stats().Idle
	}

	result["timestamp"] = time.Now().Format(time.RFC3339)
	result["success"] = true
	result["message"] = "Conexão com banco de dados estabelecida com sucesso"

	c.JSON(http.StatusOK, result)
}

// ListUsers lista os usuários do sistema para fins de teste
func (h *DBTestHandler) ListUsers(c *gin.Context) {
	// Tentar obter o DB do GORM
	db := database.GetGormDB()
	if db == nil {
		var err error
		db, err = database.InitGorm()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Falha ao conectar com o banco de dados GORM",
				"error":   err.Error(),
			})
			return
		}
	}

	// Buscar usuários (com informações limitadas por segurança)
	// Consulta bruta utilizando os nomes exatos das colunas no banco de dados
	rows, err := db.Raw("SELECT id, name, email, type FROM users").Rows()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Falha ao buscar usuários",
			"error":   err.Error(),
		})
		return
	}
	defer rows.Close()

	var users []gin.H
	for rows.Next() {
		var id uint
		var name, email, userType string

		if err := rows.Scan(&id, &name, &email, &userType); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Falha ao processar dados dos usuários",
				"error":   err.Error(),
			})
			return
		}

		users = append(users, gin.H{
			"id":    id,
			"name":  name,
			"email": email,
			"type":  userType,
		})
	}

	if err := rows.Err(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Falha ao buscar usuários",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"count":   len(users),
		"users":   users,
	})
}

// ListBranches lista as filiais do sistema para fins de teste
func (h *DBTestHandler) ListBranches(c *gin.Context) {
	// Tentar obter o DB do GORM
	db := database.GetGormDB()
	if db == nil {
		var err error
		db, err = database.InitGorm()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Falha ao conectar com o banco de dados GORM",
				"error":   err.Error(),
			})
			return
		}
	}

	// Buscar filiais - usando as colunas corretas conforme a estrutura do banco de dados
	rows, err := db.Raw("SELECT id, name, address, cnpj, status, manager_id FROM branches WHERE deleted_at IS NULL").Rows()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Falha ao buscar filiais",
			"error":   err.Error(),
		})
		return
	}
	defer rows.Close()

	var branches []gin.H
	for rows.Next() {
		var id uint
		var name, address, cnpj, status string
		var managerId uint

		if err := rows.Scan(&id, &name, &address, &cnpj, &status, &managerId); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Falha ao processar dados das filiais",
				"error":   err.Error(),
			})
			return
		}

		branches = append(branches, gin.H{
			"id":         id,
			"name":       name,
			"address":    address,
			"cnpj":       cnpj,
			"status":     status,
			"manager_id": managerId,
		})
	}

	if err := rows.Err(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Falha ao buscar filiais",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"count":    len(branches),
		"branches": branches,
	})
}

// ListEquipment lista os equipamentos do sistema para fins de teste
func (h *DBTestHandler) ListEquipment(c *gin.Context) {
	// Tentar obter o DB do GORM
	db := database.GetGormDB()
	if db == nil {
		var err error
		db, err = database.InitGorm()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Falha ao conectar com o banco de dados GORM",
				"error":   err.Error(),
			})
			return
		}
	}

	// Buscar equipamentos
	rows, err := db.Raw(`
                SELECT e.id, e.type, e.brand, e.model, e.serial_number, 
                e.installation_date, e.last_maintenance, e.next_preventive, 
                e.status, e.location, e.branch_id, b.name as branch_name
                FROM equipment e
                LEFT JOIN branches b ON e.branch_id = b.id
                WHERE e.deleted_at IS NULL
        `).Rows()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Falha ao buscar equipamentos",
			"error":   err.Error(),
		})
		return
	}
	defer rows.Close()

	var equipment []gin.H
	for rows.Next() {
		var id, branchId uint
		var equipType, brand, model, serialNumber, status, location, branchName string
		var installationDate, lastMaintenance, nextPreventive *time.Time

		if err := rows.Scan(
			&id, &equipType, &brand, &model, &serialNumber,
			&installationDate, &lastMaintenance, &nextPreventive,
			&status, &location, &branchId, &branchName); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Falha ao processar dados dos equipamentos",
				"error":   err.Error(),
			})
			return
		}

		// Formatar datas para exibição
		var installationDateStr, lastMaintenanceStr, nextPreventiveStr string
		if installationDate != nil {
			installationDateStr = installationDate.Format("02/01/2006")
		}
		if lastMaintenance != nil {
			lastMaintenanceStr = lastMaintenance.Format("02/01/2006")
		}
		if nextPreventive != nil {
			nextPreventiveStr = nextPreventive.Format("02/01/2006")
		}

		equipment = append(equipment, gin.H{
			"id":                id,
			"type":              equipType,
			"brand":             brand,
			"model":             model,
			"serial_number":     serialNumber,
			"installation_date": installationDateStr,
			"last_maintenance":  lastMaintenanceStr,
			"next_preventive":   nextPreventiveStr,
			"status":            status,
			"location":          location,
			"branch_id":         branchId,
			"branch_name":       branchName,
		})
	}

	if err := rows.Err(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Falha ao buscar equipamentos",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"count":     len(equipment),
		"equipment": equipment,
	})
}

// GetTableSchema retorna o esquema de uma tabela do banco de dados
func (h *DBTestHandler) GetTableSchema(c *gin.Context) {
	tableName := c.Query("table")
	if tableName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Nome da tabela não especificado",
			"error":   "O parâmetro 'table' é obrigatório",
		})
		return
	}

	// Tentar obter o DB do GORM
	db := database.GetGormDB()
	if db == nil {
		var err error
		db, err = database.InitGorm()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Falha ao conectar com o banco de dados GORM",
				"error":   err.Error(),
			})
			return
		}
	}

	// Obter conexão SQL para consulta de esquema
	sqlDB, err := db.DB()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Falha ao obter conexão SQL",
			"error":   err.Error(),
		})
		return
	}

	// Consulta para obter informações das colunas
	query := `
                SELECT column_name, data_type, character_maximum_length, 
                       column_default, is_nullable
                FROM information_schema.columns 
                WHERE table_name = $1
                ORDER BY ordinal_position
        `

	rows, err := sqlDB.Query(query, tableName)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Falha ao consultar esquema da tabela",
			"error":   err.Error(),
		})
		return
	}
	defer rows.Close()

	var columns []gin.H
	for rows.Next() {
		var columnName, dataType, isNullable string
		var charMaxLength sql.NullInt64
		var columnDefault sql.NullString

		if err := rows.Scan(&columnName, &dataType, &charMaxLength, &columnDefault, &isNullable); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Falha ao processar informações do esquema",
				"error":   err.Error(),
			})
			return
		}

		column := gin.H{
			"column_name": columnName,
			"data_type":   dataType,
			"is_nullable": isNullable == "YES",
		}

		if charMaxLength.Valid {
			column["max_length"] = charMaxLength.Int64
		}

		if columnDefault.Valid {
			column["default_value"] = columnDefault.String
		}

		columns = append(columns, column)
	}

	if err := rows.Err(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Falha ao buscar esquema da tabela",
			"error":   err.Error(),
		})
		return
	}

	// Verificar se a tabela existe (sem colunas = tabela não existe)
	if len(columns) == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Tabela não encontrada",
			"error":   "A tabela especificada não existe no banco de dados",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"table":   tableName,
		"columns": columns,
	})
}

// ListMaintenanceOrders lista as ordens de manutenção do sistema para fins de teste
func (h *DBTestHandler) ListMaintenanceOrders(c *gin.Context) {
	// Tentar obter o DB do GORM
	db := database.GetGormDB()
	if db == nil {
		var err error
		db, err = database.InitGorm()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Falha ao conectar com o banco de dados GORM",
				"error":   err.Error(),
			})
			return
		}
	}

	// Buscar ordens de manutenção com informações relacionadas
	rows, err := db.Raw(`
                SELECT mo.id, mo.number, mo.open_date, mo.due_date, mo.problem, 
                       mo.priority, mo.status, mo.completion_date, mo.approval_status,
                       mo.payment_status, mo.estimated_cost, mo.actual_cost, mo.rating,
                       b.id as branch_id, b.name as branch_name,
                       e.id as equipment_id, e.type as equipment_type, 
                       e.brand as equipment_brand, e.model as equipment_model,
                       sp.id as provider_id, sp.name as provider_name
                FROM maintenance_orders mo
                LEFT JOIN branches b ON mo.branch_id = b.id
                LEFT JOIN equipment e ON mo.equipment_id = e.id
                LEFT JOIN service_providers sp ON mo.service_provider_id = sp.id
                WHERE mo.deleted_at IS NULL
                ORDER BY mo.open_date DESC
        `).Rows()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Falha ao buscar ordens de manutenção",
			"error":   err.Error(),
		})
		return
	}
	defer rows.Close()

	var orders []gin.H
	for rows.Next() {
		var id, branchId, equipmentId uint
		var providerId *uint
		var number, problem, priority, status, approvalStatus, paymentStatus string
		var branchName, equipmentType, equipmentBrand, equipmentModel string
		var providerName *string
		var openDate, dueDate, completionDate *time.Time
		var estimatedCost, actualCost float64
		var rating int

		if err := rows.Scan(
			&id, &number, &openDate, &dueDate, &problem,
			&priority, &status, &completionDate, &approvalStatus,
			&paymentStatus, &estimatedCost, &actualCost, &rating,
			&branchId, &branchName,
			&equipmentId, &equipmentType, &equipmentBrand, &equipmentModel,
			&providerId, &providerName); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Falha ao processar dados das ordens de manutenção",
				"error":   err.Error(),
			})
			return
		}

		// Formatar datas para exibição
		var openDateStr, dueDateStr, completionDateStr string
		if openDate != nil {
			openDateStr = openDate.Format("02/01/2006")
		}
		if dueDate != nil {
			dueDateStr = dueDate.Format("02/01/2006")
		}
		if completionDate != nil {
			completionDateStr = completionDate.Format("02/01/2006")
		}

		// Formatar dados do provedor de serviços
		var providerNameStr string
		if providerName != nil {
			providerNameStr = *providerName
		} else {
			providerNameStr = "Não atribuído"
		}

		providerInfo := gin.H{
			"id":   providerId,
			"name": providerNameStr,
		}
		if providerId == nil {
			providerInfo = gin.H{
				"id":   nil,
				"name": "Não atribuído",
			}
		}

		orders = append(orders, gin.H{
			"id":              id,
			"number":          number,
			"open_date":       openDateStr,
			"due_date":        dueDateStr,
			"problem":         problem,
			"priority":        priority,
			"status":          status,
			"completion_date": completionDateStr,
			"approval_status": approvalStatus,
			"payment_status":  paymentStatus,
			"estimated_cost":  estimatedCost,
			"actual_cost":     actualCost,
			"rating":          rating,
			"branch": gin.H{
				"id":   branchId,
				"name": branchName,
			},
			"equipment": gin.H{
				"id":    equipmentId,
				"type":  equipmentType,
				"brand": equipmentBrand,
				"model": equipmentModel,
			},
			"service_provider": providerInfo,
		})
	}

	if err := rows.Err(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Falha ao buscar ordens de manutenção",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"count":   len(orders),
		"orders":  orders,
	})
}
