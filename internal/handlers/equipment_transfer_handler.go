package handlers

import (
	"net/http"
	"strconv"

	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// EquipmentTransferHandler gerencia as requisições HTTP relacionadas a transferências de equipamentos
type EquipmentTransferHandler struct {
	service *services.EquipmentTransferService
}

// NewEquipmentTransferHandler cria uma nova instância do handler de transferências de equipamentos
func NewEquipmentTransferHandler(service *services.EquipmentTransferService) *EquipmentTransferHandler {
	return &EquipmentTransferHandler{
		service: service,
	}
}

// RequestTransferRequest representa a requisição para solicitar uma transferência
type RequestTransferRequest struct {
	EquipmentID         uint   `json:"equipment_id" binding:"required"`
	DestinationBranchID uint   `json:"destination_branch_id" binding:"required"`
	Justification       string `json:"justification" binding:"required"`
	AuthorizedBy        string `json:"authorized_by" binding:"required"`
}

// RequestTransfer solicita uma transferência de equipamento
func (h *EquipmentTransferHandler) RequestTransfer(c *gin.Context) {
	var req RequestTransferRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Obter o ID da filial do usuário atual
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}

	// Obter o ID da filial do usuário
	filialID, exists := c.Get("filialID")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Usuário não está associado a uma filial"})
		return
	}

	// Converter para uint
	userIDUint := uint(userID.(int64))
	filialIDUint := uint(filialID.(int64))

	// Solicitar a transferência
	transfer, err := h.service.RequestTransfer(
		c.Request.Context(),
		req.EquipmentID,
		filialIDUint,
		req.DestinationBranchID,
		userIDUint,
		req.Justification,
		req.AuthorizedBy,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, transfer)
}

// GetTransferByID busca uma transferência pelo ID
func (h *EquipmentTransferHandler) GetTransferByID(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	transfer, err := h.service.GetTransferByID(c.Request.Context(), uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if transfer == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Transferência não encontrada"})
		return
	}

	c.JSON(http.StatusOK, transfer)
}

// GetTransfersByEquipment busca transferências de um equipamento
func (h *EquipmentTransferHandler) GetTransfersByEquipment(c *gin.Context) {
	equipmentID, err := strconv.ParseUint(c.Param("equipmentId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do equipamento inválido"})
		return
	}

	transfers, err := h.service.GetTransfersByEquipment(c.Request.Context(), uint(equipmentID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, transfers)
}

// GetPendingTransfersByBranch busca transferências pendentes de/para uma filial
type TransferDirection string

const (
	TransferDirectionSource      TransferDirection = "source"
	TransferDirectionDestination TransferDirection = "destination"
)

// GetPendingTransfersByBranch busca transferências pendentes de/para uma filial
func (h *EquipmentTransferHandler) GetPendingTransfersByBranch(c *gin.Context) {
	branchID, err := strconv.ParseUint(c.Param("branchId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}

	direction := c.DefaultQuery("direction", string(TransferDirectionSource))

	var transfers []models.EquipmentTransfer
	if direction == string(TransferDirectionSource) {
		transfers, err = h.service.GetPendingTransfersBySourceBranch(c.Request.Context(), uint(branchID))
	} else {
		transfers, err = h.service.GetPendingTransfersByDestinationBranch(c.Request.Context(), uint(branchID))
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, transfers)
}

// UpdateTransferStatusRequest representa a requisição para atualizar o status de uma transferência
type UpdateTransferStatusRequest struct {
	Notes string `json:"notes"`
}

// ApproveTransfer aprova uma transferência
func (h *EquipmentTransferHandler) ApproveTransfer(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var req UpdateTransferStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Obter o ID do usuário atual
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}

	// Converter para uint
	userIDUint := uint(userID.(int64))

	// Aprovar a transferência
	if err := h.service.ApproveTransfer(c.Request.Context(), uint(id), userIDUint, req.Notes); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Transferência aprovada com sucesso"})
}

// RejectTransfer rejeita uma transferência
func (h *EquipmentTransferHandler) RejectTransfer(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var req UpdateTransferStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Obter o ID do usuário atual
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}

	// Converter para uint
	userIDUint := uint(userID.(int64))

	// Rejeitar a transferência
	if err := h.service.RejectTransfer(c.Request.Context(), uint(id), userIDUint, req.Notes); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Transferência rejeitada com sucesso"})
}

// CancelTransfer cancela uma transferência
func (h *EquipmentTransferHandler) CancelTransfer(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var req UpdateTransferStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Obter o ID do usuário atual
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}

	// Converter para uint
	userIDUint := uint(userID.(int64))

	// Cancelar a transferência
	if err := h.service.CancelTransfer(c.Request.Context(), uint(id), userIDUint, req.Notes); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Transferência cancelada com sucesso"})
}

// ListAllTransfers lista todas as transferências
func (h *EquipmentTransferHandler) ListAllTransfers(c *gin.Context) {
	transfers, err := h.service.ListAllTransfers(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, transfers)
}
