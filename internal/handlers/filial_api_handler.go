package handlers

import (
	"net/http"
	"strconv"
	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// FilialAPIHandler gerencia as requisições da API relacionadas a filiais
type FilialAPIHandler struct {
	service *services.FilialService
}

// NewFilialAPIHandler cria um novo handler para a API de filiais
func NewFilialAPIHandler(service *services.FilialService) *FilialAPIHandler {
	return &FilialAPIHandler{
		service: service,
	}
}

// GetAllFiliais retorna todas as filiais
func (h *FilialAPIHandler) GetAllFiliais(c *gin.Context) {
	filiais, err := h.service.GetAllFiliais()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar filiais: " + err.Error()})
		return
	}

	c.<PERSON>(http.StatusOK, filiais)
}

// GetFilialByID retorna uma filial pelo ID
func (h *FilialAPIHandler) GetFilialByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	filial, err := h.service.GetFilialByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Filial não encontrada: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, filial)
}

// CreateFilial cria uma nova filial
func (h *FilialAPIHandler) CreateFilial(c *gin.Context) {
	var filial models.Filial
	if err := c.ShouldBindJSON(&filial); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos: " + err.Error()})
		return
	}

	err := h.service.CreateFilial(&filial)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao criar filial: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Filial criada com sucesso",
		"id":      filial.ID,
	})
}

// UpdateFilial atualiza uma filial existente
func (h *FilialAPIHandler) UpdateFilial(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var filial models.Filial
	if err := c.ShouldBindJSON(&filial); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos: " + err.Error()})
		return
	}

	// Definir o ID da filial
	filial.ID = uint(id)

	err = h.service.UpdateFilial(&filial)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar filial: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Filial atualizada com sucesso"})
}

// DeleteFilial exclui uma filial
func (h *FilialAPIHandler) DeleteFilial(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	err = h.service.DeleteFilial(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao excluir filial: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Filial excluída com sucesso"})
}

// GetFiliaisByRegion retorna filiais por região
func (h *FilialAPIHandler) GetFiliaisByRegion(c *gin.Context) {
	region := c.Param("region")
	filiais, err := h.service.GetFiliaisByRegion(region)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar filiais por região: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, filiais)
}

// GetActiveFiliais retorna todas as filiais ativas
func (h *FilialAPIHandler) GetActiveFiliais(c *gin.Context) {
	filiais, err := h.service.GetActiveFiliais()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar filiais ativas: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, filiais)
}

// GetFilialMetrics retorna métricas das filiais
func (h *FilialAPIHandler) GetFilialMetrics(c *gin.Context) {
	metrics, err := h.service.GetFilialMetrics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar métricas das filiais: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, metrics)
}
