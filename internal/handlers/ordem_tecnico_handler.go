package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"tradicao/internal/database"
	"tradicao/internal/models"

	"github.com/gin-gonic/gin"
)

// GetOrdemTecnicoHandler retorna as ordens de manutenção atribuídas ao técnico atual
func GetOrdemTecnicoHandler(c *gin.Context) {
	// Obter o ID do usuário do contexto - verificar diferentes chaves possíveis
	var userID interface{}
	var exists bool

	// Tentar obter o ID do usuário com diferentes chaves
	if userID, exists = c.Get("user_id"); !exists {
		if userID, exists = c.Get("userID"); !exists {
			if userID, exists = c.Get("userId"); !exists {
				c.JSON(http.StatusUnauthorized, gin.H{
					"success": false,
					"message": "Usuário não autenticado",
				})
				return
			}
		}
	}

	// Imprimir o ID do usuário para debug
	fmt.Printf("ID do usuário encontrado: %v\n", userID)

	// Converter para uint
	var userIDUint uint
	switch v := userID.(type) {
	case uint:
		userIDUint = v
	case int:
		userIDUint = uint(v)
	case float64:
		userIDUint = uint(v)
	case string:
		id, err := strconv.ParseUint(v, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "ID de usuário inválido",
			})
			return
		}
		userIDUint = uint(id)
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "ID de usuário em formato inválido",
		})
		return
	}

	// Obter conexão com o banco de dados
	db := database.GetGormDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao conectar ao banco de dados",
		})
		return
	}

	// Buscar ordens de manutenção atribuídas ao técnico
	var ordens []models.MaintenanceOrder

	// Buscar ordens atribuídas ao prestador de serviço do técnico
	var serviceProviderID uint

	// Verificar se o usuário é um prestador de serviço
	var serviceProvider models.ServiceProvider
	if err := db.Where("user_id = ?", userIDUint).First(&serviceProvider).Error; err == nil {
		serviceProviderID = serviceProvider.ID
		fmt.Println("Prestador de serviço encontrado com ID:", serviceProviderID)
	} else {
		fmt.Println("Usuário não é um prestador de serviço:", err)
	}

	// Verificar se o usuário é um técnico
	var tecnico models.Technician
	db.Where("user_id = ?", userIDUint).First(&tecnico)

	// Verificar se o usuário técnico está associado a um prestador de serviço
	var usuario models.User
	if err := db.First(&usuario, userIDUint).Error; err == nil {
		if usuario.ServiceProviderID != nil && *usuario.ServiceProviderID > 0 {
			// Se o usuário técnico está associado a um prestador, usar esse ID
			serviceProviderID = *usuario.ServiceProviderID
			fmt.Println("Técnico associado ao prestador de serviço com ID:", serviceProviderID)
		}
	}

	// Verificar se foi fornecida uma data específica
	dateParam := c.Query("date")
	var filterByDate bool
	var filterDate time.Time
	var err error

	if dateParam != "" {
		filterDate, err = time.Parse("2006-01-02", dateParam)
		if err == nil {
			filterByDate = true
			fmt.Printf("Filtrando ordens para a data: %s\n", filterDate.Format("2006-01-02"))
		} else {
			fmt.Printf("Erro ao analisar data '%s': %v\n", dateParam, err)
		}
	}

	// Buscar ordens atribuídas ao técnico ou ao prestador de serviço
	query := db.Debug().Table("maintenance_orders")

	// Verificar o papel do usuário no contexto
	userRole, _ := c.Get("user_role")
	fmt.Printf("Papel do usuário: %v\n", userRole)

	// Imprimir SQL para debug
	fmt.Println("SQL antes da consulta:", query.Statement.SQL.String())

	// Buscar ordens atribuídas ao técnico através da tabela technician_orders
	query = query.Joins("JOIN technician_orders ON technician_orders.order_id = maintenance_orders.id")
	query = query.Where("technician_orders.technician_id = ?", userIDUint)
	fmt.Printf("Buscando ordens para técnico ID: %d através da tabela technician_orders\n", userIDUint)

	// Se o técnico estiver associado a um prestador, também buscar ordens atribuídas ao prestador
	if serviceProviderID > 0 {
		query = query.Or("maintenance_orders.service_provider_id = ?", serviceProviderID)
		fmt.Printf("Também buscando ordens para prestador ID: %d\n", serviceProviderID)
	}

	// Garantir que apenas ordens válidas sejam retornadas
	query = query.Where("id IS NOT NULL AND id > 0")
	// Remover filtro por title que não existe na tabela
	// query = query.Where("title IS NOT NULL AND title != ''")
	query = query.Where("due_date IS NOT NULL")

	// Se houver filtro por data, aplicar
	if filterByDate {
		// Criar intervalo para o dia inteiro
		startOfDay := filterDate
		endOfDay := filterDate.Add(24 * time.Hour).Add(-time.Second)

		// Aplicar filtro de data
		query = query.Where("(created_at BETWEEN ? AND ? OR due_date BETWEEN ? AND ?)",
			startOfDay, endOfDay, startOfDay, endOfDay)
		fmt.Printf("Aplicando filtro de data: %s a %s\n",
			startOfDay.Format("2006-01-02 15:04:05"),
			endOfDay.Format("2006-01-02 15:04:05"))
	}

	// Imprimir SQL para debug
	fmt.Println("SQL após a consulta:", query.Statement.SQL.String())

	// Imprimir as ordens encontradas para debug
	var ordensDebug []struct {
		ID                uint
		BranchID          uint
		EquipmentID       uint
		TechnicianID      uint
		ServiceProviderID uint
		Number            string
		Problem           string
		Status            string
	}

	// Clonar a query para não afetar a original
	queryDebug := db.Debug().Table("maintenance_orders")
	queryDebug = queryDebug.Joins("JOIN technician_orders ON technician_orders.order_id = maintenance_orders.id")
	queryDebug = queryDebug.Where("technician_orders.technician_id = ?", userIDUint)

	// Se o técnico estiver associado a um prestador, também buscar ordens atribuídas ao prestador
	if serviceProviderID > 0 {
		queryDebug = queryDebug.Or("maintenance_orders.service_provider_id = ?", serviceProviderID)
	}

	if err := queryDebug.Find(&ordensDebug).Error; err != nil {
		fmt.Println("Erro ao buscar ordens para debug:", err)
	} else {
		fmt.Printf("Encontradas %d ordens para debug:\n", len(ordensDebug))
		for _, o := range ordensDebug {
			fmt.Printf("  ID: %d, Branch: %d, Equipment: %d, Technician: %d, Provider: %d, Number: %s, Status: %s\n",
				o.ID, o.BranchID, o.EquipmentID, o.TechnicianID, o.ServiceProviderID, o.Number, o.Status)
		}
	}

	// Ordenar por data de criação
	query = query.Order("created_at DESC")

	if err := query.Find(&ordens).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao buscar ordens de manutenção: " + err.Error(),
		})
		return
	}

	// Log para debug
	fmt.Printf("Encontradas %d ordens para o técnico/prestador\n", len(ordens))

	// Se não encontrou nenhuma ordem, retornar lista vazia
	if len(ordens) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Nenhuma ordem encontrada para este técnico",
			"data":    []models.MaintenanceOrder{},
		})
		return
	}

	// Preparar resposta com dados formatados
	type OrdemResponse struct {
		ID          uint   `json:"id"`
		Titulo      string `json:"titulo"`
		Equipamento string `json:"equipamento"`
		Filial      string `json:"filial"`
		Prioridade  string `json:"prioridade"`
		Status      string `json:"status"`
		Data        string `json:"data"`
	}

	var response []OrdemResponse
	for _, ordem := range ordens {
		// Verificar se a ordem é válida
		if ordem.ID <= 0 || ordem.DueDate.IsZero() {
			fmt.Printf("Ignorando ordem inválida: ID=%d, DueDate=%v\n",
				ordem.ID, ordem.DueDate)
			continue
		}

		// Buscar informações da filial
		var filial models.Branch
		if err := db.First(&filial, ordem.BranchID).Error; err != nil {
			fmt.Printf("Erro ao buscar filial para ordem %d: %v\n", ordem.ID, err)
			continue // Pular ordens sem filial válida
		}

		// Buscar informações do equipamento
		var equipamento models.Equipment
		if err := db.First(&equipamento, ordem.EquipmentID).Error; err != nil {
			fmt.Printf("Erro ao buscar equipamento para ordem %d: %v\n", ordem.ID, err)
			continue // Pular ordens sem equipamento válido
		}

		// Verificar se o nome da filial e do equipamento são válidos
		if filial.Name == "" || equipamento.Name == "" {
			fmt.Printf("Ignorando ordem com filial ou equipamento inválido: ID=%d, Filial=%s, Equipamento=%s\n",
				ordem.ID, filial.Name, equipamento.Name)
			continue
		}

		// Formatar a data
		var dataFormatada string
		if !ordem.DueDate.IsZero() {
			dataFormatada = ordem.DueDate.Format("2006-01-02")
		} else if !ordem.CreatedAt.IsZero() {
			dataFormatada = ordem.CreatedAt.Format("2006-01-02")
		} else {
			fmt.Printf("Ignorando ordem sem data válida: ID=%d\n", ordem.ID)
			continue // Pular ordens sem data válida
		}

		// Adicionar à resposta
		titulo := "Manutenção " + strconv.Itoa(int(ordem.ID))
		if ordem.Title != "" {
			titulo = ordem.Title
		}

		response = append(response, OrdemResponse{
			ID:          ordem.ID,
			Titulo:      titulo,
			Equipamento: equipamento.Name,
			Filial:      filial.Name,
			Prioridade:  string(ordem.Priority),
			Status:      string(ordem.Status),
			Data:        dataFormatada,
		})
	}

	// Verificar se há ordens válidas
	if len(response) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Nenhuma ordem de manutenção válida encontrada",
			"data":    []OrdemResponse{},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Ordens de manutenção encontradas",
		"data":    response,
	})
}
