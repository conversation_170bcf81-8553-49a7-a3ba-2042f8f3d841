package handlers

import (
	"net/http"
	"strconv"
	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// UnifiedFilialHandler é um handler unificado que implementa todas as funcionalidades
// relacionadas a filiais (FilialHandler, StationHandler, BranchHandler)
type UnifiedFilialHandler struct {
	service *services.UnifiedFilialService
}

// NewUnifiedFilialHandler cria um novo handler unificado
func NewUnifiedFilialHandler(service *services.UnifiedFilialService) *UnifiedFilialHandler {
	return &UnifiedFilialHandler{
		service: service,
	}
}

// GetAllFiliais retorna todas as filiais
func (h *UnifiedFilialHandler) GetAllFiliais(c *gin.Context) {
	filiais, err := h.service.GetAllFiliais()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar filiais", "details": err.<PERSON>rror()})
		return
	}

	c.<PERSON>(http.StatusOK, filiais)
}

// GetFilial retorna uma filial pelo ID
func (h *UnifiedFilialHandler) GetFilial(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de filial inválido"})
		return
	}

	filial, err := h.service.GetFilialByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar filial", "details": err.Error()})
		return
	}

	if filial == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Filial não encontrada"})
		return
	}

	c.JSON(http.StatusOK, filial)
}

// CreateFilial cria uma nova filial
func (h *UnifiedFilialHandler) CreateFilial(c *gin.Context) {
	var filial models.Filial
	if err := c.ShouldBindJSON(&filial); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
		return
	}

	if err := h.service.CreateFilial(&filial); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao criar filial", "details": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, filial)
}

// UpdateFilial atualiza uma filial existente
func (h *UnifiedFilialHandler) UpdateFilial(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de filial inválido"})
		return
	}

	var filial models.Filial
	if err := c.ShouldBindJSON(&filial); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
		return
	}

	filial.ID = uint(id)
	if err := h.service.UpdateFilial(&filial); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar filial", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, filial)
}

// DeleteFilial remove uma filial
func (h *UnifiedFilialHandler) DeleteFilial(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de filial inválido"})
		return
	}

	if err := h.service.DeleteFilial(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao excluir filial", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Filial excluída com sucesso"})
}

// GetFiliaisByUserID retorna filiais associadas a um usuário
func (h *UnifiedFilialHandler) GetFiliaisByUserID(c *gin.Context) {
	userIDStr := c.Param("userId")
	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de usuário inválido"})
		return
	}

	filiais, err := h.service.GetFiliaisByUserID(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar filiais do usuário", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, filiais)
}

// GetAllBranches retorna todas as filiais (como Branch)
func (h *UnifiedFilialHandler) GetAllBranches(c *gin.Context) {
	// Obter parâmetros de paginação e filtros
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))
	search := c.Query("search")
	var isActive *bool
	if activeStr := c.Query("active"); activeStr != "" {
		active := activeStr == "true"
		isActive = &active
	}

	branches, total, err := h.service.GetAllBranches(page, limit, search, isActive)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar filiais", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"branches": branches,
		"total":    total,
		"page":     page,
		"limit":    limit,
	})
}

// GetBranch retorna uma filial pelo ID (como Branch)
func (h *UnifiedFilialHandler) GetBranch(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de filial inválido"})
		return
	}

	branch, err := h.service.GetBranchByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar filial", "details": err.Error()})
		return
	}

	if branch == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Filial não encontrada"})
		return
	}

	c.JSON(http.StatusOK, branch)
}

// CreateBranch cria uma nova filial (como Branch)
func (h *UnifiedFilialHandler) CreateBranch(c *gin.Context) {
	var branch models.Branch
	if err := c.ShouldBindJSON(&branch); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
		return
	}

	if err := h.service.CreateBranch(&branch); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao criar filial", "details": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, branch)
}

// UpdateBranch atualiza uma filial existente (como Branch)
func (h *UnifiedFilialHandler) UpdateBranch(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de filial inválido"})
		return
	}

	var branch models.Branch
	if err := c.ShouldBindJSON(&branch); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
		return
	}

	branch.ID = uint(id)
	if err := h.service.UpdateBranch(&branch); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar filial", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, branch)
}

// DeleteBranch remove uma filial (como Branch)
func (h *UnifiedFilialHandler) DeleteBranch(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de filial inválido"})
		return
	}

	if err := h.service.DeleteBranch(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao excluir filial", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Filial excluída com sucesso"})
}

// GetBranchStations retorna as estações associadas a uma filial
// Deprecated: Use GetBranchFiliais em vez disso
func (h *UnifiedFilialHandler) GetBranchStations(c *gin.Context) {
	h.GetBranchFiliais(c)
}

// GetBranchFiliais retorna as filiais associadas a uma filial principal
func (h *UnifiedFilialHandler) GetBranchFiliais(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de filial inválido"})
		return
	}

	// Verificar se a filial existe
	branch, err := h.service.GetBranchByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar filial", "details": err.Error()})
		return
	}

	if branch == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Filial não encontrada"})
		return
	}

	// Obter filiais associadas
	// Implementar conforme necessário
	filiais := []models.FilialSummary{
		{ID: 1, Name: "Posto Shell Jardins", Code: "SH-JAR", City: "São Paulo", State: "SP", IsActive: true},
		{ID: 2, Name: "Posto Shell Morumbi", Code: "SH-MOR", City: "São Paulo", State: "SP", IsActive: true},
		{ID: 3, Name: "Posto Shell Anchieta", Code: "SH-ANC", City: "São Bernardo", State: "SP", IsActive: true},
	}

	c.JSON(http.StatusOK, filiais)
}

// GetAllStations retorna todas as estações
func (h *UnifiedFilialHandler) GetAllStations(c *gin.Context) {
	stations, err := h.service.GetAllStations()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar estações", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, stations)
}

// GetStation retorna uma estação pelo ID
func (h *UnifiedFilialHandler) GetStation(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de estação inválido"})
		return
	}

	station, err := h.service.GetStationByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar estação", "details": err.Error()})
		return
	}

	if station == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Estação não encontrada"})
		return
	}

	c.JSON(http.StatusOK, station)
}

// CreateStation cria uma nova estação
func (h *UnifiedFilialHandler) CreateStation(c *gin.Context) {
	var station models.Station
	if err := c.ShouldBindJSON(&station); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
		return
	}

	if err := h.service.CreateStation(&station); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao criar estação", "details": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, station)
}

// UpdateStation atualiza uma estação existente
func (h *UnifiedFilialHandler) UpdateStation(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de estação inválido"})
		return
	}

	var station models.Station
	if err := c.ShouldBindJSON(&station); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
		return
	}

	station.ID = uint(id)
	if err := h.service.UpdateStation(&station); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar estação", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, station)
}

// DeleteStation remove uma estação
func (h *UnifiedFilialHandler) DeleteStation(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de estação inválido"})
		return
	}

	if err := h.service.DeleteStation(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao excluir estação", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Estação excluída com sucesso"})
}

// GetStationsByBranch retorna estações associadas a uma filial
func (h *UnifiedFilialHandler) GetStationsByBranch(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de filial inválido"})
		return
	}

	stations, err := h.service.GetStationsByBranchID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar estações da filial", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, stations)
}

// GetEquipmentsByFilial retorna equipamentos associados a uma filial
func (h *UnifiedFilialHandler) GetEquipmentsByFilial(c *gin.Context) {
	// Obter o ID da filial da URL
	idStr := c.Param("id")
	_, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de filial inválido"})
		return
	}

	// Redirecionar para a API de equipamentos por filial
	// Isso é apenas um redirecionamento temporário, pois a implementação real está em EquipmentAPIHandler
	c.Redirect(http.StatusTemporaryRedirect, "/api/equipments/filial/"+idStr)
}

// GetEquipmentsByBranch retorna equipamentos associados a uma filial (como Branch)
// Deprecated: Use GetEquipmentsByFilial em vez disso
func (h *UnifiedFilialHandler) GetEquipmentsByBranch(c *gin.Context) {
	h.GetEquipmentsByFilial(c)
}
