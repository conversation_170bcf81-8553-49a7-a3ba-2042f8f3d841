package handlers

import (
	"net/http"
	"strconv"
	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// EquipmentHandler gerencia as requisições HTTP relacionadas a equipamentos
type EquipmentHandler struct {
	equipmentService *services.EquipmentService
}

// NewEquipmentHandler cria uma nova instância do handler de equipamentos
func NewEquipmentHandler(equipmentService *services.EquipmentService) *EquipmentHandler {
	return &EquipmentHandler{
		equipmentService: equipmentService,
	}
}

// GetAllEquipment retorna todos os equipamentos
func (h *EquipmentHandler) GetAllEquipment(c *gin.Context) {
	equipments, err := h.equipmentService.GetAllEquipment(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.<PERSON>(http.StatusOK, equipments)
}

// GetEquipmentByID retorna um equipamento pelo ID
func (h *EquipmentHandler) GetEquipmentByID(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	equipment, err := h.equipmentService.GetEquipmentByID(c.Request.Context(), uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if equipment == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Equipamento não encontrado"})
		return
	}

	c.JSON(http.StatusOK, equipment)
}

// GetEquipmentsByType retorna equipamentos de um tipo específico
func (h *EquipmentHandler) GetEquipmentsByType(c *gin.Context) {
	equipmentType := c.Param("type")
	if equipmentType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tipo de equipamento não especificado"})
		return
	}

	equipments, err := h.equipmentService.GetEquipmentsByType(c.Request.Context(), equipmentType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, equipments)
}

// CreateEquipment cria um novo equipamento
func (h *EquipmentHandler) CreateEquipment(c *gin.Context) {
	var equipment models.Equipment
	if err := c.ShouldBindJSON(&equipment); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.equipmentService.CreateEquipment(c.Request.Context(), &equipment); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, equipment)
}

// UpdateEquipment atualiza um equipamento existente
func (h *EquipmentHandler) UpdateEquipment(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var equipment models.Equipment
	if err := c.ShouldBindJSON(&equipment); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	equipment.ID = uint(id)
	if err := h.equipmentService.UpdateEquipment(c.Request.Context(), &equipment); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, equipment)
}

// DeleteEquipment remove um equipamento
func (h *EquipmentHandler) DeleteEquipment(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	if err := h.equipmentService.DeleteEquipment(c.Request.Context(), uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetEquipmentsByFilial retorna equipamentos de uma filial específica
func (h *EquipmentHandler) GetEquipmentsByFilial(c *gin.Context) {
	filialID, err := strconv.ParseUint(c.Param("filialId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}

	equipments, err := h.equipmentService.GetEquipmentsByFilial(c.Request.Context(), uint(filialID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, equipments)
}

// GetEquipmentsNeedingMaintenance retorna equipamentos que precisam de manutenção
func (h *EquipmentHandler) GetEquipmentsNeedingMaintenance(c *gin.Context) {
	equipments, err := h.equipmentService.GetEquipmentsNeedingMaintenance(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, equipments)
}
