function createCalculadoraContent(container) {
    // Estrutura HTML da calculadora
    container.innerHTML = `
        <div class="tool-section">
            <h3 class="tool-section-title">
                <i class="fas fa-calculator"></i> Calculadora de Margem de Lucro
            </h3>
            <div class="tool-form">
                <!-- Seção de Custos Fixos -->
                <div class="form-group">
                    <label>Preço de Compra (R$/litro)</label>
                    <input type="number" id="precoCusto" step="0.01" value="4.25">
                </div>
                <div class="form-group">
                    <label>Preço de Venda (R$/litro)</label>
                    <input type="number" id="precoVenda" step="0.01" value="6.79">
                </div>
                
                <!-- Seção de Custos Variáveis -->
                <div class="form-section">
                    <h4>Custos Variáveis</h4>
                    <div id="custosVariaveis">
                        <div class="custo-item">
                            <div class="form-group">
                                <label>Transporte</label>
                                <input type="number" class="custo-valor" step="0.01" value="0.15">
                            </div>
                        </div>
                        <div class="custo-item">
                            <div class="form-group">
                                <label>Impostos</label>
                                <input type="number" class="custo-valor" step="0.01" value="1.25">
                            </div>
                        </div>
                    </div>
                    <button class="btn-outline-shell" id="addCustoBtn">
                        <i class="fas fa-plus"></i> Adicionar Custo
                    </button>
                </div>
                
                <div class="form-actions">
                    <button class="btn-shell-yellow" id="calcularBtn">Calcular</button>
                    <button class="btn-outline-shell" id="resetBtn">Limpar</button>
                </div>
            </div>
        </div>
        
        <!-- Resultados -->
        <div class="tool-section" id="resultadosSection" style="display: none;">
            <h3 class="tool-section-title">
                <i class="fas fa-chart-pie"></i> Resultados
            </h3>
            <div class="results-grid">
                <div class="result-card">
                    <div class="result-title">Margem Bruta</div>
                    <div class="result-value" id="margemBruta">R$ 0,00</div>
                    <div class="result-percent" id="margemBrutaPercent">0%</div>
                </div>
                <div class="result-card">
                    <div class="result-title">Margem Líquida</div>
                    <div class="result-value" id="margemLiquida">R$ 0,00</div>
                    <div class="result-percent" id="margemLiquidaPercent">0%</div>
                </div>
                <div class="result-card">
                    <div class="result-title">Total de Custos</div>
                    <div class="result-value" id="totalCustos">R$ 0,00</div>
                    <div class="result-percent" id="totalCustosPercent">0%</div>
                </div>
            </div>
            
            <div class="chart-container">
                <canvas id="margemChart" width="100%" height="250"></canvas>
            </div>
        </div>
    `;
    
    // Adicionar eventos
    const addCustoBtn = container.querySelector('#addCustoBtn');
    const calcularBtn = container.querySelector('#calcularBtn');
    const resetBtn = container.querySelector('#resetBtn');
    const custosVariaveis = container.querySelector('#custosVariaveis');
    
    // Evento para adicionar novo custo
    addCustoBtn.addEventListener('click', function() {
        const novoCusto = document.createElement('div');
        novoCusto.className = 'custo-item';
        novoCusto.innerHTML = `
            <div class="form-group">
                <label>Novo Custo</label>
                <div class="input-group">
                    <input type="text" class="custo-nome" placeholder="Nome do custo">
                    <input type="number" class="custo-valor" step="0.01" value="0.00">
                    <button class="btn-remove-custo"><i class="fas fa-times"></i></button>
                </div>
            </div>
        `;
        custosVariaveis.appendChild(novoCusto);
        
        // Adicionar evento para remover custo
        novoCusto.querySelector('.btn-remove-custo').addEventListener('click', function() {
            custosVariaveis.removeChild(novoCusto);
        });
    });
    
    // Evento para calcular margem
    calcularBtn.addEventListener('click', function() {
        const precoCusto = parseFloat(container.querySelector('#precoCusto').value) || 0;
        const precoVenda = parseFloat(container.querySelector('#precoVenda').value) || 0;
        
        // Calcular total de custos variáveis
        let totalCustosVariaveis = 0;
        const custoInputs = container.querySelectorAll('.custo-valor');
        custoInputs.forEach(input => {
            totalCustosVariaveis += parseFloat(input.value) || 0;
        });
        
        // Calcular margens
        const margemBruta = precoVenda - precoCusto;
        const margemLiquida = margemBruta - totalCustosVariaveis;
        const totalCustos = precoCusto + totalCustosVariaveis;
        
        // Calcular percentuais
        const margemBrutaPercent = (margemBruta / precoVenda) * 100;
        const margemLiquidaPercent = (margemLiquida / precoVenda) * 100;
        const totalCustosPercent = (totalCustos / precoVenda) * 100;
        
        // Atualizar resultados
        container.querySelector('#margemBruta').textContent = `R$ ${margemBruta.toFixed(2)}`;
        container.querySelector('#margemBrutaPercent').textContent = `${margemBrutaPercent.toFixed(2)}%`;
        
        container.querySelector('#margemLiquida').textContent = `R$ ${margemLiquida.toFixed(2)}`;
        container.querySelector('#margemLiquidaPercent').textContent = `${margemLiquidaPercent.toFixed(2)}%`;
        
        container.querySelector('#totalCustos').textContent = `R$ ${totalCustos.toFixed(2)}`;
        container.querySelector('#totalCustosPercent').textContent = `${totalCustosPercent.toFixed(2)}%`;
        
        // Mostrar seção de resultados
        container.querySelector('#resultadosSection').style.display = 'block';
        
        // Criar gráfico
        createMargemChart(precoCusto, totalCustosVariaveis, margemLiquida);
    });
    
    // Evento para resetar calculadora
    resetBtn.addEventListener('click', function() {
        container.querySelector('#precoCusto').value = '4.25';
        container.querySelector('#precoVenda').value = '6.79';
        
        // Remover custos adicionais
        const custosAdicionais = container.querySelectorAll('.custo-item:nth-child(n+3)');
        custosAdicionais.forEach(custo => {
            custosVariaveis.removeChild(custo);
        });
        
        // Resetar valores dos custos padrão
        container.querySelectorAll('.custo-item:nth-child(-n+2) .custo-valor').forEach((input, index) => {
            input.value = index === 0 ? '0.15' : '1.25';
        });
        
        // Esconder resultados
        container.querySelector('#resultadosSection').style.display = 'none';
    });
}

function createMargemChart(precoCusto, custosVariaveis, margemLiquida) {
    const ctx = document.getElementById('margemChart');
    if (!ctx) return;
    
    // Destruir gráfico existente se houver
    if (window.margemChart) {
        window.margemChart.destroy();
    }
    
    // Criar novo gráfico
    window.margemChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Custo de Aquisição', 'Custos Variáveis', 'Margem Líquida'],
            datasets: [{
                data: [precoCusto, custosVariaveis, margemLiquida],
                backgroundColor: ['#ED1C24', '#FDB813', '#28a745'],
                borderColor: '#333',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    labels: {
                        color: '#ccc',
                        font: {
                            family: "'Rajdhani', sans-serif",
                            size: 12
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(2);
                            return `${label}: R$ ${value.toFixed(2)} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}