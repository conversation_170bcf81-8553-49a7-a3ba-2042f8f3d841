package handlers

import (
	"bytes"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"regexp"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"

	"tradicao/internal/db"
	"tradicao/internal/models"
	"tradicao/internal/services"
)

var jwtKey []byte

func init() {
	secret := os.Getenv("JWT_SECRET")
	if secret == "" {
		jwtKey = []byte("chave-secreta-padrao-nao-use-em-producao")
		log.Println("AVISO: Usando chave JWT padrão em ambiente de desenvolvimento")
	} else {
		jwtKey = []byte(secret)
	}
}

type Claims struct {
	UserID uint   `json:"user_id"`
	Role   string `json:"role"`
	jwt.StandardClaims
}

func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Authorization header missing"})
			return
		}
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		claims := &Claims{}
		token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
			return jwtKey, nil
		})
		if err != nil || !token.Valid {
			log.Printf("Erro ao validar token: %v", err)
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Não autorizado", "detail": "Sessão inválida ou expirada"})
			return
		}
		c.Set("userID", claims.UserID)
		c.Set("role", claims.Role)
		c.Next()
	}
}

func RoleMiddleware(requiredRole string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole := c.GetString("role")
		if userRole != requiredRole {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "Insufficient permissions"})
			return
		}
		c.Next()
	}
}

// AuthHandler gerencia as rotas de autenticação
type AuthHandler struct {
	userService *services.UserService
}

// NewAuthHandler cria um novo manipulador de autenticação
func NewAuthHandler(userService *services.UserService) *AuthHandler {
	return &AuthHandler{
		userService: userService,
	}
}

// Login autentica um usuário
func (h *AuthHandler) Login(c *gin.Context) {
	log.Printf("[AUTH] Iniciando processo de login")
	log.Printf("[AUTH] Headers recebidos: %v", c.Request.Header)

	// Recupera os dados brutos do body para depuração
	bodyData, err := c.GetRawData()
	if err != nil {
		log.Printf("[AUTH] Erro ao ler corpo da requisição: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Erro ao ler dados da requisição"})
		return
	}

	// Mostra o body recebido (sem a senha)
	log.Printf("[AUTH] Body recebido (mascarado): %s", maskSensitiveData(string(bodyData)))

	// Como GetRawData já consumiu o body, precisamos restaurá-lo
	c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyData))

	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("[AUTH] Erro no parsing do JSON: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos: " + err.Error()})
		return
	}

	log.Printf("[AUTH] Dados de login recebidos: email=%s", req.Email)

	// Verifica se foi enviado um código TOTP
	var response *models.LoginResponse
	var authErr error

	if req.TOTPCode != "" {
		log.Printf("[AUTH] Código TOTP fornecido, autenticando com 2FA...")
		response, authErr = h.userService.Authenticate(req.Email, req.Password, req.TOTPCode)
	} else {
		log.Printf("[AUTH] Nenhum código TOTP fornecido, tentando autenticação básica...")
		response, authErr = h.userService.Authenticate(req.Email, req.Password)
	}

	if authErr != nil {
		log.Printf("[AUTH] Falha na autenticação: %v", authErr)
		c.JSON(http.StatusUnauthorized, gin.H{"error": authErr.Error()})
		return
	}

	// Verifica se precisa de verificação TOTP adicional
	if response.RequiresTOTP {
		log.Printf("[AUTH] Usuário possui 2FA ativado, solicitando código TOTP...")
		c.JSON(http.StatusOK, response)
		return
	}

	log.Printf("[AUTH] Autenticação bem-sucedida, definindo cookie...")

	// Define o cookie com duração de 24 horas
	c.SetCookie(
		"session_token",
		response.Token,
		3600*24, // 24 horas em segundos
		"/",
		"",
		false,
		true,
	)

	log.Printf("[AUTH] Cookie definido, verificando necessidade de troca de senha...")

	// Verifica se o usuário precisa trocar a senha
	changeRequired, err := h.userService.IsPasswordChangeRequired(response.User.ID)
	if err != nil {
		log.Printf("[AUTH] Erro ao verificar status da senha: %v", err)
		// Não bloqueia o login em caso de erro
	} else if changeRequired {
		log.Printf("[AUTH] Usuário precisa trocar a senha")
		response.RequiresPasswordChange = true
		response.Redirect = "/change-password"
	} else {
		// Define o redirecionamento baseado no perfil do usuário
		switch response.User.Role {
		case "financeiro":
			response.Redirect = "/calendario"
		case "prestador", "prestadores":
			response.Redirect = "/calendario"
		case "tecnico":
			response.Redirect = "/ordemtecnico"
		case "filial":
			response.Redirect = "/calendario"
		default:
			response.Redirect = "/dashboard"
		}
		log.Printf("[AUTH] Redirecionando usuário %s com perfil %s para %s", response.User.Email, response.User.Role, response.Redirect)
	}

	c.JSON(http.StatusOK, response)
	log.Printf("[AUTH] Login concluído com sucesso para usuário: %s", req.Email)
}

// Função auxiliar para mascarar dados sensíveis nos logs
func maskSensitiveData(data string) string {
	// Mascara a senha no JSON
	re := regexp.MustCompile(`"password":"[^"]*"`)
	return re.ReplaceAllString(data, `"password":"********"`)
}

// Logout realiza o logout do usuário
func (h *AuthHandler) Logout(c *gin.Context) {
	// Remove o cookie definindo um tempo de expiração negativo
	c.SetCookie(
		"session_token", // Alterado de "token" para "session_token" para compatibilidade com middleware de autenticação
		"",
		-1,
		"/",
		"",
		false,
		true,
	)

	c.JSON(http.StatusOK, gin.H{"message": "Logout realizado com sucesso"})
}

// GetCurrentUser retorna informações do usuário atual
func (h *AuthHandler) GetCurrentUser(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}

	// Converter userID para o tipo correto (int64)
	var userIDInt64 int64
	switch v := userID.(type) {
	case uint:
		userIDInt64 = int64(v)
	case int:
		userIDInt64 = int64(v)
	case int64:
		userIDInt64 = v
	case float64:
		userIDInt64 = int64(v)
	default:
		fmt.Printf("Tipo inesperado para userID: %T\n", userID)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro de conversão de ID"})
		return
	}

	user, err := h.userService.GetUserByID(userIDInt64)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao obter dados do usuário: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, user)
}

// SetupTOTP inicia a configuração 2FA para o usuário
func (h *AuthHandler) SetupTOTP(c *gin.Context) {
	// Obtém o ID do usuário do contexto (definido pelo middleware de autenticação)
	userIDInterface, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}

	// Converte para uint
	userID, ok := userIDInterface.(uint)
	if !ok {
		// Tenta converter outros tipos possíveis
		switch v := userIDInterface.(type) {
		case int:
			userID = uint(v)
		case int64:
			userID = uint(v)
		case float64:
			userID = uint(v)
		default:
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro de conversão de ID"})
			return
		}
	}

	// Gera configuração TOTP para o usuário
	secret, qrURL, err := h.userService.SetupTOTP(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao configurar 2FA: " + err.Error()})
		return
	}

	// Gera códigos de recuperação
	recoveryCodes, err := h.userService.TotpService.GenerateRecoveryCodes()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao gerar códigos de recuperação: " + err.Error()})
		return
	}

	// Retorna os dados para o cliente
	c.JSON(http.StatusOK, models.TOTPSetupResponse{
		Secret:        secret,
		QRCodeURL:     qrURL,
		RecoveryCodes: recoveryCodes,
	})
}

// VerifyAndEnableTOTP verifica o código TOTP e ativa o 2FA
func (h *AuthHandler) VerifyAndEnableTOTP(c *gin.Context) {
	// Obtém o ID do usuário do contexto
	userIDInterface, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}

	// Converte para uint
	userID, ok := userIDInterface.(uint)
	if !ok {
		// Tenta converter outros tipos possíveis
		switch v := userIDInterface.(type) {
		case int:
			userID = uint(v)
		case int64:
			userID = uint(v)
		case float64:
			userID = uint(v)
		default:
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro de conversão de ID"})
			return
		}
	}

	// Obtém o código TOTP da requisição
	var req struct {
		TOTPCode string `json:"totp_code" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Código inválido: " + err.Error()})
		return
	}

	// Ativa o TOTP para o usuário
	if err := h.userService.EnableTOTP(userID, req.TOTPCode); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "2FA ativado com sucesso"})
}

// DisableTOTP desativa o 2FA para o usuário
func (h *AuthHandler) DisableTOTP(c *gin.Context) {
	// Obtém o ID do usuário do contexto
	userIDInterface, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}

	// Converte para uint
	userID, ok := userIDInterface.(uint)
	if !ok {
		// Tenta converter outros tipos possíveis
		switch v := userIDInterface.(type) {
		case int:
			userID = uint(v)
		case int64:
			userID = uint(v)
		case float64:
			userID = uint(v)
		default:
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro de conversão de ID"})
			return
		}
	}

	// Obtém a senha da requisição para confirmação
	var req struct {
		Password string `json:"password" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Senha inválida: " + err.Error()})
		return
	}

	// Desativa o TOTP para o usuário
	if err := h.userService.DisableTOTP(userID, req.Password); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "2FA desativado com sucesso"})
}

// ServeQRCode serve a imagem QR code para configuração do 2FA
func (h *AuthHandler) ServeQRCode(c *gin.Context) {
	// Obtém o nome do arquivo do parâmetro URL
	filename := c.Param("filename")
	if filename == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Nome de arquivo não especificado"})
		return
	}

	// Verifica se o nome do arquivo é válido (evita diretório transversal)
	if filename != c.Param("filename") {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Nome de arquivo inválido"})
		return
	}

	// Serve o arquivo
	qrPath := fmt.Sprintf("./data/temp/qrcodes/%s", filename)
	c.File(qrPath)
}

// ChangePassword processa uma solicitação de troca de senha
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	// Obtém o ID do usuário do contexto
	userIDInterface, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}

	// Converte para uint
	userID, ok := userIDInterface.(uint)
	if !ok {
		// Tenta converter outros tipos possíveis
		switch v := userIDInterface.(type) {
		case int:
			userID = uint(v)
		case int64:
			userID = uint(v)
		case float64:
			userID = uint(v)
		default:
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro de conversão de ID"})
			return
		}
	}

	// Obtém os dados da requisição
	var req struct {
		CurrentPassword string `json:"current_password" binding:"required"`
		NewPassword     string `json:"new_password" binding:"required"`
		ConfirmPassword string `json:"confirm_password" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos: " + err.Error()})
		return
	}

	// Verifica se a nova senha e a confirmação são iguais
	if req.NewPassword != req.ConfirmPassword {
		c.JSON(http.StatusBadRequest, gin.H{"error": "A nova senha e a confirmação não coincidem"})
		return
	}

	// Processa a troca de senha
	if err := h.userService.ChangePassword(userID, req.CurrentPassword, req.NewPassword); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Registra a ação no log de auditoria
	ipAddress := c.ClientIP()
	userAgent := c.Request.UserAgent()

	auditLog := models.AuditLog{
		UserID:    &userID,
		Action:    "password_change",
		Resource:  "user",
		Details:   "Alteração de senha realizada pelo usuário",
		IP:        ipAddress,
		UserAgent: userAgent,
	}

	if err := h.userService.CreateAuditLog(auditLog); err != nil {
		// Apenas registra o erro, não impede o sucesso da operação
		fmt.Printf("Erro ao registrar log de auditoria: %v\n", err)
	}

	c.JSON(http.StatusOK, gin.H{"message": "Senha alterada com sucesso"})
}

// ResetPassword permite que um administrador redefina a senha de um usuário
func (h *AuthHandler) ResetPassword(c *gin.Context) {
	// Verifica se o usuário atual é administrador
	role, exists := c.Get("userRole")
	if !exists || role != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Acesso negado. Apenas administradores podem redefinir senhas"})
		return
	}

	// Obtém o ID do usuário a ter a senha redefinida
	userIDStr := c.Param("id")
	var userID uint

	if _, err := fmt.Sscanf(userIDStr, "%d", &userID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID de usuário inválido"})
		return
	}

	// Gera nova senha aleatória ou obtém a senha fornecida
	var req struct {
		NewPassword string `json:"new_password"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos: " + err.Error()})
		return
	}

	// Usa uma senha padrão se não for fornecida
	newPassword := req.NewPassword
	if newPassword == "" {
		// Gera senha aleatória de 12 caracteres
		newPassword = h.userService.TotpService.GenerateSecurePassword(12)
	}

	// Redefine a senha e força a troca no próximo login
	if err := h.userService.ResetUserPassword(userID, newPassword); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao redefinir senha: " + err.Error()})
		return
	}

	// Registra a ação no log de auditoria
	adminID, _ := c.Get("userID")
	ipAddress := c.ClientIP()
	userAgent := c.Request.UserAgent()

	adminIDValue, ok := adminID.(uint)
	if !ok {
		// Tenta converter outros tipos possíveis
		switch v := adminID.(type) {
		case int:
			adminIDValue = uint(v)
		case int64:
			adminIDValue = uint(v)
		case float64:
			adminIDValue = uint(v)
		default:
			fmt.Printf("Erro na conversão do tipo adminID: %T\n", adminID)
			// Use um valor default para não bloquear a operação
			adminIDValue = 0
		}
	}

	auditLog := models.AuditLog{
		UserID:    &adminIDValue,
		Action:    "password_reset",
		Resource:  fmt.Sprintf("user_%d", userID),
		Details:   fmt.Sprintf("Senha redefinida para o usuário ID %d por um administrador", userID),
		IP:        ipAddress,
		UserAgent: userAgent,
	}

	if err := h.userService.CreateAuditLog(auditLog); err != nil {
		fmt.Printf("Erro ao registrar log de auditoria: %v\n", err)
	}

	// Retorna a senha gerada apenas se for automática
	if req.NewPassword == "" {
		c.JSON(http.StatusOK, gin.H{
			"message":  "Senha redefinida com sucesso",
			"password": newPassword,
		})
	} else {
		c.JSON(http.StatusOK, gin.H{"message": "Senha redefinida com sucesso"})
	}
}

// CheckPasswordStatus verifica se o usuário precisa trocar a senha
func (h *AuthHandler) CheckPasswordStatus(c *gin.Context) {
	// Obtém o ID do usuário do contexto
	userIDInterface, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}

	// Converte para uint
	userID, ok := userIDInterface.(uint)
	if !ok {
		// Tenta converter outros tipos possíveis
		switch v := userIDInterface.(type) {
		case int:
			userID = uint(v)
		case int64:
			userID = uint(v)
		case float64:
			userID = uint(v)
		default:
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro de conversão de ID"})
			return
		}
	}

	// Verifica o status da senha
	changeRequired, err := h.userService.IsPasswordChangeRequired(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao verificar status da senha: " + err.Error()})
		return
	}

	// Obtém informações sobre política de senhas
	passwordPolicy := services.NewPasswordPolicyService(h.userService.GetUserRepo())
	policyText, err := passwordPolicy.GeneratePasswordPolicy()
	if err != nil {
		fmt.Printf("Erro ao gerar texto da política de senhas: %v\n", err)
		policyText = "Não foi possível carregar a política de senhas."
	}

	c.JSON(http.StatusOK, gin.H{
		"change_required": changeRequired,
		"password_policy": policyText,
	})
}

func Login(c *gin.Context) {
	var loginData struct {
		Email    string `json:"email"`
		Password string `json:"password"`
	}
	if err := c.ShouldBindJSON(&loginData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}
	var user User

	database := db.GetDB()
	if database == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro de conexão com o banco de dados"})
		return
	}

	if err := database.Where("email = ?", loginData.Email).First(&user).Error; err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(loginData.Password)); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}
	expirationTime := time.Now().Add(24 * time.Hour)
	claims := &Claims{
		UserID: user.ID,
		Role:   user.Role,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: expirationTime.Unix(),
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(jwtKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Could not generate token"})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"token": tokenString,
		"user": gin.H{
			"id":    user.ID,
			"name":  user.Name,
			"email": user.Email,
			"role":  user.Role,
		},
	})
}

func RefreshToken(c *gin.Context) {
	claims := &Claims{}
	tokenString := c.GetHeader("Authorization")
	tokenString = strings.TrimPrefix(tokenString, "Bearer ")
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		return jwtKey, nil
	})
	if err != nil || !token.Valid {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Token inválido"})
		return
	}
	if time.Unix(claims.ExpiresAt, 0).Sub(time.Now()) > 30*time.Minute {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Token ainda é válido"})
		return
	}
	expirationTime := time.Now().Add(24 * time.Hour)
	claims.ExpiresAt = expirationTime.Unix()
	newToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err = newToken.SignedString(jwtKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao gerar novo token"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"token": tokenString})
}

func RequestPasswordReset(c *gin.Context) {
	var request struct {
		Email string `json:"email"`
	}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Requisição inválida"})
		return
	}
	var user User

	database := db.GetDB()
	if database == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro de conexão com o banco de dados"})
		return
	}

	if err := database.Where("email = ?", request.Email).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Usuário não encontrado"})
		return
	}
	resetToken := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.StandardClaims{
		Subject:   user.Email,
		ExpiresAt: time.Now().Add(1 * time.Hour).Unix(),
	})
	_, err := resetToken.SignedString(jwtKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao gerar token de recuperação"})
		return
	}
	// TODO: Implementar envio de email com o token
	c.JSON(http.StatusOK, gin.H{"message": "Link de recuperação enviado com sucesso"})
}

func ResetPassword(c *gin.Context) {
	var request struct {
		Token       string `json:"token"`
		NewPassword string `json:"new_password"`
	}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Requisição inválida"})
		return
	}
	claims := &jwt.StandardClaims{}
	token, err := jwt.ParseWithClaims(request.Token, claims, func(token *jwt.Token) (interface{}, error) {
		return jwtKey, nil
	})
	if err != nil || !token.Valid {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Token inválido ou expirado"})
		return
	}
	var user User

	database := db.GetDB()
	if database == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro de conexão com o banco de dados"})
		return
	}

	if err := database.Where("email = ?", claims.Subject).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Usuário não encontrado"})
		return
	}
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(request.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao gerar nova senha"})
		return
	}
	user.Password = string(hashedPassword)
	database.Save(&user)
	c.JSON(http.StatusOK, gin.H{"message": "Senha alterada com sucesso"})
}

func CheckAuth(c *gin.Context) {
	userID := c.GetInt("userID")
	if userID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{"authenticated": false})
		return
	}
	c.JSON(http.StatusOK, gin.H{"authenticated": true, "userID": userID})
}
