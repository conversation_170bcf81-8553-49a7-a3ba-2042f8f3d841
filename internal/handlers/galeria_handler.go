package handlers

import (
	"log"
	"net/http"

	"tradicao/internal/db"
	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// Variável global para o serviço de filtragem de filiais
var filialFilterService *services.FilialFilterService

// InitGaleriaHandler inicializa os serviços necessários para o handler da galeria
func InitGaleriaHandler() {
	// Inicializar o serviço de filtragem de filiais
	database := db.GetDB()
	if database != nil {
		filialFilterService = services.NewFilialFilterService(database)
	} else {
		log.Println("AVISO: Não foi possível inicializar o serviço de filtragem de filiais")
	}
}

// GaleriaHandler renderiza a página de galeria de equipamentos
func GaleriaHandler(c *gin.Context) {
	// Obter informações do usuário do contexto
	userID, _ := c.Get("userID")
	userRole, _ := c.Get("userRole")
	userName, _ := c.Get("userName")

	// Converter userID para uint
	var userIDUint uint
	if id, ok := userID.(uint); ok {
		userIDUint = id
	} else if id, ok := userID.(int); ok {
		userIDUint = uint(id)
	}

	// Converter userRole para string
	var userRoleStr string
	if role, ok := userRole.(string); ok {
		userRoleStr = role
	}

	log.Printf("[GALERIA] Usuário ID: %d, Role: %s, Nome: %s acessando a galeria", userIDUint, userRoleStr, userName)

	var filiais []models.Branch
	var err error

	// Usar o serviço de filtragem para obter filiais autorizadas
	if filialFilterService != nil {
		log.Printf("[GALERIA] Usando serviço de filtragem para obter filiais autorizadas para usuário ID: %d, Role: %s", userIDUint, userRoleStr)

		// O serviço já tem lógica específica para o usuário <EMAIL> (ID 94)
		filiais, err = filialFilterService.GetAuthorizedFiliais(userIDUint, userRoleStr)
		if err != nil {
			log.Printf("[GALERIA-ERROR] Erro ao buscar filiais autorizadas: %v", err)
		} else {
			log.Printf("[GALERIA] Encontradas %d filiais autorizadas para o usuário", len(filiais))
			// Logar as filiais encontradas para diagnóstico
			for i, f := range filiais {
				log.Printf("[GALERIA] Filial #%d: ID=%d, Nome=%s", i+1, f.ID, f.Name)
			}
		}
	} else {
		log.Printf("[GALERIA-ERROR] Serviço de filtragem não está disponível")

		// Verificação específica para o usuário <EMAIL> (ID 94)
		// que deve ter acesso apenas às filiais 102 e 105
		if userIDUint == 94 {
			log.Printf("[GALERIA] Verificação especial para usuário <EMAIL> (ID: 94)")

			// Buscar diretamente as filiais 102 e 105
			database := db.GetDB()
			if database != nil {
				// Corrigido para usar os IDs corretos das filiais 102 e 105
				query := `
					SELECT DISTINCT * FROM branches WHERE id IN (102, 105)
				`
				if err := database.Raw(query).Scan(&filiais).Error; err != nil {
					log.Printf("[GALERIA-ERROR] Erro ao buscar filiais 102 e 105: %v", err)
				} else {
					log.Printf("[GALERIA] Encontradas %d filiais (102 e 105) para o usuário <EMAIL>", len(filiais))
					for i, f := range filiais {
						log.Printf("[GALERIA] Filial #%d: ID=%d, Nome=%s", i+1, f.ID, f.Name)
					}
				}
			} else {
				log.Printf("[GALERIA-ERROR] Banco de dados não disponível para buscar filiais 102 e 105")
			}
		} else {
			// Fallback para o método antigo para outros usuários
			filiaisAntigo, errAntigo := services.ListarFiliais()
			if errAntigo != nil {
				log.Printf("[GALERIA-ERROR] Erro ao buscar filiais: %v", errAntigo)
			} else {
				log.Printf("[GALERIA] Usando método antigo, encontradas %d filiais", len(filiaisAntigo))
				// Converter de Filial para Branch
				for _, f := range filiaisAntigo {
					filiais = append(filiais, models.Branch{
						ID:   f.ID,
						Name: f.Name,
					})
				}
			}
		}
	}

	// Converter filiais para o formato esperado pelo template
	var branchesData []gin.H

	// Verificar se há filiais duplicadas (para diagnóstico)
	filialIDs := make(map[uint]bool)
	var duplicadas []uint

	for _, filial := range filiais {
		// Verificar se esta filial já foi processada
		if filialIDs[filial.ID] {
			duplicadas = append(duplicadas, filial.ID)
			log.Printf("[GALERIA-WARN] Filial duplicada detectada: ID=%d, Nome=%s", filial.ID, filial.Name)
			// Pular filial duplicada
			continue
		}

		// Marcar filial como processada
		filialIDs[filial.ID] = true

		branchData := gin.H{
			"ID":   filial.ID,
			"Name": filial.Name,
		}

		// Adicionar campos adicionais se disponíveis
		if filial.Address != "" {
			branchData["Address"] = filial.Address
		}
		if filial.City != "" {
			branchData["City"] = filial.City
		}
		if filial.State != "" {
			branchData["State"] = filial.State
		}
		if filial.Phone != "" {
			branchData["Phone"] = filial.Phone
		}
		if filial.Email != "" {
			branchData["Email"] = filial.Email
		}

		branchesData = append(branchesData, branchData)
	}

	// Logar informações sobre filiais duplicadas
	if len(duplicadas) > 0 {
		log.Printf("[GALERIA-WARN] Foram encontradas %d filiais duplicadas: %v", len(duplicadas), duplicadas)
	}

	log.Printf("[GALERIA] Total de filiais após remoção de duplicadas: %d", len(branchesData))

	// Se não houver filiais, usar dados de fallback apenas para desenvolvimento
	if len(branchesData) == 0 {
		log.Println("Nenhuma filial autorizada encontrada para o usuário")
		// Em produção, não devemos mostrar filiais de fallback
		// mas para desenvolvimento, podemos manter
		if gin.Mode() != gin.ReleaseMode {
			log.Println("Usando dados de fallback para desenvolvimento")
			branchesData = []gin.H{
				{"ID": 1, "Name": "Posto Tradição - Centro", "Address": "Av. Principal, 1000", "City": "São Paulo", "State": "SP", "Phone": "(11) 3333-4444"},
				{"ID": 2, "Name": "Posto Tradição - Norte", "Address": "Rua Norte, 500", "City": "São Paulo", "State": "SP", "Phone": "(11) 4444-5555"},
				{"ID": 3, "Name": "Posto Tradição - Sul", "Address": "Av. Sul, 250", "City": "São Paulo", "State": "SP", "Phone": "(11) 5555-6666"},
			}
		}
	}

	// Verificar se o usuário é técnico ou prestador para mostrar a visualização simplificada
	isTechnicianOrProvider := userRoleStr == "tecnico" || userRoleStr == "prestadores"

	// Parâmetro branch_id na URL (para técnicos/prestadores que clicaram em "Ver Equipamentos")
	branchIDParam := c.Query("branch_id")

	// Se for técnico/prestador e tiver um branch_id na URL, mostrar a visualização normal
	if isTechnicianOrProvider && branchIDParam == "" {
		// Renderizar a visualização simplificada para técnicos e prestadores
		c.HTML(http.StatusOK, "galeria/galeria_tecnico_new.html", gin.H{
			"title":      "Postos Cadastrados - Rede Tradição",
			"page":       "galeria",
			"ActivePage": "galeria",
			"User": gin.H{
				"ID":    userID,
				"Role":  userRole,
				"Name":  userName,
				"Email": c.GetString("email"),
			},
			"Branches": branchesData,
			"UserID":   userIDUint, // Adicionado para diagnóstico
		})
	} else {
		// Renderizar a visualização normal para outros perfis ou quando um técnico/prestador seleciona um posto específico
		c.HTML(http.StatusOK, "galeria/galeria_new.html", gin.H{
			"title":      "Galeria de Equipamentos - Rede Tradição",
			"page":       "galeria",
			"ActivePage": "galeria",
			"User": gin.H{
				"ID":    userID,
				"Role":  userRole,
				"Name":  userName,
				"Email": c.GetString("email"),
			},
			"Branches": branchesData,
			"EquipmentTypes": []gin.H{
				{"Value": "bomba", "Label": "Bomba de Combustível"},
				{"Value": "tanque", "Label": "Tanque de Armazenamento"},
				{"Value": "compressor", "Label": "Compressor de Ar"},
				{"Value": "gerador", "Label": "Gerador de Energia"},
				{"Value": "sistema_pagamento", "Label": "Sistema de Pagamento"},
				{"Value": "outro", "Label": "Outro"},
			},
		})
	}
}
