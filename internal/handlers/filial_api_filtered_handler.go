package handlers

import (
	"log"
	"net/http"
	"strconv"

	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// FilialAPIFilteredHandler gerencia as requisições da API relacionadas a filiais com filtro de permissões
type FilialAPIFilteredHandler struct {
	filialService          *services.FilialService
	filialPermissionService *services.FilialPermissionService
}

// NewFilialAPIFilteredHandler cria um novo handler para a API de filiais com filtro de permissões
func NewFilialAPIFilteredHandler(filialService *services.FilialService, database *gorm.DB) *FilialAPIFilteredHandler {
	return &FilialAPIFilteredHandler{
		filialService:          filialService,
		filialPermissionService: services.NewFilialPermissionService(database),
	}
}

// GetAllFiliais retorna todas as filiais que o usuário tem permissão para acessar
func (h *FilialAPIFilteredHandler) GetAllFiliais(c *gin.Context) {
	// Obter informações do usuário do contexto
	userID, _ := c.Get("userID")
	userRole, _ := c.Get("userRole")

	// Converter userID para uint
	var userIDUint uint
	if id, ok := userID.(uint); ok {
		userIDUint = id
	} else if id, ok := userID.(int); ok {
		userIDUint = uint(id)
	}

	// Converter userRole para string
	var userRoleStr string
	if role, ok := userRole.(string); ok {
		userRoleStr = role
	}

	// Usar o serviço de permissões para obter filiais autorizadas
	filiais, err := h.filialPermissionService.GetAuthorizedFiliais(userIDUint, userRoleStr)
	if err != nil {
		log.Printf("Erro ao buscar filiais autorizadas: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar filiais: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, filiais)
}

// GetFilialByID retorna uma filial pelo ID se o usuário tiver permissão para acessá-la
func (h *FilialAPIFilteredHandler) GetFilialByID(c *gin.Context) {
	// Obter informações do usuário do contexto
	userID, _ := c.Get("userID")
	userRole, _ := c.Get("userRole")

	// Converter userID para uint
	var userIDUint uint
	if id, ok := userID.(uint); ok {
		userIDUint = id
	} else if id, ok := userID.(int); ok {
		userIDUint = uint(id)
	}

	// Converter userRole para string
	var userRoleStr string
	if role, ok := userRole.(string); ok {
		userRoleStr = role
	}

	// Obter ID da filial da URL
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Verificar se o usuário tem permissão para acessar esta filial
	// Para perfis administrativos, permitir acesso direto
	if userRoleStr == string(models.RoleAdmin) || userRoleStr == string(models.RoleGerente) {
		filial, err := h.filialService.GetFilialByID(id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar filial: " + err.Error()})
			return
		}
		if filial == nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Filial não encontrada"})
			return
		}
		c.JSON(http.StatusOK, filial)
		return
	}

	// Para outros perfis, verificar se a filial está na lista de filiais autorizadas
	filiais, err := h.filialPermissionService.GetAuthorizedFiliais(userIDUint, userRoleStr)
	if err != nil {
		log.Printf("Erro ao buscar filiais autorizadas: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao verificar permissões: " + err.Error()})
		return
	}

	// Verificar se a filial solicitada está na lista de filiais autorizadas
	var filialAutorizada bool
	for _, filial := range filiais {
		if filial.ID == uint(id) {
			filialAutorizada = true
			break
		}
	}

	if !filialAutorizada {
		c.JSON(http.StatusForbidden, gin.H{"error": "Sem permissão para acessar esta filial"})
		return
	}

	// Buscar a filial
	filial, err := h.filialService.GetFilialByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar filial: " + err.Error()})
		return
	}
	if filial == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Filial não encontrada"})
		return
	}

	c.JSON(http.StatusOK, filial)
}
