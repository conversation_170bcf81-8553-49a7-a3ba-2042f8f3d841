package handlers

import (
	"net/http"
	"strconv"
	"time"

	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// ListarOrdensAPI retorna a lista paginada de ordens de manutenção
func ListarOrdensAPI(c *gin.Context) {
	// Parâmetros de paginação
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("pageSize", "10"))

	// Extrair filtros da query
	filtros := extrairFiltrosOrdens(c)

	// Obter ordens do serviço
	ordens, total, err := services.ListarOrdens(page, pageSize, filtros)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar ordens de manutenção"})
		return
	}

	// Calcular número total de páginas
	totalPages := int(total+int64(pageSize)-1) / pageSize

	c.JSON(http.StatusOK, gin.H{
		"data": ordens,
		"meta": gin.H{
			"total":      total,
			"page":       page,
			"pageSize":   pageSize,
			"totalPages": totalPages,
		},
	})
}

// ObterOrdemAPI retorna uma ordem específica pelo ID
func ObterOrdemAPI(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	ordem, err := services.ObterOrdem(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Ordem não encontrada"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": ordem})
}

// CriarOrdemAPI cria uma nova ordem de manutenção
func CriarOrdemAPI(c *gin.Context) {
	// Obter ID do usuário logado (solicitante)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}

	// Obter dados da requisição
	var ordemInput models.OrdemManutencaoExpandida
	if err := c.ShouldBindJSON(&ordemInput); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
		return
	}

	// Definir campos automáticos
	ordemInput.SolicitanteID = userID.(uint)
	ordemInput.DataAbertura = time.Now()
	ordemInput.Status = models.StatusOrdem(models.StatusPending)

	// Chamar serviço para criar a ordem
	ordem, err := services.CriarOrdem(ordemInput)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao criar ordem", "details": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"data": ordem, "message": "Ordem criada com sucesso"})
}

// AtualizarStatusOrdemAPI atualiza o status de uma ordem
func AtualizarStatusOrdemAPI(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	// Obter dados da requisição
	var input struct {
		Status     models.StatusOrdem `json:"status"`
		Observacao string             `json:"observacao"`
	}

	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
		return
	}

	// Obter ID do usuário que está atualizando
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}

	// Chamar serviço para atualizar status
	ordem, err := services.AtualizarStatusOrdem(uint(id), input.Status, input.Observacao, userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar status", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": ordem, "message": "Status atualizado com sucesso"})
}

// ListarEquipamentosAPI retorna a lista de equipamentos disponíveis
func ListarEquipamentosAPI(c *gin.Context) {
	filialID, _ := strconv.ParseUint(c.Query("filial_id"), 10, 64)

	equipamentos, err := services.ListarEquipamentos(uint(filialID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar equipamentos"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": equipamentos})
}

// ListarFiliaisAPI retorna a lista de filiais
func ListarFiliaisAPI(c *gin.Context) {
	filiais, err := services.ListarFiliais()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar filiais"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": filiais})
}

// Função auxiliar para extrair filtros da query
func extrairFiltrosOrdens(c *gin.Context) map[string]interface{} {
	filtros := make(map[string]interface{})

	// Filtros básicos
	if status := c.Query("status"); status != "" {
		filtros["status"] = status
	}

	if prioridade := c.Query("prioridade"); prioridade != "" {
		filtros["prioridade"] = prioridade
	}

	if filialID := c.Query("filial_id"); filialID != "" {
		if id, err := strconv.ParseUint(filialID, 10, 64); err == nil {
			filtros["filial_id"] = uint(id)
		}
	}

	if equipamentoID := c.Query("equipamento_id"); equipamentoID != "" {
		if id, err := strconv.ParseUint(equipamentoID, 10, 64); err == nil {
			filtros["equipamento_id"] = uint(id)
		}
	}

	if tecnicoID := c.Query("tecnico_id"); tecnicoID != "" {
		if id, err := strconv.ParseUint(tecnicoID, 10, 64); err == nil {
			filtros["tecnico_id"] = uint(id)
		}
	}

	// Filtros de data
	if dataInicio := c.Query("data_inicio"); dataInicio != "" {
		filtros["data_inicio"] = dataInicio
	}

	if dataFim := c.Query("data_fim"); dataFim != "" {
		filtros["data_fim"] = dataFim
	}

	// Pesquisa por texto
	if busca := c.Query("q"); busca != "" {
		filtros["busca"] = busca
	}

	return filtros
}
