package handlers

import (
	"net/http"
	"strconv"
	"time"
	"tradicao/internal/database"
	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// GetProviderBranchAssignments retorna as atribuições de prestadores a filiais
func GetProviderBranchAssignments(c *gin.Context) {
	db := database.GetGormDB()
	
	var assignments []struct {
		ID                uint      `json:"id"`
		ServiceProviderID uint      `json:"service_provider_id"`
		ProviderName      string    `json:"provider_name"`
		BranchID          uint      `json:"branch_id"`
		BranchName        string    `json:"branch_name"`
		CreatedAt         time.Time `json:"created_at"`
	}
	
	query := `
		SELECT pb.id, pb.service_provider_id, sp.name as provider_name, 
			   pb.branch_id, b.name as branch_name, pb.created_at
		FROM provider_branches pb
		JOIN service_providers sp ON pb.service_provider_id = sp.id
		JOIN branches b ON pb.branch_id = b.id
		ORDER BY sp.name, b.name
	`
	
	if err := db.Raw(query).Scan(&assignments).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao buscar atribuições: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    assignments,
	})
}

// AssignProviderToBranch atribui um prestador a uma filial
func AssignProviderToBranch(c *gin.Context) {
	db := database.GetGormDB()
	
	var request struct {
		ServiceProviderID uint `json:"service_provider_id" binding:"required"`
		BranchID          uint `json:"branch_id" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Dados inválidos: " + err.Error(),
		})
		return
	}
	
	// Criar serviço de atribuição automática
	autoAssignmentService := services.NewAutoAssignmentService(db)
	
	// Atribuir prestador à filial
	if err := autoAssignmentService.AssignProviderToBranch(request.ServiceProviderID, request.BranchID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Prestador atribuído à filial com sucesso",
	})
}

// RemoveProviderBranchAssignment remove a atribuição de um prestador a uma filial
func RemoveProviderBranchAssignment(c *gin.Context) {
	db := database.GetGormDB()
	
	// Obter ID da atribuição
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "ID inválido",
		})
		return
	}
	
	// Buscar a atribuição
	var assignment models.ProviderBranch
	if err := db.First(&assignment, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Atribuição não encontrada",
		})
		return
	}
	
	// Criar serviço de atribuição automática
	autoAssignmentService := services.NewAutoAssignmentService(db)
	
	// Remover atribuição
	if err := autoAssignmentService.RemoveProviderFromBranch(assignment.ServiceProviderID, assignment.BranchID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Atribuição removida com sucesso",
	})
}

// GetProviderEquipmentTypeAssignments retorna as atribuições de prestadores a tipos de equipamento
func GetProviderEquipmentTypeAssignments(c *gin.Context) {
	db := database.GetGormDB()
	
	var assignments []struct {
		ID                uint      `json:"id"`
		ServiceProviderID uint      `json:"service_provider_id"`
		ProviderName      string    `json:"provider_name"`
		EquipmentTypeID   uint      `json:"equipment_type_id"`
		EquipmentTypeName string    `json:"equipment_type_name"`
		CreatedAt         time.Time `json:"created_at"`
	}
	
	query := `
		SELECT pet.id, pet.service_provider_id, sp.name as provider_name, 
			   pet.equipment_type_id, et.name as equipment_type_name, pet.created_at
		FROM provider_equipment_types pet
		JOIN service_providers sp ON pet.service_provider_id = sp.id
		JOIN equipment_types et ON pet.equipment_type_id = et.id
		ORDER BY sp.name, et.name
	`
	
	if err := db.Raw(query).Scan(&assignments).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao buscar atribuições: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    assignments,
	})
}

// AssignProviderToEquipmentType atribui um prestador a um tipo de equipamento
func AssignProviderToEquipmentType(c *gin.Context) {
	db := database.GetGormDB()
	
	var request struct {
		ServiceProviderID uint `json:"service_provider_id" binding:"required"`
		EquipmentTypeID   uint `json:"equipment_type_id" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Dados inválidos: " + err.Error(),
		})
		return
	}
	
	// Criar serviço de atribuição automática
	autoAssignmentService := services.NewAutoAssignmentService(db)
	
	// Atribuir prestador ao tipo de equipamento
	if err := autoAssignmentService.AssignProviderToEquipmentType(request.ServiceProviderID, request.EquipmentTypeID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Prestador atribuído ao tipo de equipamento com sucesso",
	})
}

// RemoveProviderEquipmentTypeAssignment remove a atribuição de um prestador a um tipo de equipamento
func RemoveProviderEquipmentTypeAssignment(c *gin.Context) {
	db := database.GetGormDB()
	
	// Obter ID da atribuição
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "ID inválido",
		})
		return
	}
	
	// Buscar a atribuição
	var assignment models.ProviderEquipmentType
	if err := db.First(&assignment, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Atribuição não encontrada",
		})
		return
	}
	
	// Criar serviço de atribuição automática
	autoAssignmentService := services.NewAutoAssignmentService(db)
	
	// Remover atribuição
	if err := autoAssignmentService.RemoveProviderFromEquipmentType(assignment.ServiceProviderID, assignment.EquipmentTypeID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Atribuição removida com sucesso",
	})
}

// GetEligibleProviders retorna os prestadores elegíveis para uma ordem
func GetEligibleProviders(c *gin.Context) {
	db := database.GetGormDB()
	
	// Obter parâmetros
	branchIDStr := c.Query("branch_id")
	equipmentIDStr := c.Query("equipment_id")
	
	if branchIDStr == "" || equipmentIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Parâmetros branch_id e equipment_id são obrigatórios",
		})
		return
	}
	
	branchID, err := strconv.ParseUint(branchIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "branch_id inválido",
		})
		return
	}
	
	equipmentID, err := strconv.ParseUint(equipmentIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "equipment_id inválido",
		})
		return
	}
	
	// Criar serviço de atribuição automática
	autoAssignmentService := services.NewAutoAssignmentService(db)
	
	// Buscar prestadores elegíveis
	providers, err := autoAssignmentService.GetEligibleProviders(uint(branchID), uint(equipmentID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    providers,
	})
}
