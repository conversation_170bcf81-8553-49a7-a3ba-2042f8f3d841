package handlers

import (
	"net/http"
	"strconv"
	"tradicao/internal/database"
	"tradicao/internal/models"

	"github.com/gin-gonic/gin"
)

// GetEquipmentTypes retorna todos os tipos de equipamento
func GetEquipmentTypes(c *gin.Context) {
	db := database.GetGormDB()
	
	var types []models.EquipmentType
	if err := db.Order("name").Find(&types).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao buscar tipos de equipamento: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    types,
	})
}

// GetEquipmentTypeByID retorna um tipo de equipamento pelo ID
func GetEquipmentTypeByID(c *gin.Context) {
	db := database.GetGormDB()
	
	// Obter ID do tipo de equipamento
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "ID inválido",
		})
		return
	}
	
	// Buscar tipo de equipamento
	var equipmentType models.EquipmentType
	if err := db.First(&equipmentType, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Tipo de equipamento não encontrado",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    equipmentType,
	})
}

// CreateEquipmentType cria um novo tipo de equipamento
func CreateEquipmentType(c *gin.Context) {
	db := database.GetGormDB()
	
	var request struct {
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
	}
	
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Dados inválidos: " + err.Error(),
		})
		return
	}
	
	// Verificar se já existe um tipo com o mesmo nome
	var count int64
	db.Model(&models.EquipmentType{}).Where("name = ?", request.Name).Count(&count)
	if count > 0 {
		c.JSON(http.StatusConflict, gin.H{
			"success": false,
			"message": "Já existe um tipo de equipamento com este nome",
		})
		return
	}
	
	// Criar tipo de equipamento
	equipmentType := models.EquipmentType{
		Name:        request.Name,
		Description: request.Description,
	}
	
	if err := db.Create(&equipmentType).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao criar tipo de equipamento: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Tipo de equipamento criado com sucesso",
		"data":    equipmentType,
	})
}

// UpdateEquipmentType atualiza um tipo de equipamento
func UpdateEquipmentType(c *gin.Context) {
	db := database.GetGormDB()
	
	// Obter ID do tipo de equipamento
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "ID inválido",
		})
		return
	}
	
	var request struct {
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
	}
	
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Dados inválidos: " + err.Error(),
		})
		return
	}
	
	// Buscar tipo de equipamento
	var equipmentType models.EquipmentType
	if err := db.First(&equipmentType, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Tipo de equipamento não encontrado",
		})
		return
	}
	
	// Verificar se já existe outro tipo com o mesmo nome
	var count int64
	db.Model(&models.EquipmentType{}).Where("name = ? AND id != ?", request.Name, id).Count(&count)
	if count > 0 {
		c.JSON(http.StatusConflict, gin.H{
			"success": false,
			"message": "Já existe outro tipo de equipamento com este nome",
		})
		return
	}
	
	// Atualizar tipo de equipamento
	equipmentType.Name = request.Name
	equipmentType.Description = request.Description
	
	if err := db.Save(&equipmentType).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao atualizar tipo de equipamento: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Tipo de equipamento atualizado com sucesso",
		"data":    equipmentType,
	})
}

// DeleteEquipmentType exclui um tipo de equipamento
func DeleteEquipmentType(c *gin.Context) {
	db := database.GetGormDB()
	
	// Obter ID do tipo de equipamento
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "ID inválido",
		})
		return
	}
	
	// Verificar se o tipo de equipamento está sendo usado
	var countEquipments int64
	db.Model(&models.Equipment{}).Where("equipment_type_id = ?", id).Count(&countEquipments)
	if countEquipments > 0 {
		c.JSON(http.StatusConflict, gin.H{
			"success": false,
			"message": "Este tipo de equipamento está sendo usado por equipamentos e não pode ser excluído",
		})
		return
	}
	
	var countAssignments int64
	db.Model(&models.ProviderEquipmentType{}).Where("equipment_type_id = ?", id).Count(&countAssignments)
	if countAssignments > 0 {
		c.JSON(http.StatusConflict, gin.H{
			"success": false,
			"message": "Este tipo de equipamento está sendo usado em atribuições de prestadores e não pode ser excluído",
		})
		return
	}
	
	// Excluir tipo de equipamento
	result := db.Delete(&models.EquipmentType{}, uint(id))
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao excluir tipo de equipamento: " + result.Error.Error(),
		})
		return
	}
	
	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Tipo de equipamento não encontrado",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Tipo de equipamento excluído com sucesso",
	})
}
