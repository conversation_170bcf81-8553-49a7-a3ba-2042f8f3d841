package handlers

import (
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"

	"tradicao/internal/models"
	"tradicao/internal/services"
)

// WebSocketHandler gerencia as conexões WebSocket
type WebSocketHandler struct {
	notificationService *services.NotificationService
}

// NewWebSocketHandler cria um novo manipulador de WebSocket
func NewWebSocketHandler(notificationService *services.NotificationService) *WebSocketHandler {
	return &WebSocketHandler{
		notificationService: notificationService,
	}
}

// Configuração do upgrader WebSocket
var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// Permite conexões de qualquer origem em ambiente de desenvolvimento
		return true
	},
}

// HandleWebSocket gerencia a conexão WebSocket
func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
	// Obtém informações do usuário do contexto
	userID := c.GetInt64("userID")
	userName := c.GetString("userName")
	userRole := models.UserRole(c.GetString("userRole"))

	// Verifica que o usuário está autenticado
	if userID == 0 || userName == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Não autorizado"})
		return
	}

	// Atualiza a conexão HTTP para WebSocket
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("Erro ao atualizar para WebSocket: %v", err)
		return
	}

	// Cria um novo cliente WebSocket
	client := &models.WebSocketClient{
		UserID:   userID,
		Username: userName,
		Role:     userRole,
		Conn:     conn,
		Send:     make(chan []byte, 256),
	}

	// Registra o cliente
	h.notificationService.AddClient(client)

	// Inicia rotinas para o cliente
	go h.writePump(client)
	go h.readPump(client)

	// Envia mensagem de conexão bem-sucedida
	// Serializa e envia a mensagem
	client.Send <- []byte(`{"type":"welcome","message":"Conexão WebSocket estabelecida"}`)
}

// writePump envia mensagens para o cliente WebSocket
func (h *WebSocketHandler) writePump(client *models.WebSocketClient) {
	ticker := time.NewTicker(60 * time.Second)
	defer func() {
		ticker.Stop()
		client.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-client.Send:
			client.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				// Canal fechado
				client.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := client.Conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// Adiciona mensagens pendentes
			n := len(client.Send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-client.Send)
			}

			if err := w.Close(); err != nil {
				return
			}
		case <-ticker.C:
			client.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := client.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// readPump recebe mensagens do cliente WebSocket
func (h *WebSocketHandler) readPump(client *models.WebSocketClient) {
	defer func() {
		h.notificationService.RemoveClient(client)
		client.Conn.Close()
	}()

	client.Conn.SetReadLimit(512)
	client.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	client.Conn.SetPongHandler(func(string) error {
		client.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, _, err := client.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("Erro na conexão WebSocket: %v", err)
			}
			break
		}
		// Processa mensagens recebidas aqui, se necessário
	}
}
