package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"tradicao/internal/config"
	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// FinanceHandlers contém os handlers relacionados à área financeira
type FinanceHandlers struct {
	service services.FinanceService
}

// NewFinanceHandlers cria uma nova instância de FinanceHandlers
func NewFinanceHandlers(service services.FinanceService) *FinanceHandlers {
	return &FinanceHandlers{
		service: service,
	}
}

// Dashboard renderiza a página da dashboard financeira
func (h *FinanceHandlers) Dashboard(c *gin.Context) {
	data, err := h.service.GetDashboardData(c.Request.Context())
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Erro ao carregar dados do painel",
		})
		return
	}

	c.HTML(http.StatusOK, config.ActiveFinanceDashboardTemplate, gin.H{
		"title": "Painel Financeiro",
		"data":  data,
	})
}

// GetDashboardData retorna os dados do dashboard
func (h *FinanceHandlers) GetDashboardData(c *gin.Context) {
	data, err := h.service.GetDashboardData(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar dados do dashboard",
		})
		return
	}

	c.JSON(http.StatusOK, data)
}

// GetQuotes retorna as cotações atualizadas
func (h *FinanceHandlers) GetQuotes(c *gin.Context) {
	quotes, err := h.service.ListQuotes(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar cotações",
		})
		return
	}

	c.JSON(http.StatusOK, quotes)
}

// GetPayments retorna os pagamentos recentes
func (h *FinanceHandlers) GetPayments(c *gin.Context) {
	startDate := time.Now().AddDate(0, -1, 0) // Último mês
	endDate := time.Now()

	filters := &models.PaymentFilters{
		StartDate: startDate,
		EndDate:   endDate,
	}

	payments, err := h.service.ListPayments(c.Request.Context(), filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar pagamentos",
		})
		return
	}

	c.JSON(http.StatusOK, payments)
}

// GetNews retorna as notícias financeiras
func (h *FinanceHandlers) GetNews(c *gin.Context) {
	news, err := h.service.ListNews(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar notícias",
		})
		return
	}

	c.JSON(http.StatusOK, news)
}

// ExportReport exporta um relatório
func (h *FinanceHandlers) ExportReport(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	report, err := h.service.GetReport(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar relatório",
		})
		return
	}

	filename := fmt.Sprintf("%s_%s.pdf", report.Type, time.Now().Format("20060102"))
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Header("Content-Type", "application/pdf")
	c.Header("Content-Length", fmt.Sprintf("%d", len(report.Content)))

	c.String(http.StatusOK, report.Content)
}

// ListPayments lista todos os pagamentos
func (h *FinanceHandlers) ListPayments(c *gin.Context) {
	startDate, _ := time.Parse("2006-01-02", c.Query("start_date"))
	endDate, _ := time.Parse("2006-01-02", c.Query("end_date"))
	status := models.PaymentStatus(c.Query("status"))

	filters := &models.PaymentFilters{
		StartDate: startDate,
		EndDate:   endDate,
		Status:    status,
	}

	payments, err := h.service.ListPayments(c.Request.Context(), filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao listar pagamentos",
		})
		return
	}

	c.JSON(http.StatusOK, payments)
}

// GetPayment retorna um pagamento específico
func (h *FinanceHandlers) GetPayment(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	payment, err := h.service.GetPayment(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar pagamento",
		})
		return
	}

	c.JSON(http.StatusOK, payment)
}

// CreatePayment cria um novo pagamento
func (h *FinanceHandlers) CreatePayment(c *gin.Context) {
	var payment models.Payment
	if err := c.ShouldBindJSON(&payment); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Dados inválidos",
		})
		return
	}

	err := h.service.CreatePayment(c.Request.Context(), &payment)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao criar pagamento",
		})
		return
	}

	c.JSON(http.StatusCreated, payment)
}

// UpdatePayment atualiza um pagamento existente
func (h *FinanceHandlers) UpdatePayment(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	var payment models.Payment
	if err := c.ShouldBindJSON(&payment); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Dados inválidos",
		})
		return
	}

	payment.ID = id
	err = h.service.UpdatePayment(c.Request.Context(), &payment)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao atualizar pagamento",
		})
		return
	}

	c.JSON(http.StatusOK, payment)
}

// DeletePayment remove um pagamento
func (h *FinanceHandlers) DeletePayment(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	err = h.service.DeletePayment(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao remover pagamento",
		})
		return
	}

	c.Status(http.StatusNoContent)
}

// ListInvoices lista todas as notas fiscais
func (h *FinanceHandlers) ListInvoices(c *gin.Context) {
	startDate, _ := time.Parse("2006-01-02", c.Query("start_date"))
	endDate, _ := time.Parse("2006-01-02", c.Query("end_date"))
	status := models.InvoiceStatus(c.Query("status"))

	filters := &models.InvoiceFilters{
		StartDate: startDate,
		EndDate:   endDate,
		Status:    status,
	}

	invoices, err := h.service.ListInvoices(c.Request.Context(), filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao listar notas fiscais",
		})
		return
	}

	c.JSON(http.StatusOK, invoices)
}

// GetInvoice retorna uma nota fiscal específica
func (h *FinanceHandlers) GetInvoice(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	invoice, err := h.service.GetInvoice(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar nota fiscal",
		})
		return
	}

	c.JSON(http.StatusOK, invoice)
}

// CreateInvoice cria uma nova nota fiscal
func (h *FinanceHandlers) CreateInvoice(c *gin.Context) {
	var invoice models.Invoice
	if err := c.ShouldBindJSON(&invoice); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Dados inválidos",
		})
		return
	}

	err := h.service.CreateInvoice(c.Request.Context(), &invoice)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao criar nota fiscal",
		})
		return
	}

	c.JSON(http.StatusCreated, invoice)
}

// UpdateInvoice atualiza uma nota fiscal existente
func (h *FinanceHandlers) UpdateInvoice(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	var invoice models.Invoice
	if err := c.ShouldBindJSON(&invoice); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Dados inválidos",
		})
		return
	}

	invoice.ID = id
	err = h.service.UpdateInvoice(c.Request.Context(), &invoice)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao atualizar nota fiscal",
		})
		return
	}

	c.JSON(http.StatusOK, invoice)
}

// DeleteInvoice remove uma nota fiscal
func (h *FinanceHandlers) DeleteInvoice(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	err = h.service.DeleteInvoice(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao remover nota fiscal",
		})
		return
	}

	c.Status(http.StatusNoContent)
}

// ListBudgets lista todos os orçamentos
func (h *FinanceHandlers) ListBudgets(c *gin.Context) {
	startDate, _ := time.Parse("2006-01-02", c.Query("start_date"))
	endDate, _ := time.Parse("2006-01-02", c.Query("end_date"))

	filters := &models.BudgetFilters{
		StartDate: startDate,
		EndDate:   endDate,
	}

	budgets, err := h.service.ListBudgets(c.Request.Context(), filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao listar orçamentos",
		})
		return
	}

	c.JSON(http.StatusOK, budgets)
}

// GetBudget retorna um orçamento específico
func (h *FinanceHandlers) GetBudget(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	budget, err := h.service.GetBudget(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar orçamento",
		})
		return
	}

	c.JSON(http.StatusOK, budget)
}

// CreateBudget cria um novo orçamento
func (h *FinanceHandlers) CreateBudget(c *gin.Context) {
	var budget models.Budget
	if err := c.ShouldBindJSON(&budget); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Dados inválidos",
		})
		return
	}

	err := h.service.CreateBudget(c.Request.Context(), &budget)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao criar orçamento",
		})
		return
	}

	c.JSON(http.StatusCreated, budget)
}

// UpdateBudget atualiza um orçamento existente
func (h *FinanceHandlers) UpdateBudget(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	var budget models.Budget
	if err := c.ShouldBindJSON(&budget); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Dados inválidos",
		})
		return
	}

	budget.ID = id
	err = h.service.UpdateBudget(c.Request.Context(), &budget)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao atualizar orçamento",
		})
		return
	}

	c.JSON(http.StatusOK, budget)
}

// DeleteBudget remove um orçamento
func (h *FinanceHandlers) DeleteBudget(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	err = h.service.DeleteBudget(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao remover orçamento",
		})
		return
	}

	c.Status(http.StatusNoContent)
}

// ListReports lista os relatórios
func (h *FinanceHandlers) ListReports(c *gin.Context) {
	startDate, _ := time.Parse("2006-01-02", c.Query("start_date"))
	endDate, _ := time.Parse("2006-01-02", c.Query("end_date"))
	reportType := models.ReportType(c.Query("type"))

	filters := &models.ReportFilters{
		StartDate: startDate,
		EndDate:   endDate,
		Type:      reportType,
	}

	reports, err := h.service.ListReports(c.Request.Context(), filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao listar relatórios",
		})
		return
	}

	c.JSON(http.StatusOK, reports)
}

// GetReport retorna um relatório específico
func (h *FinanceHandlers) GetReport(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	report, err := h.service.GetReport(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar relatório",
		})
		return
	}

	c.JSON(http.StatusOK, report)
}

// CreateReport cria um novo relatório
func (h *FinanceHandlers) CreateReport(c *gin.Context) {
	var report models.Report
	if err := c.ShouldBindJSON(&report); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Dados inválidos",
		})
		return
	}

	err := h.service.CreateReport(c.Request.Context(), &report)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao criar relatório",
		})
		return
	}

	c.JSON(http.StatusCreated, report)
}

// UpdateReport atualiza um relatório existente
func (h *FinanceHandlers) UpdateReport(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	var report models.Report
	if err := c.ShouldBindJSON(&report); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Dados inválidos",
		})
		return
	}

	report.ID = id
	err = h.service.CreateReport(c.Request.Context(), &report)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao atualizar relatório",
		})
		return
	}

	c.JSON(http.StatusOK, report)
}

// DeleteReport remove um relatório
func (h *FinanceHandlers) DeleteReport(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	err = h.service.DeleteReport(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao remover relatório",
		})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetSettings retorna as configurações financeiras
func (h *FinanceHandlers) GetSettings(c *gin.Context) {
	settings, err := h.service.GetSettings(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar configurações",
		})
		return
	}

	c.JSON(http.StatusOK, settings)
}

// UpdateSettings atualiza as configurações financeiras
func (h *FinanceHandlers) UpdateSettings(c *gin.Context) {
	var settings models.FinanceSettings
	if err := c.ShouldBindJSON(&settings); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Dados inválidos",
		})
		return
	}

	err := h.service.UpdateSettings(c.Request.Context(), &settings)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao atualizar configurações",
		})
		return
	}

	c.JSON(http.StatusOK, settings)
}

// FinanceDashboard renderiza a página do painel financeiro
func (h *FinanceHandlers) FinanceDashboard(c *gin.Context) {
	// Obter os dados necessários para o painel
	data, err := h.service.GetDashboardData(c.Request.Context())
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Erro ao carregar dados do painel financeiro",
		})
		return
	}

	// Renderiza a página
	c.HTML(http.StatusOK, config.ActiveFinanceDashboardTemplate, gin.H{
		"title": "Painel Financeiro",
		"data":  data,
	})
}

// getDateRange retorna as datas inicial e final com base no período selecionado
func getDateRange(period string) (time.Time, time.Time) {
	now := time.Now()
	var startDate, endDate time.Time

	switch period {
	case "week":
		startDate = now.AddDate(0, 0, -7)
	case "month":
		startDate = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	case "quarter":
		startDate = time.Date(now.Year(), now.Month()-3, 1, 0, 0, 0, 0, now.Location())
	case "year":
		startDate = time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())
	default:
		startDate = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	}

	endDate = now
	return startDate, endDate
}
