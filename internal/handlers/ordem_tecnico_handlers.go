package handlers

import (
	"net/http"
	"strconv"
	"time"
	"tradicao/internal/database"
	"tradicao/internal/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Estruturas para os dados dos cards
type ManutencaoData struct {
	OrdemID         int       `json:"ordem_id"`
	Descricao       string    `json:"descricao"`
	PecasUtilizadas string    `json:"pecas_utilizadas"`
	Observacoes     string    `json:"observacoes"`
	DataRegistro    time.Time `json:"data_registro"`
	TecnicoID       int       `json:"tecnico_id"`
	TecnicoNome     string    `json:"tecnico_nome"`
}

type CustosData struct {
	OrdemID      int       `json:"ordem_id"`
	Pecas        float64   `json:"pecas"`
	MaoObra      float64   `json:"mao_obra"`
	Deslocamento float64   `json:"deslocamento"`
	Total        float64   `json:"total"`
	DataRegistro time.Time `json:"data_registro"`
	TecnicoID    int       `json:"tecnico_id"`
	TecnicoNome  string    `json:"tecnico_nome"`
}

type CronogramaData struct {
	OrdemID      int       `json:"ordem_id"`
	DataInicio   string    `json:"data_inicio"`
	HoraInicio   string    `json:"hora_inicio"`
	DataFim      string    `json:"data_fim"`
	HoraFim      string    `json:"hora_fim"`
	Status       string    `json:"status"`
	DataRegistro time.Time `json:"data_registro"`
	TecnicoID    int       `json:"tecnico_id"`
	TecnicoNome  string    `json:"tecnico_nome"`
}

type MensagemChat struct {
	OrdemID     int       `json:"ordem_id"`
	Mensagem    string    `json:"mensagem"`
	Remetente   string    `json:"remetente"`
	RemetenteID int       `json:"remetente_id"`
	DataEnvio   time.Time `json:"data_envio"`
}

// Resposta padrão da API
type APIResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    any    `json:"data,omitempty"`
}

// Handler para salvar dados de manutenção
func SaveManutencaoHandler(c *gin.Context) {
	var data ManutencaoData
	if err := c.ShouldBindJSON(&data); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Message: "Dados inválidos: " + err.Error(),
		})
		return
	}

	// Verificar se a ordem existe e se o usuário tem permissão para acessá-la
	db := database.GetGormDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao conectar ao banco de dados",
		})
		return
	}

	// Verificar se a ordem existe
	var ordem models.MaintenanceOrder
	if err := db.First(&ordem, data.OrdemID).Error; err != nil {
		c.JSON(http.StatusNotFound, APIResponse{
			Success: false,
			Message: "Ordem não encontrada",
		})
		return
	}

	// Obter ID do usuário da sessão
	userID, _ := c.Get("userID")
	userName, _ := c.Get("userName")

	// Converter para int
	var tecnicoID int
	switch v := userID.(type) {
	case uint:
		tecnicoID = int(v)
	case int:
		tecnicoID = v
	case int64:
		tecnicoID = int(v)
	case float64:
		tecnicoID = int(v)
	case string:
		tecnicoID, _ = strconv.Atoi(v)
	default:
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao converter ID do usuário",
		})
		return
	}

	// Atualizar dados com informações do usuário
	data.TecnicoID = tecnicoID
	data.TecnicoNome = userName.(string)
	data.DataRegistro = time.Now()

	// Já temos a conexão com o banco de dados

	// Converter para o modelo do banco de dados
	manutencaoModel := models.ManutencaoData{
		OrdemID:         uint(data.OrdemID),
		Descricao:       data.Descricao,
		PecasUtilizadas: data.PecasUtilizadas,
		Observacoes:     data.Observacoes,
		DataRegistro:    data.DataRegistro,
		TecnicoID:       uint(data.TecnicoID),
		TecnicoNome:     data.TecnicoNome,
	}

	// Salvar no banco de dados
	result := db.Create(&manutencaoModel)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao salvar dados de manutenção: " + result.Error.Error(),
		})
		return
	}

	// Registrar no histórico
	registrarHistorico(db, uint(data.OrdemID), uint(data.TecnicoID), "Dados de manutenção atualizados")

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "Dados de manutenção salvos com sucesso",
		Data:    manutencaoModel,
	})
}

// Handler para salvar dados de custos
func SaveCustosHandler(c *gin.Context) {
	var data CustosData
	if err := c.ShouldBindJSON(&data); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Message: "Dados inválidos: " + err.Error(),
		})
		return
	}

	// Verificar se a ordem existe
	db := database.GetGormDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao conectar ao banco de dados",
		})
		return
	}

	var ordem models.MaintenanceOrder
	if err := db.First(&ordem, data.OrdemID).Error; err != nil {
		c.JSON(http.StatusNotFound, APIResponse{
			Success: false,
			Message: "Ordem não encontrada",
		})
		return
	}

	// Obter ID do usuário da sessão
	userID, _ := c.Get("userID")
	userName, _ := c.Get("userName")

	// Converter para int
	var tecnicoID int
	switch v := userID.(type) {
	case uint:
		tecnicoID = int(v)
	case int:
		tecnicoID = v
	case int64:
		tecnicoID = int(v)
	case float64:
		tecnicoID = int(v)
	case string:
		tecnicoID, _ = strconv.Atoi(v)
	default:
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao converter ID do usuário",
		})
		return
	}

	// Atualizar dados com informações do usuário
	data.TecnicoID = tecnicoID
	data.TecnicoNome = userName.(string)
	data.DataRegistro = time.Now()

	// Calcular o total para garantir consistência
	data.Total = data.Pecas + data.MaoObra + data.Deslocamento

	// Já temos a conexão com o banco de dados

	// Converter para o modelo do banco de dados
	custosModel := models.CustosData{
		OrdemID:      uint(data.OrdemID),
		Pecas:        data.Pecas,
		MaoObra:      data.MaoObra,
		Deslocamento: data.Deslocamento,
		Total:        data.Total,
		DataRegistro: data.DataRegistro,
		TecnicoID:    uint(data.TecnicoID),
		TecnicoNome:  data.TecnicoNome,
	}

	// Salvar no banco de dados
	result := db.Create(&custosModel)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao salvar dados de custos: " + result.Error.Error(),
		})
		return
	}

	// Registrar no histórico
	registrarHistorico(db, uint(data.OrdemID), uint(data.TecnicoID), "Dados de custos atualizados")

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "Dados de custos salvos com sucesso",
		Data:    custosModel,
	})
}

// Handler para salvar dados de cronograma
func SaveCronogramaHandler(c *gin.Context) {
	var data CronogramaData
	if err := c.ShouldBindJSON(&data); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Message: "Dados inválidos: " + err.Error(),
		})
		return
	}

	// Verificar se a ordem existe
	db := database.GetGormDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao conectar ao banco de dados",
		})
		return
	}

	var ordem models.MaintenanceOrder
	if err := db.First(&ordem, data.OrdemID).Error; err != nil {
		c.JSON(http.StatusNotFound, APIResponse{
			Success: false,
			Message: "Ordem não encontrada",
		})
		return
	}

	// Obter ID do usuário da sessão
	userID, _ := c.Get("userID")
	userName, _ := c.Get("userName")

	// Converter para int
	var tecnicoID int
	switch v := userID.(type) {
	case uint:
		tecnicoID = int(v)
	case int:
		tecnicoID = v
	case int64:
		tecnicoID = int(v)
	case float64:
		tecnicoID = int(v)
	case string:
		tecnicoID, _ = strconv.Atoi(v)
	default:
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao converter ID do usuário",
		})
		return
	}

	// Atualizar dados com informações do usuário
	data.TecnicoID = tecnicoID
	data.TecnicoNome = userName.(string)
	data.DataRegistro = time.Now()

	// Já temos a conexão com o banco de dados

	// Converter para o modelo do banco de dados
	cronogramaModel := models.CronogramaData{
		OrdemID:      uint(data.OrdemID),
		DataInicio:   data.DataInicio,
		HoraInicio:   data.HoraInicio,
		DataFim:      data.DataFim,
		HoraFim:      data.HoraFim,
		Status:       data.Status,
		DataRegistro: data.DataRegistro,
		TecnicoID:    uint(data.TecnicoID),
		TecnicoNome:  data.TecnicoNome,
	}

	// Salvar no banco de dados
	result := db.Create(&cronogramaModel)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao salvar dados de cronograma: " + result.Error.Error(),
		})
		return
	}

	// Registrar no histórico
	registrarHistorico(db, uint(data.OrdemID), uint(data.TecnicoID), "Dados de cronograma atualizados")

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "Dados de cronograma salvos com sucesso",
		Data:    cronogramaModel,
	})
}

// Handler para salvar mensagem de chat
func SaveChatMessageHandler(c *gin.Context) {
	var data MensagemChat
	if err := c.ShouldBindJSON(&data); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Message: "Dados inválidos: " + err.Error(),
		})
		return
	}

	// Verificar se a ordem existe
	db := database.GetGormDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao conectar ao banco de dados",
		})
		return
	}

	var ordem models.MaintenanceOrder
	if err := db.First(&ordem, data.OrdemID).Error; err != nil {
		c.JSON(http.StatusNotFound, APIResponse{
			Success: false,
			Message: "Ordem não encontrada",
		})
		return
	}

	// Obter ID do usuário da sessão
	userID, _ := c.Get("userID")
	userName, _ := c.Get("userName")

	// Converter para int
	var remetenteID int
	switch v := userID.(type) {
	case uint:
		remetenteID = int(v)
	case int:
		remetenteID = v
	case int64:
		remetenteID = int(v)
	case float64:
		remetenteID = int(v)
	case string:
		remetenteID, _ = strconv.Atoi(v)
	default:
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao converter ID do usuário",
		})
		return
	}

	// Atualizar dados com informações do usuário
	data.RemetenteID = remetenteID
	data.Remetente = userName.(string)
	data.DataEnvio = time.Now()

	// Já temos a conexão com o banco de dados

	// Converter para o modelo do banco de dados
	mensagemModel := models.MensagemChat{
		OrdemID:     uint(data.OrdemID),
		Mensagem:    data.Mensagem,
		Remetente:   data.Remetente,
		RemetenteID: uint(data.RemetenteID),
		DataEnvio:   data.DataEnvio,
	}

	// Salvar no banco de dados
	result := db.Create(&mensagemModel)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao salvar mensagem de chat: " + result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "Mensagem enviada com sucesso",
		Data:    mensagemModel,
	})
}

// Handler para obter dados de uma ordem específica
func GetOrdemHandler(c *gin.Context) {
	ordemID := c.Param("id")

	// Converter para int
	id, err := strconv.Atoi(ordemID)
	if err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Message: "ID de ordem inválido",
		})
		return
	}

	// Verificar permissões através do sistema centralizado
	_, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Message: "Usuário não autenticado",
		})
		return
	}

	// Obter conexão com o banco de dados
	db := database.GetGormDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao conectar ao banco de dados",
		})
		return
	}

	// Buscar ordem no banco de dados
	var ordem models.MaintenanceOrder
	result := db.First(&ordem, id)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, APIResponse{
				Success: false,
				Message: "Ordem não encontrada",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao buscar ordem: " + result.Error.Error(),
		})
		return
	}

	// Buscar dados de manutenção
	var manutencao models.ManutencaoData
	db.Where("ordem_id = ?", id).Order("created_at DESC").First(&manutencao)

	// Buscar dados de custos
	var custos models.CustosData
	db.Where("ordem_id = ?", id).Order("created_at DESC").First(&custos)

	// Buscar dados de cronograma
	var cronograma models.CronogramaData
	db.Where("ordem_id = ?", id).Order("created_at DESC").First(&cronograma)

	// Buscar mensagens de chat
	var mensagens []models.MensagemChat
	db.Where("ordem_id = ?", id).Order("data_envio ASC").Find(&mensagens)

	// Buscar informações da filial
	var filial models.Branch
	if err := db.First(&filial, ordem.BranchID).Error; err == nil {
		ordem.BranchName = filial.Name
	} else {
		ordem.BranchName = "Filial " + strconv.Itoa(int(ordem.BranchID))
	}

	// Buscar informações do equipamento
	var equipamento models.Equipment
	if err := db.First(&equipamento, ordem.EquipmentID).Error; err == nil {
		ordem.EquipmentName = equipamento.Name
	} else {
		ordem.EquipmentName = ordem.Number
	}

	// Buscar informações do técnico
	var tecnico models.User
	var tecnicoNome string
	if ordem.TechnicianID != nil {
		if err := db.First(&tecnico, *ordem.TechnicianID).Error; err != nil {
			tecnicoNome = "Técnico " + strconv.Itoa(int(*ordem.TechnicianID))
		} else {
			tecnicoNome = tecnico.Name
		}
	} else {
		tecnicoNome = "Não atribuído"
	}

	// Normalizar status e prioridade para garantir formato padronizado
	ordem.Status = models.NormalizeOrderStatus(string(ordem.Status))
	ordem.Priority = models.NormalizePriorityLevel(string(ordem.Priority))

	// Usar o método ToAPIResponse para obter resposta padronizada
	ordemResponse := ordem.ToAPIResponse()

	// Adicionar campos específicos que não estão no método ToAPIResponse
	ordemDetalhada := ordemResponse
	ordemDetalhada["titulo"] = ordemResponse["title"]
	ordemDetalhada["equipamento"] = ordemResponse["equipment_name"]
	ordemDetalhada["filial"] = ordemResponse["branch_name"]
	ordemDetalhada["prioridade"] = ordemResponse["priority"]
	ordemDetalhada["status"] = ordemResponse["status"]
	ordemDetalhada["data"] = ordemResponse["due_date"]
	ordemDetalhada["responsavel"] = tecnicoNome
	ordemDetalhada["tempo"] = "2 horas" // Valor padrão, pode ser calculado com base em outros campos
	ordemDetalhada["descricao"] = ordemResponse["problem"]

	// Adicionar dados específicos do técnico
	ordemDetalhada["manutencao"] = map[string]any{
		"descricao":        manutencao.Descricao,
		"pecas_utilizadas": manutencao.PecasUtilizadas,
		"observacoes":      manutencao.Observacoes,
	}

	ordemDetalhada["custos"] = map[string]any{
		"pecas":        custos.Pecas,
		"mao_obra":     custos.MaoObra,
		"deslocamento": custos.Deslocamento,
		"total":        custos.Total,
	}

	ordemDetalhada["cronograma"] = map[string]any{
		"data_inicio": cronograma.DataInicio,
		"hora_inicio": cronograma.HoraInicio,
		"data_fim":    cronograma.DataFim,
		"hora_fim":    cronograma.HoraFim,
		"status":      cronograma.Status,
	}

	ordemDetalhada["chat"] = mensagens

	// Adicionar textos de exibição para status e prioridade
	ordemDetalhada["status_display"] = models.GetOrderStatusDisplay(ordem.Status)
	ordemDetalhada["priority_display"] = models.GetPriorityLevelDisplay(ordem.Priority)

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "Dados da ordem obtidos com sucesso",
		Data:    ordemDetalhada,
	})
}

// Handler para atualizar dados de uma ordem
func UpdateOrdemHandler(c *gin.Context) {
	// Obter ID da ordem
	ordemID := c.Param("id")
	id, err := strconv.Atoi(ordemID)
	if err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Message: "ID de ordem inválido",
		})
		return
	}

	// Bloquear acesso à ordem #18 que está causando problemas
	if id == 18 {
		c.JSON(http.StatusForbidden, APIResponse{
			Success: false,
			Message: "Esta ordem não está disponível para atualização. Por favor, contate o suporte.",
		})
		return
	}

	// Obter dados do corpo da requisição
	var dados map[string]any
	if err := c.ShouldBindJSON(&dados); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Message: "Dados inválidos: " + err.Error(),
		})
		return
	}

	// Obter ID do usuário da sessão
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Message: "Usuário não autenticado",
		})
		return
	}

	// Converter para uint
	var userIDUint uint
	switch v := userID.(type) {
	case uint:
		userIDUint = v
	case int:
		userIDUint = uint(v)
	case int64:
		userIDUint = uint(v)
	case float64:
		userIDUint = uint(v)
	case string:
		userIDInt, _ := strconv.ParseUint(v, 10, 32)
		userIDUint = uint(userIDInt)
	default:
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao converter ID do usuário",
		})
		return
	}

	// Obter conexão com o banco de dados
	db := database.GetGormDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao conectar ao banco de dados",
		})
		return
	}

	// Buscar ordem no banco de dados
	var ordem models.MaintenanceOrder
	result := db.First(&ordem, id)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, APIResponse{
				Success: false,
				Message: "Ordem não encontrada",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao buscar ordem: " + result.Error.Error(),
		})
		return
	}

	// Atualizar campos da ordem com base nos dados recebidos
	// Apenas campos permitidos podem ser atualizados
	camposPermitidos := []string{"status", "problem", "description", "completion_date", "priority"}
	camposAtualizados := []string{}

	for _, campo := range camposPermitidos {
		if valor, ok := dados[campo]; ok {
			switch campo {
			case "status":
				if status, ok := valor.(string); ok {
					// Normalizar status para garantir formato padronizado
					ordem.Status = models.NormalizeOrderStatus(status)
					camposAtualizados = append(camposAtualizados, "status")
				}
			case "priority":
				if priority, ok := valor.(string); ok {
					// Normalizar prioridade para garantir formato padronizado
					ordem.Priority = models.NormalizePriorityLevel(priority)
					camposAtualizados = append(camposAtualizados, "priority")
				}
			case "problem":
				if problem, ok := valor.(string); ok {
					ordem.Problem = problem
					camposAtualizados = append(camposAtualizados, "problem")
				}
			case "description":
				if description, ok := valor.(string); ok {
					ordem.Description = description
					camposAtualizados = append(camposAtualizados, "description")
				}
			case "completion_date":
				if completionDate, ok := valor.(string); ok {
					parsedDate, err := time.Parse("2006-01-02", completionDate)
					if err == nil {
						ordem.CompletionDate = &parsedDate
						camposAtualizados = append(camposAtualizados, "completion_date")
					}
				}
			}
		}
	}

	// Se não houver campos para atualizar, retornar erro
	if len(camposAtualizados) == 0 {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Message: "Nenhum campo válido para atualização",
		})
		return
	}

	// Atualizar a ordem no banco de dados
	if err := db.Save(&ordem).Error; err != nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao atualizar ordem: " + err.Error(),
		})
		return
	}

	// Registrar histórico
	descricao := "Atualização de ordem: " + ordemID + " - Campos: " + strconv.Itoa(len(camposAtualizados))
	registrarHistorico(db, uint(id), userIDUint, descricao)

	// Usar o método ToAPIResponse para obter resposta padronizada
	ordemResponse := ordem.ToAPIResponse()

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "Ordem atualizada com sucesso",
		Data:    ordemResponse,
	})
}

// Função auxiliar para registrar histórico de alterações
func registrarHistorico(db *gorm.DB, ordemID, usuarioID uint, descricao string) {
	// Obter nome do usuário
	var usuario models.User
	db.First(&usuario, usuarioID)

	// Criar registro de histórico
	historico := models.HistoricoOrdem{
		OrdemID:     ordemID,
		UsuarioID:   usuarioID,
		UsuarioNome: usuario.Name,
		DataEvento:  time.Now(),
		TipoEvento:  models.EventoAtualizacao,
		Observacao:  descricao,
	}

	// Salvar no banco de dados
	db.Create(&historico)
}
