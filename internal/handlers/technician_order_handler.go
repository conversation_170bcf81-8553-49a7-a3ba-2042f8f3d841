package handlers

import (
	"net/http"
	"strconv"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// TechnicianOrderHandler é o handler para gerenciar relacionamentos entre técnicos e ordens
type TechnicianOrderHandler struct {
	service *services.TechnicianOrderService
}

// NewTechnicianOrderHandler cria uma nova instância do handler
func NewTechnicianOrderHandler(service *services.TechnicianOrderService) *TechnicianOrderHandler {
	return &TechnicianOrderHandler{service: service}
}

// AssignOrderToTechnician atribui uma ordem a um técnico
func (h *TechnicianOrderHandler) AssignOrderToTechnician(c *gin.Context) {
	// Estrutura para receber os dados da requisição
	var request struct {
		TechnicianID uint   `json:"technician_id" binding:"required"`
		OrderID      uint   `json:"order_id" binding:"required"`
		Notes        string `json:"notes"`
	}

	// Validar dados da requisição
	if err := c.Should<PERSON>(&request); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Message: "Dados inválidos: " + err.Error(),
		})
		return
	}

	// Obter ID do usuário que está fazendo a atribuição
	createdByID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Message: "Usuário não autenticado",
		})
		return
	}

	// Converter para uint
	createdBy := uint(createdByID.(int))

	// Atribuir ordem ao técnico
	err := h.service.AssignOrderToTechnician(request.TechnicianID, request.OrderID, createdBy, request.Notes)
	if err != nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao atribuir ordem ao técnico: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "Ordem atribuída ao técnico com sucesso",
	})
}

// RemoveOrderFromTechnician remove uma ordem de um técnico
func (h *TechnicianOrderHandler) RemoveOrderFromTechnician(c *gin.Context) {
	// Obter IDs da URL
	technicianID, err := strconv.ParseUint(c.Param("technicianId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Message: "ID do técnico inválido",
		})
		return
	}

	orderID, err := strconv.ParseUint(c.Param("orderId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Message: "ID da ordem inválido",
		})
		return
	}

	// Remover ordem do técnico
	err = h.service.UnassignOrderFromTechnician(uint(technicianID), uint(orderID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao remover ordem do técnico: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "Ordem removida do técnico com sucesso",
	})
}

// GetOrdersByTechnician retorna todas as ordens associadas a um técnico
func (h *TechnicianOrderHandler) GetOrdersByTechnician(c *gin.Context) {
	// Obter ID do técnico da URL
	technicianID, err := strconv.ParseUint(c.Param("technicianId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Message: "ID do técnico inválido",
		})
		return
	}

	// Obter ordens do técnico
	orders, err := h.service.GetOrdersByTechnician(uint(technicianID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao obter ordens do técnico: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "Ordens obtidas com sucesso",
		Data:    orders,
	})
}

// GetTechniciansByOrder retorna todos os técnicos associados a uma ordem
func (h *TechnicianOrderHandler) GetTechniciansByOrder(c *gin.Context) {
	// Obter ID da ordem da URL
	orderID, err := strconv.ParseUint(c.Param("orderId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Message: "ID da ordem inválido",
		})
		return
	}

	// Obter técnicos da ordem
	technicians, err := h.service.GetTechniciansByOrder(uint(orderID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao obter técnicos da ordem: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "Técnicos obtidos com sucesso",
		Data:    technicians,
	})
}

// BatchAssignOrders atribui várias ordens a um técnico
func (h *TechnicianOrderHandler) BatchAssignOrders(c *gin.Context) {
	// Estrutura para receber os dados da requisição
	var request struct {
		TechnicianID uint   `json:"technician_id" binding:"required"`
		OrderIDs     []uint `json:"order_ids" binding:"required"`
		Notes        string `json:"notes"`
	}

	// Validar dados da requisição
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Message: "Dados inválidos: " + err.Error(),
		})
		return
	}

	// Obter ID do usuário que está fazendo a atribuição
	createdByID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Message: "Usuário não autenticado",
		})
		return
	}

	// Converter para uint
	createdBy := uint(createdByID.(int))

	// Atribuir ordens ao técnico
	successCount, err := h.service.BatchAssignOrders(request.TechnicianID, request.OrderIDs, createdBy, request.Notes)
	if err != nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Message: "Erro ao atribuir ordens ao técnico: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "Ordens atribuídas ao técnico com sucesso",
		Data: gin.H{
			"total":         len(request.OrderIDs),
			"success_count": successCount,
		},
	})
}
