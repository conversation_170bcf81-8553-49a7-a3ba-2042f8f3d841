package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// UserAvatarHandler gerencia requisições relacionadas a avatares de usuários
type UserAvatarHandler struct {
	userService   *services.UserService
	uploadService *services.FileUploadService
}

// NewUserAvatarHandler cria um novo UserAvatarHandler
func NewUserAvatarHandler(
	userService *services.UserService,
	uploadService *services.FileUploadService,
) *UserAvatarHandler {
	return &UserAvatarHandler{
		userService:   userService,
		uploadService: uploadService,
	}
}

// UploadUserAvatar faz upload do avatar de um usuário
func (h *UserAvatarHandler) UploadUserAvatar(c *gin.Context) {
	// Obter ID do usuário
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do usuário inválido"})
		return
	}

	// Verificar permissão (usuário só pode alterar seu próprio avatar ou ser admin)
	loggedUserID := c.GetUint("userID")
	loggedUserRole := c.GetString("userRole")

	if loggedUserID != uint(userID) && loggedUserRole != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Sem permissão para alterar este avatar"})
		return
	}

	// Obter arquivo do formulário
	file, err := c.FormFile("avatar")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Arquivo não fornecido ou inválido"})
		return
	}

	// Verificar tamanho máximo (1MB)
	if file.Size > 1*1024*1024 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tamanho máximo de arquivo excedido (1MB)"})
		return
	}

	// Fazer upload do arquivo
	avatarURL, err := h.uploadService.UploadFile(file, services.UploadTypeAvatar)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao fazer upload do arquivo", "details": err.Error()})
		return
	}

	// Verificar se o usuário existe
	_, err = h.userService.GetUserByID(int64(userID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao obter dados do usuário"})
		return
	}

	// Atualizar URL do avatar no banco de dados
	if err := h.userService.GetUserRepo().UpdateAvatar(uint(userID), avatarURL); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar avatar do usuário"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":    "Avatar atualizado com sucesso",
		"avatar_url": avatarURL,
	})
}

// UploadCurrentUserAvatar faz upload do avatar do usuário atual
func (h *UserAvatarHandler) UploadCurrentUserAvatar(c *gin.Context) {
	// Obter ID do usuário logado
	userID := c.GetUint("userID")

	// Redirecionar para a rota de upload com o ID do usuário logado
	c.Params = append(c.Params, gin.Param{Key: "id", Value: fmt.Sprintf("%d", userID)})
	h.UploadUserAvatar(c)
}
