package handlers

import (
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"tradicao/internal/services"
)

// ListarOrdensHTML manipula a exibição da página de listagem de ordens
func ListarOrdensHTML(c *gin.Context) {
	c.HTML(http.StatusOK, "ordens/orders.html", gin.H{
		"title": "Ordens de Manutenção",
	})
}

// CriarOrdemHTML manipula a exibição da página de criação de ordens
func CriarOrdemHTML(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.Redirect(http.StatusFound, "/login")
		return
	}

	// Verificar se o usuário tem permissão para criar ordens
	userRole, _ := c.Get("userRole")
	if userRole.(string) != "branch_admin" && userRole.(string) != "station_staff" {
		c.HTML(http.StatusForbidden, "error.html", gin.H{
			"error": "Você não tem permissão para criar ordens de manutenção",
		})
		return
	}

	// Obter o ID da filial do usuário
	branchID, exists := c.Get("filialID")
	if !exists {
		// Tentar obter de outra chave (compatibilidade)
		branchID, exists = c.Get("branchID")
		if !exists {
			// Valor padrão se não encontrar
			branchID = uint(0)
		}
	}

	// Obter o nome da filial, se disponível
	branchName, _ := c.Get("filialName")
	if branchName == nil {
		branchName = "Filial"
	}

	// Log para debug
	log.Printf("[ORDEM-CREATE] Renderizando página de criação de ordem para usuário %v da filial %v (%v)", userID, branchID, branchName)

	c.HTML(http.StatusOK, "ordens/create_order.html", gin.H{
		"title":      "Nova Ordem de Manutenção",
		"userID":     userID,
		"BranchID":   branchID,
		"BranchName": branchName,
	})
}

// DetalhesOrdemHTML manipula a exibição da página de detalhes de uma ordem
func DetalhesOrdemHTML(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "ID inválido",
		})
		return
	}

	ordem, err := services.ObterOrdem(uint(id))
	if err != nil {
		c.HTML(http.StatusNotFound, "error.html", gin.H{
			"error": "Ordem não encontrada",
		})
		return
	}

	c.HTML(http.StatusOK, "ordens/details.html", gin.H{
		"title": "Detalhes da Ordem #" + strconv.FormatUint(id, 10),
		"ordem": ordem,
	})
}

// EditarOrdemHTML manipula a exibição da página de edição de uma ordem
func EditarOrdemHTML(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "ID inválido",
		})
		return
	}

	ordem, err := services.ObterOrdem(uint(id))
	if err != nil {
		c.HTML(http.StatusNotFound, "error.html", gin.H{
			"error": "Ordem não encontrada",
		})
		return
	}

	// Verificar se o usuário tem permissão para editar ordens
	userRole, _ := c.Get("userRole")
	if userRole.(string) != "branch_admin" &&
		userRole.(string) != "station_staff" &&
		userRole.(string) != "technician" {
		c.HTML(http.StatusForbidden, "error.html", gin.H{
			"error": "Você não tem permissão para editar ordens de manutenção",
		})
		return
	}

	/* // TEMPORARIAMENTE COMENTADO - Lógica de verificação de status editável
	// Verificar se a ordem está em um estado editável (Exemplo: Pendente ou Em Andamento)
	// Compara os valores string subjacentes para evitar problemas com tipos alias
	if string(ordem.Status) != string(models.StatusEmAtendimento) && // Compara como string
		string(ordem.Status) != string(models.StatusPendenteAprovacao) { // Compara como string
		c.HTML(http.StatusForbidden, "error.html", gin.H{
			"error": "Esta ordem não pode ser editada no estado atual (Status: " + string(ordem.Status) + ")",
		})
		return
	}
	*/

	c.HTML(http.StatusOK, "ordens/edit.html", gin.H{
		"title": "Editar Ordem #" + strconv.FormatUint(id, 10),
		"ordem": ordem,
	})
}

// OrdemCalendarioHTML manipula a exibição da página de calendário de ordens
func OrdemCalendarioHTML(c *gin.Context) {
	// Obter mês e ano da query, ou usar o mês atual
	mes, _ := strconv.Atoi(c.DefaultQuery("mes", strconv.Itoa(int(time.Now().Month()))))
	ano, _ := strconv.Atoi(c.DefaultQuery("ano", strconv.Itoa(time.Now().Year())))

	// Validar mês e ano
	if mes < 1 || mes > 12 {
		mes = int(time.Now().Month())
	}
	if ano < 2000 || ano > 2100 {
		ano = time.Now().Year()
	}

	c.HTML(http.StatusOK, "ordens/calendar.html", gin.H{
		"title": "Calendário de Manutenções",
		"mes":   mes,
		"ano":   ano,
	})
}
