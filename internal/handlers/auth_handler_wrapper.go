package handlers

import (
	"log"

	"github.com/gin-gonic/gin"
)

// AuthHandlerWrapper é uma função que retorna um handler para autenticação
// Esta é uma função wrapper para compatibilidade com código legado
func AuthHandlerWrapper(db interface{}) gin.HandlerFunc {
	log.Println("Criando AuthHandler wrapper")

	// Retorna um handler simples para compatibilidade
	return func(c *gin.Context) {
		// Implementação temporária para compatibilidade
		c.JSON(200, gin.H{"message": "Login simulado para compatibilidade"})
	}
}
