package handlers

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"tradicao/internal/models"
)

func CreateMaintenanceOrder(w http.ResponseWriter, r *http.Request, db *sql.DB) {
	var orderReq models.MaintenanceOrderRequest
	err := json.NewDecoder(r.Body).Decode(&orderReq)
	if err != nil {
		http.Error(w, "Invalid request payload", http.StatusBadRequest)
		return
	}

	// Validate required fields
	if orderReq.BranchID == 0 || orderReq.Title == "" || orderReq.Description == "" {
		http.Error(w, "Missing required fields", http.StatusBadRequest)
		return
	}

	// Get user ID from context or request
	userID := r.Context().Value("userID")
	if userID == nil {
		http.Error(w, "User not authenticated", http.StatusUnauthorized)
		return
	}

	// Get current time
	now := time.Now()

	// Insert order
	query := `
                INSERT INTO maintenance_orders (
                        title, description, type, station_id, status, priority, 
                        created_by, assigned_to, equipment, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	result, err := db.Exec(
		query,
		orderReq.Title,
		orderReq.Description,
		orderReq.Type,
		orderReq.BranchID,    // Usando BranchID como substituto de StationID
		models.StatusPending, // Default status for new orders
		orderReq.Priority,
		userID,
		nil, // Sem AssignedTo na struct atual
		"",  // Sem Equipment na struct atual
		now,
		now,
	)

	if err != nil {
		http.Error(w, "Failed to create order: "+err.Error(), http.StatusInternalServerError)
		return
	}

	orderID, _ := result.LastInsertId()
	w.WriteHeader(http.StatusCreated)
	fmt.Fprintf(w, `{"message": "Order created successfully", "order_id": %d}`, orderID)
}

func UpdateOrderStatus(w http.ResponseWriter, r *http.Request, db *sql.DB) {
	// Extract order ID from URL or request
	orderIDStr := r.URL.Query().Get("id")
	if orderIDStr == "" {
		http.Error(w, "Order ID is required", http.StatusBadRequest)
		return
	}

	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid order ID", http.StatusBadRequest)
		return
	}

	var statusUpdate models.StatusUpdateRequest
	err = json.NewDecoder(r.Body).Decode(&statusUpdate)
	if err != nil {
		http.Error(w, "Invalid request payload", http.StatusBadRequest)
		return
	}

	// Get user ID from context or request
	userID := r.Context().Value("userID")
	if userID == nil {
		http.Error(w, "User not authenticated", http.StatusUnauthorized)
		return
	}

	// Start a transaction
	tx, err := db.Begin()
	if err != nil {
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}
	defer tx.Rollback()

	// Update order status
	updateQuery := `
                UPDATE maintenance_orders 
                SET status = ?, updated_at = ?
                WHERE id = ?`

	_, err = tx.Exec(updateQuery, statusUpdate.Status, time.Now(), orderID)
	if err != nil {
		http.Error(w, "Failed to update order status", http.StatusInternalServerError)
		return
	}

	// If completed, update completed date and actual time
	if statusUpdate.Status == models.StatusCompleted {
		completeQuery := `
                        UPDATE maintenance_orders 
                        SET completed_date = ?, actual_time = ?
                        WHERE id = ?`

		completedDate := time.Now()
		if statusUpdate.CompletedDate != nil {
			completedDate = *statusUpdate.CompletedDate
		}

		_, err = tx.Exec(completeQuery, completedDate, statusUpdate.ActualTime, orderID)
		if err != nil {
			http.Error(w, "Failed to update completion details", http.StatusInternalServerError)
			return
		}
	}

	// Add a note if provided
	if statusUpdate.Notes != "" {
		noteQuery := `
                        INSERT INTO maintenance_notes (order_id, user_id, content, created_at)
                        VALUES (?, ?, ?, ?)`

		_, err = tx.Exec(noteQuery, orderID, userID, statusUpdate.Notes, time.Now())
		if err != nil {
			http.Error(w, "Failed to add note", http.StatusInternalServerError)
			return
		}
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		http.Error(w, "Failed to commit transaction", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"message": "Order status updated successfully"}`)
}

func AddOrderMaterial(w http.ResponseWriter, r *http.Request, db *sql.DB) {
	// Extract order ID from URL or request
	orderIDStr := r.URL.Query().Get("id")
	if orderIDStr == "" {
		http.Error(w, "Order ID is required", http.StatusBadRequest)
		return
	}

	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid order ID", http.StatusBadRequest)
		return
	}

	var materialReq models.MaterialRequest
	err = json.NewDecoder(r.Body).Decode(&materialReq)
	if err != nil {
		http.Error(w, "Invalid request payload", http.StatusBadRequest)
		return
	}

	// Get user ID from context or request
	userID := r.Context().Value("userID")
	if userID == nil {
		http.Error(w, "User not authenticated", http.StatusUnauthorized)
		return
	}
	userIDInt, ok := userID.(int64)
	if !ok {
		http.Error(w, "Invalid user ID", http.StatusInternalServerError)
		return
	}

	// Insert material
	query := `
                INSERT INTO maintenance_materials (order_id, name, quantity, unit, cost, added_by, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)`

	_, err = db.Exec(
		query,
		orderID,
		materialReq.Name,
		materialReq.Quantity,
		materialReq.Unit,
		materialReq.Cost,
		userIDInt,
		time.Now(),
	)

	if err != nil {
		http.Error(w, "Failed to add material: "+err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusCreated)
	fmt.Fprintf(w, `{"message": "Material added successfully"}`)
}
