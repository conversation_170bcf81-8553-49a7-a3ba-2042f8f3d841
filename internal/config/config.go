package config

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"github.com/joho/godotenv"
)

// Config armazena todas as configurações da aplicação
type Config struct {
	// Configurações do Servidor
	ServerPort string
	ServerEnv  string
	WorkDir    string
	ServerHost string // Adicionado para controlar o host de binding

	// Configurações do Banco de Dados
	DBHost     string
	DBPort     string
	DBUser     string
	DBPassword string
	DBName     string
	DBURL      string

	// Configurações de Template
	TemplatesDir string
	StaticDir    string

	// Configurações de Segurança
	JWTSecret     string
	JWTExpiration string
}

// LoadConfig carrega todas as configurações do ambiente
func LoadConfig() (*Config, error) {
	// Tenta carregar .env do diretório atual
	if err := godotenv.Load(); err != nil {
		log.Printf("Erro ao carregar arquivo .env: %v\n", err)
	}

	// Obtém o diretório atual como diretório de trabalho
	workDir, err := os.Getwd()
	if err != nil {
		return nil, fmt.Errorf("erro ao obter diretório de trabalho atual: %v", err)
	}
	log.Printf("Usando diretório de trabalho: %s", workDir)

	// Obter configurações do banco de dados
	dbHost := os.Getenv("DB_HOST")
	if dbHost == "" {
		return nil, fmt.Errorf("variável de ambiente DB_HOST é obrigatória")
	}

	// Verificar se estamos tentando usar um banco de dados local
	if dbHost == "localhost" || dbHost == "127.0.0.1" {
		log.Fatal("ERRO: Uso de banco de dados local é EXPRESSAMENTE PROIBIDO! Use apenas o banco de dados remoto configurado no .env")
	}

	// Validar todas as variáveis obrigatórias do banco de dados
	dbPort := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASS")
	dbName := os.Getenv("DB_NAME")

	if dbPort == "" || dbUser == "" || dbPassword == "" || dbName == "" {
		return nil, fmt.Errorf("variáveis de ambiente obrigatórias não encontradas. Configure: DB_HOST, DB_PORT, DB_USER, DB_PASS, DB_NAME")
	}

	config := &Config{
		// Configurações do Servidor
		ServerPort: getEnvOrDefault("PORT", "8080"),
		ServerEnv:  getEnvOrDefault("ENV", "development"),
		WorkDir:    workDir,
		ServerHost: getEnvOrDefault("HOST", "0.0.0.0"), // Usar 0.0.0.0 para escutar em todos os IPs (IPv4 e IPv6)

		// Configurações do Banco de Dados
		DBHost:     dbHost,
		DBPort:     dbPort,
		DBUser:     dbUser,
		DBPassword: dbPassword,
		DBName:     dbName,
		DBURL:      os.Getenv("DATABASE_URL"),

		// Configurações de Template usando caminhos absolutos
		TemplatesDir: filepath.Join(workDir, "web", "templates"),
		StaticDir:    filepath.Join(workDir, "web", "static"),

		// Configurações de Segurança
		JWTSecret:     getEnvOrDefault("JWT_SECRET", "seu_segredo_jwt_padrao"),
		JWTExpiration: getEnvOrDefault("JWT_EXPIRATION", "24h"),
	}

	// Valida configurações críticas
	if err := config.validate(); err != nil {
		return nil, err
	}

	return config, nil
}

// validate verifica se todas as configurações necessárias estão presentes
func (c *Config) validate() error {
	// Verifica se temos configurações do banco de dados
	if c.DBURL == "" {
		if c.DBUser == "" || c.DBPassword == "" || c.DBName == "" {
			return fmt.Errorf("configurações do banco de dados incompletas")
		}
		// Constrói a URL do banco se não fornecida
		c.DBURL = fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable",
			c.DBUser, c.DBPassword, c.DBHost, c.DBPort, c.DBName)
	}

	// Verifica se os diretórios necessários existem
	dirs := map[string]string{
		"templates": c.TemplatesDir,
		"static":    c.StaticDir,
	}

	for name, dir := range dirs {
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			return fmt.Errorf("diretório %s não encontrado: %s", name, dir)
		}
	}

	return nil
}

// getEnvOrDefault retorna o valor da variável de ambiente ou o valor padrão
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// IsDevelopment retorna true se o ambiente for de desenvolvimento
func (c *Config) IsDevelopment() bool {
	return strings.ToLower(c.ServerEnv) == "development"
}

// IsProduction retorna true se o ambiente for de produção
func (c *Config) IsProduction() bool {
	return strings.ToLower(c.ServerEnv) == "production"
}

// GetTemplatesPattern retorna o padrão para carregamento de templates
func (c *Config) GetTemplatesPattern() string {
	return filepath.Join(c.TemplatesDir, "**", "*.html")
}
