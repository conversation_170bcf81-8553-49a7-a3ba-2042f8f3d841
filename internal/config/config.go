package config

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"github.com/joho/godotenv"
)

// Config armazena todas as configurações da aplicação
type Config struct {
	// Configurações do Servidor
	ServerPort string
	ServerEnv  string
	WorkDir    string
	ServerHost string // Adicionado para controlar o host de binding

	// Configurações do Banco de Dados
	DBHost     string
	DBPort     string
	DBUser     string
	DBPassword string
	DBName     string
	DBURL      string

	// Configurações de Template
	TemplatesDir string
	StaticDir    string

	// Configurações de Segurança
	JWTSecret     string
	JWTExpiration string
}

// LoadConfig carrega todas as configurações do ambiente
func LoadConfig() (*Config, error) {
	// Tenta carregar .env do diretório atual
	if err := godotenv.Load(); err != nil {
		log.Printf("Erro ao carregar arquivo .env: %v\n", err)
	}

	// Obtém o diretório atual como diretório de trabalho
	workDir, err := os.Getwd()
	if err != nil {
		return nil, fmt.Errorf("erro ao obter diretório de trabalho atual: %v", err)
	}
	log.Printf("Usando diretório de trabalho: %s", workDir)

	// Obter configurações do banco de dados
	dbHost := getEnvOrDefault("DB_HOST", "postgres-ag-br1-03.conteige.cloud")

	// Verificar se estamos tentando usar um banco de dados local
	if dbHost == "localhost" || dbHost == "127.0.0.1" {
		log.Fatal("ERRO: Uso de banco de dados local é EXPRESSAMENTE PROIBIDO! Use apenas o banco de dados remoto configurado no .env")
	}

	config := &Config{
		// Configurações do Servidor
		ServerPort: getEnvOrDefault("PORT", "8080"),
		ServerEnv:  getEnvOrDefault("ENV", "development"),
		WorkDir:    workDir,
		ServerHost: getEnvOrDefault("HOST", "0.0.0.0"), // Usar 0.0.0.0 para escutar em todos os IPs (IPv4 e IPv6)

		// Configurações do Banco de Dados
		DBHost:     dbHost,
		DBPort:     getEnvOrDefault("DB_PORT", "54243"),
		DBUser:     getEnvOrDefault("DB_USER", "fcobdj_tradicao"),
		DBPassword: getEnvOrDefault("DB_PASS", "67573962"),
		DBName:     getEnvOrDefault("DB_NAME", "fcobdj_tradicao"),
		DBURL:      os.Getenv("DATABASE_URL"),

		// Configurações de Template usando caminhos absolutos
		TemplatesDir: filepath.Join(workDir, "web", "templates"),
		StaticDir:    filepath.Join(workDir, "web", "static"),

		// Configurações de Segurança
		JWTSecret:     getEnvOrDefault("JWT_SECRET", "seu_segredo_jwt_padrao"),
		JWTExpiration: getEnvOrDefault("JWT_EXPIRATION", "24h"),
	}

	// Valida configurações críticas
	if err := config.validate(); err != nil {
		return nil, err
	}

	return config, nil
}

// validate verifica se todas as configurações necessárias estão presentes
func (c *Config) validate() error {
	// Verifica se temos configurações do banco de dados
	if c.DBURL == "" {
		if c.DBUser == "" || c.DBPassword == "" || c.DBName == "" {
			return fmt.Errorf("configurações do banco de dados incompletas")
		}
		// Constrói a URL do banco se não fornecida
		c.DBURL = fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable",
			c.DBUser, c.DBPassword, c.DBHost, c.DBPort, c.DBName)
	}

	// Verifica se os diretórios necessários existem
	dirs := map[string]string{
		"templates": c.TemplatesDir,
		"static":    c.StaticDir,
	}

	for name, dir := range dirs {
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			return fmt.Errorf("diretório %s não encontrado: %s", name, dir)
		}
	}

	return nil
}

// getEnvOrDefault retorna o valor da variável de ambiente ou o valor padrão
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// IsDevelopment retorna true se o ambiente for de desenvolvimento
func (c *Config) IsDevelopment() bool {
	return strings.ToLower(c.ServerEnv) == "development"
}

// IsProduction retorna true se o ambiente for de produção
func (c *Config) IsProduction() bool {
	return strings.ToLower(c.ServerEnv) == "production"
}

// GetTemplatesPattern retorna o padrão para carregamento de templates
func (c *Config) GetTemplatesPattern() string {
	return filepath.Join(c.TemplatesDir, "**", "*.html")
}
