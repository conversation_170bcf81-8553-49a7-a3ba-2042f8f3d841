package config

import (
	"fmt"
	"os"

	"github.com/joho/godotenv"
)

// DatabaseConfig contém as configurações do banco de dados
type DatabaseConfig struct {
	Host     string
	Port     string
	User     string
	Password string
	DBName   string
	SSLMode  string
}

// GetDatabaseConfig carrega e retorna as configurações do banco de dados
func GetDatabaseConfig() DatabaseConfig {
	// Carregar variáveis de ambiente
	if err := godotenv.Load(); err != nil {
		fmt.Println("Aviso: Arquivo .env não encontrado, usando valores padrão")
	}

	config := DatabaseConfig{
		Host:     os.Getenv("DB_HOST"),
		Port:     os.Getenv("DB_PORT"),
		User:     os.Getenv("DB_USER"),
		Password: os.Getenv("DB_PASS"),
		DBName:   os.Getenv("DB_NAME"),
		SSLMode:  "disable",
	}

	// Verificar se estamos tentando usar um banco de dados local
	if config.Host == "localhost" || config.Host == "127.0.0.1" {
		fmt.Println("ERRO: Uso de banco de dados local é EXPRESSAMENTE PROIBIDO! Use apenas o banco de dados remoto configurado no .env")
		os.Exit(1)
	}

	// Verificar se as variáveis foram carregadas
	if config.User == "" || config.Password == "" || config.Host == "" || config.Port == "" || config.DBName == "" {
		fmt.Println("ERRO: Variáveis de ambiente obrigatórias não encontradas!")
		fmt.Println("Por favor, configure as seguintes variáveis no arquivo .env:")
		fmt.Println("DB_HOST, DB_PORT, DB_USER, DB_PASS, DB_NAME")
		os.Exit(1)
	}

	return config
}

func (c *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		c.Host, c.Port, c.User, c.Password, c.DBName, c.SSLMode)
}
