package handler

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
	"tradicao/internal/models"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

type mockTechnicianService struct {
	mock.Mock
}

func (m *mockTechnicianService) CreateSpecialty(specialty *models.TechnicianSpecialty) error {
	args := m.Called(specialty)
	return args.Error(0)
}

func (m *mockTechnicianService) ListSpecialties() ([]models.TechnicianSpecialty, error) {
	args := m.Called()
	return args.Get(0).([]models.TechnicianSpecialty), args.Error(1)
}

func (m *mockTechnicianService) UpdateSpecialty(specialty *models.TechnicianSpecialty) error {
	args := m.Called(specialty)
	return args.Error(0)
}

func (m *mockTechnicianService) DeleteSpecialty(id uint) error {
	args := m.Called(id)
	return args.Error(0)
}

func (m *mockTechnicianService) GetSpecialty(id uint) (*models.TechnicianSpecialty, error) {
	args := m.Called(id)
	return args.Get(0).(*models.TechnicianSpecialty), args.Error(1)
}

func (m *mockTechnicianService) CreateBranchAssociation(association *models.TechnicianBranch) error {
	args := m.Called(association)
	return args.Error(0)
}

func (m *mockTechnicianService) UpdateBranchAssociation(association *models.TechnicianBranch) error {
	args := m.Called(association)
	return args.Error(0)
}

func (m *mockTechnicianService) DeleteBranchAssociation(technicianID, branchID uint) error {
	args := m.Called(technicianID, branchID)
	return args.Error(0)
}

func (m *mockTechnicianService) GetBranchAssociation(technicianID, branchID uint) (*models.TechnicianBranch, error) {
	args := m.Called(technicianID, branchID)
	return args.Get(0).(*models.TechnicianBranch), args.Error(1)
}

func (m *mockTechnicianService) ListBranchAssociations(technicianID uint) ([]models.TechnicianBranch, error) {
	args := m.Called(technicianID)
	return args.Get(0).([]models.TechnicianBranch), args.Error(1)
}

func (m *mockTechnicianService) CreateMaintenanceHistory(history *models.MaintenanceHistory) error {
	args := m.Called(history)
	return args.Error(0)
}

func (m *mockTechnicianService) ListMaintenanceHistory(technicianID uint, startDate, endDate time.Time) ([]models.MaintenanceHistory, error) {
	args := m.Called(technicianID, startDate, endDate)
	return args.Get(0).([]models.MaintenanceHistory), args.Error(1)
}

func (m *mockTechnicianService) GetMaintenanceHistory(id uint) (*models.MaintenanceHistory, error) {
	args := m.Called(id)
	return args.Get(0).(*models.MaintenanceHistory), args.Error(1)
}

func setupRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	router := gin.Default()
	return router
}

func TestTechnicianHandler(t *testing.T) {
	mockService := new(mockTechnicianService)
	handler := NewTechnicianHandler(mockService)
	router := setupRouter()

	// Rotas para especialidades
	router.POST("/specialties", handler.CreateSpecialty)
	router.GET("/specialties", handler.ListSpecialties)
	router.PUT("/specialties/:id", handler.UpdateSpecialty)
	router.DELETE("/specialties/:id", handler.DeleteSpecialty)
	router.GET("/specialties/:id", handler.GetSpecialty)

	// Rotas para associações
	router.POST("/branch-associations", handler.CreateBranchAssociation)
	router.PUT("/branch-associations", handler.UpdateBranchAssociation)
	router.DELETE("/branch-associations/:technician_id/:branch_id", handler.DeleteBranchAssociation)
	router.GET("/branch-associations/:technician_id/:branch_id", handler.GetBranchAssociation)
	router.GET("/technicians/:technician_id/branch-associations", handler.ListBranchAssociations)

	// Rotas para histórico de manutenções
	router.POST("/maintenance-history", handler.CreateMaintenanceHistory)
	router.GET("/technicians/:technician_id/maintenance-history", handler.ListMaintenanceHistory)
	router.GET("/maintenance-history/:id", handler.GetMaintenanceHistory)

	t.Run("Test CreateSpecialty", func(t *testing.T) {
		specialty := models.TechnicianSpecialty{
			Name:        "Eletricista",
			Description: "Especialista em manutenção elétrica",
		}

		mockService.On("CreateSpecialty", &specialty).Return(nil)

		body, _ := json.Marshal(specialty)
		req, _ := http.NewRequest("POST", "/specialties", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)
		mockService.AssertExpectations(t)
	})

	t.Run("Test ListSpecialties", func(t *testing.T) {
		specialties := []models.TechnicianSpecialty{
			{
				ID:          1,
				Name:        "Eletricista",
				Description: "Especialista em manutenção elétrica",
			},
		}

		mockService.On("ListSpecialties").Return(specialties, nil)

		req, _ := http.NewRequest("GET", "/specialties", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		mockService.AssertExpectations(t)
	})

	t.Run("Test CreateBranchAssociation", func(t *testing.T) {
		association := models.TechnicianBranch{
			TechnicianID: 1,
			BranchID:     1,
			SpecialtyID:  1,
		}

		mockService.On("CreateBranchAssociation", &association).Return(nil)

		body, _ := json.Marshal(association)
		req, _ := http.NewRequest("POST", "/branch-associations", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)
		mockService.AssertExpectations(t)
	})

	t.Run("Test ListMaintenanceHistory", func(t *testing.T) {
		history := []models.MaintenanceHistory{
			{
				ID:           1,
				TechnicianID: 1,
				OrderID:      1,
				EquipmentID:  1,
				Description:  "Manutenção preventiva",
				Status:       "Concluído",
			},
		}

		startDate := time.Now().AddDate(0, -1, 0)
		endDate := time.Now()

		mockService.On("ListMaintenanceHistory", uint(1), startDate, endDate).Return(history, nil)

		req, _ := http.NewRequest("GET", "/technicians/1/maintenance-history?start_date="+startDate.Format("2006-01-02")+"&end_date="+endDate.Format("2006-01-02"), nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		mockService.AssertExpectations(t)
	})
}
