package handler

import (
	"net/http"
	"strconv"
	"time"
	"tradicao/internal/models"
	"tradicao/internal/service"

	"github.com/gin-gonic/gin"
)

// TechnicianHandler define o handler para operações relacionadas a técnicos
type TechnicianHandler struct {
	service service.TechnicianService
}

// NewTechnicianHandler cria uma nova instância do handler
func NewTechnicianHandler(service service.TechnicianService) *TechnicianHandler {
	return &TechnicianHandler{service: service}
}

// CreateSpecialty cria uma nova especialidade
func (h *TechnicianHandler) CreateSpecialty(c *gin.Context) {
	var specialty models.TechnicianSpecialty
	if err := c.<PERSON>(&specialty); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.service.CreateSpecialty(&specialty); err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": err.<PERSON>rror()})
		return
	}

	c.<PERSON>(http.StatusCreated, specialty)
}

// ListSpecialties lista todas as especialidades
func (h *TechnicianHandler) ListSpecialties(c *gin.Context) {
	specialties, err := h.service.ListSpecialties()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, specialties)
}

// UpdateSpecialty atualiza uma especialidade
func (h *TechnicianHandler) UpdateSpecialty(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var specialty models.TechnicianSpecialty
	if err := c.ShouldBindJSON(&specialty); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	specialty.ID = uint(id)
	if err := h.service.UpdateSpecialty(&specialty); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, specialty)
}

// DeleteSpecialty remove uma especialidade
func (h *TechnicianHandler) DeleteSpecialty(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	if err := h.service.DeleteSpecialty(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetSpecialty obtém uma especialidade pelo ID
func (h *TechnicianHandler) GetSpecialty(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	specialty, err := h.service.GetSpecialty(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, specialty)
}

// CreateBranchAssociation cria uma nova associação entre técnico e filial
func (h *TechnicianHandler) CreateBranchAssociation(c *gin.Context) {
	var association models.TechnicianBranch
	if err := c.ShouldBindJSON(&association); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.service.CreateBranchAssociation(&association); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, association)
}

// UpdateBranchAssociation atualiza uma associação entre técnico e filial
func (h *TechnicianHandler) UpdateBranchAssociation(c *gin.Context) {
	var association models.TechnicianBranch
	if err := c.ShouldBindJSON(&association); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.service.UpdateBranchAssociation(&association); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, association)
}

// DeleteBranchAssociation remove uma associação entre técnico e filial
func (h *TechnicianHandler) DeleteBranchAssociation(c *gin.Context) {
	technicianID, err := strconv.ParseUint(c.Param("technician_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do técnico inválido"})
		return
	}

	branchID, err := strconv.ParseUint(c.Param("branch_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}

	if err := h.service.DeleteBranchAssociation(uint(technicianID), uint(branchID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetBranchAssociation obtém uma associação entre técnico e filial
func (h *TechnicianHandler) GetBranchAssociation(c *gin.Context) {
	technicianID, err := strconv.ParseUint(c.Param("technician_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do técnico inválido"})
		return
	}

	branchID, err := strconv.ParseUint(c.Param("branch_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}

	association, err := h.service.GetBranchAssociation(uint(technicianID), uint(branchID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, association)
}

// ListBranchAssociations lista todas as associações de um técnico
func (h *TechnicianHandler) ListBranchAssociations(c *gin.Context) {
	technicianID, err := strconv.ParseUint(c.Param("technician_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do técnico inválido"})
		return
	}

	associations, err := h.service.ListBranchAssociations(uint(technicianID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, associations)
}

// CreateMaintenanceHistory cria um novo histórico de manutenção
func (h *TechnicianHandler) CreateMaintenanceHistory(c *gin.Context) {
	var history models.MaintenanceHistory
	if err := c.ShouldBindJSON(&history); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.service.CreateMaintenanceHistory(&history); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, history)
}

// ListMaintenanceHistory lista o histórico de manutenções de um técnico
func (h *TechnicianHandler) ListMaintenanceHistory(c *gin.Context) {
	technicianID, err := strconv.ParseUint(c.Param("technician_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do técnico inválido"})
		return
	}

	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Data inicial inválida"})
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Data final inválida"})
		return
	}

	history, err := h.service.ListMaintenanceHistory(uint(technicianID), startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, history)
}

// GetMaintenanceHistory obtém um histórico de manutenção pelo ID
func (h *TechnicianHandler) GetMaintenanceHistory(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	history, err := h.service.GetMaintenanceHistory(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, history)
}
