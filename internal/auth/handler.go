package auth

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

type LoginResponse struct {
	Token string `json:"token"`
	User  struct {
		ID       int    `json:"id"`
		Username string `json:"username"`
		Role     string `json:"role"`
	} `json:"user"`
}

// LoginHandler lida com a autenticação do usuário
func (s *AuthService) LoginHandler(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "dados inválidos"})
		return
	}

	// TODO: Implementar verificação no banco de dados
	// Por enquanto, vamos usar um usuário mock
	if req.Username != "admin" || req.Password != "admin123" {
		c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "credenciais inválidas"})
		return
	}

	token, err := s.<PERSON>(1, "admin", "admin")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "erro ao gerar token"})
		return
	}

	response := LoginResponse{
		Token: token,
		User: struct {
			ID       int    `json:"id"`
			Username string `json:"username"`
			Role     string `json:"role"`
		}{
			ID:       1,
			Username: "admin",
			Role:     "admin",
		},
	}

	c.JSON(http.StatusOK, response)
}

// LogoutHandler lida com o logout do usuário
func (s *AuthService) LogoutHandler(c *gin.Context) {
	// Em um sistema JWT, o logout é feito no cliente
	// Aqui podemos apenas retornar sucesso
	c.JSON(http.StatusOK, gin.H{"message": "logout realizado com sucesso"})
}

// ProfileHandler retorna as informações do perfil do usuário
func (s *AuthService) ProfileHandler(c *gin.Context) {
	userID, _ := c.Get("user_id")
	username, _ := c.Get("username")
	role, _ := c.Get("role")

	c.JSON(http.StatusOK, gin.H{
		"id":       userID,
		"username": username,
		"role":     role,
	})
}
