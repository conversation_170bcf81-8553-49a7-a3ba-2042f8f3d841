package utils

import (
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
)

// LoadHTMLFile carrega um arquivo HTML e retorna seu conteúdo como string
func LoadHTMLFile(templatePath string) (string, error) {
	// Obtém o diretório de trabalho atual
	workDir, err := os.Getwd()
	if err != nil {
		log.Printf("Erro ao obter diretório de trabalho: %v", err)
		return "", err
	}

	// Constrói o caminho completo para o arquivo
	fullPath := filepath.Join(workDir, templatePath)

	// Lê o conteúdo do arquivo
	content, err := ioutil.ReadFile(fullPath)
	if err != nil {
		log.Printf("Erro ao ler arquivo HTML %s: %v", fullPath, err)
		return "", err
	}

	return string(content), nil
}
