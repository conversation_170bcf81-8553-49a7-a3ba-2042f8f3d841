package users

import (
	"database/sql"

	"tradicao/internal/auth"

	"github.com/gin-gonic/gin"
)

// Users é a interface principal do pacote de usuários
type Users interface {
	Create(req *CreateUserRequest) (*User, error)
	GetByID(id int) (*User, error)
	Update(id int, req *UpdateUserRequest) (*User, error)
	ChangePassword(id int, req *ChangePasswordRequest) error
	List() ([]*User, error)
	UpdateLastAccess(id int) error
	RegisterRoutes(r *gin.RouterGroup)
}

// Adapter é um adaptador que implementa a interface Users
type Adapter struct {
	handler *Handler
	service *Service
}

// New cria uma nova instância do serviço de usuários
func New(db *sql.DB, auth auth.Auth) Users {
	repo := NewRepository(db)
	service := NewService(repo, auth)
	handler := NewHandler(service, auth)
	return &Adapter{
		handler: handler,
		service: service,
	}
}

// Create cria um novo usuário
func (a *Adapter) Create(req *CreateUserRequest) (*User, error) {
	return a.service.Create(req)
}

// GetByID retorna um usuário pelo ID
func (a *Adapter) GetByID(id int) (*User, error) {
	return a.service.GetByID(id)
}

// Update atualiza um usuário
func (a *Adapter) Update(id int, req *UpdateUserRequest) (*User, error) {
	return a.service.Update(id, req)
}

// ChangePassword altera a senha de um usuário
func (a *Adapter) ChangePassword(id int, req *ChangePasswordRequest) error {
	return a.service.ChangePassword(id, req)
}

// List retorna todos os usuários
func (a *Adapter) List() ([]*User, error) {
	return a.service.List()
}

// UpdateLastAccess atualiza o último acesso de um usuário
func (a *Adapter) UpdateLastAccess(id int) error {
	return a.service.UpdateLastAccess(id)
}

// RegisterRoutes registra as rotas de usuários
func (a *Adapter) RegisterRoutes(r *gin.RouterGroup) {
	a.handler.RegisterRoutes(r)
}
