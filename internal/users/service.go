package users

import (
	"errors"
	"time"

	"tradicao/internal/auth"
)

type Service struct {
	repo *Repository
	auth auth.Auth
}

func NewService(repo *Repository, auth auth.Auth) *Service {
	return &Service{
		repo: repo,
		auth: auth,
	}
}

func (s *Service) Create(req *CreateUserRequest) (*User, error) {
	// Verifica se o usuário já existe
	_, err := s.repo.GetByUsername(req.Username)
	if err == nil {
		return nil, errors.New("usuário já existe")
	}

	// Gera o hash da senha
	hashedPassword, err := s.auth.HashPassword(req.Password)
	if err != nil {
		return nil, err
	}

	user := &User{
		Username:     req.Username,
		Password:     hashedPassword,
		Email:        req.Email,
		Role:         req.Role,
		Nome:         req.Nome,
		Ativo:        true,
		CriadoEm:     time.Now(),
		AtualizadoEm: time.Now(),
		BranchID:     req.<PERSON>,
	}

	err = s.repo.Create(user)
	if err != nil {
		return nil, err
	}

	// Remove a senha do retorno
	user.Password = ""

	return user, nil
}

func (s *Service) GetByID(id int) (*User, error) {
	user, err := s.repo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// Remove a senha do retorno
	user.Password = ""

	return user, nil
}

func (s *Service) Update(id int, req *UpdateUserRequest) (*User, error) {
	user, err := s.repo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// Atualiza apenas os campos fornecidos
	if req.Username != "" {
		user.Username = req.Username
	}
	if req.Email != "" {
		user.Email = req.Email
	}
	if req.Role != "" {
		user.Role = req.Role
	}
	if req.Nome != "" {
		user.Nome = req.Nome
	}
	if req.Ativo != nil {
		user.Ativo = *req.Ativo
	}
	if req.BranchID != 0 {
		user.BranchID = req.BranchID
	}

	err = s.repo.Update(user)
	if err != nil {
		return nil, err
	}

	// Remove a senha do retorno
	user.Password = ""

	return user, nil
}

func (s *Service) ChangePassword(id int, req *ChangePasswordRequest) error {
	user, err := s.repo.GetByID(id)
	if err != nil {
		return err
	}

	// Verifica a senha atual
	if !s.auth.CheckPassword(req.CurrentPassword, user.Password) {
		return errors.New("senha atual incorreta")
	}

	// Gera o hash da nova senha
	hashedPassword, err := s.auth.HashPassword(req.NewPassword)
	if err != nil {
		return err
	}

	return s.repo.UpdatePassword(id, hashedPassword)
}

func (s *Service) List() ([]*User, error) {
	users, err := s.repo.List()
	if err != nil {
		return nil, err
	}

	// Remove as senhas dos retornos
	for _, user := range users {
		user.Password = ""
	}

	return users, nil
}

func (s *Service) UpdateLastAccess(id int) error {
	return s.repo.UpdateLastAccess(id)
}
