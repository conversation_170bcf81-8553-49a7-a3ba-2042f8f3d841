package users

import (
	"tradicao/internal/models"

	"github.com/gin-gonic/gin"
)

func (h *Handler) RegisterRoutes(r *gin.RouterGroup) {
	users := r.Group("/users")
	{
		users.POST("", h.auth.AuthMiddleware(), h.auth.RoleMiddleware(string(models.RoleAdmin)), h.Create)
		users.GET("", h.auth.AuthMiddleware(), h.List)
		users.GET("/:id", h.auth.AuthMiddleware(), h.GetByID)
		users.PUT("/:id", h.auth.AuthMiddleware(), h.Update)
		users.PUT("/:id/password", h.auth.AuthMiddleware(), h.ChangePassword)
	}
}
