package users

import (
	"time"
)

type User struct {
	ID           int       `json:"id"`
	Username     string    `json:"username"`
	Password     string    `json:"-"`
	Email        string    `json:"email"`
	Role         string    `json:"role"`
	Nome         string    `json:"nome"`
	Ativo        bool      `json:"ativo"`
	CriadoEm     time.Time `json:"criado_em"`
	AtualizadoEm time.Time `json:"atualizado_em"`
	UltimoAcesso time.Time `json:"ultimo_acesso"`
	BranchID     int       `json:"branch_id,omitempty"`
}

type CreateUserRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
	Role     string `json:"role" binding:"required"`
	Nome     string `json:"nome" binding:"required"`
	BranchID int    `json:"branch_id,omitempty"`
}

type UpdateUserRequest struct {
	Username string `json:"username,omitempty"`
	Email    string `json:"email,omitempty"`
	Role     string `json:"role,omitempty"`
	Nome     string `json:"nome,omitempty"`
	Ativo    *bool  `json:"ativo,omitempty"`
	BranchID int    `json:"branch_id,omitempty"`
}

type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required"`
}
