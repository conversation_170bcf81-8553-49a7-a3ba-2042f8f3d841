package users

import (
	"database/sql"
	"errors"
	"time"

	"github.com/lib/pq"
)

type Repository struct {
	db *sql.DB
}

func NewRepository(db *sql.DB) *Repository {
	return &Repository{db: db}
}

func (r *Repository) Create(user *User) error {
	query := `
		INSERT INTO usuarios (
			username, password, email, role, nome, branch_id,
			ativo, criado_em, atualizado_em
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		RETURNING id
	`

	err := r.db.QueryRow(
		query,
		user.Username,
		user.Password,
		user.Email,
		user.Role,
		user.Nome,
		user.BranchID,
		user.Ativo,
		user.CriadoEm,
		user.AtualizadoEm,
	).Scan(&user.ID)

	if err != nil {
		if pqErr, ok := err.(*pq.Error); ok {
			if pqErr.Code == "23505" { // unique_violation
				return errors.New("usuário já existe")
			}
		}
		return err
	}

	return nil
}

func (r *Repository) GetByID(id int) (*User, error) {
	query := `
		SELECT id, username, password, email, role, nome, ativo,
			criado_em, atualizado_em, ultimo_acesso, branch_id
		FROM usuarios
		WHERE id = $1
	`

	user := &User{}
	err := r.db.QueryRow(query, id).Scan(
		&user.ID,
		&user.Username,
		&user.Password,
		&user.Email,
		&user.Role,
		&user.Nome,
		&user.Ativo,
		&user.CriadoEm,
		&user.AtualizadoEm,
		&user.UltimoAcesso,
		&user.BranchID,
	)

	if err == sql.ErrNoRows {
		return nil, errors.New("usuário não encontrado")
	}

	if err != nil {
		return nil, err
	}

	return user, nil
}

func (r *Repository) GetByUsername(username string) (*User, error) {
	query := `
		SELECT id, username, password, email, role, nome, ativo,
			criado_em, atualizado_em, ultimo_acesso, branch_id
		FROM usuarios
		WHERE username = $1
	`

	user := &User{}
	err := r.db.QueryRow(query, username).Scan(
		&user.ID,
		&user.Username,
		&user.Password,
		&user.Email,
		&user.Role,
		&user.Nome,
		&user.Ativo,
		&user.CriadoEm,
		&user.AtualizadoEm,
		&user.UltimoAcesso,
		&user.BranchID,
	)

	if err == sql.ErrNoRows {
		return nil, errors.New("usuário não encontrado")
	}

	if err != nil {
		return nil, err
	}

	return user, nil
}

func (r *Repository) Update(user *User) error {
	query := `
		UPDATE usuarios
		SET username = $1, email = $2, role = $3, nome = $4,
			ativo = $5, atualizado_em = $6, branch_id = $7
		WHERE id = $8
	`

	result, err := r.db.Exec(
		query,
		user.Username,
		user.Email,
		user.Role,
		user.Nome,
		user.Ativo,
		time.Now(),
		user.BranchID,
		user.ID,
	)

	if err != nil {
		return err
	}

	rows, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rows == 0 {
		return errors.New("usuário não encontrado")
	}

	return nil
}

func (r *Repository) UpdatePassword(id int, password string) error {
	query := `
		UPDATE usuarios
		SET password = $1, atualizado_em = $2
		WHERE id = $3
	`

	result, err := r.db.Exec(query, password, time.Now(), id)
	if err != nil {
		return err
	}

	rows, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rows == 0 {
		return errors.New("usuário não encontrado")
	}

	return nil
}

func (r *Repository) UpdateLastAccess(id int) error {
	query := `
		UPDATE usuarios
		SET ultimo_acesso = $1
		WHERE id = $2
	`

	_, err := r.db.Exec(query, time.Now(), id)
	return err
}

func (r *Repository) List() ([]*User, error) {
	query := `
		SELECT id, username, email, role, nome, ativo,
			criado_em, atualizado_em, ultimo_acesso, branch_id
		FROM usuarios
		ORDER BY nome
	`

	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var users []*User
	for rows.Next() {
		user := &User{}
		err := rows.Scan(
			&user.ID,
			&user.Username,
			&user.Email,
			&user.Role,
			&user.Nome,
			&user.Ativo,
			&user.CriadoEm,
			&user.AtualizadoEm,
			&user.UltimoAcesso,
			&user.BranchID,
		)
		if err != nil {
			return nil, err
		}
		users = append(users, user)
	}

	return users, nil
}
