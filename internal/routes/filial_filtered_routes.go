package routes

import (
	"tradicao/internal/db"
	"tradicao/internal/handlers"
	"tradicao/internal/middleware"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// SetupFilialFilteredRoutes configura as rotas para filiais com filtro de permissões
func SetupFilialFilteredRoutes(router *gin.Engine, branchService *services.BranchService) {
	// Inicializar o handler da galeria
	handlers.InitGaleriaHandler()

	// Obter o banco de dados
	database := db.GetDB()

	// Criar o handler filtrado
	filteredHandler := handlers.NewFilialFilteredAPIHandler(branchService, database)

	// Grupo de rotas para filiais
	filiais := router.Group("/api/filiais")
	filiais.Use(middleware.AuthMiddleware())

	// Rotas públicas (apenas autenticação)
	filiais.GET("", filteredHandler.GetAllFiliais)
	filiais.GET("/:id", filteredHandler.GetFilialByID)
	filiais.GET("/:id/equipamentos", filteredHandler.GetEquipmentsByFilial)

	// Configurar a rota da página de galeria
	galeria := router.Group("/galeria")
	galeria.Use(middleware.AuthMiddleware(), middleware.PageAccessMiddleware())
	galeria.GET("", handlers.GaleriaHandler)
}
