package routes

import (
	"tradicao/internal/controllers"
	"tradicao/internal/middleware"

	"github.com/gin-gonic/gin"
)

// SetupNotificationRoutes configura as rotas para notificações
func SetupNotificationRoutes(router *gin.Engine, authMiddleware gin.HandlerFunc) {
	// Criar controlador de notificações
	notificationController := controllers.NewNotificationController()

	// Grupo de rotas para a API de notificações
	apiNotifications := router.Group("/api/notifications")
	apiNotifications.Use(authMiddleware) // Middleware de autenticação

	// Rotas para notificações
	apiNotifications.GET("", notificationController.GetNotifications)
	apiNotifications.PUT("/:id/read", notificationController.MarkAsRead)
	apiNotifications.PUT("/read-all", notificationController.MarkAllAsRead)
	apiNotifications.DELETE("/:id", notificationController.DeleteNotification)

	// Rota para a página de notificações
	router.GET("/notifications", middleware.AuthMiddleware(), func(c *gin.Context) {
		c.HTML(200, "notifications/list.html", gin.H{
			"title": "Notificações - Rede Tradição",
			"page":  "notifications",
		})
	})
}
