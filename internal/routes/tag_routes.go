package routes

import (
	"tradicao/internal/handlers"

	"github.com/gin-gonic/gin"
)

func SetupTagRoutes(router *gin.Engine, tagHandler *handlers.TagHandler, authMiddleware gin.HandlerFunc) {
	// Grupo de rotas para tags
	tags := router.Group("/tags")
	tags.Use(authMiddleware)
	{
		// Página principal de tags
		tags.GET("", tagHandler.RenderTagPage)

		// API de categorias
		tags.POST("/categories", tagHandler.CreateCategory)
		tags.PUT("/categories/:id", tagHandler.UpdateCategory)
		tags.DELETE("/categories/:id", tagHandler.DeleteCategory)

		// API de tags
		tags.GET("/api", tagHandler.ListTags)
		tags.POST("/api", tagHandler.CreateTag)
		tags.GET("/api/:id", tagHandler.GetTag)
		tags.PUT("/api/:id", tagHandler.UpdateTag)
		tags.DELETE("/api/:id", tagHandler.DeleteTag)
	}
}
