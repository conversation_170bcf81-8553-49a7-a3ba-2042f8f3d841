package routes

import (
	"net/http"

	"tradicao/internal/handlers"
	"tradicao/internal/middleware"

	"github.com/gin-gonic/gin"
)

// SetupAuthRoutes registra todas as rotas relacionadas à autenticação
func SetupAuthRoutes(router *gin.Engine, authHandler *handlers.AuthHandler) {
	// Rotas públicas
	authRoutes := router.Group("/api/auth")
	{
		// Login e autenticação básica
		authRoutes.POST("/login", authHandler.Login)
		authRoutes.POST("/logout", authHandler.Logout)

		// Rota pública para QR codes (não requer autenticação pois a URL contém um identificador único)
		authRoutes.GET("/security/qrcode/:filename", authHandler.ServeQRCode)

		// Rotas protegidas - requerem autenticação
		protected := authRoutes.Group("")
		protected.Use(middleware.AuthMiddleware())
		{
			// Informações do usuário
			protected.GET("/me", authHandler.GetCurrentUser)

			// Configuração 2FA
			protected.GET("/2fa/setup", authHandler.SetupTOTP)
			protected.POST("/2fa/enable", authHandler.VerifyAndEnableTOTP)
			protected.POST("/2fa/disable", authHandler.DisableTOTP)
		}
	}

	// Rota para página de perfil com opções de segurança (frontend)
	profile := router.Group("/profile")
	profile.Use(middleware.AuthMiddleware())
	{
		profile.GET("", func(c *gin.Context) {
			c.HTML(http.StatusOK, "profile.html", gin.H{
				"title": "Meu Perfil - Rede Tradição Shell",
				"page":  "profile",
			})
		})

		profile.GET("/security", func(c *gin.Context) {
			c.HTML(http.StatusOK, "security_settings.html", gin.H{
				"title": "Configurações de Segurança - Rede Tradição Shell",
				"page":  "security",
			})
		})
	}
}
