package routes

import (
	"github.com/gin-gonic/gin"
	"tradicao/internal/handlers"
)

func RegisterUserRoutes(r *gin.Engine) {
	userGroup := r.Group("/users")
	{
		userGroup.POST("/", handlers.CreateUser)
		userGroup.GET("/:id", handlers.GetUser)
		userGroup.PUT("/:id", handlers.UpdateUser)
		userGroup.DELETE("/:id", handlers.DeleteUser)
		userGroup.GET("/", handlers.ListUsers)
		userGroup.POST("/:id/avatar", handlers.UploadAvatar)
	}
}
