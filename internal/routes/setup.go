package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// SetupRoutes configura todas as rotas da aplicação
func SetupRoutes(router *gin.Engine) *gin.Engine {
	// Rota principal
	router.GET("/", func(c *gin.Context) {
		c.HTML(http.StatusOK, "index.html", gin.H{
			"title": "Rede Tradição - Sistema de Manutenção",
		})
	})

	// Rota de login
	router.GET("/login", func(c *gin.Context) {
		c.HTML(http.StatusOK, "login/login.html", gin.H{
			"title": "Login - Rede Tradição",
		})
	})

	// Rota 404
	router.NoRoute(func(c *gin.Context) {
		c.HTML(http.StatusNotFound, "404.html", gin.H{
			"title": "Página não encontrada",
		})
	})

	return router
}
