package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// SetupModeloDashRoutes configura as rotas para a página modelodash
func SetupModeloDashRoutes(router *gin.Engine) {
	router.GET("/modelodash", handleModeloDash)
}

// handleModeloDash gerencia a renderização da página modelodash
func handleModeloDash(c *gin.Context) {
	// Obtém o usuário autenticado se existir
	user, exists := c.Get("user")

	// Prepara os dados para o template
	data := gin.H{
		"title":      "Modelo de Calendário",
		"ActivePage": "modelodash",
	}

	// Adiciona o usuário aos dados se estiver autenticado
	if exists {
		data["User"] = user
	}

	// Renderiza o template
	c.HTML(http.StatusOK, "modelodash.html", data)
}
