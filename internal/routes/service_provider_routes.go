package routes

import (
	"fmt"
	"tradicao/internal/handlers"
	"tradicao/internal/middleware"

	"github.com/gin-gonic/gin"
)

// SetupServiceProviderRoutes configura as rotas para prestadores
func SetupServiceProviderRoutes(router *gin.Engine, providerHandler *handlers.ServiceProviderHandler, authMiddleware gin.HandlerFunc) {
	// Grupo de rotas para prestadores
	providers := router.Group("/api/providers")
	providers.Use(authMiddleware)

	// Rotas básicas
	providers.GET("", providerHandler.ListProviders)                  // Listar todas as prestadoras
	providers.GET("/by-branch", providerHandler.GetProvidersByBranch) // Filtrar por filial (query param branch_id)
	providers.POST("", providerHandler.CreateProvider)
	providers.GET("/:id", providerHandler.GetProviderByID)
	providers.HEAD("/:id", providerHandler.CheckProviderExists) // Verificar se uma prestadora existe
	providers.PUT("/:id", providerHandler.UpdateProvider)
	providers.DELETE("/:id", providerHandler.DeleteProvider)

	// Rotas para técnicos
	technicians := providers.Group("/:id/technicians")
	{
		technicians.GET("", providerHandler.GetProviderTechnicians)
		technicians.POST("", providerHandler.AddTechnician)
		technicians.DELETE("/:technicianId", providerHandler.RemoveTechnician)
	}

	// Rota para upload de logomarca
	providers.POST("/:id/logo", providerHandler.UploadProviderLogo)

	// Rotas para o prestador atual
	myProvider := router.Group("/api/my-provider")
	myProvider.Use(authMiddleware)
	myProvider.Use(middleware.ProviderMiddleware())
	{
		myProvider.GET("", func(c *gin.Context) {
			providerID := c.GetUint("providerID")
			c.Params = append(c.Params, gin.Param{Key: "id", Value: fmt.Sprintf("%d", providerID)})
			providerHandler.GetProviderByID(c)
		})

		myProvider.PUT("", func(c *gin.Context) {
			providerID := c.GetUint("providerID")
			c.Params = append(c.Params, gin.Param{Key: "id", Value: fmt.Sprintf("%d", providerID)})
			providerHandler.UpdateProvider(c)
		})

		myProvider.GET("/technicians", func(c *gin.Context) {
			providerID := c.GetUint("providerID")
			c.Params = append(c.Params, gin.Param{Key: "id", Value: fmt.Sprintf("%d", providerID)})
			providerHandler.GetProviderTechnicians(c)
		})

		myProvider.POST("/technicians", func(c *gin.Context) {
			providerID := c.GetUint("providerID")
			c.Params = append(c.Params, gin.Param{Key: "id", Value: fmt.Sprintf("%d", providerID)})
			providerHandler.AddTechnician(c)
		})

		myProvider.DELETE("/technicians/:technicianId", func(c *gin.Context) {
			providerID := c.GetUint("providerID")
			c.Params = append(c.Params, gin.Param{Key: "id", Value: fmt.Sprintf("%d", providerID)})
			providerHandler.RemoveTechnician(c)
		})

		myProvider.POST("/logo", func(c *gin.Context) {
			providerID := c.GetUint("providerID")
			c.Params = append(c.Params, gin.Param{Key: "id", Value: fmt.Sprintf("%d", providerID)})
			providerHandler.UploadProviderLogo(c)
		})
	}
}
