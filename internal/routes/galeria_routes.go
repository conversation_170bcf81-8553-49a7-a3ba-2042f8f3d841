package routes

import (
	"tradicao/internal/handlers"
	"tradicao/internal/middleware"

	"github.com/gin-gonic/gin"
)

// SetupGaleriaRoutes configura as rotas para a galeria de equipamentos
// Deprecated: Use SetupFilialFilteredRoutes em vez disso
func SetupGaleriaRoutes(router *gin.Engine) {
	// Esta função está obsoleta e será removida em versões futuras
	// Use SetupFilialFilteredRoutes em vez disso

	// Grupo de rotas para a galeria (protegidas por autenticação e permissões)
	galeria := router.Group("/galeria")
	galeria.Use(middleware.AuthMiddleware(), middleware.PageAccessMiddleware())
	{
		// Página principal da galeria
		galeria.GET("", handlers.GaleriaHandler)
	}

	// API para equipamentos (já deve existir, mas adicionamos aqui para referência)
	// Estas rotas seriam usadas pelo JavaScript da galeria para obter dados
	apiEquipamentos := router.Group("/api/equipments")
	apiEquipamentos.Use(middleware.AuthMiddleware())
	{
		// Estas rotas são implementadas pelo handler unificado
		// Obtenha o handler unificado usando setup.GetUnifiedHandler()
		// apiEquipamentos.GET("", unifiedHandler.GetAllEquipment)
		// apiEquipamentos.GET("/branch/:id", unifiedHandler.GetEquipmentsByBranch) // Deprecated: Use /filial/:id em vez disso
		// apiEquipamentos.GET("/filial/:id", unifiedHandler.GetEquipmentsByFilial)
		// apiEquipamentos.GET("/:id", unifiedHandler.GetEquipment)
	}
}
