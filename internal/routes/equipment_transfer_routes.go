package routes

import (
	"tradicao/internal/handlers"
	"tradicao/internal/middleware"
	"tradicao/internal/models"

	"github.com/gin-gonic/gin"
)

// SetupEquipmentTransferRoutes configura as rotas para transferências de equipamentos
func SetupEquipmentTransferRoutes(router *gin.Engine, transferHandler *handlers.EquipmentTransferHandler) {
	// Grupo de rotas para transferências de equipamentos
	transfers := router.Group("/api/equipment-transfers")
	transfers.Use(middleware.AuthMiddleware())

	// Rotas para filiais (branch_user)
	filialRoutes := transfers.Group("")
	filialRoutes.Use(middleware.RoleMiddleware(models.RoleFilial, models.RoleBranchUser))

	// Solicitar transferência
	filialRoutes.POST("", transferHandler.RequestTransfer)
	
	// Obter transferência por ID
	filialRoutes.GET("/:id", transferHandler.GetTransferByID)
	
	// Obter transferências de um equipamento
	filialRoutes.GET("/equipment/:equipmentId", transferHandler.GetTransfersByEquipment)
	
	// Obter transferências pendentes de/para uma filial
	filialRoutes.GET("/branch/:branchId", transferHandler.GetPendingTransfersByBranch)
	
	// Aprovar transferência
	filialRoutes.POST("/:id/approve", transferHandler.ApproveTransfer)
	
	// Rejeitar transferência
	filialRoutes.POST("/:id/reject", transferHandler.RejectTransfer)
	
	// Cancelar transferência
	filialRoutes.POST("/:id/cancel", transferHandler.CancelTransfer)

	// Rotas administrativas
	adminRoutes := transfers.Group("")
	adminRoutes.Use(middleware.RoleMiddleware(models.RoleAdmin, models.RoleGerente))
	
	// Listar todas as transferências
	adminRoutes.GET("", transferHandler.ListAllTransfers)
}
