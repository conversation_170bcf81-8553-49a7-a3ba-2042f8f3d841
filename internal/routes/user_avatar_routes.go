package routes

import (
	"tradicao/internal/handlers"

	"github.com/gin-gonic/gin"
)

// SetupUserAvatarRoutes configura as rotas para upload de avatar
func SetupUserAvatarRoutes(router *gin.Engine, avatarHandler *handlers.UserAvatarHandler, authMiddleware gin.HandlerFunc) {
	// Grupo de rotas para usuários
	users := router.Group("/api/users")
	users.Use(authMiddleware)
	
	// Rota para upload de avatar
	users.POST("/:id/avatar", avatarHandler.UploadUserAvatar)
	
	// Rota específica para o usuário atual
	api := router.Group("/api")
	api.Use(authMiddleware)
	api.POST("/user/avatar", avatarHandler.UploadCurrentUserAvatar)
}
