package routes

import (
	"tradicao/internal/middleware"

	"github.com/gin-gonic/gin"
)

func SetupRouter() *gin.Engine {
	r := gin.Default()

	// Configurar templates
	r.LoadHTMLGlob("web/templates/**/*")

	// Configurar arquivos estáticos
	r.<PERSON>("/static", "web/static")

	// Middleware de autenticação
	authMiddleware := middleware.AuthMiddleware()

	// Configurar rotas
	SetupNotificationRoutes(r, authMiddleware)
	SetupProviderAssignmentRoutes(r, authMiddleware)
	SetupEquipmentTypeRoutes(r, authMiddleware)
	SetupOrderAssignmentRoutes(r, authMiddleware)
	SetupAuditLogRoutes(r, authMiddleware)

	// Configurar sistema de gerenciamento de vínculos
	// Nota: Esta função será chamada em main.go com os parâmetros corretos
	// SetupLinkManagementSystem(r, db, technicianOrderService)

	// ... outras rotas ...

	return r
}
