package routes

import (
	"log"
	"tradicao/internal/controllers"
	"tradicao/internal/repository"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupLinkManagementSystem configura o sistema de gerenciamento de vínculos
func SetupLinkManagementSystem(router *gin.Engine, db *gorm.DB, technicianOrderService *services.TechnicianOrderService) {
	log.Println("Configurando sistema de gerenciamento de vínculos...")

	// Inicializar repositórios
	branchRepoImpl := repository.NewBranchRepository(db)
	technicianRepo := repository.NewTechnicianRepository(db)
	serviceProviderRepo := repository.NewServiceProviderRepository(db)

	// Criar adaptador para a interface IBranchRepository
	branchRepo := repository.NewBranchRepositoryAdapter(branchRepoImpl)

	// Inicializar serviço
	linkManagementService := services.NewLinkManagementService(
		db,
		branchRepo,
		technicianRepo,
		serviceProviderRepo,
	)

	// Inicializar controlador
	linkManagementController := controllers.NewLinkManagementController(linkManagementService)

	// Configurar rotas
	SetupLinkManagementRoutes(router, linkManagementController, technicianOrderService)

	log.Println("Sistema de gerenciamento de vínculos configurado com sucesso")
}
