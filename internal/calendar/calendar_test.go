package calendar

import (
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestGetCalendarEvents(t *testing.T) {
	req, err := http.NewRequest("GET", "/api/calendar-events", nil)
	if err != nil {
		t.<PERSON>al(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(GetCalendarEvents)
	handler.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.<PERSON>rf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}

	expected := `[{"id":"1","title":"Maintenance","date":"2023-10-01"},{"id":"2","title":"Inspection","date":"2023-10-02"}]`
	if rr.Body.String() != expected {
		t.<PERSON>rf("handler returned unexpected body: got %v want %v", rr.Body.String(), expected)
	}
}
