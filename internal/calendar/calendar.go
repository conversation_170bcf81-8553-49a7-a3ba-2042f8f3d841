package calendar

import (
	"encoding/json"
	"net/http"
)

type CalendarEvent struct {
	ID    string `json:"id"`
	Title string `json:"title"`
	Date  string `json:"date"`
}

func GetCalendarEvents(w http.ResponseWriter, r *http.Request) {
	events := []CalendarEvent{
		{ID: "1", Title: "Maintenance", Date: "2023-10-01"},
		{ID: "2", Title: "Inspection", Date: "2023-10-02"},
	}
	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(events)
}
