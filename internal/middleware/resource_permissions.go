package middleware

import (
	"log"
	"net/http"
	"strconv"
	"tradicao/internal/permissions"

	"github.com/gin-gonic/gin"
)

// ResourcePermissionMiddleware é um middleware para verificar permissões de recursos
func ResourcePermissionMiddleware(resourceType permissions.ResourceType, action permissions.Action) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Obter ID do recurso do parâmetro da URL
		resourceIDStr := c.Param("id")
		if resourceIDStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "ID do recurso não fornecido",
			})
			c.Abort()
			return
		}

		// Converter para uint
		resourceID, err := strconv.ParseUint(resourceIDStr, 10, 32)
		if err != nil {
			c.J<PERSON>(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "ID do recurso inválido",
			})
			c.Abort()
			return
		}

		// Obter ID do usuário da sessão
		userIDInterface, exists := c.Get("userID")
		if !exists {
			log.Printf("[RESOURCE-MIDDLEWARE] ID do usuário não encontrado no contexto")
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "Usuário não autenticado",
			})
			c.Abort()
			return
		}

		// Converter para uint
		var userID uint
		switch v := userIDInterface.(type) {
		case uint:
			userID = v
		case int:
			userID = uint(v)
		case int64:
			userID = uint(v)
		case float64:
			userID = uint(v)
		case string:
			id, err := strconv.ParseUint(v, 10, 32)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"message": "ID do usuário inválido",
				})
				c.Abort()
				return
			}
			userID = uint(id)
		default:
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "Tipo de ID do usuário não suportado",
			})
			c.Abort()
			return
		}

		// Obter perfil do usuário
		userRoleInterface, exists := c.Get("userRole")
		if !exists {
			log.Printf("[RESOURCE-MIDDLEWARE] Perfil do usuário não encontrado no contexto")
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "Perfil do usuário não encontrado",
			})
			c.Abort()
			return
		}

		userRole := userRoleInterface.(string)

		// Obter IP e User-Agent
		ip := c.ClientIP()
		userAgent := c.Request.UserAgent()

		// Usar o serviço unificado de permissões
		unifiedService := permissions.GetGlobalUnifiedService()
		if unifiedService != nil {
			// Verificar permissão usando o serviço unificado
			if !unifiedService.HasResourcePermission(userID, userRole, resourceType, uint(resourceID), action, ip, userAgent) {
				log.Printf("[RESOURCE-MIDDLEWARE] Acesso negado: Usuário %d (role: %s) tentou %s recurso %s:%d",
					userID, userRole, action, resourceType, resourceID)
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"message": "Você não tem permissão para acessar este recurso",
				})
				c.Abort()
				return
			}
		} else {
			// Fallback para o serviço antigo
			service := permissions.GetGlobalService()
			if service == nil {
				log.Printf("[RESOURCE-MIDDLEWARE] Serviço de permissões não configurado")
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"message": "Serviço de permissões não configurado",
				})
				c.Abort()
				return
			}

			// Verificar permissão usando o serviço antigo
			if !service.HasResourcePermission(userID, resourceType, uint(resourceID), action) {
				log.Printf("[RESOURCE-MIDDLEWARE] Acesso negado: Usuário %d tentou %s recurso %s:%d",
					userID, action, resourceType, resourceID)
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"message": "Você não tem permissão para acessar este recurso",
				})
				c.Abort()
				return
			}
		}

		// Permissão concedida
		log.Printf("[RESOURCE-MIDDLEWARE] Acesso permitido: Usuário %d pode %s recurso %s:%d",
			userID, action, resourceType, resourceID)
		c.Next()
	}
}

// OrderViewMiddleware é um middleware para verificar permissão de visualização de ordens
func OrderViewMiddleware() gin.HandlerFunc {
	return ResourcePermissionMiddleware(permissions.ResourceOrder, permissions.ActionView)
}

// OrderUpdateMiddleware é um middleware para verificar permissão de atualização de ordens
func OrderUpdateMiddleware() gin.HandlerFunc {
	return ResourcePermissionMiddleware(permissions.ResourceOrder, permissions.ActionUpdate)
}

// OrderAssignMiddleware é um middleware para verificar permissão de atribuição de ordens
func OrderAssignMiddleware() gin.HandlerFunc {
	return ResourcePermissionMiddleware(permissions.ResourceOrder, permissions.ActionAssign)
}
