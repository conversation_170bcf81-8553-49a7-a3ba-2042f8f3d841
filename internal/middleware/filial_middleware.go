package middleware

import (
	"log"
	"tradicao/internal/models"
	"tradicao/internal/repository"

	"github.com/gin-gonic/gin"
)

// FilialMiddleware adiciona o ID da filial do usuário ao contexto
func FilialMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		log.Printf("[FILIAL-MIDDLEWARE] Iniciando middleware para rota: %s %s", c.Request.Method, c.Request.URL.Path)

		// Obtém o ID do usuário do contexto
		userIDInterface, exists := c.Get("userID")
		if !exists {
			log.Printf("[FILIAL-MIDDLEWARE] ID do usuário não encontrado no contexto")
			c.Next()
			return
		}
		log.Printf("[FILIAL-MIDDLEWARE] ID do usuário encontrado: %v (tipo: %T)", userIDInterface, userIDInterface)

		// Converte para uint
		var userID uint
		switch v := userIDInterface.(type) {
		case uint:
			userID = v
		case int:
			userID = uint(v)
		case int64:
			userID = uint(v)
		case float64:
			userID = uint(v)
		default:
			log.Printf("[FILIAL-MIDDLEWARE] Tipo de userID não suportado: %T", userIDInterface)
			c.Next()
			return
		}

		// Obtém o perfil do usuário
		userRoleInterface, exists := c.Get("userRole")
		if !exists {
			log.Printf("[FILIAL-MIDDLEWARE] Perfil do usuário não encontrado no contexto")
			c.Next()
			return
		}

		userRole := userRoleInterface.(string)
		log.Printf("[FILIAL-MIDDLEWARE] Perfil do usuário: %s", userRole)

		// Permitir acesso para usuários do tipo filial e branch_user
		if userRole != string(models.RoleFilial) && userRole != "branch_user" {
			log.Printf("[FILIAL-MIDDLEWARE] Usuário não é do tipo filial ou branch_user, não adicionando FilialID")
			// Para usuários admin, gerente e outros, permitimos acesso sem restrição de filial
			c.Next()
			return
		}

		// Obtém o FilialID do usuário
		userRepo := repository.NewGormUserRepository()
		user, err := userRepo.FindByID(userID)
		if err != nil {
			log.Printf("[FILIAL-MIDDLEWARE] Erro ao obter usuário: %v", err)
			c.Next()
			return
		}

		log.Printf("[FILIAL-MIDDLEWARE] Usuário obtido: ID=%d, Nome=%s, Email=%s, Role=%s, BranchID=%v",
			user.ID, user.Name, user.Email, user.Role, user.BranchID)

		// Verifica se o usuário tem um BranchID
		if user.BranchID == nil {
			log.Printf("[FILIAL-MIDDLEWARE] Usuário não tem BranchID")
			c.Next()
			return
		}

		// Adiciona o BranchID ao contexto como filialID
		c.Set("filialID", *user.BranchID)
		log.Printf("[FILIAL-MIDDLEWARE] FilialID %d adicionado ao contexto para o usuário %d", *user.BranchID, userID)

		// Adiciona também os IDs antigos ao contexto para compatibilidade
		c.Set("stationID", *user.BranchID)
		c.Set("branchID", *user.BranchID)
		log.Printf("[FILIAL-MIDDLEWARE] IDs de compatibilidade adicionados ao contexto para o usuário %d", userID)

		c.Next()
	}
}
