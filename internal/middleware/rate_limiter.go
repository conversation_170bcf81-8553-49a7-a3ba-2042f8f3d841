package middleware

import (
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/time/rate"
)

const (
	// DefaultLimit define o número padrão de requisições por segundo
	DefaultLimit = 5
	// DefaultBurst define o número padrão de requisições permitidas em burst
	DefaultBurst = 10
	// DefaultIPLimiterCleanupInterval é o intervalo para limpar limitadores expirados
	DefaultIPLimiterCleanupInterval = 5 * time.Minute
	// DefaultIPLimiterExpiration é o tempo para expirar um limitador por IP
	DefaultIPLimiterExpiration = 1 * time.Hour
)

// IPRateLimiter contém limitadores de taxa por IP
type IPRateLimiter struct {
	limiters     map[string]*rateLimiterEntry
	mu           sync.RWMutex
	limit        rate.Limit
	burst        int
	cleanupTimer *time.Timer
}

// rateLimiterEntry representa um limitador para um IP específico
type rateLimiterEntry struct {
	limiter  *rate.Limiter
	lastSeen time.Time
}

// NewIPRateLimiter cria um novo limitador de taxa baseado em IP
func NewIPRateLimiter(requestsPerSecond float64, burst int) *IPRateLimiter {
	limiter := &IPRateLimiter{
		limiters: make(map[string]*rateLimiterEntry),
		limit:    rate.Limit(requestsPerSecond),
		burst:    burst,
	}

	// Inicia temporizador para limpar limitadores não utilizados
	limiter.cleanupTimer = time.AfterFunc(DefaultIPLimiterCleanupInterval, func() {
		limiter.cleanup()
	})

	return limiter
}

// AddIP adiciona um IP ao limitador ou atualiza o timestamp se já existir
func (i *IPRateLimiter) AddIP(ip string) *rate.Limiter {
	i.mu.Lock()
	defer i.mu.Unlock()

	entry, exists := i.limiters[ip]
	if !exists {
		limiter := rate.NewLimiter(i.limit, i.burst)
		i.limiters[ip] = &rateLimiterEntry{
			limiter:  limiter,
			lastSeen: time.Now(),
		}
		return limiter
	}

	// Atualiza o timestamp
	entry.lastSeen = time.Now()
	return entry.limiter
}

// GetLimiter retorna o limitador para um IP específico
func (i *IPRateLimiter) GetLimiter(ip string) *rate.Limiter {
	i.mu.RLock()
	entry, exists := i.limiters[ip]
	i.mu.RUnlock()

	if !exists {
		return i.AddIP(ip)
	}

	// Atualiza o timestamp em uma goroutine separada para evitar bloqueio
	go func() {
		i.mu.Lock()
		entry.lastSeen = time.Now()
		i.mu.Unlock()
	}()

	return entry.limiter
}

// cleanup remove limitadores não utilizados
func (i *IPRateLimiter) cleanup() {
	i.mu.Lock()
	defer i.mu.Unlock()

	for ip, entry := range i.limiters {
		if time.Since(entry.lastSeen) > DefaultIPLimiterExpiration {
			delete(i.limiters, ip)
		}
	}

	// Agenda próxima limpeza
	i.cleanupTimer.Reset(DefaultIPLimiterCleanupInterval)
}

// Close encerra o temporizador de limpeza
func (i *IPRateLimiter) Close() {
	if i.cleanupTimer != nil {
		i.cleanupTimer.Stop()
	}
}

// Instâncias globais de limitadores para diferentes endpoints
var (
	// Limitador global padrão (5 requisições por segundo, burst de 10)
	GlobalLimiter = NewIPRateLimiter(DefaultLimit, DefaultBurst)

	// Limitador para login (3 requisições por segundo, burst de 5)
	LoginLimiter = NewIPRateLimiter(3, 5)

	// Limitador para 2FA (2 requisições por segundo, burst de 3)
	TwoFALimiter = NewIPRateLimiter(2, 3)

	// Limitador para endpoints de API (10 requisições por segundo, burst de 20)
	APILimiter = NewIPRateLimiter(10, 20)
)

// RateLimiterMiddleware aplica rate limiting baseado em IP
func RateLimiterMiddleware(limiter *IPRateLimiter) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Obtém o IP real do cliente, considerando proxies
		ip := c.ClientIP()

		// Obtém o limitador específico para este IP
		limiter := limiter.GetLimiter(ip)

		// Verifica se a requisição é permitida
		if !limiter.Allow() {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Limite de requisições excedido. Tente novamente mais tarde.",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// GlobalRateLimiter aplica o limitador global
func GlobalRateLimiter() gin.HandlerFunc {
	return RateLimiterMiddleware(GlobalLimiter)
}

// LoginRateLimiter aplica o limitador específico para login
func LoginRateLimiter() gin.HandlerFunc {
	return RateLimiterMiddleware(LoginLimiter)
}

// TwoFARateLimiter aplica o limitador específico para 2FA
func TwoFARateLimiter() gin.HandlerFunc {
	return RateLimiterMiddleware(TwoFALimiter)
}

// APIRateLimiter aplica o limitador específico para endpoints de API
func APIRateLimiter() gin.HandlerFunc {
	return RateLimiterMiddleware(APILimiter)
}
