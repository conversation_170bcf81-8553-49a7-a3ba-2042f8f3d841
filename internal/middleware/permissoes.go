package middleware

import (
	"log"
	"net/http"
	"tradicao/internal/permissions"

	"github.com/gin-gonic/gin"
)

// Este arquivo contém as funções de middleware para verificação de permissões
// Usa o sistema de permissões unificado definido em internal/permissions

// PageAccessMiddleware_Internal verifica se o usuário tem permissão para acessar uma página específica
// Esta é a implementação interna usada por PageAccessMiddleware
func PageAccessMiddleware_Internal() gin.HandlerFunc {
	// Usar o sistema de permissões unificado
	unifiedMiddleware := permissions.GetGlobalUnifiedMiddleware()
	if unifiedMiddleware != nil {
		// Usar o middleware unificado de permissões
		return unifiedMiddleware.PageAccessMiddleware()
	}

	// Fallback para o sistema antigo
	middleware := permissions.GetGlobalMiddleware()
	if middleware != nil {
		// Usar o middleware de permissões existente
		return middleware.PageAccessMiddleware()
	}

	// Se nenhum middleware estiver disponível, retornar erro
	log.Println("ERRO: Nenhum middleware de permissões configurado")
	return func(c *gin.Context) {
		log.Printf("ERRO: Tentativa de acesso à página %s sem middleware de permissões configurado", c.Request.URL.Path)
		c.HTML(http.StatusInternalServerError, "auth/acesso_negado.html", gin.H{
			"title":   "Erro de Configuração",
			"message": "Sistema de permissões não configurado corretamente. Entre em contato com o administrador.",
		})
		c.Abort()
	}
}
