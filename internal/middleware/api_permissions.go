package middleware

import (
	"log"
	"net/http"
	"tradicao/internal/permissions"

	"github.com/gin-gonic/gin"
)

// Este arquivo contém as funções de middleware para verificação de permissões de API
// Usa o sistema de permissões unificado definido em internal/permissions

// APIAccessMiddleware verifica se o usuário tem permissão para acessar uma API específica
func APIAccessMiddleware() gin.HandlerFunc {
	// Usar o sistema de permissões unificado
	unifiedMiddleware := permissions.GetGlobalUnifiedMiddleware()
	if unifiedMiddleware != nil {
		// Usar o middleware unificado de permissões
		return unifiedMiddleware.APIAccessMiddleware()
	}

	// Fallback para o sistema antigo
	middleware := permissions.GetGlobalMiddleware()
	if middleware != nil {
		// Usar o middleware de permissões existente
		return middleware.APIAccessMiddleware()
	}

	// Se nenhum middleware estiver disponível, retornar erro
	log.Println("ERRO: Nenhum middleware de permissões configurado")
	return func(c *gin.Context) {
		log.Printf("ERRO: Tentativa de acesso à API %s sem middleware de permissões configurado", c.Request.URL.Path)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Sistema de permissões não configurado corretamente. Entre em contato com o administrador.",
		})
		c.Abort()
	}
}
