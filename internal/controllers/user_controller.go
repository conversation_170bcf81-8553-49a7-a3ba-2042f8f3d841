package controllers

import (
	"log"
	"net/http"
	"strconv"

	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// UserController gerencia endpoints relacionados a usuários
type UserController struct {
	userService *services.UserService
}

// NewUserController cria um novo controlador de usuários
func NewUserController(userService *services.UserService) *UserController {
	return &UserController{
		userService: userService,
	}
}

// ListUsers lista todos os usuários (apenas para admin)
func ListUsers(c *gin.Context) {
	users, err := services.GetAllUsers()
	if err != nil {
		log.Printf("Erro ao listar usuários: %v", err)
		c.JSO<PERSON>(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar usuários",
		})
		return
	}

	// Remover informações sensíveis como senhas
	var safeUsers []gin.H
	for _, user := range users {
		safeUsers = append(safeUsers, gin.H{
			"id":         user.ID,
			"name":       user.Name,
			"email":      user.Email,
			"role":       user.Role,
			"created_at": user.CreatedAt,
			"active":     !user.Blocked,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"users": safeUsers,
	})
}

// CreateUser cria um novo usuário (apenas para admin)
func CreateUser(c *gin.Context) {
	var input struct {
		Name     string `json:"name" binding:"required"`
		Email    string `json:"email" binding:"required,email"`
		Password string `json:"password" binding:"required,min=8"`
		Role     string `json:"role" binding:"required"`
		BranchID *uint  `json:"branch_id"`
	}

	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Dados inválidos",
			"details": err.Error(),
		})
		return
	}

	// Criar o usuário
	user := models.User{
		Name:     input.Name,
		Email:    input.Email,
		Role:     models.UserRole(input.Role),
		BranchID: input.BranchID,
	}

	createdUser, err := services.CreateUser(user, input.Password)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Remover senha antes de retornar
	createdUser.Password = ""

	c.JSON(http.StatusCreated, gin.H{
		"message": "Usuário criado com sucesso",
		"user":    createdUser,
	})
}

// GetUser obtém um usuário pelo ID
func GetUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	user, err := services.GetUserByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Usuário não encontrado",
		})
		return
	}

	// Remover senha antes de retornar
	user.Password = ""

	c.JSON(http.StatusOK, user)
}

// UpdateUser atualiza um usuário existente
func UpdateUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	var input struct {
		Name     string `json:"name"`
		Email    string `json:"email"`
		Role     string `json:"role"`
		BranchID *uint  `json:"branch_id"`
		Active   bool   `json:"active"`
	}

	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Dados inválidos",
			"details": err.Error(),
		})
		return
	}

	// Buscar o usuário existente
	user, err := services.GetUserByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Usuário não encontrado",
		})
		return
	}

	// Atualizar campos
	if input.Name != "" {
		user.Name = input.Name
	}
	if input.Email != "" {
		user.Email = input.Email
	}
	if input.Role != "" {
		user.Role = models.UserRole(input.Role)
	}
	if input.BranchID != nil {
		user.BranchID = input.BranchID
	}
	user.Blocked = !input.Active

	// Salvar alterações
	updatedUser, err := services.UpdateUser(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao atualizar usuário",
		})
		return
	}

	// Remover senha antes de retornar
	updatedUser.Password = ""

	c.JSON(http.StatusOK, gin.H{
		"message": "Usuário atualizado com sucesso",
		"user":    updatedUser,
	})
}

// DeleteUser remove um usuário (apenas para admin)
func DeleteUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "ID inválido",
		})
		return
	}

	err = services.DeleteUser(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao remover usuário",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Usuário removido com sucesso",
	})
}
