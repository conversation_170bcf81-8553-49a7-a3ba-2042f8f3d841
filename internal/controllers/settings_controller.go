package controllers

import (
	"encoding/json"
	"net/http"
)

// SettingsResponse representa a resposta para operações de configurações
type SettingsResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
}

// SaveSettings processa e salva as configurações do usuário
func SaveSettings(w http.ResponseWriter, r *http.Request) {
	// Verificar se o método é POST
	if r.Method != http.MethodPost {
		http.Error(w, "Método não permitido", http.StatusMethodNotAllowed)
		return
	}

	// Analisar o formulário
	err := r.ParseForm()
	if err != nil {
		respondWithError(w, "Erro ao processar formulário", http.StatusBadRequest)
		return
	}

	// Obter valores do formulário
	// Removido: emailNotifications := r.FormValue("email_notifications") == "on"
	// Removido: smsNotifications := r.FormValue("sms_notifications") == "on"
	// Removido: language := r.FormValue("language")
	// Removido: twoFactorAuth := r.FormValue("two_factor_auth") == "on"

	// Aqui você implementaria a lógica para salvar essas configurações no banco de dados
	// Por exemplo:
	// err = services.SaveUserSettings(userID, emailNotifications, smsNotifications, language, twoFactorAuth)
	// if err != nil {
	//     respondWithError(w, "Erro ao salvar configurações", http.StatusInternalServerError)
	//     return
	// }

	// Responder com sucesso
	respondWithSuccess(w, "Configurações salvas com sucesso")
}

// Função auxiliar para responder com erro
func respondWithError(w http.ResponseWriter, message string, statusCode int) {
	response := SettingsResponse{
		Success: false,
		Message: message,
	}
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}

// Função auxiliar para responder com sucesso
func respondWithSuccess(w http.ResponseWriter, message string) {
	response := SettingsResponse{
		Success: true,
		Message: message,
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}
