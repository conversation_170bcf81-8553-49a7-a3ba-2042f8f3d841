package controllers

import (
	"net/http"
	"strconv"
	"time"

	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// BranchController gerencia as requisições relacionadas a filiais
type BranchController struct {
	service *services.BranchService
}

// NewBranchController cria uma nova instância do controlador de filiais
func NewBranchController(service *services.BranchService) *BranchController {
	return &BranchController{service: service}
}

// Create cria uma nova filial
func (c *BranchController) Create(ctx *gin.Context) {
	var branch models.Branch
	if err := ctx.ShouldBindJSON(&branch); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
		return
	}

	if err := c.service.CreateBranch(&branch); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao criar filial", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{"message": "Filial criada com sucesso", "branch": branch})
}

// Get busca uma filial pelo ID
func (c *BranchController) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	branch, err := c.service.GetBranchByID(uint(id))
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, branch)
}

// List lista todas as filiais com filtros
func (c *BranchController) List(ctx *gin.Context) {
	// Parâmetros de paginação e filtros
	pageStr := ctx.DefaultQuery("page", "1")
	limitStr := ctx.DefaultQuery("limit", "10")
	search := ctx.DefaultQuery("search", "")
	statusStr := ctx.DefaultQuery("status", "active")

	page, _ := strconv.Atoi(pageStr)
	limit, _ := strconv.Atoi(limitStr)

	var isActive *bool
	if statusStr == "active" {
		active := true
		isActive = &active
	}

	branches, total, err := c.service.GetAllBranches(page, limit, search, isActive)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar filiais", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"branches": branches,
		"total":    total,
		"page":     page,
		"limit":    limit,
	})
}

// Update atualiza uma filial existente
func (c *BranchController) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var branch models.Branch
	if err := ctx.ShouldBindJSON(&branch); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
		return
	}

	branch.ID = uint(id)
	if err := c.service.UpdateBranch(&branch); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar filial", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Filial atualizada com sucesso", "branch": branch})
}

// Delete remove uma filial
func (c *BranchController) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	if err := c.service.DeleteBranch(uint(id)); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao remover filial", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Filial removida com sucesso"})
}

// LinkProvider vincula um prestador a uma filial
func (c *BranchController) LinkProvider(ctx *gin.Context) {
	branchIDStr := ctx.Param("id")
	branchID, err := strconv.ParseUint(branchIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}

	var data struct {
		ProviderID uint `json:"provider_id" binding:"required"`
	}
	if err := ctx.ShouldBindJSON(&data); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
		return
	}

	if err := c.service.LinkProvider(uint(branchID), data.ProviderID); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao vincular prestador", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Prestador vinculado com sucesso"})
}

// UnlinkProvider remove o vínculo entre um prestador e uma filial
func (c *BranchController) UnlinkProvider(ctx *gin.Context) {
	branchIDStr := ctx.Param("id")
	branchID, err := strconv.ParseUint(branchIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}

	providerIDStr := ctx.Param("provider_id")
	providerID, err := strconv.ParseUint(providerIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID do prestador inválido"})
		return
	}

	if err := c.service.UnlinkProvider(uint(branchID), uint(providerID)); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao desvincular prestador", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Prestador desvinculado com sucesso"})
}

// GetLinkedProviders lista os prestadores vinculados a uma filial
func (c *BranchController) GetLinkedProviders(ctx *gin.Context) {
	branchIDStr := ctx.Param("id")
	branchID, err := strconv.ParseUint(branchIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}

	providerIDs, err := c.service.GetLinkedProviders(uint(branchID))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao listar prestadores", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"provider_ids": providerIDs})
}

// GenerateToken gera um token temporário para a filial
func (c *BranchController) GenerateToken(ctx *gin.Context) {
	branchIDStr := ctx.Param("id")
	branchID, err := strconv.ParseUint(branchIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}

	token, err := c.service.GenerateAuthToken(uint(branchID))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao gerar token", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"token":      token,
		"expires_at": time.Now().Add(24 * time.Hour),
	})
}

// ValidateToken valida um token temporário
func (c *BranchController) ValidateToken(ctx *gin.Context) {
	var data struct {
		Token string `json:"token" binding:"required"`
	}
	if err := ctx.ShouldBindJSON(&data); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Token não fornecido"})
		return
	}

	branchID, err := c.service.ValidateAuthToken(data.Token)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
		return
	}

	// Marcar token como usado
	if err := c.service.UseAuthToken(data.Token); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao processar token"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message":   "Token válido",
		"branch_id": branchID,
	})
}
