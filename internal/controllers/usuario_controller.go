package controllers

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// UsuarioController estrutura para o controller de usuários
type UsuarioController struct {
}

// NewUsuarioController cria uma nova instância do controller de usuários
func NewUsuarioController() *UsuarioController {
	return &UsuarioController{}
}

// Index renderiza a página principal de usuários
func (c *UsuarioController) Index(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "usuarios/index.html", gin.H{
		"title": "Usuários",
	})
}
