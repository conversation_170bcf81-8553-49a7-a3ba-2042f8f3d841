package controllers

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// DashboardController estrutura para o controller do dashboard
type DashboardController struct {
}

// NewDashboardController cria uma nova instância do controller do dashboard
func NewDashboardController() *DashboardController {
	return &DashboardController{}
}

// Index renderiza a página principal do dashboard
func (c *DashboardController) Index(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "dashboard/index.html", gin.H{
		"title": "Dashboard",
	})
}
