package controllers

import (
	"net/http"
	"strconv"
	"tradicao/internal/notifications"

	"github.com/gin-gonic/gin"
)

// NotificationController é o controlador de notificações
type NotificationController struct {
	service *notifications.Service
}

// NewNotificationController cria um novo controlador de notificações
func NewNotificationController() *NotificationController {
	return &NotificationController{
		service: notifications.NewService(),
	}
}

// GetNotifications retorna as notificações do usuário
func (c *NotificationController) GetNotifications(ctx *gin.Context) {
	// Obter ID do usuário do contexto
	userIDInterface, exists := ctx.Get("userID")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "Usuário não autenticado",
		})
		return
	}

	// Converter para uint
	var userID uint
	switch v := userIDInterface.(type) {
	case uint:
		userID = v
	case int:
		userID = uint(v)
	case int64:
		userID = uint(v)
	case float64:
		userID = uint(v)
	case string:
		id, err := strconv.ParseUint(v, 10, 32)
		if err != nil {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "ID do usuário inválido",
			})
			return
		}
		userID = uint(id)
	default:
		ctx.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Tipo de ID do usuário não suportado",
		})
		return
	}

	// Obter parâmetros de paginação
	limitStr := ctx.DefaultQuery("limit", "10")
	offsetStr := ctx.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 10
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		offset = 0
	}

	// Obter notificações
	notifications, err := c.service.GetUserNotifications(userID, limit, offset)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao obter notificações",
			"error":   err.Error(),
		})
		return
	}

	// Obter contagem de não lidas
	unreadCount, err := c.service.GetUnreadCount(userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao obter contagem de notificações não lidas",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success":      true,
		"notifications": notifications,
		"unread_count": unreadCount,
	})
}

// MarkAsRead marca uma notificação como lida
func (c *NotificationController) MarkAsRead(ctx *gin.Context) {
	// Obter ID do usuário do contexto
	userIDInterface, exists := ctx.Get("userID")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "Usuário não autenticado",
		})
		return
	}

	// Converter para uint
	var userID uint
	switch v := userIDInterface.(type) {
	case uint:
		userID = v
	case int:
		userID = uint(v)
	case int64:
		userID = uint(v)
	case float64:
		userID = uint(v)
	case string:
		id, err := strconv.ParseUint(v, 10, 32)
		if err != nil {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "ID do usuário inválido",
			})
			return
		}
		userID = uint(id)
	default:
		ctx.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Tipo de ID do usuário não suportado",
		})
		return
	}

	// Obter ID da notificação
	notificationIDStr := ctx.Param("id")
	notificationID, err := strconv.ParseUint(notificationIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "ID da notificação inválido",
		})
		return
	}

	// Marcar como lida
	err = c.service.MarkAsRead(uint(notificationID), userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao marcar notificação como lida",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Notificação marcada como lida",
	})
}

// MarkAllAsRead marca todas as notificações do usuário como lidas
func (c *NotificationController) MarkAllAsRead(ctx *gin.Context) {
	// Obter ID do usuário do contexto
	userIDInterface, exists := ctx.Get("userID")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "Usuário não autenticado",
		})
		return
	}

	// Converter para uint
	var userID uint
	switch v := userIDInterface.(type) {
	case uint:
		userID = v
	case int:
		userID = uint(v)
	case int64:
		userID = uint(v)
	case float64:
		userID = uint(v)
	case string:
		id, err := strconv.ParseUint(v, 10, 32)
		if err != nil {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "ID do usuário inválido",
			})
			return
		}
		userID = uint(id)
	default:
		ctx.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Tipo de ID do usuário não suportado",
		})
		return
	}

	// Marcar todas como lidas
	err := c.service.MarkAllAsRead(userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao marcar todas as notificações como lidas",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Todas as notificações marcadas como lidas",
	})
}

// DeleteNotification exclui uma notificação
func (c *NotificationController) DeleteNotification(ctx *gin.Context) {
	// Obter ID do usuário do contexto
	userIDInterface, exists := ctx.Get("userID")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "Usuário não autenticado",
		})
		return
	}

	// Converter para uint
	var userID uint
	switch v := userIDInterface.(type) {
	case uint:
		userID = v
	case int:
		userID = uint(v)
	case int64:
		userID = uint(v)
	case float64:
		userID = uint(v)
	case string:
		id, err := strconv.ParseUint(v, 10, 32)
		if err != nil {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "ID do usuário inválido",
			})
			return
		}
		userID = uint(id)
	default:
		ctx.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Tipo de ID do usuário não suportado",
		})
		return
	}

	// Obter ID da notificação
	notificationIDStr := ctx.Param("id")
	notificationID, err := strconv.ParseUint(notificationIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "ID da notificação inválido",
		})
		return
	}

	// Excluir notificação
	err = c.service.DeleteNotification(uint(notificationID), userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao excluir notificação",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Notificação excluída",
	})
}
