package controllers

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// ImpostosController estrutura para o controller de impostos
type ImpostosController struct {
}

// NewImpostosController cria uma nova instância do controller de impostos
func NewImpostosController() *ImpostosController {
	return &ImpostosController{}
}

// ConsultaImpostos renderiza a página de consulta de impostos
func (c *ImpostosController) ConsultaImpostos(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "financeiro/consulta-impostos-new.html", gin.H{
		"title": "Consulta de Impostos de Combustíveis",
	})
}
