package controllers

import (
	"log"
	"net/http"

	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// SettingsController gerencia endpoints relacionados a configurações
type SettingsController struct {
	settingsService *services.SettingsService
}

// NewSettingsController cria um novo controlador de configurações
func NewSettingsController(settingsService *services.SettingsService) *SettingsController {
	return &SettingsController{
		settingsService: settingsService,
	}
}

// GetSettings retorna as configurações do sistema
func GetSettings(c *gin.Context) {
	// Obter configurações do sistema
	settings, err := services.GetSystemSettings()
	if err != nil {
		log.Printf("Erro ao buscar configurações: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar configurações",
		})
		return
	}

	c.JSON(http.StatusOK, settings)
}

// UpdateSettings atualiza as configurações do sistema
func UpdateSettings(c *gin.Context) {
	var input models.SystemSettings

	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Dados inválidos",
			"details": err.Error(),
		})
		return
	}

	// Atualizar configurações
	updatedSettings, err := services.UpdateSystemSettings(input)
	if err != nil {
		log.Printf("Erro ao atualizar configurações: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao atualizar configurações",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":  "Configurações atualizadas com sucesso",
		"settings": updatedSettings,
	})
}

// GetBackups lista os backups disponíveis
func GetBackups(c *gin.Context) {
	// Obter lista de backups
	backups, err := services.GetBackupsList()
	if err != nil {
		log.Printf("Erro ao listar backups: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao listar backups",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"backups": backups,
	})
}

// CreateBackup cria um novo backup
func CreateBackup(c *gin.Context) {
	// Criar backup
	backup, err := services.CreateBackup()
	if err != nil {
		log.Printf("Erro ao criar backup: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao criar backup",
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Backup criado com sucesso",
		"backup":  backup,
	})
}

// RestoreBackup restaura um backup
func RestoreBackup(c *gin.Context) {
	var input struct {
		BackupID string `json:"backup_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Dados inválidos",
			"details": err.Error(),
		})
		return
	}

	// Restaurar backup
	err := services.RestoreBackup(input.BackupID)
	if err != nil {
		log.Printf("Erro ao restaurar backup: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao restaurar backup",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Backup restaurado com sucesso",
	})
}

// GetLogs retorna os logs do sistema
func GetLogs(c *gin.Context) {
	// Obter logs do sistema
	logs, err := services.GetSystemLogs()
	if err != nil {
		log.Printf("Erro ao buscar logs: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar logs",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"logs": logs,
	})
}

// CreateIntegration cria uma nova integração
func CreateIntegration(c *gin.Context) {
	var input models.Integration

	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Dados inválidos",
			"details": err.Error(),
		})
		return
	}

	// Criar integração
	integration, err := services.CreateIntegration(input)
	if err != nil {
		log.Printf("Erro ao criar integração: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao criar integração",
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":     "Integração criada com sucesso",
		"integration": integration,
	})
}
