package controllers

import (
	"log"
	"net/http"

	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// AuthController gerencia endpoints de autenticação
type AuthController struct {
	authService *services.AuthService
}

// NewAuthController cria um novo controlador de autenticação
func NewAuthController(authService *services.AuthService) *AuthController {
	return &AuthController{
		authService: authService,
	}
}

// Login realiza a autenticação do usuário
func (c *AuthController) Login(ctx *gin.Context) {
	var loginInput struct {
		Email    string `json:"email" binding:"required,email"`
		Password string `json:"password" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&loginInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":   "Formato de requisição inválido",
			"details": err.Error(),
		})
		return
	}

	token, user, err := c.authService.Login(loginInput.Email, loginInput.Password)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"error": err.Error(),
		})
		return
	}

	ctx.SetCookie(
		"session_token",
		token,
		3600*24, // 1 dia
		"/",
		"",
		false, // em produção, deveria ser true para HTTPS
		true,  // HttpOnly
	)

	c.logAction(ctx, user.ID, "login", "Autenticação bem-sucedida")

	ctx.JSON(http.StatusOK, gin.H{
		"token": token,
		"user": gin.H{
			"id":    user.ID,
			"name":  user.Name,
			"email": user.Email,
			"role":  user.Role,
		},
	})
}

// Logout finaliza a sessão do usuário
func (c *AuthController) Logout(ctx *gin.Context) {
	ctx.SetCookie(
		"session_token",
		"",
		-1,
		"/",
		"",
		false,
		true,
	)

	userID, exists := ctx.Get("userID")
	if exists {
		c.logAction(ctx, userID.(uint), "logout", "Logout bem-sucedido")
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Logout realizado com sucesso",
	})
}

// Register registra um novo usuário
func (c *AuthController) Register(ctx *gin.Context) {
	var registerInput struct {
		Name     string `json:"name" binding:"required"`
		Email    string `json:"email" binding:"required,email"`
		Password string `json:"password" binding:"required,min=8"`
	}

	if err := ctx.ShouldBindJSON(&registerInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":   "Dados inválidos",
			"details": err.Error(),
		})
		return
	}

	user := models.User{
		Name:  registerInput.Name,
		Email: registerInput.Email,
		Role:  "user", // Role padrão
	}

	newUser, err := c.authService.Register(user, registerInput.Password)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.logAction(ctx, newUser.ID, "register", "Novo usuário registrado")

	ctx.JSON(http.StatusCreated, gin.H{
		"message": "Usuário criado com sucesso",
		"user": gin.H{
			"id":    newUser.ID,
			"name":  newUser.Name,
			"email": newUser.Email,
			"role":  newUser.Role,
		},
	})
}

// RequestPasswordReset solicita a redefinição de senha
func (c *AuthController) RequestPasswordReset(ctx *gin.Context) {
	var resetRequest struct {
		Email string `json:"email" binding:"required,email"`
	}

	if err := ctx.ShouldBindJSON(&resetRequest); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Email inválido",
		})
		return
	}

	_, err := c.authService.RequestPasswordReset(resetRequest.Email)
	if err != nil {
		log.Printf("Erro ao solicitar reset de senha: %v", err)
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Se o email estiver cadastrado, você receberá instruções para redefinir sua senha",
	})
}

// ResetPassword redefine a senha com o token recebido
func (c *AuthController) ResetPassword(ctx *gin.Context) {
	var resetData struct {
		Token       string `json:"token" binding:"required"`
		NewPassword string `json:"new_password" binding:"required,min=8"`
	}

	if err := ctx.ShouldBindJSON(&resetData); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":   "Dados inválidos",
			"details": err.Error(),
		})
		return
	}

	err := c.authService.ResetPassword(resetData.Token, resetData.NewPassword)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Senha redefinida com sucesso",
	})
}

// ChangePassword altera a senha do usuário autenticado
func (c *AuthController) ChangePassword(ctx *gin.Context) {
	userID, _ := ctx.Get("userID")

	var changeData struct {
		CurrentPassword string `json:"current_password" binding:"required"`
		NewPassword     string `json:"new_password" binding:"required,min=8"`
	}

	if err := ctx.ShouldBindJSON(&changeData); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":   "Dados inválidos",
			"details": err.Error(),
		})
		return
	}

	err := c.authService.ChangePassword(userID.(uint), changeData.CurrentPassword, changeData.NewPassword)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.logAction(ctx, userID.(uint), "change_password", "Senha alterada com sucesso")

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Senha alterada com sucesso",
	})
}

// CheckAuth verifica se o usuário está autenticado
func (c *AuthController) CheckAuth(ctx *gin.Context) {
	userID, _ := ctx.Get("userID")
	role, _ := ctx.Get("role")

	ctx.JSON(http.StatusOK, gin.H{
		"authenticated": true,
		"user_id":       userID,
		"role":          role,
	})
}

// RefreshToken renova o token JWT
func (c *AuthController) RefreshToken(ctx *gin.Context) {
	newToken := ctx.GetHeader("X-Renew-Token")
	if newToken != "" {
		ctx.SetCookie(
			"session_token",
			newToken,
			3600*24, // 1 dia
			"/",
			"",
			false,
			true,
		)

		ctx.JSON(http.StatusOK, gin.H{
			"token":   newToken,
			"message": "Token renovado com sucesso",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"authenticated": true,
		"message":       "Token ainda válido",
	})
}

// --- Métodos para Status de Senha e 2FA ---

// CheckPasswordStatus verifica se a senha do usuário precisa ser alterada
func (c *AuthController) CheckPasswordStatus(ctx *gin.Context) {
	userIDValue, exists := ctx.Get("userID")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}
	userID, ok := userIDValue.(uint)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "ID do usuário inválido no contexto"})
		return
	}

	status, err := c.authService.GetPasswordStatus(userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Erro ao verificar status da senha",
			"details": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"password_status": status})
}

// SetupTOTP inicia o processo de configuração do 2FA
func (c *AuthController) SetupTOTP(ctx *gin.Context) {
	userIDValue, exists := ctx.Get("userID")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}
	userID, ok := userIDValue.(uint)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "ID do usuário inválido no contexto"})
		return
	}

	secret, qrCodeURL, err := c.authService.SetupTOTP(userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Erro ao iniciar configuração TOTP",
			"details": err.Error(),
		})
		return
	}

	// Retorna o segredo e a URL para o frontend gerar o QR code
	ctx.JSON(http.StatusOK, gin.H{
		"secret":      secret,
		"qr_code_url": qrCodeURL,
	})
}

// VerifyAndEnableTOTP verifica o código TOTP e ativa o 2FA
func (c *AuthController) VerifyAndEnableTOTP(ctx *gin.Context) {
	userIDValue, exists := ctx.Get("userID")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}
	userID, ok := userIDValue.(uint)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "ID do usuário inválido no contexto"})
		return
	}

	var input struct {
		TOTPCode string `json:"totp_code" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&input); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":   "Código TOTP não fornecido ou inválido",
			"details": err.Error(),
		})
		return
	}

	err := c.authService.VerifyAndEnableTOTP(userID, input.TOTPCode)
	if err != nil {
		// Erro pode ser "código inválido" ou erro interno
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":   "Falha ao ativar 2FA",
			"details": err.Error(),
		})
		return
	}

	c.logAction(ctx, userID, "enable_totp", "2FA ativado com sucesso")
	ctx.JSON(http.StatusOK, gin.H{"message": "Autenticação de dois fatores ativada com sucesso"})
}

// DisableTOTP desativa o 2FA para o usuário
func (c *AuthController) DisableTOTP(ctx *gin.Context) {
	userIDValue, exists := ctx.Get("userID")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}
	userID, ok := userIDValue.(uint)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "ID do usuário inválido no contexto"})
		return
	}

	err := c.authService.DisableTOTP(userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Falha ao desativar 2FA",
			"details": err.Error(),
		})
		return
	}

	c.logAction(ctx, userID, "disable_totp", "2FA desativado com sucesso")
	ctx.JSON(http.StatusOK, gin.H{"message": "Autenticação de dois fatores desativada com sucesso"})
}

// ServeQRCode - Removido conforme solicitado
// func (c *AuthController) ServeQRCode(ctx *gin.Context) {
// 	 // ... implementação para gerar e servir QR code ...
// }

// logAction é um helper para registrar ações do usuário (se existir)
func (c *AuthController) logAction(ctx *gin.Context, userID uint, action string, details string) {
	// Exemplo: poderia chamar um serviço de log ou auditoria
	log.Printf("UserAction: UserID=%d, Action=%s, Details=%s, IP=%s",
		userID, action, details, ctx.ClientIP())
}
