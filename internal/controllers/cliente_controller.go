package controllers

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// ClienteController estrutura para o controller de clientes
type ClienteController struct {
}

// NewClienteController cria uma nova instância do controller de clientes
func NewClienteController() *ClienteController {
	return &ClienteController{}
}

// Index renderiza a página principal de clientes
func (c *ClienteController) Index(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "clientes/index.html", gin.H{
		"title": "Clientes",
	})
}
