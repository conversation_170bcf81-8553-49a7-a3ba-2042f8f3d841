package controllers

import (
	"net/http"
	"strconv"
	"time"

	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

type TechnicianController struct {
	service services.TechnicianService
}

func NewTechnicianController(service services.TechnicianService) *TechnicianController {
	return &TechnicianController{service: service}
}

// @Summary Criar especialidade
// @Description Cria uma nova especialidade para técnicos
// @Tags technician
// @Accept json
// @Produce json
// @Param specialty body models.TechnicianSpecialty true "Dados da especialidade"
// @Success 201 {object} models.TechnicianSpecialty
// @Router /api/technician/specialties [post]
func (c *TechnicianController) CreateSpecialty(ctx *gin.Context) {
	var specialty models.TechnicianSpecialty
	if err := ctx.ShouldBindJSON(&specialty); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := c.service.CreateSpecialty(&specialty); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, specialty)
}

// @Summary Listar especialidades
// @Description Lista todas as especialidades de técnicos
// @Tags technician
// @Produce json
// @Success 200 {array} models.TechnicianSpecialty
// @Router /api/technician/specialties [get]
func (c *TechnicianController) ListSpecialties(ctx *gin.Context) {
	specialties, err := c.service.ListSpecialties()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, specialties)
}

// UpdateSpecialty atualiza uma especialidade existente
func (c *TechnicianController) UpdateSpecialty(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var specialty models.TechnicianSpecialty
	if err := ctx.ShouldBindJSON(&specialty); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	specialty.ID = uint(id)
	if err := c.service.UpdateSpecialty(&specialty); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, specialty)
}

// DeleteSpecialty remove uma especialidade
func (c *TechnicianController) DeleteSpecialty(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	if err := c.service.DeleteSpecialty(uint(id)); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusNoContent)
}

// @Summary Vincular técnico a filial
// @Description Vincula um técnico a uma filial com uma especialidade
// @Tags technician
// @Accept json
// @Produce json
// @Param association body models.TechnicianBranch true "Dados da vinculação"
// @Success 201 {object} models.TechnicianBranch
// @Router /api/technician/branches [post]
func (c *TechnicianController) CreateBranchAssociation(ctx *gin.Context) {
	var association models.TechnicianBranch
	if err := ctx.ShouldBindJSON(&association); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := c.service.CreateBranchAssociation(&association); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, association)
}

// @Summary Listar vinculações do técnico
// @Description Lista todas as vinculações de um técnico com filiais
// @Tags technician
// @Produce json
// @Param technician_id path int true "ID do técnico"
// @Success 200 {array} models.TechnicianBranch
// @Router /api/technician/{technician_id}/branches [get]
func (c *TechnicianController) ListBranchAssociations(ctx *gin.Context) {
	technicianID, err := strconv.ParseUint(ctx.Param("technician_id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	associations, err := c.service.ListBranchAssociations(uint(technicianID))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, associations)
}

// DeleteBranchAssociation remove uma associação
func (c *TechnicianController) DeleteBranchAssociation(ctx *gin.Context) {
	technicianID, err := strconv.ParseUint(ctx.Param("technician_id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID do técnico inválido"})
		return
	}

	branchID, err := strconv.ParseUint(ctx.Param("branch_id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}

	if err := c.service.DeleteBranchAssociation(uint(technicianID), uint(branchID)); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusNoContent)
}

// @Summary Criar histórico de manutenção
// @Description Registra um novo histórico de manutenção
// @Tags technician
// @Accept json
// @Produce json
// @Param history body models.MaintenanceHistory true "Dados do histórico"
// @Success 201 {object} models.MaintenanceHistory
// @Router /api/technician/history [post]
func (c *TechnicianController) CreateMaintenanceHistory(ctx *gin.Context) {
	var history models.MaintenanceHistory
	if err := ctx.ShouldBindJSON(&history); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := c.service.CreateMaintenanceHistory(&history); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, history)
}

// @Summary Listar histórico de manutenção
// @Description Lista o histórico de manutenções de um técnico
// @Tags technician
// @Produce json
// @Param technician_id path int true "ID do técnico"
// @Param start_date query string true "Data inicial (YYYY-MM-DD)"
// @Param end_date query string true "Data final (YYYY-MM-DD)"
// @Success 200 {array} models.MaintenanceHistory
// @Router /api/technician/{technician_id}/history [get]
func (c *TechnicianController) ListMaintenanceHistory(ctx *gin.Context) {
	technicianID, err := strconv.ParseUint(ctx.Param("technician_id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	startDate, err := time.Parse("2006-01-02", ctx.Query("start_date"))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Data inicial inválida"})
		return
	}

	endDate, err := time.Parse("2006-01-02", ctx.Query("end_date"))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Data final inválida"})
		return
	}

	history, err := c.service.ListMaintenanceHistory(uint(technicianID), startDate, endDate)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, history)
}
