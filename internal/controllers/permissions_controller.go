package controllers

import (
	"log"
	"net/http"
	"tradicao/internal/permissions"

	"github.com/gin-gonic/gin"
)

type PermissionsController struct {
	service    *permissions.Service
	configPath string
}

func NewPermissionsController(service *permissions.Service, configPath string) *PermissionsController {
	return &PermissionsController{
		service:    service,
		configPath: configPath,
	}
}

// RenderPermissionsPage renderiza a página de gerenciamento de permissões
func (pc *PermissionsController) RenderPermissionsPage(c *gin.Context) {
	config := pc.service.GetConfig()

	c.HTML(http.StatusOK, "admin/permissions.html", gin.H{
		"title":      "Gerenciamento de Permissões - Rede Tradição",
		"page":       "permissions",
		"ActivePage": "permissions",
		"Config":     config,
		"User": gin.H{
			"ID":    c.Get<PERSON>nt("userID"),
			"Name":  c.GetString("userName"),
			"Email": c.GetS<PERSON>("userEmail"),
			"Role":  c.<PERSON>("userRole"),
		},
	})
}

// GetPermissionsPage renderiza a página de gerenciamento de permissões
func (pc *PermissionsController) GetPermissionsPage(c *gin.Context) {
	config := pc.service.GetConfig()

	c.HTML(http.StatusOK, "admin/permissions.html", gin.H{
		"title":      "Gerenciamento de Permissões - Rede Tradição",
		"page":       "permissions",
		"ActivePage": "permissions",
		"Config":     config,
		"User": gin.H{
			"ID":    c.GetInt("userID"),
			"Name":  c.GetString("userName"),
			"Email": c.GetString("userEmail"),
			"Role":  c.GetString("userRole"),
		},
	})
}

// GetPermissionsAPI retorna todas as permissões em formato JSON
func (pc *PermissionsController) GetPermissionsAPI(c *gin.Context) {
	config := pc.service.GetConfig()
	c.JSON(http.StatusOK, config)
}

// GetRolePermissions retorna as permissões de um papel
func (pc *PermissionsController) GetRolePermissions(c *gin.Context) {
	role := c.Param("role")

	config := pc.service.GetConfig()
	if roleConfig, exists := config.Roles[role]; exists {
		c.JSON(http.StatusOK, roleConfig)
		return
	}

	c.JSON(http.StatusNotFound, gin.H{"error": "Papel não encontrado"})
}

// UpdateRolePermissions atualiza as permissões de um papel
func (pc *PermissionsController) UpdateRolePermissions(c *gin.Context) {
	var req struct {
		Role        string `json:"role" binding:"required"`
		Permissions struct {
			Pages []string `json:"pages"`
			APIs  []string `json:"apis"`
		} `json:"permissions" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos"})
		return
	}

	if err := pc.service.UpdateRolePermissions(req.Role, req.Permissions.Pages, req.Permissions.APIs); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Permissões atualizadas com sucesso"})
}

// DeleteRole exclui um papel
func (pc *PermissionsController) DeleteRole(c *gin.Context) {
	role := c.Param("role")

	// Verificar se o papel existe
	config := pc.service.GetConfig()
	if _, exists := config.Roles[role]; !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Papel não encontrado"})
		return
	}

	// Não permitir excluir o papel de administrador
	if role == "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Não é permitido excluir o papel de administrador"})
		return
	}

	// Implementar a exclusão do papel
	// TODO: Implementar a exclusão do papel no serviço de permissões

	c.JSON(http.StatusOK, gin.H{"message": "Papel excluído com sucesso"})
}

// UpdatePublicResources atualiza os recursos públicos
func (pc *PermissionsController) UpdatePublicResources(c *gin.Context) {
	var req struct {
		PublicPages []string `json:"publicPages"`
		PublicAPIs  []string `json:"publicAPIs"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos"})
		return
	}

	// Implementar a atualização dos recursos públicos
	// TODO: Implementar a atualização dos recursos públicos no serviço de permissões

	c.JSON(http.StatusOK, gin.H{"message": "Recursos públicos atualizados com sucesso"})
}

// TestPermission testa se um papel tem permissão para acessar um recurso
func (pc *PermissionsController) TestPermission(c *gin.Context) {
	var req struct {
		Role     string `json:"role" binding:"required"`
		Resource string `json:"resource" binding:"required"`
		Type     string `json:"type" binding:"required"` // "page" ou "api"
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos"})
		return
	}

	var permType permissions.PermissionType
	if req.Type == "page" {
		permType = permissions.PagePermission
	} else if req.Type == "api" {
		permType = permissions.APIPermission
	} else {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tipo de permissão inválido"})
		return
	}

	hasPermission := pc.service.HasPermission(req.Role, req.Resource, permType)

	c.JSON(http.StatusOK, gin.H{
		"hasPermission": hasPermission,
		"role":          req.Role,
		"resource":      req.Resource,
		"type":          req.Type,
	})
}

// ReloadPermissions recarrega as permissões do arquivo de configuração
func (pc *PermissionsController) ReloadPermissions(c *gin.Context) {
	// Recarregar as permissões
	newService, err := permissions.NewService(pc.configPath)
	if err != nil {
		log.Printf("Erro ao recarregar permissões: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao recarregar permissões"})
		return
	}

	// Atualizar o serviço
	pc.service = newService

	// Atualizar o middleware global
	permissions.SetGlobalMiddleware(permissions.NewMiddleware(newService))

	c.JSON(http.StatusOK, gin.H{"message": "Permissões recarregadas com sucesso"})
}
