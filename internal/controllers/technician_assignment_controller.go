package controllers

import (
	"net/http"
	"strconv"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// TechnicianAssignmentController é o controlador para gerenciar a atribuição de técnicos a ordens
type TechnicianAssignmentController struct {
	technicianOrderService *services.TechnicianOrderService
}

// NewTechnicianAssignmentController cria uma nova instância do controlador
func NewTechnicianAssignmentController(technicianOrderService *services.TechnicianOrderService) *TechnicianAssignmentController {
	return &TechnicianAssignmentController{
		technicianOrderService: technicianOrderService,
	}
}

// AssignTechnicianToOrder atribui um técnico a uma ordem
func (c *TechnicianAssignmentController) AssignTechnicianToOrder(ctx *gin.Context) {
	var data struct {
		TechnicianID uint   `json:"technician_id" binding:"required"`
		OrderID      uint   `json:"order_id" binding:"required"`
		Notes        string `json:"notes"`
	}
	if err := ctx.ShouldBindJSON(&data); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos", "details": err.Error()})
		return
	}

	// Obter o ID do usuário que está fazendo a atribuição
	userID, exists := ctx.Get("userID")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
		return
	}
	createdByID := uint(userID.(uint64))

	// Atribuir o técnico à ordem
	if err := c.technicianOrderService.AssignOrderToTechnician(data.TechnicianID, data.OrderID, createdByID, data.Notes); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atribuir técnico", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"success": true, "message": "Técnico atribuído com sucesso"})
}

// UnassignTechnicianFromOrder remove a atribuição de um técnico a uma ordem
func (c *TechnicianAssignmentController) UnassignTechnicianFromOrder(ctx *gin.Context) {
	technicianIDStr := ctx.Param("technician_id")
	technicianID, err := strconv.ParseUint(technicianIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID do técnico inválido"})
		return
	}

	orderIDStr := ctx.Param("order_id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID da ordem inválido"})
		return
	}

	if err := c.technicianOrderService.UnassignOrderFromTechnician(uint(technicianID), uint(orderID)); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao remover atribuição", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"success": true, "message": "Atribuição removida com sucesso"})
}

// GetOrderTechnicians retorna os técnicos atribuídos a uma ordem
func (c *TechnicianAssignmentController) GetOrderTechnicians(ctx *gin.Context) {
	orderIDStr := ctx.Param("order_id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID da ordem inválido"})
		return
	}

	technicians, err := c.technicianOrderService.GetTechniciansByOrder(uint(orderID))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao obter técnicos", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, technicians)
}

// GetTechnicianOrders retorna as ordens atribuídas a um técnico
func (c *TechnicianAssignmentController) GetTechnicianOrders(ctx *gin.Context) {
	technicianIDStr := ctx.Param("technician_id")
	technicianID, err := strconv.ParseUint(technicianIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID do técnico inválido"})
		return
	}

	orders, err := c.technicianOrderService.GetOrdersByTechnician(uint(technicianID))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao obter ordens", "details": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, orders)
}
