package controllers

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// FinanceiroController estrutura para o controller financeiro
type FinanceiroController struct {
}

// NewFinanceiroController cria uma nova instância do controller financeiro
func NewFinanceiroController() *FinanceiroController {
	return &FinanceiroController{}
}

// Painel renderiza o painel financeiro
func (c *FinanceiroController) Painel(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "financeiro/painel.html", gin.H{
		"title": "Painel Financeiro",
	})
}

// Calculadora renderiza a calculadora financeira
func (c *FinanceiroController) Calculadora(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "financeiro/calculadora.html", gin.H{
		"title": "Calculadora Financeira",
	})
}
