package setup

import (
	"tradicao/internal/handlers"
	"tradicao/internal/repository"
	"tradicao/internal/routes"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Variáveis globais para armazenar instâncias dos componentes unificados
var (
	unifiedRepo    *repository.UnifiedFilialRepository
	unifiedService *services.UnifiedFilialService
	unifiedHandler *handlers.UnifiedFilialHandler
)

// SetupUnifiedComponents configura todos os componentes unificados
func SetupUnifiedComponents(router *gin.Engine, db *gorm.DB) {
	// Repositórios originais
	filialRepo := repository.NewGormFilialRepository(db)
	stationRepo := repository.NewGormStationRepository(db)
	branchRepo := repository.NewGormBranchRepository(db)

	// Repositório unificado
	unifiedRepo = repository.NewUnifiedFilialRepository(filialRepo, stationRepo, branchRepo)

	// Serviço unificado
	unifiedService = services.NewUnifiedFilialService(unifiedRepo)

	// Handler unificado
	unifiedHandler = handlers.NewUnifiedFilialHandler(unifiedService)

	// Configurar rotas
	routes.SetupUnifiedRoutes(router, unifiedHandler)
}

// GetUnifiedRepo retorna o repositório unificado
func GetUnifiedRepo() *repository.UnifiedFilialRepository {
	return unifiedRepo
}

// GetUnifiedService retorna o serviço unificado
func GetUnifiedService() *services.UnifiedFilialService {
	return unifiedService
}

// GetUnifiedHandler retorna o handler unificado
func GetUnifiedHandler() *handlers.UnifiedFilialHandler {
	return unifiedHandler
}
