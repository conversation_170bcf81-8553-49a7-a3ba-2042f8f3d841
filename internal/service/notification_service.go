package service

import (
	"tradicao/internal/models"
	"tradicao/internal/repository"
)

type NotificationService struct {
	repo repository.NotificationRepository
}

func NewNotificationService(repo repository.NotificationRepository) *NotificationService {
	return &NotificationService{repo: repo}
}

func (s *NotificationService) CreateNotification(notification *models.Notification) error {
	return s.repo.Create(notification)
}

func (s *NotificationService) GetNotifications(userID uint) ([]models.Notification, error) {
	return s.repo.GetByUserID(userID)
}

func (s *NotificationService) MarkAsRead(notificationID uint) error {
	return s.repo.MarkAsRead(notificationID)
}

func (s *NotificationService) GetUnreadCount(userID uint) (int64, error) {
	return s.repo.GetUnreadCount(userID)
}
