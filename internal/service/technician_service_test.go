package service

import (
	"testing"
	"time"
	"tradicao/internal/models"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

type mockTechnicianRepository struct {
	mock.Mock
}

func (m *mockTechnicianRepository) CreateSpecialty(specialty *models.TechnicianSpecialty) error {
	args := m.Called(specialty)
	return args.Error(0)
}

func (m *mockTechnicianRepository) ListSpecialties() ([]models.TechnicianSpecialty, error) {
	args := m.Called()
	return args.Get(0).([]models.TechnicianSpecialty), args.Error(1)
}

func (m *mockTechnicianRepository) UpdateSpecialty(specialty *models.TechnicianSpecialty) error {
	args := m.Called(specialty)
	return args.Error(0)
}

func (m *mockTechnicianRepository) DeleteSpecialty(id uint) error {
	args := m.Called(id)
	return args.Error(0)
}

func (m *mockTechnicianRepository) GetSpecialty(id uint) (*models.TechnicianSpecialty, error) {
	args := m.Called(id)
	return args.Get(0).(*models.TechnicianSpecialty), args.Error(1)
}

func (m *mockTechnicianRepository) CreateBranchAssociation(association *models.TechnicianBranch) error {
	args := m.Called(association)
	return args.Error(0)
}

func (m *mockTechnicianRepository) UpdateBranchAssociation(association *models.TechnicianBranch) error {
	args := m.Called(association)
	return args.Error(0)
}

func (m *mockTechnicianRepository) DeleteBranchAssociation(technicianID, branchID uint) error {
	args := m.Called(technicianID, branchID)
	return args.Error(0)
}

func (m *mockTechnicianRepository) GetBranchAssociation(technicianID, branchID uint) (*models.TechnicianBranch, error) {
	args := m.Called(technicianID, branchID)
	return args.Get(0).(*models.TechnicianBranch), args.Error(1)
}

func (m *mockTechnicianRepository) ListBranchAssociations(technicianID uint) ([]models.TechnicianBranch, error) {
	args := m.Called(technicianID)
	return args.Get(0).([]models.TechnicianBranch), args.Error(1)
}

func (m *mockTechnicianRepository) CreateMaintenanceHistory(history *models.MaintenanceHistory) error {
	args := m.Called(history)
	return args.Error(0)
}

func (m *mockTechnicianRepository) ListMaintenanceHistory(technicianID uint, startDate, endDate time.Time) ([]models.MaintenanceHistory, error) {
	args := m.Called(technicianID, startDate, endDate)
	return args.Get(0).([]models.MaintenanceHistory), args.Error(1)
}

func (m *mockTechnicianRepository) GetMaintenanceHistory(id uint) (*models.MaintenanceHistory, error) {
	args := m.Called(id)
	return args.Get(0).(*models.MaintenanceHistory), args.Error(1)
}

func TestTechnicianService(t *testing.T) {
	mockRepo := new(mockTechnicianRepository)
	service := NewTechnicianService(mockRepo)

	t.Run("Test CreateSpecialty", func(t *testing.T) {
		specialty := &models.TechnicianSpecialty{
			Name:        "Eletricista",
			Description: "Especialista em manutenção elétrica",
		}

		mockRepo.On("CreateSpecialty", specialty).Return(nil)
		err := service.CreateSpecialty(specialty)
		assert.NoError(t, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Test GetSpecialty", func(t *testing.T) {
		specialty := &models.TechnicianSpecialty{
			ID:          1,
			Name:        "Mecânico",
			Description: "Especialista em manutenção mecânica",
		}

		mockRepo.On("GetSpecialty", uint(1)).Return(specialty, nil)
		found, err := service.GetSpecialty(1)
		assert.NoError(t, err)
		assert.Equal(t, specialty.Name, found.Name)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Test CreateBranchAssociation", func(t *testing.T) {
		association := &models.TechnicianBranch{
			TechnicianID: 1,
			BranchID:     1,
			SpecialtyID:  1,
		}

		mockRepo.On("CreateBranchAssociation", association).Return(nil)
		err := service.CreateBranchAssociation(association)
		assert.NoError(t, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Test ListBranchAssociations", func(t *testing.T) {
		associations := []models.TechnicianBranch{
			{
				TechnicianID: 1,
				BranchID:     1,
				SpecialtyID:  1,
			},
		}

		mockRepo.On("ListBranchAssociations", uint(1)).Return(associations, nil)
		result, err := service.ListBranchAssociations(1)
		assert.NoError(t, err)
		assert.Equal(t, len(associations), len(result))
		mockRepo.AssertExpectations(t)
	})

	t.Run("Test CreateMaintenanceHistory", func(t *testing.T) {
		history := &models.MaintenanceHistory{
			TechnicianID: 1,
			OrderID:      1,
			EquipmentID:  1,
			Description:  "Manutenção preventiva",
			Status:       "Concluído",
		}

		mockRepo.On("CreateMaintenanceHistory", history).Return(nil)
		err := service.CreateMaintenanceHistory(history)
		assert.NoError(t, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Test ListMaintenanceHistory", func(t *testing.T) {
		history := []models.MaintenanceHistory{
			{
				ID:           1,
				TechnicianID: 1,
				OrderID:      1,
				EquipmentID:  1,
				Description:  "Manutenção preventiva",
				Status:       "Concluído",
			},
		}

		startDate := time.Now().AddDate(0, -1, 0)
		endDate := time.Now()

		mockRepo.On("ListMaintenanceHistory", uint(1), startDate, endDate).Return(history, nil)
		result, err := service.ListMaintenanceHistory(1, startDate, endDate)
		assert.NoError(t, err)
		assert.Equal(t, len(history), len(result))
		mockRepo.AssertExpectations(t)
	})
}
