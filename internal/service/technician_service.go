package service

import (
	"time"
	"tradicao/internal/models"
	"tradicao/internal/repository"
)

// TechnicianService define a interface para operações relacionadas a técnicos
type TechnicianService interface {
	CreateSpecialty(specialty *models.TechnicianSpecialty) error
	ListSpecialties() ([]models.TechnicianSpecialty, error)
	UpdateSpecialty(specialty *models.TechnicianSpecialty) error
	DeleteSpecialty(id uint) error
	GetSpecialty(id uint) (*models.TechnicianSpecialty, error)
	CreateBranchAssociation(association *models.TechnicianBranch) error
	UpdateBranchAssociation(association *models.TechnicianBranch) error
	DeleteBranchAssociation(technicianID, branchID uint) error
	GetBranchAssociation(technicianID, branchID uint) (*models.TechnicianBranch, error)
	ListBranchAssociations(technicianID uint) ([]models.TechnicianBranch, error)
	CreateMaintenanceHistory(history *models.MaintenanceHistory) error
	ListMaintenanceHistory(technicianID uint, startDate, endDate time.Time) ([]models.MaintenanceHistory, error)
	GetMaintenanceHistory(id uint) (*models.MaintenanceHistory, error)
}

// technicianService implementa TechnicianService
type technicianService struct {
	repo repository.TechnicianRepositoryInterface
}

// NewTechnicianService cria uma nova instância do serviço
func NewTechnicianService(repo repository.TechnicianRepositoryInterface) TechnicianService {
	return &technicianService{repo: repo}
}

// Implementação dos métodos da interface

func (s *technicianService) CreateSpecialty(specialty *models.TechnicianSpecialty) error {
	return s.repo.CreateSpecialty(specialty)
}

func (s *technicianService) ListSpecialties() ([]models.TechnicianSpecialty, error) {
	return s.repo.ListSpecialties()
}

func (s *technicianService) UpdateSpecialty(specialty *models.TechnicianSpecialty) error {
	return s.repo.UpdateSpecialty(specialty)
}

func (s *technicianService) DeleteSpecialty(id uint) error {
	return s.repo.DeleteSpecialty(id)
}

func (s *technicianService) GetSpecialty(id uint) (*models.TechnicianSpecialty, error) {
	return s.repo.GetSpecialty(id)
}

func (s *technicianService) CreateBranchAssociation(association *models.TechnicianBranch) error {
	return s.repo.CreateBranchAssociation(association)
}

func (s *technicianService) UpdateBranchAssociation(association *models.TechnicianBranch) error {
	return s.repo.UpdateBranchAssociation(association)
}

func (s *technicianService) DeleteBranchAssociation(technicianID, branchID uint) error {
	return s.repo.DeleteBranchAssociation(technicianID, branchID)
}

func (s *technicianService) GetBranchAssociation(technicianID, branchID uint) (*models.TechnicianBranch, error) {
	return s.repo.GetBranchAssociation(technicianID, branchID)
}

func (s *technicianService) ListBranchAssociations(technicianID uint) ([]models.TechnicianBranch, error) {
	return s.repo.ListBranchAssociations(technicianID)
}

func (s *technicianService) CreateMaintenanceHistory(history *models.MaintenanceHistory) error {
	return s.repo.CreateMaintenanceHistory(history)
}

func (s *technicianService) ListMaintenanceHistory(technicianID uint, startDate, endDate time.Time) ([]models.MaintenanceHistory, error) {
	return s.repo.ListMaintenanceHistory(technicianID, startDate, endDate)
}

func (s *technicianService) GetMaintenanceHistory(id uint) (*models.MaintenanceHistory, error) {
	return s.repo.GetMaintenanceHistory(id)
}
