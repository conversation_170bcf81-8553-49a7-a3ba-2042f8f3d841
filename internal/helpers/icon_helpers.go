package helpers

import (
	"html/template"
)

// StatusToIcon retorna o nome da classe de ícone Boxicons para um determinado status
func StatusToIcon(status string) string {
	switch status {
	case "pendente":
		return "bx-time-five"
	case "em_andamento":
		return "bx-loader-alt"
	case "concluida":
		return "bx-check-circle"
	case "cancelada":
		return "bx-x-circle"
	default:
		return "bx-help-circle"
	}
}

// PriorityToIcon retorna o nome da classe de ícone Boxicons para uma determinada prioridade
func PriorityToIcon(priority string) string {
	switch priority {
	case "baixa":
		return "bx-signal-1"
	case "media":
		return "bx-signal-2"
	case "alta":
		return "bx-signal-3"
	case "urgente":
		return "bx-signal-4"
	default:
		return "bx-signal-1"
	}
}

// RegisterIconHelpers registra as funções auxiliares de ícones no template
func RegisterIconHelpers(funcMap template.FuncMap) template.FuncMap {
	funcMap["statusToIcon"] = StatusToIcon
	funcMap["priorityToIcon"] = PriorityToIcon
	return funcMap
}
