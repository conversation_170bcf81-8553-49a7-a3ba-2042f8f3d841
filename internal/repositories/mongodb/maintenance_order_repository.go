package mongodb

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"tradicao/internal/models"
)

type maintenanceOrderRepository struct {
	db *mongo.Database
}

// NewMaintenanceOrderRepository cria uma nova instância do repositório
func NewMaintenanceOrderRepository(db *mongo.Database) *maintenanceOrderRepository {
	return &maintenanceOrderRepository{
		db: db,
	}
}

// GetByID retorna uma ordem de manutenção pelo ID
func (r *maintenanceOrderRepository) GetByID(ctx context.Context, id string) (*models.MaintenanceOrder, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}

	var order models.MaintenanceOrder
	err = r.db.Collection("maintenance_orders").FindOne(ctx, bson.M{"_id": objectID}).Decode(&order)
	if err != nil {
		return nil, err
	}

	return &order, nil
}

// GetByBranch retorna todas as ordens de manutenção de uma filial
func (r *maintenanceOrderRepository) GetByBranch(ctx context.Context, branchID string) ([]*models.MaintenanceOrder, error) {
	cursor, err := r.db.Collection("maintenance_orders").Find(ctx, bson.M{"branch_id": branchID})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var orders []*models.MaintenanceOrder
	if err = cursor.All(ctx, &orders); err != nil {
		return nil, err
	}

	return orders, nil
}

// Create cria uma nova ordem de manutenção
func (r *maintenanceOrderRepository) Create(ctx context.Context, order *models.MaintenanceOrder) error {
	order.CreatedAt = time.Now()
	order.UpdatedAt = time.Now()

	result, err := r.db.Collection("maintenance_orders").InsertOne(ctx, order)
	if err != nil {
		return err
	}

	order.ID = result.InsertedID.(primitive.ObjectID)
	return nil
}

// Update atualiza uma ordem de manutenção existente
func (r *maintenanceOrderRepository) Update(ctx context.Context, order *models.MaintenanceOrder) error {
	order.UpdatedAt = time.Now()

	_, err := r.db.Collection("maintenance_orders").UpdateOne(
		ctx,
		bson.M{"_id": order.ID},
		bson.M{"$set": order},
	)
	return err
}

// Delete remove uma ordem de manutenção
func (r *maintenanceOrderRepository) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	_, err = r.db.Collection("maintenance_orders").DeleteOne(ctx, bson.M{"_id": objectID})
	return err
}

// AddMaterial adiciona um material à ordem de manutenção
func (r *maintenanceOrderRepository) AddMaterial(ctx context.Context, material *models.MaintenanceMaterial) error {
	// Criar uma versão MongoDB do material
	materialMongo := &models.MaintenanceMaterialMongoDB{
		OrderID:     primitive.ObjectID{}, // Converter para ObjectID
		Name:        material.Name,
		Description: material.Description,
		Quantity:    material.Quantity,
		UnitPrice:   material.UnitPrice,
		TotalPrice:  material.TotalPrice,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	_, err := r.db.Collection("maintenance_materials").InsertOne(ctx, materialMongo)
	if err != nil {
		return err
	}

	// Não podemos converter ObjectID para uint diretamente
	// Apenas registramos que a operação foi bem-sucedida

	return nil
}

// UpdateMaterial atualiza um material existente
func (r *maintenanceOrderRepository) UpdateMaterial(ctx context.Context, material *models.MaintenanceMaterial) error {
	material.UpdatedAt = time.Now()

	_, err := r.db.Collection("maintenance_materials").UpdateOne(
		ctx,
		bson.M{"_id": material.ID},
		bson.M{"$set": material},
	)
	return err
}

// DeleteMaterial remove um material
func (r *maintenanceOrderRepository) DeleteMaterial(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	_, err = r.db.Collection("maintenance_materials").DeleteOne(ctx, bson.M{"_id": objectID})
	return err
}

// GetMaterialsByOrder retorna todos os materiais de uma ordem
func (r *maintenanceOrderRepository) GetMaterialsByOrder(ctx context.Context, orderID string) ([]*models.MaintenanceMaterial, error) {
	cursor, err := r.db.Collection("maintenance_materials").Find(ctx, bson.M{"order_id": orderID})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var materials []*models.MaintenanceMaterial
	if err = cursor.All(ctx, &materials); err != nil {
		return nil, err
	}

	return materials, nil
}

// AddNote adiciona uma nota à ordem de manutenção
func (r *maintenanceOrderRepository) AddNote(ctx context.Context, note *models.MaintenanceNote) error {
	// Criar uma versão MongoDB da nota
	noteMongo := &models.MaintenanceNoteMongoDB{
		OrderID:   primitive.ObjectID{}, // Converter para ObjectID
		Content:   note.Content,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	_, err := r.db.Collection("maintenance_notes").InsertOne(ctx, noteMongo)
	if err != nil {
		return err
	}

	// Não podemos converter ObjectID para uint diretamente
	// Apenas registramos que a operação foi bem-sucedida

	return nil
}

// UpdateNote atualiza uma nota existente
func (r *maintenanceOrderRepository) UpdateNote(ctx context.Context, note *models.MaintenanceNote) error {
	note.UpdatedAt = time.Now()

	_, err := r.db.Collection("maintenance_notes").UpdateOne(
		ctx,
		bson.M{"_id": note.ID},
		bson.M{"$set": note},
	)
	return err
}

// DeleteNote remove uma nota
func (r *maintenanceOrderRepository) DeleteNote(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	_, err = r.db.Collection("maintenance_notes").DeleteOne(ctx, bson.M{"_id": objectID})
	return err
}

// GetNotesByOrder retorna todas as notas de uma ordem
func (r *maintenanceOrderRepository) GetNotesByOrder(ctx context.Context, orderID string) ([]*models.MaintenanceNote, error) {
	cursor, err := r.db.Collection("maintenance_notes").Find(ctx, bson.M{"order_id": orderID})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var notes []*models.MaintenanceNote
	if err = cursor.All(ctx, &notes); err != nil {
		return nil, err
	}

	return notes, nil
}

// UpdateStatus atualiza o status de uma ordem
func (r *maintenanceOrderRepository) UpdateStatus(ctx context.Context, id string, status string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	_, err = r.db.Collection("maintenance_orders").UpdateOne(
		ctx,
		bson.M{"_id": objectID},
		bson.M{"$set": bson.M{
			"status":     status,
			"updated_at": time.Now(),
		}},
	)
	return err
}

// UpdateCost atualiza o custo de uma ordem
func (r *maintenanceOrderRepository) UpdateCost(ctx context.Context, id string, cost float64) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	_, err = r.db.Collection("maintenance_orders").UpdateOne(
		ctx,
		bson.M{"_id": objectID},
		bson.M{"$set": bson.M{
			"cost":       cost,
			"updated_at": time.Now(),
		}},
	)
	return err
}

// GetByStatus retorna todas as ordens com um determinado status
func (r *maintenanceOrderRepository) GetByStatus(ctx context.Context, status string) ([]*models.MaintenanceOrder, error) {
	cursor, err := r.db.Collection("maintenance_orders").Find(ctx, bson.M{"status": status})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var orders []*models.MaintenanceOrder
	if err = cursor.All(ctx, &orders); err != nil {
		return nil, err
	}

	return orders, nil
}

// GetByDateRange retorna todas as ordens dentro de um período
func (r *maintenanceOrderRepository) GetByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*models.MaintenanceOrder, error) {
	cursor, err := r.db.Collection("maintenance_orders").Find(
		ctx,
		bson.M{
			"created_at": bson.M{
				"$gte": startDate,
				"$lte": endDate,
			},
		},
	)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var orders []*models.MaintenanceOrder
	if err = cursor.All(ctx, &orders); err != nil {
		return nil, err
	}

	return orders, nil
}

// GetMetrics retorna as métricas das ordens de manutenção
func (r *maintenanceOrderRepository) GetMetrics(ctx context.Context) (*models.MaintenanceMetrics, error) {
	metrics := &models.MaintenanceMetrics{}

	// Total de ordens
	totalCount, err := r.db.Collection("maintenance_orders").CountDocuments(ctx, bson.M{})
	if err != nil {
		return nil, err
	}
	metrics.TotalOrders = int(totalCount)

	// Ordens por status
	pipeline := []bson.M{
		{
			"$group": bson.M{
				"_id": "$status",
				"count": bson.M{
					"$sum": 1,
				},
			},
		},
	}

	cursor, err := r.db.Collection("maintenance_orders").Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	type StatusCount struct {
		Status string `bson:"_id"`
		Count  int64  `bson:"count"`
	}

	var results []StatusCount
	if err = cursor.All(ctx, &results); err != nil {
		return nil, err
	}

	// Inicializar o mapa se não existir
	if metrics.OrdersByStatus == nil {
		metrics.OrdersByStatus = make(map[string]int)
	}

	// Preencher o mapa com os resultados
	for _, result := range results {
		metrics.OrdersByStatus[result.Status] = int(result.Count)
	}

	// Custo total e médio
	costPipeline := []bson.M{
		{
			"$group": bson.M{
				"_id": nil,
				"total_cost": bson.M{
					"$sum": "$cost",
				},
				"avg_cost": bson.M{
					"$avg": "$cost",
				},
			},
		},
	}

	cursor, err = r.db.Collection("maintenance_orders").Aggregate(ctx, costPipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	type CostMetrics struct {
		TotalCost float64 `bson:"total_cost"`
		AvgCost   float64 `bson:"avg_cost"`
	}

	var costResults []CostMetrics
	if err = cursor.All(ctx, &costResults); err != nil {
		return nil, err
	}

	if len(costResults) > 0 {
		metrics.TotalCost = costResults[0].TotalCost
		metrics.AverageCostPerOrder = costResults[0].AvgCost
	}

	// Tempo médio de conclusão
	completionPipeline := []bson.M{
		{
			"$match": bson.M{
				"status": models.StatusCompleted,
			},
		},
		{
			"$group": bson.M{
				"_id": nil,
				"avg_time": bson.M{
					"$avg": bson.M{
						"$subtract": []string{"$updated_at", "$created_at"},
					},
				},
			},
		},
	}

	cursor, err = r.db.Collection("maintenance_orders").Aggregate(ctx, completionPipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	type CompletionMetrics struct {
		AvgTime float64 `bson:"avg_time"`
	}

	var completionResults []CompletionMetrics
	if err = cursor.All(ctx, &completionResults); err != nil {
		return nil, err
	}

	if len(completionResults) > 0 {
		metrics.AverageCompletionTime = completionResults[0].AvgTime / (24 * 60 * 60) // Converter para dias
	}

	return metrics, nil
}

// GetOverdueOrders retorna todas as ordens atrasadas
func (r *maintenanceOrderRepository) GetOverdueOrders(ctx context.Context) ([]*models.MaintenanceOrder, error) {
	now := time.Now()
	cursor, err := r.db.Collection("maintenance_orders").Find(
		ctx,
		bson.M{
			"due_date": bson.M{
				"$lt": now,
			},
			"status": bson.M{
				"$nin": bson.A{string(models.StatusCompleted), string(models.StatusCanceled)},
			},
		},
		options.Find().SetSort(bson.D{{Key: "due_date", Value: 1}}),
	)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var orders []*models.MaintenanceOrder
	if err = cursor.All(ctx, &orders); err != nil {
		return nil, err
	}

	return orders, nil
}
