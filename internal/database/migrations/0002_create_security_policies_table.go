package migrations

import (
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// CreateSecurityPoliciesTable cria a tabela security_policies se ela não existir
func CreateSecurityPoliciesTable(db *gorm.DB) error {
	// Usar AutoMigrate para criar a tabela com base no modelo
	if err := db.AutoMigrate(&models.SecurityPolicy{}); err != nil {
		return err
	}

	// Verificar se já existe pelo menos um registro na tabela
	var count int64
	db.Model(&models.SecurityPolicy{}).Count(&count)

	// Se não existir nenhum registro, criar um com valores padrão
	if count == 0 {
		defaultPolicy := models.SecurityPolicy{
			ID:                         1,
			Name:                       "Política Padrão",
			PasswordMinLength:          8,
			PasswordRequireUppercase:   true,
			PasswordRequireNumber:      true,
			PasswordRequireSpecialChar: true,
			PasswordExpiryDays:         90,
			MaxLoginAttempts:           5,
			LockoutDurationMinutes:     30,
			Enable2FA:                  false,
			SessionTimeoutMinutes:      60,
		}

		return db.Create(&defaultPolicy).Error
	}

	return nil
}
