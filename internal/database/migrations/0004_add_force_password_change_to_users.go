package migrations

import (
	"gorm.io/gorm"
)

// AddForcePasswordChangeToUsers adiciona a coluna force_password_change à tabela users
func AddForcePasswordChangeToUsers(db *gorm.DB) error {
	// Verificar se a coluna já existe
	var count int64
	db.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'force_password_change'").Count(&count)

	// Se a coluna não existir, adicioná-la
	if count == 0 {
		return db.Exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS force_password_change BOOLEAN DEFAULT FALSE").Error
	}

	return nil
}
