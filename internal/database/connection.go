package database

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	_ "github.com/lib/pq"
)

var db *sql.DB

func InitDB() (*sql.DB, error) {
	// Verificar se estamos tentando usar um banco de dados local
	host := os.Getenv("DB_HOST")
	if host == "localhost" || host == "127.0.0.1" {
		log.Fatal("ERRO: Uso de banco de dados local é EXPRESSAMENTE PROIBIDO! Use apenas o banco de dados remoto configurado no .env")
	}

	// Usar preferencialmente a string de conexão completa do DATABASE_URL
	var connStr string
	if dbURL := os.Getenv("DATABASE_URL"); dbURL != "" {
		connStr = dbURL
	} else {
		// Se não tiver DATABASE_URL, construir a partir das variáveis individuais
		port := os.Getenv("DB_PORT")
		user := os.Getenv("DB_USER")
		password := os.Getenv("DB_PASS")
		dbname := os.Getenv("DB_NAME")

		if host == "" || port == "" || user == "" || password == "" || dbname == "" {
			log.Fatal("ERRO: Variáveis de ambiente obrigatórias não encontradas! Configure: DB_HOST, DB_PORT, DB_USER, DB_PASS, DB_NAME")
		}

		connStr = fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
			host, port, user, password, dbname)
	}

	var err error
	db, err = sql.Open("postgres", connStr)
	if err != nil {
		return nil, fmt.Errorf("erro ao abrir conexão: %v", err)
	}

	err = db.Ping()
	if err != nil {
		return nil, fmt.Errorf("erro ao conectar: %v", err)
	}

	log.Println("Conexão com banco de dados estabelecida com sucesso!")
	return db, nil
}

func GetDB() *sql.DB {
	return db
}
