package database

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"database/sql"
)

// RunMigrations executa todas as migrações SQL encontradas na pasta de migrações
func RunMigrations(db *sql.DB) error {
	migrationFiles := []string{
		"migrations/004_create_complete_schemas.sql",
		"migrations/005_create_users.sql",
	}

	for _, file := range migrationFiles {
		log.Printf("Executando migração: %s", file)

		content, err := os.ReadFile(file)
		if err != nil {
			return fmt.Errorf("erro ao ler arquivo de migração %s: %v", file, err)
		}

		// Divide o conteúdo em consultas separadas (cada consulta termina com ;)
		queries := strings.Split(string(content), ";")

		for _, query := range queries {
			query = strings.TrimSpace(query)
			if query == "" {
				continue
			}

			// Ignora linhas de comentário que começam com "--"
			if strings.HasPrefix(query, "--") {
				continue
			}

			// Executa a consulta em uma transação
			tx, err := db.Begin()
			if err != nil {
				return fmt.Errorf("erro ao iniciar transação: %v", err)
			}

			_, err = tx.Exec(query)
			if err != nil {
				tx.Rollback()
				// Registro mais detalhado do erro
				log.Printf("ERRO SQL: %v\nQuery: %s", err, query)

				// Continua com a próxima consulta em vez de parar completamente
				// apenas em caso de tabela já existente ou outros erros não fatais
				if strings.Contains(err.Error(), "already exists") || strings.Contains(err.Error(), "duplicate key") {
					log.Printf("Aviso: Ignorando erro não fatal (objeto já existe)")
					continue
				}

				return fmt.Errorf("erro ao executar consulta de migração: %v\nConsulta: %s", err, query)
			}

			if err := tx.Commit(); err != nil {
				return fmt.Errorf("erro ao confirmar transação: %v", err)
			}
		}

		log.Printf("Migração %s executada com sucesso", filepath.Base(file))
	}

	return nil
}
