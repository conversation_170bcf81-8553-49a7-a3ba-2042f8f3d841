package database

import (
	"database/sql"
	"fmt"
	"os"
	"time"

	_ "github.com/lib/pq"

	"tradicao/internal/config"
)

// NewPostgreSQLConnection estabelece uma nova conexão com o banco de dados PostgreSQL
func NewPostgreSQLConnection(cfg *config.Config) (*sql.DB, error) {
	// Verificar se estamos tentando usar um banco de dados local
	if cfg.DBHost == "localhost" || cfg.DBHost == "127.0.0.1" {
		return nil, fmt.Errorf("ERRO: Uso de banco de dados local é EXPRESSAMENTE PROIBIDO! Use apenas o banco de dados remoto configurado no .env")
	}

	// Tenta usar a URL de conexão diretamente se disponível
	dbURL := os.Getenv("DATABASE_URL")

	var db *sql.DB
	var err error

	if dbURL != "" {
		// Usa a URL do banco de dados se disponível
		fmt.Println("Conectando ao PostgreSQL usando DATABASE_URL")
		db, err = sql.Open("postgres", dbURL)
	} else {
		// Usa configurações individuais
		connStr := fmt.Sprintf(
			"host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
			cfg.DBHost,
			cfg.DBPort,
			cfg.DBUser,
			cfg.DBPassword,
			cfg.DBName,
		)
		fmt.Println("Conectando ao PostgreSQL usando configurações individuais")
		db, err = sql.Open("postgres", connStr)
	}

	if err != nil {
		return nil, fmt.Errorf("erro ao abrir conexão com o banco: %v", err)
	}

	// Configura o pool de conexões
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(10)
	db.SetConnMaxLifetime(5 * time.Minute)

	// Verifica se a conexão está funcionando
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("erro ao pingar o banco de dados: %v", err)
	}

	fmt.Println("Conexão com banco de dados PostgreSQL estabelecida")
	return db, nil
}
