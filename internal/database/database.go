package database

import (
	"fmt"
	"log"
	"os"
	"sync"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	DB     *gorm.DB
	gormDB *gorm.DB
	once   sync.Once
	mutex  sync.Mutex
	testDB interface{} // Interface para mock em testes
)

// Initialize conecta ao banco de dados e retorna a conexão e um erro, se houver.
// Esta função é usada para inicializar a conexão global DB.
func Initialize() (*gorm.DB, error) {
	mutex.Lock()
	defer mutex.Unlock()

	// Se já temos uma conexão estabelecida, retorna ela
	if DB != nil {
		// Verifica se a conexão ainda está ativa
		sqlDB, err := DB.DB()
		if err == nil {
			if err = sqlDB.Ping(); err == nil {
				return DB, nil // Conexão existente está ativa
			}
		}
		// Se chegou aqui, a conexão existente não está ativa
	}

	// Verificar se estamos tentando usar um banco de dados local
	host := os.Getenv("DB_HOST")
	if host == "localhost" || host == "127.0.0.1" {
		log.Fatal("ERRO: Uso de banco de dados local é EXPRESSAMENTE PROIBIDO! Use apenas o banco de dados remoto configurado no .env")
	}

	// Usar preferencialmente a string de conexão completa do DATABASE_URL
	var dsn string
	if dbURL := os.Getenv("DATABASE_URL"); dbURL != "" {
		dsn = dbURL
	} else {
		// Se não tiver DATABASE_URL, construir a partir das variáveis individuais
		dsn = fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable",
			os.Getenv("DB_HOST"),
			os.Getenv("DB_USER"),
			os.Getenv("DB_PASS"),
			os.Getenv("DB_NAME"),
			os.Getenv("DB_PORT"),
		)
	}

	// Configuração otimizada do GORM
	config := &gorm.Config{
		PrepareStmt: true,                                  // Prepara e armazena statements SQL para reutilização
		Logger:      logger.Default.LogMode(logger.Silent), // Reduz logs para melhorar performance
	}

	var err error
	DB, err = gorm.Open(postgres.Open(dsn), config)
	if err != nil {
		return nil, err
	}

	// Sincroniza a variável gormDB com DB para manter compatibilidade
	gormDB = DB

	// Otimizar o pool de conexões
	sqlDB, err := DB.DB()
	if err != nil {
		return nil, err
	}

	// Configurações otimizadas para manter conexões persistentes
	sqlDB.SetMaxIdleConns(10)                // Manter até 10 conexões inativas no pool
	sqlDB.SetMaxOpenConns(100)               // Máximo de 100 conexões abertas simultaneamente
	sqlDB.SetConnMaxLifetime(time.Hour * 24) // Conexões podem ser reutilizadas por até 24 horas
	sqlDB.SetConnMaxIdleTime(time.Hour * 1)  // Conexões inativas por mais de 1 hora são fechadas

	log.Println("Conexão com banco de dados PostgreSQL via GORM estabelecida com sucesso (modo otimizado)")
	return DB, nil
}

// Connect é um alias para Initialize para manter compatibilidade com o código existente
func Connect() (*gorm.DB, error) {
	return Initialize()
}

// GetGormDB retorna a instância atual do GORM DB
// Esta função é usada pelos repositórios para obter a conexão global
func GetGormDB() *gorm.DB {
	// Se estamos em modo de teste, retorna o mock
	if testDB != nil {
		// Verificar se é um *gorm.DB
		if db, ok := testDB.(*gorm.DB); ok {
			return db
		}
		// Se não for um *gorm.DB, retorna nil (para testes)
		return nil
	}

	// Inicializa a conexão apenas uma vez usando sync.Once
	once.Do(func() {
		if gormDB == nil {
			db, err := Initialize()
			if err != nil {
				log.Printf("Erro ao inicializar GORM: %v", err)
				return
			}
			gormDB = db
		}
	})

	return gormDB
}

// SetTestDB configura um banco de dados mock para testes
func SetTestDB(db interface{}) {
	testDB = db
}

// ResetTestDB limpa o banco de dados mock
func ResetTestDB() {
	testDB = nil
}
