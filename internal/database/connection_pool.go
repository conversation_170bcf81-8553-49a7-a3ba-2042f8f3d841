package database

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"sync"
	"time"

	_ "github.com/lib/pq"
)

var (
	// Singleton para o pool de conexões
	sqlDB     *sql.DB
	poolOnce  sync.Once
	poolMutex sync.Mutex
)

// InitConnectionPool inicializa o pool de conexões com o banco de dados
// Esta função é chamada apenas uma vez durante a inicialização da aplicação
func InitConnectionPool() (*sql.DB, error) {
	var err error

	poolOnce.Do(func() {
		poolMutex.Lock()
		defer poolMutex.Unlock()

		// Tentamos usar a conexão PostgreSQL existente no sistema
		// Assumimos que o serviço PostgreSQL já está em execução
		log.Println("Tentando usar conexão PostgreSQL existente no sistema...")

		// Verificar se já existe uma variável de ambiente com a conexão persistente
		if os.Getenv("DB_PERSISTENT_CONNECTION") == "true" {
			// Usar configurações padrão para conexão existente
			host := "postgres-ag-br1-03.conteige.cloud"
			port := "54243"
			user := "fcobdj_tradicao"
			password := "67573962"
			dbname := "fcobdj_tradicao"

			// Construir string de conexão para o serviço existente
			connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable connect_timeout=3",
				host, port, user, password, dbname)

			// Tentar conectar ao serviço existente com timeout reduzido
			tempDB, tempErr := sql.Open("postgres", connStr)
			if tempErr == nil {
				// Configurar timeout para o ping
				tempDB.SetConnMaxIdleTime(time.Second * 3)

				// Verificar se a conexão está funcionando
				if tempErr = tempDB.Ping(); tempErr == nil {
					// Conexão bem-sucedida, usar esta conexão
					sqlDB = tempDB

					// Configurar o pool de conexões para otimização
					sqlDB.SetMaxOpenConns(100)               // Máximo de 100 conexões abertas simultaneamente
					sqlDB.SetMaxIdleConns(20)                // Manter até 20 conexões inativas no pool
					sqlDB.SetConnMaxLifetime(time.Hour * 24) // Conexões podem ser reutilizadas por até 24 horas
					sqlDB.SetConnMaxIdleTime(time.Hour * 1)  // Conexões inativas por mais de 1 hora são fechadas

					log.Println("Conexão PostgreSQL existente detectada e reutilizada com sucesso")
					return
				}
				tempDB.Close() // Fechar conexão temporária se não funcionou
			}
			log.Printf("Não foi possível usar conexão existente: %v", tempErr)
		} else {
			// Definir a variável de ambiente para futuras execuções
			os.Setenv("DB_PERSISTENT_CONNECTION", "true")
			log.Println("Primeira execução, configurando conexão persistente para próximas inicializações")
		}

		// Se já temos uma conexão estabelecida, verificamos se está ativa
		if sqlDB != nil {
			if err = sqlDB.Ping(); err == nil {
				log.Println("Reutilizando pool de conexões existente")
				return // Pool existente está ativo
			}
			log.Printf("Pool de conexões existente não está ativo: %v", err)
		}

		// Verificar se estamos tentando usar um banco de dados local
		host2 := os.Getenv("DB_HOST")
		if host2 == "localhost" || host2 == "127.0.0.1" {
			log.Fatal("ERRO: Uso de banco de dados local é EXPRESSAMENTE PROIBIDO! Use apenas o banco de dados remoto configurado no .env")
		}

		// Usar preferencialmente a string de conexão completa do DATABASE_URL
		var connStr2 string
		if dbURL := os.Getenv("DATABASE_URL"); dbURL != "" {
			connStr2 = dbURL
		} else {
			// Se não tiver DATABASE_URL, construir a partir das variáveis individuais
			port2 := os.Getenv("DB_PORT")
			user2 := os.Getenv("DB_USER")
			password2 := os.Getenv("DB_PASS")
			dbname2 := os.Getenv("DB_NAME")

			// Usar valores padrão se as variáveis de ambiente não estiverem definidas
			if host2 == "" || port2 == "" || user2 == "" || password2 == "" || dbname2 == "" {
				log.Println("Variáveis de ambiente não encontradas, usando configuração padrão")
				host2 = "postgres-ag-br1-03.conteige.cloud"
				port2 = "54243"
				user2 = "fcobdj_tradicao"
				password2 = "67573962"
				dbname2 = "fcobdj_tradicao"
			}

			// Construir string de conexão
			connStr2 = fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
				host2, port2, user2, password2, dbname2)
		}

		// Abrir conexão com o banco de dados
		sqlDB, err = sql.Open("postgres", connStr2)
		if err != nil {
			log.Printf("Erro ao abrir conexão com o banco de dados: %v", err)
			return
		}

		// Configurar o pool de conexões para otimização
		sqlDB.SetMaxOpenConns(100)               // Máximo de 100 conexões abertas simultaneamente
		sqlDB.SetMaxIdleConns(20)                // Manter até 20 conexões inativas no pool
		sqlDB.SetConnMaxLifetime(time.Hour * 24) // Conexões podem ser reutilizadas por até 24 horas
		sqlDB.SetConnMaxIdleTime(time.Hour * 1)  // Conexões inativas por mais de 1 hora são fechadas

		// Verificar se a conexão está funcionando
		if err = sqlDB.Ping(); err != nil {
			log.Printf("Erro ao pingar o banco de dados: %v", err)
			return
		}

		log.Println("Pool de conexões PostgreSQL inicializado com sucesso (modo otimizado)")
	})

	if err != nil {
		return nil, err
	}

	return sqlDB, nil
}

// GetConnectionPool retorna o pool de conexões existente ou inicializa um novo
func GetConnectionPool() (*sql.DB, error) {
	if sqlDB != nil {
		// Verificar se a conexão ainda está ativa
		if err := sqlDB.Ping(); err == nil {
			return sqlDB, nil
		}
		// Se a conexão não estiver ativa, inicializar novamente
	}

	return InitConnectionPool()
}
