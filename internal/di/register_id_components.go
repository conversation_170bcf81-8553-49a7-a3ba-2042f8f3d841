package di

import (
	"database/sql"
	"tradicao/internal/handlers"
	"tradicao/internal/models"
	"tradicao/internal/repository"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"
	"gorm.io/gorm"
)

// RegisterIDComponents registra os componentes relacionados à nova estratégia de IDs
// no container de dependências
func RegisterIDComponents(
	router *gin.Engine,
	gormDB *gorm.DB,
	mongoDB *mongo.Database,
	sqlDB *sql.DB,
) {
	// Registrar repositório genérico para MaintenanceOrder
	orderRepo := repository.NewMaintenanceOrderGenericRepository(
		gormDB,
		sqlDB,
		"maintenance_orders",
	)

	// Registrar serviço genérico para MaintenanceOrder
	orderService := services.NewMaintenanceOrderGenericService(orderRepo)

	// Registrar handler genérico para MaintenanceOrder
	orderHandler := handlers.NewMaintenanceOrderGenericHandler(orderService)

	// Registrar rotas para o handler genérico
	apiGroup := router.Group("/api/orders-generic")
	{
		apiGroup.GET("", orderHandler.ListOrders)
		apiGroup.POST("", orderHandler.CreateOrder)
		apiGroup.GET("/:id", orderHandler.GetOrder)
		apiGroup.PUT("/:id", orderHandler.UpdateOrder)
		apiGroup.DELETE("/:id", orderHandler.DeleteOrder)
	}

	// Registrar rotas para páginas HTML
	router.GET("/orders-generic/:id", orderHandler.ShowOrderDetail)
}

// ExampleUsage mostra como usar IDs numéricos em código
func ExampleUsage() {
	// Criar um ID numérico
	id1 := uint(123)

	// Usar em um modelo
	order := &models.MaintenanceOrderAdapted{
		ID:      id1,
		Number:  "ORD-123",
		Problem: "Descrição do problema",
	}

	// Acessar o ID
	_ = order.ID
}
