package models

import (
	"time"

	"gorm.io/gorm"
)

// Filial representa uma filial no sistema
type Filial struct {
	ID              uint           `gorm:"primaryKey" json:"id"`
	Name            string         `gorm:"size:100;not null" json:"name"`
	Code            string         `gorm:"size:20;unique;not null" json:"code"`
	Address         string         `gorm:"size:255" json:"address"`
	City            string         `gorm:"size:100" json:"city"`
	State           string         `gorm:"size:2" json:"state"`
	ZipCode         string         `gorm:"size:20" json:"zip_code"`
	Phone           string         `gorm:"size:20" json:"phone"`
	Email           string         `gorm:"size:100" json:"email"`
	IsActive        bool           `gorm:"default:true" json:"is_active"`
	FilialType      string         `gorm:"column:station_type;size:20;default:'urbano'" json:"filial_type"` // urbano, rodovia, rural, industrial
	ResponsibleName string         `gorm:"size:100" json:"responsible_name"`
	Notes           string         `gorm:"size:500" json:"notes"`
	BranchID        *uint          `gorm:"default:null" json:"branch_id"` // Para compatibilidade com código legado
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `gorm:"index" json:"-"`

	// Campos adicionais para compatibilidade com o StationRepository
	Type         string   `json:"type"`
	PostalCode   string   `json:"postal_code"`
	PhoneNumber  string   `json:"phone_number"`
	Latitude     *float64 `json:"latitude"`
	Longitude    *float64 `json:"longitude"`
	ManagerID    *uint    `json:"manager_id"`
	ManagerName  string   `json:"manager_name"`
	OpeningHours string   `json:"opening_hours"`

	// Relações
	Branches []Branch `gorm:"many2many:branch_station_links;" json:"-"`
	// Removendo a relação com Equipment para evitar conflitos
}

// TableName especifica o nome da tabela para o modelo Filial
func (Filial) TableName() string {
	return "stations"
}

// ToSummary converte Filial para FilialSummary
// Deprecated: Use Branch.ToSummary() em vez disso
// Esta função é mantida apenas para compatibilidade com código existente.
func (f *Filial) ToSummary() FilialSummary {
	// Converter para Branch e usar o método ToSummary do Branch
	branch := FilialToBranch(f)
	if branch == nil {
		return FilialSummary{}
	}

	// Converter BranchSummary para FilialSummary
	return branch.ToFilialSummary()
}

// FilialMetrics representa métricas das filiais
type FilialMetrics struct {
	TotalFiliais    int64            `json:"total_filiais"`
	ActiveFiliais   int64            `json:"filiais_ativas"`
	FiliaisByRegion map[string]int64 `json:"filiais_por_regiao"`
}

// FromStation converte um Station para Filial
// Deprecated: Use Branch diretamente em vez de converter entre Station e Filial
// Esta função é mantida apenas para compatibilidade com código existente.
func FromStation(s *Station) *Filial {
	if s == nil {
		return nil
	}

	// Converter Station para Branch e depois Branch para Filial
	branch := StationToBranch(s)
	return BranchToFilial(branch)
}

// ToStation converte uma Filial para Station
// Deprecated: Use Branch diretamente em vez de converter entre Filial e Station
// Esta função é mantida apenas para compatibilidade com código existente.
func (f *Filial) ToStation() *Station {
	// Converter Filial para Branch e depois Branch para Station
	branch := FilialToBranch(f)
	return BranchToStation(branch)
}
