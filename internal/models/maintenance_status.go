package models

// Helpers e métodos adicionais para os tipos definidos em maintenance_enums.go

// String converte o status para uma string
func (s OrderStatus) String() string {
	return string(s)
}

// FromString converte uma string para um status
func FromString(s string) OrderStatus {
	return OrderStatus(s)
}

// String converte a prioridade para uma string
func (p PriorityLevel) String() string {
	return string(p)
}

// String converte o tipo para uma string
func (t MaintenanceType) String() string {
	return string(t)
}

// Constante adicional que pode não estar presente em maintenance_enums.go
const (
	StatusConfirmed OrderStatus     = "confirmed"
	TypeEmergency   MaintenanceType = "emergency"
)

// Valor legado para compatibilidade
const StatusConfirmada OrderStatus = "confirmada"

// NormalizeOrderStatus converte valores legados para os novos valores padronizados
func NormalizeOrderStatus(status string) OrderStatus {
	switch status {
	case string(StatusPendente):
		return StatusPending
	case string(StatusAgendada):
		return StatusScheduled
	case string(StatusEmAndamento):
		return StatusInProgress
	case string(StatusConcluida):
		return StatusCompleted
	case string(StatusCancelada):
		return StatusCancelled
	case string(StatusVerificada):
		return StatusVerified
	case string(StatusRejeitada):
		return StatusRejected
	case string(StatusAprovada):
		return StatusApproved
	case string(StatusConfirmada):
		return StatusConfirmed
	default:
		// Se já for um valor padronizado, retorna como está
		if IsStandardOrderStatus(status) {
			return OrderStatus(status)
		}
		// Valor desconhecido, retorna pendente como padrão
		return StatusPending
	}
}

// IsStandardOrderStatus verifica se o status já está no formato padronizado
func IsStandardOrderStatus(status string) bool {
	switch status {
	case string(StatusPending), string(StatusScheduled), string(StatusInProgress),
		string(StatusCompleted), string(StatusCancelled), string(StatusVerified),
		string(StatusRejected), string(StatusApproved), string(StatusConfirmed):
		return true
	default:
		return false
	}
}

// IsOrderStatus verifica se uma string é um status de ordem válido,
// considerando tanto os valores padronizados quanto os valores legados.
// Esta função deve ser usada em vez de comparações diretas com constantes de status
// para garantir compatibilidade com dados existentes.
func IsOrderStatus(status string, validStatus OrderStatus) bool {
	normalizedStatus := NormalizeOrderStatus(status)
	return normalizedStatus == validStatus
}

// IsOrderStatusInSet verifica se uma string é um dos status de ordem válidos no conjunto fornecido,
// considerando tanto os valores padronizados quanto os valores legados.
// Esta função deve ser usada em vez de múltiplas comparações diretas com constantes de status
// para garantir compatibilidade com dados existentes.
func IsOrderStatusInSet(status string, validStatuses ...OrderStatus) bool {
	normalizedStatus := NormalizeOrderStatus(status)
	for _, validStatus := range validStatuses {
		if normalizedStatus == validStatus {
			return true
		}
	}
	return false
}

// NormalizePriorityLevel converte valores legados para os novos valores padronizados
func NormalizePriorityLevel(priority string) PriorityLevel {
	switch priority {
	case string(PrioridadeBaixa):
		return PriorityLow
	case string(PrioridadeMedia):
		return PriorityMedium
	case string(PrioridadeAlta):
		return PriorityHigh
	case string(PrioridadeCritica):
		return PriorityCritical
	default:
		// Se já for um valor padronizado, retorna como está
		if IsStandardPriorityLevel(priority) {
			return PriorityLevel(priority)
		}
		// Valor desconhecido, retorna média como padrão
		return PriorityMedium
	}
}

// IsStandardPriorityLevel verifica se a prioridade já está no formato padronizado
func IsStandardPriorityLevel(priority string) bool {
	switch priority {
	case string(PriorityLow), string(PriorityMedium), string(PriorityHigh), string(PriorityCritical):
		return true
	default:
		return false
	}
}

// NormalizeMaintenanceType converte valores legados para os novos valores padronizados
func NormalizeMaintenanceType(maintenanceType string) MaintenanceType {
	switch maintenanceType {
	case string(TipoPreventiva):
		return TypePreventive
	case string(TipoCorretiva):
		return TypeCorrective
	case string(TipoInspecao):
		return TypeInspection
	case string(TipoCalibragam):
		return TypeCalibration
	case string(TipoInstalacao):
		return TypeInstallation
	default:
		// Se já for um valor padronizado, retorna como está
		if IsStandardMaintenanceType(maintenanceType) {
			return MaintenanceType(maintenanceType)
		}
		// Valor desconhecido, retorna corretiva como padrão
		return TypeCorrective
	}
}

// IsStandardMaintenanceType verifica se o tipo de manutenção já está no formato padronizado
func IsStandardMaintenanceType(maintenanceType string) bool {
	switch maintenanceType {
	case string(TypePreventive), string(TypeCorrective), string(TypeInspection),
		string(TypeCalibration), string(TypeInstallation), string(TypeEmergency):
		return true
	default:
		return false
	}
}

// GetOrderStatusDisplay retorna o texto em português para exibição do status
func GetOrderStatusDisplay(status OrderStatus) string {
	if display, ok := OrderStatusDisplay[status]; ok {
		return display
	}
	return string(status)
}

// GetPriorityLevelDisplay retorna o texto em português para exibição da prioridade
func GetPriorityLevelDisplay(priority PriorityLevel) string {
	if display, ok := PriorityLevelDisplay[priority]; ok {
		return display
	}
	return string(priority)
}

// GetMaintenanceTypeDisplay retorna o texto em português para exibição do tipo de manutenção
func GetMaintenanceTypeDisplay(maintenanceType MaintenanceType) string {
	if display, ok := MaintenanceTypeDisplay[maintenanceType]; ok {
		return display
	}
	return string(maintenanceType)
}
