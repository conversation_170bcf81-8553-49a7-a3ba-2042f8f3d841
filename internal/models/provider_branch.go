package models

import (
	"time"
)

// ProviderBranch representa a relação entre prestador e filial
type ProviderBranch struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	ServiceProviderID uint      `json:"service_provider_id" gorm:"not null"`
	BranchID          uint      `json:"branch_id" gorm:"not null"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`

	// Relações
	ServiceProvider *ServiceProvider `json:"service_provider,omitempty" gorm:"foreignKey:ServiceProviderID"`
	Branch          *Branch          `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
}

// TableName retorna o nome da tabela no banco de dados
func (ProviderBranch) TableName() string {
	return "branch_providers"
}
