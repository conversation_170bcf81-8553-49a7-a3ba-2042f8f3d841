package models

import (
	"time"
)

// Equipamento representa um equipamento cadastrado no sistema
type Equipamento struct {
	ID                uint       `json:"id" gorm:"primaryKey"`
	Nome              string     `json:"nome"`
	Tipo              string     `json:"tipo"`
	Modelo            string     `json:"modelo"`
	NumeroSerie       string     `json:"numero_serie"`
	Fabricante        string     `json:"fabricante"`
	FilialID          uint       `json:"filial_id" gorm:"index"`
	FilialNome        string     `json:"filial_nome"`
	Status            string     `json:"status"` // ativo, inativo, em manutenção
	DataCompra        time.Time  `json:"data_compra,omitempty"`
	UltimaManutencao  *time.Time `json:"ultima_manutencao,omitempty"`
	ProximaManutencao *time.Time `json:"proxima_manutencao,omitempty"`
	Observacoes       string     `json:"observacoes"`
	CreatedAt         time.Time  `json:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at"`
}
