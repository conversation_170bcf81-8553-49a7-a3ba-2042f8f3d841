package models

// OrderStatus é um tipo para status de ordens de manutenção
// Representa o estado atual de uma ordem de manutenção no sistema
type OrderStatus string

// Constantes para status de manutenção
// IMPORTANTE: Estas são as constantes padronizadas para status de ordem.
// Todas as novas implementações devem usar estas constantes em inglês.
// Para compatibilidade com código existente, use as funções em maintenance_status.go:
// - NormalizeOrderStatus: converte valores legados para os novos valores padronizados
// - IsOrderStatus: verifica se um status é igual a um valor específico
// - IsOrderStatusInSet: verifica se um status está em um conjunto de valores válidos
const (
	StatusPending    OrderStatus = "pending"     // Ordem pendente, aguardando processamento
	StatusScheduled  OrderStatus = "scheduled"   // Ordem agendada para uma data específica
	StatusInProgress OrderStatus = "in_progress" // Ordem em andamento, sendo executada
	StatusCompleted  OrderStatus = "completed"   // Ordem concluída com sucesso
	StatusCancelled  OrderStatus = "cancelled"   // Ordem cancelada (não será executada)
	StatusVerified   OrderStatus = "verified"    // Ordem verificada após conclusão
	StatusRejected   OrderStatus = "rejected"    // Ordem rejeitada (não aprovada)
	StatusApproved   OrderStatus = "approved"    // Ordem aprovada para execução
)

// Alias para compatibilidade com código existente
const StatusCanceled = StatusCancelled

// Mapeamento de status para exibição em português
var OrderStatusDisplay = map[OrderStatus]string{
	StatusPending:    "Pendente",
	StatusScheduled:  "Agendada",
	StatusInProgress: "Em Andamento",
	StatusCompleted:  "Concluída",
	StatusCancelled:  "Cancelada",
	StatusVerified:   "Verificada",
	StatusRejected:   "Rejeitada",
	StatusApproved:   "Aprovada",
}

// Valores legados para compatibilidade
// IMPORTANTE: Estas constantes são mantidas apenas para compatibilidade com código existente.
// Novas implementações NÃO devem usar estas constantes, mas sim as constantes padronizadas em inglês.
// Para converter entre valores legados e padronizados, use a função NormalizeOrderStatus.
const (
	StatusPendente    OrderStatus = "pendente"     // Legado: use StatusPending
	StatusAgendada    OrderStatus = "agendada"     // Legado: use StatusScheduled
	StatusEmAndamento OrderStatus = "em_andamento" // Legado: use StatusInProgress
	StatusConcluida   OrderStatus = "concluida"    // Legado: use StatusCompleted
	StatusCancelada   OrderStatus = "cancelada"    // Legado: use StatusCancelled
	StatusVerificada  OrderStatus = "verificada"   // Legado: use StatusVerified
	StatusRejeitada   OrderStatus = "rejeitada"    // Legado: use StatusRejected
	StatusAprovada    OrderStatus = "aprovada"     // Legado: use StatusApproved
)

// PriorityLevel é um tipo para prioridades de ordens de manutenção
type PriorityLevel string

// Constantes para prioridades de manutenção
const (
	PriorityLow      PriorityLevel = "low"
	PriorityMedium   PriorityLevel = "medium"
	PriorityHigh     PriorityLevel = "high"
	PriorityCritical PriorityLevel = "critical"
)

// Mapeamento de prioridades para exibição em português
var PriorityLevelDisplay = map[PriorityLevel]string{
	PriorityLow:      "Baixa",
	PriorityMedium:   "Média",
	PriorityHigh:     "Alta",
	PriorityCritical: "Crítica",
}

// Valores legados para compatibilidade
const (
	PrioridadeBaixa   PriorityLevel = "baixa"
	PrioridadeMedia   PriorityLevel = "media"
	PrioridadeAlta    PriorityLevel = "alta"
	PrioridadeCritica PriorityLevel = "critica"
)

// MaintenanceType é um tipo para o tipo de manutenção
type MaintenanceType string

// Constantes para tipos de manutenção
const (
	TypePreventive   MaintenanceType = "preventive"
	TypeCorrective   MaintenanceType = "corrective"
	TypeInspection   MaintenanceType = "inspection"
	TypeCalibration  MaintenanceType = "calibration"
	TypeInstallation MaintenanceType = "installation"
)

// Mapeamento de tipos de manutenção para exibição em português
var MaintenanceTypeDisplay = map[MaintenanceType]string{
	TypePreventive:   "Preventiva",
	TypeCorrective:   "Corretiva",
	TypeInspection:   "Inspeção",
	TypeCalibration:  "Calibragem",
	TypeInstallation: "Instalação",
}

// Valores legados para compatibilidade
const (
	TipoPreventiva MaintenanceType = "preventiva"
	TipoCorretiva  MaintenanceType = "corretiva"
	TipoInspecao   MaintenanceType = "inspecao"
	TipoCalibragam MaintenanceType = "calibragem"
	TipoInstalacao MaintenanceType = "instalacao"
)

// ApprovalStatus é um tipo para status de aprovação
type ApprovalStatus string

// Constantes para status de aprovação
const (
	ApprovalPending  ApprovalStatus = "pending"
	ApprovalApproved ApprovalStatus = "approved"
	ApprovalRejected ApprovalStatus = "rejected"
)

// Mapeamento de status de aprovação para exibição em português
var ApprovalStatusDisplay = map[ApprovalStatus]string{
	ApprovalPending:  "Pendente",
	ApprovalApproved: "Aprovado",
	ApprovalRejected: "Rejeitado",
}

// Valores legados para compatibilidade
const (
	AprovacaoPendente  ApprovalStatus = "pendente"
	AprovacaoAprovada  ApprovalStatus = "aprovado"
	AprovacaoRejeitada ApprovalStatus = "rejeitado"
)
