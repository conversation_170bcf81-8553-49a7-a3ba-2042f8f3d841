package models

import (
	"fmt"
	"strconv"
)

// ID é uma interface para abstrair diferentes tipos de identificadores
type ID interface {
	String() string
	IsZero() bool
}

// NumericID representa um ID numérico
type NumericID struct {
	Value uint
}

// NewNumericID cria um novo NumericID
func NewNumericID(value uint) NumericID {
	return NumericID{Value: value}
}

// String retorna a representação em string do NumericID
func (id NumericID) String() string {
	return fmt.Sprintf("%d", id.Value)
}

// IsZero verifica se o NumericID é zero
func (id NumericID) IsZero() bool {
	return id.Value == 0
}

// ParseID converte uma string para ID
func ParseID(idStr string) (ID, error) {
	// Tentar converter para uint
	if val, err := strconv.ParseUint(idStr, 10, 64); err == nil {
		return NumericID{Value: uint(val)}, nil
	}

	return nil, fmt.Errorf("formato de ID inválido: %s", idStr)
}

// ConvertToNumericID converte um ID para NumericID
func ConvertToNumericID(id ID) (NumericID, error) {
	// Se já for NumericID, retornar diretamente
	if numID, ok := id.(NumericID); ok {
		return numID, nil
	}

	// Tentar converter a partir da string
	idStr := id.String()
	val, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return NumericID{}, fmt.Errorf("não foi possível converter para NumericID: %v", err)
	}

	return NumericID{Value: uint(val)}, nil
}
