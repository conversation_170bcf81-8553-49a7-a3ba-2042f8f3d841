package models

import (
	"time"
)

// PaymentStatus representa o status de um pagamento
type PaymentStatus string

const (
	PaymentStatusPending   PaymentStatus = "pending"
	PaymentStatusPaid      PaymentStatus = "paid"
	PaymentStatusOverdue   PaymentStatus = "overdue"
	PaymentStatusCancelled PaymentStatus = "cancelled"
)

// PaymentFilters representa os filtros para listagem de pagamentos
type PaymentFilters struct {
	Status    PaymentStatus `json:"status"`
	StartDate time.Time     `json:"start_date"`
	EndDate   time.Time     `json:"end_date"`
}

// PaymentType define o tipo de pagamento
type PaymentType string

const (
	PaymentTypeIncome  PaymentType = "income"
	PaymentTypeExpense PaymentType = "expense"
)

// Payment representa um pagamento
type Payment struct {
	ID          int64         `json:"id"`
	Type        PaymentType   `json:"type"`
	Amount      float64       `json:"amount"`
	Description string        `json:"description"`
	Category    string        `json:"category"`
	Date        time.Time     `json:"date"`
	DueDate     time.Time     `json:"due_date"`
	Status      PaymentStatus `json:"status"`
	CreatedAt   time.Time     `json:"created_at"`
	UpdatedAt   time.Time     `json:"updated_at"`
}

// InvoiceStatus representa o status de uma nota fiscal
type InvoiceStatus string

const (
	InvoiceStatusPending   InvoiceStatus = "pending"
	InvoiceStatusPaid      InvoiceStatus = "paid"
	InvoiceStatusOverdue   InvoiceStatus = "overdue"
	InvoiceStatusCancelled InvoiceStatus = "cancelled"
)

// InvoiceFilters representa os filtros para listagem de notas fiscais
type InvoiceFilters struct {
	Status    InvoiceStatus `json:"status"`
	StartDate time.Time     `json:"start_date"`
	EndDate   time.Time     `json:"end_date"`
}

// Invoice representa uma nota fiscal
type Invoice struct {
	ID          int64         `json:"id"`
	Number      string        `json:"number"`
	Amount      float64       `json:"amount"`
	Description string        `json:"description"`
	Date        time.Time     `json:"date"`
	IssueDate   time.Time     `json:"issue_date"`
	DueDate     time.Time     `json:"due_date"`
	Status      InvoiceStatus `json:"status"`
	CreatedAt   time.Time     `json:"created_at"`
	UpdatedAt   time.Time     `json:"updated_at"`
}

// Budget representa um orçamento
type Budget struct {
	ID          int64     `json:"id"`
	Category    string    `json:"category"`
	Amount      float64   `json:"amount"`
	Description string    `json:"description"`
	PeriodStart time.Time `json:"period_start"`
	PeriodEnd   time.Time `json:"period_end"`
	StartDate   time.Time `json:"start_date"`
	EndDate     time.Time `json:"end_date"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// BudgetFilters representa os filtros para listagem de orçamentos
type BudgetFilters struct {
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`
}

// ReportType representa o tipo de relatório
type ReportType string

const (
	ReportTypeFinancial ReportType = "financial"
	ReportTypeBudget    ReportType = "budget"
	ReportTypeTax       ReportType = "tax"
)

// ReportFilters representa os filtros para listagem de relatórios
type ReportFilters struct {
	Type      ReportType `json:"type"`
	StartDate time.Time  `json:"start_date"`
	EndDate   time.Time  `json:"end_date"`
}

// Report representa um relatório financeiro
type Report struct {
	ID          int64      `json:"id"`
	Type        ReportType `json:"type"`
	Title       string     `json:"title"`
	Description string     `json:"description"`
	Content     string     `json:"content"`
	Data        string     `json:"data"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
}

// ExpenseCategory representa um gasto por categoria
type ExpenseCategory struct {
	Category string  `json:"category"`
	Amount   float64 `json:"amount"`
}

// News representa uma notícia financeira
type News struct {
	ID          int64     `json:"id"`
	Title       string    `json:"title"`
	Content     string    `json:"content"`
	Source      string    `json:"source"`
	URL         string    `json:"url"`
	PublishedAt time.Time `json:"published_at"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Quote representa uma cotação
type Quote struct {
	ID            int64     `json:"id"`
	Symbol        string    `json:"symbol"`
	Price         float64   `json:"price"`
	Change        float64   `json:"change"`
	ChangePercent float64   `json:"change_percent"`
	Variation     float64   `json:"variation"`
	LastUpdate    time.Time `json:"last_update"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// FinanceSettings representa as configurações financeiras
type FinanceSettings struct {
	ID                  int64     `json:"id"`
	DefaultCurrency     string    `json:"default_currency"`
	TaxRate             float64   `json:"tax_rate"`
	InvoicePrefix       string    `json:"invoice_prefix"`
	PaymentTerms        int       `json:"payment_terms"`
	AutoApproveInvoices bool      `json:"auto_approve_invoices"`
	AutoApprovePayments bool      `json:"auto_approve_payments"`
	AutoRefresh         bool      `json:"auto_refresh"`
	RefreshInterval     int       `json:"refresh_interval"`
	ColorScheme         string    `json:"color_scheme"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`
}

// DashboardData representa os dados exibidos no painel financeiro
type DashboardData struct {
	TotalRevenue  float64  `json:"total_revenue"`
	TotalExpenses float64  `json:"total_expenses"`
	Profit        float64  `json:"profit"`
	Balance       float64  `json:"balance"`
	Quotes        []*Quote `json:"quotes"`
	News          []*News  `json:"news"`
}
