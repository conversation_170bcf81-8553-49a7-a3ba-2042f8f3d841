package models

import "time"

// ServiceProvider representa um prestador de serviço
type ServiceProvider struct {
	ID              uint      `json:"id" gorm:"primaryKey"`
	Name            string    `json:"name" gorm:"not null"`
	CompanyName     string    `json:"company_name" gorm:"not null"`
	CNPJ            string    `json:"cnpj" gorm:"not null;unique"`
	Address         string    `json:"address"`
	City            string    `json:"city"`
	State           string    `json:"state"`
	ZipCode         string    `json:"zip_code"`
	ContactName     string    `json:"contact_name"`
	ContactEmail    string    `json:"contact_email"`
	ContactPhone    string    `json:"contact_phone"`
	Specialties     string    `json:"specialties"` // Armazenado como JSON ou texto delimitado
	AreaOfExpertise string    `json:"area_of_expertise"`
	AverageRating   float64   `json:"average_rating"`
	Status          string    `json:"status" gorm:"default:active"` // ativo/inativo
	LogoURL         string    `json:"logo_url"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`

	// Relações
	Technicians []User `json:"technicians,omitempty" gorm:"foreignKey:ServiceProviderID"`
}

// TableName retorna o nome da tabela no banco de dados
func (ServiceProvider) TableName() string {
	return TableServiceProviders
}
