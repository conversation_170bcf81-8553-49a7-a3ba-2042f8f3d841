package models

import "time"

// MaintenanceActivity representa uma atividade de manutenção
type MaintenanceActivity struct {
	ID                 int       `json:"id" gorm:"primaryKey"`
	Description        string    `json:"description"`
	Timestamp          time.Time `json:"timestamp"`
	Type               string    `json:"type"` // diagnóstico, reparo, substituição, preventiva
	MaintenanceOrderID int       `json:"maintenance_order_id" gorm:"index"`
	CreatedAt          time.Time `json:"created_at"`
}
