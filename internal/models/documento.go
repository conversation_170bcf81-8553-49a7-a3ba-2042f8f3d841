package models

import (
	"time"

	"gorm.io/gorm"
)

// Documento representa um documento no sistema
type Documento struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	Title       string    `gorm:"column:title" json:"title"`
	Description string    `gorm:"column:description" json:"description"`
	Content     string    `gorm:"column:content;type:text" json:"content"`
	OrdemID     *uint     `gorm:"column:ordem_id" json:"ordem_id,omitempty"`
	UserID      uint      `gorm:"column:user_id" json:"user_id"`
	CreatedAt   time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"updated_at"`
	DeletedAt   *time.Time `gorm:"column:deleted_at" json:"deleted_at,omitempty"`
}

// TableName retorna o nome da tabela no banco de dados
func (Documento) TableName() string {
	return "documentos"
}

// BeforeCreate é chamado antes de criar um novo registro
func (d *Documento) BeforeCreate(tx *gorm.DB) error {
	if d.CreatedAt.<PERSON>ero() {
		d.CreatedAt = time.Now()
	}
	d.UpdatedAt = time.Now()
	return nil
}

// BeforeUpdate é chamado antes de atualizar um registro
func (d *Documento) BeforeUpdate(tx *gorm.DB) error {
	d.UpdatedAt = time.Now()
	return nil
}
