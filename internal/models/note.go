package models

import (
	"time"
)

type Note struct {
	ID                 uint      `json:"id" gorm:"primaryKey"`
	MaintenanceOrderID uint      `json:"maintenance_order_id" gorm:"column:maintenance_order_id"`
	Content            string    `json:"content"`
	AddedByUserID      uint      `json:"added_by_user_id" gorm:"column:added_by_user_id"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
}

func (Note) TableName() string {
	return "maintenance_notes"
}
