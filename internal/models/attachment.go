package models

import "time"

// Tipos de anexos para o sistema
const (
	// Tipos de anexos para equipamentos
	AttachmentTypeEquipment       = "equipment"        // Foto do equipamento
	AttachmentTypeEquipmentBefore = "equipment_before" // Foto do equipamento antes do reparo
	AttachmentTypeEquipmentAfter  = "equipment_after"  // Foto do equipamento após o reparo

	// Tipos de anexos para peças
	AttachmentTypePartOld = "part_old" // Foto da peça antiga/com defeito
	AttachmentTypePartNew = "part_new" // Foto da peça nova/substituição

	// Tipos de anexos para documentos
	AttachmentTypeDocument = "document" // Documento relacionado à ordem
	AttachmentTypeInvoice  = "invoice"  // Nota fiscal ou fatura
	AttachmentTypeReport   = "report"   // Relatório técnico
)

// Attachment representa um anexo para ordens de manutenção ou atividades
type Attachment struct {
	ID                    uint       `json:"id" gorm:"primaryKey"`
	File                  string     `json:"file"`        // Caminho do arquivo no sistema
	Type                  string     `json:"type"`        // Tipo do anexo (ver constantes acima)
	Description           string     `json:"description"` // Descrição opcional do anexo
	MaintenanceOrderID    uint       `json:"maintenance_order_id" gorm:"index"`
	MaintenanceActivityID uint       `json:"maintenance_activity_id" gorm:"index"`
	MaterialID            uint       `json:"material_id" gorm:"index"` // Para fotos de peças específicas
	UploadedBy            uint       `json:"uploaded_by"`              // ID do usuário que enviou o anexo
	UploadedByName        string     `json:"uploaded_by_name"`         // Nome do usuário que enviou
	CreatedAt             time.Time  `json:"created_at"`
	UpdatedAt             time.Time  `json:"updated_at"`
	DeletedAt             *time.Time `json:"deleted_at,omitempty" gorm:"index"`
}

// AttachmentRequest representa a estrutura para solicitar upload de anexo
type AttachmentRequest struct {
	Type                  string `json:"type" binding:"required"`
	Description           string `json:"description"`
	MaintenanceOrderID    uint   `json:"maintenance_order_id"`
	MaintenanceActivityID uint   `json:"maintenance_activity_id"`
	MaterialID            uint   `json:"material_id"`
}

// GetAttachmentTypeLabel retorna um rótulo legível para o tipo de anexo
func GetAttachmentTypeLabel(attachmentType string) string {
	switch attachmentType {
	case AttachmentTypeEquipment:
		return "Foto do Equipamento"
	case AttachmentTypeEquipmentBefore:
		return "Antes do Reparo"
	case AttachmentTypeEquipmentAfter:
		return "Após o Reparo"
	case AttachmentTypePartOld:
		return "Peça Antiga"
	case AttachmentTypePartNew:
		return "Peça Nova"
	case AttachmentTypeDocument:
		return "Documento"
	case AttachmentTypeInvoice:
		return "Nota Fiscal"
	case AttachmentTypeReport:
		return "Relatório Técnico"
	default:
		return "Anexo"
	}
}
