package models

import "time"

// Tipos importados de maintenance_types.go

// ResponseAuditLog representa um registro de auditoria para uso em APIs
type ResponseAuditLog struct {
	ID        uint      `json:"id"`
	UserID    uint      `json:"userId"`
	User      *User     `json:"user,omitempty"`
	Action    string    `json:"action"`
	Entity    string    `json:"entity"`
	EntityID  uint      `json:"entityId"`
	Details   string    `json:"details"`
	IP        string    `json:"ip"`
	UserAgent string    `json:"userAgent"`
	CreatedAt time.Time `json:"createdAt"`
}

// FilialSummary representa um resumo dos dados de uma filial
type FilialSummary struct {
	ID         uint   `json:"id"`
	Name       string `json:"name"`
	Code       string `json:"code"`
	Type       string `json:"type"`
	Address    string `json:"address,omitempty"`
	City       string `json:"city,omitempty"`
	State      string `json:"state,omitempty"`
	IsActive   bool   `json:"isActive"`
	BranchID   uint   `json:"branchId"`
	BranchName string `json:"branchName,omitempty"`
}

// StationSummary representa um resumo dos dados de uma estação (alias para FilialSummary para compatibilidade)
type StationSummary struct {
	ID         uint   `json:"id"`
	Name       string `json:"name"`
	Code       string `json:"code"`
	Type       string `json:"type"`
	Address    string `json:"address,omitempty"`
	City       string `json:"city,omitempty"`
	State      string `json:"state,omitempty"`
	IsActive   bool   `json:"isActive"`
	BranchID   uint   `json:"branchId"`
	BranchName string `json:"branchName,omitempty"`
}

// ResponseMaintenanceOrderDetailed representa uma ordem de manutenção com detalhes adicionais
// Renomeado para evitar conflito com a definição em maintenance_order_detailed.go
type ResponseMaintenanceOrderDetailed struct {
	ID             uint            `json:"id"`
	Title          string          `json:"title"`
	Description    string          `json:"description"`
	Type           MaintenanceType `json:"type"`
	Priority       PriorityLevel   `json:"priority"`
	Status         OrderStatus     `json:"status"`
	ApprovalStatus ApprovalStatus  `json:"approvalStatus"`
	PaymentStatus  PaymentStatus   `json:"paymentStatus"`
	RequestedDate  time.Time       `json:"requestedDate"`
	ScheduledDate  *time.Time      `json:"scheduledDate,omitempty"`
	StartDate      *time.Time      `json:"startDate,omitempty"`
	EndDate        *time.Time      `json:"endDate,omitempty"`
	VerifiedDate   *time.Time      `json:"verifiedDate,omitempty"`
	ApprovedDate   *time.Time      `json:"approvedDate,omitempty"`
	Notes          string          `json:"notes,omitempty"`
	EstimatedCost  float64         `json:"estimatedCost"`
	FinalCost      float64         `json:"finalCost"`

	// Relacionamentos expandidos
	BranchID        uint   `json:"branchId"`
	BranchName      string `json:"branchName"`
	StationID       uint   `json:"stationId"`
	StationName     string `json:"stationName"`
	EquipmentID     uint   `json:"equipmentId"`
	EquipmentName   string `json:"equipmentName"`
	EquipmentType   string `json:"equipmentType"`
	TechnicianID    *uint  `json:"technicianId,omitempty"`
	TechnicianName  string `json:"technicianName,omitempty"`
	RequestedByID   uint   `json:"requestedById"`
	RequestedByName string `json:"requestedByName"`
	ApprovedByID    *uint  `json:"approvedById,omitempty"`
	ApprovedByName  string `json:"approvedByName,omitempty"`
	VerifiedByID    *uint  `json:"verifiedById,omitempty"`
	VerifiedByName  string `json:"verifiedByName,omitempty"`

	// Contadores
	PhotoCount    int `json:"photoCount"`
	NoteCount     int `json:"noteCount"`
	MaterialCount int `json:"materialCount"`

	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

// MaintenanceOrderMetrics representa métricas agregadas de ordens de manutenção
type MaintenanceOrderMetrics struct {
	TotalOrders      int `json:"totalOrders"`
	PendingOrders    int `json:"pendingOrders"`
	CompletedOrders  int `json:"completedOrders"`
	InProgressOrders int `json:"inProgressOrders"`
	DelayedOrders    int `json:"delayedOrders"`

	AverageCompletionTime float64 `json:"averageCompletionTime"` // Em horas
	AverageResponseTime   float64 `json:"averageResponseTime"`   // Em horas
	AverageCost           float64 `json:"averageCost"`
	TotalCost             float64 `json:"totalCost"`

	HighPriorityCount   int `json:"highPriorityCount"`
	MediumPriorityCount int `json:"mediumPriorityCount"`
	LowPriorityCount    int `json:"lowPriorityCount"`

	// Distribuição por tipo
	PreventiveCount   int `json:"preventiveCount"`
	CorrectiveCount   int `json:"correctiveCount"`
	InspectionCount   int `json:"inspectionCount"`
	CalibrationCount  int `json:"calibrationCount"`
	InstallationCount int `json:"installationCount"`
}
