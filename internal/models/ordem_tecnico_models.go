package models

import (
	"time"
)

// ManutencaoData representa os dados de manutenção de uma ordem
type ManutencaoData struct {
	ID              uint      `json:"id" gorm:"primaryKey"`
	OrdemID         uint      `json:"ordem_id" gorm:"index"`
	Descricao       string    `json:"descricao"`
	PecasUtilizadas string    `json:"pecas_utilizadas"`
	Observacoes     string    `json:"observacoes"`
	DataRegistro    time.Time `json:"data_registro"`
	TecnicoID       uint      `json:"tecnico_id"`
	TecnicoNome     string    `json:"tecnico_nome"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// CustosData representa os dados de custos de uma ordem
type CustosData struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	OrdemID      uint      `json:"ordem_id" gorm:"index"`
	Pecas        float64   `json:"pecas"`
	MaoObra      float64   `json:"mao_obra"`
	Deslocamento float64   `json:"deslocamento"`
	Total        float64   `json:"total"`
	DataRegistro time.Time `json:"data_registro"`
	TecnicoID    uint      `json:"tecnico_id"`
	TecnicoNome  string    `json:"tecnico_nome"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// CronogramaData representa os dados de cronograma de uma ordem
type CronogramaData struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	OrdemID      uint      `json:"ordem_id" gorm:"index"`
	DataInicio   string    `json:"data_inicio"`
	HoraInicio   string    `json:"hora_inicio"`
	DataFim      string    `json:"data_fim"`
	HoraFim      string    `json:"hora_fim"`
	Status       string    `json:"status"`
	DataRegistro time.Time `json:"data_registro"`
	TecnicoID    uint      `json:"tecnico_id"`
	TecnicoNome  string    `json:"tecnico_nome"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// MensagemChat representa uma mensagem de chat de uma ordem
type MensagemChat struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	OrdemID     uint      `json:"ordem_id" gorm:"index"`
	Mensagem    string    `json:"mensagem"`
	Remetente   string    `json:"remetente"`
	RemetenteID uint      `json:"remetente_id"`
	DataEnvio   time.Time `json:"data_envio"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Já existe um modelo HistoricoOrdem em internal/models/historico.go

// TableName retorna o nome da tabela para ManutencaoData
func (ManutencaoData) TableName() string {
	return "ordem_manutencao_dados"
}

// TableName retorna o nome da tabela para CustosData
func (CustosData) TableName() string {
	return "ordem_custos_dados"
}

// TableName retorna o nome da tabela para CronogramaData
func (CronogramaData) TableName() string {
	return "ordem_cronograma_dados"
}

// TableName retorna o nome da tabela para MensagemChat
func (MensagemChat) TableName() string {
	return "ordem_mensagens_chat"
}
