package models

import (
	"time"

	"gorm.io/gorm"
)

// StatusTransferencia representa o status de uma transferência de equipamento
type StatusTransferencia string

const (
	StatusTransferenciaPendente   StatusTransferencia = "pendente"
	StatusTransferenciaAprovada   StatusTransferencia = "aprovada"
	StatusTransferenciaRejeitada  StatusTransferencia = "rejeitada"
	StatusTransferenciaCancelada  StatusTransferencia = "cancelada"
	StatusTransferenciaConcluida  StatusTransferencia = "concluida"
)

// EquipmentTransfer representa uma transferência de equipamento entre filiais
type EquipmentTransfer struct {
	ID                 uint               `json:"id" gorm:"primaryKey"`
	EquipmentID        uint               `json:"equipment_id" gorm:"index"`
	SourceBranchID     uint               `json:"source_branch_id" gorm:"index"`
	DestinationBranchID uint              `json:"destination_branch_id" gorm:"index"`
	RequestedByUserID  uint               `json:"requested_by_user_id"`
	ApprovedByUserID   *uint              `json:"approved_by_user_id"`
	Justification      string             `json:"justification" gorm:"type:text"`
	AuthorizedBy       string             `json:"authorized_by"`
	Status             StatusTransferencia `json:"status" gorm:"type:varchar(20);default:'pendente'"`
	RequestedAt        time.Time          `json:"requested_at"`
	ApprovedAt         *time.Time         `json:"approved_at"`
	CompletedAt        *time.Time         `json:"completed_at"`
	Notes              string             `json:"notes" gorm:"type:text"`
	CreatedAt          time.Time          `json:"created_at"`
	UpdatedAt          time.Time          `json:"updated_at"`
	DeletedAt          gorm.DeletedAt     `json:"deleted_at" gorm:"index"`
	
	// Relações
	Equipment          *Equipment         `json:"equipment" gorm:"foreignKey:EquipmentID"`
	SourceBranch       *Branch            `json:"source_branch" gorm:"foreignKey:SourceBranchID"`
	DestinationBranch  *Branch            `json:"destination_branch" gorm:"foreignKey:DestinationBranchID"`
	RequestedByUser    *User              `json:"requested_by_user" gorm:"foreignKey:RequestedByUserID"`
	ApprovedByUser     *User              `json:"approved_by_user" gorm:"foreignKey:ApprovedByUserID"`
}

// TableName especifica o nome da tabela para o modelo EquipmentTransfer
func (EquipmentTransfer) TableName() string {
	return "equipment_transfers"
}
