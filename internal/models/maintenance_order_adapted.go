package models

import (
	"time"

	"gorm.io/gorm"
)

// MaintenanceOrderAdapted representa uma ordem de manutenção adaptada para PostgreSQL
type MaintenanceOrderAdapted struct {
	ID                 ID            `json:"id" gorm:"primaryKey"`
	BranchID           uint          `json:"branch_id" gorm:"column:branch_id"`
	EquipmentID        uint          `json:"equipment_id" gorm:"column:equipment_id"`
	ServiceProviderID  ID            `json:"service_provider_id,omitempty" gorm:"column:service_provider_id"`
	CreatedByUserID    ID            `json:"created_by_user_id" gorm:"column:created_by_user_id"`
	Number             string        `json:"number" gorm:"column:number"`
	Title              string        `json:"title" gorm:"column:title"`
	Description        string        `json:"description" gorm:"column:description"`
	Problem            string        `json:"problem" gorm:"column:problem"`
	Status             OrderStatus   `json:"status" gorm:"column:status"`
	Priority           PriorityLevel `json:"priority" gorm:"column:priority"`
	EstimatedCost      float64       `json:"estimated_cost" gorm:"column:estimated_cost"`
	ActualCost         float64       `json:"actual_cost" gorm:"column:actual_cost"`
	DueDate            time.Time     `json:"due_date" gorm:"column:due_date"`
	OpenDate           time.Time     `json:"open_date" gorm:"column:open_date"`
	CreatedAt          time.Time     `json:"created_at" gorm:"column:created_at"`
	UpdatedAt          time.Time     `json:"updated_at" gorm:"column:updated_at"`
	DeletedAt          *time.Time    `json:"deleted_at,omitempty" gorm:"column:deleted_at"`
	CompletionDate     *time.Time    `json:"completion_date,omitempty" gorm:"column:completion_date"`
	ApprovalStatus     string        `json:"approval_status" gorm:"column:approval_status"`
	ApprovalDate       *time.Time    `json:"approval_date,omitempty" gorm:"column:approval_date"`
	PaymentStatus      string        `json:"payment_status" gorm:"column:payment_status"`
	PaymentDate        *time.Time    `json:"payment_date,omitempty" gorm:"column:payment_date"`
	Rating             *uint         `json:"rating,omitempty" gorm:"column:rating"`
	PartialFunctioning bool          `json:"partial_functioning" gorm:"column:partial_functioning"`
	ExtraEquipment     bool          `json:"extra_equipment" gorm:"column:extra_equipment"`
	SameDay            bool          `json:"same_day" gorm:"column:same_day"`
	PartReplacement    bool          `json:"part_replacement" gorm:"column:part_replacement"`
}

// TableName retorna o nome da tabela no banco de dados
func (MaintenanceOrderAdapted) TableName() string {
	return "maintenance_orders"
}

// BeforeCreate é chamado antes de criar um novo registro
func (m *MaintenanceOrderAdapted) BeforeCreate(tx *gorm.DB) error {
	if m.CreatedAt.IsZero() {
		m.CreatedAt = time.Now()
	}
	m.UpdatedAt = time.Now()
	return nil
}

// BeforeUpdate é chamado antes de atualizar um registro
func (m *MaintenanceOrderAdapted) BeforeUpdate(tx *gorm.DB) error {
	m.UpdatedAt = time.Now()
	return nil
}

// FromMaintenanceOrder converte uma MaintenanceOrder para MaintenanceOrderAdapted
func FromMaintenanceOrder(order *MaintenanceOrder) *MaintenanceOrderAdapted {
	if order == nil {
		return nil
	}

	// Converter IDs para o tipo ID
	id := NewNumericID(order.ID)
	var serviceProviderID ID
	if order.ServiceProviderID != nil {
		serviceProviderID = NewNumericID(*order.ServiceProviderID)
	}
	createdByUserID := NewNumericID(order.CreatedByUserID)

	return &MaintenanceOrderAdapted{
		ID:                 id,
		BranchID:           order.BranchID,
		EquipmentID:        order.EquipmentID,
		ServiceProviderID:  serviceProviderID,
		CreatedByUserID:    createdByUserID,
		Number:             order.Number,
		Title:              order.Title,
		Description:        order.Description,
		Problem:            order.Problem,
		Status:             order.Status,
		Priority:           order.Priority,
		EstimatedCost:      order.EstimatedCost,
		ActualCost:         order.ActualCost,
		DueDate:            order.DueDate,
		OpenDate:           order.OpenDate,
		CreatedAt:          order.CreatedAt,
		UpdatedAt:          order.UpdatedAt,
		DeletedAt:          order.DeletedAt,
		CompletionDate:     order.CompletionDate,
		ApprovalStatus:     order.ApprovalStatus,
		ApprovalDate:       order.ApprovalDate,
		PaymentStatus:      order.PaymentStatus,
		PaymentDate:        order.PaymentDate,
		Rating:             order.Rating,
		PartialFunctioning: order.PartialFunctioning,
		ExtraEquipment:     order.ExtraEquipment,
		SameDay:            order.SameDay,
		PartReplacement:    order.PartReplacement,
	}
}

// ToMaintenanceOrder converte uma MaintenanceOrderAdapted para MaintenanceOrder
func (m *MaintenanceOrderAdapted) ToMaintenanceOrder() *MaintenanceOrder {
	// Converter IDs para os tipos apropriados
	var id uint
	if m.ID != nil {
		numID, err := ConvertToNumericID(m.ID)
		if err == nil {
			id = numID.Value
		}
	}

	var serviceProviderID *uint
	if m.ServiceProviderID != nil && !m.ServiceProviderID.IsZero() {
		numID, err := ConvertToNumericID(m.ServiceProviderID)
		if err == nil {
			value := numID.Value
			serviceProviderID = &value
		}
	}

	var createdByUserID uint
	if m.CreatedByUserID != nil {
		numID, err := ConvertToNumericID(m.CreatedByUserID)
		if err == nil {
			createdByUserID = numID.Value
		}
	}

	return &MaintenanceOrder{
		ID:                 id,
		BranchID:           m.BranchID,
		EquipmentID:        m.EquipmentID,
		ServiceProviderID:  serviceProviderID,
		CreatedByUserID:    createdByUserID,
		Number:             m.Number,
		Title:              m.Title,
		Description:        m.Description,
		Problem:            m.Problem,
		Status:             m.Status,
		Priority:           m.Priority,
		EstimatedCost:      m.EstimatedCost,
		ActualCost:         m.ActualCost,
		DueDate:            m.DueDate,
		OpenDate:           m.OpenDate,
		CreatedAt:          m.CreatedAt,
		UpdatedAt:          m.UpdatedAt,
		DeletedAt:          m.DeletedAt,
		CompletionDate:     m.CompletionDate,
		ApprovalStatus:     m.ApprovalStatus,
		ApprovalDate:       m.ApprovalDate,
		PaymentStatus:      m.PaymentStatus,
		PaymentDate:        m.PaymentDate,
		Rating:             m.Rating,
		PartialFunctioning: m.PartialFunctioning,
		ExtraEquipment:     m.ExtraEquipment,
		SameDay:            m.SameDay,
		PartReplacement:    m.PartReplacement,
	}
}
