package models

import (
	"crypto/rand"
	"encoding/hex"
	"time"
)

// BranchAuthToken representa um token de autenticação temporário para uma filial
type BranchAuthToken struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	BranchID  uint      `json:"branch_id" gorm:"index;not null"`
	Token     string    `json:"token" gorm:"uniqueIndex;size:100;not null"`
	ExpiresAt time.Time `json:"expires_at" gorm:"index;not null"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	IsUsed    bool      `json:"is_used" gorm:"default:false"`
}

// GenerateToken cria um novo token para uma filial específica
func GenerateToken(branchID uint) (*BranchAuthToken, error) {
	// Gerar 32 bytes de dados aleatórios
	tokenBytes := make([]byte, 32)
	if _, err := rand.Read(tokenBytes); err != nil {
		return nil, err
	}

	// Converter para string hexadecimal
	tokenString := hex.EncodeToString(tokenBytes)

	// Criar o token com validade de 24 horas
	return &BranchAuthToken{
		BranchID:  branchID,
		Token:     tokenString,
		ExpiresAt: time.Now().Add(24 * time.Hour),
		IsUsed:    false,
	}, nil
}

// IsExpired verifica se o token está expirado
func (t *BranchAuthToken) IsExpired() bool {
	return time.Now().After(t.ExpiresAt)
}

// IsValid verifica se o token é válido (não expirado e não usado)
func (t *BranchAuthToken) IsValid() bool {
	return !t.IsExpired() && !t.IsUsed
}
