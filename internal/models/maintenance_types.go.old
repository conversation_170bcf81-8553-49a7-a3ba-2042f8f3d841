package models

import "time"

// MaintenanceStatus é um tipo para status de ordens de manutenção
type MaintenanceStatus string

// Usamos as constantes já definidas em maintenance_order.go
// StatusPending, StatusInProgress, StatusCompleted, StatusCanceled

// Alias para compatibilidade
const StatusCancelled = StatusCanceled

// MaintenanceType é um tipo para o tipo de manutenção
type MaintenanceType string

// Constantes para tipos de manutenção
const (
    TypePreventive    MaintenanceType = "preventiva"
    TypeCorrective    MaintenanceType = "corretiva"
    TypeInspection    MaintenanceType = "inspecao"
    TypeCalibration   MaintenanceType = "calibragem"
    TypeInstallation  MaintenanceType = "instalacao"
)

// MaintenancePriority é um tipo para prioridades de ordens de manutenção
type MaintenancePriority string

// Constantes para prioridades de manutenção
const (
    PriorityLow      MaintenancePriority = "baixa"
    PriorityMedium   MaintenancePriority = "media"
    PriorityHigh     MaintenancePriority = "alta"
    PriorityCritical MaintenancePriority = "critica"
)

// MaintenanceNote representa uma anotação em uma ordem de manutenção (modelo antigo)
type MaintenanceNote struct {
    ID        int64  `json:"id"`
    OrderID   int64  `json:"order_id"`
    UserID    int64  `json:"user_id"`
    UserName  string `json:"user_name"`
    Content   string `json:"content"`
    CreatedAt string `json:"created_at"`
}

// MaintenanceMaterial representa um material usado em uma ordem de manutenção (modelo antigo)
type MaintenanceMaterial struct {
    ID          int64     `json:"id"`
    OrderID     int64     `json:"order_id"`
    Name        string    `json:"name"`
    Quantity    int       `json:"quantity"`
    UnitCost    float64   `json:"unit_cost"`
    Total       float64   `json:"total"`
    Cost        *float64  `json:"cost"`
    Unit        string    `json:"unit"`
    AddedBy     int64     `json:"added_by"`
    AddedByName string    `json:"added_by_name"`
    CreatedAt   time.Time `json:"created_at"`
}