package models

import "time"

// OrderPhoto representa uma foto associada a uma ordem de manutenção
type OrderPhoto struct {
	ID          uint      `json:"id"`
	OrderID     uint      `json:"order_id"`
	FilePath    string    `json:"file_path"`
	FileName    string    `json:"file_name"`
	FileSize    int64     `json:"file_size"`
	ContentType string    `json:"content_type"`
	Caption     string    `json:"caption"`
	UploadedBy  uint      `json:"uploaded_by"`
	CreatedAt   time.Time `json:"created_at"`
}

// PhotoGorm representa o modelo GORM para fotos de ordem
type PhotoGorm struct {
	ID          uint      `gorm:"primaryKey"`
	OrderID     uint      `gorm:"index;not null"`
	FilePath    string    `gorm:"size:255;not null"`
	FileName    string    `gorm:"size:100;not null"`
	FileSize    int64     `gorm:"not null"`
	ContentType string    `gorm:"size:50;not null"`
	Caption     string    `gorm:"size:255"`
	UploadedBy  uint      `gorm:"not null"`
	CreatedAt   time.Time `gorm:"not null"`
}

// TableName define o nome da tabela para o modelo GORM
func (PhotoGorm) TableName() string {
	return "maintenance_order_photos"
}

// ToPhoto converte o modelo GORM para o modelo API
func (p PhotoGorm) ToPhoto() OrderPhoto {
	return OrderPhoto{
		ID:          p.ID,
		OrderID:     p.OrderID,
		FilePath:    p.FilePath,
		FileName:    p.FileName,
		FileSize:    p.FileSize,
		ContentType: p.ContentType,
		Caption:     p.Caption,
		UploadedBy:  p.UploadedBy,
		CreatedAt:   p.CreatedAt,
	}
}

// FromPhoto converte o modelo API para o modelo GORM
func FromPhoto(p OrderPhoto) PhotoGorm {
	return PhotoGorm{
		ID:          p.ID,
		OrderID:     p.OrderID,
		FilePath:    p.FilePath,
		FileName:    p.FileName,
		FileSize:    p.FileSize,
		ContentType: p.ContentType,
		Caption:     p.Caption,
		UploadedBy:  p.UploadedBy,
		CreatedAt:   p.CreatedAt,
	}
}
