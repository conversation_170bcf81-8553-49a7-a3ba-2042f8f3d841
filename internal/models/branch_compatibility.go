package models

// Este arquivo contém funções de compatibilidade para os modelos de filial.
// Ele fornece funções para converter entre os diferentes modelos de filial
// (Branch, Filial, Station) e para verificar se uma filial é válida.

// BranchToFilial converte Branch para Filial
// Deprecated: Use Branch diretamente em vez de converter para Filial
func BranchToFilial(b *Branch) *Filial {
	if b == nil {
		return nil
	}

	var branchID *uint
	if b.ID > 0 {
		branchID = &b.ID
	}

	return &Filial{
		ID:              b.ID,
		Name:            b.Name,
		Code:            b.Code,
		Address:         b.Address,
		City:            b.City,
		State:           b.State,
		ZipCode:         b.ZipCode,
		Phone:           b.Phone,
		Email:           b.Email,
		IsActive:        b.IsActive,
		FilialType:      b.BranchType,
		ResponsibleName: b.ResponsibleName,
		Notes:           b.Notes,
		BranchID:        branchID,
		CreatedAt:       b.CreatedAt,
		UpdatedAt:       b.UpdatedAt,
		DeletedAt:       b.DeletedAt,
		Type:            b.Type,
		PostalCode:      b.PostalCode,
		PhoneNumber:     b.PhoneNumber,
		Latitude:        b.Latitude,
		Longitude:       b.Longitude,
		ManagerID:       b.ManagerID,
		ManagerName:     b.ManagerName,
		OpeningHours:    b.OpeningHours,
	}
}

// FilialToBranch converte Filial para Branch
// Deprecated: Use Branch diretamente em vez de converter de Filial
func FilialToBranch(f *Filial) *Branch {
	if f == nil {
		return nil
	}

	return &Branch{
		ID:              f.ID,
		Name:            f.Name,
		Code:            f.Code,
		Address:         f.Address,
		City:            f.City,
		State:           f.State,
		ZipCode:         f.ZipCode,
		Phone:           f.Phone,
		Email:           f.Email,
		IsActive:        f.IsActive,
		BranchType:      f.FilialType,
		ResponsibleName: f.ResponsibleName,
		Notes:           f.Notes,
		ContactInfo:     f.Notes,
		CreatedAt:       f.CreatedAt,
		UpdatedAt:       f.UpdatedAt,
		DeletedAt:       f.DeletedAt,
		Type:            f.Type,
		PostalCode:      f.PostalCode,
		PhoneNumber:     f.PhoneNumber,
		Latitude:        f.Latitude,
		Longitude:       f.Longitude,
		ManagerID:       f.ManagerID,
		ManagerName:     f.ManagerName,
		OpeningHours:    f.OpeningHours,
	}
}

// BranchToStation converte Branch para Station
// Deprecated: Use Branch diretamente em vez de converter para Station
func BranchToStation(b *Branch) *Station {
	if b == nil {
		return nil
	}

	filial := BranchToFilial(b)
	return filial.ToStation()
}

// StationToBranch converte Station para Branch
// Deprecated: Use Branch diretamente em vez de converter de Station
func StationToBranch(s *Station) *Branch {
	if s == nil {
		return nil
	}

	filial := FromStation(s)
	return FilialToBranch(filial)
}

// BranchModelToBranch converte BranchModel para Branch
// Deprecated: Use Branch diretamente em vez de converter de BranchModel
func BranchModelToBranch(bm *BranchModel) *Branch {
	if bm == nil {
		return nil
	}

	return &Branch{
		ID:          bm.ID,
		Name:        bm.Name,
		Address:     bm.Address,
		City:        bm.City,
		State:       bm.State,
		ZipCode:     bm.ZipCode,
		Phone:       bm.Phone,
		Email:       bm.Email,
		ContactInfo: bm.ContactInfo,
		IsActive:    bm.IsActive,
	}
}

// BranchToBranchModel converte Branch para BranchModel
// Deprecated: Use Branch diretamente em vez de converter para BranchModel
func BranchToBranchModel(b *Branch) *BranchModel {
	if b == nil {
		return nil
	}

	return &BranchModel{
		ID:          b.ID,
		Name:        b.Name,
		Address:     b.Address,
		City:        b.City,
		State:       b.State,
		ZipCode:     b.ZipCode,
		Phone:       b.Phone,
		Email:       b.Email,
		ContactInfo: b.ContactInfo,
		IsActive:    b.IsActive,
	}
}

// BranchSummaryToBranch converte BranchSummary para Branch
// Deprecated: Use Branch diretamente em vez de converter de BranchSummary
func BranchSummaryToBranch(bs *BranchSummary) *Branch {
	if bs == nil {
		return nil
	}

	return &Branch{
		ID:       bs.ID,
		Name:     bs.Name,
		Code:     bs.Code,
		City:     bs.City,
		State:    bs.State,
		IsActive: bs.IsActive,
		Type:     bs.Type,
		Address:  bs.Address,
	}
}

// IsBranch verifica se um ID de filial é válido
func IsBranch(id uint) bool {
	return id > 0
}

// NormalizeBranchType normaliza o tipo de filial
func NormalizeBranchType(branchType string) string {
	switch branchType {
	case "urbano", "urban":
		return "urban"
	case "rodovia", "highway":
		return "highway"
	case "rural":
		return "rural"
	case "industrial":
		return "industrial"
	default:
		return "urban" // Valor padrão
	}
}

// GetBranchTypeDisplay retorna o texto em português para exibição do tipo de filial
func GetBranchTypeDisplay(branchType string) string {
	switch NormalizeBranchType(branchType) {
	case "urban":
		return "Urbano"
	case "highway":
		return "Rodovia"
	case "rural":
		return "Rural"
	case "industrial":
		return "Industrial"
	default:
		return branchType
	}
}
