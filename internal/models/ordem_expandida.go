package models

import (
	"time"
)

// OrdemManutencaoExpandida representa uma ordem com campos adicionais e relacionamentos
type OrdemManutencaoExpandida struct {
	ID              uint            `json:"id"`
	Titulo          string          `json:"titulo"`
	Descricao       string          `json:"descricao"`
	FilialID        uint            `json:"filial_id"`
	FilialNome      string          `json:"filial_nome"`
	EquipamentoID   uint            `json:"equipamento_id"`
	EquipamentoNome string          `json:"equipamento_nome"`
	Status          StatusOrdem     `json:"status"`
	Prioridade      PrioridadeOrdem `json:"prioridade"`
	Tipo            string          `json:"tipo"`
	SolicitanteID   uint            `json:"solicitante_id"`
	SolicitanteNome string          `json:"solicitante_nome"`
	TecnicoID       *uint           `json:"tecnico_id,omitempty"`
	TecnicoNome     *string         `json:"tecnico_nome,omitempty"`
	DataAbertura    time.Time       `json:"data_abertura"`
	DataDesejada    *time.Time      `json:"data_desejada,omitempty"`
	DataInicio      *time.Time      `json:"data_inicio,omitempty"`
	DataConclusao   *time.Time      `json:"data_conclusao,omitempty"`
	TempoEstimado   *int            `json:"tempo_estimado,omitempty"`
	TempoReal       *int            `json:"tempo_real,omitempty"`
	Observacoes     string          `json:"observacoes"`
	CreatedAt       time.Time       `json:"created_at"`
	UpdatedAt       time.Time       `json:"updated_at"`

	// Relacionamentos
	Historico []HistoricoOrdem `json:"historico,omitempty"`
	Fotos     []FotoOrdem      `json:"fotos,omitempty"`
}
