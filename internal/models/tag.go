package models

import (
	"time"

	"gorm.io/gorm"
)

// TagCategory representa uma categoria de tags
type TagCategory struct {
	ID          int64          `json:"id" db:"id"`
	Name        string         `json:"name" db:"name"`
	Description string         `json:"description" db:"description"`
	CreatedAt   time.Time      `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at" db:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// Tag representa uma tag que pode ser associada a equipamentos
type Tag struct {
	ID          int64          `json:"id" db:"id"`
	Name        string         `json:"name" db:"name"`
	Description string         `json:"description" db:"description"`
	CategoryID  int64          `json:"category_id" db:"category_id"`
	Color       string         `json:"color" db:"color"`
	CreatedAt   time.Time      `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at" db:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
	Category    TagCategory    `json:"category" gorm:"foreignKey:CategoryID"`
}

// EquipmentTag representa o relacionamento entre equipamentos e tags
type EquipmentTag struct {
	EquipmentID int64     `json:"equipment_id" db:"equipment_id"`
	TagID       int64     `json:"tag_id" db:"tag_id"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	Equipment   Equipment `json:"equipment" gorm:"foreignKey:EquipmentID"`
	Tag         Tag       `json:"tag" gorm:"foreignKey:TagID"`
}

// TableName especifica o nome da tabela para TagCategory
func (TagCategory) TableName() string {
	return "tag_categories"
}

// TableName especifica o nome da tabela para Tag
func (Tag) TableName() string {
	return "tags"
}

// TableName especifica o nome da tabela para EquipmentTag
func (EquipmentTag) TableName() string {
	return "equipment_tags"
}
