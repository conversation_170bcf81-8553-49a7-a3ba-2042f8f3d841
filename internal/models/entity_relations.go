package models

// Este arquivo contém definições padronizadas de relações entre entidades.
// Ele define constantes para nomes de chaves estrangeiras, tabelas de junção
// e outras informações relacionadas às relações entre entidades.

// Constantes para nomes de chaves estrangeiras
const (
	// Chaves estrangeiras relacionadas a Branch
	FKBranchID       = "branch_id"       // Chave estrangeira para Branch
	FKManagerID      = "manager_id"      // Chave estrangeira para gerente (User)
	
	// Chaves estrangeiras relacionadas a User
	FKUserID         = "user_id"         // Chave estrangeira para User
	FKRequesterID    = "requester_id"    // Chave estrangeira para solicitante (User)
	FKTechnicianID   = "technician_id"   // Chave estrangeira para técnico (User)
	FKApproverID     = "approver_id"     // Chave estrangeira para aprovador (User)
	FKCreatedByID    = "created_by_id"   // Chave estrangeira para criador (User)
	FKAssignedToID   = "assigned_to_id"  // Chave estrangeira para responsável (User)
	
	// Chaves estrangeiras relacionadas a Equipment
	FKEquipmentID    = "equipment_id"    // Chave estrangeira para Equipment
	FKEquipmentTypeID = "equipment_type_id" // Chave estrangeira para EquipmentType
	
	// Chaves estrangeiras relacionadas a MaintenanceOrder
	FKOrderID        = "order_id"        // Chave estrangeira para MaintenanceOrder
	
	// Chaves estrangeiras relacionadas a ServiceProvider
	FKProviderID     = "provider_id"     // Chave estrangeira para ServiceProvider
)

// Constantes para nomes de tabelas de junção
const (
	// Tabelas de junção relacionadas a Branch
	JoinTableTechnicianBranches = "technician_branches" // Tabela de junção entre técnicos e filiais
	JoinTableProviderBranches   = "provider_branches"   // Tabela de junção entre prestadores e filiais
	
	// Tabelas de junção relacionadas a Equipment
	JoinTableEquipmentTags      = "equipment_tags"      // Tabela de junção entre equipamentos e tags
)

// Constantes para nomes de campos de relação
const (
	// Campos de relação em Branch
	RelFieldManager         = "Manager"         // Relação com gerente (User)
	RelFieldEquipment       = "Equipment"       // Relação com equipamentos
	RelFieldMaintenanceOrders = "MaintenanceOrders" // Relação com ordens de manutenção
	RelFieldTechnicians     = "Technicians"     // Relação com técnicos
	RelFieldServiceProviders = "ServiceProviders" // Relação com prestadores
	
	// Campos de relação em Equipment
	RelFieldBranch          = "Branch"          // Relação com filial
	RelFieldEquipmentType   = "EquipmentType"   // Relação com tipo de equipamento
	RelFieldMaintenanceOrdersEquip = "MaintenanceOrders" // Relação com ordens de manutenção
	RelFieldTags            = "Tags"            // Relação com tags
	
	// Campos de relação em MaintenanceOrder
	RelFieldBranchOrder     = "Branch"          // Relação com filial
	RelFieldEquipmentOrder  = "Equipment"       // Relação com equipamento
	RelFieldRequester       = "Requester"       // Relação com solicitante
	RelFieldTechnician      = "Technician"      // Relação com técnico
	RelFieldApprover        = "Approver"        // Relação com aprovador
	RelFieldCreatedBy       = "CreatedBy"       // Relação com criador
	RelFieldAssignedTo      = "AssignedTo"      // Relação com responsável
	RelFieldCostItems       = "CostItems"       // Relação com itens de custo
	RelFieldInteractions    = "Interactions"    // Relação com interações
	
	// Campos de relação em User
	RelFieldManagedBranches = "ManagedBranches" // Relação com filiais gerenciadas
	RelFieldBranchUser      = "Branch"          // Relação com filial
	RelFieldRequestedOrders = "RequestedOrders" // Relação com ordens solicitadas
	RelFieldAssignedOrders  = "AssignedOrders"  // Relação com ordens atribuídas
	RelFieldApprovedOrders  = "ApprovedOrders"  // Relação com ordens aprovadas
)

// Definições de relações GORM para Branch
var BranchRelations = map[string]string{
	RelFieldManager:         "foreignKey:" + FKManagerID,
	RelFieldEquipment:       "foreignKey:" + FKBranchID,
	RelFieldMaintenanceOrders: "foreignKey:" + FKBranchID,
	RelFieldTechnicians:     "many2many:" + JoinTableTechnicianBranches,
	RelFieldServiceProviders: "many2many:" + JoinTableProviderBranches,
}

// Definições de relações GORM para Equipment
var EquipmentRelations = map[string]string{
	RelFieldBranch:          "foreignKey:" + FKBranchID,
	RelFieldEquipmentType:   "foreignKey:" + FKEquipmentTypeID,
	RelFieldMaintenanceOrdersEquip: "foreignKey:" + FKEquipmentID,
	RelFieldTags:            "many2many:" + JoinTableEquipmentTags,
}

// Definições de relações GORM para MaintenanceOrder
var MaintenanceOrderRelations = map[string]string{
	RelFieldBranchOrder:     "foreignKey:" + FKBranchID,
	RelFieldEquipmentOrder:  "foreignKey:" + FKEquipmentID,
	RelFieldRequester:       "foreignKey:" + FKRequesterID,
	RelFieldTechnician:      "foreignKey:" + FKTechnicianID,
	RelFieldApprover:        "foreignKey:" + FKApproverID,
	RelFieldCreatedBy:       "foreignKey:" + FKCreatedByID,
	RelFieldAssignedTo:      "foreignKey:" + FKAssignedToID,
	RelFieldCostItems:       "foreignKey:" + FKOrderID,
	RelFieldInteractions:    "foreignKey:" + FKOrderID,
}

// Definições de relações GORM para User
var UserRelations = map[string]string{
	RelFieldManagedBranches: "foreignKey:" + FKManagerID,
	RelFieldBranchUser:      "foreignKey:" + FKBranchID,
	RelFieldRequestedOrders: "foreignKey:" + FKRequesterID,
	RelFieldAssignedOrders:  "foreignKey:" + FKTechnicianID,
	RelFieldApprovedOrders:  "foreignKey:" + FKApproverID,
}

// GetRelationDefinition retorna a definição GORM para uma relação específica
func GetRelationDefinition(entityType string, relationField string) string {
	switch entityType {
	case "Branch":
		return BranchRelations[relationField]
	case "Equipment":
		return EquipmentRelations[relationField]
	case "MaintenanceOrder":
		return MaintenanceOrderRelations[relationField]
	case "User":
		return UserRelations[relationField]
	default:
		return ""
	}
}

// GetForeignKeyName retorna o nome da chave estrangeira para uma relação específica
func GetForeignKeyName(entityType string, relationField string) string {
	switch entityType {
	case "Branch":
		switch relationField {
		case RelFieldManager:
			return FKManagerID
		default:
			return ""
		}
	case "Equipment":
		switch relationField {
		case RelFieldBranch:
			return FKBranchID
		case RelFieldEquipmentType:
			return FKEquipmentTypeID
		default:
			return ""
		}
	case "MaintenanceOrder":
		switch relationField {
		case RelFieldBranchOrder:
			return FKBranchID
		case RelFieldEquipmentOrder:
			return FKEquipmentID
		case RelFieldRequester:
			return FKRequesterID
		case RelFieldTechnician:
			return FKTechnicianID
		case RelFieldApprover:
			return FKApproverID
		case RelFieldCreatedBy:
			return FKCreatedByID
		case RelFieldAssignedTo:
			return FKAssignedToID
		default:
			return ""
		}
	case "User":
		switch relationField {
		case RelFieldBranchUser:
			return FKBranchID
		default:
			return ""
		}
	default:
		return ""
	}
}
