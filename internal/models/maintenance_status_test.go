package models

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNormalizeOrderStatus(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected OrderStatus
	}{
		{
			name:     "Legacy pending status",
			input:    string(StatusPendente),
			expected: StatusPending,
		},
		{
			name:     "Legacy in progress status",
			input:    string(StatusEmAndamento),
			expected: StatusInProgress,
		},
		{
			name:     "Legacy completed status",
			input:    string(StatusConcluida),
			expected: StatusCompleted,
		},
		{
			name:     "Legacy cancelled status",
			input:    string(StatusCancelada),
			expected: StatusCancelled,
		},
		{
			name:     "Standard pending status",
			input:    string(StatusPending),
			expected: StatusPending,
		},
		{
			name:     "Standard in progress status",
			input:    string(StatusInProgress),
			expected: StatusInProgress,
		},
		{
			name:     "Unknown status",
			input:    "unknown_status",
			expected: StatusPending, // Default to pending
		},
		{
			name:     "Empty status",
			input:    "",
			expected: StatusPending, // Default to pending
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NormalizeOrderStatus(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestNormalizePriorityLevel(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected PriorityLevel
	}{
		{
			name:     "Legacy low priority",
			input:    string(PrioridadeBaixa),
			expected: PriorityLow,
		},
		{
			name:     "Legacy medium priority",
			input:    string(PrioridadeMedia),
			expected: PriorityMedium,
		},
		{
			name:     "Legacy high priority",
			input:    string(PrioridadeAlta),
			expected: PriorityHigh,
		},
		{
			name:     "Legacy critical priority",
			input:    string(PrioridadeCritica),
			expected: PriorityCritical,
		},
		{
			name:     "Standard low priority",
			input:    string(PriorityLow),
			expected: PriorityLow,
		},
		{
			name:     "Standard medium priority",
			input:    string(PriorityMedium),
			expected: PriorityMedium,
		},
		{
			name:     "Unknown priority",
			input:    "unknown_priority",
			expected: PriorityMedium, // Default to medium
		},
		{
			name:     "Empty priority",
			input:    "",
			expected: PriorityMedium, // Default to medium
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NormalizePriorityLevel(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetOrderStatusDisplay(t *testing.T) {
	tests := []struct {
		name     string
		input    OrderStatus
		expected string
	}{
		{
			name:     "Pending status",
			input:    StatusPending,
			expected: "Pendente",
		},
		{
			name:     "In progress status",
			input:    StatusInProgress,
			expected: "Em Andamento",
		},
		{
			name:     "Completed status",
			input:    StatusCompleted,
			expected: "Concluída",
		},
		{
			name:     "Cancelled status",
			input:    StatusCancelled,
			expected: "Cancelada",
		},
		{
			name:     "Unknown status",
			input:    "unknown_status",
			expected: "unknown_status", // Return as is
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetOrderStatusDisplay(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetPriorityLevelDisplay(t *testing.T) {
	tests := []struct {
		name     string
		input    PriorityLevel
		expected string
	}{
		{
			name:     "Low priority",
			input:    PriorityLow,
			expected: "Baixa",
		},
		{
			name:     "Medium priority",
			input:    PriorityMedium,
			expected: "Média",
		},
		{
			name:     "High priority",
			input:    PriorityHigh,
			expected: "Alta",
		},
		{
			name:     "Critical priority",
			input:    PriorityCritical,
			expected: "Crítica",
		},
		{
			name:     "Unknown priority",
			input:    "unknown_priority",
			expected: "unknown_priority", // Return as is
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetPriorityLevelDisplay(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestIsStandardOrderStatus(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "Standard pending status",
			input:    string(StatusPending),
			expected: true,
		},
		{
			name:     "Standard in progress status",
			input:    string(StatusInProgress),
			expected: true,
		},
		{
			name:     "Legacy pending status",
			input:    string(StatusPendente),
			expected: false,
		},
		{
			name:     "Legacy in progress status",
			input:    string(StatusEmAndamento),
			expected: false,
		},
		{
			name:     "Unknown status",
			input:    "unknown_status",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsStandardOrderStatus(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestIsStandardPriorityLevel(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "Standard low priority",
			input:    string(PriorityLow),
			expected: true,
		},
		{
			name:     "Standard medium priority",
			input:    string(PriorityMedium),
			expected: true,
		},
		{
			name:     "Legacy low priority",
			input:    string(PrioridadeBaixa),
			expected: false,
		},
		{
			name:     "Legacy medium priority",
			input:    string(PrioridadeMedia),
			expected: false,
		},
		{
			name:     "Unknown priority",
			input:    "unknown_priority",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsStandardPriorityLevel(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}
