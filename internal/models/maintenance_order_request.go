package models

import "time"

// MaintenanceOrderRequest representa os dados recebidos para criar ou atualizar uma ordem de manutenção
type MaintenanceOrderRequest struct {
	Title              string          `json:"title" binding:"required"`
	Description        string          `json:"description"`
	Problem            string          `json:"problem"`
	Priority           PriorityLevel   `json:"priority" binding:"required"`
	Status             OrderStatus     `json:"status"`
	BranchID           int64           `json:"branch_id" binding:"required"`
	EquipmentID        int64           `json:"equipment_id" binding:"required"`
	ServiceProviderID  *int64          `json:"service_provider_id"`
	PartialFunctioning bool            `json:"partial_functioning"`
	ExtraEquipment     bool            `json:"extra_equipment"`
	SameDay            bool            `json:"same_day"`
	PartReplacement    bool            `json:"part_replacement"`
	EstimatedCost      *float64        `json:"estimated_cost"`
	DueDate            *time.Time      `json:"due_date"`
	Type               MaintenanceType `json:"type" binding:"required"`
}

// StatusUpdateRequest contém os dados necessários para atualizar o status de uma ordem
type StatusUpdateRequest struct {
	Status        OrderStatus `json:"status" binding:"required"`
	Notes         string      `json:"notes"`
	CompletedDate *time.Time  `json:"completed_date"`
	ActualTime    *int        `json:"actual_time"`
}

// PriorityUpdateRequest contém os dados necessários para atualizar a prioridade de uma ordem
type PriorityUpdateRequest struct {
	Priority PriorityLevel `json:"priority" binding:"required"`
	Notes    string        `json:"notes"`
}

// MaterialRequest contém os dados necessários para adicionar um material a uma ordem
type MaterialRequest struct {
	Name     string   `json:"name" binding:"required"`
	Quantity int      `json:"quantity" binding:"required"`
	UnitCost float64  `json:"unit_cost" binding:"required"`
	Unit     string   `json:"unit" binding:"required"`
	Cost     *float64 `json:"cost"`
}

// NoteRequest contém os dados necessários para adicionar uma nota a uma ordem
type NoteRequest struct {
	Content string `json:"content" binding:"required"`
}
