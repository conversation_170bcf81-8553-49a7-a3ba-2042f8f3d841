package models

// LoginRequest contém os dados necessários para autenticação
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
	TOTPCode string `json:"totp_code,omitempty"` // Código TOTP opcional
}

// LoginResponse contém os dados de resposta para o login
type LoginResponse struct {
	Token                  string       `json:"token,omitempty"`
	User                   UserResponse `json:"user"`
	Redirect               string       `json:"redirect,omitempty"`
	RequiresTOTP           bool         `json:"requires_totp,omitempty"`            // Indica se ainda precisa validar TOTP
	RequiresPasswordChange bool         `json:"requires_password_change,omitempty"` // Indica se precisa trocar a senha
}

// TOTPSetupResponse representa a resposta ao configurar o TOTP
type TOTPSetupResponse struct {
	Secret        string   `json:"secret"`
	QRCodeURL     string   `json:"qr_code_url"`
	RecoveryCodes []string `json:"recovery_codes,omitempty"`
}

// NewUserResponse contém os dados de resposta para criação de usuário
type NewUserResponse struct {
	User  UserResponse `json:"user"`
	Token string       `json:"token,omitempty"`
}
