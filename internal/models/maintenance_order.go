package models

import (
	"time"

	"gorm.io/gorm"
)

// MaintenanceOrder representa uma ordem de manutenção
type MaintenanceOrder struct {
	ID                 uint          `gorm:"primaryKey" json:"id"`
	BranchID           uint          `gorm:"column:branch_id" json:"branch_id"`
	BranchName         string        `gorm:"-" json:"branch_name"` // Campo virtual para nome da filial
	EquipmentID        uint          `gorm:"column:equipment_id" json:"equipment_id"`
	ServiceProviderID  *uint         `gorm:"column:service_provider_id" json:"service_provider_id,omitempty"`
	TechnicianID       *uint         `json:"technician_id" gorm:"column:technician_id"`
	CreatedByUserID    uint          `gorm:"column:created_by_user_id" json:"created_by_user_id"`
	CreatedByName      string        `gorm:"-" json:"created_by_name"` // Campo virtual para nome do criador
	Title              string        `gorm:"column:title" json:"title"`
	Description        string        `gorm:"column:description" json:"description"`
	Number             string        `gorm:"column:number" json:"number"`
	Problem            string        `gorm:"column:problem" json:"problem"`
	Status             OrderStatus   `gorm:"column:status" json:"status"`
	Priority           PriorityLevel `gorm:"column:priority" json:"priority"`
	EstimatedCost      float64       `gorm:"column:estimated_cost" json:"estimated_cost"`
	ActualCost         float64       `gorm:"column:actual_cost" json:"actual_cost"`
	DueDate            time.Time     `gorm:"column:due_date" json:"due_date"`
	OpenDate           time.Time     `gorm:"column:open_date" json:"open_date"`
	CompletionDate     *time.Time    `gorm:"column:completion_date" json:"completion_date"`
	ApprovalStatus     string        `gorm:"column:approval_status" json:"approval_status"`
	ApprovalDate       *time.Time    `gorm:"column:approval_date" json:"approval_date"`
	PaymentStatus      string        `gorm:"column:payment_status" json:"payment_status"`
	PaymentDate        *time.Time    `gorm:"column:payment_date" json:"payment_date"`
	Rating             *uint         `gorm:"column:rating" json:"rating"`
	PartialFunctioning bool          `gorm:"column:partial_functioning" json:"partial_functioning"`
	ExtraEquipment     bool          `gorm:"column:extra_equipment" json:"extra_equipment"`
	SameDay            bool          `gorm:"column:same_day" json:"same_day"`
	PartReplacement    bool          `gorm:"column:part_replacement" json:"part_replacement"`
	CreatedAt          time.Time     `gorm:"column:created_at" json:"created_at"`
	UpdatedAt          time.Time     `gorm:"column:updated_at" json:"updated_at"`
	DeletedAt          *time.Time    `gorm:"column:deleted_at" json:"deleted_at,omitempty"`
	AssignedToUserID   uint          `gorm:"column:assigned_to_user_id" json:"assigned_to_user_id"`
	EquipmentName      string        `gorm:"-" json:"equipment_name"`

	// Relações com outras entidades
	// Estas relações seguem o padrão definido em entity_relations.go
	Branch          *Branch               `json:"branch,omitempty" gorm:"foreignKey:BranchID"`                    // Filial à qual a ordem pertence
	Equipment       *Equipment            `json:"equipment,omitempty" gorm:"foreignKey:EquipmentID"`              // Equipamento associado à ordem
	Requester       *User                 `json:"requester,omitempty" gorm:"foreignKey:CreatedByUserID"`          // Usuário que criou a ordem
	Technician      *User                 `json:"technician,omitempty" gorm:"foreignKey:TechnicianID"`            // Técnico responsável pela ordem
	ServiceProvider *ServiceProvider      `json:"service_provider,omitempty" gorm:"foreignKey:ServiceProviderID"` // Prestador responsável pela ordem
	AssignedTo      *User                 `json:"assigned_to,omitempty" gorm:"foreignKey:AssignedToUserID"`       // Usuário responsável pela ordem
	Materials       []MaintenanceMaterial `json:"materials,omitempty" gorm:"foreignKey:OrderID"`                  // Materiais usados na ordem
	Notes           []MaintenanceNote     `json:"notes,omitempty" gorm:"foreignKey:OrderID"`                      // Notas da ordem
}

// MaintenanceMaterial representa um material usado em uma ordem de manutenção
type MaintenanceMaterial struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	OrderID     uint      `gorm:"column:order_id" json:"order_id"`
	Name        string    `gorm:"column:name" json:"name"`
	Description string    `gorm:"column:description" json:"description"`
	Quantity    int       `gorm:"column:quantity" json:"quantity"`
	UnitPrice   float64   `gorm:"column:unit_price" json:"unit_price"`
	TotalPrice  float64   `gorm:"column:total_price" json:"total_price"`
	CreatedAt   time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"updated_at"`
}

// MaintenanceNote representa uma nota em uma ordem de manutenção
type MaintenanceNote struct {
	ID        uint      `gorm:"primaryKey" json:"id"`
	OrderID   uint      `gorm:"column:order_id" json:"order_id"`
	Content   string    `gorm:"column:content" json:"content"`
	CreatedAt time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updated_at"`
}

// NewMaintenanceOrder cria uma nova ordem de manutenção
func NewMaintenanceOrder(branchID uint, problem string, priority PriorityLevel, dueDate time.Time) *MaintenanceOrder {
	now := time.Now()
	return &MaintenanceOrder{
		// ID será gerado automaticamente pelo banco de dados
		BranchID:      branchID,
		Problem:       problem,
		Status:        StatusPending,
		Priority:      priority,
		DueDate:       dueDate,
		EstimatedCost: 0,
		ActualCost:    0,
		OpenDate:      now,
		CreatedAt:     now,
		UpdatedAt:     now,
	}
}

// NewMaintenanceMaterial cria um novo material para uma ordem de manutenção
func NewMaintenanceMaterial(orderID uint, name, description string, quantity int, unitPrice float64) *MaintenanceMaterial {
	return &MaintenanceMaterial{
		// ID será gerado automaticamente pelo banco de dados
		OrderID:     orderID,
		Name:        name,
		Description: description,
		Quantity:    quantity,
		UnitPrice:   unitPrice,
		TotalPrice:  float64(quantity) * unitPrice,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
}

// NewMaintenanceNote cria uma nova nota para uma ordem de manutenção
func NewMaintenanceNote(orderID uint, content string) *MaintenanceNote {
	return &MaintenanceNote{
		// ID será gerado automaticamente pelo banco de dados
		OrderID:   orderID,
		Content:   content,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
}

// TableName retorna o nome da tabela no banco de dados
func (MaintenanceOrder) TableName() string {
	return TableMaintenanceOrders
}

// BeforeCreate é chamado antes de criar um novo registro
func (m *MaintenanceOrder) BeforeCreate(tx *gorm.DB) error {
	if m.CreatedAt.IsZero() {
		m.CreatedAt = time.Now()
	}
	m.UpdatedAt = time.Now()
	return nil
}

// BeforeUpdate é chamado antes de atualizar um registro
func (m *MaintenanceOrder) BeforeUpdate(tx *gorm.DB) error {
	m.UpdatedAt = time.Now()
	return nil
}

// GetIDAsUint retorna o ID como uint (já está no formato correto)
func (m *MaintenanceOrder) GetIDAsUint() uint {
	return m.ID
}

// GetIDAsInt64 converte o ID para int64
func (m *MaintenanceOrder) GetIDAsInt64() int64 {
	return int64(m.ID)
}

// ToAPIResponse converte o modelo para o formato de resposta da API
func (m *MaintenanceOrder) ToAPIResponse() map[string]any {
	return map[string]any{
		"id":                  m.ID,
		"number":              m.Number,
		"title":               m.Title,
		"description":         m.Description,
		"problem":             m.Problem,
		"status":              m.Status,
		"priority":            m.Priority,
		"branch_id":           m.BranchID,
		"branch_name":         m.BranchName,
		"equipment_id":        m.EquipmentID,
		"equipment_name":      m.EquipmentName,
		"technician_id":       m.TechnicianID,
		"service_provider_id": m.ServiceProviderID,
		"created_by_user_id":  m.CreatedByUserID,
		"created_by_name":     m.CreatedByName,
		"due_date":            m.DueDate.Format("2006-01-02"),
		"open_date":           m.OpenDate.Format("2006-01-02"),
		"completion_date":     formatNullableDate(m.CompletionDate),
		"estimated_cost":      m.EstimatedCost,
		"actual_cost":         m.ActualCost,
		"created_at":          m.CreatedAt.Format(time.RFC3339),
		"updated_at":          m.UpdatedAt.Format(time.RFC3339),
		"status_display":      GetOrderStatusDisplay(m.Status),
		"priority_display":    GetPriorityLevelDisplay(m.Priority),
	}
}

// FromLegacyOrdem converte um modelo legado para o modelo padronizado
func FromLegacyOrdem(ordem *OrdemManutencaoExpandida) *MaintenanceOrder {
	if ordem == nil {
		return nil
	}

	status := NormalizeOrderStatus(string(ordem.Status))
	priority := NormalizePriorityLevel(string(ordem.Prioridade))

	return &MaintenanceOrder{
		ID:              ordem.ID,
		Title:           ordem.Titulo,
		Description:     ordem.Descricao,
		BranchID:        ordem.FilialID,
		BranchName:      ordem.FilialNome,
		EquipmentID:     ordem.EquipamentoID,
		EquipmentName:   ordem.EquipamentoNome,
		Status:          status,
		Priority:        priority,
		CreatedByUserID: ordem.SolicitanteID,
		CreatedByName:   ordem.SolicitanteNome,
		TechnicianID:    ordem.TecnicoID,
		OpenDate:        ordem.DataAbertura,
		DueDate:         time.Now().AddDate(0, 0, 7), // Valor padrão: 7 dias a partir de hoje
		CompletionDate:  ordem.DataConclusao,
		CreatedAt:       ordem.CreatedAt,
		UpdatedAt:       ordem.UpdatedAt,
	}
}

// BeforeSave é chamado antes de salvar o registro no banco
func (m *MaintenanceOrder) BeforeSave(tx *gorm.DB) error {
	// Normalizar status e prioridade para garantir que estejam no formato padronizado
	m.Status = NormalizeOrderStatus(string(m.Status))
	m.Priority = NormalizePriorityLevel(string(m.Priority))

	return nil
}

// Função auxiliar para formatar datas nulas
func formatNullableDate(date *time.Time) string {
	if date == nil {
		return ""
	}
	return date.Format("2006-01-02")
}
