package models

import (
	"time"

	"gorm.io/gorm"
)

// Station representa um posto/estação no sistema
// Deprecated: Use Filial em vez disso
type Station struct {
	ID              uint           `gorm:"primaryKey" json:"id"`
	Name            string         `gorm:"size:100;not null" json:"name"`
	Code            string         `gorm:"size:20;unique;not null" json:"code"`
	Address         string         `gorm:"size:255" json:"address"`
	City            string         `gorm:"size:100" json:"city"`
	State           string         `gorm:"size:2" json:"state"`
	ZipCode         string         `gorm:"size:20" json:"zip_code"`
	Phone           string         `gorm:"size:20" json:"phone"`
	Email           string         `gorm:"size:100" json:"email"`
	IsActive        bool           `gorm:"default:true" json:"is_active"`
	StationType     string         `gorm:"size:20;default:'urbano'" json:"station_type"` // urbano, rodovia, rural, industrial
	ResponsibleName string         `gorm:"size:100" json:"responsible_name"`
	Notes           string         `gorm:"size:500" json:"notes"`
	BranchID        *uint          `gorm:"default:null" json:"branch_id"` // Para compatibilidade com código legado
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `gorm:"index" json:"-"`

	// Campos adicionais para compatibilidade com o StationRepository
	Type         string   `json:"type"`
	PostalCode   string   `json:"postal_code"`
	PhoneNumber  string   `json:"phone_number"`
	Latitude     *float64 `json:"latitude"`
	Longitude    *float64 `json:"longitude"`
	ManagerID    *uint    `json:"manager_id"`
	ManagerName  string   `json:"manager_name"`
	OpeningHours string   `json:"opening_hours"`

	// Relações
	Branches []Branch `gorm:"many2many:branch_station_links;" json:"-"`
	// Removendo a relação com Equipment para evitar conflitos
}

// Nota: StationSummary foi movido para response_types.go

// TableName especifica o nome da tabela para o modelo Station
func (Station) TableName() string {
	return "stations"
}

// ToSummary converte Station para StationSummary
// Deprecated: Use Branch.ToSummary() em vez disso
// Esta função é mantida apenas para compatibilidade com código existente.
func (s *Station) ToSummary() StationSummary {
	// Converter para Branch e usar o método ToSummary do Branch
	branch := StationToBranch(s)
	if branch == nil {
		return StationSummary{}
	}

	// Converter BranchSummary para StationSummary
	return branch.ToStationSummary()
}

// StationMetrics representa métricas das estações
// Deprecated: Use FilialMetrics em vez disso
type StationMetrics struct {
	TotalStations    int64            `json:"total_stations"`
	ActiveStations   int64            `json:"active_stations"`
	StationsByRegion map[string]int64 `json:"stations_by_region"`
}

// FilialToStation converte uma Filial para Station
// Deprecated: Use Branch diretamente em vez de converter entre Filial e Station
// Esta função é mantida apenas para compatibilidade com código existente.
func FilialToStation(f *Filial) *Station {
	if f == nil {
		return nil
	}

	// Converter Filial para Branch e depois Branch para Station
	branch := FilialToBranch(f)
	return BranchToStation(branch)
}
