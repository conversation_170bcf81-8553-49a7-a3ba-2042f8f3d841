package models

import (
	"time"
)

// MaintenanceOrderEnt representa uma ordem de manutenção compatível com o repositório Ent
type MaintenanceOrderEnt struct {
	ID                 uint          `json:"id"`
	Number             string        `json:"number"`
	Problem            string        `json:"problem"`
	Status             OrderStatus   `json:"status"`
	Priority           PriorityLevel `json:"priority"`
	BranchID           uint          `json:"branch_id"`
	EquipmentID        uint          `json:"equipment_id"`
	ServiceProviderID  *uint         `json:"service_provider_id,omitempty"`
	EstimatedCost      float64       `json:"estimated_cost"`
	ActualCost         float64       `json:"actual_cost"`
	DueDate            time.Time     `json:"due_date"`
	OpenDate           time.Time     `json:"open_date"`
	CompletionDate     *time.Time    `json:"completion_date,omitempty"`
	ApprovalStatus     string        `json:"approval_status"`
	ApprovalDate       *time.Time    `json:"approval_date,omitempty"`
	PaymentStatus      string        `json:"payment_status"`
	PaymentDate        *time.Time    `json:"payment_date,omitempty"`
	Rating             *uint         `json:"rating,omitempty"`
	PartialFunctioning bool          `json:"partial_functioning"`
	ExtraEquipment     bool          `json:"extra_equipment"`
	SameDay            bool          `json:"same_day"`
	PartReplacement    bool          `json:"part_replacement"`
	CreatedAt          time.Time     `json:"created_at"`
	UpdatedAt          time.Time     `json:"updated_at"`
	DeletedAt          *time.Time    `json:"deleted_at,omitempty"`
}

// ConvertToEnt converte uma MaintenanceOrder para MaintenanceOrderEnt
func (m *MaintenanceOrder) ConvertToEnt() *MaintenanceOrderEnt {
	return &MaintenanceOrderEnt{
		ID:                 m.ID,
		Number:             m.Number,
		Problem:            m.Problem,
		Status:             m.Status,
		Priority:           m.Priority,
		BranchID:           m.BranchID,
		EquipmentID:        m.EquipmentID,
		ServiceProviderID:  m.ServiceProviderID,
		EstimatedCost:      m.EstimatedCost,
		ActualCost:         m.ActualCost,
		DueDate:            m.DueDate,
		OpenDate:           m.OpenDate,
		CompletionDate:     m.CompletionDate,
		ApprovalStatus:     m.ApprovalStatus,
		ApprovalDate:       m.ApprovalDate,
		PaymentStatus:      m.PaymentStatus,
		PaymentDate:        m.PaymentDate,
		Rating:             m.Rating,
		PartialFunctioning: m.PartialFunctioning,
		ExtraEquipment:     m.ExtraEquipment,
		SameDay:            m.SameDay,
		PartReplacement:    m.PartReplacement,
		CreatedAt:          m.CreatedAt,
		UpdatedAt:          m.UpdatedAt,
		DeletedAt:          m.DeletedAt,
	}
}
