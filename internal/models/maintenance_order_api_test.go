package models

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestMaintenanceOrderToAPIResponse(t *testing.T) {
	// Criar uma data fixa para testes
	fixedTime := time.Date(2023, 5, 15, 10, 30, 0, 0, time.UTC)
	completionTime := time.Date(2023, 5, 20, 15, 45, 0, 0, time.UTC)

	// Criar uma ordem de manutenção para teste
	order := &MaintenanceOrder{
		ID:                1,
		BranchID:          2,
		BranchName:        "Posto Central",
		EquipmentID:       3,
		EquipmentName:     "Bomba de Combustível #1",
		ServiceProviderID: new(uint),
		TechnicianID:      new(uint),
		CreatedByUserID:   4,
		CreatedByName:     "<PERSON>",
		Title:             "Manutenção Preventiva",
		Description:       "Verificação periódica do equipamento",
		Number:            "ORD-2023-001",
		Problem:           "Ruído anormal durante operação",
		Status:            StatusPending,
		Priority:          PriorityMedium,
		EstimatedCost:     150.50,
		ActualCost:        175.25,
		DueDate:           fixedTime.AddDate(0, 0, 7),
		OpenDate:          fixedTime,
		CompletionDate:    &completionTime,
		CreatedAt:         fixedTime,
		UpdatedAt:         fixedTime.Add(2 * time.Hour),
	}

	// Definir valores para os ponteiros
	*order.ServiceProviderID = 5
	*order.TechnicianID = 6

	// Chamar o método ToAPIResponse
	response := order.ToAPIResponse()

	// Verificar se todos os campos foram convertidos corretamente
	assert.Equal(t, uint(1), response["id"])
	assert.Equal(t, "ORD-2023-001", response["number"])
	assert.Equal(t, "Manutenção Preventiva", response["title"])
	assert.Equal(t, "Verificação periódica do equipamento", response["description"])
	assert.Equal(t, "Ruído anormal durante operação", response["problem"])
	assert.Equal(t, StatusPending, response["status"])
	assert.Equal(t, PriorityMedium, response["priority"])
	assert.Equal(t, uint(2), response["branch_id"])
	assert.Equal(t, "Posto Central", response["branch_name"])
	assert.Equal(t, uint(3), response["equipment_id"])
	assert.Equal(t, "Bomba de Combustível #1", response["equipment_name"])
	// Verificar apenas se os campos existem e são ponteiros para uint
	_, ok1 := response["technician_id"].(*uint)
	assert.True(t, ok1, "technician_id deve ser um ponteiro para uint")

	_, ok2 := response["service_provider_id"].(*uint)
	assert.True(t, ok2, "service_provider_id deve ser um ponteiro para uint")
	assert.Equal(t, uint(4), response["created_by_user_id"])
	assert.Equal(t, "João Silva", response["created_by_name"])
	assert.Equal(t, "2023-05-22", response["due_date"])
	assert.Equal(t, "2023-05-15", response["open_date"])
	assert.Equal(t, "2023-05-20", response["completion_date"])
	assert.Equal(t, 150.50, response["estimated_cost"])
	assert.Equal(t, 175.25, response["actual_cost"])
	assert.Equal(t, fixedTime.Format(time.RFC3339), response["created_at"])
	assert.Equal(t, fixedTime.Add(2*time.Hour).Format(time.RFC3339), response["updated_at"])
	assert.Equal(t, "Pendente", response["status_display"])
	assert.Equal(t, "Média", response["priority_display"])
}

func TestMaintenanceOrderFromLegacyOrdem(t *testing.T) {
	// Criar uma data fixa para testes
	fixedTime := time.Date(2023, 5, 15, 10, 30, 0, 0, time.UTC)

	// Criar uma ordem legada para teste
	legacyOrder := &OrdemManutencaoExpandida{
		ID:              1,
		Titulo:          "Manutenção Preventiva",
		Descricao:       "Verificação periódica do equipamento",
		FilialID:        2,
		FilialNome:      "Posto Central",
		EquipamentoID:   3,
		EquipamentoNome: "Bomba de Combustível #1",
		Status:          "pendente",
		Prioridade:      "media",
		SolicitanteID:   4,
		SolicitanteNome: "João Silva",
		TecnicoID:       new(uint),
		DataAbertura:    fixedTime,
		CreatedAt:       fixedTime,
		UpdatedAt:       fixedTime.Add(2 * time.Hour),
	}

	// Definir valores para os ponteiros
	*legacyOrder.TecnicoID = 6

	// Chamar o método FromLegacyOrdem
	order := FromLegacyOrdem(legacyOrder)

	// Verificar se todos os campos foram convertidos corretamente
	assert.Equal(t, uint(1), order.ID)
	assert.Equal(t, "Manutenção Preventiva", order.Title)
	assert.Equal(t, "Verificação periódica do equipamento", order.Description)
	assert.Equal(t, uint(2), order.BranchID)
	assert.Equal(t, "Posto Central", order.BranchName)
	assert.Equal(t, uint(3), order.EquipmentID)
	assert.Equal(t, "Bomba de Combustível #1", order.EquipmentName)
	assert.Equal(t, StatusPending, order.Status)
	assert.Equal(t, PriorityMedium, order.Priority)
	assert.Equal(t, uint(4), order.CreatedByUserID)
	assert.Equal(t, "João Silva", order.CreatedByName)
	// Verificar apenas se o campo existe e é um ponteiro para uint
	assert.NotNil(t, order.TechnicianID, "TechnicianID não deve ser nil")
	assert.Equal(t, fixedTime, order.OpenDate)
	assert.Equal(t, fixedTime, order.CreatedAt)
	assert.Equal(t, fixedTime.Add(2*time.Hour), order.UpdatedAt)
}

func TestMaintenanceOrderBeforeSave(t *testing.T) {
	// Criar uma ordem com valores legados
	order := &MaintenanceOrder{
		Status:   OrderStatus("pendente"),
		Priority: PriorityLevel("media"),
	}

	// Chamar o método BeforeSave
	err := order.BeforeSave(nil)

	// Verificar se não houve erro
	assert.NoError(t, err)

	// Verificar se os valores foram normalizados
	assert.Equal(t, StatusPending, order.Status)
	assert.Equal(t, PriorityMedium, order.Priority)
}
