package models

import (
	"time"
)

// EquipmentType representa um tipo de equipamento padronizado
type EquipmentType struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"unique;not null"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// Relações
	Equipments []Equipment `json:"equipments,omitempty" gorm:"foreignKey:EquipmentTypeID"`
}

// TableName retorna o nome da tabela no banco de dados
func (EquipmentType) TableName() string {
	return TableEquipmentTypes
}
