package models

import (
	"time"

	"gorm.io/gorm"
)

// User representa um usuário no sistema
// IMPORTANTE: Este é o modelo principal e padronizado para representar usuários no sistema.
// Todas as novas implementações devem usar este modelo.
//
// Seguindo a convenção de nomenclatura padronizada, usamos o termo "user" em inglês
// no código-fonte, enquanto na interface do usuário usamos "Usuário" em português.
type User struct {
	// Campos principais armazenados no banco de dados
	ID                  uint           `json:"id" gorm:"primaryKey"`                       // ID único do usuário
	Name                string         `json:"name" gorm:"not null"`                       // Nome completo do usuário
	Email               string         `json:"email" gorm:"unique;not null"`               // Email do usuário (único)
	Password            string         `json:"-" gorm:"not null"`                          // Senha do usuário (não exposta em JSON)
	Role                UserRole       `json:"role" gorm:"column:type;not null"`           // Perfil do usuário (admin, gerente, técnico, etc.)
	BranchID            *uint          `json:"branch_id,omitempty"`                        // ID da filial associada (opcional)
	ServiceProviderID   *uint          `json:"service_provider_id,omitempty" gorm:"index"` // ID do prestador associado (opcional)
	FailedAttempts      int            `json:"failed_attempts" gorm:"default:0"`           // Número de tentativas de login falhas
	Blocked             bool           `json:"blocked" gorm:"default:false"`               // Indica se o usuário está bloqueado
	TOTPSecret          string         `json:"-" gorm:"column:totp_secret"`                // Segredo para autenticação de dois fatores
	TOTPEnabled         bool           `json:"totp_enabled" gorm:"default:false"`          // Indica se a autenticação de dois fatores está ativada
	LastPasswordChange  *time.Time     `json:"last_password_change,omitempty"`             // Data da última alteração de senha
	ForcePasswordChange bool           `json:"force_password_change" gorm:"default:false"` // Indica se o usuário deve alterar a senha no próximo login
	Phone               string         `json:"phone"`                                      // Telefone do usuário
	WhatsApp            string         `json:"whatsapp"`                                   // WhatsApp do usuário
	AvatarURL           string         `json:"avatar_url"`                                 // URL da foto do usuário
	CreatedAt           time.Time      `json:"created_at"`                                 // Data de criação
	UpdatedAt           time.Time      `json:"updated_at"`                                 // Data de atualização
	DeletedAt           gorm.DeletedAt `json:"-" gorm:"index"`                             // Data de exclusão (soft delete)

	// Relações com outras entidades
	// Estas relações seguem o padrão definido em entity_relations.go
	Branch             *Branch             `json:"branch,omitempty" gorm:"foreignKey:BranchID"`                        // Filial associada ao usuário
	ServiceProvider    *ServiceProvider    `json:"service_provider,omitempty" gorm:"foreignKey:ServiceProviderID"`     // Prestador associado ao usuário
	ManagedBranches    []*Branch           `json:"managed_branches,omitempty" gorm:"foreignKey:ManagerID"`             // Filiais gerenciadas pelo usuário
	RequestedOrders    []*MaintenanceOrder `json:"requested_orders,omitempty" gorm:"foreignKey:CreatedByUserID"`       // Ordens solicitadas pelo usuário
	AssignedOrders     []*MaintenanceOrder `json:"assigned_orders,omitempty" gorm:"foreignKey:AssignedToUserID"`       // Ordens atribuídas ao usuário
	TechnicianOrders   []*MaintenanceOrder `json:"technician_orders,omitempty" gorm:"foreignKey:TechnicianID"`         // Ordens atribuídas ao técnico
	TechnicianBranches []*Branch           `json:"technician_branches,omitempty" gorm:"many2many:technician_branches"` // Filiais associadas ao técnico
}

// TableName especifica o nome da tabela para o modelo User
func (User) TableName() string {
	return TableUsers
}

// ToResponse converte um User para UserResponse
func (u *User) ToResponse() UserResponse {
	return UserResponse{
		ID:    u.ID,
		Name:  u.Name,
		Email: u.Email,
		Role:  u.Role,
	}
}

// BeforeCreate é um hook do GORM que é executado antes de criar um registro
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.CreatedAt.IsZero() {
		u.CreatedAt = time.Now()
	}
	u.UpdatedAt = time.Now()
	return nil
}

// BeforeUpdate é um hook do GORM que é executado antes de atualizar um registro
func (u *User) BeforeUpdate(tx *gorm.DB) error {
	u.UpdatedAt = time.Now()
	return nil
}

// IsAdmin verifica se o usuário é um administrador
func (u *User) IsAdmin() bool {
	return u.Role == RoleAdmin
}

// IsManager verifica se o usuário é um gerente
func (u *User) IsManager() bool {
	return u.Role == RoleGerente
}

// IsTechnician verifica se o usuário é um técnico
func (u *User) IsTechnician() bool {
	return IsTechnician(string(u.Role))
}

// IsProvider verifica se o usuário é um prestador
func (u *User) IsProvider() bool {
	return IsProvider(string(u.Role))
}

// IsBranchUser verifica se o usuário é um usuário de filial
func (u *User) IsBranchUser() bool {
	return u.Role == RoleFilial || u.Role == RoleBranchUser
}

// HasBranch verifica se o usuário está associado a uma filial
func (u *User) HasBranch() bool {
	return u.BranchID != nil && *u.BranchID > 0
}

// HasServiceProvider verifica se o usuário está associado a um prestador
func (u *User) HasServiceProvider() bool {
	return u.ServiceProviderID != nil && *u.ServiceProviderID > 0
}

// BranchModel representa uma versão simplificada de Branch
// Deprecated: Use Branch diretamente em vez de BranchModel
type BranchModel struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Name        string `json:"name" gorm:"not null"`
	Address     string `json:"address" gorm:"size:255"`
	City        string `json:"city" gorm:"size:100"`
	State       string `json:"state" gorm:"size:2"`
	ZipCode     string `json:"zip_code" gorm:"size:10"`
	Phone       string `json:"phone" gorm:"size:20"`
	Email       string `json:"email" gorm:"size:100"`
	ContactInfo string `json:"contact_info" gorm:"size:255"`
	IsActive    bool   `json:"is_active" gorm:"default:true"`
}

func (BranchModel) TableName() string {
	return "branches"
}
