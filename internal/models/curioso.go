package models

import "time"

// Curioso representa um item da tabela de curiosidades do sistema
type Curioso struct {
	ID               int       `json:"id" gorm:"primaryKey"`
	Nome             string    `json:"nome" gorm:"not null"`
	FatoCurioso      string    `json:"fato_curioso" gorm:"type:text;not null"`
	NivelCuriosidade int       `json:"nivel_curiosidade" gorm:"check:nivel_curiosidade BETWEEN 1 AND 10"`
	DataDescoberta   time.Time `json:"data_descoberta" gorm:"default:CURRENT_TIMESTAMP"`
	Verificado       bool      `json:"verificado" gorm:"default:false"`
	Categoria        string    `json:"categoria" gorm:"type:varchar(50)"`
	Fonte            string    `json:"fonte" gorm:"type:varchar(255)"`
	ImagemURL        string    `json:"imagem_url" gorm:"type:varchar(255)"`
	Likes            int       `json:"likes" gorm:"default:0"`
	CreatedAt        time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
	UpdatedAt        time.Time `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP"`
}

// TableName especifica o nome da tabela para o GORM
func (Curioso) TableName() string {
	return "curiosos"
}
