package models

import (
	"time"
)

// PushSubscription representa uma assinatura de notificação push de um navegador
type PushSubscription struct {
	Endpoint string            `json:"endpoint"`
	Keys     map[string]string `json:"keys"`
}

// NotificationSubscription representa uma assinatura de notificação no banco de dados
type NotificationSubscription struct {
	ID           int64            `json:"id"`
	UserID       int64            `json:"userId"`
	UserRole     string           `json:"userRole"`
	Subscription PushSubscription `json:"subscription"`
	CreatedAt    time.Time        `json:"createdAt"`
	UpdatedAt    time.Time        `json:"updatedAt"`
}

type NotificationType string

const (
	NotificationTypeInfo    NotificationType = "info"
	NotificationTypeWarning NotificationType = "warning"
	NotificationTypeError   NotificationType = "error"
	NotificationTypeSuccess NotificationType = "success"
)

// Notification representa uma notificação enviada a um usuário
type Notification struct {
	ID        uint                 `json:"id" gorm:"primaryKey"`
	UserID    uint                 `json:"user_id"`
	Title     string               `json:"title"`
	Message   string               `json:"message"`
	Body      string               `json:"body"`
	Type      NotificationType     `json:"type"`
	Read      bool                 `json:"read" gorm:"default:false"`
	URL       string               `json:"url"`
	OrderID   *uint                `json:"order_id"`
	Action    string               `json:"action"`
	Actions   []NotificationAction `json:"actions" gorm:"-"`
	CreatedAt time.Time            `json:"created_at"`
	UpdatedAt time.Time            `json:"updated_at"`
}

func (Notification) TableName() string {
	return "notifications"
}

// NotificationAction representa uma ação que pode ser tomada a partir da notificação
type NotificationAction struct {
	Label string `json:"label"`
	URL   string `json:"url"`
}
