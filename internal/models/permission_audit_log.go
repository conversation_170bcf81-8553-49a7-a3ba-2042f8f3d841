package models

import "time"

// PermissionAuditLog representa um log de auditoria de permissões
type PermissionAuditLog struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	UserID       *uint     `json:"user_id" gorm:"column:user_id"`
	UserRole     string    `json:"user_role" gorm:"size:50"`
	ResourceType string    `json:"resource_type" gorm:"size:50"`
	ResourceID   uint      `json:"resource_id" gorm:"column:resource_id"`
	Action       string    `json:"action" gorm:"size:50"`
	Allowed      bool      `json:"allowed"`
	IPAddress    string    `json:"ip_address" gorm:"size:50"`
	UserAgent    string    `json:"user_agent" gorm:"type:text"`
	CreatedAt    time.Time `json:"created_at" gorm:"autoCreateTime"`
}

// TableName especifica o nome da tabela para o modelo PermissionAuditLog
func (PermissionAuditLog) TableName() string {
	return "permission_audit_logs"
}
