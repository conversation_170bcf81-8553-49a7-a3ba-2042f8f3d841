package models

import "time"

// MaintenanceOrderMetricsV2 representa métricas e estatísticas sobre ordens de manutenção (versão compatível)
type MaintenanceOrderMetricsV2 struct {
	// Contagens por status
	TotalOrders      int64 `json:"total_orders"`
	PendingOrders    int64 `json:"pending_orders"`
	ScheduledOrders  int64 `json:"scheduled_orders"`
	InProgressOrders int64 `json:"in_progress_orders"`
	CompletedOrders  int64 `json:"completed_orders"`
	CancelledOrders  int64 `json:"cancelled_orders"`

	// Campos para compatibilidade com testes
	TotalCount      int `json:"total_count"`
	PendingCount    int `json:"pending_count"`
	InProgressCount int `json:"in_progress_count"`
	CompletedCount  int `json:"completed_count"`
	CancelledCount  int `json:"cancelled_count"`
	OnHoldCount     int `json:"on_hold_count"`

	// Contagens por prioridade
	LowPriorityOrders      int64 `json:"low_priority_orders"`
	MediumPriorityOrders   int64 `json:"medium_priority_orders"`
	HighPriorityOrders     int64 `json:"high_priority_orders"`
	CriticalPriorityOrders int64 `json:"critical_priority_orders"`

	// Contagens por tipo
	PreventiveOrders   int64 `json:"preventive_orders"`
	CorrectiveOrders   int64 `json:"corrective_orders"`
	InspectionOrders   int64 `json:"inspection_orders"`
	CalibrationOrders  int64 `json:"calibration_orders"`
	InstallationOrders int64 `json:"installation_orders"`

	// Métricas temporais
	AverageCompletionTime float64 `json:"average_completion_time"` // em horas
	AverageResponseTime   float64 `json:"average_response_time"`   // em horas

	// Informações financeiras
	TotalCost           float64 `json:"total_cost"`
	AverageCostPerOrder float64 `json:"average_cost_per_order"`

	// Períodos
	LastUpdate        time.Time `json:"last_update"`
	ReportPeriodStart time.Time `json:"report_period_start"`
	ReportPeriodEnd   time.Time `json:"report_period_end"`

	// Dados adicionais para gráficos
	OrdersByMonth   map[string]int64 `json:"orders_by_month"`
	OrdersByStation map[string]int64 `json:"orders_by_station"`
	OrdersByStatus  map[string]int64 `json:"orders_by_status"`
}
