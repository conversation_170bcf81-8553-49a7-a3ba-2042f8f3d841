package models

import "time"

// Technician representa um técnico no sistema
type Technician struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	UserID      uint      `json:"user_id" gorm:"uniqueIndex"`
	Name        string    `json:"name"`
	Email       string    `json:"email"`
	Phone       string    `json:"phone"`
	Specialties []uint    `json:"specialties" gorm:"-"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TechnicianSpecialty representa uma especialidade de técnico
type TechnicianSpecialty struct {
	ID             uint      `gorm:"primaryKey" json:"id"`
	Name           string    `gorm:"size:100;not null" json:"name"`
	Description    string    `gorm:"type:text" json:"description"`
	EquipmentTypes string    `gorm:"type:text;not null" json:"equipment_types"` // Alterado para string simples
	CreatedAt      time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt      time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName retorna o nome da tabela
func (TechnicianSpecialty) TableName() string {
	return "technician_specialties"
}

// Nota: A definição de TechnicianBranch foi movida para o arquivo technician_branch.go

// MaintenanceHistory representa o histórico de manutenções realizadas
type MaintenanceHistory struct {
	ID           uint      `gorm:"primaryKey" json:"id"`
	TechnicianID uint      `gorm:"not null" json:"technician_id"`
	OrderID      uint      `gorm:"not null" json:"order_id"`
	EquipmentID  uint      `gorm:"not null" json:"equipment_id"`
	Description  string    `gorm:"type:text;not null" json:"description"`
	PartsUsed    []string  `gorm:"type:text[]" json:"parts_used"`
	Costs        float64   `gorm:"type:decimal(10,2)" json:"costs"`
	Status       string    `gorm:"size:50;not null" json:"status"`
	CreatedAt    time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt    time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`

	// Relacionamentos
	Technician User             `gorm:"foreignKey:TechnicianID" json:"technician"`
	Order      MaintenanceOrder `gorm:"foreignKey:OrderID" json:"order"`
	Equipment  Equipment        `gorm:"foreignKey:EquipmentID" json:"equipment"`
}

// TableName retorna o nome da tabela
func (MaintenanceHistory) TableName() string {
	return "maintenance_history"
}
