package models

import (
	"time"
)

// FotoOrdem representa uma foto anexada a uma ordem de manutenção
type FotoOrdem struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	OrdemID     uint      `json:"ordem_id" gorm:"index"`
	URL         string    `json:"url"`
	Descricao   string    `json:"descricao"`
	TipoFoto    string    `json:"tipo_foto"` // antes, durante, depois, diagnóstico
	DataUpload  time.Time `json:"data_upload"`
	UsuarioID   uint      `json:"usuario_id"`
	UsuarioNome string    `json:"usuario_nome"`
	CreatedAt   time.Time `json:"created_at"`
}
