package models

import (
	"time"
)

// TechnicianBranch representa um relacionamento entre um técnico, uma filial e uma especialidade
type TechnicianBranch struct {
	TechnicianID uint      `json:"technician_id" gorm:"primaryKey;column:technician_id"`
	BranchID     uint      `json:"branch_id" gorm:"primaryKey;column:branch_id"`
	SpecialtyID  uint      `json:"specialty_id" gorm:"primaryKey;column:specialty_id"`
	CreatedAt    time.Time `json:"created_at" gorm:"column:created_at;default:CURRENT_TIMESTAMP"`
	UpdatedAt    time.Time `json:"updated_at" gorm:"column:updated_at;default:CURRENT_TIMESTAMP"`

	// Relacionamentos
	Technician *User              `json:"technician,omitempty" gorm:"foreignKey:TechnicianID"`
	Branch     *Branch            `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Specialty  *TechnicianSpecialty `json:"specialty,omitempty" gorm:"foreignKey:SpecialtyID"`
}

// TableName define o nome da tabela no banco de dados
func (TechnicianBranch) TableName() string {
	return "technician_branches"
}
