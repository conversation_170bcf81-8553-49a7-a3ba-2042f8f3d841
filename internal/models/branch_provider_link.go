package models

import "time"

// BranchProviderLink representa um vínculo entre uma filial e um prestador de serviço
type BranchProviderLink struct {
	ServiceProviderID uint      `json:"service_provider_id" gorm:"primaryKey;column:service_provider_id"`
	BranchID          uint      `json:"branch_id" gorm:"primaryKey;column:branch_id"`
	CreatedAt         time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt         time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName especifica o nome da tabela no banco de dados
func (BranchProviderLink) TableName() string {
	return "branch_providers"
}
