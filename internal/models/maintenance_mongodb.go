package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// MaintenanceOrderMongoDB representa uma ordem de manutenção para MongoDB
type MaintenanceOrderMongoDB struct {
	ID                 primitive.ObjectID  `bson:"_id,omitempty" json:"id"`
	BranchID           primitive.ObjectID  `bson:"branch_id" json:"branch_id"`
	BranchName         string              `bson:"-" json:"branch_name"`
	EquipmentID        primitive.ObjectID  `bson:"equipment_id" json:"equipment_id"`
	ServiceProviderID  *primitive.ObjectID `bson:"service_provider_id,omitempty" json:"service_provider_id,omitempty"`
	CreatedByUserID    primitive.ObjectID  `bson:"created_by_user_id" json:"created_by_user_id"`
	CreatedByName      string              `bson:"-" json:"created_by_name"`
	Title              string              `bson:"title" json:"title"`
	Description        string              `bson:"description" json:"description"`
	Number             string              `bson:"number" json:"number"`
	Problem            string              `bson:"problem" json:"problem"`
	Status             string              `bson:"status" json:"status"`
	Priority           string              `bson:"priority" json:"priority"`
	EstimatedCost      float64             `bson:"estimated_cost" json:"estimated_cost"`
	ActualCost         float64             `bson:"actual_cost" json:"actual_cost"`
	DueDate            time.Time           `bson:"due_date" json:"due_date"`
	OpenDate           time.Time           `bson:"open_date" json:"open_date"`
	CreatedAt          time.Time           `bson:"created_at" json:"created_at"`
	UpdatedAt          time.Time           `bson:"updated_at" json:"updated_at"`
}

// MaintenanceMaterialMongoDB representa um material usado em uma ordem de manutenção para MongoDB
type MaintenanceMaterialMongoDB struct {
	ID          primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	OrderID     primitive.ObjectID `bson:"order_id" json:"order_id"`
	Name        string             `bson:"name" json:"name"`
	Description string             `bson:"description" json:"description"`
	Quantity    int                `bson:"quantity" json:"quantity"`
	UnitPrice   float64            `bson:"unit_price" json:"unit_price"`
	TotalPrice  float64            `bson:"total_price" json:"total_price"`
	CreatedAt   time.Time          `bson:"created_at" json:"created_at"`
	UpdatedAt   time.Time          `bson:"updated_at" json:"updated_at"`
}

// MaintenanceNoteMongoDB representa uma nota em uma ordem de manutenção para MongoDB
type MaintenanceNoteMongoDB struct {
	ID        primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	OrderID   primitive.ObjectID `bson:"order_id" json:"order_id"`
	Content   string             `bson:"content" json:"content"`
	CreatedAt time.Time          `bson:"created_at" json:"created_at"`
	UpdatedAt time.Time          `bson:"updated_at" json:"updated_at"`
}
