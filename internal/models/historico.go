package models

import (
	"time"
)

// HistoricoOrdem representa um evento de alteração em uma ordem
type HistoricoOrdem struct {
	ID            uint       `json:"id" gorm:"primaryKey"`
	OrdemID       uint       `json:"ordem_id" gorm:"index"`
	UsuarioID     uint       `json:"usuario_id"`
	UsuarioNome   string     `json:"usuario_nome"`
	DataEvento    time.Time  `json:"data_evento"`
	TipoEvento    TipoEvento `json:"tipo_evento"`
	ValorAnterior string     `json:"valor_anterior,omitempty"`
	ValorNovo     string     `json:"valor_novo,omitempty"`
	Observacao    string     `json:"observacao"`
	CreatedAt     time.Time  `json:"created_at"`
}
