package models

// MaintenanceOrderFilters define os filtros para busca de ordens de manutenção
type MaintenanceOrderFilters struct {
	Status      *string `json:"status,omitempty"`
	Priority    *string `json:"priority,omitempty"`
	BranchID    *uint   `json:"branch_id,omitempty"`
	EquipmentID *uint   `json:"equipment_id,omitempty"`
	ProviderID  *uint   `json:"provider_id,omitempty"`
	StartDate   *string `json:"start_date,omitempty"`
	EndDate     *string `json:"end_date,omitempty"`
	SearchTerm  *string `json:"search_term,omitempty"`
	Page        int     `json:"page"`
	Limit       int     `json:"limit"`
}
