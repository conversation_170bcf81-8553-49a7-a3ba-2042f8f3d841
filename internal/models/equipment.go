package models

import (
	"strconv"
	"time"

	"gorm.io/gorm"
)

// Equipment representa um equipamento no sistema
type Equipment struct {
	ID               uint           `gorm:"primaryKey" json:"id"`
	Name             string         `gorm:"column:name" json:"name"` // Nome personalizado do equipamento
	SerialNumber     string         `gorm:"column:serial_number" json:"serial_number"`
	Model            string         `gorm:"column:model" json:"model"`
	Brand            string         `gorm:"column:brand" json:"brand"`
	Type             string         `gorm:"column:type" json:"type"`                           // Tipo de equipamento: bomba, tanque, compressor, etc.
	EquipmentTypeID  *uint          `gorm:"column:equipment_type_id" json:"equipment_type_id"` // Referência ao tipo padronizado de equipamento
	Category         string         `gorm:"-" json:"category"`                                 // Campo virtual, não existe na tabela
	InstallationDate *time.Time     `gorm:"column:installation_date" json:"installation_date"`
	WarrantyEndDate  *time.Time     `gorm:"-" json:"warranty_end_date"`     // Campo virtual, não existe na tabela
	MaintenanceFreq  int            `gorm:"-" json:"maintenance_frequency"` // Campo virtual, não existe na tabela
	LastMaintenance  *time.Time     `gorm:"column:last_maintenance" json:"last_maintenance"`
	LastPreventive   *time.Time     `gorm:"column:last_preventive" json:"last_preventive"`
	NextPreventive   *time.Time     `gorm:"column:next_preventive" json:"next_preventive"`
	NextMaintenance  *time.Time     `gorm:"-" json:"next_maintenance"`   // Campo virtual, mapeado para next_preventive
	Status           string         `gorm:"column:status" json:"status"` // ativo, inativo, manutenção, descontinuado
	Location         string         `gorm:"column:location" json:"location"`
	BranchID         uint           `json:"branch_id" gorm:"column:branch_id"`
	FilialID         uint           `gorm:"-" json:"filial_id"`         // Campo virtual, mapeado para branch_id
	Notes            string         `gorm:"-" json:"notes"`             // Campo virtual, não existe na tabela
	ManufacturerDocs string         `gorm:"-" json:"manufacturer_docs"` // Campo virtual, não existe na tabela
	ImageURL         string         `gorm:"-" json:"image_url"`         // URL da imagem principal do equipamento (campo virtual)
	Images           []string       `gorm:"-" json:"images"`            // Lista de URLs de imagens adicionais (campo virtual)
	CreatedAt        time.Time      `gorm:"column:created_at" json:"created_at"`
	UpdatedAt        time.Time      `gorm:"column:updated_at" json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`

	// Relações com outras entidades
	// Estas relações seguem o padrão definido em entity_relations.go
	Branch            *Branch            `json:"branch,omitempty" gorm:"foreignKey:BranchID"`                // Filial à qual o equipamento pertence
	MaintenanceOrders []MaintenanceOrder `json:"maintenance_orders,omitempty" gorm:"foreignKey:EquipmentID"` // Ordens de manutenção do equipamento
	Tags              []Tag              `json:"tags,omitempty" gorm:"many2many:equipment_tags"`             // Tags associadas ao equipamento
	EquipmentType     *EquipmentType     `json:"equipment_type,omitempty" gorm:"foreignKey:EquipmentTypeID"` // Tipo padronizado do equipamento
}

// EquipmentSimple é uma versão simplificada do equipamento para listas e seleções
type EquipmentSimple struct {
	ID           uint   `json:"id"`
	Name         string `json:"name"`
	SerialNumber string `json:"serial_number"`
	Model        string `json:"model"`
	Brand        string `json:"brand"`
	Type         string `json:"type"`
	Status       string `json:"status"`
}

// EquipmentTypeOption representa uma opção de tipo de equipamento para o cadastro de novos equipamentos
type EquipmentTypeOption struct {
	Value       string `json:"value"`
	Label       string `json:"label"`
	Description string `json:"description"`
}

// TableName especifica o nome da tabela para o modelo Equipment
func (Equipment) TableName() string {
	return TableEquipment
}

// ToSimple converte Equipment para EquipmentSimple
func (e *Equipment) ToSimple() EquipmentSimple {
	return EquipmentSimple{
		ID:           e.ID,
		Name:         e.GetName(),
		SerialNumber: e.SerialNumber,
		Model:        e.Model,
		Brand:        e.Brand,
		Type:         e.Type,
		Status:       e.Status,
	}
}

// GetName retorna o nome do equipamento, priorizando o nome personalizado
func (e *Equipment) GetName() string {
	// Se o nome personalizado existir, retorná-lo
	if e.Name != "" {
		return e.Name
	}

	// Caso contrário, usar o campo Type como nome
	if e.Type != "" {
		// Formatar o tipo para exibição
		switch e.Type {
		case "bomba":
			return "Bomba de Combustível"
		case "tanque":
			return "Tanque de Armazenamento"
		case "compressor":
			return "Compressor de Ar"
		case "gerador":
			return "Gerador de Energia"
		case "sistema_pagamento":
			return "Sistema de Pagamento"
		case "sistema_automacao":
			return "Sistema de Automação"
		case "iluminacao":
			return "Sistema de Iluminação"
		case "refrigeracao":
			return "Sistema de Refrigeração"
		case "detector_vazamento":
			return "Detector de Vazamento"
		default:
			return e.Type
		}
	}

	// Fallback para combinação de marca e modelo
	name := ""
	if e.Brand != "" {
		name += e.Brand
	}

	if e.Model != "" {
		if name != "" {
			name += " "
		}
		name += e.Model
	}

	if name == "" && e.SerialNumber != "" {
		name = "Equipamento " + e.SerialNumber
	} else if name == "" {
		name = "Equipamento #" + strconv.FormatUint(uint64(e.ID), 10)
	}

	return name
}

// AfterFind é chamado pelo GORM após carregar o registro do banco
func (e *Equipment) AfterFind(tx *gorm.DB) error {
	// Se o nome não estiver definido, usar o tipo formatado
	if e.Name == "" {
		e.Name = e.GetName()
	}

	// Preencher campos virtuais com valores padrão se necessário
	e.NextMaintenance = e.NextPreventive

	// Mapear FilialID para BranchID para compatibilidade com código existente
	e.FilialID = e.BranchID

	return nil
}

// BeforeSave é chamado pelo GORM antes de salvar o registro no banco
func (e *Equipment) BeforeSave(tx *gorm.DB) error {
	// Garantir que o campo Type seja preenchido corretamente
	if e.Type == "" {
		// Se o Type estiver vazio, definir um valor padrão
		e.Type = "outro"
	}

	// Garantir que o BranchID seja preenchido
	if e.BranchID == 0 && e.FilialID > 0 {
		e.BranchID = e.FilialID
	}

	return nil
}

// GetEquipmentTypes retorna os tipos de equipamentos disponíveis
func GetEquipmentTypes() []EquipmentTypeOption {
	return []EquipmentTypeOption{
		{Value: "bomba", Label: "Bomba de Combustível", Description: "Bombas para abastecimento de veículos"},
		{Value: "tanque", Label: "Tanque de Armazenamento", Description: "Tanques para armazenamento de combustíveis"},
		{Value: "compressor", Label: "Compressor de Ar", Description: "Equipamentos para calibragem de pneus"},
		{Value: "gerador", Label: "Gerador de Energia", Description: "Geradores para fornecimento de energia de emergência"},
		{Value: "sistema_pagamento", Label: "Sistema de Pagamento", Description: "Terminais e máquinas de pagamento"},
		{Value: "sistema_automacao", Label: "Sistema de Automação", Description: "Sistemas de controle e automação do posto"},
		{Value: "iluminacao", Label: "Sistema de Iluminação", Description: "Equipamentos de iluminação e sinalização"},
		{Value: "refrigeracao", Label: "Sistema de Refrigeração", Description: "Equipamentos de refrigeração e climatização"},
		{Value: "detector_vazamento", Label: "Detector de Vazamento", Description: "Sistemas para detecção de vazamentos"},
		{Value: "outro", Label: "Outro", Description: "Outros tipos de equipamentos"},
	}
}
