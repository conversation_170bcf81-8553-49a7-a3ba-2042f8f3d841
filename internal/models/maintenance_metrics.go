package models

import "time"

// MaintenanceMetrics representa métricas das ordens de manutenção
type MaintenanceMetrics struct {
	// Contadores básicos
	TotalOrders int `json:"total_orders"`

	// Distribuição por status e prioridade
	OrdersByStatus   map[string]int `json:"orders_by_status"`
	OrdersByPriority map[string]int `json:"orders_by_priority"`

	// Métricas temporais
	AverageCompletionTime float64   `json:"average_completion_time"`
	LastUpdate            time.Time `json:"last_update"`
	ReportPeriodStart     time.Time `json:"report_period_start"`
	ReportPeriodEnd       time.Time `json:"report_period_end"`

	// Métricas financeiras
	TotalCost           float64 `json:"total_cost"`
	AverageCostPerOrder float64 `json:"average_cost_per_order"`

	// Distribuição temporal e espacial
	OrdersByMonth  map[string]int `json:"orders_by_month"`
	OrdersByBranch map[string]int `json:"orders_by_branch"`
}
