package models

import (
	"time"
)

type Material struct {
	ID                 uint      `json:"id" gorm:"primaryKey"`
	MaintenanceOrderID uint      `json:"maintenance_order_id" gorm:"column:maintenance_order_id"`
	Name               string    `json:"name"`
	Description        string    `json:"description"`
	Quantity           int       `json:"quantity"`
	Unit               string    `json:"unit"`
	Cost               float64   `json:"cost"`
	UnitCost           float64   `json:"unit_cost"`
	AddedByUserID      uint      `json:"added_by_user_id" gorm:"column:added_by_user_id"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
}

func (Material) TableName() string {
	return "maintenance_materials"
}
