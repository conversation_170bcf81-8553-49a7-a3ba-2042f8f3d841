package models

import (
	"time"
)

// ProviderEquipmentType representa a relação entre prestador e tipo de equipamento
type ProviderEquipmentType struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	ServiceProviderID uint      `json:"service_provider_id" gorm:"not null"`
	EquipmentTypeID   uint      `json:"equipment_type_id" gorm:"not null"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
	
	// Relações
	ServiceProvider   ServiceProvider `json:"service_provider,omitempty" gorm:"foreignKey:ServiceProviderID"`
	EquipmentType     EquipmentType   `json:"equipment_type,omitempty" gorm:"foreignKey:EquipmentTypeID"`
}

// TableName retorna o nome da tabela no banco de dados
func (ProviderEquipmentType) TableName() string {
	return "provider_equipment_types"
}
