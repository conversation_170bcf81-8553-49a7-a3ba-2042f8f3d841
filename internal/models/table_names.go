package models

// Este arquivo contém definições padronizadas de nomes de tabelas.
// Ele define constantes para nomes de tabelas e funções para obter nomes de tabelas.

// Constantes para nomes de tabelas principais
const (
	// Tabelas principais
	TableUsers             = "users"              // Tabela de usuários
	TableBranches          = "branches"           // Tabela de filiais
	TableEquipment         = "equipment"          // Tabela de equipamentos
	TableMaintenanceOrders = "maintenance_orders" // Tabela de ordens de manutenção
	TableServiceProviders  = "service_providers"  // Tabela de prestadores de serviço
	TableEquipmentTypes    = "equipment_types"    // Tabela de tipos de equipamento
	TableTags              = "tags"               // Tabela de tags
	TableNotifications     = "notifications"      // Tabela de notificações
	TableSecurityPolicies  = "security_policies"  // Tabela de políticas de segurança
	TableCalendarEvents    = "calendar_events"    // Tabela de eventos de calendário
	
	// Tabelas de junção
	TableTechnicianBranches = "technician_branches" // Tabela de junção entre técnicos e filiais
	TableProviderBranches   = "provider_branches"   // Tabela de junção entre prestadores e filiais
	TableEquipmentTags      = "equipment_tags"      // Tabela de junção entre equipamentos e tags
	
	// Tabelas de controle
	TableSchemaVersions    = "schema_versions"     // Tabela de versões do schema
)

// Constantes para nomes de tabelas legadas (para compatibilidade)
const (
	// Tabelas legadas em português
	TableUsuarios         = "usuarios"          // Tabela legada de usuários
	TableFiliais          = "filiais"           // Tabela legada de filiais
	TableEquipamentos     = "equipamentos"      // Tabela legada de equipamentos
	TableOrdensServico    = "ordens_servico"    // Tabela legada de ordens de serviço
	TablePrestadores      = "prestadores"       // Tabela legada de prestadores de serviço
	
	// Tabelas legadas com nomenclatura inconsistente
	TableStations         = "stations"          // Tabela legada de filiais (duplicada)
	TableBranchStationLinks = "branch_station_links" // Tabela legada de junção entre filiais e postos
)

// GetTableName retorna o nome padronizado da tabela para um modelo específico
func GetTableName(modelName string) string {
	switch modelName {
	case "User":
		return TableUsers
	case "Branch":
		return TableBranches
	case "Equipment":
		return TableEquipment
	case "MaintenanceOrder":
		return TableMaintenanceOrders
	case "ServiceProvider":
		return TableServiceProviders
	case "EquipmentType":
		return TableEquipmentTypes
	case "Tag":
		return TableTags
	case "Notification":
		return TableNotifications
	case "SecurityPolicy":
		return TableSecurityPolicies
	case "CalendarEvent":
		return TableCalendarEvents
	default:
		return ""
	}
}

// GetLegacyTableName retorna o nome legado da tabela para um modelo específico
func GetLegacyTableName(modelName string) string {
	switch modelName {
	case "User":
		return TableUsuarios
	case "Branch":
		return TableFiliais
	case "Equipment":
		return TableEquipamentos
	case "MaintenanceOrder":
		return TableOrdensServico
	case "ServiceProvider":
		return TablePrestadores
	case "Station":
		return TableStations
	default:
		return ""
	}
}

// IsStandardTableName verifica se o nome da tabela já está no formato padronizado
func IsStandardTableName(tableName string) bool {
	switch tableName {
	case TableUsers, TableBranches, TableEquipment, TableMaintenanceOrders,
		TableServiceProviders, TableEquipmentTypes, TableTags, TableNotifications,
		TableSecurityPolicies, TableCalendarEvents, TableTechnicianBranches,
		TableProviderBranches, TableEquipmentTags, TableSchemaVersions:
		return true
	default:
		return false
	}
}

// NormalizeTableName normaliza o nome da tabela para o formato padronizado
func NormalizeTableName(tableName string) string {
	switch tableName {
	case TableUsuarios:
		return TableUsers
	case TableFiliais, TableStations:
		return TableBranches
	case TableEquipamentos:
		return TableEquipment
	case TableOrdensServico:
		return TableMaintenanceOrders
	case TablePrestadores:
		return TableServiceProviders
	case TableBranchStationLinks:
		return TableTechnicianBranches
	default:
		return tableName
	}
}
