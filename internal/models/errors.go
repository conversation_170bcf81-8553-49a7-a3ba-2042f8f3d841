package models

import "errors"

// Definição de erros comuns para padronização
var (
	// ErrNotFound indica que um recurso não foi encontrado
	ErrNotFound = errors.New("registro não encontrado")

	// ErrInvalidInput indica que os dados de entrada são inválidos
	ErrInvalidInput = errors.New("dados de entrada inválidos")

	// ErrDuplicateEntry indica que já existe um registro com os mesmos dados únicos
	ErrDuplicateEntry = errors.New("já existe um registro com esses dados")

	// ErrUnauthorized indica que o usuário não tem permissão para a operação
	ErrUnauthorized = errors.New("usuário não autorizado para esta operação")

	// ErrInternalServer indica um erro interno do servidor
	ErrInternalServer = errors.New("erro interno do servidor")

	// ErrDatabaseOperation indica um erro na operação com o banco de dados
	ErrDatabaseOperation = errors.New("erro na operação com o banco de dados")
)
