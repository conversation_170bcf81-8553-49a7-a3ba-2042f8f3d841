package models

import (
	"database/sql"
	"time"
)

// --- T<PERSON><PERSON> e Constantes ---

// UserRole define os papéis dos usuários
type UserRole string

const (
	RoleAdmin      UserRole = "admin"
	RoleGerente    UserRole = "manager"
	RoleFinanceiro UserRole = "financial"
	RoleFilial     UserRole = "filial"
	RoleBranchUser UserRole = "branch_user" // Mantendo para compatibilidade
	RoleTechnician UserRole = "technician"  // Constante padronizada para técnicos
	// IMPORTANTE: Anteriormente esta constante usava "tecnico". Foi alterada para "technician"
	// para padronização da nomenclatura. O código foi adaptado para manter compatibilidade.
	RolePrestador UserRole = "provider" // Constante padronizada para prestadores
	// IMPORTANTE: Esta constante representa o perfil de prestador de serviço.
	// O valor "provider" é usado internamente, enquanto "prestadores" é usado em algumas partes do sistema.
)

// NullTime representa um time.Time que pode ser nulo
type NullTime sql.NullTime

// UserResponse is a simplified version of User for API responses
type UserResponse struct {
	ID    uint     `json:"id"`
	Name  string   `json:"name"`
	Email string   `json:"email"`
	Role  UserRole `json:"role"`
}

// --- Structs ---

// Nota: A definição de Branch foi movida para internal/models/branch.go
// Nota: A definição de User foi movida para internal/models/user.go

// CostItem representa um item de custo
type CostItem struct {
	ID                 uint      `json:"id" gorm:"primaryKey"`
	MaintenanceOrderID uint      `json:"maintenance_order_id"`
	AddedByUserID      uint      `json:"added_by_user_id"`
	Description        string    `json:"description"`
	Quantity           float64   `json:"quantity"`
	UnitPrice          float64   `json:"unit_price"`
	TotalItemCost      float64   `json:"total_item_cost"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`

	MaintenanceOrder *MaintenanceOrder `json:"maintenance_order,omitempty" gorm:"foreignKey:MaintenanceOrderID"`
	AddedByUser      *User             `json:"added_by_user,omitempty" gorm:"foreignKey:AddedByUserID"`
}

// Interaction representa uma interação
type Interaction struct {
	ID                 uint      `json:"id" gorm:"primaryKey"`
	MaintenanceOrderID uint      `json:"maintenance_order_id"`
	UserID             uint      `json:"user_id"`
	Message            string    `json:"message"`
	Timestamp          time.Time `json:"timestamp"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`

	MaintenanceOrder *MaintenanceOrder `json:"maintenance_order,omitempty" gorm:"foreignKey:MaintenanceOrderID"`
	User             *User             `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// OrdemManutencao representa uma ordem de manutenção
type OrdemManutencao struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Title     string    `json:"title"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// SecurityPolicy representa uma política de segurança
type SecurityPolicy struct {
	ID                         uint      `json:"id" gorm:"primaryKey"`
	Name                       string    `json:"name"`
	PasswordMinLength          int       `json:"password_min_length" gorm:"default:8"`
	PasswordRequireUppercase   bool      `json:"password_require_uppercase" gorm:"default:true"`
	PasswordRequireNumber      bool      `json:"password_require_number" gorm:"default:true"`
	PasswordRequireSpecialChar bool      `json:"password_require_special_char" gorm:"default:true"`
	PasswordExpiryDays         int       `json:"password_expiry_days" gorm:"default:90"`
	MaxLoginAttempts           int       `json:"max_login_attempts" gorm:"default:5"`
	LockoutDurationMinutes     int       `json:"lockout_duration_minutes" gorm:"default:30"`
	Enable2FA                  bool      `json:"enable_2fa" gorm:"default:false"`
	SessionTimeoutMinutes      int       `json:"session_timeout_minutes" gorm:"default:60"`
	CreatedAt                  time.Time `json:"created_at"`
	UpdatedAt                  time.Time `json:"updated_at"`
}

// --- Tipos Adicionais/Aliases para Compatibilidade ---

// --- Hooks e Métodos ---

// Nota: Os hooks BeforeCreate e BeforeUpdate para User foram movidos para internal/models/user.go

// OrdemManutencao representa uma ordem de manutenção
// ... existing code ...
