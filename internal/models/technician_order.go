package models

import (
	"time"
)

// TechnicianOrder representa um relacionamento entre um técnico e uma ordem de manutenção
type TechnicianOrder struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	TechnicianID uint      `json:"technician_id" gorm:"column:technician_id;not null"`
	OrderID      uint      `json:"order_id" gorm:"column:order_id;not null"`
	CreatedAt    time.Time `json:"created_at" gorm:"column:created_at;default:CURRENT_TIMESTAMP"`
	CreatedBy    *uint     `json:"created_by,omitempty" gorm:"column:created_by"`
	Notes        string    `json:"notes,omitempty" gorm:"column:notes"`

	// Relacionamentos
	Technician *User             `json:"technician,omitempty" gorm:"foreignKey:TechnicianID"`
	Order      *MaintenanceOrder `json:"order,omitempty" gorm:"foreignKey:OrderID"`
	Creator    *User             `json:"creator,omitempty" gorm:"foreignKey:CreatedBy"`
}

// TableName define o nome da tabela no banco de dados
func (TechnicianOrder) TableName() string {
	return "technician_orders"
}

// TechnicianOrderRepository é a interface para operações com a tabela technician_orders
type TechnicianOrderRepository interface {
	// Create cria um novo relacionamento entre técnico e ordem
	Create(technicianID, orderID, createdBy uint, notes string) error

	// Delete remove um relacionamento entre técnico e ordem
	Delete(technicianID, orderID uint) error

	// GetByTechnician retorna todas as ordens associadas a um técnico
	GetByTechnician(technicianID uint) ([]TechnicianOrder, error)

	// GetByOrder retorna todos os técnicos associados a uma ordem
	GetByOrder(orderID uint) ([]TechnicianOrder, error)

	// Exists verifica se existe um relacionamento entre técnico e ordem
	Exists(technicianID, orderID uint) (bool, error)

	// GetAll retorna todos os relacionamentos
	GetAll() ([]TechnicianOrder, error)
}
