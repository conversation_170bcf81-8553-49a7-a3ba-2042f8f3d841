package models

import "time"

// ServiceProviderManager representa um gestor de empresa prestadora
type ServiceProviderManager struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	UserID            uint      `json:"user_id" gorm:"not null"`
	ServiceProviderID uint      `json:"service_provider_id" gorm:"not null"`
	Role              string    `json:"role" gorm:"not null"` // 'owner', 'manager', 'admin'
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
	
	// Relações
	User              User              `json:"user" gorm:"foreignKey:UserID"`
	ServiceProvider   ServiceProvider   `json:"service_provider" gorm:"foreignKey:ServiceProviderID"`
}
