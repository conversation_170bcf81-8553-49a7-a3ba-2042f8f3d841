package models

// Este arquivo contém funções de compatibilidade para lidar com a padronização
// das constantes de perfil de usuário, especialmente a mudança de "tecnico" para "technician".

// IsTechnician verifica se um perfil de usuário é um técnico,
// considerando tanto o valor padronizado "technician" quanto o valor legado "tecnico".
// Esta função deve ser usada em vez de comparações diretas com RoleTechnician
// para garantir compatibilidade com dados existentes.
func IsTechnician(role string) bool {
	return role == string(RoleTechnician) || role == "tecnico"
}

// IsProvider verifica se um perfil de usuário é um prestador de serviço,
// considerando tanto o valor padronizado "provider" quanto os valores legados "prestadores" e "prestador".
// Esta função deve ser usada em vez de comparações diretas com RolePrestador
// para garantir compatibilidade com dados existentes.
func IsProvider(role string) bool {
	return role == string(RolePrestador) || role == "prestadores" || role == "prestador"
}

// NormalizeRole normaliza um perfil de usuário para o valor padronizado.
// Por exemplo, converte "tecnico" para "technician" e "prestadores" para "provider".
func NormalizeRole(role string) string {
	switch role {
	case "tecnico":
		return string(RoleTechnician)
	case "prestadores", "prestador":
		return string(RolePrestador)
	default:
		return role
	}
}

// GetRoleConstant retorna a constante UserRole correspondente a uma string de perfil.
// Útil para converter strings de perfil em constantes UserRole.
func GetRoleConstant(role string) UserRole {
	switch role {
	case "admin":
		return RoleAdmin
	case "manager":
		return RoleGerente
	case "financial":
		return RoleFinanceiro
	case "filial":
		return RoleFilial
	case "branch_user":
		return RoleBranchUser
	case "technician", "tecnico":
		return RoleTechnician
	case "provider", "prestadores", "prestador":
		return RolePrestador
	default:
		// Se a string já for igual a uma das constantes, retorná-la diretamente
		if role == string(RoleAdmin) || role == string(RoleGerente) ||
			role == string(RoleFinanceiro) || role == string(RoleFilial) ||
			role == string(RoleBranchUser) || role == string(RoleTechnician) ||
			role == string(RolePrestador) {
			return UserRole(role)
		}
		// Caso contrário, retornar a string como UserRole
		return UserRole(role)
	}
}
