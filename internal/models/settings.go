package models

import (
	"time"
)

// SystemSettings representa as configurações do sistema
type SystemSettings struct {
	ID                 uint      `json:"id"`
	MaintenanceMode    bool      `json:"maintenance_mode"`
	PasswordPolicy     string    `json:"password_policy"`
	SessionTimeout     int       `json:"session_timeout"`
	MaxLoginAttempts   int       `json:"max_login_attempts"`
	RequireTwoFactor   bool      `json:"require_two_factor"`
	AllowRegistration  bool      `json:"allow_registration"`
	DefaultUserRole    string    `json:"default_user_role"`
	EmailNotifications bool      `json:"email_notifications"`
	SMSNotifications   bool      `json:"sms_notifications"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
}

// SecuritySettings representa as configurações de segurança
type SecuritySettings struct {
	ID                uint      `json:"id"`
	PasswordMinLength int       `json:"password_min_length"`
	PasswordExpiry    int       `json:"password_expiry"` // Em dias, 0 = nunca expira
	LockoutThreshold  int       `json:"lockout_threshold"`
	LockoutDuration   int       `json:"lockout_duration"` // Em minutos
	SessionTimeout    int       `json:"session_timeout"`  // Em minutos
	RequireTwoFactor  bool      `json:"require_two_factor"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// AuditSettings representa as configurações de auditoria
type AuditSettings struct {
	ID              uint      `json:"id"`
	EnableAudit     bool      `json:"enable_audit"`
	LogLogins       bool      `json:"log_logins"`
	LogFailedLogins bool      `json:"log_failed_logins"`
	LogDataChanges  bool      `json:"log_data_changes"`
	RetentionPeriod int       `json:"retention_period"` // Em dias
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// NotificationSettings representa as configurações de notificação
type NotificationSettings struct {
	ID                 uint      `json:"id"`
	EmailNotifications bool      `json:"email_notifications"`
	SMSNotifications   bool      `json:"sms_notifications"`
	PushNotifications  bool      `json:"push_notifications"`
	MaintenanceAlerts  bool      `json:"maintenance_alerts"`
	SecurityAlerts     bool      `json:"security_alerts"`
	SystemUpdates      bool      `json:"system_updates"`
	DailyDigest        bool      `json:"daily_digest"`
	WeeklyReport       bool      `json:"weekly_report"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
}

// Integration representa uma integração com sistema externo
type Integration struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Type        string    `json:"type"` // API, Webhook, etc.
	URL         string    `json:"url"`
	APIKey      string    `json:"api_key,omitempty"`
	Secret      string    `json:"secret,omitempty"`
	Active      bool      `json:"active"`
	LastSync    time.Time `json:"last_sync"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}
