package models

import (
	"time"

	"gorm.io/gorm"
)

// Branch representa uma filial no sistema
// IMPORTANTE: Este é o modelo principal e padronizado para representar filiais/postos no sistema.
// Todas as novas implementações devem usar este modelo em vez de Filial ou Station.
//
// Seguindo a convenção de nomenclatura padronizada, usamos o termo "branch" em inglês
// no código-fonte, enquanto na interface do usuário usamos "Filial" ou "Posto" em português.
//
// Para compatibilidade com código existente, use as funções em branch_compatibility.go:
// - BranchToFilial: converte Branch para Filial
// - BranchToStation: converte Branch para Station
// - FilialToBranch: converte Filial para Branch
// - StationToBranch: converte Station para Branch
//
// Para mais detalhes, consulte docs/guias/padronizacao_nomenclatura.md
type Branch struct {
	// Campos principais armazenados no banco de dados
	ID              uint           `gorm:"primaryKey" json:"id"`                       // ID único da filial
	Name            string         `gorm:"size:100;not null" json:"name"`              // Nome da filial
	Code            string         `gorm:"size:20;unique;not null" json:"code"`        // Código único da filial
	Address         string         `gorm:"size:255" json:"address"`                    // Endereço completo
	City            string         `gorm:"size:100" json:"city"`                       // Cidade
	State           string         `gorm:"size:2" json:"state"`                        // Estado (sigla)
	ZipCode         string         `gorm:"size:20" json:"zip_code"`                    // CEP
	Phone           string         `gorm:"size:20" json:"phone"`                       // Telefone principal
	Email           string         `gorm:"size:100" json:"email"`                      // Email de contato
	IsActive        bool           `gorm:"default:true" json:"is_active"`              // Indica se a filial está ativa
	BranchType      string         `gorm:"size:20;default:'urban'" json:"branch_type"` // Tipo: urban, highway, rural, industrial
	ResponsibleName string         `gorm:"size:100" json:"responsible_name"`           // Nome do responsável
	Notes           string         `gorm:"size:500" json:"notes"`                      // Observações gerais
	ContactInfo     string         `gorm:"size:500" json:"contact_info"`               // Informações de contato adicionais
	CreatedAt       time.Time      `json:"created_at"`                                 // Data de criação
	UpdatedAt       time.Time      `json:"updated_at"`                                 // Data de atualização
	DeletedAt       gorm.DeletedAt `gorm:"index" json:"-"`                             // Data de exclusão (soft delete)

	// Campos virtuais para compatibilidade com outros modelos
	// Estes campos não são armazenados no banco de dados
	Type         string   `json:"type" gorm:"-"`          // Alias para BranchType (compatibilidade com Station)
	PostalCode   string   `json:"postal_code" gorm:"-"`   // Alias para ZipCode (compatibilidade com Station)
	PhoneNumber  string   `json:"phone_number" gorm:"-"`  // Alias para Phone (compatibilidade com Station)
	Latitude     *float64 `json:"latitude" gorm:"-"`      // Coordenada geográfica
	Longitude    *float64 `json:"longitude" gorm:"-"`     // Coordenada geográfica
	ManagerID    *uint    `json:"manager_id" gorm:"-"`    // ID do gerente responsável
	ManagerName  string   `json:"manager_name" gorm:"-"`  // Nome do gerente responsável
	OpeningHours string   `json:"opening_hours" gorm:"-"` // Horário de funcionamento

	// Relações com outras entidades
	// Estas relações seguem o padrão definido em entity_relations.go
	Manager           *User              `json:"manager,omitempty" gorm:"foreignKey:ManagerID"`                  // Gerente responsável pela filial
	Equipment         []Equipment        `json:"equipment,omitempty" gorm:"foreignKey:BranchID"`                 // Equipamentos da filial
	MaintenanceOrders []MaintenanceOrder `json:"maintenance_orders,omitempty" gorm:"foreignKey:BranchID"`        // Ordens de manutenção da filial
	Technicians       []User             `json:"technicians,omitempty" gorm:"many2many:technician_branches"`     // Técnicos associados à filial
	ServiceProviders  []ServiceProvider  `json:"service_providers,omitempty" gorm:"many2many:provider_branches"` // Prestadores associados à filial
}

// TableName especifica o nome da tabela para o modelo Branch
func (Branch) TableName() string {
	return TableBranches
}

// ToFilial converte Branch para Filial
// Deprecated: Use Branch diretamente em vez de converter para Filial
// Esta função é mantida apenas para compatibilidade com código existente.
// Novas implementações devem usar o modelo Branch diretamente.
func (b *Branch) ToFilial() *Filial {
	return BranchToFilial(b)
}

// FromFilial converte Filial para Branch
// Deprecated: Use Branch diretamente em vez de converter de Filial
// Esta função é mantida apenas para compatibilidade com código existente.
// Novas implementações devem usar o modelo Branch diretamente.
func FromFilial(f *Filial) *Branch {
	return FilialToBranch(f)
}

// ToSummary converte Branch para BranchSummary
// Esta é a função principal para obter um resumo da filial.
// Novas implementações devem usar esta função.
func (b *Branch) ToSummary() BranchSummary {
	if b == nil {
		return BranchSummary{}
	}

	return BranchSummary{
		ID:       b.ID,
		Name:     b.Name,
		Code:     b.Code,
		Type:     b.BranchType,
		Address:  b.Address,
		City:     b.City,
		State:    b.State,
		IsActive: b.IsActive,
	}
}

// ToFilialSummary converte Branch para FilialSummary
// Deprecated: Use ToSummary em vez desta função.
// Esta função é mantida apenas para compatibilidade com código existente.
func (b *Branch) ToFilialSummary() FilialSummary {
	if b == nil {
		return FilialSummary{}
	}

	summary := b.ToSummary()
	return FilialSummary{
		ID:       summary.ID,
		Name:     summary.Name,
		Code:     summary.Code,
		Type:     summary.Type,
		Address:  summary.Address,
		City:     summary.City,
		State:    summary.State,
		IsActive: summary.IsActive,
	}
}

// ToStationSummary converte Branch para StationSummary
// Deprecated: Use ToSummary em vez desta função.
// Esta função é mantida apenas para compatibilidade com código existente.
func (b *Branch) ToStationSummary() StationSummary {
	if b == nil {
		return StationSummary{}
	}

	summary := b.ToSummary()
	return StationSummary{
		ID:       summary.ID,
		Name:     summary.Name,
		Code:     summary.Code,
		Type:     summary.Type,
		Address:  summary.Address,
		City:     summary.City,
		State:    summary.State,
		IsActive: summary.IsActive,
	}
}

// BranchSummary representa um resumo dos dados de uma filial
// Este é o modelo principal para resumos de filiais.
// Novas implementações devem usar este modelo em vez de FilialSummary ou StationSummary.
type BranchSummary struct {
	ID         uint   `json:"id"`                   // ID único da filial
	Name       string `json:"name"`                 // Nome da filial
	Code       string `json:"code"`                 // Código único da filial
	Type       string `json:"type,omitempty"`       // Tipo da filial (urban, highway, rural, industrial)
	Address    string `json:"address,omitempty"`    // Endereço completo
	City       string `json:"city,omitempty"`       // Cidade
	State      string `json:"state,omitempty"`      // Estado (sigla)
	IsActive   bool   `json:"isActive"`             // Indica se a filial está ativa
	BranchID   uint   `json:"branchId,omitempty"`   // ID da filial pai (para compatibilidade)
	BranchName string `json:"branchName,omitempty"` // Nome da filial pai (para compatibilidade)
}

// BranchMetrics representa métricas das filiais
// Este é o modelo principal para métricas de filiais.
// Novas implementações devem usar este modelo em vez de FilialMetrics ou StationMetrics.
type BranchMetrics struct {
	TotalBranches    int64            `json:"total_branches"`     // Total de filiais
	ActiveBranches   int64            `json:"active_branches"`    // Total de filiais ativas
	BranchesByRegion map[string]int64 `json:"branches_by_region"` // Filiais por região
}

// InitVirtualFields inicializa os campos virtuais do modelo Branch
// para compatibilidade com outros modelos.
func (b *Branch) InitVirtualFields() {
	if b == nil {
		return
	}

	// Inicializar campos virtuais para compatibilidade com Station
	b.Type = b.BranchType
	b.PostalCode = b.ZipCode
	b.PhoneNumber = b.Phone
}

// AfterFind é um hook do GORM que é executado após carregar um registro do banco de dados
func (b *Branch) AfterFind(tx *gorm.DB) error {
	b.InitVirtualFields()
	return nil
}

// BeforeSave é um hook do GORM que é executado antes de salvar um registro no banco de dados
func (b *Branch) BeforeSave(tx *gorm.DB) error {
	// Sincronizar campos virtuais com campos reais
	if b.Type != "" && b.BranchType == "" {
		b.BranchType = b.Type
	}
	if b.PostalCode != "" && b.ZipCode == "" {
		b.ZipCode = b.PostalCode
	}
	if b.PhoneNumber != "" && b.Phone == "" {
		b.Phone = b.PhoneNumber
	}

	// Normalizar o tipo de filial
	b.BranchType = NormalizeBranchType(b.BranchType)

	return nil
}
