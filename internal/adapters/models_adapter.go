package adapters

import (
	"tradicao/internal/models"
)

// ConvertStationToFilial converte uma Station para Filial
func ConvertStationToFilial(station *models.Station) *models.Filial {
	if station == nil {
		return nil
	}

	return models.FromStation(station)
}

// ConvertFilialToStation converte uma Filial para Station
func ConvertFilialToStation(filial *models.Filial) *models.Station {
	if filial == nil {
		return nil
	}

	return filial.ToStation()
}

// ConvertBranchToFilial converte uma Branch para Filial
func ConvertBranchToFilial(branch *models.Branch) *models.Filial {
	if branch == nil {
		return nil
	}

	return branch.ToFilial()
}

// ConvertFilialToBranch converte uma Filial para Branch
func ConvertFilialToBranch(filial *models.Filial) *models.Branch {
	if filial == nil {
		return nil
	}

	return models.FromFilial(filial)
}
