package permissions

import (
	"log"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// Middleware é o middleware de permissões para o Gin
type Middleware struct {
	service *Service
}

// NewMiddleware cria um novo middleware de permissões
func NewMiddleware(service *Service) *Middleware {
	return &Middleware{
		service: service,
	}
}

// PageAccessMiddleware verifica se o usuário tem permissão para acessar uma página
func (m *Middleware) PageAccessMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Obtém a role do usuário do contexto
		userRole, exists := c.Get("userRole")
		if !exists {
			c.HTML(http.StatusUnauthorized, "auth/acesso_negado.html", gin.H{
				"title":   "Acesso Negado",
				"message": "Usuário não autenticado",
			})
			c.Abort()
			return
		}

		// Obtém o caminho da página atual
		path := c.Request.URL.Path

		// Verifica se o usuário tem permissão para acessar a página
		if !m.service.HasPermission(userRole.(string), path, PagePermission) {
			userID, _ := c.Get("userID")
			email, _ := c.Get("email")

			// Obtém a lista de papéis com permissão para esta página
			permittedRoles := m.service.GetPermittedRoles(path, PagePermission)

			log.Printf("ACESSO NEGADO: Usuário %v (email: %s, role: %s) tentou acessar página: %s (permissões requeridas: %v)",
				userID, email, userRole, path, permittedRoles)

			// Formata a lista de papéis permitidos como string para log
			var permittedRolesStr string
			if len(permittedRoles) > 0 {
				permittedRolesStr = strings.Join(permittedRoles, ", ")
			} else {
				permittedRolesStr = "Informação de permissão não disponível"
			}

			c.HTML(http.StatusForbidden, "auth/acesso_negado.html", gin.H{
				"title":   "Acesso Negado",
				"message": "Você não tem permissão para acessar esta página.",
				"user": gin.H{
					"ID":    userID,
					"Email": email,
					"Role":  userRole,
				},
				"path":              path,
				"permittedRoles":    permittedRoles,
				"permittedRolesStr": permittedRolesStr,
			})
			c.Abort()
			return
		}

		// Log de acesso permitido
		log.Printf("ACESSO PERMITIDO: Usuário com role '%s' acessou página: %s", userRole, path)

		c.Next()
	}
}

// APIAccessMiddleware verifica se o usuário tem permissão para acessar uma API
func (m *Middleware) APIAccessMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Obtém a role do usuário do contexto
		userRole, exists := c.Get("userRole")
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error": "Usuário não autenticado",
			})
			return
		}

		// Obtém o caminho da API atual
		path := c.Request.URL.Path
		method := c.Request.Method

		// Log detalhado para debug
		log.Printf("[AUTH-DEBUG] Verificando permissão para: %s, método: %s, perfil: %s",
			path, method, userRole.(string))

		// Verifica se o usuário tem permissão para acessar a API
		if !m.service.HasPermission(userRole.(string), path, APIPermission) {
			userID, _ := c.Get("userID")
			email, _ := c.Get("email")

			// Obtém a lista de papéis com permissão para esta API
			permittedRoles := m.service.GetPermittedRoles(path, APIPermission)

			log.Printf("[AUTH-ERROR] ACESSO NEGADO: Usuário %v (email: %s, role: %s) tentou acessar API: %s (permissões requeridas: %v)",
				userID, email, userRole, path, permittedRoles)

			c.JSON(http.StatusForbidden, gin.H{
				"error": "Você não tem permissão para acessar este recurso",
			})
			c.Abort()
			return
		}

		log.Printf("[AUTH-DEBUG] Acesso permitido para usuário com role '%s' à API: %s", userRole, path)
		c.Next()
	}
}

// RoleMiddleware verifica se o usuário tem um dos papéis especificados
func (m *Middleware) RoleMiddleware(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("userRole")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Role do usuário não encontrada"})
			c.Abort()
			return
		}

		hasRole := false
		for _, role := range roles {
			if userRole.(string) == role {
				hasRole = true
				break
			}
		}

		if !hasRole {
			c.JSON(http.StatusForbidden, gin.H{"error": "Usuário não tem permissão para acessar este recurso"})
			c.Abort()
			return
		}

		c.Next()
	}
}
