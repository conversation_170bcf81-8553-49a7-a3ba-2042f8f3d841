package permissions

import (
	"log"
	"sync"
	"tradicao/internal/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Variáveis globais para armazenar os serviços e middlewares de permissões
var (
	globalPermissionsMiddleware       *Middleware
	globalUnifiedPermissionService    *UnifiedPermissionService
	globalUnifiedPermissionMiddleware *UnifiedMiddleware
	globalMutex                       sync.RWMutex
)

// SetGlobalMiddleware define o middleware global de permissões
func SetGlobalMiddleware(middleware *Middleware) {
	globalMutex.Lock()
	defer globalMutex.Unlock()

	globalPermissionsMiddleware = middleware
	log.Println("Middleware global de permissões configurado")
}

// GetGlobalMiddleware retorna o middleware global de permissões
func GetGlobalMiddleware() *Middleware {
	globalMutex.RLock()
	defer globalMutex.RUnlock()

	return globalPermissionsMiddleware
}

// GetGlobalService retorna o serviço de permissões global
func GetGlobalService() *Service {
	globalMutex.RLock()
	defer globalMutex.RUnlock()

	if globalPermissionsMiddleware == nil {
		return nil
	}
	return globalPermissionsMiddleware.service
}

// SetGlobalUnifiedService define o serviço unificado global de permissões
func SetGlobalUnifiedService(service *UnifiedPermissionService) {
	globalMutex.Lock()
	defer globalMutex.Unlock()

	globalUnifiedPermissionService = service
	log.Println("Serviço unificado global de permissões configurado")
}

// GetGlobalUnifiedService retorna o serviço unificado global de permissões
func GetGlobalUnifiedService() *UnifiedPermissionService {
	globalMutex.RLock()
	defer globalMutex.RUnlock()

	return globalUnifiedPermissionService
}

// SetGlobalUnifiedMiddleware define o middleware unificado global de permissões
func SetGlobalUnifiedMiddleware(middleware *UnifiedMiddleware) {
	globalMutex.Lock()
	defer globalMutex.Unlock()

	globalUnifiedPermissionMiddleware = middleware
	log.Println("Middleware unificado global de permissões configurado")
}

// GetGlobalUnifiedMiddleware retorna o middleware unificado global de permissões
func GetGlobalUnifiedMiddleware() *UnifiedMiddleware {
	globalMutex.RLock()
	defer globalMutex.RUnlock()

	return globalUnifiedPermissionMiddleware
}

// InitializeGlobalPermissions inicializa o sistema global de permissões
func InitializeGlobalPermissions(db *gorm.DB, configPath string) error {
	// Inicializar o serviço de permissões existente
	service, err := NewService(configPath)
	if err != nil {
		return err
	}

	// Inicializar o middleware existente
	middleware := NewMiddleware(service)

	// Inicializar o serviço unificado
	unifiedService := NewUnifiedPermissionService(db, service)

	// Inicializar o middleware unificado
	unifiedMiddleware := NewUnifiedMiddleware(unifiedService)

	// Configurar as variáveis globais
	SetGlobalMiddleware(middleware)
	SetGlobalUnifiedService(unifiedService)
	SetGlobalUnifiedMiddleware(unifiedMiddleware)

	log.Println("Sistema global de permissões inicializado com sucesso")
	return nil
}

// UpdateGlobalPermissions atualiza o sistema global de permissões
func UpdateGlobalPermissions(configPath string) error {
	globalMutex.Lock()
	defer globalMutex.Unlock()

	// Recarregar o serviço de permissões existente
	service, err := NewService(configPath)
	if err != nil {
		return err
	}

	// Atualizar o middleware existente
	middleware := NewMiddleware(service)

	// Atualizar o serviço unificado
	unifiedService := NewUnifiedPermissionService(globalUnifiedPermissionService.db, service)

	// Se o serviço de atribuição de ordens estiver configurado, mantê-lo
	if globalUnifiedPermissionService != nil && globalUnifiedPermissionService.orderAssignmentSvc != nil {
		unifiedService.SetOrderAssignmentService(globalUnifiedPermissionService.orderAssignmentSvc)
	}

	// Atualizar o middleware unificado
	unifiedMiddleware := NewUnifiedMiddleware(unifiedService)

	// Atualizar as variáveis globais
	globalPermissionsMiddleware = middleware
	globalUnifiedPermissionService = unifiedService
	globalUnifiedPermissionMiddleware = unifiedMiddleware

	log.Println("Sistema global de permissões atualizado com sucesso")
	return nil
}

// RoleMiddleware é uma função auxiliar para compatibilidade com o middleware antigo
// Converte os tipos UserRole para string e chama o novo middleware
func RoleMiddleware(roles ...models.UserRole) gin.HandlerFunc {
	globalMutex.RLock()
	defer globalMutex.RUnlock()
	
	if globalPermissionsMiddleware == nil {
		log.Println("AVISO: Middleware global de permissões não configurado, usando implementação padrão")
		return func(c *gin.Context) {
			c.Next()
		}
	}

	// Converter UserRole para string
	stringRoles := make([]string, len(roles))
	for i, role := range roles {
		stringRoles[i] = string(role)
	}

	return globalPermissionsMiddleware.RoleMiddleware(stringRoles...)
}
