package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// Equipment holds the schema definition for the Equipment entity.
type Equipment struct {
	ent.Schema
}

// Mixin of the Equipment.
func (Equipment) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
	}
}

// Fields of the Equipment.
func (Equipment) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").NotEmpty(),
		field.String("type").NotEmpty(),
		field.String("model").NotEmpty(),
		field.String("serial_number").Unique().NotEmpty(),
		field.Time("installation_date").Optional(),
		field.Time("last_maintenance_date").Optional(),
		field.Int("branch_id").Optional(),
	}
}

// Edges of the Equipment.
func (Equipment) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("branch", Branch.Type).Ref("equipment").Unique().Field("branch_id"),
		edge.To("maintenance_orders", MaintenanceOrder.Type),
	}
}
