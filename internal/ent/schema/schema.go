package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/mixin"
)

// BaseMixin implementa campos comuns a todas as entidades
type BaseMixin struct {
	mixin.Schema
}

func (BaseMixin) Fields() []ent.Field {
	return []ent.Field{
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
		field.Bool("active").
			Default(true),
	}
}

// Status representa o status de uma entidade
type Status string

const (
	StatusAtivo    Status = "ativo"
	StatusInativo  Status = "inativo"
	StatusPendente Status = "pendente"
)

// Prioridade representa o nível de prioridade
type Prioridade string

const (
	PrioridadeBaixa   Prioridade = "baixa"
	PrioridadeMedia   Prioridade = "media"
	PrioridadeAlta    Prioridade = "alta"
	PrioridadeUrgente Prioridade = "urgente"
)
