package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// User holds the schema definition for the User entity.
type User struct {
	ent.Schema
}

// Mixin of the User.
func (User) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
	}
}

// Fields of the User.
func (User) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").NotEmpty(),
		field.String("email").Unique().NotEmpty(),
		field.String("password").NotEmpty(),
		field.String("role").NotEmpty(),
	}
}

// Edges of the User.
func (User) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("managed_branches", Branch.Type),
		edge.To("requested_orders", MaintenanceOrder.Type),
		edge.To("assigned_orders", MaintenanceOrder.Type),
		edge.To("approved_orders", MaintenanceOrder.Type),
		edge.To("cost_items", CostItem.Type),
		edge.To("interactions", Interaction.Type),
	}
}
