package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// CostItem holds the schema definition for the CostItem entity.
type CostItem struct {
	ent.Schema
}

// Mixin of the CostItem.
func (CostItem) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
	}
}

// Fields of the CostItem.
func (CostItem) Fields() []ent.Field {
	return []ent.Field{
		field.Int("maintenance_order_id").Optional(),
		field.Int("user_id").Optional(),
		field.String("description").NotEmpty(),
		field.Float("quantity").Default(1),
		field.Float("unit_price").Default(0),
		field.Float("total_price").Default(0),
	}
}

// Edges of the CostItem.
func (CostItem) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("maintenance_order", MaintenanceOrder.Type).Ref("cost_items").Unique().Field("maintenance_order_id"),
		edge.From("user", User.Type).Ref("cost_items").Unique().Field("user_id"),
	}
}
