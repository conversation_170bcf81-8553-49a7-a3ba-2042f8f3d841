package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// MaintenanceOrder holds the schema definition for the MaintenanceOrder entity.
type MaintenanceOrder struct {
	ent.Schema
}

// Mixin of the MaintenanceOrder.
func (MaintenanceOrder) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
	}
}

// Fields of the MaintenanceOrder.
func (MaintenanceOrder) Fields() []ent.Field {
	return []ent.Field{
		field.String("title").NotEmpty(),
		field.String("description").NotEmpty(),
		field.String("status").Default("pending"),
		field.String("priority").Default("medium"),
		field.Int("branch_id").Optional(),
		field.Int("equipment_id").Optional(),
		field.Int("requester_id").Optional(),
		field.Int("approver_id").Optional(),
		field.Int("technician_id").Optional(),
		field.String("cancellation_reason").Optional(),
		field.String("rejection_reason").Optional(),
	}
}

// Edges of the MaintenanceOrder.
func (MaintenanceOrder) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("branch", Branch.Type).Ref("maintenance_orders").Unique().Field("branch_id"),
		edge.From("equipment", Equipment.Type).Ref("maintenance_orders").Unique().Field("equipment_id"),
		edge.From("requester", User.Type).Ref("requested_orders").Unique().Field("requester_id"),
		edge.From("approver", User.Type).Ref("approved_orders").Unique().Field("approver_id"),
		edge.From("technician", User.Type).Ref("assigned_orders").Unique().Field("technician_id"),
		edge.To("cost_items", CostItem.Type),
		edge.To("interactions", Interaction.Type),
	}
}
