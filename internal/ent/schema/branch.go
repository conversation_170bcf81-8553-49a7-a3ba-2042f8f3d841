package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// Branch holds the schema definition for the Branch entity.
type Branch struct {
	ent.Schema
}

// Mixin of the Branch.
func (Branch) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
	}
}

// Fields of the Branch.
func (Branch) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").NotEmpty(),
		field.String("code").NotEmpty(),
		field.String("address").NotEmpty(),
		field.String("city").NotEmpty(),
		field.String("state").NotEmpty(),
		field.String("zip_code").NotEmpty(),
		field.String("phone").NotEmpty(),
		field.String("email").Optional(),
		field.String("status").Default(string(StatusAtivo)),
		field.Time("inauguration_date").Default(time.Now),
		field.Bool("is_active").Default(true),
	}
}

// Edges of the Branch.
func (Branch) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("equipment", Equipment.Type),
		edge.To("maintenance_orders", MaintenanceOrder.Type),
	}
}
