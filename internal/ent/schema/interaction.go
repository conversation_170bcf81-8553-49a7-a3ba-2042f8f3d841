package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/mixin"
)

// Interaction representa uma interação em uma ordem de manutenção
type Interaction struct {
	ent.Schema
}

// Mixin do Interaction
func (Interaction) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.Time{},
	}
}

// Fields do Interaction
func (Interaction) Fields() []ent.Field {
	return []ent.Field{
		field.String("message").
			NotEmpty().
			MaxLen(1000),
		field.String("type").
			NotEmpty().
			Default("comment"),
	}
}

// Edges do Interaction
func (Interaction) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("maintenance_order", MaintenanceOrder.Type).
			Ref("interactions").
			Unique().
			Required(),
		edge.From("user", User.Type).
			Ref("interactions").
			Unique().
			Required(),
	}
}
