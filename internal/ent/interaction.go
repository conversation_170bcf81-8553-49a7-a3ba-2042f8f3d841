// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"
	"tradicao/internal/ent/interaction"
	"tradicao/internal/ent/maintenanceorder"
	"tradicao/internal/ent/user"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Interaction is the model entity for the Interaction schema.
type Interaction struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// CreateTime holds the value of the "create_time" field.
	CreateTime time.Time `json:"create_time,omitempty"`
	// UpdateTime holds the value of the "update_time" field.
	UpdateTime time.Time `json:"update_time,omitempty"`
	// Message holds the value of the "message" field.
	Message string `json:"message,omitempty"`
	// Type holds the value of the "type" field.
	Type string `json:"type,omitempty"`
	// <PERSON>s holds the relations/edges for other nodes in the graph.
	// The values are being populated by the InteractionQuery when eager-loading is set.
	Edges                          InteractionEdges `json:"edges"`
	maintenance_order_interactions *int
	user_interactions              *int
	selectValues                   sql.SelectValues
}

// InteractionEdges holds the relations/edges for other nodes in the graph.
type InteractionEdges struct {
	// MaintenanceOrder holds the value of the maintenance_order edge.
	MaintenanceOrder *MaintenanceOrder `json:"maintenance_order,omitempty"`
	// User holds the value of the user edge.
	User *User `json:"user,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// MaintenanceOrderOrErr returns the MaintenanceOrder value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e InteractionEdges) MaintenanceOrderOrErr() (*MaintenanceOrder, error) {
	if e.MaintenanceOrder != nil {
		return e.MaintenanceOrder, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: maintenanceorder.Label}
	}
	return nil, &NotLoadedError{edge: "maintenance_order"}
}

// UserOrErr returns the User value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e InteractionEdges) UserOrErr() (*User, error) {
	if e.User != nil {
		return e.User, nil
	} else if e.loadedTypes[1] {
		return nil, &NotFoundError{label: user.Label}
	}
	return nil, &NotLoadedError{edge: "user"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Interaction) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case interaction.FieldID:
			values[i] = new(sql.NullInt64)
		case interaction.FieldMessage, interaction.FieldType:
			values[i] = new(sql.NullString)
		case interaction.FieldCreateTime, interaction.FieldUpdateTime:
			values[i] = new(sql.NullTime)
		case interaction.ForeignKeys[0]: // maintenance_order_interactions
			values[i] = new(sql.NullInt64)
		case interaction.ForeignKeys[1]: // user_interactions
			values[i] = new(sql.NullInt64)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Interaction fields.
func (i *Interaction) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for j := range columns {
		switch columns[j] {
		case interaction.FieldID:
			value, ok := values[j].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			i.ID = int(value.Int64)
		case interaction.FieldCreateTime:
			if value, ok := values[j].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[j])
			} else if value.Valid {
				i.CreateTime = value.Time
			}
		case interaction.FieldUpdateTime:
			if value, ok := values[j].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[j])
			} else if value.Valid {
				i.UpdateTime = value.Time
			}
		case interaction.FieldMessage:
			if value, ok := values[j].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field message", values[j])
			} else if value.Valid {
				i.Message = value.String
			}
		case interaction.FieldType:
			if value, ok := values[j].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[j])
			} else if value.Valid {
				i.Type = value.String
			}
		case interaction.ForeignKeys[0]:
			if value, ok := values[j].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for edge-field maintenance_order_interactions", value)
			} else if value.Valid {
				i.maintenance_order_interactions = new(int)
				*i.maintenance_order_interactions = int(value.Int64)
			}
		case interaction.ForeignKeys[1]:
			if value, ok := values[j].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for edge-field user_interactions", value)
			} else if value.Valid {
				i.user_interactions = new(int)
				*i.user_interactions = int(value.Int64)
			}
		default:
			i.selectValues.Set(columns[j], values[j])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Interaction.
// This includes values selected through modifiers, order, etc.
func (i *Interaction) Value(name string) (ent.Value, error) {
	return i.selectValues.Get(name)
}

// QueryMaintenanceOrder queries the "maintenance_order" edge of the Interaction entity.
func (i *Interaction) QueryMaintenanceOrder() *MaintenanceOrderQuery {
	return NewInteractionClient(i.config).QueryMaintenanceOrder(i)
}

// QueryUser queries the "user" edge of the Interaction entity.
func (i *Interaction) QueryUser() *UserQuery {
	return NewInteractionClient(i.config).QueryUser(i)
}

// Update returns a builder for updating this Interaction.
// Note that you need to call Interaction.Unwrap() before calling this method if this Interaction
// was returned from a transaction, and the transaction was committed or rolled back.
func (i *Interaction) Update() *InteractionUpdateOne {
	return NewInteractionClient(i.config).UpdateOne(i)
}

// Unwrap unwraps the Interaction entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (i *Interaction) Unwrap() *Interaction {
	_tx, ok := i.config.driver.(*txDriver)
	if !ok {
		panic("ent: Interaction is not a transactional entity")
	}
	i.config.driver = _tx.drv
	return i
}

// String implements the fmt.Stringer.
func (i *Interaction) String() string {
	var builder strings.Builder
	builder.WriteString("Interaction(")
	builder.WriteString(fmt.Sprintf("id=%v, ", i.ID))
	builder.WriteString("create_time=")
	builder.WriteString(i.CreateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_time=")
	builder.WriteString(i.UpdateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("message=")
	builder.WriteString(i.Message)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(i.Type)
	builder.WriteByte(')')
	return builder.String()
}

// Interactions is a parsable slice of Interaction.
type Interactions []*Interaction
