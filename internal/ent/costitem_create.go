// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"
	"tradicao/internal/ent/costitem"
	"tradicao/internal/ent/maintenanceorder"
	"tradicao/internal/ent/user"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CostItemCreate is the builder for creating a CostItem entity.
type CostItemCreate struct {
	config
	mutation *CostItemMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (cic *CostItemCreate) SetCreatedAt(t time.Time) *CostItemCreate {
	cic.mutation.SetCreatedAt(t)
	return cic
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (cic *CostItemCreate) SetNillableCreatedAt(t *time.Time) *CostItemCreate {
	if t != nil {
		cic.SetCreatedAt(*t)
	}
	return cic
}

// SetUpdatedAt sets the "updated_at" field.
func (cic *CostItemCreate) SetUpdatedAt(t time.Time) *CostItemCreate {
	cic.mutation.SetUpdatedAt(t)
	return cic
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (cic *CostItemCreate) SetNillableUpdatedAt(t *time.Time) *CostItemCreate {
	if t != nil {
		cic.SetUpdatedAt(*t)
	}
	return cic
}

// SetActive sets the "active" field.
func (cic *CostItemCreate) SetActive(b bool) *CostItemCreate {
	cic.mutation.SetActive(b)
	return cic
}

// SetNillableActive sets the "active" field if the given value is not nil.
func (cic *CostItemCreate) SetNillableActive(b *bool) *CostItemCreate {
	if b != nil {
		cic.SetActive(*b)
	}
	return cic
}

// SetMaintenanceOrderID sets the "maintenance_order_id" field.
func (cic *CostItemCreate) SetMaintenanceOrderID(i int) *CostItemCreate {
	cic.mutation.SetMaintenanceOrderID(i)
	return cic
}

// SetNillableMaintenanceOrderID sets the "maintenance_order_id" field if the given value is not nil.
func (cic *CostItemCreate) SetNillableMaintenanceOrderID(i *int) *CostItemCreate {
	if i != nil {
		cic.SetMaintenanceOrderID(*i)
	}
	return cic
}

// SetUserID sets the "user_id" field.
func (cic *CostItemCreate) SetUserID(i int) *CostItemCreate {
	cic.mutation.SetUserID(i)
	return cic
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (cic *CostItemCreate) SetNillableUserID(i *int) *CostItemCreate {
	if i != nil {
		cic.SetUserID(*i)
	}
	return cic
}

// SetDescription sets the "description" field.
func (cic *CostItemCreate) SetDescription(s string) *CostItemCreate {
	cic.mutation.SetDescription(s)
	return cic
}

// SetQuantity sets the "quantity" field.
func (cic *CostItemCreate) SetQuantity(f float64) *CostItemCreate {
	cic.mutation.SetQuantity(f)
	return cic
}

// SetNillableQuantity sets the "quantity" field if the given value is not nil.
func (cic *CostItemCreate) SetNillableQuantity(f *float64) *CostItemCreate {
	if f != nil {
		cic.SetQuantity(*f)
	}
	return cic
}

// SetUnitPrice sets the "unit_price" field.
func (cic *CostItemCreate) SetUnitPrice(f float64) *CostItemCreate {
	cic.mutation.SetUnitPrice(f)
	return cic
}

// SetNillableUnitPrice sets the "unit_price" field if the given value is not nil.
func (cic *CostItemCreate) SetNillableUnitPrice(f *float64) *CostItemCreate {
	if f != nil {
		cic.SetUnitPrice(*f)
	}
	return cic
}

// SetTotalPrice sets the "total_price" field.
func (cic *CostItemCreate) SetTotalPrice(f float64) *CostItemCreate {
	cic.mutation.SetTotalPrice(f)
	return cic
}

// SetNillableTotalPrice sets the "total_price" field if the given value is not nil.
func (cic *CostItemCreate) SetNillableTotalPrice(f *float64) *CostItemCreate {
	if f != nil {
		cic.SetTotalPrice(*f)
	}
	return cic
}

// SetMaintenanceOrder sets the "maintenance_order" edge to the MaintenanceOrder entity.
func (cic *CostItemCreate) SetMaintenanceOrder(m *MaintenanceOrder) *CostItemCreate {
	return cic.SetMaintenanceOrderID(m.ID)
}

// SetUser sets the "user" edge to the User entity.
func (cic *CostItemCreate) SetUser(u *User) *CostItemCreate {
	return cic.SetUserID(u.ID)
}

// Mutation returns the CostItemMutation object of the builder.
func (cic *CostItemCreate) Mutation() *CostItemMutation {
	return cic.mutation
}

// Save creates the CostItem in the database.
func (cic *CostItemCreate) Save(ctx context.Context) (*CostItem, error) {
	cic.defaults()
	return withHooks(ctx, cic.sqlSave, cic.mutation, cic.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (cic *CostItemCreate) SaveX(ctx context.Context) *CostItem {
	v, err := cic.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (cic *CostItemCreate) Exec(ctx context.Context) error {
	_, err := cic.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cic *CostItemCreate) ExecX(ctx context.Context) {
	if err := cic.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cic *CostItemCreate) defaults() {
	if _, ok := cic.mutation.CreatedAt(); !ok {
		v := costitem.DefaultCreatedAt()
		cic.mutation.SetCreatedAt(v)
	}
	if _, ok := cic.mutation.UpdatedAt(); !ok {
		v := costitem.DefaultUpdatedAt()
		cic.mutation.SetUpdatedAt(v)
	}
	if _, ok := cic.mutation.Active(); !ok {
		v := costitem.DefaultActive
		cic.mutation.SetActive(v)
	}
	if _, ok := cic.mutation.Quantity(); !ok {
		v := costitem.DefaultQuantity
		cic.mutation.SetQuantity(v)
	}
	if _, ok := cic.mutation.UnitPrice(); !ok {
		v := costitem.DefaultUnitPrice
		cic.mutation.SetUnitPrice(v)
	}
	if _, ok := cic.mutation.TotalPrice(); !ok {
		v := costitem.DefaultTotalPrice
		cic.mutation.SetTotalPrice(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (cic *CostItemCreate) check() error {
	if _, ok := cic.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "CostItem.created_at"`)}
	}
	if _, ok := cic.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "CostItem.updated_at"`)}
	}
	if _, ok := cic.mutation.Active(); !ok {
		return &ValidationError{Name: "active", err: errors.New(`ent: missing required field "CostItem.active"`)}
	}
	if _, ok := cic.mutation.Description(); !ok {
		return &ValidationError{Name: "description", err: errors.New(`ent: missing required field "CostItem.description"`)}
	}
	if v, ok := cic.mutation.Description(); ok {
		if err := costitem.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "CostItem.description": %w`, err)}
		}
	}
	if _, ok := cic.mutation.Quantity(); !ok {
		return &ValidationError{Name: "quantity", err: errors.New(`ent: missing required field "CostItem.quantity"`)}
	}
	if _, ok := cic.mutation.UnitPrice(); !ok {
		return &ValidationError{Name: "unit_price", err: errors.New(`ent: missing required field "CostItem.unit_price"`)}
	}
	if _, ok := cic.mutation.TotalPrice(); !ok {
		return &ValidationError{Name: "total_price", err: errors.New(`ent: missing required field "CostItem.total_price"`)}
	}
	return nil
}

func (cic *CostItemCreate) sqlSave(ctx context.Context) (*CostItem, error) {
	if err := cic.check(); err != nil {
		return nil, err
	}
	_node, _spec := cic.createSpec()
	if err := sqlgraph.CreateNode(ctx, cic.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	cic.mutation.id = &_node.ID
	cic.mutation.done = true
	return _node, nil
}

func (cic *CostItemCreate) createSpec() (*CostItem, *sqlgraph.CreateSpec) {
	var (
		_node = &CostItem{config: cic.config}
		_spec = sqlgraph.NewCreateSpec(costitem.Table, sqlgraph.NewFieldSpec(costitem.FieldID, field.TypeInt))
	)
	if value, ok := cic.mutation.CreatedAt(); ok {
		_spec.SetField(costitem.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := cic.mutation.UpdatedAt(); ok {
		_spec.SetField(costitem.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := cic.mutation.Active(); ok {
		_spec.SetField(costitem.FieldActive, field.TypeBool, value)
		_node.Active = value
	}
	if value, ok := cic.mutation.Description(); ok {
		_spec.SetField(costitem.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := cic.mutation.Quantity(); ok {
		_spec.SetField(costitem.FieldQuantity, field.TypeFloat64, value)
		_node.Quantity = value
	}
	if value, ok := cic.mutation.UnitPrice(); ok {
		_spec.SetField(costitem.FieldUnitPrice, field.TypeFloat64, value)
		_node.UnitPrice = value
	}
	if value, ok := cic.mutation.TotalPrice(); ok {
		_spec.SetField(costitem.FieldTotalPrice, field.TypeFloat64, value)
		_node.TotalPrice = value
	}
	if nodes := cic.mutation.MaintenanceOrderIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   costitem.MaintenanceOrderTable,
			Columns: []string{costitem.MaintenanceOrderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.MaintenanceOrderID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := cic.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   costitem.UserTable,
			Columns: []string{costitem.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.UserID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// CostItemCreateBulk is the builder for creating many CostItem entities in bulk.
type CostItemCreateBulk struct {
	config
	err      error
	builders []*CostItemCreate
}

// Save creates the CostItem entities in the database.
func (cicb *CostItemCreateBulk) Save(ctx context.Context) ([]*CostItem, error) {
	if cicb.err != nil {
		return nil, cicb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(cicb.builders))
	nodes := make([]*CostItem, len(cicb.builders))
	mutators := make([]Mutator, len(cicb.builders))
	for i := range cicb.builders {
		func(i int, root context.Context) {
			builder := cicb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*CostItemMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, cicb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, cicb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, cicb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (cicb *CostItemCreateBulk) SaveX(ctx context.Context) []*CostItem {
	v, err := cicb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (cicb *CostItemCreateBulk) Exec(ctx context.Context) error {
	_, err := cicb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cicb *CostItemCreateBulk) ExecX(ctx context.Context) {
	if err := cicb.Exec(ctx); err != nil {
		panic(err)
	}
}
