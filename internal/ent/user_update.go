// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"
	"tradicao/internal/ent/branch"
	"tradicao/internal/ent/costitem"
	"tradicao/internal/ent/interaction"
	"tradicao/internal/ent/maintenanceorder"
	"tradicao/internal/ent/predicate"
	"tradicao/internal/ent/user"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UserUpdate is the builder for updating User entities.
type UserUpdate struct {
	config
	hooks    []Hook
	mutation *UserMutation
}

// Where appends a list predicates to the UserUpdate builder.
func (uu *UserUpdate) Where(ps ...predicate.User) *UserUpdate {
	uu.mutation.Where(ps...)
	return uu
}

// SetUpdatedAt sets the "updated_at" field.
func (uu *UserUpdate) SetUpdatedAt(t time.Time) *UserUpdate {
	uu.mutation.SetUpdatedAt(t)
	return uu
}

// SetActive sets the "active" field.
func (uu *UserUpdate) SetActive(b bool) *UserUpdate {
	uu.mutation.SetActive(b)
	return uu
}

// SetNillableActive sets the "active" field if the given value is not nil.
func (uu *UserUpdate) SetNillableActive(b *bool) *UserUpdate {
	if b != nil {
		uu.SetActive(*b)
	}
	return uu
}

// SetName sets the "name" field.
func (uu *UserUpdate) SetName(s string) *UserUpdate {
	uu.mutation.SetName(s)
	return uu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (uu *UserUpdate) SetNillableName(s *string) *UserUpdate {
	if s != nil {
		uu.SetName(*s)
	}
	return uu
}

// SetEmail sets the "email" field.
func (uu *UserUpdate) SetEmail(s string) *UserUpdate {
	uu.mutation.SetEmail(s)
	return uu
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (uu *UserUpdate) SetNillableEmail(s *string) *UserUpdate {
	if s != nil {
		uu.SetEmail(*s)
	}
	return uu
}

// SetPassword sets the "password" field.
func (uu *UserUpdate) SetPassword(s string) *UserUpdate {
	uu.mutation.SetPassword(s)
	return uu
}

// SetNillablePassword sets the "password" field if the given value is not nil.
func (uu *UserUpdate) SetNillablePassword(s *string) *UserUpdate {
	if s != nil {
		uu.SetPassword(*s)
	}
	return uu
}

// SetRole sets the "role" field.
func (uu *UserUpdate) SetRole(s string) *UserUpdate {
	uu.mutation.SetRole(s)
	return uu
}

// SetNillableRole sets the "role" field if the given value is not nil.
func (uu *UserUpdate) SetNillableRole(s *string) *UserUpdate {
	if s != nil {
		uu.SetRole(*s)
	}
	return uu
}

// AddManagedBranchIDs adds the "managed_branches" edge to the Branch entity by IDs.
func (uu *UserUpdate) AddManagedBranchIDs(ids ...int) *UserUpdate {
	uu.mutation.AddManagedBranchIDs(ids...)
	return uu
}

// AddManagedBranches adds the "managed_branches" edges to the Branch entity.
func (uu *UserUpdate) AddManagedBranches(b ...*Branch) *UserUpdate {
	ids := make([]int, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return uu.AddManagedBranchIDs(ids...)
}

// AddRequestedOrderIDs adds the "requested_orders" edge to the MaintenanceOrder entity by IDs.
func (uu *UserUpdate) AddRequestedOrderIDs(ids ...int) *UserUpdate {
	uu.mutation.AddRequestedOrderIDs(ids...)
	return uu
}

// AddRequestedOrders adds the "requested_orders" edges to the MaintenanceOrder entity.
func (uu *UserUpdate) AddRequestedOrders(m ...*MaintenanceOrder) *UserUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return uu.AddRequestedOrderIDs(ids...)
}

// AddAssignedOrderIDs adds the "assigned_orders" edge to the MaintenanceOrder entity by IDs.
func (uu *UserUpdate) AddAssignedOrderIDs(ids ...int) *UserUpdate {
	uu.mutation.AddAssignedOrderIDs(ids...)
	return uu
}

// AddAssignedOrders adds the "assigned_orders" edges to the MaintenanceOrder entity.
func (uu *UserUpdate) AddAssignedOrders(m ...*MaintenanceOrder) *UserUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return uu.AddAssignedOrderIDs(ids...)
}

// AddApprovedOrderIDs adds the "approved_orders" edge to the MaintenanceOrder entity by IDs.
func (uu *UserUpdate) AddApprovedOrderIDs(ids ...int) *UserUpdate {
	uu.mutation.AddApprovedOrderIDs(ids...)
	return uu
}

// AddApprovedOrders adds the "approved_orders" edges to the MaintenanceOrder entity.
func (uu *UserUpdate) AddApprovedOrders(m ...*MaintenanceOrder) *UserUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return uu.AddApprovedOrderIDs(ids...)
}

// AddCostItemIDs adds the "cost_items" edge to the CostItem entity by IDs.
func (uu *UserUpdate) AddCostItemIDs(ids ...int) *UserUpdate {
	uu.mutation.AddCostItemIDs(ids...)
	return uu
}

// AddCostItems adds the "cost_items" edges to the CostItem entity.
func (uu *UserUpdate) AddCostItems(c ...*CostItem) *UserUpdate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return uu.AddCostItemIDs(ids...)
}

// AddInteractionIDs adds the "interactions" edge to the Interaction entity by IDs.
func (uu *UserUpdate) AddInteractionIDs(ids ...int) *UserUpdate {
	uu.mutation.AddInteractionIDs(ids...)
	return uu
}

// AddInteractions adds the "interactions" edges to the Interaction entity.
func (uu *UserUpdate) AddInteractions(i ...*Interaction) *UserUpdate {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return uu.AddInteractionIDs(ids...)
}

// Mutation returns the UserMutation object of the builder.
func (uu *UserUpdate) Mutation() *UserMutation {
	return uu.mutation
}

// ClearManagedBranches clears all "managed_branches" edges to the Branch entity.
func (uu *UserUpdate) ClearManagedBranches() *UserUpdate {
	uu.mutation.ClearManagedBranches()
	return uu
}

// RemoveManagedBranchIDs removes the "managed_branches" edge to Branch entities by IDs.
func (uu *UserUpdate) RemoveManagedBranchIDs(ids ...int) *UserUpdate {
	uu.mutation.RemoveManagedBranchIDs(ids...)
	return uu
}

// RemoveManagedBranches removes "managed_branches" edges to Branch entities.
func (uu *UserUpdate) RemoveManagedBranches(b ...*Branch) *UserUpdate {
	ids := make([]int, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return uu.RemoveManagedBranchIDs(ids...)
}

// ClearRequestedOrders clears all "requested_orders" edges to the MaintenanceOrder entity.
func (uu *UserUpdate) ClearRequestedOrders() *UserUpdate {
	uu.mutation.ClearRequestedOrders()
	return uu
}

// RemoveRequestedOrderIDs removes the "requested_orders" edge to MaintenanceOrder entities by IDs.
func (uu *UserUpdate) RemoveRequestedOrderIDs(ids ...int) *UserUpdate {
	uu.mutation.RemoveRequestedOrderIDs(ids...)
	return uu
}

// RemoveRequestedOrders removes "requested_orders" edges to MaintenanceOrder entities.
func (uu *UserUpdate) RemoveRequestedOrders(m ...*MaintenanceOrder) *UserUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return uu.RemoveRequestedOrderIDs(ids...)
}

// ClearAssignedOrders clears all "assigned_orders" edges to the MaintenanceOrder entity.
func (uu *UserUpdate) ClearAssignedOrders() *UserUpdate {
	uu.mutation.ClearAssignedOrders()
	return uu
}

// RemoveAssignedOrderIDs removes the "assigned_orders" edge to MaintenanceOrder entities by IDs.
func (uu *UserUpdate) RemoveAssignedOrderIDs(ids ...int) *UserUpdate {
	uu.mutation.RemoveAssignedOrderIDs(ids...)
	return uu
}

// RemoveAssignedOrders removes "assigned_orders" edges to MaintenanceOrder entities.
func (uu *UserUpdate) RemoveAssignedOrders(m ...*MaintenanceOrder) *UserUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return uu.RemoveAssignedOrderIDs(ids...)
}

// ClearApprovedOrders clears all "approved_orders" edges to the MaintenanceOrder entity.
func (uu *UserUpdate) ClearApprovedOrders() *UserUpdate {
	uu.mutation.ClearApprovedOrders()
	return uu
}

// RemoveApprovedOrderIDs removes the "approved_orders" edge to MaintenanceOrder entities by IDs.
func (uu *UserUpdate) RemoveApprovedOrderIDs(ids ...int) *UserUpdate {
	uu.mutation.RemoveApprovedOrderIDs(ids...)
	return uu
}

// RemoveApprovedOrders removes "approved_orders" edges to MaintenanceOrder entities.
func (uu *UserUpdate) RemoveApprovedOrders(m ...*MaintenanceOrder) *UserUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return uu.RemoveApprovedOrderIDs(ids...)
}

// ClearCostItems clears all "cost_items" edges to the CostItem entity.
func (uu *UserUpdate) ClearCostItems() *UserUpdate {
	uu.mutation.ClearCostItems()
	return uu
}

// RemoveCostItemIDs removes the "cost_items" edge to CostItem entities by IDs.
func (uu *UserUpdate) RemoveCostItemIDs(ids ...int) *UserUpdate {
	uu.mutation.RemoveCostItemIDs(ids...)
	return uu
}

// RemoveCostItems removes "cost_items" edges to CostItem entities.
func (uu *UserUpdate) RemoveCostItems(c ...*CostItem) *UserUpdate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return uu.RemoveCostItemIDs(ids...)
}

// ClearInteractions clears all "interactions" edges to the Interaction entity.
func (uu *UserUpdate) ClearInteractions() *UserUpdate {
	uu.mutation.ClearInteractions()
	return uu
}

// RemoveInteractionIDs removes the "interactions" edge to Interaction entities by IDs.
func (uu *UserUpdate) RemoveInteractionIDs(ids ...int) *UserUpdate {
	uu.mutation.RemoveInteractionIDs(ids...)
	return uu
}

// RemoveInteractions removes "interactions" edges to Interaction entities.
func (uu *UserUpdate) RemoveInteractions(i ...*Interaction) *UserUpdate {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return uu.RemoveInteractionIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (uu *UserUpdate) Save(ctx context.Context) (int, error) {
	uu.defaults()
	return withHooks(ctx, uu.sqlSave, uu.mutation, uu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uu *UserUpdate) SaveX(ctx context.Context) int {
	affected, err := uu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (uu *UserUpdate) Exec(ctx context.Context) error {
	_, err := uu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uu *UserUpdate) ExecX(ctx context.Context) {
	if err := uu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uu *UserUpdate) defaults() {
	if _, ok := uu.mutation.UpdatedAt(); !ok {
		v := user.UpdateDefaultUpdatedAt()
		uu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uu *UserUpdate) check() error {
	if v, ok := uu.mutation.Name(); ok {
		if err := user.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "User.name": %w`, err)}
		}
	}
	if v, ok := uu.mutation.Email(); ok {
		if err := user.EmailValidator(v); err != nil {
			return &ValidationError{Name: "email", err: fmt.Errorf(`ent: validator failed for field "User.email": %w`, err)}
		}
	}
	if v, ok := uu.mutation.Password(); ok {
		if err := user.PasswordValidator(v); err != nil {
			return &ValidationError{Name: "password", err: fmt.Errorf(`ent: validator failed for field "User.password": %w`, err)}
		}
	}
	if v, ok := uu.mutation.Role(); ok {
		if err := user.RoleValidator(v); err != nil {
			return &ValidationError{Name: "role", err: fmt.Errorf(`ent: validator failed for field "User.role": %w`, err)}
		}
	}
	return nil
}

func (uu *UserUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := uu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(user.Table, user.Columns, sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt))
	if ps := uu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uu.mutation.UpdatedAt(); ok {
		_spec.SetField(user.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := uu.mutation.Active(); ok {
		_spec.SetField(user.FieldActive, field.TypeBool, value)
	}
	if value, ok := uu.mutation.Name(); ok {
		_spec.SetField(user.FieldName, field.TypeString, value)
	}
	if value, ok := uu.mutation.Email(); ok {
		_spec.SetField(user.FieldEmail, field.TypeString, value)
	}
	if value, ok := uu.mutation.Password(); ok {
		_spec.SetField(user.FieldPassword, field.TypeString, value)
	}
	if value, ok := uu.mutation.Role(); ok {
		_spec.SetField(user.FieldRole, field.TypeString, value)
	}
	if uu.mutation.ManagedBranchesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.ManagedBranchesTable,
			Columns: []string{user.ManagedBranchesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(branch.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedManagedBranchesIDs(); len(nodes) > 0 && !uu.mutation.ManagedBranchesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.ManagedBranchesTable,
			Columns: []string{user.ManagedBranchesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(branch.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.ManagedBranchesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.ManagedBranchesTable,
			Columns: []string{user.ManagedBranchesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(branch.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.RequestedOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.RequestedOrdersTable,
			Columns: []string{user.RequestedOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedRequestedOrdersIDs(); len(nodes) > 0 && !uu.mutation.RequestedOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.RequestedOrdersTable,
			Columns: []string{user.RequestedOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RequestedOrdersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.RequestedOrdersTable,
			Columns: []string{user.RequestedOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.AssignedOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.AssignedOrdersTable,
			Columns: []string{user.AssignedOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedAssignedOrdersIDs(); len(nodes) > 0 && !uu.mutation.AssignedOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.AssignedOrdersTable,
			Columns: []string{user.AssignedOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.AssignedOrdersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.AssignedOrdersTable,
			Columns: []string{user.AssignedOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.ApprovedOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.ApprovedOrdersTable,
			Columns: []string{user.ApprovedOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedApprovedOrdersIDs(); len(nodes) > 0 && !uu.mutation.ApprovedOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.ApprovedOrdersTable,
			Columns: []string{user.ApprovedOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.ApprovedOrdersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.ApprovedOrdersTable,
			Columns: []string{user.ApprovedOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.CostItemsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.CostItemsTable,
			Columns: []string{user.CostItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(costitem.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedCostItemsIDs(); len(nodes) > 0 && !uu.mutation.CostItemsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.CostItemsTable,
			Columns: []string{user.CostItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(costitem.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.CostItemsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.CostItemsTable,
			Columns: []string{user.CostItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(costitem.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.InteractionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.InteractionsTable,
			Columns: []string{user.InteractionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(interaction.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedInteractionsIDs(); len(nodes) > 0 && !uu.mutation.InteractionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.InteractionsTable,
			Columns: []string{user.InteractionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(interaction.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.InteractionsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.InteractionsTable,
			Columns: []string{user.InteractionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(interaction.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, uu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{user.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	uu.mutation.done = true
	return n, nil
}

// UserUpdateOne is the builder for updating a single User entity.
type UserUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *UserMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (uuo *UserUpdateOne) SetUpdatedAt(t time.Time) *UserUpdateOne {
	uuo.mutation.SetUpdatedAt(t)
	return uuo
}

// SetActive sets the "active" field.
func (uuo *UserUpdateOne) SetActive(b bool) *UserUpdateOne {
	uuo.mutation.SetActive(b)
	return uuo
}

// SetNillableActive sets the "active" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableActive(b *bool) *UserUpdateOne {
	if b != nil {
		uuo.SetActive(*b)
	}
	return uuo
}

// SetName sets the "name" field.
func (uuo *UserUpdateOne) SetName(s string) *UserUpdateOne {
	uuo.mutation.SetName(s)
	return uuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableName(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetName(*s)
	}
	return uuo
}

// SetEmail sets the "email" field.
func (uuo *UserUpdateOne) SetEmail(s string) *UserUpdateOne {
	uuo.mutation.SetEmail(s)
	return uuo
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableEmail(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetEmail(*s)
	}
	return uuo
}

// SetPassword sets the "password" field.
func (uuo *UserUpdateOne) SetPassword(s string) *UserUpdateOne {
	uuo.mutation.SetPassword(s)
	return uuo
}

// SetNillablePassword sets the "password" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillablePassword(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetPassword(*s)
	}
	return uuo
}

// SetRole sets the "role" field.
func (uuo *UserUpdateOne) SetRole(s string) *UserUpdateOne {
	uuo.mutation.SetRole(s)
	return uuo
}

// SetNillableRole sets the "role" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableRole(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetRole(*s)
	}
	return uuo
}

// AddManagedBranchIDs adds the "managed_branches" edge to the Branch entity by IDs.
func (uuo *UserUpdateOne) AddManagedBranchIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.AddManagedBranchIDs(ids...)
	return uuo
}

// AddManagedBranches adds the "managed_branches" edges to the Branch entity.
func (uuo *UserUpdateOne) AddManagedBranches(b ...*Branch) *UserUpdateOne {
	ids := make([]int, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return uuo.AddManagedBranchIDs(ids...)
}

// AddRequestedOrderIDs adds the "requested_orders" edge to the MaintenanceOrder entity by IDs.
func (uuo *UserUpdateOne) AddRequestedOrderIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.AddRequestedOrderIDs(ids...)
	return uuo
}

// AddRequestedOrders adds the "requested_orders" edges to the MaintenanceOrder entity.
func (uuo *UserUpdateOne) AddRequestedOrders(m ...*MaintenanceOrder) *UserUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return uuo.AddRequestedOrderIDs(ids...)
}

// AddAssignedOrderIDs adds the "assigned_orders" edge to the MaintenanceOrder entity by IDs.
func (uuo *UserUpdateOne) AddAssignedOrderIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.AddAssignedOrderIDs(ids...)
	return uuo
}

// AddAssignedOrders adds the "assigned_orders" edges to the MaintenanceOrder entity.
func (uuo *UserUpdateOne) AddAssignedOrders(m ...*MaintenanceOrder) *UserUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return uuo.AddAssignedOrderIDs(ids...)
}

// AddApprovedOrderIDs adds the "approved_orders" edge to the MaintenanceOrder entity by IDs.
func (uuo *UserUpdateOne) AddApprovedOrderIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.AddApprovedOrderIDs(ids...)
	return uuo
}

// AddApprovedOrders adds the "approved_orders" edges to the MaintenanceOrder entity.
func (uuo *UserUpdateOne) AddApprovedOrders(m ...*MaintenanceOrder) *UserUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return uuo.AddApprovedOrderIDs(ids...)
}

// AddCostItemIDs adds the "cost_items" edge to the CostItem entity by IDs.
func (uuo *UserUpdateOne) AddCostItemIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.AddCostItemIDs(ids...)
	return uuo
}

// AddCostItems adds the "cost_items" edges to the CostItem entity.
func (uuo *UserUpdateOne) AddCostItems(c ...*CostItem) *UserUpdateOne {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return uuo.AddCostItemIDs(ids...)
}

// AddInteractionIDs adds the "interactions" edge to the Interaction entity by IDs.
func (uuo *UserUpdateOne) AddInteractionIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.AddInteractionIDs(ids...)
	return uuo
}

// AddInteractions adds the "interactions" edges to the Interaction entity.
func (uuo *UserUpdateOne) AddInteractions(i ...*Interaction) *UserUpdateOne {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return uuo.AddInteractionIDs(ids...)
}

// Mutation returns the UserMutation object of the builder.
func (uuo *UserUpdateOne) Mutation() *UserMutation {
	return uuo.mutation
}

// ClearManagedBranches clears all "managed_branches" edges to the Branch entity.
func (uuo *UserUpdateOne) ClearManagedBranches() *UserUpdateOne {
	uuo.mutation.ClearManagedBranches()
	return uuo
}

// RemoveManagedBranchIDs removes the "managed_branches" edge to Branch entities by IDs.
func (uuo *UserUpdateOne) RemoveManagedBranchIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.RemoveManagedBranchIDs(ids...)
	return uuo
}

// RemoveManagedBranches removes "managed_branches" edges to Branch entities.
func (uuo *UserUpdateOne) RemoveManagedBranches(b ...*Branch) *UserUpdateOne {
	ids := make([]int, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return uuo.RemoveManagedBranchIDs(ids...)
}

// ClearRequestedOrders clears all "requested_orders" edges to the MaintenanceOrder entity.
func (uuo *UserUpdateOne) ClearRequestedOrders() *UserUpdateOne {
	uuo.mutation.ClearRequestedOrders()
	return uuo
}

// RemoveRequestedOrderIDs removes the "requested_orders" edge to MaintenanceOrder entities by IDs.
func (uuo *UserUpdateOne) RemoveRequestedOrderIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.RemoveRequestedOrderIDs(ids...)
	return uuo
}

// RemoveRequestedOrders removes "requested_orders" edges to MaintenanceOrder entities.
func (uuo *UserUpdateOne) RemoveRequestedOrders(m ...*MaintenanceOrder) *UserUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return uuo.RemoveRequestedOrderIDs(ids...)
}

// ClearAssignedOrders clears all "assigned_orders" edges to the MaintenanceOrder entity.
func (uuo *UserUpdateOne) ClearAssignedOrders() *UserUpdateOne {
	uuo.mutation.ClearAssignedOrders()
	return uuo
}

// RemoveAssignedOrderIDs removes the "assigned_orders" edge to MaintenanceOrder entities by IDs.
func (uuo *UserUpdateOne) RemoveAssignedOrderIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.RemoveAssignedOrderIDs(ids...)
	return uuo
}

// RemoveAssignedOrders removes "assigned_orders" edges to MaintenanceOrder entities.
func (uuo *UserUpdateOne) RemoveAssignedOrders(m ...*MaintenanceOrder) *UserUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return uuo.RemoveAssignedOrderIDs(ids...)
}

// ClearApprovedOrders clears all "approved_orders" edges to the MaintenanceOrder entity.
func (uuo *UserUpdateOne) ClearApprovedOrders() *UserUpdateOne {
	uuo.mutation.ClearApprovedOrders()
	return uuo
}

// RemoveApprovedOrderIDs removes the "approved_orders" edge to MaintenanceOrder entities by IDs.
func (uuo *UserUpdateOne) RemoveApprovedOrderIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.RemoveApprovedOrderIDs(ids...)
	return uuo
}

// RemoveApprovedOrders removes "approved_orders" edges to MaintenanceOrder entities.
func (uuo *UserUpdateOne) RemoveApprovedOrders(m ...*MaintenanceOrder) *UserUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return uuo.RemoveApprovedOrderIDs(ids...)
}

// ClearCostItems clears all "cost_items" edges to the CostItem entity.
func (uuo *UserUpdateOne) ClearCostItems() *UserUpdateOne {
	uuo.mutation.ClearCostItems()
	return uuo
}

// RemoveCostItemIDs removes the "cost_items" edge to CostItem entities by IDs.
func (uuo *UserUpdateOne) RemoveCostItemIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.RemoveCostItemIDs(ids...)
	return uuo
}

// RemoveCostItems removes "cost_items" edges to CostItem entities.
func (uuo *UserUpdateOne) RemoveCostItems(c ...*CostItem) *UserUpdateOne {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return uuo.RemoveCostItemIDs(ids...)
}

// ClearInteractions clears all "interactions" edges to the Interaction entity.
func (uuo *UserUpdateOne) ClearInteractions() *UserUpdateOne {
	uuo.mutation.ClearInteractions()
	return uuo
}

// RemoveInteractionIDs removes the "interactions" edge to Interaction entities by IDs.
func (uuo *UserUpdateOne) RemoveInteractionIDs(ids ...int) *UserUpdateOne {
	uuo.mutation.RemoveInteractionIDs(ids...)
	return uuo
}

// RemoveInteractions removes "interactions" edges to Interaction entities.
func (uuo *UserUpdateOne) RemoveInteractions(i ...*Interaction) *UserUpdateOne {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return uuo.RemoveInteractionIDs(ids...)
}

// Where appends a list predicates to the UserUpdate builder.
func (uuo *UserUpdateOne) Where(ps ...predicate.User) *UserUpdateOne {
	uuo.mutation.Where(ps...)
	return uuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (uuo *UserUpdateOne) Select(field string, fields ...string) *UserUpdateOne {
	uuo.fields = append([]string{field}, fields...)
	return uuo
}

// Save executes the query and returns the updated User entity.
func (uuo *UserUpdateOne) Save(ctx context.Context) (*User, error) {
	uuo.defaults()
	return withHooks(ctx, uuo.sqlSave, uuo.mutation, uuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uuo *UserUpdateOne) SaveX(ctx context.Context) *User {
	node, err := uuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (uuo *UserUpdateOne) Exec(ctx context.Context) error {
	_, err := uuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uuo *UserUpdateOne) ExecX(ctx context.Context) {
	if err := uuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uuo *UserUpdateOne) defaults() {
	if _, ok := uuo.mutation.UpdatedAt(); !ok {
		v := user.UpdateDefaultUpdatedAt()
		uuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uuo *UserUpdateOne) check() error {
	if v, ok := uuo.mutation.Name(); ok {
		if err := user.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "User.name": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.Email(); ok {
		if err := user.EmailValidator(v); err != nil {
			return &ValidationError{Name: "email", err: fmt.Errorf(`ent: validator failed for field "User.email": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.Password(); ok {
		if err := user.PasswordValidator(v); err != nil {
			return &ValidationError{Name: "password", err: fmt.Errorf(`ent: validator failed for field "User.password": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.Role(); ok {
		if err := user.RoleValidator(v); err != nil {
			return &ValidationError{Name: "role", err: fmt.Errorf(`ent: validator failed for field "User.role": %w`, err)}
		}
	}
	return nil
}

func (uuo *UserUpdateOne) sqlSave(ctx context.Context) (_node *User, err error) {
	if err := uuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(user.Table, user.Columns, sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt))
	id, ok := uuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "User.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := uuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, user.FieldID)
		for _, f := range fields {
			if !user.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != user.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := uuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uuo.mutation.UpdatedAt(); ok {
		_spec.SetField(user.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := uuo.mutation.Active(); ok {
		_spec.SetField(user.FieldActive, field.TypeBool, value)
	}
	if value, ok := uuo.mutation.Name(); ok {
		_spec.SetField(user.FieldName, field.TypeString, value)
	}
	if value, ok := uuo.mutation.Email(); ok {
		_spec.SetField(user.FieldEmail, field.TypeString, value)
	}
	if value, ok := uuo.mutation.Password(); ok {
		_spec.SetField(user.FieldPassword, field.TypeString, value)
	}
	if value, ok := uuo.mutation.Role(); ok {
		_spec.SetField(user.FieldRole, field.TypeString, value)
	}
	if uuo.mutation.ManagedBranchesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.ManagedBranchesTable,
			Columns: []string{user.ManagedBranchesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(branch.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedManagedBranchesIDs(); len(nodes) > 0 && !uuo.mutation.ManagedBranchesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.ManagedBranchesTable,
			Columns: []string{user.ManagedBranchesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(branch.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.ManagedBranchesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.ManagedBranchesTable,
			Columns: []string{user.ManagedBranchesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(branch.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.RequestedOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.RequestedOrdersTable,
			Columns: []string{user.RequestedOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedRequestedOrdersIDs(); len(nodes) > 0 && !uuo.mutation.RequestedOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.RequestedOrdersTable,
			Columns: []string{user.RequestedOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RequestedOrdersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.RequestedOrdersTable,
			Columns: []string{user.RequestedOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.AssignedOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.AssignedOrdersTable,
			Columns: []string{user.AssignedOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedAssignedOrdersIDs(); len(nodes) > 0 && !uuo.mutation.AssignedOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.AssignedOrdersTable,
			Columns: []string{user.AssignedOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.AssignedOrdersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.AssignedOrdersTable,
			Columns: []string{user.AssignedOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.ApprovedOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.ApprovedOrdersTable,
			Columns: []string{user.ApprovedOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedApprovedOrdersIDs(); len(nodes) > 0 && !uuo.mutation.ApprovedOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.ApprovedOrdersTable,
			Columns: []string{user.ApprovedOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.ApprovedOrdersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.ApprovedOrdersTable,
			Columns: []string{user.ApprovedOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.CostItemsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.CostItemsTable,
			Columns: []string{user.CostItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(costitem.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedCostItemsIDs(); len(nodes) > 0 && !uuo.mutation.CostItemsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.CostItemsTable,
			Columns: []string{user.CostItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(costitem.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.CostItemsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.CostItemsTable,
			Columns: []string{user.CostItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(costitem.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.InteractionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.InteractionsTable,
			Columns: []string{user.InteractionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(interaction.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedInteractionsIDs(); len(nodes) > 0 && !uuo.mutation.InteractionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.InteractionsTable,
			Columns: []string{user.InteractionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(interaction.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.InteractionsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.InteractionsTable,
			Columns: []string{user.InteractionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(interaction.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &User{config: uuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, uuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{user.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	uuo.mutation.done = true
	return _node, nil
}
