// Code generated by ent, DO NOT EDIT.

package costitem

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the costitem type in the database.
	Label = "cost_item"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldActive holds the string denoting the active field in the database.
	FieldActive = "active"
	// FieldMaintenanceOrderID holds the string denoting the maintenance_order_id field in the database.
	FieldMaintenanceOrderID = "maintenance_order_id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldQuantity holds the string denoting the quantity field in the database.
	FieldQuantity = "quantity"
	// FieldUnitPrice holds the string denoting the unit_price field in the database.
	FieldUnitPrice = "unit_price"
	// FieldTotalPrice holds the string denoting the total_price field in the database.
	FieldTotalPrice = "total_price"
	// EdgeMaintenanceOrder holds the string denoting the maintenance_order edge name in mutations.
	EdgeMaintenanceOrder = "maintenance_order"
	// EdgeUser holds the string denoting the user edge name in mutations.
	EdgeUser = "user"
	// Table holds the table name of the costitem in the database.
	Table = "cost_items"
	// MaintenanceOrderTable is the table that holds the maintenance_order relation/edge.
	MaintenanceOrderTable = "cost_items"
	// MaintenanceOrderInverseTable is the table name for the MaintenanceOrder entity.
	// It exists in this package in order to avoid circular dependency with the "maintenanceorder" package.
	MaintenanceOrderInverseTable = "maintenance_orders"
	// MaintenanceOrderColumn is the table column denoting the maintenance_order relation/edge.
	MaintenanceOrderColumn = "maintenance_order_id"
	// UserTable is the table that holds the user relation/edge.
	UserTable = "cost_items"
	// UserInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	UserInverseTable = "users"
	// UserColumn is the table column denoting the user relation/edge.
	UserColumn = "user_id"
)

// Columns holds all SQL columns for costitem fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldActive,
	FieldMaintenanceOrderID,
	FieldUserID,
	FieldDescription,
	FieldQuantity,
	FieldUnitPrice,
	FieldTotalPrice,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultActive holds the default value on creation for the "active" field.
	DefaultActive bool
	// DescriptionValidator is a validator for the "description" field. It is called by the builders before save.
	DescriptionValidator func(string) error
	// DefaultQuantity holds the default value on creation for the "quantity" field.
	DefaultQuantity float64
	// DefaultUnitPrice holds the default value on creation for the "unit_price" field.
	DefaultUnitPrice float64
	// DefaultTotalPrice holds the default value on creation for the "total_price" field.
	DefaultTotalPrice float64
)

// OrderOption defines the ordering options for the CostItem queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByActive orders the results by the active field.
func ByActive(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldActive, opts...).ToFunc()
}

// ByMaintenanceOrderID orders the results by the maintenance_order_id field.
func ByMaintenanceOrderID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaintenanceOrderID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByQuantity orders the results by the quantity field.
func ByQuantity(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldQuantity, opts...).ToFunc()
}

// ByUnitPrice orders the results by the unit_price field.
func ByUnitPrice(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUnitPrice, opts...).ToFunc()
}

// ByTotalPrice orders the results by the total_price field.
func ByTotalPrice(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTotalPrice, opts...).ToFunc()
}

// ByMaintenanceOrderField orders the results by maintenance_order field.
func ByMaintenanceOrderField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newMaintenanceOrderStep(), sql.OrderByField(field, opts...))
	}
}

// ByUserField orders the results by user field.
func ByUserField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newUserStep(), sql.OrderByField(field, opts...))
	}
}
func newMaintenanceOrderStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(MaintenanceOrderInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, MaintenanceOrderTable, MaintenanceOrderColumn),
	)
}
func newUserStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(UserInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, UserTable, UserColumn),
	)
}
