// Code generated by ent, DO NOT EDIT.

package costitem

import (
	"time"
	"tradicao/internal/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.CostItem {
	return predicate.CostItem(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.CostItem {
	return predicate.CostItem(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.CostItem {
	return predicate.CostItem(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.CostItem {
	return predicate.CostItem(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.CostItem {
	return predicate.CostItem(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.CostItem {
	return predicate.CostItem(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.CostItem {
	return predicate.CostItem(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldUpdatedAt, v))
}

// Active applies equality check predicate on the "active" field. It's identical to ActiveEQ.
func Active(v bool) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldActive, v))
}

// MaintenanceOrderID applies equality check predicate on the "maintenance_order_id" field. It's identical to MaintenanceOrderIDEQ.
func MaintenanceOrderID(v int) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldMaintenanceOrderID, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v int) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldUserID, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldDescription, v))
}

// Quantity applies equality check predicate on the "quantity" field. It's identical to QuantityEQ.
func Quantity(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldQuantity, v))
}

// UnitPrice applies equality check predicate on the "unit_price" field. It's identical to UnitPriceEQ.
func UnitPrice(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldUnitPrice, v))
}

// TotalPrice applies equality check predicate on the "total_price" field. It's identical to TotalPriceEQ.
func TotalPrice(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldTotalPrice, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.CostItem {
	return predicate.CostItem(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.CostItem {
	return predicate.CostItem(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.CostItem {
	return predicate.CostItem(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.CostItem {
	return predicate.CostItem(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.CostItem {
	return predicate.CostItem(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.CostItem {
	return predicate.CostItem(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.CostItem {
	return predicate.CostItem(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.CostItem {
	return predicate.CostItem(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.CostItem {
	return predicate.CostItem(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.CostItem {
	return predicate.CostItem(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.CostItem {
	return predicate.CostItem(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.CostItem {
	return predicate.CostItem(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.CostItem {
	return predicate.CostItem(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.CostItem {
	return predicate.CostItem(sql.FieldLTE(FieldUpdatedAt, v))
}

// ActiveEQ applies the EQ predicate on the "active" field.
func ActiveEQ(v bool) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldActive, v))
}

// ActiveNEQ applies the NEQ predicate on the "active" field.
func ActiveNEQ(v bool) predicate.CostItem {
	return predicate.CostItem(sql.FieldNEQ(FieldActive, v))
}

// MaintenanceOrderIDEQ applies the EQ predicate on the "maintenance_order_id" field.
func MaintenanceOrderIDEQ(v int) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldMaintenanceOrderID, v))
}

// MaintenanceOrderIDNEQ applies the NEQ predicate on the "maintenance_order_id" field.
func MaintenanceOrderIDNEQ(v int) predicate.CostItem {
	return predicate.CostItem(sql.FieldNEQ(FieldMaintenanceOrderID, v))
}

// MaintenanceOrderIDIn applies the In predicate on the "maintenance_order_id" field.
func MaintenanceOrderIDIn(vs ...int) predicate.CostItem {
	return predicate.CostItem(sql.FieldIn(FieldMaintenanceOrderID, vs...))
}

// MaintenanceOrderIDNotIn applies the NotIn predicate on the "maintenance_order_id" field.
func MaintenanceOrderIDNotIn(vs ...int) predicate.CostItem {
	return predicate.CostItem(sql.FieldNotIn(FieldMaintenanceOrderID, vs...))
}

// MaintenanceOrderIDIsNil applies the IsNil predicate on the "maintenance_order_id" field.
func MaintenanceOrderIDIsNil() predicate.CostItem {
	return predicate.CostItem(sql.FieldIsNull(FieldMaintenanceOrderID))
}

// MaintenanceOrderIDNotNil applies the NotNil predicate on the "maintenance_order_id" field.
func MaintenanceOrderIDNotNil() predicate.CostItem {
	return predicate.CostItem(sql.FieldNotNull(FieldMaintenanceOrderID))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v int) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v int) predicate.CostItem {
	return predicate.CostItem(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...int) predicate.CostItem {
	return predicate.CostItem(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...int) predicate.CostItem {
	return predicate.CostItem(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDIsNil applies the IsNil predicate on the "user_id" field.
func UserIDIsNil() predicate.CostItem {
	return predicate.CostItem(sql.FieldIsNull(FieldUserID))
}

// UserIDNotNil applies the NotNil predicate on the "user_id" field.
func UserIDNotNil() predicate.CostItem {
	return predicate.CostItem(sql.FieldNotNull(FieldUserID))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.CostItem {
	return predicate.CostItem(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.CostItem {
	return predicate.CostItem(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.CostItem {
	return predicate.CostItem(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.CostItem {
	return predicate.CostItem(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.CostItem {
	return predicate.CostItem(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.CostItem {
	return predicate.CostItem(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.CostItem {
	return predicate.CostItem(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.CostItem {
	return predicate.CostItem(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.CostItem {
	return predicate.CostItem(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.CostItem {
	return predicate.CostItem(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.CostItem {
	return predicate.CostItem(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.CostItem {
	return predicate.CostItem(sql.FieldContainsFold(FieldDescription, v))
}

// QuantityEQ applies the EQ predicate on the "quantity" field.
func QuantityEQ(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldQuantity, v))
}

// QuantityNEQ applies the NEQ predicate on the "quantity" field.
func QuantityNEQ(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldNEQ(FieldQuantity, v))
}

// QuantityIn applies the In predicate on the "quantity" field.
func QuantityIn(vs ...float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldIn(FieldQuantity, vs...))
}

// QuantityNotIn applies the NotIn predicate on the "quantity" field.
func QuantityNotIn(vs ...float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldNotIn(FieldQuantity, vs...))
}

// QuantityGT applies the GT predicate on the "quantity" field.
func QuantityGT(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldGT(FieldQuantity, v))
}

// QuantityGTE applies the GTE predicate on the "quantity" field.
func QuantityGTE(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldGTE(FieldQuantity, v))
}

// QuantityLT applies the LT predicate on the "quantity" field.
func QuantityLT(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldLT(FieldQuantity, v))
}

// QuantityLTE applies the LTE predicate on the "quantity" field.
func QuantityLTE(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldLTE(FieldQuantity, v))
}

// UnitPriceEQ applies the EQ predicate on the "unit_price" field.
func UnitPriceEQ(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldUnitPrice, v))
}

// UnitPriceNEQ applies the NEQ predicate on the "unit_price" field.
func UnitPriceNEQ(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldNEQ(FieldUnitPrice, v))
}

// UnitPriceIn applies the In predicate on the "unit_price" field.
func UnitPriceIn(vs ...float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldIn(FieldUnitPrice, vs...))
}

// UnitPriceNotIn applies the NotIn predicate on the "unit_price" field.
func UnitPriceNotIn(vs ...float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldNotIn(FieldUnitPrice, vs...))
}

// UnitPriceGT applies the GT predicate on the "unit_price" field.
func UnitPriceGT(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldGT(FieldUnitPrice, v))
}

// UnitPriceGTE applies the GTE predicate on the "unit_price" field.
func UnitPriceGTE(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldGTE(FieldUnitPrice, v))
}

// UnitPriceLT applies the LT predicate on the "unit_price" field.
func UnitPriceLT(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldLT(FieldUnitPrice, v))
}

// UnitPriceLTE applies the LTE predicate on the "unit_price" field.
func UnitPriceLTE(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldLTE(FieldUnitPrice, v))
}

// TotalPriceEQ applies the EQ predicate on the "total_price" field.
func TotalPriceEQ(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldEQ(FieldTotalPrice, v))
}

// TotalPriceNEQ applies the NEQ predicate on the "total_price" field.
func TotalPriceNEQ(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldNEQ(FieldTotalPrice, v))
}

// TotalPriceIn applies the In predicate on the "total_price" field.
func TotalPriceIn(vs ...float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldIn(FieldTotalPrice, vs...))
}

// TotalPriceNotIn applies the NotIn predicate on the "total_price" field.
func TotalPriceNotIn(vs ...float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldNotIn(FieldTotalPrice, vs...))
}

// TotalPriceGT applies the GT predicate on the "total_price" field.
func TotalPriceGT(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldGT(FieldTotalPrice, v))
}

// TotalPriceGTE applies the GTE predicate on the "total_price" field.
func TotalPriceGTE(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldGTE(FieldTotalPrice, v))
}

// TotalPriceLT applies the LT predicate on the "total_price" field.
func TotalPriceLT(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldLT(FieldTotalPrice, v))
}

// TotalPriceLTE applies the LTE predicate on the "total_price" field.
func TotalPriceLTE(v float64) predicate.CostItem {
	return predicate.CostItem(sql.FieldLTE(FieldTotalPrice, v))
}

// HasMaintenanceOrder applies the HasEdge predicate on the "maintenance_order" edge.
func HasMaintenanceOrder() predicate.CostItem {
	return predicate.CostItem(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, MaintenanceOrderTable, MaintenanceOrderColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasMaintenanceOrderWith applies the HasEdge predicate on the "maintenance_order" edge with a given conditions (other predicates).
func HasMaintenanceOrderWith(preds ...predicate.MaintenanceOrder) predicate.CostItem {
	return predicate.CostItem(func(s *sql.Selector) {
		step := newMaintenanceOrderStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasUser applies the HasEdge predicate on the "user" edge.
func HasUser() predicate.CostItem {
	return predicate.CostItem(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, UserTable, UserColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasUserWith applies the HasEdge predicate on the "user" edge with a given conditions (other predicates).
func HasUserWith(preds ...predicate.User) predicate.CostItem {
	return predicate.CostItem(func(s *sql.Selector) {
		step := newUserStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.CostItem) predicate.CostItem {
	return predicate.CostItem(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.CostItem) predicate.CostItem {
	return predicate.CostItem(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.CostItem) predicate.CostItem {
	return predicate.CostItem(sql.NotPredicates(p))
}
