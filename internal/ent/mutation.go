// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"
	"tradicao/internal/ent/branch"
	"tradicao/internal/ent/costitem"
	"tradicao/internal/ent/equipment"
	"tradicao/internal/ent/interaction"
	"tradicao/internal/ent/maintenanceorder"
	"tradicao/internal/ent/predicate"
	"tradicao/internal/ent/user"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeBranch           = "Branch"
	TypeCostItem         = "CostItem"
	TypeEquipment        = "Equipment"
	TypeInteraction      = "Interaction"
	TypeMaintenanceOrder = "MaintenanceOrder"
	TypeUser             = "User"
)

// BranchMutation represents an operation that mutates the Branch nodes in the graph.
type BranchMutation struct {
	config
	op                        Op
	typ                       string
	id                        *int
	created_at                *time.Time
	updated_at                *time.Time
	active                    *bool
	name                      *string
	code                      *string
	address                   *string
	city                      *string
	state                     *string
	zip_code                  *string
	phone                     *string
	email                     *string
	status                    *string
	inauguration_date         *time.Time
	is_active                 *bool
	clearedFields             map[string]struct{}
	equipment                 map[int]struct{}
	removedequipment          map[int]struct{}
	clearedequipment          bool
	maintenance_orders        map[int]struct{}
	removedmaintenance_orders map[int]struct{}
	clearedmaintenance_orders bool
	done                      bool
	oldValue                  func(context.Context) (*Branch, error)
	predicates                []predicate.Branch
}

var _ ent.Mutation = (*BranchMutation)(nil)

// branchOption allows management of the mutation configuration using functional options.
type branchOption func(*BranchMutation)

// newBranchMutation creates new mutation for the Branch entity.
func newBranchMutation(c config, op Op, opts ...branchOption) *BranchMutation {
	m := &BranchMutation{
		config:        c,
		op:            op,
		typ:           TypeBranch,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withBranchID sets the ID field of the mutation.
func withBranchID(id int) branchOption {
	return func(m *BranchMutation) {
		var (
			err   error
			once  sync.Once
			value *Branch
		)
		m.oldValue = func(ctx context.Context) (*Branch, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Branch.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withBranch sets the old Branch of the mutation.
func withBranch(node *Branch) branchOption {
	return func(m *BranchMutation) {
		m.oldValue = func(context.Context) (*Branch, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m BranchMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m BranchMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *BranchMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *BranchMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Branch.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *BranchMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *BranchMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Branch entity.
// If the Branch object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BranchMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *BranchMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *BranchMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *BranchMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Branch entity.
// If the Branch object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BranchMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *BranchMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetActive sets the "active" field.
func (m *BranchMutation) SetActive(b bool) {
	m.active = &b
}

// Active returns the value of the "active" field in the mutation.
func (m *BranchMutation) Active() (r bool, exists bool) {
	v := m.active
	if v == nil {
		return
	}
	return *v, true
}

// OldActive returns the old "active" field's value of the Branch entity.
// If the Branch object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BranchMutation) OldActive(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldActive is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldActive requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldActive: %w", err)
	}
	return oldValue.Active, nil
}

// ResetActive resets all changes to the "active" field.
func (m *BranchMutation) ResetActive() {
	m.active = nil
}

// SetName sets the "name" field.
func (m *BranchMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *BranchMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Branch entity.
// If the Branch object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BranchMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *BranchMutation) ResetName() {
	m.name = nil
}

// SetCode sets the "code" field.
func (m *BranchMutation) SetCode(s string) {
	m.code = &s
}

// Code returns the value of the "code" field in the mutation.
func (m *BranchMutation) Code() (r string, exists bool) {
	v := m.code
	if v == nil {
		return
	}
	return *v, true
}

// OldCode returns the old "code" field's value of the Branch entity.
// If the Branch object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BranchMutation) OldCode(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCode: %w", err)
	}
	return oldValue.Code, nil
}

// ResetCode resets all changes to the "code" field.
func (m *BranchMutation) ResetCode() {
	m.code = nil
}

// SetAddress sets the "address" field.
func (m *BranchMutation) SetAddress(s string) {
	m.address = &s
}

// Address returns the value of the "address" field in the mutation.
func (m *BranchMutation) Address() (r string, exists bool) {
	v := m.address
	if v == nil {
		return
	}
	return *v, true
}

// OldAddress returns the old "address" field's value of the Branch entity.
// If the Branch object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BranchMutation) OldAddress(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAddress is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAddress requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAddress: %w", err)
	}
	return oldValue.Address, nil
}

// ResetAddress resets all changes to the "address" field.
func (m *BranchMutation) ResetAddress() {
	m.address = nil
}

// SetCity sets the "city" field.
func (m *BranchMutation) SetCity(s string) {
	m.city = &s
}

// City returns the value of the "city" field in the mutation.
func (m *BranchMutation) City() (r string, exists bool) {
	v := m.city
	if v == nil {
		return
	}
	return *v, true
}

// OldCity returns the old "city" field's value of the Branch entity.
// If the Branch object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BranchMutation) OldCity(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCity is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCity requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCity: %w", err)
	}
	return oldValue.City, nil
}

// ResetCity resets all changes to the "city" field.
func (m *BranchMutation) ResetCity() {
	m.city = nil
}

// SetState sets the "state" field.
func (m *BranchMutation) SetState(s string) {
	m.state = &s
}

// State returns the value of the "state" field in the mutation.
func (m *BranchMutation) State() (r string, exists bool) {
	v := m.state
	if v == nil {
		return
	}
	return *v, true
}

// OldState returns the old "state" field's value of the Branch entity.
// If the Branch object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BranchMutation) OldState(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldState is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldState requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldState: %w", err)
	}
	return oldValue.State, nil
}

// ResetState resets all changes to the "state" field.
func (m *BranchMutation) ResetState() {
	m.state = nil
}

// SetZipCode sets the "zip_code" field.
func (m *BranchMutation) SetZipCode(s string) {
	m.zip_code = &s
}

// ZipCode returns the value of the "zip_code" field in the mutation.
func (m *BranchMutation) ZipCode() (r string, exists bool) {
	v := m.zip_code
	if v == nil {
		return
	}
	return *v, true
}

// OldZipCode returns the old "zip_code" field's value of the Branch entity.
// If the Branch object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BranchMutation) OldZipCode(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldZipCode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldZipCode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldZipCode: %w", err)
	}
	return oldValue.ZipCode, nil
}

// ResetZipCode resets all changes to the "zip_code" field.
func (m *BranchMutation) ResetZipCode() {
	m.zip_code = nil
}

// SetPhone sets the "phone" field.
func (m *BranchMutation) SetPhone(s string) {
	m.phone = &s
}

// Phone returns the value of the "phone" field in the mutation.
func (m *BranchMutation) Phone() (r string, exists bool) {
	v := m.phone
	if v == nil {
		return
	}
	return *v, true
}

// OldPhone returns the old "phone" field's value of the Branch entity.
// If the Branch object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BranchMutation) OldPhone(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPhone is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPhone requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPhone: %w", err)
	}
	return oldValue.Phone, nil
}

// ResetPhone resets all changes to the "phone" field.
func (m *BranchMutation) ResetPhone() {
	m.phone = nil
}

// SetEmail sets the "email" field.
func (m *BranchMutation) SetEmail(s string) {
	m.email = &s
}

// Email returns the value of the "email" field in the mutation.
func (m *BranchMutation) Email() (r string, exists bool) {
	v := m.email
	if v == nil {
		return
	}
	return *v, true
}

// OldEmail returns the old "email" field's value of the Branch entity.
// If the Branch object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BranchMutation) OldEmail(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEmail is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEmail requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEmail: %w", err)
	}
	return oldValue.Email, nil
}

// ClearEmail clears the value of the "email" field.
func (m *BranchMutation) ClearEmail() {
	m.email = nil
	m.clearedFields[branch.FieldEmail] = struct{}{}
}

// EmailCleared returns if the "email" field was cleared in this mutation.
func (m *BranchMutation) EmailCleared() bool {
	_, ok := m.clearedFields[branch.FieldEmail]
	return ok
}

// ResetEmail resets all changes to the "email" field.
func (m *BranchMutation) ResetEmail() {
	m.email = nil
	delete(m.clearedFields, branch.FieldEmail)
}

// SetStatus sets the "status" field.
func (m *BranchMutation) SetStatus(s string) {
	m.status = &s
}

// Status returns the value of the "status" field in the mutation.
func (m *BranchMutation) Status() (r string, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the Branch entity.
// If the Branch object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BranchMutation) OldStatus(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *BranchMutation) ResetStatus() {
	m.status = nil
}

// SetInaugurationDate sets the "inauguration_date" field.
func (m *BranchMutation) SetInaugurationDate(t time.Time) {
	m.inauguration_date = &t
}

// InaugurationDate returns the value of the "inauguration_date" field in the mutation.
func (m *BranchMutation) InaugurationDate() (r time.Time, exists bool) {
	v := m.inauguration_date
	if v == nil {
		return
	}
	return *v, true
}

// OldInaugurationDate returns the old "inauguration_date" field's value of the Branch entity.
// If the Branch object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BranchMutation) OldInaugurationDate(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldInaugurationDate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldInaugurationDate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldInaugurationDate: %w", err)
	}
	return oldValue.InaugurationDate, nil
}

// ResetInaugurationDate resets all changes to the "inauguration_date" field.
func (m *BranchMutation) ResetInaugurationDate() {
	m.inauguration_date = nil
}

// SetIsActive sets the "is_active" field.
func (m *BranchMutation) SetIsActive(b bool) {
	m.is_active = &b
}

// IsActive returns the value of the "is_active" field in the mutation.
func (m *BranchMutation) IsActive() (r bool, exists bool) {
	v := m.is_active
	if v == nil {
		return
	}
	return *v, true
}

// OldIsActive returns the old "is_active" field's value of the Branch entity.
// If the Branch object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BranchMutation) OldIsActive(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsActive is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsActive requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsActive: %w", err)
	}
	return oldValue.IsActive, nil
}

// ResetIsActive resets all changes to the "is_active" field.
func (m *BranchMutation) ResetIsActive() {
	m.is_active = nil
}

// AddEquipmentIDs adds the "equipment" edge to the Equipment entity by ids.
func (m *BranchMutation) AddEquipmentIDs(ids ...int) {
	if m.equipment == nil {
		m.equipment = make(map[int]struct{})
	}
	for i := range ids {
		m.equipment[ids[i]] = struct{}{}
	}
}

// ClearEquipment clears the "equipment" edge to the Equipment entity.
func (m *BranchMutation) ClearEquipment() {
	m.clearedequipment = true
}

// EquipmentCleared reports if the "equipment" edge to the Equipment entity was cleared.
func (m *BranchMutation) EquipmentCleared() bool {
	return m.clearedequipment
}

// RemoveEquipmentIDs removes the "equipment" edge to the Equipment entity by IDs.
func (m *BranchMutation) RemoveEquipmentIDs(ids ...int) {
	if m.removedequipment == nil {
		m.removedequipment = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.equipment, ids[i])
		m.removedequipment[ids[i]] = struct{}{}
	}
}

// RemovedEquipment returns the removed IDs of the "equipment" edge to the Equipment entity.
func (m *BranchMutation) RemovedEquipmentIDs() (ids []int) {
	for id := range m.removedequipment {
		ids = append(ids, id)
	}
	return
}

// EquipmentIDs returns the "equipment" edge IDs in the mutation.
func (m *BranchMutation) EquipmentIDs() (ids []int) {
	for id := range m.equipment {
		ids = append(ids, id)
	}
	return
}

// ResetEquipment resets all changes to the "equipment" edge.
func (m *BranchMutation) ResetEquipment() {
	m.equipment = nil
	m.clearedequipment = false
	m.removedequipment = nil
}

// AddMaintenanceOrderIDs adds the "maintenance_orders" edge to the MaintenanceOrder entity by ids.
func (m *BranchMutation) AddMaintenanceOrderIDs(ids ...int) {
	if m.maintenance_orders == nil {
		m.maintenance_orders = make(map[int]struct{})
	}
	for i := range ids {
		m.maintenance_orders[ids[i]] = struct{}{}
	}
}

// ClearMaintenanceOrders clears the "maintenance_orders" edge to the MaintenanceOrder entity.
func (m *BranchMutation) ClearMaintenanceOrders() {
	m.clearedmaintenance_orders = true
}

// MaintenanceOrdersCleared reports if the "maintenance_orders" edge to the MaintenanceOrder entity was cleared.
func (m *BranchMutation) MaintenanceOrdersCleared() bool {
	return m.clearedmaintenance_orders
}

// RemoveMaintenanceOrderIDs removes the "maintenance_orders" edge to the MaintenanceOrder entity by IDs.
func (m *BranchMutation) RemoveMaintenanceOrderIDs(ids ...int) {
	if m.removedmaintenance_orders == nil {
		m.removedmaintenance_orders = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.maintenance_orders, ids[i])
		m.removedmaintenance_orders[ids[i]] = struct{}{}
	}
}

// RemovedMaintenanceOrders returns the removed IDs of the "maintenance_orders" edge to the MaintenanceOrder entity.
func (m *BranchMutation) RemovedMaintenanceOrdersIDs() (ids []int) {
	for id := range m.removedmaintenance_orders {
		ids = append(ids, id)
	}
	return
}

// MaintenanceOrdersIDs returns the "maintenance_orders" edge IDs in the mutation.
func (m *BranchMutation) MaintenanceOrdersIDs() (ids []int) {
	for id := range m.maintenance_orders {
		ids = append(ids, id)
	}
	return
}

// ResetMaintenanceOrders resets all changes to the "maintenance_orders" edge.
func (m *BranchMutation) ResetMaintenanceOrders() {
	m.maintenance_orders = nil
	m.clearedmaintenance_orders = false
	m.removedmaintenance_orders = nil
}

// Where appends a list predicates to the BranchMutation builder.
func (m *BranchMutation) Where(ps ...predicate.Branch) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the BranchMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *BranchMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Branch, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *BranchMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *BranchMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Branch).
func (m *BranchMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *BranchMutation) Fields() []string {
	fields := make([]string, 0, 14)
	if m.created_at != nil {
		fields = append(fields, branch.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, branch.FieldUpdatedAt)
	}
	if m.active != nil {
		fields = append(fields, branch.FieldActive)
	}
	if m.name != nil {
		fields = append(fields, branch.FieldName)
	}
	if m.code != nil {
		fields = append(fields, branch.FieldCode)
	}
	if m.address != nil {
		fields = append(fields, branch.FieldAddress)
	}
	if m.city != nil {
		fields = append(fields, branch.FieldCity)
	}
	if m.state != nil {
		fields = append(fields, branch.FieldState)
	}
	if m.zip_code != nil {
		fields = append(fields, branch.FieldZipCode)
	}
	if m.phone != nil {
		fields = append(fields, branch.FieldPhone)
	}
	if m.email != nil {
		fields = append(fields, branch.FieldEmail)
	}
	if m.status != nil {
		fields = append(fields, branch.FieldStatus)
	}
	if m.inauguration_date != nil {
		fields = append(fields, branch.FieldInaugurationDate)
	}
	if m.is_active != nil {
		fields = append(fields, branch.FieldIsActive)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *BranchMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case branch.FieldCreatedAt:
		return m.CreatedAt()
	case branch.FieldUpdatedAt:
		return m.UpdatedAt()
	case branch.FieldActive:
		return m.Active()
	case branch.FieldName:
		return m.Name()
	case branch.FieldCode:
		return m.Code()
	case branch.FieldAddress:
		return m.Address()
	case branch.FieldCity:
		return m.City()
	case branch.FieldState:
		return m.State()
	case branch.FieldZipCode:
		return m.ZipCode()
	case branch.FieldPhone:
		return m.Phone()
	case branch.FieldEmail:
		return m.Email()
	case branch.FieldStatus:
		return m.Status()
	case branch.FieldInaugurationDate:
		return m.InaugurationDate()
	case branch.FieldIsActive:
		return m.IsActive()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *BranchMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case branch.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case branch.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case branch.FieldActive:
		return m.OldActive(ctx)
	case branch.FieldName:
		return m.OldName(ctx)
	case branch.FieldCode:
		return m.OldCode(ctx)
	case branch.FieldAddress:
		return m.OldAddress(ctx)
	case branch.FieldCity:
		return m.OldCity(ctx)
	case branch.FieldState:
		return m.OldState(ctx)
	case branch.FieldZipCode:
		return m.OldZipCode(ctx)
	case branch.FieldPhone:
		return m.OldPhone(ctx)
	case branch.FieldEmail:
		return m.OldEmail(ctx)
	case branch.FieldStatus:
		return m.OldStatus(ctx)
	case branch.FieldInaugurationDate:
		return m.OldInaugurationDate(ctx)
	case branch.FieldIsActive:
		return m.OldIsActive(ctx)
	}
	return nil, fmt.Errorf("unknown Branch field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *BranchMutation) SetField(name string, value ent.Value) error {
	switch name {
	case branch.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case branch.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case branch.FieldActive:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetActive(v)
		return nil
	case branch.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case branch.FieldCode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCode(v)
		return nil
	case branch.FieldAddress:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAddress(v)
		return nil
	case branch.FieldCity:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCity(v)
		return nil
	case branch.FieldState:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetState(v)
		return nil
	case branch.FieldZipCode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetZipCode(v)
		return nil
	case branch.FieldPhone:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPhone(v)
		return nil
	case branch.FieldEmail:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEmail(v)
		return nil
	case branch.FieldStatus:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case branch.FieldInaugurationDate:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetInaugurationDate(v)
		return nil
	case branch.FieldIsActive:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsActive(v)
		return nil
	}
	return fmt.Errorf("unknown Branch field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *BranchMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *BranchMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *BranchMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Branch numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *BranchMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(branch.FieldEmail) {
		fields = append(fields, branch.FieldEmail)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *BranchMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *BranchMutation) ClearField(name string) error {
	switch name {
	case branch.FieldEmail:
		m.ClearEmail()
		return nil
	}
	return fmt.Errorf("unknown Branch nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *BranchMutation) ResetField(name string) error {
	switch name {
	case branch.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case branch.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case branch.FieldActive:
		m.ResetActive()
		return nil
	case branch.FieldName:
		m.ResetName()
		return nil
	case branch.FieldCode:
		m.ResetCode()
		return nil
	case branch.FieldAddress:
		m.ResetAddress()
		return nil
	case branch.FieldCity:
		m.ResetCity()
		return nil
	case branch.FieldState:
		m.ResetState()
		return nil
	case branch.FieldZipCode:
		m.ResetZipCode()
		return nil
	case branch.FieldPhone:
		m.ResetPhone()
		return nil
	case branch.FieldEmail:
		m.ResetEmail()
		return nil
	case branch.FieldStatus:
		m.ResetStatus()
		return nil
	case branch.FieldInaugurationDate:
		m.ResetInaugurationDate()
		return nil
	case branch.FieldIsActive:
		m.ResetIsActive()
		return nil
	}
	return fmt.Errorf("unknown Branch field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *BranchMutation) AddedEdges() []string {
	edges := make([]string, 0, 2)
	if m.equipment != nil {
		edges = append(edges, branch.EdgeEquipment)
	}
	if m.maintenance_orders != nil {
		edges = append(edges, branch.EdgeMaintenanceOrders)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *BranchMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case branch.EdgeEquipment:
		ids := make([]ent.Value, 0, len(m.equipment))
		for id := range m.equipment {
			ids = append(ids, id)
		}
		return ids
	case branch.EdgeMaintenanceOrders:
		ids := make([]ent.Value, 0, len(m.maintenance_orders))
		for id := range m.maintenance_orders {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *BranchMutation) RemovedEdges() []string {
	edges := make([]string, 0, 2)
	if m.removedequipment != nil {
		edges = append(edges, branch.EdgeEquipment)
	}
	if m.removedmaintenance_orders != nil {
		edges = append(edges, branch.EdgeMaintenanceOrders)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *BranchMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case branch.EdgeEquipment:
		ids := make([]ent.Value, 0, len(m.removedequipment))
		for id := range m.removedequipment {
			ids = append(ids, id)
		}
		return ids
	case branch.EdgeMaintenanceOrders:
		ids := make([]ent.Value, 0, len(m.removedmaintenance_orders))
		for id := range m.removedmaintenance_orders {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *BranchMutation) ClearedEdges() []string {
	edges := make([]string, 0, 2)
	if m.clearedequipment {
		edges = append(edges, branch.EdgeEquipment)
	}
	if m.clearedmaintenance_orders {
		edges = append(edges, branch.EdgeMaintenanceOrders)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *BranchMutation) EdgeCleared(name string) bool {
	switch name {
	case branch.EdgeEquipment:
		return m.clearedequipment
	case branch.EdgeMaintenanceOrders:
		return m.clearedmaintenance_orders
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *BranchMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown Branch unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *BranchMutation) ResetEdge(name string) error {
	switch name {
	case branch.EdgeEquipment:
		m.ResetEquipment()
		return nil
	case branch.EdgeMaintenanceOrders:
		m.ResetMaintenanceOrders()
		return nil
	}
	return fmt.Errorf("unknown Branch edge %s", name)
}

// CostItemMutation represents an operation that mutates the CostItem nodes in the graph.
type CostItemMutation struct {
	config
	op                       Op
	typ                      string
	id                       *int
	created_at               *time.Time
	updated_at               *time.Time
	active                   *bool
	description              *string
	quantity                 *float64
	addquantity              *float64
	unit_price               *float64
	addunit_price            *float64
	total_price              *float64
	addtotal_price           *float64
	clearedFields            map[string]struct{}
	maintenance_order        *int
	clearedmaintenance_order bool
	user                     *int
	cleareduser              bool
	done                     bool
	oldValue                 func(context.Context) (*CostItem, error)
	predicates               []predicate.CostItem
}

var _ ent.Mutation = (*CostItemMutation)(nil)

// costitemOption allows management of the mutation configuration using functional options.
type costitemOption func(*CostItemMutation)

// newCostItemMutation creates new mutation for the CostItem entity.
func newCostItemMutation(c config, op Op, opts ...costitemOption) *CostItemMutation {
	m := &CostItemMutation{
		config:        c,
		op:            op,
		typ:           TypeCostItem,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withCostItemID sets the ID field of the mutation.
func withCostItemID(id int) costitemOption {
	return func(m *CostItemMutation) {
		var (
			err   error
			once  sync.Once
			value *CostItem
		)
		m.oldValue = func(ctx context.Context) (*CostItem, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().CostItem.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withCostItem sets the old CostItem of the mutation.
func withCostItem(node *CostItem) costitemOption {
	return func(m *CostItemMutation) {
		m.oldValue = func(context.Context) (*CostItem, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m CostItemMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m CostItemMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *CostItemMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *CostItemMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().CostItem.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *CostItemMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *CostItemMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the CostItem entity.
// If the CostItem object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CostItemMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *CostItemMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *CostItemMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *CostItemMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the CostItem entity.
// If the CostItem object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CostItemMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *CostItemMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetActive sets the "active" field.
func (m *CostItemMutation) SetActive(b bool) {
	m.active = &b
}

// Active returns the value of the "active" field in the mutation.
func (m *CostItemMutation) Active() (r bool, exists bool) {
	v := m.active
	if v == nil {
		return
	}
	return *v, true
}

// OldActive returns the old "active" field's value of the CostItem entity.
// If the CostItem object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CostItemMutation) OldActive(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldActive is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldActive requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldActive: %w", err)
	}
	return oldValue.Active, nil
}

// ResetActive resets all changes to the "active" field.
func (m *CostItemMutation) ResetActive() {
	m.active = nil
}

// SetMaintenanceOrderID sets the "maintenance_order_id" field.
func (m *CostItemMutation) SetMaintenanceOrderID(i int) {
	m.maintenance_order = &i
}

// MaintenanceOrderID returns the value of the "maintenance_order_id" field in the mutation.
func (m *CostItemMutation) MaintenanceOrderID() (r int, exists bool) {
	v := m.maintenance_order
	if v == nil {
		return
	}
	return *v, true
}

// OldMaintenanceOrderID returns the old "maintenance_order_id" field's value of the CostItem entity.
// If the CostItem object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CostItemMutation) OldMaintenanceOrderID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMaintenanceOrderID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMaintenanceOrderID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMaintenanceOrderID: %w", err)
	}
	return oldValue.MaintenanceOrderID, nil
}

// ClearMaintenanceOrderID clears the value of the "maintenance_order_id" field.
func (m *CostItemMutation) ClearMaintenanceOrderID() {
	m.maintenance_order = nil
	m.clearedFields[costitem.FieldMaintenanceOrderID] = struct{}{}
}

// MaintenanceOrderIDCleared returns if the "maintenance_order_id" field was cleared in this mutation.
func (m *CostItemMutation) MaintenanceOrderIDCleared() bool {
	_, ok := m.clearedFields[costitem.FieldMaintenanceOrderID]
	return ok
}

// ResetMaintenanceOrderID resets all changes to the "maintenance_order_id" field.
func (m *CostItemMutation) ResetMaintenanceOrderID() {
	m.maintenance_order = nil
	delete(m.clearedFields, costitem.FieldMaintenanceOrderID)
}

// SetUserID sets the "user_id" field.
func (m *CostItemMutation) SetUserID(i int) {
	m.user = &i
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *CostItemMutation) UserID() (r int, exists bool) {
	v := m.user
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the CostItem entity.
// If the CostItem object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CostItemMutation) OldUserID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// ClearUserID clears the value of the "user_id" field.
func (m *CostItemMutation) ClearUserID() {
	m.user = nil
	m.clearedFields[costitem.FieldUserID] = struct{}{}
}

// UserIDCleared returns if the "user_id" field was cleared in this mutation.
func (m *CostItemMutation) UserIDCleared() bool {
	_, ok := m.clearedFields[costitem.FieldUserID]
	return ok
}

// ResetUserID resets all changes to the "user_id" field.
func (m *CostItemMutation) ResetUserID() {
	m.user = nil
	delete(m.clearedFields, costitem.FieldUserID)
}

// SetDescription sets the "description" field.
func (m *CostItemMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *CostItemMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the CostItem entity.
// If the CostItem object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CostItemMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ResetDescription resets all changes to the "description" field.
func (m *CostItemMutation) ResetDescription() {
	m.description = nil
}

// SetQuantity sets the "quantity" field.
func (m *CostItemMutation) SetQuantity(f float64) {
	m.quantity = &f
	m.addquantity = nil
}

// Quantity returns the value of the "quantity" field in the mutation.
func (m *CostItemMutation) Quantity() (r float64, exists bool) {
	v := m.quantity
	if v == nil {
		return
	}
	return *v, true
}

// OldQuantity returns the old "quantity" field's value of the CostItem entity.
// If the CostItem object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CostItemMutation) OldQuantity(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldQuantity is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldQuantity requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldQuantity: %w", err)
	}
	return oldValue.Quantity, nil
}

// AddQuantity adds f to the "quantity" field.
func (m *CostItemMutation) AddQuantity(f float64) {
	if m.addquantity != nil {
		*m.addquantity += f
	} else {
		m.addquantity = &f
	}
}

// AddedQuantity returns the value that was added to the "quantity" field in this mutation.
func (m *CostItemMutation) AddedQuantity() (r float64, exists bool) {
	v := m.addquantity
	if v == nil {
		return
	}
	return *v, true
}

// ResetQuantity resets all changes to the "quantity" field.
func (m *CostItemMutation) ResetQuantity() {
	m.quantity = nil
	m.addquantity = nil
}

// SetUnitPrice sets the "unit_price" field.
func (m *CostItemMutation) SetUnitPrice(f float64) {
	m.unit_price = &f
	m.addunit_price = nil
}

// UnitPrice returns the value of the "unit_price" field in the mutation.
func (m *CostItemMutation) UnitPrice() (r float64, exists bool) {
	v := m.unit_price
	if v == nil {
		return
	}
	return *v, true
}

// OldUnitPrice returns the old "unit_price" field's value of the CostItem entity.
// If the CostItem object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CostItemMutation) OldUnitPrice(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUnitPrice is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUnitPrice requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUnitPrice: %w", err)
	}
	return oldValue.UnitPrice, nil
}

// AddUnitPrice adds f to the "unit_price" field.
func (m *CostItemMutation) AddUnitPrice(f float64) {
	if m.addunit_price != nil {
		*m.addunit_price += f
	} else {
		m.addunit_price = &f
	}
}

// AddedUnitPrice returns the value that was added to the "unit_price" field in this mutation.
func (m *CostItemMutation) AddedUnitPrice() (r float64, exists bool) {
	v := m.addunit_price
	if v == nil {
		return
	}
	return *v, true
}

// ResetUnitPrice resets all changes to the "unit_price" field.
func (m *CostItemMutation) ResetUnitPrice() {
	m.unit_price = nil
	m.addunit_price = nil
}

// SetTotalPrice sets the "total_price" field.
func (m *CostItemMutation) SetTotalPrice(f float64) {
	m.total_price = &f
	m.addtotal_price = nil
}

// TotalPrice returns the value of the "total_price" field in the mutation.
func (m *CostItemMutation) TotalPrice() (r float64, exists bool) {
	v := m.total_price
	if v == nil {
		return
	}
	return *v, true
}

// OldTotalPrice returns the old "total_price" field's value of the CostItem entity.
// If the CostItem object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CostItemMutation) OldTotalPrice(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTotalPrice is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTotalPrice requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTotalPrice: %w", err)
	}
	return oldValue.TotalPrice, nil
}

// AddTotalPrice adds f to the "total_price" field.
func (m *CostItemMutation) AddTotalPrice(f float64) {
	if m.addtotal_price != nil {
		*m.addtotal_price += f
	} else {
		m.addtotal_price = &f
	}
}

// AddedTotalPrice returns the value that was added to the "total_price" field in this mutation.
func (m *CostItemMutation) AddedTotalPrice() (r float64, exists bool) {
	v := m.addtotal_price
	if v == nil {
		return
	}
	return *v, true
}

// ResetTotalPrice resets all changes to the "total_price" field.
func (m *CostItemMutation) ResetTotalPrice() {
	m.total_price = nil
	m.addtotal_price = nil
}

// ClearMaintenanceOrder clears the "maintenance_order" edge to the MaintenanceOrder entity.
func (m *CostItemMutation) ClearMaintenanceOrder() {
	m.clearedmaintenance_order = true
	m.clearedFields[costitem.FieldMaintenanceOrderID] = struct{}{}
}

// MaintenanceOrderCleared reports if the "maintenance_order" edge to the MaintenanceOrder entity was cleared.
func (m *CostItemMutation) MaintenanceOrderCleared() bool {
	return m.MaintenanceOrderIDCleared() || m.clearedmaintenance_order
}

// MaintenanceOrderIDs returns the "maintenance_order" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// MaintenanceOrderID instead. It exists only for internal usage by the builders.
func (m *CostItemMutation) MaintenanceOrderIDs() (ids []int) {
	if id := m.maintenance_order; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetMaintenanceOrder resets all changes to the "maintenance_order" edge.
func (m *CostItemMutation) ResetMaintenanceOrder() {
	m.maintenance_order = nil
	m.clearedmaintenance_order = false
}

// ClearUser clears the "user" edge to the User entity.
func (m *CostItemMutation) ClearUser() {
	m.cleareduser = true
	m.clearedFields[costitem.FieldUserID] = struct{}{}
}

// UserCleared reports if the "user" edge to the User entity was cleared.
func (m *CostItemMutation) UserCleared() bool {
	return m.UserIDCleared() || m.cleareduser
}

// UserIDs returns the "user" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// UserID instead. It exists only for internal usage by the builders.
func (m *CostItemMutation) UserIDs() (ids []int) {
	if id := m.user; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetUser resets all changes to the "user" edge.
func (m *CostItemMutation) ResetUser() {
	m.user = nil
	m.cleareduser = false
}

// Where appends a list predicates to the CostItemMutation builder.
func (m *CostItemMutation) Where(ps ...predicate.CostItem) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the CostItemMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *CostItemMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.CostItem, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *CostItemMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *CostItemMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (CostItem).
func (m *CostItemMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *CostItemMutation) Fields() []string {
	fields := make([]string, 0, 9)
	if m.created_at != nil {
		fields = append(fields, costitem.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, costitem.FieldUpdatedAt)
	}
	if m.active != nil {
		fields = append(fields, costitem.FieldActive)
	}
	if m.maintenance_order != nil {
		fields = append(fields, costitem.FieldMaintenanceOrderID)
	}
	if m.user != nil {
		fields = append(fields, costitem.FieldUserID)
	}
	if m.description != nil {
		fields = append(fields, costitem.FieldDescription)
	}
	if m.quantity != nil {
		fields = append(fields, costitem.FieldQuantity)
	}
	if m.unit_price != nil {
		fields = append(fields, costitem.FieldUnitPrice)
	}
	if m.total_price != nil {
		fields = append(fields, costitem.FieldTotalPrice)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *CostItemMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case costitem.FieldCreatedAt:
		return m.CreatedAt()
	case costitem.FieldUpdatedAt:
		return m.UpdatedAt()
	case costitem.FieldActive:
		return m.Active()
	case costitem.FieldMaintenanceOrderID:
		return m.MaintenanceOrderID()
	case costitem.FieldUserID:
		return m.UserID()
	case costitem.FieldDescription:
		return m.Description()
	case costitem.FieldQuantity:
		return m.Quantity()
	case costitem.FieldUnitPrice:
		return m.UnitPrice()
	case costitem.FieldTotalPrice:
		return m.TotalPrice()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *CostItemMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case costitem.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case costitem.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case costitem.FieldActive:
		return m.OldActive(ctx)
	case costitem.FieldMaintenanceOrderID:
		return m.OldMaintenanceOrderID(ctx)
	case costitem.FieldUserID:
		return m.OldUserID(ctx)
	case costitem.FieldDescription:
		return m.OldDescription(ctx)
	case costitem.FieldQuantity:
		return m.OldQuantity(ctx)
	case costitem.FieldUnitPrice:
		return m.OldUnitPrice(ctx)
	case costitem.FieldTotalPrice:
		return m.OldTotalPrice(ctx)
	}
	return nil, fmt.Errorf("unknown CostItem field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *CostItemMutation) SetField(name string, value ent.Value) error {
	switch name {
	case costitem.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case costitem.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case costitem.FieldActive:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetActive(v)
		return nil
	case costitem.FieldMaintenanceOrderID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMaintenanceOrderID(v)
		return nil
	case costitem.FieldUserID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case costitem.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case costitem.FieldQuantity:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetQuantity(v)
		return nil
	case costitem.FieldUnitPrice:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUnitPrice(v)
		return nil
	case costitem.FieldTotalPrice:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTotalPrice(v)
		return nil
	}
	return fmt.Errorf("unknown CostItem field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *CostItemMutation) AddedFields() []string {
	var fields []string
	if m.addquantity != nil {
		fields = append(fields, costitem.FieldQuantity)
	}
	if m.addunit_price != nil {
		fields = append(fields, costitem.FieldUnitPrice)
	}
	if m.addtotal_price != nil {
		fields = append(fields, costitem.FieldTotalPrice)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *CostItemMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case costitem.FieldQuantity:
		return m.AddedQuantity()
	case costitem.FieldUnitPrice:
		return m.AddedUnitPrice()
	case costitem.FieldTotalPrice:
		return m.AddedTotalPrice()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *CostItemMutation) AddField(name string, value ent.Value) error {
	switch name {
	case costitem.FieldQuantity:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddQuantity(v)
		return nil
	case costitem.FieldUnitPrice:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddUnitPrice(v)
		return nil
	case costitem.FieldTotalPrice:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddTotalPrice(v)
		return nil
	}
	return fmt.Errorf("unknown CostItem numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *CostItemMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(costitem.FieldMaintenanceOrderID) {
		fields = append(fields, costitem.FieldMaintenanceOrderID)
	}
	if m.FieldCleared(costitem.FieldUserID) {
		fields = append(fields, costitem.FieldUserID)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *CostItemMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *CostItemMutation) ClearField(name string) error {
	switch name {
	case costitem.FieldMaintenanceOrderID:
		m.ClearMaintenanceOrderID()
		return nil
	case costitem.FieldUserID:
		m.ClearUserID()
		return nil
	}
	return fmt.Errorf("unknown CostItem nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *CostItemMutation) ResetField(name string) error {
	switch name {
	case costitem.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case costitem.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case costitem.FieldActive:
		m.ResetActive()
		return nil
	case costitem.FieldMaintenanceOrderID:
		m.ResetMaintenanceOrderID()
		return nil
	case costitem.FieldUserID:
		m.ResetUserID()
		return nil
	case costitem.FieldDescription:
		m.ResetDescription()
		return nil
	case costitem.FieldQuantity:
		m.ResetQuantity()
		return nil
	case costitem.FieldUnitPrice:
		m.ResetUnitPrice()
		return nil
	case costitem.FieldTotalPrice:
		m.ResetTotalPrice()
		return nil
	}
	return fmt.Errorf("unknown CostItem field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *CostItemMutation) AddedEdges() []string {
	edges := make([]string, 0, 2)
	if m.maintenance_order != nil {
		edges = append(edges, costitem.EdgeMaintenanceOrder)
	}
	if m.user != nil {
		edges = append(edges, costitem.EdgeUser)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *CostItemMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case costitem.EdgeMaintenanceOrder:
		if id := m.maintenance_order; id != nil {
			return []ent.Value{*id}
		}
	case costitem.EdgeUser:
		if id := m.user; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *CostItemMutation) RemovedEdges() []string {
	edges := make([]string, 0, 2)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *CostItemMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *CostItemMutation) ClearedEdges() []string {
	edges := make([]string, 0, 2)
	if m.clearedmaintenance_order {
		edges = append(edges, costitem.EdgeMaintenanceOrder)
	}
	if m.cleareduser {
		edges = append(edges, costitem.EdgeUser)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *CostItemMutation) EdgeCleared(name string) bool {
	switch name {
	case costitem.EdgeMaintenanceOrder:
		return m.clearedmaintenance_order
	case costitem.EdgeUser:
		return m.cleareduser
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *CostItemMutation) ClearEdge(name string) error {
	switch name {
	case costitem.EdgeMaintenanceOrder:
		m.ClearMaintenanceOrder()
		return nil
	case costitem.EdgeUser:
		m.ClearUser()
		return nil
	}
	return fmt.Errorf("unknown CostItem unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *CostItemMutation) ResetEdge(name string) error {
	switch name {
	case costitem.EdgeMaintenanceOrder:
		m.ResetMaintenanceOrder()
		return nil
	case costitem.EdgeUser:
		m.ResetUser()
		return nil
	}
	return fmt.Errorf("unknown CostItem edge %s", name)
}

// EquipmentMutation represents an operation that mutates the Equipment nodes in the graph.
type EquipmentMutation struct {
	config
	op                        Op
	typ                       string
	id                        *int
	created_at                *time.Time
	updated_at                *time.Time
	active                    *bool
	name                      *string
	_type                     *string
	model                     *string
	serial_number             *string
	installation_date         *time.Time
	last_maintenance_date     *time.Time
	clearedFields             map[string]struct{}
	branch                    *int
	clearedbranch             bool
	maintenance_orders        map[int]struct{}
	removedmaintenance_orders map[int]struct{}
	clearedmaintenance_orders bool
	done                      bool
	oldValue                  func(context.Context) (*Equipment, error)
	predicates                []predicate.Equipment
}

var _ ent.Mutation = (*EquipmentMutation)(nil)

// equipmentOption allows management of the mutation configuration using functional options.
type equipmentOption func(*EquipmentMutation)

// newEquipmentMutation creates new mutation for the Equipment entity.
func newEquipmentMutation(c config, op Op, opts ...equipmentOption) *EquipmentMutation {
	m := &EquipmentMutation{
		config:        c,
		op:            op,
		typ:           TypeEquipment,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withEquipmentID sets the ID field of the mutation.
func withEquipmentID(id int) equipmentOption {
	return func(m *EquipmentMutation) {
		var (
			err   error
			once  sync.Once
			value *Equipment
		)
		m.oldValue = func(ctx context.Context) (*Equipment, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Equipment.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withEquipment sets the old Equipment of the mutation.
func withEquipment(node *Equipment) equipmentOption {
	return func(m *EquipmentMutation) {
		m.oldValue = func(context.Context) (*Equipment, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m EquipmentMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m EquipmentMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *EquipmentMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *EquipmentMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Equipment.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *EquipmentMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *EquipmentMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Equipment entity.
// If the Equipment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EquipmentMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *EquipmentMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *EquipmentMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *EquipmentMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Equipment entity.
// If the Equipment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EquipmentMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *EquipmentMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetActive sets the "active" field.
func (m *EquipmentMutation) SetActive(b bool) {
	m.active = &b
}

// Active returns the value of the "active" field in the mutation.
func (m *EquipmentMutation) Active() (r bool, exists bool) {
	v := m.active
	if v == nil {
		return
	}
	return *v, true
}

// OldActive returns the old "active" field's value of the Equipment entity.
// If the Equipment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EquipmentMutation) OldActive(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldActive is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldActive requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldActive: %w", err)
	}
	return oldValue.Active, nil
}

// ResetActive resets all changes to the "active" field.
func (m *EquipmentMutation) ResetActive() {
	m.active = nil
}

// SetName sets the "name" field.
func (m *EquipmentMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *EquipmentMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Equipment entity.
// If the Equipment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EquipmentMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *EquipmentMutation) ResetName() {
	m.name = nil
}

// SetType sets the "type" field.
func (m *EquipmentMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *EquipmentMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the Equipment entity.
// If the Equipment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EquipmentMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *EquipmentMutation) ResetType() {
	m._type = nil
}

// SetModel sets the "model" field.
func (m *EquipmentMutation) SetModel(s string) {
	m.model = &s
}

// Model returns the value of the "model" field in the mutation.
func (m *EquipmentMutation) Model() (r string, exists bool) {
	v := m.model
	if v == nil {
		return
	}
	return *v, true
}

// OldModel returns the old "model" field's value of the Equipment entity.
// If the Equipment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EquipmentMutation) OldModel(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldModel is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldModel requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldModel: %w", err)
	}
	return oldValue.Model, nil
}

// ResetModel resets all changes to the "model" field.
func (m *EquipmentMutation) ResetModel() {
	m.model = nil
}

// SetSerialNumber sets the "serial_number" field.
func (m *EquipmentMutation) SetSerialNumber(s string) {
	m.serial_number = &s
}

// SerialNumber returns the value of the "serial_number" field in the mutation.
func (m *EquipmentMutation) SerialNumber() (r string, exists bool) {
	v := m.serial_number
	if v == nil {
		return
	}
	return *v, true
}

// OldSerialNumber returns the old "serial_number" field's value of the Equipment entity.
// If the Equipment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EquipmentMutation) OldSerialNumber(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSerialNumber is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSerialNumber requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSerialNumber: %w", err)
	}
	return oldValue.SerialNumber, nil
}

// ResetSerialNumber resets all changes to the "serial_number" field.
func (m *EquipmentMutation) ResetSerialNumber() {
	m.serial_number = nil
}

// SetInstallationDate sets the "installation_date" field.
func (m *EquipmentMutation) SetInstallationDate(t time.Time) {
	m.installation_date = &t
}

// InstallationDate returns the value of the "installation_date" field in the mutation.
func (m *EquipmentMutation) InstallationDate() (r time.Time, exists bool) {
	v := m.installation_date
	if v == nil {
		return
	}
	return *v, true
}

// OldInstallationDate returns the old "installation_date" field's value of the Equipment entity.
// If the Equipment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EquipmentMutation) OldInstallationDate(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldInstallationDate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldInstallationDate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldInstallationDate: %w", err)
	}
	return oldValue.InstallationDate, nil
}

// ClearInstallationDate clears the value of the "installation_date" field.
func (m *EquipmentMutation) ClearInstallationDate() {
	m.installation_date = nil
	m.clearedFields[equipment.FieldInstallationDate] = struct{}{}
}

// InstallationDateCleared returns if the "installation_date" field was cleared in this mutation.
func (m *EquipmentMutation) InstallationDateCleared() bool {
	_, ok := m.clearedFields[equipment.FieldInstallationDate]
	return ok
}

// ResetInstallationDate resets all changes to the "installation_date" field.
func (m *EquipmentMutation) ResetInstallationDate() {
	m.installation_date = nil
	delete(m.clearedFields, equipment.FieldInstallationDate)
}

// SetLastMaintenanceDate sets the "last_maintenance_date" field.
func (m *EquipmentMutation) SetLastMaintenanceDate(t time.Time) {
	m.last_maintenance_date = &t
}

// LastMaintenanceDate returns the value of the "last_maintenance_date" field in the mutation.
func (m *EquipmentMutation) LastMaintenanceDate() (r time.Time, exists bool) {
	v := m.last_maintenance_date
	if v == nil {
		return
	}
	return *v, true
}

// OldLastMaintenanceDate returns the old "last_maintenance_date" field's value of the Equipment entity.
// If the Equipment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EquipmentMutation) OldLastMaintenanceDate(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLastMaintenanceDate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLastMaintenanceDate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLastMaintenanceDate: %w", err)
	}
	return oldValue.LastMaintenanceDate, nil
}

// ClearLastMaintenanceDate clears the value of the "last_maintenance_date" field.
func (m *EquipmentMutation) ClearLastMaintenanceDate() {
	m.last_maintenance_date = nil
	m.clearedFields[equipment.FieldLastMaintenanceDate] = struct{}{}
}

// LastMaintenanceDateCleared returns if the "last_maintenance_date" field was cleared in this mutation.
func (m *EquipmentMutation) LastMaintenanceDateCleared() bool {
	_, ok := m.clearedFields[equipment.FieldLastMaintenanceDate]
	return ok
}

// ResetLastMaintenanceDate resets all changes to the "last_maintenance_date" field.
func (m *EquipmentMutation) ResetLastMaintenanceDate() {
	m.last_maintenance_date = nil
	delete(m.clearedFields, equipment.FieldLastMaintenanceDate)
}

// SetBranchID sets the "branch_id" field.
func (m *EquipmentMutation) SetBranchID(i int) {
	m.branch = &i
}

// BranchID returns the value of the "branch_id" field in the mutation.
func (m *EquipmentMutation) BranchID() (r int, exists bool) {
	v := m.branch
	if v == nil {
		return
	}
	return *v, true
}

// OldBranchID returns the old "branch_id" field's value of the Equipment entity.
// If the Equipment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EquipmentMutation) OldBranchID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBranchID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBranchID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBranchID: %w", err)
	}
	return oldValue.BranchID, nil
}

// ClearBranchID clears the value of the "branch_id" field.
func (m *EquipmentMutation) ClearBranchID() {
	m.branch = nil
	m.clearedFields[equipment.FieldBranchID] = struct{}{}
}

// BranchIDCleared returns if the "branch_id" field was cleared in this mutation.
func (m *EquipmentMutation) BranchIDCleared() bool {
	_, ok := m.clearedFields[equipment.FieldBranchID]
	return ok
}

// ResetBranchID resets all changes to the "branch_id" field.
func (m *EquipmentMutation) ResetBranchID() {
	m.branch = nil
	delete(m.clearedFields, equipment.FieldBranchID)
}

// ClearBranch clears the "branch" edge to the Branch entity.
func (m *EquipmentMutation) ClearBranch() {
	m.clearedbranch = true
	m.clearedFields[equipment.FieldBranchID] = struct{}{}
}

// BranchCleared reports if the "branch" edge to the Branch entity was cleared.
func (m *EquipmentMutation) BranchCleared() bool {
	return m.BranchIDCleared() || m.clearedbranch
}

// BranchIDs returns the "branch" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// BranchID instead. It exists only for internal usage by the builders.
func (m *EquipmentMutation) BranchIDs() (ids []int) {
	if id := m.branch; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetBranch resets all changes to the "branch" edge.
func (m *EquipmentMutation) ResetBranch() {
	m.branch = nil
	m.clearedbranch = false
}

// AddMaintenanceOrderIDs adds the "maintenance_orders" edge to the MaintenanceOrder entity by ids.
func (m *EquipmentMutation) AddMaintenanceOrderIDs(ids ...int) {
	if m.maintenance_orders == nil {
		m.maintenance_orders = make(map[int]struct{})
	}
	for i := range ids {
		m.maintenance_orders[ids[i]] = struct{}{}
	}
}

// ClearMaintenanceOrders clears the "maintenance_orders" edge to the MaintenanceOrder entity.
func (m *EquipmentMutation) ClearMaintenanceOrders() {
	m.clearedmaintenance_orders = true
}

// MaintenanceOrdersCleared reports if the "maintenance_orders" edge to the MaintenanceOrder entity was cleared.
func (m *EquipmentMutation) MaintenanceOrdersCleared() bool {
	return m.clearedmaintenance_orders
}

// RemoveMaintenanceOrderIDs removes the "maintenance_orders" edge to the MaintenanceOrder entity by IDs.
func (m *EquipmentMutation) RemoveMaintenanceOrderIDs(ids ...int) {
	if m.removedmaintenance_orders == nil {
		m.removedmaintenance_orders = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.maintenance_orders, ids[i])
		m.removedmaintenance_orders[ids[i]] = struct{}{}
	}
}

// RemovedMaintenanceOrders returns the removed IDs of the "maintenance_orders" edge to the MaintenanceOrder entity.
func (m *EquipmentMutation) RemovedMaintenanceOrdersIDs() (ids []int) {
	for id := range m.removedmaintenance_orders {
		ids = append(ids, id)
	}
	return
}

// MaintenanceOrdersIDs returns the "maintenance_orders" edge IDs in the mutation.
func (m *EquipmentMutation) MaintenanceOrdersIDs() (ids []int) {
	for id := range m.maintenance_orders {
		ids = append(ids, id)
	}
	return
}

// ResetMaintenanceOrders resets all changes to the "maintenance_orders" edge.
func (m *EquipmentMutation) ResetMaintenanceOrders() {
	m.maintenance_orders = nil
	m.clearedmaintenance_orders = false
	m.removedmaintenance_orders = nil
}

// Where appends a list predicates to the EquipmentMutation builder.
func (m *EquipmentMutation) Where(ps ...predicate.Equipment) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the EquipmentMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *EquipmentMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Equipment, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *EquipmentMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *EquipmentMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Equipment).
func (m *EquipmentMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *EquipmentMutation) Fields() []string {
	fields := make([]string, 0, 10)
	if m.created_at != nil {
		fields = append(fields, equipment.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, equipment.FieldUpdatedAt)
	}
	if m.active != nil {
		fields = append(fields, equipment.FieldActive)
	}
	if m.name != nil {
		fields = append(fields, equipment.FieldName)
	}
	if m._type != nil {
		fields = append(fields, equipment.FieldType)
	}
	if m.model != nil {
		fields = append(fields, equipment.FieldModel)
	}
	if m.serial_number != nil {
		fields = append(fields, equipment.FieldSerialNumber)
	}
	if m.installation_date != nil {
		fields = append(fields, equipment.FieldInstallationDate)
	}
	if m.last_maintenance_date != nil {
		fields = append(fields, equipment.FieldLastMaintenanceDate)
	}
	if m.branch != nil {
		fields = append(fields, equipment.FieldBranchID)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *EquipmentMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case equipment.FieldCreatedAt:
		return m.CreatedAt()
	case equipment.FieldUpdatedAt:
		return m.UpdatedAt()
	case equipment.FieldActive:
		return m.Active()
	case equipment.FieldName:
		return m.Name()
	case equipment.FieldType:
		return m.GetType()
	case equipment.FieldModel:
		return m.Model()
	case equipment.FieldSerialNumber:
		return m.SerialNumber()
	case equipment.FieldInstallationDate:
		return m.InstallationDate()
	case equipment.FieldLastMaintenanceDate:
		return m.LastMaintenanceDate()
	case equipment.FieldBranchID:
		return m.BranchID()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *EquipmentMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case equipment.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case equipment.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case equipment.FieldActive:
		return m.OldActive(ctx)
	case equipment.FieldName:
		return m.OldName(ctx)
	case equipment.FieldType:
		return m.OldType(ctx)
	case equipment.FieldModel:
		return m.OldModel(ctx)
	case equipment.FieldSerialNumber:
		return m.OldSerialNumber(ctx)
	case equipment.FieldInstallationDate:
		return m.OldInstallationDate(ctx)
	case equipment.FieldLastMaintenanceDate:
		return m.OldLastMaintenanceDate(ctx)
	case equipment.FieldBranchID:
		return m.OldBranchID(ctx)
	}
	return nil, fmt.Errorf("unknown Equipment field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *EquipmentMutation) SetField(name string, value ent.Value) error {
	switch name {
	case equipment.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case equipment.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case equipment.FieldActive:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetActive(v)
		return nil
	case equipment.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case equipment.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case equipment.FieldModel:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetModel(v)
		return nil
	case equipment.FieldSerialNumber:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSerialNumber(v)
		return nil
	case equipment.FieldInstallationDate:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetInstallationDate(v)
		return nil
	case equipment.FieldLastMaintenanceDate:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLastMaintenanceDate(v)
		return nil
	case equipment.FieldBranchID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBranchID(v)
		return nil
	}
	return fmt.Errorf("unknown Equipment field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *EquipmentMutation) AddedFields() []string {
	var fields []string
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *EquipmentMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *EquipmentMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Equipment numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *EquipmentMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(equipment.FieldInstallationDate) {
		fields = append(fields, equipment.FieldInstallationDate)
	}
	if m.FieldCleared(equipment.FieldLastMaintenanceDate) {
		fields = append(fields, equipment.FieldLastMaintenanceDate)
	}
	if m.FieldCleared(equipment.FieldBranchID) {
		fields = append(fields, equipment.FieldBranchID)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *EquipmentMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *EquipmentMutation) ClearField(name string) error {
	switch name {
	case equipment.FieldInstallationDate:
		m.ClearInstallationDate()
		return nil
	case equipment.FieldLastMaintenanceDate:
		m.ClearLastMaintenanceDate()
		return nil
	case equipment.FieldBranchID:
		m.ClearBranchID()
		return nil
	}
	return fmt.Errorf("unknown Equipment nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *EquipmentMutation) ResetField(name string) error {
	switch name {
	case equipment.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case equipment.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case equipment.FieldActive:
		m.ResetActive()
		return nil
	case equipment.FieldName:
		m.ResetName()
		return nil
	case equipment.FieldType:
		m.ResetType()
		return nil
	case equipment.FieldModel:
		m.ResetModel()
		return nil
	case equipment.FieldSerialNumber:
		m.ResetSerialNumber()
		return nil
	case equipment.FieldInstallationDate:
		m.ResetInstallationDate()
		return nil
	case equipment.FieldLastMaintenanceDate:
		m.ResetLastMaintenanceDate()
		return nil
	case equipment.FieldBranchID:
		m.ResetBranchID()
		return nil
	}
	return fmt.Errorf("unknown Equipment field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *EquipmentMutation) AddedEdges() []string {
	edges := make([]string, 0, 2)
	if m.branch != nil {
		edges = append(edges, equipment.EdgeBranch)
	}
	if m.maintenance_orders != nil {
		edges = append(edges, equipment.EdgeMaintenanceOrders)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *EquipmentMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case equipment.EdgeBranch:
		if id := m.branch; id != nil {
			return []ent.Value{*id}
		}
	case equipment.EdgeMaintenanceOrders:
		ids := make([]ent.Value, 0, len(m.maintenance_orders))
		for id := range m.maintenance_orders {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *EquipmentMutation) RemovedEdges() []string {
	edges := make([]string, 0, 2)
	if m.removedmaintenance_orders != nil {
		edges = append(edges, equipment.EdgeMaintenanceOrders)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *EquipmentMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case equipment.EdgeMaintenanceOrders:
		ids := make([]ent.Value, 0, len(m.removedmaintenance_orders))
		for id := range m.removedmaintenance_orders {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *EquipmentMutation) ClearedEdges() []string {
	edges := make([]string, 0, 2)
	if m.clearedbranch {
		edges = append(edges, equipment.EdgeBranch)
	}
	if m.clearedmaintenance_orders {
		edges = append(edges, equipment.EdgeMaintenanceOrders)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *EquipmentMutation) EdgeCleared(name string) bool {
	switch name {
	case equipment.EdgeBranch:
		return m.clearedbranch
	case equipment.EdgeMaintenanceOrders:
		return m.clearedmaintenance_orders
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *EquipmentMutation) ClearEdge(name string) error {
	switch name {
	case equipment.EdgeBranch:
		m.ClearBranch()
		return nil
	}
	return fmt.Errorf("unknown Equipment unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *EquipmentMutation) ResetEdge(name string) error {
	switch name {
	case equipment.EdgeBranch:
		m.ResetBranch()
		return nil
	case equipment.EdgeMaintenanceOrders:
		m.ResetMaintenanceOrders()
		return nil
	}
	return fmt.Errorf("unknown Equipment edge %s", name)
}

// InteractionMutation represents an operation that mutates the Interaction nodes in the graph.
type InteractionMutation struct {
	config
	op                       Op
	typ                      string
	id                       *int
	create_time              *time.Time
	update_time              *time.Time
	message                  *string
	_type                    *string
	clearedFields            map[string]struct{}
	maintenance_order        *int
	clearedmaintenance_order bool
	user                     *int
	cleareduser              bool
	done                     bool
	oldValue                 func(context.Context) (*Interaction, error)
	predicates               []predicate.Interaction
}

var _ ent.Mutation = (*InteractionMutation)(nil)

// interactionOption allows management of the mutation configuration using functional options.
type interactionOption func(*InteractionMutation)

// newInteractionMutation creates new mutation for the Interaction entity.
func newInteractionMutation(c config, op Op, opts ...interactionOption) *InteractionMutation {
	m := &InteractionMutation{
		config:        c,
		op:            op,
		typ:           TypeInteraction,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withInteractionID sets the ID field of the mutation.
func withInteractionID(id int) interactionOption {
	return func(m *InteractionMutation) {
		var (
			err   error
			once  sync.Once
			value *Interaction
		)
		m.oldValue = func(ctx context.Context) (*Interaction, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Interaction.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withInteraction sets the old Interaction of the mutation.
func withInteraction(node *Interaction) interactionOption {
	return func(m *InteractionMutation) {
		m.oldValue = func(context.Context) (*Interaction, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m InteractionMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m InteractionMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *InteractionMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *InteractionMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Interaction.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *InteractionMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *InteractionMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the Interaction entity.
// If the Interaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *InteractionMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *InteractionMutation) ResetCreateTime() {
	m.create_time = nil
}

// SetUpdateTime sets the "update_time" field.
func (m *InteractionMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *InteractionMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the Interaction entity.
// If the Interaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *InteractionMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *InteractionMutation) ResetUpdateTime() {
	m.update_time = nil
}

// SetMessage sets the "message" field.
func (m *InteractionMutation) SetMessage(s string) {
	m.message = &s
}

// Message returns the value of the "message" field in the mutation.
func (m *InteractionMutation) Message() (r string, exists bool) {
	v := m.message
	if v == nil {
		return
	}
	return *v, true
}

// OldMessage returns the old "message" field's value of the Interaction entity.
// If the Interaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *InteractionMutation) OldMessage(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMessage is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMessage requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMessage: %w", err)
	}
	return oldValue.Message, nil
}

// ResetMessage resets all changes to the "message" field.
func (m *InteractionMutation) ResetMessage() {
	m.message = nil
}

// SetType sets the "type" field.
func (m *InteractionMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *InteractionMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the Interaction entity.
// If the Interaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *InteractionMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *InteractionMutation) ResetType() {
	m._type = nil
}

// SetMaintenanceOrderID sets the "maintenance_order" edge to the MaintenanceOrder entity by id.
func (m *InteractionMutation) SetMaintenanceOrderID(id int) {
	m.maintenance_order = &id
}

// ClearMaintenanceOrder clears the "maintenance_order" edge to the MaintenanceOrder entity.
func (m *InteractionMutation) ClearMaintenanceOrder() {
	m.clearedmaintenance_order = true
}

// MaintenanceOrderCleared reports if the "maintenance_order" edge to the MaintenanceOrder entity was cleared.
func (m *InteractionMutation) MaintenanceOrderCleared() bool {
	return m.clearedmaintenance_order
}

// MaintenanceOrderID returns the "maintenance_order" edge ID in the mutation.
func (m *InteractionMutation) MaintenanceOrderID() (id int, exists bool) {
	if m.maintenance_order != nil {
		return *m.maintenance_order, true
	}
	return
}

// MaintenanceOrderIDs returns the "maintenance_order" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// MaintenanceOrderID instead. It exists only for internal usage by the builders.
func (m *InteractionMutation) MaintenanceOrderIDs() (ids []int) {
	if id := m.maintenance_order; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetMaintenanceOrder resets all changes to the "maintenance_order" edge.
func (m *InteractionMutation) ResetMaintenanceOrder() {
	m.maintenance_order = nil
	m.clearedmaintenance_order = false
}

// SetUserID sets the "user" edge to the User entity by id.
func (m *InteractionMutation) SetUserID(id int) {
	m.user = &id
}

// ClearUser clears the "user" edge to the User entity.
func (m *InteractionMutation) ClearUser() {
	m.cleareduser = true
}

// UserCleared reports if the "user" edge to the User entity was cleared.
func (m *InteractionMutation) UserCleared() bool {
	return m.cleareduser
}

// UserID returns the "user" edge ID in the mutation.
func (m *InteractionMutation) UserID() (id int, exists bool) {
	if m.user != nil {
		return *m.user, true
	}
	return
}

// UserIDs returns the "user" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// UserID instead. It exists only for internal usage by the builders.
func (m *InteractionMutation) UserIDs() (ids []int) {
	if id := m.user; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetUser resets all changes to the "user" edge.
func (m *InteractionMutation) ResetUser() {
	m.user = nil
	m.cleareduser = false
}

// Where appends a list predicates to the InteractionMutation builder.
func (m *InteractionMutation) Where(ps ...predicate.Interaction) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the InteractionMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *InteractionMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Interaction, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *InteractionMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *InteractionMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Interaction).
func (m *InteractionMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *InteractionMutation) Fields() []string {
	fields := make([]string, 0, 4)
	if m.create_time != nil {
		fields = append(fields, interaction.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, interaction.FieldUpdateTime)
	}
	if m.message != nil {
		fields = append(fields, interaction.FieldMessage)
	}
	if m._type != nil {
		fields = append(fields, interaction.FieldType)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *InteractionMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case interaction.FieldCreateTime:
		return m.CreateTime()
	case interaction.FieldUpdateTime:
		return m.UpdateTime()
	case interaction.FieldMessage:
		return m.Message()
	case interaction.FieldType:
		return m.GetType()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *InteractionMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case interaction.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case interaction.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case interaction.FieldMessage:
		return m.OldMessage(ctx)
	case interaction.FieldType:
		return m.OldType(ctx)
	}
	return nil, fmt.Errorf("unknown Interaction field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *InteractionMutation) SetField(name string, value ent.Value) error {
	switch name {
	case interaction.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case interaction.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case interaction.FieldMessage:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMessage(v)
		return nil
	case interaction.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	}
	return fmt.Errorf("unknown Interaction field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *InteractionMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *InteractionMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *InteractionMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Interaction numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *InteractionMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *InteractionMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *InteractionMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Interaction nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *InteractionMutation) ResetField(name string) error {
	switch name {
	case interaction.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case interaction.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case interaction.FieldMessage:
		m.ResetMessage()
		return nil
	case interaction.FieldType:
		m.ResetType()
		return nil
	}
	return fmt.Errorf("unknown Interaction field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *InteractionMutation) AddedEdges() []string {
	edges := make([]string, 0, 2)
	if m.maintenance_order != nil {
		edges = append(edges, interaction.EdgeMaintenanceOrder)
	}
	if m.user != nil {
		edges = append(edges, interaction.EdgeUser)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *InteractionMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case interaction.EdgeMaintenanceOrder:
		if id := m.maintenance_order; id != nil {
			return []ent.Value{*id}
		}
	case interaction.EdgeUser:
		if id := m.user; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *InteractionMutation) RemovedEdges() []string {
	edges := make([]string, 0, 2)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *InteractionMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *InteractionMutation) ClearedEdges() []string {
	edges := make([]string, 0, 2)
	if m.clearedmaintenance_order {
		edges = append(edges, interaction.EdgeMaintenanceOrder)
	}
	if m.cleareduser {
		edges = append(edges, interaction.EdgeUser)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *InteractionMutation) EdgeCleared(name string) bool {
	switch name {
	case interaction.EdgeMaintenanceOrder:
		return m.clearedmaintenance_order
	case interaction.EdgeUser:
		return m.cleareduser
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *InteractionMutation) ClearEdge(name string) error {
	switch name {
	case interaction.EdgeMaintenanceOrder:
		m.ClearMaintenanceOrder()
		return nil
	case interaction.EdgeUser:
		m.ClearUser()
		return nil
	}
	return fmt.Errorf("unknown Interaction unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *InteractionMutation) ResetEdge(name string) error {
	switch name {
	case interaction.EdgeMaintenanceOrder:
		m.ResetMaintenanceOrder()
		return nil
	case interaction.EdgeUser:
		m.ResetUser()
		return nil
	}
	return fmt.Errorf("unknown Interaction edge %s", name)
}

// MaintenanceOrderMutation represents an operation that mutates the MaintenanceOrder nodes in the graph.
type MaintenanceOrderMutation struct {
	config
	op                  Op
	typ                 string
	id                  *int
	created_at          *time.Time
	updated_at          *time.Time
	active              *bool
	title               *string
	description         *string
	status              *string
	priority            *string
	cancellation_reason *string
	rejection_reason    *string
	clearedFields       map[string]struct{}
	branch              *int
	clearedbranch       bool
	equipment           *int
	clearedequipment    bool
	requester           *int
	clearedrequester    bool
	approver            *int
	clearedapprover     bool
	technician          *int
	clearedtechnician   bool
	cost_items          map[int]struct{}
	removedcost_items   map[int]struct{}
	clearedcost_items   bool
	interactions        map[int]struct{}
	removedinteractions map[int]struct{}
	clearedinteractions bool
	done                bool
	oldValue            func(context.Context) (*MaintenanceOrder, error)
	predicates          []predicate.MaintenanceOrder
}

var _ ent.Mutation = (*MaintenanceOrderMutation)(nil)

// maintenanceorderOption allows management of the mutation configuration using functional options.
type maintenanceorderOption func(*MaintenanceOrderMutation)

// newMaintenanceOrderMutation creates new mutation for the MaintenanceOrder entity.
func newMaintenanceOrderMutation(c config, op Op, opts ...maintenanceorderOption) *MaintenanceOrderMutation {
	m := &MaintenanceOrderMutation{
		config:        c,
		op:            op,
		typ:           TypeMaintenanceOrder,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withMaintenanceOrderID sets the ID field of the mutation.
func withMaintenanceOrderID(id int) maintenanceorderOption {
	return func(m *MaintenanceOrderMutation) {
		var (
			err   error
			once  sync.Once
			value *MaintenanceOrder
		)
		m.oldValue = func(ctx context.Context) (*MaintenanceOrder, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().MaintenanceOrder.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withMaintenanceOrder sets the old MaintenanceOrder of the mutation.
func withMaintenanceOrder(node *MaintenanceOrder) maintenanceorderOption {
	return func(m *MaintenanceOrderMutation) {
		m.oldValue = func(context.Context) (*MaintenanceOrder, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m MaintenanceOrderMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m MaintenanceOrderMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *MaintenanceOrderMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *MaintenanceOrderMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().MaintenanceOrder.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *MaintenanceOrderMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *MaintenanceOrderMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the MaintenanceOrder entity.
// If the MaintenanceOrder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaintenanceOrderMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *MaintenanceOrderMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *MaintenanceOrderMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *MaintenanceOrderMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the MaintenanceOrder entity.
// If the MaintenanceOrder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaintenanceOrderMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *MaintenanceOrderMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetActive sets the "active" field.
func (m *MaintenanceOrderMutation) SetActive(b bool) {
	m.active = &b
}

// Active returns the value of the "active" field in the mutation.
func (m *MaintenanceOrderMutation) Active() (r bool, exists bool) {
	v := m.active
	if v == nil {
		return
	}
	return *v, true
}

// OldActive returns the old "active" field's value of the MaintenanceOrder entity.
// If the MaintenanceOrder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaintenanceOrderMutation) OldActive(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldActive is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldActive requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldActive: %w", err)
	}
	return oldValue.Active, nil
}

// ResetActive resets all changes to the "active" field.
func (m *MaintenanceOrderMutation) ResetActive() {
	m.active = nil
}

// SetTitle sets the "title" field.
func (m *MaintenanceOrderMutation) SetTitle(s string) {
	m.title = &s
}

// Title returns the value of the "title" field in the mutation.
func (m *MaintenanceOrderMutation) Title() (r string, exists bool) {
	v := m.title
	if v == nil {
		return
	}
	return *v, true
}

// OldTitle returns the old "title" field's value of the MaintenanceOrder entity.
// If the MaintenanceOrder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaintenanceOrderMutation) OldTitle(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTitle is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTitle requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTitle: %w", err)
	}
	return oldValue.Title, nil
}

// ResetTitle resets all changes to the "title" field.
func (m *MaintenanceOrderMutation) ResetTitle() {
	m.title = nil
}

// SetDescription sets the "description" field.
func (m *MaintenanceOrderMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *MaintenanceOrderMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the MaintenanceOrder entity.
// If the MaintenanceOrder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaintenanceOrderMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ResetDescription resets all changes to the "description" field.
func (m *MaintenanceOrderMutation) ResetDescription() {
	m.description = nil
}

// SetStatus sets the "status" field.
func (m *MaintenanceOrderMutation) SetStatus(s string) {
	m.status = &s
}

// Status returns the value of the "status" field in the mutation.
func (m *MaintenanceOrderMutation) Status() (r string, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the MaintenanceOrder entity.
// If the MaintenanceOrder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaintenanceOrderMutation) OldStatus(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *MaintenanceOrderMutation) ResetStatus() {
	m.status = nil
}

// SetPriority sets the "priority" field.
func (m *MaintenanceOrderMutation) SetPriority(s string) {
	m.priority = &s
}

// Priority returns the value of the "priority" field in the mutation.
func (m *MaintenanceOrderMutation) Priority() (r string, exists bool) {
	v := m.priority
	if v == nil {
		return
	}
	return *v, true
}

// OldPriority returns the old "priority" field's value of the MaintenanceOrder entity.
// If the MaintenanceOrder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaintenanceOrderMutation) OldPriority(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPriority is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPriority requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPriority: %w", err)
	}
	return oldValue.Priority, nil
}

// ResetPriority resets all changes to the "priority" field.
func (m *MaintenanceOrderMutation) ResetPriority() {
	m.priority = nil
}

// SetBranchID sets the "branch_id" field.
func (m *MaintenanceOrderMutation) SetBranchID(i int) {
	m.branch = &i
}

// BranchID returns the value of the "branch_id" field in the mutation.
func (m *MaintenanceOrderMutation) BranchID() (r int, exists bool) {
	v := m.branch
	if v == nil {
		return
	}
	return *v, true
}

// OldBranchID returns the old "branch_id" field's value of the MaintenanceOrder entity.
// If the MaintenanceOrder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaintenanceOrderMutation) OldBranchID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBranchID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBranchID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBranchID: %w", err)
	}
	return oldValue.BranchID, nil
}

// ClearBranchID clears the value of the "branch_id" field.
func (m *MaintenanceOrderMutation) ClearBranchID() {
	m.branch = nil
	m.clearedFields[maintenanceorder.FieldBranchID] = struct{}{}
}

// BranchIDCleared returns if the "branch_id" field was cleared in this mutation.
func (m *MaintenanceOrderMutation) BranchIDCleared() bool {
	_, ok := m.clearedFields[maintenanceorder.FieldBranchID]
	return ok
}

// ResetBranchID resets all changes to the "branch_id" field.
func (m *MaintenanceOrderMutation) ResetBranchID() {
	m.branch = nil
	delete(m.clearedFields, maintenanceorder.FieldBranchID)
}

// SetEquipmentID sets the "equipment_id" field.
func (m *MaintenanceOrderMutation) SetEquipmentID(i int) {
	m.equipment = &i
}

// EquipmentID returns the value of the "equipment_id" field in the mutation.
func (m *MaintenanceOrderMutation) EquipmentID() (r int, exists bool) {
	v := m.equipment
	if v == nil {
		return
	}
	return *v, true
}

// OldEquipmentID returns the old "equipment_id" field's value of the MaintenanceOrder entity.
// If the MaintenanceOrder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaintenanceOrderMutation) OldEquipmentID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEquipmentID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEquipmentID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEquipmentID: %w", err)
	}
	return oldValue.EquipmentID, nil
}

// ClearEquipmentID clears the value of the "equipment_id" field.
func (m *MaintenanceOrderMutation) ClearEquipmentID() {
	m.equipment = nil
	m.clearedFields[maintenanceorder.FieldEquipmentID] = struct{}{}
}

// EquipmentIDCleared returns if the "equipment_id" field was cleared in this mutation.
func (m *MaintenanceOrderMutation) EquipmentIDCleared() bool {
	_, ok := m.clearedFields[maintenanceorder.FieldEquipmentID]
	return ok
}

// ResetEquipmentID resets all changes to the "equipment_id" field.
func (m *MaintenanceOrderMutation) ResetEquipmentID() {
	m.equipment = nil
	delete(m.clearedFields, maintenanceorder.FieldEquipmentID)
}

// SetRequesterID sets the "requester_id" field.
func (m *MaintenanceOrderMutation) SetRequesterID(i int) {
	m.requester = &i
}

// RequesterID returns the value of the "requester_id" field in the mutation.
func (m *MaintenanceOrderMutation) RequesterID() (r int, exists bool) {
	v := m.requester
	if v == nil {
		return
	}
	return *v, true
}

// OldRequesterID returns the old "requester_id" field's value of the MaintenanceOrder entity.
// If the MaintenanceOrder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaintenanceOrderMutation) OldRequesterID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRequesterID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRequesterID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRequesterID: %w", err)
	}
	return oldValue.RequesterID, nil
}

// ClearRequesterID clears the value of the "requester_id" field.
func (m *MaintenanceOrderMutation) ClearRequesterID() {
	m.requester = nil
	m.clearedFields[maintenanceorder.FieldRequesterID] = struct{}{}
}

// RequesterIDCleared returns if the "requester_id" field was cleared in this mutation.
func (m *MaintenanceOrderMutation) RequesterIDCleared() bool {
	_, ok := m.clearedFields[maintenanceorder.FieldRequesterID]
	return ok
}

// ResetRequesterID resets all changes to the "requester_id" field.
func (m *MaintenanceOrderMutation) ResetRequesterID() {
	m.requester = nil
	delete(m.clearedFields, maintenanceorder.FieldRequesterID)
}

// SetApproverID sets the "approver_id" field.
func (m *MaintenanceOrderMutation) SetApproverID(i int) {
	m.approver = &i
}

// ApproverID returns the value of the "approver_id" field in the mutation.
func (m *MaintenanceOrderMutation) ApproverID() (r int, exists bool) {
	v := m.approver
	if v == nil {
		return
	}
	return *v, true
}

// OldApproverID returns the old "approver_id" field's value of the MaintenanceOrder entity.
// If the MaintenanceOrder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaintenanceOrderMutation) OldApproverID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldApproverID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldApproverID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldApproverID: %w", err)
	}
	return oldValue.ApproverID, nil
}

// ClearApproverID clears the value of the "approver_id" field.
func (m *MaintenanceOrderMutation) ClearApproverID() {
	m.approver = nil
	m.clearedFields[maintenanceorder.FieldApproverID] = struct{}{}
}

// ApproverIDCleared returns if the "approver_id" field was cleared in this mutation.
func (m *MaintenanceOrderMutation) ApproverIDCleared() bool {
	_, ok := m.clearedFields[maintenanceorder.FieldApproverID]
	return ok
}

// ResetApproverID resets all changes to the "approver_id" field.
func (m *MaintenanceOrderMutation) ResetApproverID() {
	m.approver = nil
	delete(m.clearedFields, maintenanceorder.FieldApproverID)
}

// SetTechnicianID sets the "technician_id" field.
func (m *MaintenanceOrderMutation) SetTechnicianID(i int) {
	m.technician = &i
}

// TechnicianID returns the value of the "technician_id" field in the mutation.
func (m *MaintenanceOrderMutation) TechnicianID() (r int, exists bool) {
	v := m.technician
	if v == nil {
		return
	}
	return *v, true
}

// OldTechnicianID returns the old "technician_id" field's value of the MaintenanceOrder entity.
// If the MaintenanceOrder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaintenanceOrderMutation) OldTechnicianID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTechnicianID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTechnicianID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTechnicianID: %w", err)
	}
	return oldValue.TechnicianID, nil
}

// ClearTechnicianID clears the value of the "technician_id" field.
func (m *MaintenanceOrderMutation) ClearTechnicianID() {
	m.technician = nil
	m.clearedFields[maintenanceorder.FieldTechnicianID] = struct{}{}
}

// TechnicianIDCleared returns if the "technician_id" field was cleared in this mutation.
func (m *MaintenanceOrderMutation) TechnicianIDCleared() bool {
	_, ok := m.clearedFields[maintenanceorder.FieldTechnicianID]
	return ok
}

// ResetTechnicianID resets all changes to the "technician_id" field.
func (m *MaintenanceOrderMutation) ResetTechnicianID() {
	m.technician = nil
	delete(m.clearedFields, maintenanceorder.FieldTechnicianID)
}

// SetCancellationReason sets the "cancellation_reason" field.
func (m *MaintenanceOrderMutation) SetCancellationReason(s string) {
	m.cancellation_reason = &s
}

// CancellationReason returns the value of the "cancellation_reason" field in the mutation.
func (m *MaintenanceOrderMutation) CancellationReason() (r string, exists bool) {
	v := m.cancellation_reason
	if v == nil {
		return
	}
	return *v, true
}

// OldCancellationReason returns the old "cancellation_reason" field's value of the MaintenanceOrder entity.
// If the MaintenanceOrder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaintenanceOrderMutation) OldCancellationReason(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCancellationReason is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCancellationReason requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCancellationReason: %w", err)
	}
	return oldValue.CancellationReason, nil
}

// ClearCancellationReason clears the value of the "cancellation_reason" field.
func (m *MaintenanceOrderMutation) ClearCancellationReason() {
	m.cancellation_reason = nil
	m.clearedFields[maintenanceorder.FieldCancellationReason] = struct{}{}
}

// CancellationReasonCleared returns if the "cancellation_reason" field was cleared in this mutation.
func (m *MaintenanceOrderMutation) CancellationReasonCleared() bool {
	_, ok := m.clearedFields[maintenanceorder.FieldCancellationReason]
	return ok
}

// ResetCancellationReason resets all changes to the "cancellation_reason" field.
func (m *MaintenanceOrderMutation) ResetCancellationReason() {
	m.cancellation_reason = nil
	delete(m.clearedFields, maintenanceorder.FieldCancellationReason)
}

// SetRejectionReason sets the "rejection_reason" field.
func (m *MaintenanceOrderMutation) SetRejectionReason(s string) {
	m.rejection_reason = &s
}

// RejectionReason returns the value of the "rejection_reason" field in the mutation.
func (m *MaintenanceOrderMutation) RejectionReason() (r string, exists bool) {
	v := m.rejection_reason
	if v == nil {
		return
	}
	return *v, true
}

// OldRejectionReason returns the old "rejection_reason" field's value of the MaintenanceOrder entity.
// If the MaintenanceOrder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaintenanceOrderMutation) OldRejectionReason(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRejectionReason is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRejectionReason requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRejectionReason: %w", err)
	}
	return oldValue.RejectionReason, nil
}

// ClearRejectionReason clears the value of the "rejection_reason" field.
func (m *MaintenanceOrderMutation) ClearRejectionReason() {
	m.rejection_reason = nil
	m.clearedFields[maintenanceorder.FieldRejectionReason] = struct{}{}
}

// RejectionReasonCleared returns if the "rejection_reason" field was cleared in this mutation.
func (m *MaintenanceOrderMutation) RejectionReasonCleared() bool {
	_, ok := m.clearedFields[maintenanceorder.FieldRejectionReason]
	return ok
}

// ResetRejectionReason resets all changes to the "rejection_reason" field.
func (m *MaintenanceOrderMutation) ResetRejectionReason() {
	m.rejection_reason = nil
	delete(m.clearedFields, maintenanceorder.FieldRejectionReason)
}

// ClearBranch clears the "branch" edge to the Branch entity.
func (m *MaintenanceOrderMutation) ClearBranch() {
	m.clearedbranch = true
	m.clearedFields[maintenanceorder.FieldBranchID] = struct{}{}
}

// BranchCleared reports if the "branch" edge to the Branch entity was cleared.
func (m *MaintenanceOrderMutation) BranchCleared() bool {
	return m.BranchIDCleared() || m.clearedbranch
}

// BranchIDs returns the "branch" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// BranchID instead. It exists only for internal usage by the builders.
func (m *MaintenanceOrderMutation) BranchIDs() (ids []int) {
	if id := m.branch; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetBranch resets all changes to the "branch" edge.
func (m *MaintenanceOrderMutation) ResetBranch() {
	m.branch = nil
	m.clearedbranch = false
}

// ClearEquipment clears the "equipment" edge to the Equipment entity.
func (m *MaintenanceOrderMutation) ClearEquipment() {
	m.clearedequipment = true
	m.clearedFields[maintenanceorder.FieldEquipmentID] = struct{}{}
}

// EquipmentCleared reports if the "equipment" edge to the Equipment entity was cleared.
func (m *MaintenanceOrderMutation) EquipmentCleared() bool {
	return m.EquipmentIDCleared() || m.clearedequipment
}

// EquipmentIDs returns the "equipment" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// EquipmentID instead. It exists only for internal usage by the builders.
func (m *MaintenanceOrderMutation) EquipmentIDs() (ids []int) {
	if id := m.equipment; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetEquipment resets all changes to the "equipment" edge.
func (m *MaintenanceOrderMutation) ResetEquipment() {
	m.equipment = nil
	m.clearedequipment = false
}

// ClearRequester clears the "requester" edge to the User entity.
func (m *MaintenanceOrderMutation) ClearRequester() {
	m.clearedrequester = true
	m.clearedFields[maintenanceorder.FieldRequesterID] = struct{}{}
}

// RequesterCleared reports if the "requester" edge to the User entity was cleared.
func (m *MaintenanceOrderMutation) RequesterCleared() bool {
	return m.RequesterIDCleared() || m.clearedrequester
}

// RequesterIDs returns the "requester" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// RequesterID instead. It exists only for internal usage by the builders.
func (m *MaintenanceOrderMutation) RequesterIDs() (ids []int) {
	if id := m.requester; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetRequester resets all changes to the "requester" edge.
func (m *MaintenanceOrderMutation) ResetRequester() {
	m.requester = nil
	m.clearedrequester = false
}

// ClearApprover clears the "approver" edge to the User entity.
func (m *MaintenanceOrderMutation) ClearApprover() {
	m.clearedapprover = true
	m.clearedFields[maintenanceorder.FieldApproverID] = struct{}{}
}

// ApproverCleared reports if the "approver" edge to the User entity was cleared.
func (m *MaintenanceOrderMutation) ApproverCleared() bool {
	return m.ApproverIDCleared() || m.clearedapprover
}

// ApproverIDs returns the "approver" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// ApproverID instead. It exists only for internal usage by the builders.
func (m *MaintenanceOrderMutation) ApproverIDs() (ids []int) {
	if id := m.approver; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetApprover resets all changes to the "approver" edge.
func (m *MaintenanceOrderMutation) ResetApprover() {
	m.approver = nil
	m.clearedapprover = false
}

// ClearTechnician clears the "technician" edge to the User entity.
func (m *MaintenanceOrderMutation) ClearTechnician() {
	m.clearedtechnician = true
	m.clearedFields[maintenanceorder.FieldTechnicianID] = struct{}{}
}

// TechnicianCleared reports if the "technician" edge to the User entity was cleared.
func (m *MaintenanceOrderMutation) TechnicianCleared() bool {
	return m.TechnicianIDCleared() || m.clearedtechnician
}

// TechnicianIDs returns the "technician" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// TechnicianID instead. It exists only for internal usage by the builders.
func (m *MaintenanceOrderMutation) TechnicianIDs() (ids []int) {
	if id := m.technician; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetTechnician resets all changes to the "technician" edge.
func (m *MaintenanceOrderMutation) ResetTechnician() {
	m.technician = nil
	m.clearedtechnician = false
}

// AddCostItemIDs adds the "cost_items" edge to the CostItem entity by ids.
func (m *MaintenanceOrderMutation) AddCostItemIDs(ids ...int) {
	if m.cost_items == nil {
		m.cost_items = make(map[int]struct{})
	}
	for i := range ids {
		m.cost_items[ids[i]] = struct{}{}
	}
}

// ClearCostItems clears the "cost_items" edge to the CostItem entity.
func (m *MaintenanceOrderMutation) ClearCostItems() {
	m.clearedcost_items = true
}

// CostItemsCleared reports if the "cost_items" edge to the CostItem entity was cleared.
func (m *MaintenanceOrderMutation) CostItemsCleared() bool {
	return m.clearedcost_items
}

// RemoveCostItemIDs removes the "cost_items" edge to the CostItem entity by IDs.
func (m *MaintenanceOrderMutation) RemoveCostItemIDs(ids ...int) {
	if m.removedcost_items == nil {
		m.removedcost_items = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.cost_items, ids[i])
		m.removedcost_items[ids[i]] = struct{}{}
	}
}

// RemovedCostItems returns the removed IDs of the "cost_items" edge to the CostItem entity.
func (m *MaintenanceOrderMutation) RemovedCostItemsIDs() (ids []int) {
	for id := range m.removedcost_items {
		ids = append(ids, id)
	}
	return
}

// CostItemsIDs returns the "cost_items" edge IDs in the mutation.
func (m *MaintenanceOrderMutation) CostItemsIDs() (ids []int) {
	for id := range m.cost_items {
		ids = append(ids, id)
	}
	return
}

// ResetCostItems resets all changes to the "cost_items" edge.
func (m *MaintenanceOrderMutation) ResetCostItems() {
	m.cost_items = nil
	m.clearedcost_items = false
	m.removedcost_items = nil
}

// AddInteractionIDs adds the "interactions" edge to the Interaction entity by ids.
func (m *MaintenanceOrderMutation) AddInteractionIDs(ids ...int) {
	if m.interactions == nil {
		m.interactions = make(map[int]struct{})
	}
	for i := range ids {
		m.interactions[ids[i]] = struct{}{}
	}
}

// ClearInteractions clears the "interactions" edge to the Interaction entity.
func (m *MaintenanceOrderMutation) ClearInteractions() {
	m.clearedinteractions = true
}

// InteractionsCleared reports if the "interactions" edge to the Interaction entity was cleared.
func (m *MaintenanceOrderMutation) InteractionsCleared() bool {
	return m.clearedinteractions
}

// RemoveInteractionIDs removes the "interactions" edge to the Interaction entity by IDs.
func (m *MaintenanceOrderMutation) RemoveInteractionIDs(ids ...int) {
	if m.removedinteractions == nil {
		m.removedinteractions = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.interactions, ids[i])
		m.removedinteractions[ids[i]] = struct{}{}
	}
}

// RemovedInteractions returns the removed IDs of the "interactions" edge to the Interaction entity.
func (m *MaintenanceOrderMutation) RemovedInteractionsIDs() (ids []int) {
	for id := range m.removedinteractions {
		ids = append(ids, id)
	}
	return
}

// InteractionsIDs returns the "interactions" edge IDs in the mutation.
func (m *MaintenanceOrderMutation) InteractionsIDs() (ids []int) {
	for id := range m.interactions {
		ids = append(ids, id)
	}
	return
}

// ResetInteractions resets all changes to the "interactions" edge.
func (m *MaintenanceOrderMutation) ResetInteractions() {
	m.interactions = nil
	m.clearedinteractions = false
	m.removedinteractions = nil
}

// Where appends a list predicates to the MaintenanceOrderMutation builder.
func (m *MaintenanceOrderMutation) Where(ps ...predicate.MaintenanceOrder) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the MaintenanceOrderMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *MaintenanceOrderMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.MaintenanceOrder, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *MaintenanceOrderMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *MaintenanceOrderMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (MaintenanceOrder).
func (m *MaintenanceOrderMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *MaintenanceOrderMutation) Fields() []string {
	fields := make([]string, 0, 14)
	if m.created_at != nil {
		fields = append(fields, maintenanceorder.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, maintenanceorder.FieldUpdatedAt)
	}
	if m.active != nil {
		fields = append(fields, maintenanceorder.FieldActive)
	}
	if m.title != nil {
		fields = append(fields, maintenanceorder.FieldTitle)
	}
	if m.description != nil {
		fields = append(fields, maintenanceorder.FieldDescription)
	}
	if m.status != nil {
		fields = append(fields, maintenanceorder.FieldStatus)
	}
	if m.priority != nil {
		fields = append(fields, maintenanceorder.FieldPriority)
	}
	if m.branch != nil {
		fields = append(fields, maintenanceorder.FieldBranchID)
	}
	if m.equipment != nil {
		fields = append(fields, maintenanceorder.FieldEquipmentID)
	}
	if m.requester != nil {
		fields = append(fields, maintenanceorder.FieldRequesterID)
	}
	if m.approver != nil {
		fields = append(fields, maintenanceorder.FieldApproverID)
	}
	if m.technician != nil {
		fields = append(fields, maintenanceorder.FieldTechnicianID)
	}
	if m.cancellation_reason != nil {
		fields = append(fields, maintenanceorder.FieldCancellationReason)
	}
	if m.rejection_reason != nil {
		fields = append(fields, maintenanceorder.FieldRejectionReason)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *MaintenanceOrderMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case maintenanceorder.FieldCreatedAt:
		return m.CreatedAt()
	case maintenanceorder.FieldUpdatedAt:
		return m.UpdatedAt()
	case maintenanceorder.FieldActive:
		return m.Active()
	case maintenanceorder.FieldTitle:
		return m.Title()
	case maintenanceorder.FieldDescription:
		return m.Description()
	case maintenanceorder.FieldStatus:
		return m.Status()
	case maintenanceorder.FieldPriority:
		return m.Priority()
	case maintenanceorder.FieldBranchID:
		return m.BranchID()
	case maintenanceorder.FieldEquipmentID:
		return m.EquipmentID()
	case maintenanceorder.FieldRequesterID:
		return m.RequesterID()
	case maintenanceorder.FieldApproverID:
		return m.ApproverID()
	case maintenanceorder.FieldTechnicianID:
		return m.TechnicianID()
	case maintenanceorder.FieldCancellationReason:
		return m.CancellationReason()
	case maintenanceorder.FieldRejectionReason:
		return m.RejectionReason()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *MaintenanceOrderMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case maintenanceorder.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case maintenanceorder.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case maintenanceorder.FieldActive:
		return m.OldActive(ctx)
	case maintenanceorder.FieldTitle:
		return m.OldTitle(ctx)
	case maintenanceorder.FieldDescription:
		return m.OldDescription(ctx)
	case maintenanceorder.FieldStatus:
		return m.OldStatus(ctx)
	case maintenanceorder.FieldPriority:
		return m.OldPriority(ctx)
	case maintenanceorder.FieldBranchID:
		return m.OldBranchID(ctx)
	case maintenanceorder.FieldEquipmentID:
		return m.OldEquipmentID(ctx)
	case maintenanceorder.FieldRequesterID:
		return m.OldRequesterID(ctx)
	case maintenanceorder.FieldApproverID:
		return m.OldApproverID(ctx)
	case maintenanceorder.FieldTechnicianID:
		return m.OldTechnicianID(ctx)
	case maintenanceorder.FieldCancellationReason:
		return m.OldCancellationReason(ctx)
	case maintenanceorder.FieldRejectionReason:
		return m.OldRejectionReason(ctx)
	}
	return nil, fmt.Errorf("unknown MaintenanceOrder field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *MaintenanceOrderMutation) SetField(name string, value ent.Value) error {
	switch name {
	case maintenanceorder.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case maintenanceorder.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case maintenanceorder.FieldActive:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetActive(v)
		return nil
	case maintenanceorder.FieldTitle:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTitle(v)
		return nil
	case maintenanceorder.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case maintenanceorder.FieldStatus:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case maintenanceorder.FieldPriority:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPriority(v)
		return nil
	case maintenanceorder.FieldBranchID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBranchID(v)
		return nil
	case maintenanceorder.FieldEquipmentID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEquipmentID(v)
		return nil
	case maintenanceorder.FieldRequesterID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRequesterID(v)
		return nil
	case maintenanceorder.FieldApproverID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetApproverID(v)
		return nil
	case maintenanceorder.FieldTechnicianID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTechnicianID(v)
		return nil
	case maintenanceorder.FieldCancellationReason:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCancellationReason(v)
		return nil
	case maintenanceorder.FieldRejectionReason:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRejectionReason(v)
		return nil
	}
	return fmt.Errorf("unknown MaintenanceOrder field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *MaintenanceOrderMutation) AddedFields() []string {
	var fields []string
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *MaintenanceOrderMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *MaintenanceOrderMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown MaintenanceOrder numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *MaintenanceOrderMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(maintenanceorder.FieldBranchID) {
		fields = append(fields, maintenanceorder.FieldBranchID)
	}
	if m.FieldCleared(maintenanceorder.FieldEquipmentID) {
		fields = append(fields, maintenanceorder.FieldEquipmentID)
	}
	if m.FieldCleared(maintenanceorder.FieldRequesterID) {
		fields = append(fields, maintenanceorder.FieldRequesterID)
	}
	if m.FieldCleared(maintenanceorder.FieldApproverID) {
		fields = append(fields, maintenanceorder.FieldApproverID)
	}
	if m.FieldCleared(maintenanceorder.FieldTechnicianID) {
		fields = append(fields, maintenanceorder.FieldTechnicianID)
	}
	if m.FieldCleared(maintenanceorder.FieldCancellationReason) {
		fields = append(fields, maintenanceorder.FieldCancellationReason)
	}
	if m.FieldCleared(maintenanceorder.FieldRejectionReason) {
		fields = append(fields, maintenanceorder.FieldRejectionReason)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *MaintenanceOrderMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *MaintenanceOrderMutation) ClearField(name string) error {
	switch name {
	case maintenanceorder.FieldBranchID:
		m.ClearBranchID()
		return nil
	case maintenanceorder.FieldEquipmentID:
		m.ClearEquipmentID()
		return nil
	case maintenanceorder.FieldRequesterID:
		m.ClearRequesterID()
		return nil
	case maintenanceorder.FieldApproverID:
		m.ClearApproverID()
		return nil
	case maintenanceorder.FieldTechnicianID:
		m.ClearTechnicianID()
		return nil
	case maintenanceorder.FieldCancellationReason:
		m.ClearCancellationReason()
		return nil
	case maintenanceorder.FieldRejectionReason:
		m.ClearRejectionReason()
		return nil
	}
	return fmt.Errorf("unknown MaintenanceOrder nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *MaintenanceOrderMutation) ResetField(name string) error {
	switch name {
	case maintenanceorder.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case maintenanceorder.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case maintenanceorder.FieldActive:
		m.ResetActive()
		return nil
	case maintenanceorder.FieldTitle:
		m.ResetTitle()
		return nil
	case maintenanceorder.FieldDescription:
		m.ResetDescription()
		return nil
	case maintenanceorder.FieldStatus:
		m.ResetStatus()
		return nil
	case maintenanceorder.FieldPriority:
		m.ResetPriority()
		return nil
	case maintenanceorder.FieldBranchID:
		m.ResetBranchID()
		return nil
	case maintenanceorder.FieldEquipmentID:
		m.ResetEquipmentID()
		return nil
	case maintenanceorder.FieldRequesterID:
		m.ResetRequesterID()
		return nil
	case maintenanceorder.FieldApproverID:
		m.ResetApproverID()
		return nil
	case maintenanceorder.FieldTechnicianID:
		m.ResetTechnicianID()
		return nil
	case maintenanceorder.FieldCancellationReason:
		m.ResetCancellationReason()
		return nil
	case maintenanceorder.FieldRejectionReason:
		m.ResetRejectionReason()
		return nil
	}
	return fmt.Errorf("unknown MaintenanceOrder field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *MaintenanceOrderMutation) AddedEdges() []string {
	edges := make([]string, 0, 7)
	if m.branch != nil {
		edges = append(edges, maintenanceorder.EdgeBranch)
	}
	if m.equipment != nil {
		edges = append(edges, maintenanceorder.EdgeEquipment)
	}
	if m.requester != nil {
		edges = append(edges, maintenanceorder.EdgeRequester)
	}
	if m.approver != nil {
		edges = append(edges, maintenanceorder.EdgeApprover)
	}
	if m.technician != nil {
		edges = append(edges, maintenanceorder.EdgeTechnician)
	}
	if m.cost_items != nil {
		edges = append(edges, maintenanceorder.EdgeCostItems)
	}
	if m.interactions != nil {
		edges = append(edges, maintenanceorder.EdgeInteractions)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *MaintenanceOrderMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case maintenanceorder.EdgeBranch:
		if id := m.branch; id != nil {
			return []ent.Value{*id}
		}
	case maintenanceorder.EdgeEquipment:
		if id := m.equipment; id != nil {
			return []ent.Value{*id}
		}
	case maintenanceorder.EdgeRequester:
		if id := m.requester; id != nil {
			return []ent.Value{*id}
		}
	case maintenanceorder.EdgeApprover:
		if id := m.approver; id != nil {
			return []ent.Value{*id}
		}
	case maintenanceorder.EdgeTechnician:
		if id := m.technician; id != nil {
			return []ent.Value{*id}
		}
	case maintenanceorder.EdgeCostItems:
		ids := make([]ent.Value, 0, len(m.cost_items))
		for id := range m.cost_items {
			ids = append(ids, id)
		}
		return ids
	case maintenanceorder.EdgeInteractions:
		ids := make([]ent.Value, 0, len(m.interactions))
		for id := range m.interactions {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *MaintenanceOrderMutation) RemovedEdges() []string {
	edges := make([]string, 0, 7)
	if m.removedcost_items != nil {
		edges = append(edges, maintenanceorder.EdgeCostItems)
	}
	if m.removedinteractions != nil {
		edges = append(edges, maintenanceorder.EdgeInteractions)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *MaintenanceOrderMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case maintenanceorder.EdgeCostItems:
		ids := make([]ent.Value, 0, len(m.removedcost_items))
		for id := range m.removedcost_items {
			ids = append(ids, id)
		}
		return ids
	case maintenanceorder.EdgeInteractions:
		ids := make([]ent.Value, 0, len(m.removedinteractions))
		for id := range m.removedinteractions {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *MaintenanceOrderMutation) ClearedEdges() []string {
	edges := make([]string, 0, 7)
	if m.clearedbranch {
		edges = append(edges, maintenanceorder.EdgeBranch)
	}
	if m.clearedequipment {
		edges = append(edges, maintenanceorder.EdgeEquipment)
	}
	if m.clearedrequester {
		edges = append(edges, maintenanceorder.EdgeRequester)
	}
	if m.clearedapprover {
		edges = append(edges, maintenanceorder.EdgeApprover)
	}
	if m.clearedtechnician {
		edges = append(edges, maintenanceorder.EdgeTechnician)
	}
	if m.clearedcost_items {
		edges = append(edges, maintenanceorder.EdgeCostItems)
	}
	if m.clearedinteractions {
		edges = append(edges, maintenanceorder.EdgeInteractions)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *MaintenanceOrderMutation) EdgeCleared(name string) bool {
	switch name {
	case maintenanceorder.EdgeBranch:
		return m.clearedbranch
	case maintenanceorder.EdgeEquipment:
		return m.clearedequipment
	case maintenanceorder.EdgeRequester:
		return m.clearedrequester
	case maintenanceorder.EdgeApprover:
		return m.clearedapprover
	case maintenanceorder.EdgeTechnician:
		return m.clearedtechnician
	case maintenanceorder.EdgeCostItems:
		return m.clearedcost_items
	case maintenanceorder.EdgeInteractions:
		return m.clearedinteractions
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *MaintenanceOrderMutation) ClearEdge(name string) error {
	switch name {
	case maintenanceorder.EdgeBranch:
		m.ClearBranch()
		return nil
	case maintenanceorder.EdgeEquipment:
		m.ClearEquipment()
		return nil
	case maintenanceorder.EdgeRequester:
		m.ClearRequester()
		return nil
	case maintenanceorder.EdgeApprover:
		m.ClearApprover()
		return nil
	case maintenanceorder.EdgeTechnician:
		m.ClearTechnician()
		return nil
	}
	return fmt.Errorf("unknown MaintenanceOrder unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *MaintenanceOrderMutation) ResetEdge(name string) error {
	switch name {
	case maintenanceorder.EdgeBranch:
		m.ResetBranch()
		return nil
	case maintenanceorder.EdgeEquipment:
		m.ResetEquipment()
		return nil
	case maintenanceorder.EdgeRequester:
		m.ResetRequester()
		return nil
	case maintenanceorder.EdgeApprover:
		m.ResetApprover()
		return nil
	case maintenanceorder.EdgeTechnician:
		m.ResetTechnician()
		return nil
	case maintenanceorder.EdgeCostItems:
		m.ResetCostItems()
		return nil
	case maintenanceorder.EdgeInteractions:
		m.ResetInteractions()
		return nil
	}
	return fmt.Errorf("unknown MaintenanceOrder edge %s", name)
}

// UserMutation represents an operation that mutates the User nodes in the graph.
type UserMutation struct {
	config
	op                      Op
	typ                     string
	id                      *int
	created_at              *time.Time
	updated_at              *time.Time
	active                  *bool
	name                    *string
	email                   *string
	password                *string
	role                    *string
	clearedFields           map[string]struct{}
	managed_branches        map[int]struct{}
	removedmanaged_branches map[int]struct{}
	clearedmanaged_branches bool
	requested_orders        map[int]struct{}
	removedrequested_orders map[int]struct{}
	clearedrequested_orders bool
	assigned_orders         map[int]struct{}
	removedassigned_orders  map[int]struct{}
	clearedassigned_orders  bool
	approved_orders         map[int]struct{}
	removedapproved_orders  map[int]struct{}
	clearedapproved_orders  bool
	cost_items              map[int]struct{}
	removedcost_items       map[int]struct{}
	clearedcost_items       bool
	interactions            map[int]struct{}
	removedinteractions     map[int]struct{}
	clearedinteractions     bool
	done                    bool
	oldValue                func(context.Context) (*User, error)
	predicates              []predicate.User
}

var _ ent.Mutation = (*UserMutation)(nil)

// userOption allows management of the mutation configuration using functional options.
type userOption func(*UserMutation)

// newUserMutation creates new mutation for the User entity.
func newUserMutation(c config, op Op, opts ...userOption) *UserMutation {
	m := &UserMutation{
		config:        c,
		op:            op,
		typ:           TypeUser,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withUserID sets the ID field of the mutation.
func withUserID(id int) userOption {
	return func(m *UserMutation) {
		var (
			err   error
			once  sync.Once
			value *User
		)
		m.oldValue = func(ctx context.Context) (*User, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().User.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withUser sets the old User of the mutation.
func withUser(node *User) userOption {
	return func(m *UserMutation) {
		m.oldValue = func(context.Context) (*User, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m UserMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m UserMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *UserMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *UserMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().User.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *UserMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *UserMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *UserMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *UserMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *UserMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *UserMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetActive sets the "active" field.
func (m *UserMutation) SetActive(b bool) {
	m.active = &b
}

// Active returns the value of the "active" field in the mutation.
func (m *UserMutation) Active() (r bool, exists bool) {
	v := m.active
	if v == nil {
		return
	}
	return *v, true
}

// OldActive returns the old "active" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldActive(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldActive is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldActive requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldActive: %w", err)
	}
	return oldValue.Active, nil
}

// ResetActive resets all changes to the "active" field.
func (m *UserMutation) ResetActive() {
	m.active = nil
}

// SetName sets the "name" field.
func (m *UserMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *UserMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *UserMutation) ResetName() {
	m.name = nil
}

// SetEmail sets the "email" field.
func (m *UserMutation) SetEmail(s string) {
	m.email = &s
}

// Email returns the value of the "email" field in the mutation.
func (m *UserMutation) Email() (r string, exists bool) {
	v := m.email
	if v == nil {
		return
	}
	return *v, true
}

// OldEmail returns the old "email" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldEmail(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEmail is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEmail requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEmail: %w", err)
	}
	return oldValue.Email, nil
}

// ResetEmail resets all changes to the "email" field.
func (m *UserMutation) ResetEmail() {
	m.email = nil
}

// SetPassword sets the "password" field.
func (m *UserMutation) SetPassword(s string) {
	m.password = &s
}

// Password returns the value of the "password" field in the mutation.
func (m *UserMutation) Password() (r string, exists bool) {
	v := m.password
	if v == nil {
		return
	}
	return *v, true
}

// OldPassword returns the old "password" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldPassword(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPassword is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPassword requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPassword: %w", err)
	}
	return oldValue.Password, nil
}

// ResetPassword resets all changes to the "password" field.
func (m *UserMutation) ResetPassword() {
	m.password = nil
}

// SetRole sets the "role" field.
func (m *UserMutation) SetRole(s string) {
	m.role = &s
}

// Role returns the value of the "role" field in the mutation.
func (m *UserMutation) Role() (r string, exists bool) {
	v := m.role
	if v == nil {
		return
	}
	return *v, true
}

// OldRole returns the old "role" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldRole(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRole is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRole requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRole: %w", err)
	}
	return oldValue.Role, nil
}

// ResetRole resets all changes to the "role" field.
func (m *UserMutation) ResetRole() {
	m.role = nil
}

// AddManagedBranchIDs adds the "managed_branches" edge to the Branch entity by ids.
func (m *UserMutation) AddManagedBranchIDs(ids ...int) {
	if m.managed_branches == nil {
		m.managed_branches = make(map[int]struct{})
	}
	for i := range ids {
		m.managed_branches[ids[i]] = struct{}{}
	}
}

// ClearManagedBranches clears the "managed_branches" edge to the Branch entity.
func (m *UserMutation) ClearManagedBranches() {
	m.clearedmanaged_branches = true
}

// ManagedBranchesCleared reports if the "managed_branches" edge to the Branch entity was cleared.
func (m *UserMutation) ManagedBranchesCleared() bool {
	return m.clearedmanaged_branches
}

// RemoveManagedBranchIDs removes the "managed_branches" edge to the Branch entity by IDs.
func (m *UserMutation) RemoveManagedBranchIDs(ids ...int) {
	if m.removedmanaged_branches == nil {
		m.removedmanaged_branches = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.managed_branches, ids[i])
		m.removedmanaged_branches[ids[i]] = struct{}{}
	}
}

// RemovedManagedBranches returns the removed IDs of the "managed_branches" edge to the Branch entity.
func (m *UserMutation) RemovedManagedBranchesIDs() (ids []int) {
	for id := range m.removedmanaged_branches {
		ids = append(ids, id)
	}
	return
}

// ManagedBranchesIDs returns the "managed_branches" edge IDs in the mutation.
func (m *UserMutation) ManagedBranchesIDs() (ids []int) {
	for id := range m.managed_branches {
		ids = append(ids, id)
	}
	return
}

// ResetManagedBranches resets all changes to the "managed_branches" edge.
func (m *UserMutation) ResetManagedBranches() {
	m.managed_branches = nil
	m.clearedmanaged_branches = false
	m.removedmanaged_branches = nil
}

// AddRequestedOrderIDs adds the "requested_orders" edge to the MaintenanceOrder entity by ids.
func (m *UserMutation) AddRequestedOrderIDs(ids ...int) {
	if m.requested_orders == nil {
		m.requested_orders = make(map[int]struct{})
	}
	for i := range ids {
		m.requested_orders[ids[i]] = struct{}{}
	}
}

// ClearRequestedOrders clears the "requested_orders" edge to the MaintenanceOrder entity.
func (m *UserMutation) ClearRequestedOrders() {
	m.clearedrequested_orders = true
}

// RequestedOrdersCleared reports if the "requested_orders" edge to the MaintenanceOrder entity was cleared.
func (m *UserMutation) RequestedOrdersCleared() bool {
	return m.clearedrequested_orders
}

// RemoveRequestedOrderIDs removes the "requested_orders" edge to the MaintenanceOrder entity by IDs.
func (m *UserMutation) RemoveRequestedOrderIDs(ids ...int) {
	if m.removedrequested_orders == nil {
		m.removedrequested_orders = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.requested_orders, ids[i])
		m.removedrequested_orders[ids[i]] = struct{}{}
	}
}

// RemovedRequestedOrders returns the removed IDs of the "requested_orders" edge to the MaintenanceOrder entity.
func (m *UserMutation) RemovedRequestedOrdersIDs() (ids []int) {
	for id := range m.removedrequested_orders {
		ids = append(ids, id)
	}
	return
}

// RequestedOrdersIDs returns the "requested_orders" edge IDs in the mutation.
func (m *UserMutation) RequestedOrdersIDs() (ids []int) {
	for id := range m.requested_orders {
		ids = append(ids, id)
	}
	return
}

// ResetRequestedOrders resets all changes to the "requested_orders" edge.
func (m *UserMutation) ResetRequestedOrders() {
	m.requested_orders = nil
	m.clearedrequested_orders = false
	m.removedrequested_orders = nil
}

// AddAssignedOrderIDs adds the "assigned_orders" edge to the MaintenanceOrder entity by ids.
func (m *UserMutation) AddAssignedOrderIDs(ids ...int) {
	if m.assigned_orders == nil {
		m.assigned_orders = make(map[int]struct{})
	}
	for i := range ids {
		m.assigned_orders[ids[i]] = struct{}{}
	}
}

// ClearAssignedOrders clears the "assigned_orders" edge to the MaintenanceOrder entity.
func (m *UserMutation) ClearAssignedOrders() {
	m.clearedassigned_orders = true
}

// AssignedOrdersCleared reports if the "assigned_orders" edge to the MaintenanceOrder entity was cleared.
func (m *UserMutation) AssignedOrdersCleared() bool {
	return m.clearedassigned_orders
}

// RemoveAssignedOrderIDs removes the "assigned_orders" edge to the MaintenanceOrder entity by IDs.
func (m *UserMutation) RemoveAssignedOrderIDs(ids ...int) {
	if m.removedassigned_orders == nil {
		m.removedassigned_orders = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.assigned_orders, ids[i])
		m.removedassigned_orders[ids[i]] = struct{}{}
	}
}

// RemovedAssignedOrders returns the removed IDs of the "assigned_orders" edge to the MaintenanceOrder entity.
func (m *UserMutation) RemovedAssignedOrdersIDs() (ids []int) {
	for id := range m.removedassigned_orders {
		ids = append(ids, id)
	}
	return
}

// AssignedOrdersIDs returns the "assigned_orders" edge IDs in the mutation.
func (m *UserMutation) AssignedOrdersIDs() (ids []int) {
	for id := range m.assigned_orders {
		ids = append(ids, id)
	}
	return
}

// ResetAssignedOrders resets all changes to the "assigned_orders" edge.
func (m *UserMutation) ResetAssignedOrders() {
	m.assigned_orders = nil
	m.clearedassigned_orders = false
	m.removedassigned_orders = nil
}

// AddApprovedOrderIDs adds the "approved_orders" edge to the MaintenanceOrder entity by ids.
func (m *UserMutation) AddApprovedOrderIDs(ids ...int) {
	if m.approved_orders == nil {
		m.approved_orders = make(map[int]struct{})
	}
	for i := range ids {
		m.approved_orders[ids[i]] = struct{}{}
	}
}

// ClearApprovedOrders clears the "approved_orders" edge to the MaintenanceOrder entity.
func (m *UserMutation) ClearApprovedOrders() {
	m.clearedapproved_orders = true
}

// ApprovedOrdersCleared reports if the "approved_orders" edge to the MaintenanceOrder entity was cleared.
func (m *UserMutation) ApprovedOrdersCleared() bool {
	return m.clearedapproved_orders
}

// RemoveApprovedOrderIDs removes the "approved_orders" edge to the MaintenanceOrder entity by IDs.
func (m *UserMutation) RemoveApprovedOrderIDs(ids ...int) {
	if m.removedapproved_orders == nil {
		m.removedapproved_orders = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.approved_orders, ids[i])
		m.removedapproved_orders[ids[i]] = struct{}{}
	}
}

// RemovedApprovedOrders returns the removed IDs of the "approved_orders" edge to the MaintenanceOrder entity.
func (m *UserMutation) RemovedApprovedOrdersIDs() (ids []int) {
	for id := range m.removedapproved_orders {
		ids = append(ids, id)
	}
	return
}

// ApprovedOrdersIDs returns the "approved_orders" edge IDs in the mutation.
func (m *UserMutation) ApprovedOrdersIDs() (ids []int) {
	for id := range m.approved_orders {
		ids = append(ids, id)
	}
	return
}

// ResetApprovedOrders resets all changes to the "approved_orders" edge.
func (m *UserMutation) ResetApprovedOrders() {
	m.approved_orders = nil
	m.clearedapproved_orders = false
	m.removedapproved_orders = nil
}

// AddCostItemIDs adds the "cost_items" edge to the CostItem entity by ids.
func (m *UserMutation) AddCostItemIDs(ids ...int) {
	if m.cost_items == nil {
		m.cost_items = make(map[int]struct{})
	}
	for i := range ids {
		m.cost_items[ids[i]] = struct{}{}
	}
}

// ClearCostItems clears the "cost_items" edge to the CostItem entity.
func (m *UserMutation) ClearCostItems() {
	m.clearedcost_items = true
}

// CostItemsCleared reports if the "cost_items" edge to the CostItem entity was cleared.
func (m *UserMutation) CostItemsCleared() bool {
	return m.clearedcost_items
}

// RemoveCostItemIDs removes the "cost_items" edge to the CostItem entity by IDs.
func (m *UserMutation) RemoveCostItemIDs(ids ...int) {
	if m.removedcost_items == nil {
		m.removedcost_items = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.cost_items, ids[i])
		m.removedcost_items[ids[i]] = struct{}{}
	}
}

// RemovedCostItems returns the removed IDs of the "cost_items" edge to the CostItem entity.
func (m *UserMutation) RemovedCostItemsIDs() (ids []int) {
	for id := range m.removedcost_items {
		ids = append(ids, id)
	}
	return
}

// CostItemsIDs returns the "cost_items" edge IDs in the mutation.
func (m *UserMutation) CostItemsIDs() (ids []int) {
	for id := range m.cost_items {
		ids = append(ids, id)
	}
	return
}

// ResetCostItems resets all changes to the "cost_items" edge.
func (m *UserMutation) ResetCostItems() {
	m.cost_items = nil
	m.clearedcost_items = false
	m.removedcost_items = nil
}

// AddInteractionIDs adds the "interactions" edge to the Interaction entity by ids.
func (m *UserMutation) AddInteractionIDs(ids ...int) {
	if m.interactions == nil {
		m.interactions = make(map[int]struct{})
	}
	for i := range ids {
		m.interactions[ids[i]] = struct{}{}
	}
}

// ClearInteractions clears the "interactions" edge to the Interaction entity.
func (m *UserMutation) ClearInteractions() {
	m.clearedinteractions = true
}

// InteractionsCleared reports if the "interactions" edge to the Interaction entity was cleared.
func (m *UserMutation) InteractionsCleared() bool {
	return m.clearedinteractions
}

// RemoveInteractionIDs removes the "interactions" edge to the Interaction entity by IDs.
func (m *UserMutation) RemoveInteractionIDs(ids ...int) {
	if m.removedinteractions == nil {
		m.removedinteractions = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.interactions, ids[i])
		m.removedinteractions[ids[i]] = struct{}{}
	}
}

// RemovedInteractions returns the removed IDs of the "interactions" edge to the Interaction entity.
func (m *UserMutation) RemovedInteractionsIDs() (ids []int) {
	for id := range m.removedinteractions {
		ids = append(ids, id)
	}
	return
}

// InteractionsIDs returns the "interactions" edge IDs in the mutation.
func (m *UserMutation) InteractionsIDs() (ids []int) {
	for id := range m.interactions {
		ids = append(ids, id)
	}
	return
}

// ResetInteractions resets all changes to the "interactions" edge.
func (m *UserMutation) ResetInteractions() {
	m.interactions = nil
	m.clearedinteractions = false
	m.removedinteractions = nil
}

// Where appends a list predicates to the UserMutation builder.
func (m *UserMutation) Where(ps ...predicate.User) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the UserMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *UserMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.User, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *UserMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *UserMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (User).
func (m *UserMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *UserMutation) Fields() []string {
	fields := make([]string, 0, 7)
	if m.created_at != nil {
		fields = append(fields, user.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, user.FieldUpdatedAt)
	}
	if m.active != nil {
		fields = append(fields, user.FieldActive)
	}
	if m.name != nil {
		fields = append(fields, user.FieldName)
	}
	if m.email != nil {
		fields = append(fields, user.FieldEmail)
	}
	if m.password != nil {
		fields = append(fields, user.FieldPassword)
	}
	if m.role != nil {
		fields = append(fields, user.FieldRole)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *UserMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case user.FieldCreatedAt:
		return m.CreatedAt()
	case user.FieldUpdatedAt:
		return m.UpdatedAt()
	case user.FieldActive:
		return m.Active()
	case user.FieldName:
		return m.Name()
	case user.FieldEmail:
		return m.Email()
	case user.FieldPassword:
		return m.Password()
	case user.FieldRole:
		return m.Role()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *UserMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case user.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case user.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case user.FieldActive:
		return m.OldActive(ctx)
	case user.FieldName:
		return m.OldName(ctx)
	case user.FieldEmail:
		return m.OldEmail(ctx)
	case user.FieldPassword:
		return m.OldPassword(ctx)
	case user.FieldRole:
		return m.OldRole(ctx)
	}
	return nil, fmt.Errorf("unknown User field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UserMutation) SetField(name string, value ent.Value) error {
	switch name {
	case user.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case user.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case user.FieldActive:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetActive(v)
		return nil
	case user.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case user.FieldEmail:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEmail(v)
		return nil
	case user.FieldPassword:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPassword(v)
		return nil
	case user.FieldRole:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRole(v)
		return nil
	}
	return fmt.Errorf("unknown User field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *UserMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *UserMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UserMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown User numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *UserMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *UserMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *UserMutation) ClearField(name string) error {
	return fmt.Errorf("unknown User nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *UserMutation) ResetField(name string) error {
	switch name {
	case user.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case user.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case user.FieldActive:
		m.ResetActive()
		return nil
	case user.FieldName:
		m.ResetName()
		return nil
	case user.FieldEmail:
		m.ResetEmail()
		return nil
	case user.FieldPassword:
		m.ResetPassword()
		return nil
	case user.FieldRole:
		m.ResetRole()
		return nil
	}
	return fmt.Errorf("unknown User field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *UserMutation) AddedEdges() []string {
	edges := make([]string, 0, 6)
	if m.managed_branches != nil {
		edges = append(edges, user.EdgeManagedBranches)
	}
	if m.requested_orders != nil {
		edges = append(edges, user.EdgeRequestedOrders)
	}
	if m.assigned_orders != nil {
		edges = append(edges, user.EdgeAssignedOrders)
	}
	if m.approved_orders != nil {
		edges = append(edges, user.EdgeApprovedOrders)
	}
	if m.cost_items != nil {
		edges = append(edges, user.EdgeCostItems)
	}
	if m.interactions != nil {
		edges = append(edges, user.EdgeInteractions)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *UserMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case user.EdgeManagedBranches:
		ids := make([]ent.Value, 0, len(m.managed_branches))
		for id := range m.managed_branches {
			ids = append(ids, id)
		}
		return ids
	case user.EdgeRequestedOrders:
		ids := make([]ent.Value, 0, len(m.requested_orders))
		for id := range m.requested_orders {
			ids = append(ids, id)
		}
		return ids
	case user.EdgeAssignedOrders:
		ids := make([]ent.Value, 0, len(m.assigned_orders))
		for id := range m.assigned_orders {
			ids = append(ids, id)
		}
		return ids
	case user.EdgeApprovedOrders:
		ids := make([]ent.Value, 0, len(m.approved_orders))
		for id := range m.approved_orders {
			ids = append(ids, id)
		}
		return ids
	case user.EdgeCostItems:
		ids := make([]ent.Value, 0, len(m.cost_items))
		for id := range m.cost_items {
			ids = append(ids, id)
		}
		return ids
	case user.EdgeInteractions:
		ids := make([]ent.Value, 0, len(m.interactions))
		for id := range m.interactions {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *UserMutation) RemovedEdges() []string {
	edges := make([]string, 0, 6)
	if m.removedmanaged_branches != nil {
		edges = append(edges, user.EdgeManagedBranches)
	}
	if m.removedrequested_orders != nil {
		edges = append(edges, user.EdgeRequestedOrders)
	}
	if m.removedassigned_orders != nil {
		edges = append(edges, user.EdgeAssignedOrders)
	}
	if m.removedapproved_orders != nil {
		edges = append(edges, user.EdgeApprovedOrders)
	}
	if m.removedcost_items != nil {
		edges = append(edges, user.EdgeCostItems)
	}
	if m.removedinteractions != nil {
		edges = append(edges, user.EdgeInteractions)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *UserMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case user.EdgeManagedBranches:
		ids := make([]ent.Value, 0, len(m.removedmanaged_branches))
		for id := range m.removedmanaged_branches {
			ids = append(ids, id)
		}
		return ids
	case user.EdgeRequestedOrders:
		ids := make([]ent.Value, 0, len(m.removedrequested_orders))
		for id := range m.removedrequested_orders {
			ids = append(ids, id)
		}
		return ids
	case user.EdgeAssignedOrders:
		ids := make([]ent.Value, 0, len(m.removedassigned_orders))
		for id := range m.removedassigned_orders {
			ids = append(ids, id)
		}
		return ids
	case user.EdgeApprovedOrders:
		ids := make([]ent.Value, 0, len(m.removedapproved_orders))
		for id := range m.removedapproved_orders {
			ids = append(ids, id)
		}
		return ids
	case user.EdgeCostItems:
		ids := make([]ent.Value, 0, len(m.removedcost_items))
		for id := range m.removedcost_items {
			ids = append(ids, id)
		}
		return ids
	case user.EdgeInteractions:
		ids := make([]ent.Value, 0, len(m.removedinteractions))
		for id := range m.removedinteractions {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *UserMutation) ClearedEdges() []string {
	edges := make([]string, 0, 6)
	if m.clearedmanaged_branches {
		edges = append(edges, user.EdgeManagedBranches)
	}
	if m.clearedrequested_orders {
		edges = append(edges, user.EdgeRequestedOrders)
	}
	if m.clearedassigned_orders {
		edges = append(edges, user.EdgeAssignedOrders)
	}
	if m.clearedapproved_orders {
		edges = append(edges, user.EdgeApprovedOrders)
	}
	if m.clearedcost_items {
		edges = append(edges, user.EdgeCostItems)
	}
	if m.clearedinteractions {
		edges = append(edges, user.EdgeInteractions)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *UserMutation) EdgeCleared(name string) bool {
	switch name {
	case user.EdgeManagedBranches:
		return m.clearedmanaged_branches
	case user.EdgeRequestedOrders:
		return m.clearedrequested_orders
	case user.EdgeAssignedOrders:
		return m.clearedassigned_orders
	case user.EdgeApprovedOrders:
		return m.clearedapproved_orders
	case user.EdgeCostItems:
		return m.clearedcost_items
	case user.EdgeInteractions:
		return m.clearedinteractions
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *UserMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown User unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *UserMutation) ResetEdge(name string) error {
	switch name {
	case user.EdgeManagedBranches:
		m.ResetManagedBranches()
		return nil
	case user.EdgeRequestedOrders:
		m.ResetRequestedOrders()
		return nil
	case user.EdgeAssignedOrders:
		m.ResetAssignedOrders()
		return nil
	case user.EdgeApprovedOrders:
		m.ResetApprovedOrders()
		return nil
	case user.EdgeCostItems:
		m.ResetCostItems()
		return nil
	case user.EdgeInteractions:
		m.ResetInteractions()
		return nil
	}
	return fmt.Errorf("unknown User edge %s", name)
}
