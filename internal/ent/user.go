// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"
	"tradicao/internal/ent/user"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// User is the model entity for the User schema.
type User struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// Active holds the value of the "active" field.
	Active bool `json:"active,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// Email holds the value of the "email" field.
	Email string `json:"email,omitempty"`
	// Password holds the value of the "password" field.
	Password string `json:"password,omitempty"`
	// Role holds the value of the "role" field.
	Role string `json:"role,omitempty"`
	// <PERSON>s holds the relations/edges for other nodes in the graph.
	// The values are being populated by the UserQuery when eager-loading is set.
	Edges        UserEdges `json:"edges"`
	selectValues sql.SelectValues
}

// UserEdges holds the relations/edges for other nodes in the graph.
type UserEdges struct {
	// ManagedBranches holds the value of the managed_branches edge.
	ManagedBranches []*Branch `json:"managed_branches,omitempty"`
	// RequestedOrders holds the value of the requested_orders edge.
	RequestedOrders []*MaintenanceOrder `json:"requested_orders,omitempty"`
	// AssignedOrders holds the value of the assigned_orders edge.
	AssignedOrders []*MaintenanceOrder `json:"assigned_orders,omitempty"`
	// ApprovedOrders holds the value of the approved_orders edge.
	ApprovedOrders []*MaintenanceOrder `json:"approved_orders,omitempty"`
	// CostItems holds the value of the cost_items edge.
	CostItems []*CostItem `json:"cost_items,omitempty"`
	// Interactions holds the value of the interactions edge.
	Interactions []*Interaction `json:"interactions,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [6]bool
}

// ManagedBranchesOrErr returns the ManagedBranches value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) ManagedBranchesOrErr() ([]*Branch, error) {
	if e.loadedTypes[0] {
		return e.ManagedBranches, nil
	}
	return nil, &NotLoadedError{edge: "managed_branches"}
}

// RequestedOrdersOrErr returns the RequestedOrders value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) RequestedOrdersOrErr() ([]*MaintenanceOrder, error) {
	if e.loadedTypes[1] {
		return e.RequestedOrders, nil
	}
	return nil, &NotLoadedError{edge: "requested_orders"}
}

// AssignedOrdersOrErr returns the AssignedOrders value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) AssignedOrdersOrErr() ([]*MaintenanceOrder, error) {
	if e.loadedTypes[2] {
		return e.AssignedOrders, nil
	}
	return nil, &NotLoadedError{edge: "assigned_orders"}
}

// ApprovedOrdersOrErr returns the ApprovedOrders value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) ApprovedOrdersOrErr() ([]*MaintenanceOrder, error) {
	if e.loadedTypes[3] {
		return e.ApprovedOrders, nil
	}
	return nil, &NotLoadedError{edge: "approved_orders"}
}

// CostItemsOrErr returns the CostItems value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) CostItemsOrErr() ([]*CostItem, error) {
	if e.loadedTypes[4] {
		return e.CostItems, nil
	}
	return nil, &NotLoadedError{edge: "cost_items"}
}

// InteractionsOrErr returns the Interactions value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) InteractionsOrErr() ([]*Interaction, error) {
	if e.loadedTypes[5] {
		return e.Interactions, nil
	}
	return nil, &NotLoadedError{edge: "interactions"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*User) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case user.FieldActive:
			values[i] = new(sql.NullBool)
		case user.FieldID:
			values[i] = new(sql.NullInt64)
		case user.FieldName, user.FieldEmail, user.FieldPassword, user.FieldRole:
			values[i] = new(sql.NullString)
		case user.FieldCreatedAt, user.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the User fields.
func (u *User) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case user.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			u.ID = int(value.Int64)
		case user.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				u.CreatedAt = value.Time
			}
		case user.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				u.UpdatedAt = value.Time
			}
		case user.FieldActive:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field active", values[i])
			} else if value.Valid {
				u.Active = value.Bool
			}
		case user.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				u.Name = value.String
			}
		case user.FieldEmail:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field email", values[i])
			} else if value.Valid {
				u.Email = value.String
			}
		case user.FieldPassword:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field password", values[i])
			} else if value.Valid {
				u.Password = value.String
			}
		case user.FieldRole:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field role", values[i])
			} else if value.Valid {
				u.Role = value.String
			}
		default:
			u.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the User.
// This includes values selected through modifiers, order, etc.
func (u *User) Value(name string) (ent.Value, error) {
	return u.selectValues.Get(name)
}

// QueryManagedBranches queries the "managed_branches" edge of the User entity.
func (u *User) QueryManagedBranches() *BranchQuery {
	return NewUserClient(u.config).QueryManagedBranches(u)
}

// QueryRequestedOrders queries the "requested_orders" edge of the User entity.
func (u *User) QueryRequestedOrders() *MaintenanceOrderQuery {
	return NewUserClient(u.config).QueryRequestedOrders(u)
}

// QueryAssignedOrders queries the "assigned_orders" edge of the User entity.
func (u *User) QueryAssignedOrders() *MaintenanceOrderQuery {
	return NewUserClient(u.config).QueryAssignedOrders(u)
}

// QueryApprovedOrders queries the "approved_orders" edge of the User entity.
func (u *User) QueryApprovedOrders() *MaintenanceOrderQuery {
	return NewUserClient(u.config).QueryApprovedOrders(u)
}

// QueryCostItems queries the "cost_items" edge of the User entity.
func (u *User) QueryCostItems() *CostItemQuery {
	return NewUserClient(u.config).QueryCostItems(u)
}

// QueryInteractions queries the "interactions" edge of the User entity.
func (u *User) QueryInteractions() *InteractionQuery {
	return NewUserClient(u.config).QueryInteractions(u)
}

// Update returns a builder for updating this User.
// Note that you need to call User.Unwrap() before calling this method if this User
// was returned from a transaction, and the transaction was committed or rolled back.
func (u *User) Update() *UserUpdateOne {
	return NewUserClient(u.config).UpdateOne(u)
}

// Unwrap unwraps the User entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (u *User) Unwrap() *User {
	_tx, ok := u.config.driver.(*txDriver)
	if !ok {
		panic("ent: User is not a transactional entity")
	}
	u.config.driver = _tx.drv
	return u
}

// String implements the fmt.Stringer.
func (u *User) String() string {
	var builder strings.Builder
	builder.WriteString("User(")
	builder.WriteString(fmt.Sprintf("id=%v, ", u.ID))
	builder.WriteString("created_at=")
	builder.WriteString(u.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(u.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("active=")
	builder.WriteString(fmt.Sprintf("%v", u.Active))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(u.Name)
	builder.WriteString(", ")
	builder.WriteString("email=")
	builder.WriteString(u.Email)
	builder.WriteString(", ")
	builder.WriteString("password=")
	builder.WriteString(u.Password)
	builder.WriteString(", ")
	builder.WriteString("role=")
	builder.WriteString(u.Role)
	builder.WriteByte(')')
	return builder.String()
}

// Users is a parsable slice of User.
type Users []*User
