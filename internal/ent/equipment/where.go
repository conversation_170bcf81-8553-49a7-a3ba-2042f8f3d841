// Code generated by ent, DO NOT EDIT.

package equipment

import (
	"time"
	"tradicao/internal/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.Equipment {
	return predicate.Equipment(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.Equipment {
	return predicate.Equipment(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.Equipment {
	return predicate.Equipment(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.Equipment {
	return predicate.Equipment(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.Equipment {
	return predicate.Equipment(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.Equipment {
	return predicate.Equipment(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.Equipment {
	return predicate.Equipment(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldUpdatedAt, v))
}

// Active applies equality check predicate on the "active" field. It's identical to ActiveEQ.
func Active(v bool) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldActive, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldName, v))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldType, v))
}

// Model applies equality check predicate on the "model" field. It's identical to ModelEQ.
func Model(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldModel, v))
}

// SerialNumber applies equality check predicate on the "serial_number" field. It's identical to SerialNumberEQ.
func SerialNumber(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldSerialNumber, v))
}

// InstallationDate applies equality check predicate on the "installation_date" field. It's identical to InstallationDateEQ.
func InstallationDate(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldInstallationDate, v))
}

// LastMaintenanceDate applies equality check predicate on the "last_maintenance_date" field. It's identical to LastMaintenanceDateEQ.
func LastMaintenanceDate(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldLastMaintenanceDate, v))
}

// BranchID applies equality check predicate on the "branch_id" field. It's identical to BranchIDEQ.
func BranchID(v int) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldBranchID, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldLTE(FieldUpdatedAt, v))
}

// ActiveEQ applies the EQ predicate on the "active" field.
func ActiveEQ(v bool) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldActive, v))
}

// ActiveNEQ applies the NEQ predicate on the "active" field.
func ActiveNEQ(v bool) predicate.Equipment {
	return predicate.Equipment(sql.FieldNEQ(FieldActive, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Equipment {
	return predicate.Equipment(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Equipment {
	return predicate.Equipment(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldContainsFold(FieldName, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...string) predicate.Equipment {
	return predicate.Equipment(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...string) predicate.Equipment {
	return predicate.Equipment(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldLTE(FieldType, v))
}

// TypeContains applies the Contains predicate on the "type" field.
func TypeContains(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldContains(FieldType, v))
}

// TypeHasPrefix applies the HasPrefix predicate on the "type" field.
func TypeHasPrefix(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldHasPrefix(FieldType, v))
}

// TypeHasSuffix applies the HasSuffix predicate on the "type" field.
func TypeHasSuffix(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldHasSuffix(FieldType, v))
}

// TypeEqualFold applies the EqualFold predicate on the "type" field.
func TypeEqualFold(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldEqualFold(FieldType, v))
}

// TypeContainsFold applies the ContainsFold predicate on the "type" field.
func TypeContainsFold(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldContainsFold(FieldType, v))
}

// ModelEQ applies the EQ predicate on the "model" field.
func ModelEQ(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldModel, v))
}

// ModelNEQ applies the NEQ predicate on the "model" field.
func ModelNEQ(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldNEQ(FieldModel, v))
}

// ModelIn applies the In predicate on the "model" field.
func ModelIn(vs ...string) predicate.Equipment {
	return predicate.Equipment(sql.FieldIn(FieldModel, vs...))
}

// ModelNotIn applies the NotIn predicate on the "model" field.
func ModelNotIn(vs ...string) predicate.Equipment {
	return predicate.Equipment(sql.FieldNotIn(FieldModel, vs...))
}

// ModelGT applies the GT predicate on the "model" field.
func ModelGT(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldGT(FieldModel, v))
}

// ModelGTE applies the GTE predicate on the "model" field.
func ModelGTE(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldGTE(FieldModel, v))
}

// ModelLT applies the LT predicate on the "model" field.
func ModelLT(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldLT(FieldModel, v))
}

// ModelLTE applies the LTE predicate on the "model" field.
func ModelLTE(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldLTE(FieldModel, v))
}

// ModelContains applies the Contains predicate on the "model" field.
func ModelContains(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldContains(FieldModel, v))
}

// ModelHasPrefix applies the HasPrefix predicate on the "model" field.
func ModelHasPrefix(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldHasPrefix(FieldModel, v))
}

// ModelHasSuffix applies the HasSuffix predicate on the "model" field.
func ModelHasSuffix(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldHasSuffix(FieldModel, v))
}

// ModelEqualFold applies the EqualFold predicate on the "model" field.
func ModelEqualFold(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldEqualFold(FieldModel, v))
}

// ModelContainsFold applies the ContainsFold predicate on the "model" field.
func ModelContainsFold(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldContainsFold(FieldModel, v))
}

// SerialNumberEQ applies the EQ predicate on the "serial_number" field.
func SerialNumberEQ(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldSerialNumber, v))
}

// SerialNumberNEQ applies the NEQ predicate on the "serial_number" field.
func SerialNumberNEQ(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldNEQ(FieldSerialNumber, v))
}

// SerialNumberIn applies the In predicate on the "serial_number" field.
func SerialNumberIn(vs ...string) predicate.Equipment {
	return predicate.Equipment(sql.FieldIn(FieldSerialNumber, vs...))
}

// SerialNumberNotIn applies the NotIn predicate on the "serial_number" field.
func SerialNumberNotIn(vs ...string) predicate.Equipment {
	return predicate.Equipment(sql.FieldNotIn(FieldSerialNumber, vs...))
}

// SerialNumberGT applies the GT predicate on the "serial_number" field.
func SerialNumberGT(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldGT(FieldSerialNumber, v))
}

// SerialNumberGTE applies the GTE predicate on the "serial_number" field.
func SerialNumberGTE(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldGTE(FieldSerialNumber, v))
}

// SerialNumberLT applies the LT predicate on the "serial_number" field.
func SerialNumberLT(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldLT(FieldSerialNumber, v))
}

// SerialNumberLTE applies the LTE predicate on the "serial_number" field.
func SerialNumberLTE(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldLTE(FieldSerialNumber, v))
}

// SerialNumberContains applies the Contains predicate on the "serial_number" field.
func SerialNumberContains(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldContains(FieldSerialNumber, v))
}

// SerialNumberHasPrefix applies the HasPrefix predicate on the "serial_number" field.
func SerialNumberHasPrefix(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldHasPrefix(FieldSerialNumber, v))
}

// SerialNumberHasSuffix applies the HasSuffix predicate on the "serial_number" field.
func SerialNumberHasSuffix(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldHasSuffix(FieldSerialNumber, v))
}

// SerialNumberEqualFold applies the EqualFold predicate on the "serial_number" field.
func SerialNumberEqualFold(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldEqualFold(FieldSerialNumber, v))
}

// SerialNumberContainsFold applies the ContainsFold predicate on the "serial_number" field.
func SerialNumberContainsFold(v string) predicate.Equipment {
	return predicate.Equipment(sql.FieldContainsFold(FieldSerialNumber, v))
}

// InstallationDateEQ applies the EQ predicate on the "installation_date" field.
func InstallationDateEQ(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldInstallationDate, v))
}

// InstallationDateNEQ applies the NEQ predicate on the "installation_date" field.
func InstallationDateNEQ(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldNEQ(FieldInstallationDate, v))
}

// InstallationDateIn applies the In predicate on the "installation_date" field.
func InstallationDateIn(vs ...time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldIn(FieldInstallationDate, vs...))
}

// InstallationDateNotIn applies the NotIn predicate on the "installation_date" field.
func InstallationDateNotIn(vs ...time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldNotIn(FieldInstallationDate, vs...))
}

// InstallationDateGT applies the GT predicate on the "installation_date" field.
func InstallationDateGT(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldGT(FieldInstallationDate, v))
}

// InstallationDateGTE applies the GTE predicate on the "installation_date" field.
func InstallationDateGTE(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldGTE(FieldInstallationDate, v))
}

// InstallationDateLT applies the LT predicate on the "installation_date" field.
func InstallationDateLT(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldLT(FieldInstallationDate, v))
}

// InstallationDateLTE applies the LTE predicate on the "installation_date" field.
func InstallationDateLTE(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldLTE(FieldInstallationDate, v))
}

// InstallationDateIsNil applies the IsNil predicate on the "installation_date" field.
func InstallationDateIsNil() predicate.Equipment {
	return predicate.Equipment(sql.FieldIsNull(FieldInstallationDate))
}

// InstallationDateNotNil applies the NotNil predicate on the "installation_date" field.
func InstallationDateNotNil() predicate.Equipment {
	return predicate.Equipment(sql.FieldNotNull(FieldInstallationDate))
}

// LastMaintenanceDateEQ applies the EQ predicate on the "last_maintenance_date" field.
func LastMaintenanceDateEQ(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldLastMaintenanceDate, v))
}

// LastMaintenanceDateNEQ applies the NEQ predicate on the "last_maintenance_date" field.
func LastMaintenanceDateNEQ(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldNEQ(FieldLastMaintenanceDate, v))
}

// LastMaintenanceDateIn applies the In predicate on the "last_maintenance_date" field.
func LastMaintenanceDateIn(vs ...time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldIn(FieldLastMaintenanceDate, vs...))
}

// LastMaintenanceDateNotIn applies the NotIn predicate on the "last_maintenance_date" field.
func LastMaintenanceDateNotIn(vs ...time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldNotIn(FieldLastMaintenanceDate, vs...))
}

// LastMaintenanceDateGT applies the GT predicate on the "last_maintenance_date" field.
func LastMaintenanceDateGT(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldGT(FieldLastMaintenanceDate, v))
}

// LastMaintenanceDateGTE applies the GTE predicate on the "last_maintenance_date" field.
func LastMaintenanceDateGTE(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldGTE(FieldLastMaintenanceDate, v))
}

// LastMaintenanceDateLT applies the LT predicate on the "last_maintenance_date" field.
func LastMaintenanceDateLT(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldLT(FieldLastMaintenanceDate, v))
}

// LastMaintenanceDateLTE applies the LTE predicate on the "last_maintenance_date" field.
func LastMaintenanceDateLTE(v time.Time) predicate.Equipment {
	return predicate.Equipment(sql.FieldLTE(FieldLastMaintenanceDate, v))
}

// LastMaintenanceDateIsNil applies the IsNil predicate on the "last_maintenance_date" field.
func LastMaintenanceDateIsNil() predicate.Equipment {
	return predicate.Equipment(sql.FieldIsNull(FieldLastMaintenanceDate))
}

// LastMaintenanceDateNotNil applies the NotNil predicate on the "last_maintenance_date" field.
func LastMaintenanceDateNotNil() predicate.Equipment {
	return predicate.Equipment(sql.FieldNotNull(FieldLastMaintenanceDate))
}

// BranchIDEQ applies the EQ predicate on the "branch_id" field.
func BranchIDEQ(v int) predicate.Equipment {
	return predicate.Equipment(sql.FieldEQ(FieldBranchID, v))
}

// BranchIDNEQ applies the NEQ predicate on the "branch_id" field.
func BranchIDNEQ(v int) predicate.Equipment {
	return predicate.Equipment(sql.FieldNEQ(FieldBranchID, v))
}

// BranchIDIn applies the In predicate on the "branch_id" field.
func BranchIDIn(vs ...int) predicate.Equipment {
	return predicate.Equipment(sql.FieldIn(FieldBranchID, vs...))
}

// BranchIDNotIn applies the NotIn predicate on the "branch_id" field.
func BranchIDNotIn(vs ...int) predicate.Equipment {
	return predicate.Equipment(sql.FieldNotIn(FieldBranchID, vs...))
}

// BranchIDIsNil applies the IsNil predicate on the "branch_id" field.
func BranchIDIsNil() predicate.Equipment {
	return predicate.Equipment(sql.FieldIsNull(FieldBranchID))
}

// BranchIDNotNil applies the NotNil predicate on the "branch_id" field.
func BranchIDNotNil() predicate.Equipment {
	return predicate.Equipment(sql.FieldNotNull(FieldBranchID))
}

// HasBranch applies the HasEdge predicate on the "branch" edge.
func HasBranch() predicate.Equipment {
	return predicate.Equipment(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, BranchTable, BranchColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasBranchWith applies the HasEdge predicate on the "branch" edge with a given conditions (other predicates).
func HasBranchWith(preds ...predicate.Branch) predicate.Equipment {
	return predicate.Equipment(func(s *sql.Selector) {
		step := newBranchStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasMaintenanceOrders applies the HasEdge predicate on the "maintenance_orders" edge.
func HasMaintenanceOrders() predicate.Equipment {
	return predicate.Equipment(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, MaintenanceOrdersTable, MaintenanceOrdersColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasMaintenanceOrdersWith applies the HasEdge predicate on the "maintenance_orders" edge with a given conditions (other predicates).
func HasMaintenanceOrdersWith(preds ...predicate.MaintenanceOrder) predicate.Equipment {
	return predicate.Equipment(func(s *sql.Selector) {
		step := newMaintenanceOrdersStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Equipment) predicate.Equipment {
	return predicate.Equipment(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Equipment) predicate.Equipment {
	return predicate.Equipment(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Equipment) predicate.Equipment {
	return predicate.Equipment(sql.NotPredicates(p))
}
