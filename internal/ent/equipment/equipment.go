// Code generated by ent, DO NOT EDIT.

package equipment

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the equipment type in the database.
	Label = "equipment"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldActive holds the string denoting the active field in the database.
	FieldActive = "active"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldModel holds the string denoting the model field in the database.
	FieldModel = "model"
	// FieldSerialNumber holds the string denoting the serial_number field in the database.
	FieldSerialNumber = "serial_number"
	// FieldInstallationDate holds the string denoting the installation_date field in the database.
	FieldInstallationDate = "installation_date"
	// FieldLastMaintenanceDate holds the string denoting the last_maintenance_date field in the database.
	FieldLastMaintenanceDate = "last_maintenance_date"
	// FieldBranchID holds the string denoting the branch_id field in the database.
	FieldBranchID = "branch_id"
	// EdgeBranch holds the string denoting the branch edge name in mutations.
	EdgeBranch = "branch"
	// EdgeMaintenanceOrders holds the string denoting the maintenance_orders edge name in mutations.
	EdgeMaintenanceOrders = "maintenance_orders"
	// Table holds the table name of the equipment in the database.
	Table = "equipment"
	// BranchTable is the table that holds the branch relation/edge.
	BranchTable = "equipment"
	// BranchInverseTable is the table name for the Branch entity.
	// It exists in this package in order to avoid circular dependency with the "branch" package.
	BranchInverseTable = "branches"
	// BranchColumn is the table column denoting the branch relation/edge.
	BranchColumn = "branch_id"
	// MaintenanceOrdersTable is the table that holds the maintenance_orders relation/edge.
	MaintenanceOrdersTable = "maintenance_orders"
	// MaintenanceOrdersInverseTable is the table name for the MaintenanceOrder entity.
	// It exists in this package in order to avoid circular dependency with the "maintenanceorder" package.
	MaintenanceOrdersInverseTable = "maintenance_orders"
	// MaintenanceOrdersColumn is the table column denoting the maintenance_orders relation/edge.
	MaintenanceOrdersColumn = "equipment_id"
)

// Columns holds all SQL columns for equipment fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldActive,
	FieldName,
	FieldType,
	FieldModel,
	FieldSerialNumber,
	FieldInstallationDate,
	FieldLastMaintenanceDate,
	FieldBranchID,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultActive holds the default value on creation for the "active" field.
	DefaultActive bool
	// NameValidator is a validator for the "name" field. It is called by the builders before save.
	NameValidator func(string) error
	// TypeValidator is a validator for the "type" field. It is called by the builders before save.
	TypeValidator func(string) error
	// ModelValidator is a validator for the "model" field. It is called by the builders before save.
	ModelValidator func(string) error
	// SerialNumberValidator is a validator for the "serial_number" field. It is called by the builders before save.
	SerialNumberValidator func(string) error
)

// OrderOption defines the ordering options for the Equipment queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByActive orders the results by the active field.
func ByActive(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldActive, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByModel orders the results by the model field.
func ByModel(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldModel, opts...).ToFunc()
}

// BySerialNumber orders the results by the serial_number field.
func BySerialNumber(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSerialNumber, opts...).ToFunc()
}

// ByInstallationDate orders the results by the installation_date field.
func ByInstallationDate(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInstallationDate, opts...).ToFunc()
}

// ByLastMaintenanceDate orders the results by the last_maintenance_date field.
func ByLastMaintenanceDate(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLastMaintenanceDate, opts...).ToFunc()
}

// ByBranchID orders the results by the branch_id field.
func ByBranchID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBranchID, opts...).ToFunc()
}

// ByBranchField orders the results by branch field.
func ByBranchField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newBranchStep(), sql.OrderByField(field, opts...))
	}
}

// ByMaintenanceOrdersCount orders the results by maintenance_orders count.
func ByMaintenanceOrdersCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newMaintenanceOrdersStep(), opts...)
	}
}

// ByMaintenanceOrders orders the results by maintenance_orders terms.
func ByMaintenanceOrders(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newMaintenanceOrdersStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newBranchStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(BranchInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, BranchTable, BranchColumn),
	)
}
func newMaintenanceOrdersStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(MaintenanceOrdersInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, MaintenanceOrdersTable, MaintenanceOrdersColumn),
	)
}
