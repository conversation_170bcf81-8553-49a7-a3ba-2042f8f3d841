// Code generated by ent, DO NOT EDIT.

package ent

import (
	"time"
	"tradicao/internal/ent/branch"
	"tradicao/internal/ent/costitem"
	"tradicao/internal/ent/equipment"
	"tradicao/internal/ent/interaction"
	"tradicao/internal/ent/maintenanceorder"
	"tradicao/internal/ent/schema"
	"tradicao/internal/ent/user"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	branchMixin := schema.Branch{}.Mixin()
	branchMixinFields0 := branchMixin[0].Fields()
	_ = branchMixinFields0
	branchFields := schema.Branch{}.Fields()
	_ = branchFields
	// branchDescCreatedAt is the schema descriptor for created_at field.
	branchDescCreatedAt := branchMixinFields0[0].Descriptor()
	// branch.DefaultCreatedAt holds the default value on creation for the created_at field.
	branch.DefaultCreatedAt = branchDescCreatedAt.Default.(func() time.Time)
	// branchDescUpdatedAt is the schema descriptor for updated_at field.
	branchDescUpdatedAt := branchMixinFields0[1].Descriptor()
	// branch.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	branch.DefaultUpdatedAt = branchDescUpdatedAt.Default.(func() time.Time)
	// branch.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	branch.UpdateDefaultUpdatedAt = branchDescUpdatedAt.UpdateDefault.(func() time.Time)
	// branchDescActive is the schema descriptor for active field.
	branchDescActive := branchMixinFields0[2].Descriptor()
	// branch.DefaultActive holds the default value on creation for the active field.
	branch.DefaultActive = branchDescActive.Default.(bool)
	// branchDescName is the schema descriptor for name field.
	branchDescName := branchFields[0].Descriptor()
	// branch.NameValidator is a validator for the "name" field. It is called by the builders before save.
	branch.NameValidator = branchDescName.Validators[0].(func(string) error)
	// branchDescCode is the schema descriptor for code field.
	branchDescCode := branchFields[1].Descriptor()
	// branch.CodeValidator is a validator for the "code" field. It is called by the builders before save.
	branch.CodeValidator = branchDescCode.Validators[0].(func(string) error)
	// branchDescAddress is the schema descriptor for address field.
	branchDescAddress := branchFields[2].Descriptor()
	// branch.AddressValidator is a validator for the "address" field. It is called by the builders before save.
	branch.AddressValidator = branchDescAddress.Validators[0].(func(string) error)
	// branchDescCity is the schema descriptor for city field.
	branchDescCity := branchFields[3].Descriptor()
	// branch.CityValidator is a validator for the "city" field. It is called by the builders before save.
	branch.CityValidator = branchDescCity.Validators[0].(func(string) error)
	// branchDescState is the schema descriptor for state field.
	branchDescState := branchFields[4].Descriptor()
	// branch.StateValidator is a validator for the "state" field. It is called by the builders before save.
	branch.StateValidator = branchDescState.Validators[0].(func(string) error)
	// branchDescZipCode is the schema descriptor for zip_code field.
	branchDescZipCode := branchFields[5].Descriptor()
	// branch.ZipCodeValidator is a validator for the "zip_code" field. It is called by the builders before save.
	branch.ZipCodeValidator = branchDescZipCode.Validators[0].(func(string) error)
	// branchDescPhone is the schema descriptor for phone field.
	branchDescPhone := branchFields[6].Descriptor()
	// branch.PhoneValidator is a validator for the "phone" field. It is called by the builders before save.
	branch.PhoneValidator = branchDescPhone.Validators[0].(func(string) error)
	// branchDescStatus is the schema descriptor for status field.
	branchDescStatus := branchFields[8].Descriptor()
	// branch.DefaultStatus holds the default value on creation for the status field.
	branch.DefaultStatus = branchDescStatus.Default.(string)
	// branchDescInaugurationDate is the schema descriptor for inauguration_date field.
	branchDescInaugurationDate := branchFields[9].Descriptor()
	// branch.DefaultInaugurationDate holds the default value on creation for the inauguration_date field.
	branch.DefaultInaugurationDate = branchDescInaugurationDate.Default.(func() time.Time)
	// branchDescIsActive is the schema descriptor for is_active field.
	branchDescIsActive := branchFields[10].Descriptor()
	// branch.DefaultIsActive holds the default value on creation for the is_active field.
	branch.DefaultIsActive = branchDescIsActive.Default.(bool)
	costitemMixin := schema.CostItem{}.Mixin()
	costitemMixinFields0 := costitemMixin[0].Fields()
	_ = costitemMixinFields0
	costitemFields := schema.CostItem{}.Fields()
	_ = costitemFields
	// costitemDescCreatedAt is the schema descriptor for created_at field.
	costitemDescCreatedAt := costitemMixinFields0[0].Descriptor()
	// costitem.DefaultCreatedAt holds the default value on creation for the created_at field.
	costitem.DefaultCreatedAt = costitemDescCreatedAt.Default.(func() time.Time)
	// costitemDescUpdatedAt is the schema descriptor for updated_at field.
	costitemDescUpdatedAt := costitemMixinFields0[1].Descriptor()
	// costitem.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	costitem.DefaultUpdatedAt = costitemDescUpdatedAt.Default.(func() time.Time)
	// costitem.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	costitem.UpdateDefaultUpdatedAt = costitemDescUpdatedAt.UpdateDefault.(func() time.Time)
	// costitemDescActive is the schema descriptor for active field.
	costitemDescActive := costitemMixinFields0[2].Descriptor()
	// costitem.DefaultActive holds the default value on creation for the active field.
	costitem.DefaultActive = costitemDescActive.Default.(bool)
	// costitemDescDescription is the schema descriptor for description field.
	costitemDescDescription := costitemFields[2].Descriptor()
	// costitem.DescriptionValidator is a validator for the "description" field. It is called by the builders before save.
	costitem.DescriptionValidator = costitemDescDescription.Validators[0].(func(string) error)
	// costitemDescQuantity is the schema descriptor for quantity field.
	costitemDescQuantity := costitemFields[3].Descriptor()
	// costitem.DefaultQuantity holds the default value on creation for the quantity field.
	costitem.DefaultQuantity = costitemDescQuantity.Default.(float64)
	// costitemDescUnitPrice is the schema descriptor for unit_price field.
	costitemDescUnitPrice := costitemFields[4].Descriptor()
	// costitem.DefaultUnitPrice holds the default value on creation for the unit_price field.
	costitem.DefaultUnitPrice = costitemDescUnitPrice.Default.(float64)
	// costitemDescTotalPrice is the schema descriptor for total_price field.
	costitemDescTotalPrice := costitemFields[5].Descriptor()
	// costitem.DefaultTotalPrice holds the default value on creation for the total_price field.
	costitem.DefaultTotalPrice = costitemDescTotalPrice.Default.(float64)
	equipmentMixin := schema.Equipment{}.Mixin()
	equipmentMixinFields0 := equipmentMixin[0].Fields()
	_ = equipmentMixinFields0
	equipmentFields := schema.Equipment{}.Fields()
	_ = equipmentFields
	// equipmentDescCreatedAt is the schema descriptor for created_at field.
	equipmentDescCreatedAt := equipmentMixinFields0[0].Descriptor()
	// equipment.DefaultCreatedAt holds the default value on creation for the created_at field.
	equipment.DefaultCreatedAt = equipmentDescCreatedAt.Default.(func() time.Time)
	// equipmentDescUpdatedAt is the schema descriptor for updated_at field.
	equipmentDescUpdatedAt := equipmentMixinFields0[1].Descriptor()
	// equipment.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	equipment.DefaultUpdatedAt = equipmentDescUpdatedAt.Default.(func() time.Time)
	// equipment.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	equipment.UpdateDefaultUpdatedAt = equipmentDescUpdatedAt.UpdateDefault.(func() time.Time)
	// equipmentDescActive is the schema descriptor for active field.
	equipmentDescActive := equipmentMixinFields0[2].Descriptor()
	// equipment.DefaultActive holds the default value on creation for the active field.
	equipment.DefaultActive = equipmentDescActive.Default.(bool)
	// equipmentDescName is the schema descriptor for name field.
	equipmentDescName := equipmentFields[0].Descriptor()
	// equipment.NameValidator is a validator for the "name" field. It is called by the builders before save.
	equipment.NameValidator = equipmentDescName.Validators[0].(func(string) error)
	// equipmentDescType is the schema descriptor for type field.
	equipmentDescType := equipmentFields[1].Descriptor()
	// equipment.TypeValidator is a validator for the "type" field. It is called by the builders before save.
	equipment.TypeValidator = equipmentDescType.Validators[0].(func(string) error)
	// equipmentDescModel is the schema descriptor for model field.
	equipmentDescModel := equipmentFields[2].Descriptor()
	// equipment.ModelValidator is a validator for the "model" field. It is called by the builders before save.
	equipment.ModelValidator = equipmentDescModel.Validators[0].(func(string) error)
	// equipmentDescSerialNumber is the schema descriptor for serial_number field.
	equipmentDescSerialNumber := equipmentFields[3].Descriptor()
	// equipment.SerialNumberValidator is a validator for the "serial_number" field. It is called by the builders before save.
	equipment.SerialNumberValidator = equipmentDescSerialNumber.Validators[0].(func(string) error)
	interactionMixin := schema.Interaction{}.Mixin()
	interactionMixinFields0 := interactionMixin[0].Fields()
	_ = interactionMixinFields0
	interactionFields := schema.Interaction{}.Fields()
	_ = interactionFields
	// interactionDescCreateTime is the schema descriptor for create_time field.
	interactionDescCreateTime := interactionMixinFields0[0].Descriptor()
	// interaction.DefaultCreateTime holds the default value on creation for the create_time field.
	interaction.DefaultCreateTime = interactionDescCreateTime.Default.(func() time.Time)
	// interactionDescUpdateTime is the schema descriptor for update_time field.
	interactionDescUpdateTime := interactionMixinFields0[1].Descriptor()
	// interaction.DefaultUpdateTime holds the default value on creation for the update_time field.
	interaction.DefaultUpdateTime = interactionDescUpdateTime.Default.(func() time.Time)
	// interaction.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	interaction.UpdateDefaultUpdateTime = interactionDescUpdateTime.UpdateDefault.(func() time.Time)
	// interactionDescMessage is the schema descriptor for message field.
	interactionDescMessage := interactionFields[0].Descriptor()
	// interaction.MessageValidator is a validator for the "message" field. It is called by the builders before save.
	interaction.MessageValidator = func() func(string) error {
		validators := interactionDescMessage.Validators
		fns := [...]func(string) error{
			validators[0].(func(string) error),
			validators[1].(func(string) error),
		}
		return func(message string) error {
			for _, fn := range fns {
				if err := fn(message); err != nil {
					return err
				}
			}
			return nil
		}
	}()
	// interactionDescType is the schema descriptor for type field.
	interactionDescType := interactionFields[1].Descriptor()
	// interaction.DefaultType holds the default value on creation for the type field.
	interaction.DefaultType = interactionDescType.Default.(string)
	// interaction.TypeValidator is a validator for the "type" field. It is called by the builders before save.
	interaction.TypeValidator = interactionDescType.Validators[0].(func(string) error)
	maintenanceorderMixin := schema.MaintenanceOrder{}.Mixin()
	maintenanceorderMixinFields0 := maintenanceorderMixin[0].Fields()
	_ = maintenanceorderMixinFields0
	maintenanceorderFields := schema.MaintenanceOrder{}.Fields()
	_ = maintenanceorderFields
	// maintenanceorderDescCreatedAt is the schema descriptor for created_at field.
	maintenanceorderDescCreatedAt := maintenanceorderMixinFields0[0].Descriptor()
	// maintenanceorder.DefaultCreatedAt holds the default value on creation for the created_at field.
	maintenanceorder.DefaultCreatedAt = maintenanceorderDescCreatedAt.Default.(func() time.Time)
	// maintenanceorderDescUpdatedAt is the schema descriptor for updated_at field.
	maintenanceorderDescUpdatedAt := maintenanceorderMixinFields0[1].Descriptor()
	// maintenanceorder.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	maintenanceorder.DefaultUpdatedAt = maintenanceorderDescUpdatedAt.Default.(func() time.Time)
	// maintenanceorder.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	maintenanceorder.UpdateDefaultUpdatedAt = maintenanceorderDescUpdatedAt.UpdateDefault.(func() time.Time)
	// maintenanceorderDescActive is the schema descriptor for active field.
	maintenanceorderDescActive := maintenanceorderMixinFields0[2].Descriptor()
	// maintenanceorder.DefaultActive holds the default value on creation for the active field.
	maintenanceorder.DefaultActive = maintenanceorderDescActive.Default.(bool)
	// maintenanceorderDescTitle is the schema descriptor for title field.
	maintenanceorderDescTitle := maintenanceorderFields[0].Descriptor()
	// maintenanceorder.TitleValidator is a validator for the "title" field. It is called by the builders before save.
	maintenanceorder.TitleValidator = maintenanceorderDescTitle.Validators[0].(func(string) error)
	// maintenanceorderDescDescription is the schema descriptor for description field.
	maintenanceorderDescDescription := maintenanceorderFields[1].Descriptor()
	// maintenanceorder.DescriptionValidator is a validator for the "description" field. It is called by the builders before save.
	maintenanceorder.DescriptionValidator = maintenanceorderDescDescription.Validators[0].(func(string) error)
	// maintenanceorderDescStatus is the schema descriptor for status field.
	maintenanceorderDescStatus := maintenanceorderFields[2].Descriptor()
	// maintenanceorder.DefaultStatus holds the default value on creation for the status field.
	maintenanceorder.DefaultStatus = maintenanceorderDescStatus.Default.(string)
	// maintenanceorderDescPriority is the schema descriptor for priority field.
	maintenanceorderDescPriority := maintenanceorderFields[3].Descriptor()
	// maintenanceorder.DefaultPriority holds the default value on creation for the priority field.
	maintenanceorder.DefaultPriority = maintenanceorderDescPriority.Default.(string)
	userMixin := schema.User{}.Mixin()
	userMixinFields0 := userMixin[0].Fields()
	_ = userMixinFields0
	userFields := schema.User{}.Fields()
	_ = userFields
	// userDescCreatedAt is the schema descriptor for created_at field.
	userDescCreatedAt := userMixinFields0[0].Descriptor()
	// user.DefaultCreatedAt holds the default value on creation for the created_at field.
	user.DefaultCreatedAt = userDescCreatedAt.Default.(func() time.Time)
	// userDescUpdatedAt is the schema descriptor for updated_at field.
	userDescUpdatedAt := userMixinFields0[1].Descriptor()
	// user.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	user.DefaultUpdatedAt = userDescUpdatedAt.Default.(func() time.Time)
	// user.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	user.UpdateDefaultUpdatedAt = userDescUpdatedAt.UpdateDefault.(func() time.Time)
	// userDescActive is the schema descriptor for active field.
	userDescActive := userMixinFields0[2].Descriptor()
	// user.DefaultActive holds the default value on creation for the active field.
	user.DefaultActive = userDescActive.Default.(bool)
	// userDescName is the schema descriptor for name field.
	userDescName := userFields[0].Descriptor()
	// user.NameValidator is a validator for the "name" field. It is called by the builders before save.
	user.NameValidator = userDescName.Validators[0].(func(string) error)
	// userDescEmail is the schema descriptor for email field.
	userDescEmail := userFields[1].Descriptor()
	// user.EmailValidator is a validator for the "email" field. It is called by the builders before save.
	user.EmailValidator = userDescEmail.Validators[0].(func(string) error)
	// userDescPassword is the schema descriptor for password field.
	userDescPassword := userFields[2].Descriptor()
	// user.PasswordValidator is a validator for the "password" field. It is called by the builders before save.
	user.PasswordValidator = userDescPassword.Validators[0].(func(string) error)
	// userDescRole is the schema descriptor for role field.
	userDescRole := userFields[3].Descriptor()
	// user.RoleValidator is a validator for the "role" field. It is called by the builders before save.
	user.RoleValidator = userDescRole.Validators[0].(func(string) error)
}
