// Code generated by ent, DO NOT EDIT.

package maintenanceorder

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the maintenanceorder type in the database.
	Label = "maintenance_order"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldActive holds the string denoting the active field in the database.
	FieldActive = "active"
	// FieldTitle holds the string denoting the title field in the database.
	FieldTitle = "title"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldPriority holds the string denoting the priority field in the database.
	FieldPriority = "priority"
	// FieldBranchID holds the string denoting the branch_id field in the database.
	FieldBranchID = "branch_id"
	// FieldEquipmentID holds the string denoting the equipment_id field in the database.
	FieldEquipmentID = "equipment_id"
	// FieldRequesterID holds the string denoting the requester_id field in the database.
	FieldRequesterID = "requester_id"
	// FieldApproverID holds the string denoting the approver_id field in the database.
	FieldApproverID = "approver_id"
	// FieldTechnicianID holds the string denoting the technician_id field in the database.
	FieldTechnicianID = "technician_id"
	// FieldCancellationReason holds the string denoting the cancellation_reason field in the database.
	FieldCancellationReason = "cancellation_reason"
	// FieldRejectionReason holds the string denoting the rejection_reason field in the database.
	FieldRejectionReason = "rejection_reason"
	// EdgeBranch holds the string denoting the branch edge name in mutations.
	EdgeBranch = "branch"
	// EdgeEquipment holds the string denoting the equipment edge name in mutations.
	EdgeEquipment = "equipment"
	// EdgeRequester holds the string denoting the requester edge name in mutations.
	EdgeRequester = "requester"
	// EdgeApprover holds the string denoting the approver edge name in mutations.
	EdgeApprover = "approver"
	// EdgeTechnician holds the string denoting the technician edge name in mutations.
	EdgeTechnician = "technician"
	// EdgeCostItems holds the string denoting the cost_items edge name in mutations.
	EdgeCostItems = "cost_items"
	// EdgeInteractions holds the string denoting the interactions edge name in mutations.
	EdgeInteractions = "interactions"
	// Table holds the table name of the maintenanceorder in the database.
	Table = "maintenance_orders"
	// BranchTable is the table that holds the branch relation/edge.
	BranchTable = "maintenance_orders"
	// BranchInverseTable is the table name for the Branch entity.
	// It exists in this package in order to avoid circular dependency with the "branch" package.
	BranchInverseTable = "branches"
	// BranchColumn is the table column denoting the branch relation/edge.
	BranchColumn = "branch_id"
	// EquipmentTable is the table that holds the equipment relation/edge.
	EquipmentTable = "maintenance_orders"
	// EquipmentInverseTable is the table name for the Equipment entity.
	// It exists in this package in order to avoid circular dependency with the "equipment" package.
	EquipmentInverseTable = "equipment"
	// EquipmentColumn is the table column denoting the equipment relation/edge.
	EquipmentColumn = "equipment_id"
	// RequesterTable is the table that holds the requester relation/edge.
	RequesterTable = "maintenance_orders"
	// RequesterInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	RequesterInverseTable = "users"
	// RequesterColumn is the table column denoting the requester relation/edge.
	RequesterColumn = "requester_id"
	// ApproverTable is the table that holds the approver relation/edge.
	ApproverTable = "maintenance_orders"
	// ApproverInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	ApproverInverseTable = "users"
	// ApproverColumn is the table column denoting the approver relation/edge.
	ApproverColumn = "approver_id"
	// TechnicianTable is the table that holds the technician relation/edge.
	TechnicianTable = "maintenance_orders"
	// TechnicianInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	TechnicianInverseTable = "users"
	// TechnicianColumn is the table column denoting the technician relation/edge.
	TechnicianColumn = "technician_id"
	// CostItemsTable is the table that holds the cost_items relation/edge.
	CostItemsTable = "cost_items"
	// CostItemsInverseTable is the table name for the CostItem entity.
	// It exists in this package in order to avoid circular dependency with the "costitem" package.
	CostItemsInverseTable = "cost_items"
	// CostItemsColumn is the table column denoting the cost_items relation/edge.
	CostItemsColumn = "maintenance_order_id"
	// InteractionsTable is the table that holds the interactions relation/edge.
	InteractionsTable = "interactions"
	// InteractionsInverseTable is the table name for the Interaction entity.
	// It exists in this package in order to avoid circular dependency with the "interaction" package.
	InteractionsInverseTable = "interactions"
	// InteractionsColumn is the table column denoting the interactions relation/edge.
	InteractionsColumn = "maintenance_order_interactions"
)

// Columns holds all SQL columns for maintenanceorder fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldActive,
	FieldTitle,
	FieldDescription,
	FieldStatus,
	FieldPriority,
	FieldBranchID,
	FieldEquipmentID,
	FieldRequesterID,
	FieldApproverID,
	FieldTechnicianID,
	FieldCancellationReason,
	FieldRejectionReason,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultActive holds the default value on creation for the "active" field.
	DefaultActive bool
	// TitleValidator is a validator for the "title" field. It is called by the builders before save.
	TitleValidator func(string) error
	// DescriptionValidator is a validator for the "description" field. It is called by the builders before save.
	DescriptionValidator func(string) error
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus string
	// DefaultPriority holds the default value on creation for the "priority" field.
	DefaultPriority string
)

// OrderOption defines the ordering options for the MaintenanceOrder queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByActive orders the results by the active field.
func ByActive(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldActive, opts...).ToFunc()
}

// ByTitle orders the results by the title field.
func ByTitle(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTitle, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByPriority orders the results by the priority field.
func ByPriority(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPriority, opts...).ToFunc()
}

// ByBranchID orders the results by the branch_id field.
func ByBranchID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBranchID, opts...).ToFunc()
}

// ByEquipmentID orders the results by the equipment_id field.
func ByEquipmentID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEquipmentID, opts...).ToFunc()
}

// ByRequesterID orders the results by the requester_id field.
func ByRequesterID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRequesterID, opts...).ToFunc()
}

// ByApproverID orders the results by the approver_id field.
func ByApproverID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldApproverID, opts...).ToFunc()
}

// ByTechnicianID orders the results by the technician_id field.
func ByTechnicianID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTechnicianID, opts...).ToFunc()
}

// ByCancellationReason orders the results by the cancellation_reason field.
func ByCancellationReason(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCancellationReason, opts...).ToFunc()
}

// ByRejectionReason orders the results by the rejection_reason field.
func ByRejectionReason(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRejectionReason, opts...).ToFunc()
}

// ByBranchField orders the results by branch field.
func ByBranchField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newBranchStep(), sql.OrderByField(field, opts...))
	}
}

// ByEquipmentField orders the results by equipment field.
func ByEquipmentField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newEquipmentStep(), sql.OrderByField(field, opts...))
	}
}

// ByRequesterField orders the results by requester field.
func ByRequesterField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newRequesterStep(), sql.OrderByField(field, opts...))
	}
}

// ByApproverField orders the results by approver field.
func ByApproverField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newApproverStep(), sql.OrderByField(field, opts...))
	}
}

// ByTechnicianField orders the results by technician field.
func ByTechnicianField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTechnicianStep(), sql.OrderByField(field, opts...))
	}
}

// ByCostItemsCount orders the results by cost_items count.
func ByCostItemsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newCostItemsStep(), opts...)
	}
}

// ByCostItems orders the results by cost_items terms.
func ByCostItems(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newCostItemsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByInteractionsCount orders the results by interactions count.
func ByInteractionsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newInteractionsStep(), opts...)
	}
}

// ByInteractions orders the results by interactions terms.
func ByInteractions(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newInteractionsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newBranchStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(BranchInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, BranchTable, BranchColumn),
	)
}
func newEquipmentStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(EquipmentInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, EquipmentTable, EquipmentColumn),
	)
}
func newRequesterStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(RequesterInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, RequesterTable, RequesterColumn),
	)
}
func newApproverStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(ApproverInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, ApproverTable, ApproverColumn),
	)
}
func newTechnicianStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TechnicianInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, TechnicianTable, TechnicianColumn),
	)
}
func newCostItemsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(CostItemsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, CostItemsTable, CostItemsColumn),
	)
}
func newInteractionsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(InteractionsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, InteractionsTable, InteractionsColumn),
	)
}
