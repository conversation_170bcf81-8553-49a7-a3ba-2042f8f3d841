// Code generated by ent, DO NOT EDIT.

package maintenanceorder

import (
	"time"
	"tradicao/internal/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldUpdatedAt, v))
}

// Active applies equality check predicate on the "active" field. It's identical to ActiveEQ.
func Active(v bool) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldActive, v))
}

// Title applies equality check predicate on the "title" field. It's identical to TitleEQ.
func Title(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldTitle, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldDescription, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldStatus, v))
}

// Priority applies equality check predicate on the "priority" field. It's identical to PriorityEQ.
func Priority(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldPriority, v))
}

// BranchID applies equality check predicate on the "branch_id" field. It's identical to BranchIDEQ.
func BranchID(v int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldBranchID, v))
}

// EquipmentID applies equality check predicate on the "equipment_id" field. It's identical to EquipmentIDEQ.
func EquipmentID(v int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldEquipmentID, v))
}

// RequesterID applies equality check predicate on the "requester_id" field. It's identical to RequesterIDEQ.
func RequesterID(v int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldRequesterID, v))
}

// ApproverID applies equality check predicate on the "approver_id" field. It's identical to ApproverIDEQ.
func ApproverID(v int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldApproverID, v))
}

// TechnicianID applies equality check predicate on the "technician_id" field. It's identical to TechnicianIDEQ.
func TechnicianID(v int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldTechnicianID, v))
}

// CancellationReason applies equality check predicate on the "cancellation_reason" field. It's identical to CancellationReasonEQ.
func CancellationReason(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldCancellationReason, v))
}

// RejectionReason applies equality check predicate on the "rejection_reason" field. It's identical to RejectionReasonEQ.
func RejectionReason(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldRejectionReason, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldLTE(FieldUpdatedAt, v))
}

// ActiveEQ applies the EQ predicate on the "active" field.
func ActiveEQ(v bool) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldActive, v))
}

// ActiveNEQ applies the NEQ predicate on the "active" field.
func ActiveNEQ(v bool) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNEQ(FieldActive, v))
}

// TitleEQ applies the EQ predicate on the "title" field.
func TitleEQ(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldTitle, v))
}

// TitleNEQ applies the NEQ predicate on the "title" field.
func TitleNEQ(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNEQ(FieldTitle, v))
}

// TitleIn applies the In predicate on the "title" field.
func TitleIn(vs ...string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIn(FieldTitle, vs...))
}

// TitleNotIn applies the NotIn predicate on the "title" field.
func TitleNotIn(vs ...string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotIn(FieldTitle, vs...))
}

// TitleGT applies the GT predicate on the "title" field.
func TitleGT(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldGT(FieldTitle, v))
}

// TitleGTE applies the GTE predicate on the "title" field.
func TitleGTE(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldGTE(FieldTitle, v))
}

// TitleLT applies the LT predicate on the "title" field.
func TitleLT(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldLT(FieldTitle, v))
}

// TitleLTE applies the LTE predicate on the "title" field.
func TitleLTE(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldLTE(FieldTitle, v))
}

// TitleContains applies the Contains predicate on the "title" field.
func TitleContains(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldContains(FieldTitle, v))
}

// TitleHasPrefix applies the HasPrefix predicate on the "title" field.
func TitleHasPrefix(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldHasPrefix(FieldTitle, v))
}

// TitleHasSuffix applies the HasSuffix predicate on the "title" field.
func TitleHasSuffix(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldHasSuffix(FieldTitle, v))
}

// TitleEqualFold applies the EqualFold predicate on the "title" field.
func TitleEqualFold(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEqualFold(FieldTitle, v))
}

// TitleContainsFold applies the ContainsFold predicate on the "title" field.
func TitleContainsFold(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldContainsFold(FieldTitle, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldContainsFold(FieldDescription, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldContainsFold(FieldStatus, v))
}

// PriorityEQ applies the EQ predicate on the "priority" field.
func PriorityEQ(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldPriority, v))
}

// PriorityNEQ applies the NEQ predicate on the "priority" field.
func PriorityNEQ(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNEQ(FieldPriority, v))
}

// PriorityIn applies the In predicate on the "priority" field.
func PriorityIn(vs ...string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIn(FieldPriority, vs...))
}

// PriorityNotIn applies the NotIn predicate on the "priority" field.
func PriorityNotIn(vs ...string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotIn(FieldPriority, vs...))
}

// PriorityGT applies the GT predicate on the "priority" field.
func PriorityGT(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldGT(FieldPriority, v))
}

// PriorityGTE applies the GTE predicate on the "priority" field.
func PriorityGTE(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldGTE(FieldPriority, v))
}

// PriorityLT applies the LT predicate on the "priority" field.
func PriorityLT(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldLT(FieldPriority, v))
}

// PriorityLTE applies the LTE predicate on the "priority" field.
func PriorityLTE(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldLTE(FieldPriority, v))
}

// PriorityContains applies the Contains predicate on the "priority" field.
func PriorityContains(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldContains(FieldPriority, v))
}

// PriorityHasPrefix applies the HasPrefix predicate on the "priority" field.
func PriorityHasPrefix(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldHasPrefix(FieldPriority, v))
}

// PriorityHasSuffix applies the HasSuffix predicate on the "priority" field.
func PriorityHasSuffix(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldHasSuffix(FieldPriority, v))
}

// PriorityEqualFold applies the EqualFold predicate on the "priority" field.
func PriorityEqualFold(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEqualFold(FieldPriority, v))
}

// PriorityContainsFold applies the ContainsFold predicate on the "priority" field.
func PriorityContainsFold(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldContainsFold(FieldPriority, v))
}

// BranchIDEQ applies the EQ predicate on the "branch_id" field.
func BranchIDEQ(v int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldBranchID, v))
}

// BranchIDNEQ applies the NEQ predicate on the "branch_id" field.
func BranchIDNEQ(v int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNEQ(FieldBranchID, v))
}

// BranchIDIn applies the In predicate on the "branch_id" field.
func BranchIDIn(vs ...int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIn(FieldBranchID, vs...))
}

// BranchIDNotIn applies the NotIn predicate on the "branch_id" field.
func BranchIDNotIn(vs ...int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotIn(FieldBranchID, vs...))
}

// BranchIDIsNil applies the IsNil predicate on the "branch_id" field.
func BranchIDIsNil() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIsNull(FieldBranchID))
}

// BranchIDNotNil applies the NotNil predicate on the "branch_id" field.
func BranchIDNotNil() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotNull(FieldBranchID))
}

// EquipmentIDEQ applies the EQ predicate on the "equipment_id" field.
func EquipmentIDEQ(v int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldEquipmentID, v))
}

// EquipmentIDNEQ applies the NEQ predicate on the "equipment_id" field.
func EquipmentIDNEQ(v int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNEQ(FieldEquipmentID, v))
}

// EquipmentIDIn applies the In predicate on the "equipment_id" field.
func EquipmentIDIn(vs ...int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIn(FieldEquipmentID, vs...))
}

// EquipmentIDNotIn applies the NotIn predicate on the "equipment_id" field.
func EquipmentIDNotIn(vs ...int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotIn(FieldEquipmentID, vs...))
}

// EquipmentIDIsNil applies the IsNil predicate on the "equipment_id" field.
func EquipmentIDIsNil() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIsNull(FieldEquipmentID))
}

// EquipmentIDNotNil applies the NotNil predicate on the "equipment_id" field.
func EquipmentIDNotNil() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotNull(FieldEquipmentID))
}

// RequesterIDEQ applies the EQ predicate on the "requester_id" field.
func RequesterIDEQ(v int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldRequesterID, v))
}

// RequesterIDNEQ applies the NEQ predicate on the "requester_id" field.
func RequesterIDNEQ(v int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNEQ(FieldRequesterID, v))
}

// RequesterIDIn applies the In predicate on the "requester_id" field.
func RequesterIDIn(vs ...int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIn(FieldRequesterID, vs...))
}

// RequesterIDNotIn applies the NotIn predicate on the "requester_id" field.
func RequesterIDNotIn(vs ...int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotIn(FieldRequesterID, vs...))
}

// RequesterIDIsNil applies the IsNil predicate on the "requester_id" field.
func RequesterIDIsNil() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIsNull(FieldRequesterID))
}

// RequesterIDNotNil applies the NotNil predicate on the "requester_id" field.
func RequesterIDNotNil() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotNull(FieldRequesterID))
}

// ApproverIDEQ applies the EQ predicate on the "approver_id" field.
func ApproverIDEQ(v int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldApproverID, v))
}

// ApproverIDNEQ applies the NEQ predicate on the "approver_id" field.
func ApproverIDNEQ(v int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNEQ(FieldApproverID, v))
}

// ApproverIDIn applies the In predicate on the "approver_id" field.
func ApproverIDIn(vs ...int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIn(FieldApproverID, vs...))
}

// ApproverIDNotIn applies the NotIn predicate on the "approver_id" field.
func ApproverIDNotIn(vs ...int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotIn(FieldApproverID, vs...))
}

// ApproverIDIsNil applies the IsNil predicate on the "approver_id" field.
func ApproverIDIsNil() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIsNull(FieldApproverID))
}

// ApproverIDNotNil applies the NotNil predicate on the "approver_id" field.
func ApproverIDNotNil() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotNull(FieldApproverID))
}

// TechnicianIDEQ applies the EQ predicate on the "technician_id" field.
func TechnicianIDEQ(v int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldTechnicianID, v))
}

// TechnicianIDNEQ applies the NEQ predicate on the "technician_id" field.
func TechnicianIDNEQ(v int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNEQ(FieldTechnicianID, v))
}

// TechnicianIDIn applies the In predicate on the "technician_id" field.
func TechnicianIDIn(vs ...int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIn(FieldTechnicianID, vs...))
}

// TechnicianIDNotIn applies the NotIn predicate on the "technician_id" field.
func TechnicianIDNotIn(vs ...int) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotIn(FieldTechnicianID, vs...))
}

// TechnicianIDIsNil applies the IsNil predicate on the "technician_id" field.
func TechnicianIDIsNil() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIsNull(FieldTechnicianID))
}

// TechnicianIDNotNil applies the NotNil predicate on the "technician_id" field.
func TechnicianIDNotNil() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotNull(FieldTechnicianID))
}

// CancellationReasonEQ applies the EQ predicate on the "cancellation_reason" field.
func CancellationReasonEQ(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldCancellationReason, v))
}

// CancellationReasonNEQ applies the NEQ predicate on the "cancellation_reason" field.
func CancellationReasonNEQ(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNEQ(FieldCancellationReason, v))
}

// CancellationReasonIn applies the In predicate on the "cancellation_reason" field.
func CancellationReasonIn(vs ...string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIn(FieldCancellationReason, vs...))
}

// CancellationReasonNotIn applies the NotIn predicate on the "cancellation_reason" field.
func CancellationReasonNotIn(vs ...string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotIn(FieldCancellationReason, vs...))
}

// CancellationReasonGT applies the GT predicate on the "cancellation_reason" field.
func CancellationReasonGT(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldGT(FieldCancellationReason, v))
}

// CancellationReasonGTE applies the GTE predicate on the "cancellation_reason" field.
func CancellationReasonGTE(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldGTE(FieldCancellationReason, v))
}

// CancellationReasonLT applies the LT predicate on the "cancellation_reason" field.
func CancellationReasonLT(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldLT(FieldCancellationReason, v))
}

// CancellationReasonLTE applies the LTE predicate on the "cancellation_reason" field.
func CancellationReasonLTE(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldLTE(FieldCancellationReason, v))
}

// CancellationReasonContains applies the Contains predicate on the "cancellation_reason" field.
func CancellationReasonContains(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldContains(FieldCancellationReason, v))
}

// CancellationReasonHasPrefix applies the HasPrefix predicate on the "cancellation_reason" field.
func CancellationReasonHasPrefix(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldHasPrefix(FieldCancellationReason, v))
}

// CancellationReasonHasSuffix applies the HasSuffix predicate on the "cancellation_reason" field.
func CancellationReasonHasSuffix(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldHasSuffix(FieldCancellationReason, v))
}

// CancellationReasonIsNil applies the IsNil predicate on the "cancellation_reason" field.
func CancellationReasonIsNil() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIsNull(FieldCancellationReason))
}

// CancellationReasonNotNil applies the NotNil predicate on the "cancellation_reason" field.
func CancellationReasonNotNil() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotNull(FieldCancellationReason))
}

// CancellationReasonEqualFold applies the EqualFold predicate on the "cancellation_reason" field.
func CancellationReasonEqualFold(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEqualFold(FieldCancellationReason, v))
}

// CancellationReasonContainsFold applies the ContainsFold predicate on the "cancellation_reason" field.
func CancellationReasonContainsFold(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldContainsFold(FieldCancellationReason, v))
}

// RejectionReasonEQ applies the EQ predicate on the "rejection_reason" field.
func RejectionReasonEQ(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEQ(FieldRejectionReason, v))
}

// RejectionReasonNEQ applies the NEQ predicate on the "rejection_reason" field.
func RejectionReasonNEQ(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNEQ(FieldRejectionReason, v))
}

// RejectionReasonIn applies the In predicate on the "rejection_reason" field.
func RejectionReasonIn(vs ...string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIn(FieldRejectionReason, vs...))
}

// RejectionReasonNotIn applies the NotIn predicate on the "rejection_reason" field.
func RejectionReasonNotIn(vs ...string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotIn(FieldRejectionReason, vs...))
}

// RejectionReasonGT applies the GT predicate on the "rejection_reason" field.
func RejectionReasonGT(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldGT(FieldRejectionReason, v))
}

// RejectionReasonGTE applies the GTE predicate on the "rejection_reason" field.
func RejectionReasonGTE(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldGTE(FieldRejectionReason, v))
}

// RejectionReasonLT applies the LT predicate on the "rejection_reason" field.
func RejectionReasonLT(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldLT(FieldRejectionReason, v))
}

// RejectionReasonLTE applies the LTE predicate on the "rejection_reason" field.
func RejectionReasonLTE(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldLTE(FieldRejectionReason, v))
}

// RejectionReasonContains applies the Contains predicate on the "rejection_reason" field.
func RejectionReasonContains(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldContains(FieldRejectionReason, v))
}

// RejectionReasonHasPrefix applies the HasPrefix predicate on the "rejection_reason" field.
func RejectionReasonHasPrefix(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldHasPrefix(FieldRejectionReason, v))
}

// RejectionReasonHasSuffix applies the HasSuffix predicate on the "rejection_reason" field.
func RejectionReasonHasSuffix(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldHasSuffix(FieldRejectionReason, v))
}

// RejectionReasonIsNil applies the IsNil predicate on the "rejection_reason" field.
func RejectionReasonIsNil() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldIsNull(FieldRejectionReason))
}

// RejectionReasonNotNil applies the NotNil predicate on the "rejection_reason" field.
func RejectionReasonNotNil() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldNotNull(FieldRejectionReason))
}

// RejectionReasonEqualFold applies the EqualFold predicate on the "rejection_reason" field.
func RejectionReasonEqualFold(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldEqualFold(FieldRejectionReason, v))
}

// RejectionReasonContainsFold applies the ContainsFold predicate on the "rejection_reason" field.
func RejectionReasonContainsFold(v string) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.FieldContainsFold(FieldRejectionReason, v))
}

// HasBranch applies the HasEdge predicate on the "branch" edge.
func HasBranch() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, BranchTable, BranchColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasBranchWith applies the HasEdge predicate on the "branch" edge with a given conditions (other predicates).
func HasBranchWith(preds ...predicate.Branch) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(func(s *sql.Selector) {
		step := newBranchStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasEquipment applies the HasEdge predicate on the "equipment" edge.
func HasEquipment() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, EquipmentTable, EquipmentColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasEquipmentWith applies the HasEdge predicate on the "equipment" edge with a given conditions (other predicates).
func HasEquipmentWith(preds ...predicate.Equipment) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(func(s *sql.Selector) {
		step := newEquipmentStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasRequester applies the HasEdge predicate on the "requester" edge.
func HasRequester() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, RequesterTable, RequesterColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasRequesterWith applies the HasEdge predicate on the "requester" edge with a given conditions (other predicates).
func HasRequesterWith(preds ...predicate.User) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(func(s *sql.Selector) {
		step := newRequesterStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasApprover applies the HasEdge predicate on the "approver" edge.
func HasApprover() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, ApproverTable, ApproverColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasApproverWith applies the HasEdge predicate on the "approver" edge with a given conditions (other predicates).
func HasApproverWith(preds ...predicate.User) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(func(s *sql.Selector) {
		step := newApproverStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasTechnician applies the HasEdge predicate on the "technician" edge.
func HasTechnician() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, TechnicianTable, TechnicianColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTechnicianWith applies the HasEdge predicate on the "technician" edge with a given conditions (other predicates).
func HasTechnicianWith(preds ...predicate.User) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(func(s *sql.Selector) {
		step := newTechnicianStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasCostItems applies the HasEdge predicate on the "cost_items" edge.
func HasCostItems() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, CostItemsTable, CostItemsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasCostItemsWith applies the HasEdge predicate on the "cost_items" edge with a given conditions (other predicates).
func HasCostItemsWith(preds ...predicate.CostItem) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(func(s *sql.Selector) {
		step := newCostItemsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasInteractions applies the HasEdge predicate on the "interactions" edge.
func HasInteractions() predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, InteractionsTable, InteractionsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasInteractionsWith applies the HasEdge predicate on the "interactions" edge with a given conditions (other predicates).
func HasInteractionsWith(preds ...predicate.Interaction) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(func(s *sql.Selector) {
		step := newInteractionsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.MaintenanceOrder) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.MaintenanceOrder) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.MaintenanceOrder) predicate.MaintenanceOrder {
	return predicate.MaintenanceOrder(sql.NotPredicates(p))
}
