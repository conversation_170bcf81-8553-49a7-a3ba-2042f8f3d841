// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"
	"tradicao/internal/ent/branch"
	"tradicao/internal/ent/equipment"
	"tradicao/internal/ent/maintenanceorder"
	"tradicao/internal/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// EquipmentUpdate is the builder for updating Equipment entities.
type EquipmentUpdate struct {
	config
	hooks    []Hook
	mutation *EquipmentMutation
}

// Where appends a list predicates to the EquipmentUpdate builder.
func (eu *EquipmentUpdate) Where(ps ...predicate.Equipment) *EquipmentUpdate {
	eu.mutation.Where(ps...)
	return eu
}

// SetUpdatedAt sets the "updated_at" field.
func (eu *EquipmentUpdate) SetUpdatedAt(t time.Time) *EquipmentUpdate {
	eu.mutation.SetUpdatedAt(t)
	return eu
}

// SetActive sets the "active" field.
func (eu *EquipmentUpdate) SetActive(b bool) *EquipmentUpdate {
	eu.mutation.SetActive(b)
	return eu
}

// SetNillableActive sets the "active" field if the given value is not nil.
func (eu *EquipmentUpdate) SetNillableActive(b *bool) *EquipmentUpdate {
	if b != nil {
		eu.SetActive(*b)
	}
	return eu
}

// SetName sets the "name" field.
func (eu *EquipmentUpdate) SetName(s string) *EquipmentUpdate {
	eu.mutation.SetName(s)
	return eu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (eu *EquipmentUpdate) SetNillableName(s *string) *EquipmentUpdate {
	if s != nil {
		eu.SetName(*s)
	}
	return eu
}

// SetType sets the "type" field.
func (eu *EquipmentUpdate) SetType(s string) *EquipmentUpdate {
	eu.mutation.SetType(s)
	return eu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (eu *EquipmentUpdate) SetNillableType(s *string) *EquipmentUpdate {
	if s != nil {
		eu.SetType(*s)
	}
	return eu
}

// SetModel sets the "model" field.
func (eu *EquipmentUpdate) SetModel(s string) *EquipmentUpdate {
	eu.mutation.SetModel(s)
	return eu
}

// SetNillableModel sets the "model" field if the given value is not nil.
func (eu *EquipmentUpdate) SetNillableModel(s *string) *EquipmentUpdate {
	if s != nil {
		eu.SetModel(*s)
	}
	return eu
}

// SetSerialNumber sets the "serial_number" field.
func (eu *EquipmentUpdate) SetSerialNumber(s string) *EquipmentUpdate {
	eu.mutation.SetSerialNumber(s)
	return eu
}

// SetNillableSerialNumber sets the "serial_number" field if the given value is not nil.
func (eu *EquipmentUpdate) SetNillableSerialNumber(s *string) *EquipmentUpdate {
	if s != nil {
		eu.SetSerialNumber(*s)
	}
	return eu
}

// SetInstallationDate sets the "installation_date" field.
func (eu *EquipmentUpdate) SetInstallationDate(t time.Time) *EquipmentUpdate {
	eu.mutation.SetInstallationDate(t)
	return eu
}

// SetNillableInstallationDate sets the "installation_date" field if the given value is not nil.
func (eu *EquipmentUpdate) SetNillableInstallationDate(t *time.Time) *EquipmentUpdate {
	if t != nil {
		eu.SetInstallationDate(*t)
	}
	return eu
}

// ClearInstallationDate clears the value of the "installation_date" field.
func (eu *EquipmentUpdate) ClearInstallationDate() *EquipmentUpdate {
	eu.mutation.ClearInstallationDate()
	return eu
}

// SetLastMaintenanceDate sets the "last_maintenance_date" field.
func (eu *EquipmentUpdate) SetLastMaintenanceDate(t time.Time) *EquipmentUpdate {
	eu.mutation.SetLastMaintenanceDate(t)
	return eu
}

// SetNillableLastMaintenanceDate sets the "last_maintenance_date" field if the given value is not nil.
func (eu *EquipmentUpdate) SetNillableLastMaintenanceDate(t *time.Time) *EquipmentUpdate {
	if t != nil {
		eu.SetLastMaintenanceDate(*t)
	}
	return eu
}

// ClearLastMaintenanceDate clears the value of the "last_maintenance_date" field.
func (eu *EquipmentUpdate) ClearLastMaintenanceDate() *EquipmentUpdate {
	eu.mutation.ClearLastMaintenanceDate()
	return eu
}

// SetBranchID sets the "branch_id" field.
func (eu *EquipmentUpdate) SetBranchID(i int) *EquipmentUpdate {
	eu.mutation.SetBranchID(i)
	return eu
}

// SetNillableBranchID sets the "branch_id" field if the given value is not nil.
func (eu *EquipmentUpdate) SetNillableBranchID(i *int) *EquipmentUpdate {
	if i != nil {
		eu.SetBranchID(*i)
	}
	return eu
}

// ClearBranchID clears the value of the "branch_id" field.
func (eu *EquipmentUpdate) ClearBranchID() *EquipmentUpdate {
	eu.mutation.ClearBranchID()
	return eu
}

// SetBranch sets the "branch" edge to the Branch entity.
func (eu *EquipmentUpdate) SetBranch(b *Branch) *EquipmentUpdate {
	return eu.SetBranchID(b.ID)
}

// AddMaintenanceOrderIDs adds the "maintenance_orders" edge to the MaintenanceOrder entity by IDs.
func (eu *EquipmentUpdate) AddMaintenanceOrderIDs(ids ...int) *EquipmentUpdate {
	eu.mutation.AddMaintenanceOrderIDs(ids...)
	return eu
}

// AddMaintenanceOrders adds the "maintenance_orders" edges to the MaintenanceOrder entity.
func (eu *EquipmentUpdate) AddMaintenanceOrders(m ...*MaintenanceOrder) *EquipmentUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return eu.AddMaintenanceOrderIDs(ids...)
}

// Mutation returns the EquipmentMutation object of the builder.
func (eu *EquipmentUpdate) Mutation() *EquipmentMutation {
	return eu.mutation
}

// ClearBranch clears the "branch" edge to the Branch entity.
func (eu *EquipmentUpdate) ClearBranch() *EquipmentUpdate {
	eu.mutation.ClearBranch()
	return eu
}

// ClearMaintenanceOrders clears all "maintenance_orders" edges to the MaintenanceOrder entity.
func (eu *EquipmentUpdate) ClearMaintenanceOrders() *EquipmentUpdate {
	eu.mutation.ClearMaintenanceOrders()
	return eu
}

// RemoveMaintenanceOrderIDs removes the "maintenance_orders" edge to MaintenanceOrder entities by IDs.
func (eu *EquipmentUpdate) RemoveMaintenanceOrderIDs(ids ...int) *EquipmentUpdate {
	eu.mutation.RemoveMaintenanceOrderIDs(ids...)
	return eu
}

// RemoveMaintenanceOrders removes "maintenance_orders" edges to MaintenanceOrder entities.
func (eu *EquipmentUpdate) RemoveMaintenanceOrders(m ...*MaintenanceOrder) *EquipmentUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return eu.RemoveMaintenanceOrderIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (eu *EquipmentUpdate) Save(ctx context.Context) (int, error) {
	eu.defaults()
	return withHooks(ctx, eu.sqlSave, eu.mutation, eu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (eu *EquipmentUpdate) SaveX(ctx context.Context) int {
	affected, err := eu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (eu *EquipmentUpdate) Exec(ctx context.Context) error {
	_, err := eu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (eu *EquipmentUpdate) ExecX(ctx context.Context) {
	if err := eu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (eu *EquipmentUpdate) defaults() {
	if _, ok := eu.mutation.UpdatedAt(); !ok {
		v := equipment.UpdateDefaultUpdatedAt()
		eu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (eu *EquipmentUpdate) check() error {
	if v, ok := eu.mutation.Name(); ok {
		if err := equipment.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Equipment.name": %w`, err)}
		}
	}
	if v, ok := eu.mutation.GetType(); ok {
		if err := equipment.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Equipment.type": %w`, err)}
		}
	}
	if v, ok := eu.mutation.Model(); ok {
		if err := equipment.ModelValidator(v); err != nil {
			return &ValidationError{Name: "model", err: fmt.Errorf(`ent: validator failed for field "Equipment.model": %w`, err)}
		}
	}
	if v, ok := eu.mutation.SerialNumber(); ok {
		if err := equipment.SerialNumberValidator(v); err != nil {
			return &ValidationError{Name: "serial_number", err: fmt.Errorf(`ent: validator failed for field "Equipment.serial_number": %w`, err)}
		}
	}
	return nil
}

func (eu *EquipmentUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := eu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(equipment.Table, equipment.Columns, sqlgraph.NewFieldSpec(equipment.FieldID, field.TypeInt))
	if ps := eu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := eu.mutation.UpdatedAt(); ok {
		_spec.SetField(equipment.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := eu.mutation.Active(); ok {
		_spec.SetField(equipment.FieldActive, field.TypeBool, value)
	}
	if value, ok := eu.mutation.Name(); ok {
		_spec.SetField(equipment.FieldName, field.TypeString, value)
	}
	if value, ok := eu.mutation.GetType(); ok {
		_spec.SetField(equipment.FieldType, field.TypeString, value)
	}
	if value, ok := eu.mutation.Model(); ok {
		_spec.SetField(equipment.FieldModel, field.TypeString, value)
	}
	if value, ok := eu.mutation.SerialNumber(); ok {
		_spec.SetField(equipment.FieldSerialNumber, field.TypeString, value)
	}
	if value, ok := eu.mutation.InstallationDate(); ok {
		_spec.SetField(equipment.FieldInstallationDate, field.TypeTime, value)
	}
	if eu.mutation.InstallationDateCleared() {
		_spec.ClearField(equipment.FieldInstallationDate, field.TypeTime)
	}
	if value, ok := eu.mutation.LastMaintenanceDate(); ok {
		_spec.SetField(equipment.FieldLastMaintenanceDate, field.TypeTime, value)
	}
	if eu.mutation.LastMaintenanceDateCleared() {
		_spec.ClearField(equipment.FieldLastMaintenanceDate, field.TypeTime)
	}
	if eu.mutation.BranchCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   equipment.BranchTable,
			Columns: []string{equipment.BranchColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(branch.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := eu.mutation.BranchIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   equipment.BranchTable,
			Columns: []string{equipment.BranchColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(branch.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if eu.mutation.MaintenanceOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   equipment.MaintenanceOrdersTable,
			Columns: []string{equipment.MaintenanceOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := eu.mutation.RemovedMaintenanceOrdersIDs(); len(nodes) > 0 && !eu.mutation.MaintenanceOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   equipment.MaintenanceOrdersTable,
			Columns: []string{equipment.MaintenanceOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := eu.mutation.MaintenanceOrdersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   equipment.MaintenanceOrdersTable,
			Columns: []string{equipment.MaintenanceOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, eu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{equipment.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	eu.mutation.done = true
	return n, nil
}

// EquipmentUpdateOne is the builder for updating a single Equipment entity.
type EquipmentUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *EquipmentMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (euo *EquipmentUpdateOne) SetUpdatedAt(t time.Time) *EquipmentUpdateOne {
	euo.mutation.SetUpdatedAt(t)
	return euo
}

// SetActive sets the "active" field.
func (euo *EquipmentUpdateOne) SetActive(b bool) *EquipmentUpdateOne {
	euo.mutation.SetActive(b)
	return euo
}

// SetNillableActive sets the "active" field if the given value is not nil.
func (euo *EquipmentUpdateOne) SetNillableActive(b *bool) *EquipmentUpdateOne {
	if b != nil {
		euo.SetActive(*b)
	}
	return euo
}

// SetName sets the "name" field.
func (euo *EquipmentUpdateOne) SetName(s string) *EquipmentUpdateOne {
	euo.mutation.SetName(s)
	return euo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (euo *EquipmentUpdateOne) SetNillableName(s *string) *EquipmentUpdateOne {
	if s != nil {
		euo.SetName(*s)
	}
	return euo
}

// SetType sets the "type" field.
func (euo *EquipmentUpdateOne) SetType(s string) *EquipmentUpdateOne {
	euo.mutation.SetType(s)
	return euo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (euo *EquipmentUpdateOne) SetNillableType(s *string) *EquipmentUpdateOne {
	if s != nil {
		euo.SetType(*s)
	}
	return euo
}

// SetModel sets the "model" field.
func (euo *EquipmentUpdateOne) SetModel(s string) *EquipmentUpdateOne {
	euo.mutation.SetModel(s)
	return euo
}

// SetNillableModel sets the "model" field if the given value is not nil.
func (euo *EquipmentUpdateOne) SetNillableModel(s *string) *EquipmentUpdateOne {
	if s != nil {
		euo.SetModel(*s)
	}
	return euo
}

// SetSerialNumber sets the "serial_number" field.
func (euo *EquipmentUpdateOne) SetSerialNumber(s string) *EquipmentUpdateOne {
	euo.mutation.SetSerialNumber(s)
	return euo
}

// SetNillableSerialNumber sets the "serial_number" field if the given value is not nil.
func (euo *EquipmentUpdateOne) SetNillableSerialNumber(s *string) *EquipmentUpdateOne {
	if s != nil {
		euo.SetSerialNumber(*s)
	}
	return euo
}

// SetInstallationDate sets the "installation_date" field.
func (euo *EquipmentUpdateOne) SetInstallationDate(t time.Time) *EquipmentUpdateOne {
	euo.mutation.SetInstallationDate(t)
	return euo
}

// SetNillableInstallationDate sets the "installation_date" field if the given value is not nil.
func (euo *EquipmentUpdateOne) SetNillableInstallationDate(t *time.Time) *EquipmentUpdateOne {
	if t != nil {
		euo.SetInstallationDate(*t)
	}
	return euo
}

// ClearInstallationDate clears the value of the "installation_date" field.
func (euo *EquipmentUpdateOne) ClearInstallationDate() *EquipmentUpdateOne {
	euo.mutation.ClearInstallationDate()
	return euo
}

// SetLastMaintenanceDate sets the "last_maintenance_date" field.
func (euo *EquipmentUpdateOne) SetLastMaintenanceDate(t time.Time) *EquipmentUpdateOne {
	euo.mutation.SetLastMaintenanceDate(t)
	return euo
}

// SetNillableLastMaintenanceDate sets the "last_maintenance_date" field if the given value is not nil.
func (euo *EquipmentUpdateOne) SetNillableLastMaintenanceDate(t *time.Time) *EquipmentUpdateOne {
	if t != nil {
		euo.SetLastMaintenanceDate(*t)
	}
	return euo
}

// ClearLastMaintenanceDate clears the value of the "last_maintenance_date" field.
func (euo *EquipmentUpdateOne) ClearLastMaintenanceDate() *EquipmentUpdateOne {
	euo.mutation.ClearLastMaintenanceDate()
	return euo
}

// SetBranchID sets the "branch_id" field.
func (euo *EquipmentUpdateOne) SetBranchID(i int) *EquipmentUpdateOne {
	euo.mutation.SetBranchID(i)
	return euo
}

// SetNillableBranchID sets the "branch_id" field if the given value is not nil.
func (euo *EquipmentUpdateOne) SetNillableBranchID(i *int) *EquipmentUpdateOne {
	if i != nil {
		euo.SetBranchID(*i)
	}
	return euo
}

// ClearBranchID clears the value of the "branch_id" field.
func (euo *EquipmentUpdateOne) ClearBranchID() *EquipmentUpdateOne {
	euo.mutation.ClearBranchID()
	return euo
}

// SetBranch sets the "branch" edge to the Branch entity.
func (euo *EquipmentUpdateOne) SetBranch(b *Branch) *EquipmentUpdateOne {
	return euo.SetBranchID(b.ID)
}

// AddMaintenanceOrderIDs adds the "maintenance_orders" edge to the MaintenanceOrder entity by IDs.
func (euo *EquipmentUpdateOne) AddMaintenanceOrderIDs(ids ...int) *EquipmentUpdateOne {
	euo.mutation.AddMaintenanceOrderIDs(ids...)
	return euo
}

// AddMaintenanceOrders adds the "maintenance_orders" edges to the MaintenanceOrder entity.
func (euo *EquipmentUpdateOne) AddMaintenanceOrders(m ...*MaintenanceOrder) *EquipmentUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return euo.AddMaintenanceOrderIDs(ids...)
}

// Mutation returns the EquipmentMutation object of the builder.
func (euo *EquipmentUpdateOne) Mutation() *EquipmentMutation {
	return euo.mutation
}

// ClearBranch clears the "branch" edge to the Branch entity.
func (euo *EquipmentUpdateOne) ClearBranch() *EquipmentUpdateOne {
	euo.mutation.ClearBranch()
	return euo
}

// ClearMaintenanceOrders clears all "maintenance_orders" edges to the MaintenanceOrder entity.
func (euo *EquipmentUpdateOne) ClearMaintenanceOrders() *EquipmentUpdateOne {
	euo.mutation.ClearMaintenanceOrders()
	return euo
}

// RemoveMaintenanceOrderIDs removes the "maintenance_orders" edge to MaintenanceOrder entities by IDs.
func (euo *EquipmentUpdateOne) RemoveMaintenanceOrderIDs(ids ...int) *EquipmentUpdateOne {
	euo.mutation.RemoveMaintenanceOrderIDs(ids...)
	return euo
}

// RemoveMaintenanceOrders removes "maintenance_orders" edges to MaintenanceOrder entities.
func (euo *EquipmentUpdateOne) RemoveMaintenanceOrders(m ...*MaintenanceOrder) *EquipmentUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return euo.RemoveMaintenanceOrderIDs(ids...)
}

// Where appends a list predicates to the EquipmentUpdate builder.
func (euo *EquipmentUpdateOne) Where(ps ...predicate.Equipment) *EquipmentUpdateOne {
	euo.mutation.Where(ps...)
	return euo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (euo *EquipmentUpdateOne) Select(field string, fields ...string) *EquipmentUpdateOne {
	euo.fields = append([]string{field}, fields...)
	return euo
}

// Save executes the query and returns the updated Equipment entity.
func (euo *EquipmentUpdateOne) Save(ctx context.Context) (*Equipment, error) {
	euo.defaults()
	return withHooks(ctx, euo.sqlSave, euo.mutation, euo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (euo *EquipmentUpdateOne) SaveX(ctx context.Context) *Equipment {
	node, err := euo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (euo *EquipmentUpdateOne) Exec(ctx context.Context) error {
	_, err := euo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (euo *EquipmentUpdateOne) ExecX(ctx context.Context) {
	if err := euo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (euo *EquipmentUpdateOne) defaults() {
	if _, ok := euo.mutation.UpdatedAt(); !ok {
		v := equipment.UpdateDefaultUpdatedAt()
		euo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (euo *EquipmentUpdateOne) check() error {
	if v, ok := euo.mutation.Name(); ok {
		if err := equipment.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Equipment.name": %w`, err)}
		}
	}
	if v, ok := euo.mutation.GetType(); ok {
		if err := equipment.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Equipment.type": %w`, err)}
		}
	}
	if v, ok := euo.mutation.Model(); ok {
		if err := equipment.ModelValidator(v); err != nil {
			return &ValidationError{Name: "model", err: fmt.Errorf(`ent: validator failed for field "Equipment.model": %w`, err)}
		}
	}
	if v, ok := euo.mutation.SerialNumber(); ok {
		if err := equipment.SerialNumberValidator(v); err != nil {
			return &ValidationError{Name: "serial_number", err: fmt.Errorf(`ent: validator failed for field "Equipment.serial_number": %w`, err)}
		}
	}
	return nil
}

func (euo *EquipmentUpdateOne) sqlSave(ctx context.Context) (_node *Equipment, err error) {
	if err := euo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(equipment.Table, equipment.Columns, sqlgraph.NewFieldSpec(equipment.FieldID, field.TypeInt))
	id, ok := euo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Equipment.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := euo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, equipment.FieldID)
		for _, f := range fields {
			if !equipment.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != equipment.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := euo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := euo.mutation.UpdatedAt(); ok {
		_spec.SetField(equipment.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := euo.mutation.Active(); ok {
		_spec.SetField(equipment.FieldActive, field.TypeBool, value)
	}
	if value, ok := euo.mutation.Name(); ok {
		_spec.SetField(equipment.FieldName, field.TypeString, value)
	}
	if value, ok := euo.mutation.GetType(); ok {
		_spec.SetField(equipment.FieldType, field.TypeString, value)
	}
	if value, ok := euo.mutation.Model(); ok {
		_spec.SetField(equipment.FieldModel, field.TypeString, value)
	}
	if value, ok := euo.mutation.SerialNumber(); ok {
		_spec.SetField(equipment.FieldSerialNumber, field.TypeString, value)
	}
	if value, ok := euo.mutation.InstallationDate(); ok {
		_spec.SetField(equipment.FieldInstallationDate, field.TypeTime, value)
	}
	if euo.mutation.InstallationDateCleared() {
		_spec.ClearField(equipment.FieldInstallationDate, field.TypeTime)
	}
	if value, ok := euo.mutation.LastMaintenanceDate(); ok {
		_spec.SetField(equipment.FieldLastMaintenanceDate, field.TypeTime, value)
	}
	if euo.mutation.LastMaintenanceDateCleared() {
		_spec.ClearField(equipment.FieldLastMaintenanceDate, field.TypeTime)
	}
	if euo.mutation.BranchCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   equipment.BranchTable,
			Columns: []string{equipment.BranchColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(branch.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := euo.mutation.BranchIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   equipment.BranchTable,
			Columns: []string{equipment.BranchColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(branch.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if euo.mutation.MaintenanceOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   equipment.MaintenanceOrdersTable,
			Columns: []string{equipment.MaintenanceOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := euo.mutation.RemovedMaintenanceOrdersIDs(); len(nodes) > 0 && !euo.mutation.MaintenanceOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   equipment.MaintenanceOrdersTable,
			Columns: []string{equipment.MaintenanceOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := euo.mutation.MaintenanceOrdersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   equipment.MaintenanceOrdersTable,
			Columns: []string{equipment.MaintenanceOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Equipment{config: euo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, euo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{equipment.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	euo.mutation.done = true
	return _node, nil
}
