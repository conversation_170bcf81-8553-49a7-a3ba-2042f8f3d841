// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"
	"tradicao/internal/ent/branch"
	"tradicao/internal/ent/costitem"
	"tradicao/internal/ent/equipment"
	"tradicao/internal/ent/interaction"
	"tradicao/internal/ent/maintenanceorder"
	"tradicao/internal/ent/predicate"
	"tradicao/internal/ent/user"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MaintenanceOrderUpdate is the builder for updating MaintenanceOrder entities.
type MaintenanceOrderUpdate struct {
	config
	hooks    []Hook
	mutation *MaintenanceOrderMutation
}

// Where appends a list predicates to the MaintenanceOrderUpdate builder.
func (mou *MaintenanceOrderUpdate) Where(ps ...predicate.MaintenanceOrder) *MaintenanceOrderUpdate {
	mou.mutation.Where(ps...)
	return mou
}

// SetUpdatedAt sets the "updated_at" field.
func (mou *MaintenanceOrderUpdate) SetUpdatedAt(t time.Time) *MaintenanceOrderUpdate {
	mou.mutation.SetUpdatedAt(t)
	return mou
}

// SetActive sets the "active" field.
func (mou *MaintenanceOrderUpdate) SetActive(b bool) *MaintenanceOrderUpdate {
	mou.mutation.SetActive(b)
	return mou
}

// SetNillableActive sets the "active" field if the given value is not nil.
func (mou *MaintenanceOrderUpdate) SetNillableActive(b *bool) *MaintenanceOrderUpdate {
	if b != nil {
		mou.SetActive(*b)
	}
	return mou
}

// SetTitle sets the "title" field.
func (mou *MaintenanceOrderUpdate) SetTitle(s string) *MaintenanceOrderUpdate {
	mou.mutation.SetTitle(s)
	return mou
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (mou *MaintenanceOrderUpdate) SetNillableTitle(s *string) *MaintenanceOrderUpdate {
	if s != nil {
		mou.SetTitle(*s)
	}
	return mou
}

// SetDescription sets the "description" field.
func (mou *MaintenanceOrderUpdate) SetDescription(s string) *MaintenanceOrderUpdate {
	mou.mutation.SetDescription(s)
	return mou
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (mou *MaintenanceOrderUpdate) SetNillableDescription(s *string) *MaintenanceOrderUpdate {
	if s != nil {
		mou.SetDescription(*s)
	}
	return mou
}

// SetStatus sets the "status" field.
func (mou *MaintenanceOrderUpdate) SetStatus(s string) *MaintenanceOrderUpdate {
	mou.mutation.SetStatus(s)
	return mou
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (mou *MaintenanceOrderUpdate) SetNillableStatus(s *string) *MaintenanceOrderUpdate {
	if s != nil {
		mou.SetStatus(*s)
	}
	return mou
}

// SetPriority sets the "priority" field.
func (mou *MaintenanceOrderUpdate) SetPriority(s string) *MaintenanceOrderUpdate {
	mou.mutation.SetPriority(s)
	return mou
}

// SetNillablePriority sets the "priority" field if the given value is not nil.
func (mou *MaintenanceOrderUpdate) SetNillablePriority(s *string) *MaintenanceOrderUpdate {
	if s != nil {
		mou.SetPriority(*s)
	}
	return mou
}

// SetBranchID sets the "branch_id" field.
func (mou *MaintenanceOrderUpdate) SetBranchID(i int) *MaintenanceOrderUpdate {
	mou.mutation.SetBranchID(i)
	return mou
}

// SetNillableBranchID sets the "branch_id" field if the given value is not nil.
func (mou *MaintenanceOrderUpdate) SetNillableBranchID(i *int) *MaintenanceOrderUpdate {
	if i != nil {
		mou.SetBranchID(*i)
	}
	return mou
}

// ClearBranchID clears the value of the "branch_id" field.
func (mou *MaintenanceOrderUpdate) ClearBranchID() *MaintenanceOrderUpdate {
	mou.mutation.ClearBranchID()
	return mou
}

// SetEquipmentID sets the "equipment_id" field.
func (mou *MaintenanceOrderUpdate) SetEquipmentID(i int) *MaintenanceOrderUpdate {
	mou.mutation.SetEquipmentID(i)
	return mou
}

// SetNillableEquipmentID sets the "equipment_id" field if the given value is not nil.
func (mou *MaintenanceOrderUpdate) SetNillableEquipmentID(i *int) *MaintenanceOrderUpdate {
	if i != nil {
		mou.SetEquipmentID(*i)
	}
	return mou
}

// ClearEquipmentID clears the value of the "equipment_id" field.
func (mou *MaintenanceOrderUpdate) ClearEquipmentID() *MaintenanceOrderUpdate {
	mou.mutation.ClearEquipmentID()
	return mou
}

// SetRequesterID sets the "requester_id" field.
func (mou *MaintenanceOrderUpdate) SetRequesterID(i int) *MaintenanceOrderUpdate {
	mou.mutation.SetRequesterID(i)
	return mou
}

// SetNillableRequesterID sets the "requester_id" field if the given value is not nil.
func (mou *MaintenanceOrderUpdate) SetNillableRequesterID(i *int) *MaintenanceOrderUpdate {
	if i != nil {
		mou.SetRequesterID(*i)
	}
	return mou
}

// ClearRequesterID clears the value of the "requester_id" field.
func (mou *MaintenanceOrderUpdate) ClearRequesterID() *MaintenanceOrderUpdate {
	mou.mutation.ClearRequesterID()
	return mou
}

// SetApproverID sets the "approver_id" field.
func (mou *MaintenanceOrderUpdate) SetApproverID(i int) *MaintenanceOrderUpdate {
	mou.mutation.SetApproverID(i)
	return mou
}

// SetNillableApproverID sets the "approver_id" field if the given value is not nil.
func (mou *MaintenanceOrderUpdate) SetNillableApproverID(i *int) *MaintenanceOrderUpdate {
	if i != nil {
		mou.SetApproverID(*i)
	}
	return mou
}

// ClearApproverID clears the value of the "approver_id" field.
func (mou *MaintenanceOrderUpdate) ClearApproverID() *MaintenanceOrderUpdate {
	mou.mutation.ClearApproverID()
	return mou
}

// SetTechnicianID sets the "technician_id" field.
func (mou *MaintenanceOrderUpdate) SetTechnicianID(i int) *MaintenanceOrderUpdate {
	mou.mutation.SetTechnicianID(i)
	return mou
}

// SetNillableTechnicianID sets the "technician_id" field if the given value is not nil.
func (mou *MaintenanceOrderUpdate) SetNillableTechnicianID(i *int) *MaintenanceOrderUpdate {
	if i != nil {
		mou.SetTechnicianID(*i)
	}
	return mou
}

// ClearTechnicianID clears the value of the "technician_id" field.
func (mou *MaintenanceOrderUpdate) ClearTechnicianID() *MaintenanceOrderUpdate {
	mou.mutation.ClearTechnicianID()
	return mou
}

// SetCancellationReason sets the "cancellation_reason" field.
func (mou *MaintenanceOrderUpdate) SetCancellationReason(s string) *MaintenanceOrderUpdate {
	mou.mutation.SetCancellationReason(s)
	return mou
}

// SetNillableCancellationReason sets the "cancellation_reason" field if the given value is not nil.
func (mou *MaintenanceOrderUpdate) SetNillableCancellationReason(s *string) *MaintenanceOrderUpdate {
	if s != nil {
		mou.SetCancellationReason(*s)
	}
	return mou
}

// ClearCancellationReason clears the value of the "cancellation_reason" field.
func (mou *MaintenanceOrderUpdate) ClearCancellationReason() *MaintenanceOrderUpdate {
	mou.mutation.ClearCancellationReason()
	return mou
}

// SetRejectionReason sets the "rejection_reason" field.
func (mou *MaintenanceOrderUpdate) SetRejectionReason(s string) *MaintenanceOrderUpdate {
	mou.mutation.SetRejectionReason(s)
	return mou
}

// SetNillableRejectionReason sets the "rejection_reason" field if the given value is not nil.
func (mou *MaintenanceOrderUpdate) SetNillableRejectionReason(s *string) *MaintenanceOrderUpdate {
	if s != nil {
		mou.SetRejectionReason(*s)
	}
	return mou
}

// ClearRejectionReason clears the value of the "rejection_reason" field.
func (mou *MaintenanceOrderUpdate) ClearRejectionReason() *MaintenanceOrderUpdate {
	mou.mutation.ClearRejectionReason()
	return mou
}

// SetBranch sets the "branch" edge to the Branch entity.
func (mou *MaintenanceOrderUpdate) SetBranch(b *Branch) *MaintenanceOrderUpdate {
	return mou.SetBranchID(b.ID)
}

// SetEquipment sets the "equipment" edge to the Equipment entity.
func (mou *MaintenanceOrderUpdate) SetEquipment(e *Equipment) *MaintenanceOrderUpdate {
	return mou.SetEquipmentID(e.ID)
}

// SetRequester sets the "requester" edge to the User entity.
func (mou *MaintenanceOrderUpdate) SetRequester(u *User) *MaintenanceOrderUpdate {
	return mou.SetRequesterID(u.ID)
}

// SetApprover sets the "approver" edge to the User entity.
func (mou *MaintenanceOrderUpdate) SetApprover(u *User) *MaintenanceOrderUpdate {
	return mou.SetApproverID(u.ID)
}

// SetTechnician sets the "technician" edge to the User entity.
func (mou *MaintenanceOrderUpdate) SetTechnician(u *User) *MaintenanceOrderUpdate {
	return mou.SetTechnicianID(u.ID)
}

// AddCostItemIDs adds the "cost_items" edge to the CostItem entity by IDs.
func (mou *MaintenanceOrderUpdate) AddCostItemIDs(ids ...int) *MaintenanceOrderUpdate {
	mou.mutation.AddCostItemIDs(ids...)
	return mou
}

// AddCostItems adds the "cost_items" edges to the CostItem entity.
func (mou *MaintenanceOrderUpdate) AddCostItems(c ...*CostItem) *MaintenanceOrderUpdate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return mou.AddCostItemIDs(ids...)
}

// AddInteractionIDs adds the "interactions" edge to the Interaction entity by IDs.
func (mou *MaintenanceOrderUpdate) AddInteractionIDs(ids ...int) *MaintenanceOrderUpdate {
	mou.mutation.AddInteractionIDs(ids...)
	return mou
}

// AddInteractions adds the "interactions" edges to the Interaction entity.
func (mou *MaintenanceOrderUpdate) AddInteractions(i ...*Interaction) *MaintenanceOrderUpdate {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return mou.AddInteractionIDs(ids...)
}

// Mutation returns the MaintenanceOrderMutation object of the builder.
func (mou *MaintenanceOrderUpdate) Mutation() *MaintenanceOrderMutation {
	return mou.mutation
}

// ClearBranch clears the "branch" edge to the Branch entity.
func (mou *MaintenanceOrderUpdate) ClearBranch() *MaintenanceOrderUpdate {
	mou.mutation.ClearBranch()
	return mou
}

// ClearEquipment clears the "equipment" edge to the Equipment entity.
func (mou *MaintenanceOrderUpdate) ClearEquipment() *MaintenanceOrderUpdate {
	mou.mutation.ClearEquipment()
	return mou
}

// ClearRequester clears the "requester" edge to the User entity.
func (mou *MaintenanceOrderUpdate) ClearRequester() *MaintenanceOrderUpdate {
	mou.mutation.ClearRequester()
	return mou
}

// ClearApprover clears the "approver" edge to the User entity.
func (mou *MaintenanceOrderUpdate) ClearApprover() *MaintenanceOrderUpdate {
	mou.mutation.ClearApprover()
	return mou
}

// ClearTechnician clears the "technician" edge to the User entity.
func (mou *MaintenanceOrderUpdate) ClearTechnician() *MaintenanceOrderUpdate {
	mou.mutation.ClearTechnician()
	return mou
}

// ClearCostItems clears all "cost_items" edges to the CostItem entity.
func (mou *MaintenanceOrderUpdate) ClearCostItems() *MaintenanceOrderUpdate {
	mou.mutation.ClearCostItems()
	return mou
}

// RemoveCostItemIDs removes the "cost_items" edge to CostItem entities by IDs.
func (mou *MaintenanceOrderUpdate) RemoveCostItemIDs(ids ...int) *MaintenanceOrderUpdate {
	mou.mutation.RemoveCostItemIDs(ids...)
	return mou
}

// RemoveCostItems removes "cost_items" edges to CostItem entities.
func (mou *MaintenanceOrderUpdate) RemoveCostItems(c ...*CostItem) *MaintenanceOrderUpdate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return mou.RemoveCostItemIDs(ids...)
}

// ClearInteractions clears all "interactions" edges to the Interaction entity.
func (mou *MaintenanceOrderUpdate) ClearInteractions() *MaintenanceOrderUpdate {
	mou.mutation.ClearInteractions()
	return mou
}

// RemoveInteractionIDs removes the "interactions" edge to Interaction entities by IDs.
func (mou *MaintenanceOrderUpdate) RemoveInteractionIDs(ids ...int) *MaintenanceOrderUpdate {
	mou.mutation.RemoveInteractionIDs(ids...)
	return mou
}

// RemoveInteractions removes "interactions" edges to Interaction entities.
func (mou *MaintenanceOrderUpdate) RemoveInteractions(i ...*Interaction) *MaintenanceOrderUpdate {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return mou.RemoveInteractionIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (mou *MaintenanceOrderUpdate) Save(ctx context.Context) (int, error) {
	mou.defaults()
	return withHooks(ctx, mou.sqlSave, mou.mutation, mou.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (mou *MaintenanceOrderUpdate) SaveX(ctx context.Context) int {
	affected, err := mou.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (mou *MaintenanceOrderUpdate) Exec(ctx context.Context) error {
	_, err := mou.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (mou *MaintenanceOrderUpdate) ExecX(ctx context.Context) {
	if err := mou.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (mou *MaintenanceOrderUpdate) defaults() {
	if _, ok := mou.mutation.UpdatedAt(); !ok {
		v := maintenanceorder.UpdateDefaultUpdatedAt()
		mou.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (mou *MaintenanceOrderUpdate) check() error {
	if v, ok := mou.mutation.Title(); ok {
		if err := maintenanceorder.TitleValidator(v); err != nil {
			return &ValidationError{Name: "title", err: fmt.Errorf(`ent: validator failed for field "MaintenanceOrder.title": %w`, err)}
		}
	}
	if v, ok := mou.mutation.Description(); ok {
		if err := maintenanceorder.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "MaintenanceOrder.description": %w`, err)}
		}
	}
	return nil
}

func (mou *MaintenanceOrderUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := mou.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(maintenanceorder.Table, maintenanceorder.Columns, sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt))
	if ps := mou.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := mou.mutation.UpdatedAt(); ok {
		_spec.SetField(maintenanceorder.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := mou.mutation.Active(); ok {
		_spec.SetField(maintenanceorder.FieldActive, field.TypeBool, value)
	}
	if value, ok := mou.mutation.Title(); ok {
		_spec.SetField(maintenanceorder.FieldTitle, field.TypeString, value)
	}
	if value, ok := mou.mutation.Description(); ok {
		_spec.SetField(maintenanceorder.FieldDescription, field.TypeString, value)
	}
	if value, ok := mou.mutation.Status(); ok {
		_spec.SetField(maintenanceorder.FieldStatus, field.TypeString, value)
	}
	if value, ok := mou.mutation.Priority(); ok {
		_spec.SetField(maintenanceorder.FieldPriority, field.TypeString, value)
	}
	if value, ok := mou.mutation.CancellationReason(); ok {
		_spec.SetField(maintenanceorder.FieldCancellationReason, field.TypeString, value)
	}
	if mou.mutation.CancellationReasonCleared() {
		_spec.ClearField(maintenanceorder.FieldCancellationReason, field.TypeString)
	}
	if value, ok := mou.mutation.RejectionReason(); ok {
		_spec.SetField(maintenanceorder.FieldRejectionReason, field.TypeString, value)
	}
	if mou.mutation.RejectionReasonCleared() {
		_spec.ClearField(maintenanceorder.FieldRejectionReason, field.TypeString)
	}
	if mou.mutation.BranchCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.BranchTable,
			Columns: []string{maintenanceorder.BranchColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(branch.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mou.mutation.BranchIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.BranchTable,
			Columns: []string{maintenanceorder.BranchColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(branch.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if mou.mutation.EquipmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.EquipmentTable,
			Columns: []string{maintenanceorder.EquipmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(equipment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mou.mutation.EquipmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.EquipmentTable,
			Columns: []string{maintenanceorder.EquipmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(equipment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if mou.mutation.RequesterCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.RequesterTable,
			Columns: []string{maintenanceorder.RequesterColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mou.mutation.RequesterIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.RequesterTable,
			Columns: []string{maintenanceorder.RequesterColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if mou.mutation.ApproverCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.ApproverTable,
			Columns: []string{maintenanceorder.ApproverColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mou.mutation.ApproverIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.ApproverTable,
			Columns: []string{maintenanceorder.ApproverColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if mou.mutation.TechnicianCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.TechnicianTable,
			Columns: []string{maintenanceorder.TechnicianColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mou.mutation.TechnicianIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.TechnicianTable,
			Columns: []string{maintenanceorder.TechnicianColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if mou.mutation.CostItemsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   maintenanceorder.CostItemsTable,
			Columns: []string{maintenanceorder.CostItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(costitem.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mou.mutation.RemovedCostItemsIDs(); len(nodes) > 0 && !mou.mutation.CostItemsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   maintenanceorder.CostItemsTable,
			Columns: []string{maintenanceorder.CostItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(costitem.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mou.mutation.CostItemsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   maintenanceorder.CostItemsTable,
			Columns: []string{maintenanceorder.CostItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(costitem.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if mou.mutation.InteractionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   maintenanceorder.InteractionsTable,
			Columns: []string{maintenanceorder.InteractionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(interaction.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mou.mutation.RemovedInteractionsIDs(); len(nodes) > 0 && !mou.mutation.InteractionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   maintenanceorder.InteractionsTable,
			Columns: []string{maintenanceorder.InteractionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(interaction.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mou.mutation.InteractionsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   maintenanceorder.InteractionsTable,
			Columns: []string{maintenanceorder.InteractionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(interaction.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, mou.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{maintenanceorder.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	mou.mutation.done = true
	return n, nil
}

// MaintenanceOrderUpdateOne is the builder for updating a single MaintenanceOrder entity.
type MaintenanceOrderUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *MaintenanceOrderMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (mouo *MaintenanceOrderUpdateOne) SetUpdatedAt(t time.Time) *MaintenanceOrderUpdateOne {
	mouo.mutation.SetUpdatedAt(t)
	return mouo
}

// SetActive sets the "active" field.
func (mouo *MaintenanceOrderUpdateOne) SetActive(b bool) *MaintenanceOrderUpdateOne {
	mouo.mutation.SetActive(b)
	return mouo
}

// SetNillableActive sets the "active" field if the given value is not nil.
func (mouo *MaintenanceOrderUpdateOne) SetNillableActive(b *bool) *MaintenanceOrderUpdateOne {
	if b != nil {
		mouo.SetActive(*b)
	}
	return mouo
}

// SetTitle sets the "title" field.
func (mouo *MaintenanceOrderUpdateOne) SetTitle(s string) *MaintenanceOrderUpdateOne {
	mouo.mutation.SetTitle(s)
	return mouo
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (mouo *MaintenanceOrderUpdateOne) SetNillableTitle(s *string) *MaintenanceOrderUpdateOne {
	if s != nil {
		mouo.SetTitle(*s)
	}
	return mouo
}

// SetDescription sets the "description" field.
func (mouo *MaintenanceOrderUpdateOne) SetDescription(s string) *MaintenanceOrderUpdateOne {
	mouo.mutation.SetDescription(s)
	return mouo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (mouo *MaintenanceOrderUpdateOne) SetNillableDescription(s *string) *MaintenanceOrderUpdateOne {
	if s != nil {
		mouo.SetDescription(*s)
	}
	return mouo
}

// SetStatus sets the "status" field.
func (mouo *MaintenanceOrderUpdateOne) SetStatus(s string) *MaintenanceOrderUpdateOne {
	mouo.mutation.SetStatus(s)
	return mouo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (mouo *MaintenanceOrderUpdateOne) SetNillableStatus(s *string) *MaintenanceOrderUpdateOne {
	if s != nil {
		mouo.SetStatus(*s)
	}
	return mouo
}

// SetPriority sets the "priority" field.
func (mouo *MaintenanceOrderUpdateOne) SetPriority(s string) *MaintenanceOrderUpdateOne {
	mouo.mutation.SetPriority(s)
	return mouo
}

// SetNillablePriority sets the "priority" field if the given value is not nil.
func (mouo *MaintenanceOrderUpdateOne) SetNillablePriority(s *string) *MaintenanceOrderUpdateOne {
	if s != nil {
		mouo.SetPriority(*s)
	}
	return mouo
}

// SetBranchID sets the "branch_id" field.
func (mouo *MaintenanceOrderUpdateOne) SetBranchID(i int) *MaintenanceOrderUpdateOne {
	mouo.mutation.SetBranchID(i)
	return mouo
}

// SetNillableBranchID sets the "branch_id" field if the given value is not nil.
func (mouo *MaintenanceOrderUpdateOne) SetNillableBranchID(i *int) *MaintenanceOrderUpdateOne {
	if i != nil {
		mouo.SetBranchID(*i)
	}
	return mouo
}

// ClearBranchID clears the value of the "branch_id" field.
func (mouo *MaintenanceOrderUpdateOne) ClearBranchID() *MaintenanceOrderUpdateOne {
	mouo.mutation.ClearBranchID()
	return mouo
}

// SetEquipmentID sets the "equipment_id" field.
func (mouo *MaintenanceOrderUpdateOne) SetEquipmentID(i int) *MaintenanceOrderUpdateOne {
	mouo.mutation.SetEquipmentID(i)
	return mouo
}

// SetNillableEquipmentID sets the "equipment_id" field if the given value is not nil.
func (mouo *MaintenanceOrderUpdateOne) SetNillableEquipmentID(i *int) *MaintenanceOrderUpdateOne {
	if i != nil {
		mouo.SetEquipmentID(*i)
	}
	return mouo
}

// ClearEquipmentID clears the value of the "equipment_id" field.
func (mouo *MaintenanceOrderUpdateOne) ClearEquipmentID() *MaintenanceOrderUpdateOne {
	mouo.mutation.ClearEquipmentID()
	return mouo
}

// SetRequesterID sets the "requester_id" field.
func (mouo *MaintenanceOrderUpdateOne) SetRequesterID(i int) *MaintenanceOrderUpdateOne {
	mouo.mutation.SetRequesterID(i)
	return mouo
}

// SetNillableRequesterID sets the "requester_id" field if the given value is not nil.
func (mouo *MaintenanceOrderUpdateOne) SetNillableRequesterID(i *int) *MaintenanceOrderUpdateOne {
	if i != nil {
		mouo.SetRequesterID(*i)
	}
	return mouo
}

// ClearRequesterID clears the value of the "requester_id" field.
func (mouo *MaintenanceOrderUpdateOne) ClearRequesterID() *MaintenanceOrderUpdateOne {
	mouo.mutation.ClearRequesterID()
	return mouo
}

// SetApproverID sets the "approver_id" field.
func (mouo *MaintenanceOrderUpdateOne) SetApproverID(i int) *MaintenanceOrderUpdateOne {
	mouo.mutation.SetApproverID(i)
	return mouo
}

// SetNillableApproverID sets the "approver_id" field if the given value is not nil.
func (mouo *MaintenanceOrderUpdateOne) SetNillableApproverID(i *int) *MaintenanceOrderUpdateOne {
	if i != nil {
		mouo.SetApproverID(*i)
	}
	return mouo
}

// ClearApproverID clears the value of the "approver_id" field.
func (mouo *MaintenanceOrderUpdateOne) ClearApproverID() *MaintenanceOrderUpdateOne {
	mouo.mutation.ClearApproverID()
	return mouo
}

// SetTechnicianID sets the "technician_id" field.
func (mouo *MaintenanceOrderUpdateOne) SetTechnicianID(i int) *MaintenanceOrderUpdateOne {
	mouo.mutation.SetTechnicianID(i)
	return mouo
}

// SetNillableTechnicianID sets the "technician_id" field if the given value is not nil.
func (mouo *MaintenanceOrderUpdateOne) SetNillableTechnicianID(i *int) *MaintenanceOrderUpdateOne {
	if i != nil {
		mouo.SetTechnicianID(*i)
	}
	return mouo
}

// ClearTechnicianID clears the value of the "technician_id" field.
func (mouo *MaintenanceOrderUpdateOne) ClearTechnicianID() *MaintenanceOrderUpdateOne {
	mouo.mutation.ClearTechnicianID()
	return mouo
}

// SetCancellationReason sets the "cancellation_reason" field.
func (mouo *MaintenanceOrderUpdateOne) SetCancellationReason(s string) *MaintenanceOrderUpdateOne {
	mouo.mutation.SetCancellationReason(s)
	return mouo
}

// SetNillableCancellationReason sets the "cancellation_reason" field if the given value is not nil.
func (mouo *MaintenanceOrderUpdateOne) SetNillableCancellationReason(s *string) *MaintenanceOrderUpdateOne {
	if s != nil {
		mouo.SetCancellationReason(*s)
	}
	return mouo
}

// ClearCancellationReason clears the value of the "cancellation_reason" field.
func (mouo *MaintenanceOrderUpdateOne) ClearCancellationReason() *MaintenanceOrderUpdateOne {
	mouo.mutation.ClearCancellationReason()
	return mouo
}

// SetRejectionReason sets the "rejection_reason" field.
func (mouo *MaintenanceOrderUpdateOne) SetRejectionReason(s string) *MaintenanceOrderUpdateOne {
	mouo.mutation.SetRejectionReason(s)
	return mouo
}

// SetNillableRejectionReason sets the "rejection_reason" field if the given value is not nil.
func (mouo *MaintenanceOrderUpdateOne) SetNillableRejectionReason(s *string) *MaintenanceOrderUpdateOne {
	if s != nil {
		mouo.SetRejectionReason(*s)
	}
	return mouo
}

// ClearRejectionReason clears the value of the "rejection_reason" field.
func (mouo *MaintenanceOrderUpdateOne) ClearRejectionReason() *MaintenanceOrderUpdateOne {
	mouo.mutation.ClearRejectionReason()
	return mouo
}

// SetBranch sets the "branch" edge to the Branch entity.
func (mouo *MaintenanceOrderUpdateOne) SetBranch(b *Branch) *MaintenanceOrderUpdateOne {
	return mouo.SetBranchID(b.ID)
}

// SetEquipment sets the "equipment" edge to the Equipment entity.
func (mouo *MaintenanceOrderUpdateOne) SetEquipment(e *Equipment) *MaintenanceOrderUpdateOne {
	return mouo.SetEquipmentID(e.ID)
}

// SetRequester sets the "requester" edge to the User entity.
func (mouo *MaintenanceOrderUpdateOne) SetRequester(u *User) *MaintenanceOrderUpdateOne {
	return mouo.SetRequesterID(u.ID)
}

// SetApprover sets the "approver" edge to the User entity.
func (mouo *MaintenanceOrderUpdateOne) SetApprover(u *User) *MaintenanceOrderUpdateOne {
	return mouo.SetApproverID(u.ID)
}

// SetTechnician sets the "technician" edge to the User entity.
func (mouo *MaintenanceOrderUpdateOne) SetTechnician(u *User) *MaintenanceOrderUpdateOne {
	return mouo.SetTechnicianID(u.ID)
}

// AddCostItemIDs adds the "cost_items" edge to the CostItem entity by IDs.
func (mouo *MaintenanceOrderUpdateOne) AddCostItemIDs(ids ...int) *MaintenanceOrderUpdateOne {
	mouo.mutation.AddCostItemIDs(ids...)
	return mouo
}

// AddCostItems adds the "cost_items" edges to the CostItem entity.
func (mouo *MaintenanceOrderUpdateOne) AddCostItems(c ...*CostItem) *MaintenanceOrderUpdateOne {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return mouo.AddCostItemIDs(ids...)
}

// AddInteractionIDs adds the "interactions" edge to the Interaction entity by IDs.
func (mouo *MaintenanceOrderUpdateOne) AddInteractionIDs(ids ...int) *MaintenanceOrderUpdateOne {
	mouo.mutation.AddInteractionIDs(ids...)
	return mouo
}

// AddInteractions adds the "interactions" edges to the Interaction entity.
func (mouo *MaintenanceOrderUpdateOne) AddInteractions(i ...*Interaction) *MaintenanceOrderUpdateOne {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return mouo.AddInteractionIDs(ids...)
}

// Mutation returns the MaintenanceOrderMutation object of the builder.
func (mouo *MaintenanceOrderUpdateOne) Mutation() *MaintenanceOrderMutation {
	return mouo.mutation
}

// ClearBranch clears the "branch" edge to the Branch entity.
func (mouo *MaintenanceOrderUpdateOne) ClearBranch() *MaintenanceOrderUpdateOne {
	mouo.mutation.ClearBranch()
	return mouo
}

// ClearEquipment clears the "equipment" edge to the Equipment entity.
func (mouo *MaintenanceOrderUpdateOne) ClearEquipment() *MaintenanceOrderUpdateOne {
	mouo.mutation.ClearEquipment()
	return mouo
}

// ClearRequester clears the "requester" edge to the User entity.
func (mouo *MaintenanceOrderUpdateOne) ClearRequester() *MaintenanceOrderUpdateOne {
	mouo.mutation.ClearRequester()
	return mouo
}

// ClearApprover clears the "approver" edge to the User entity.
func (mouo *MaintenanceOrderUpdateOne) ClearApprover() *MaintenanceOrderUpdateOne {
	mouo.mutation.ClearApprover()
	return mouo
}

// ClearTechnician clears the "technician" edge to the User entity.
func (mouo *MaintenanceOrderUpdateOne) ClearTechnician() *MaintenanceOrderUpdateOne {
	mouo.mutation.ClearTechnician()
	return mouo
}

// ClearCostItems clears all "cost_items" edges to the CostItem entity.
func (mouo *MaintenanceOrderUpdateOne) ClearCostItems() *MaintenanceOrderUpdateOne {
	mouo.mutation.ClearCostItems()
	return mouo
}

// RemoveCostItemIDs removes the "cost_items" edge to CostItem entities by IDs.
func (mouo *MaintenanceOrderUpdateOne) RemoveCostItemIDs(ids ...int) *MaintenanceOrderUpdateOne {
	mouo.mutation.RemoveCostItemIDs(ids...)
	return mouo
}

// RemoveCostItems removes "cost_items" edges to CostItem entities.
func (mouo *MaintenanceOrderUpdateOne) RemoveCostItems(c ...*CostItem) *MaintenanceOrderUpdateOne {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return mouo.RemoveCostItemIDs(ids...)
}

// ClearInteractions clears all "interactions" edges to the Interaction entity.
func (mouo *MaintenanceOrderUpdateOne) ClearInteractions() *MaintenanceOrderUpdateOne {
	mouo.mutation.ClearInteractions()
	return mouo
}

// RemoveInteractionIDs removes the "interactions" edge to Interaction entities by IDs.
func (mouo *MaintenanceOrderUpdateOne) RemoveInteractionIDs(ids ...int) *MaintenanceOrderUpdateOne {
	mouo.mutation.RemoveInteractionIDs(ids...)
	return mouo
}

// RemoveInteractions removes "interactions" edges to Interaction entities.
func (mouo *MaintenanceOrderUpdateOne) RemoveInteractions(i ...*Interaction) *MaintenanceOrderUpdateOne {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return mouo.RemoveInteractionIDs(ids...)
}

// Where appends a list predicates to the MaintenanceOrderUpdate builder.
func (mouo *MaintenanceOrderUpdateOne) Where(ps ...predicate.MaintenanceOrder) *MaintenanceOrderUpdateOne {
	mouo.mutation.Where(ps...)
	return mouo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (mouo *MaintenanceOrderUpdateOne) Select(field string, fields ...string) *MaintenanceOrderUpdateOne {
	mouo.fields = append([]string{field}, fields...)
	return mouo
}

// Save executes the query and returns the updated MaintenanceOrder entity.
func (mouo *MaintenanceOrderUpdateOne) Save(ctx context.Context) (*MaintenanceOrder, error) {
	mouo.defaults()
	return withHooks(ctx, mouo.sqlSave, mouo.mutation, mouo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (mouo *MaintenanceOrderUpdateOne) SaveX(ctx context.Context) *MaintenanceOrder {
	node, err := mouo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (mouo *MaintenanceOrderUpdateOne) Exec(ctx context.Context) error {
	_, err := mouo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (mouo *MaintenanceOrderUpdateOne) ExecX(ctx context.Context) {
	if err := mouo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (mouo *MaintenanceOrderUpdateOne) defaults() {
	if _, ok := mouo.mutation.UpdatedAt(); !ok {
		v := maintenanceorder.UpdateDefaultUpdatedAt()
		mouo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (mouo *MaintenanceOrderUpdateOne) check() error {
	if v, ok := mouo.mutation.Title(); ok {
		if err := maintenanceorder.TitleValidator(v); err != nil {
			return &ValidationError{Name: "title", err: fmt.Errorf(`ent: validator failed for field "MaintenanceOrder.title": %w`, err)}
		}
	}
	if v, ok := mouo.mutation.Description(); ok {
		if err := maintenanceorder.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "MaintenanceOrder.description": %w`, err)}
		}
	}
	return nil
}

func (mouo *MaintenanceOrderUpdateOne) sqlSave(ctx context.Context) (_node *MaintenanceOrder, err error) {
	if err := mouo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(maintenanceorder.Table, maintenanceorder.Columns, sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt))
	id, ok := mouo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "MaintenanceOrder.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := mouo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, maintenanceorder.FieldID)
		for _, f := range fields {
			if !maintenanceorder.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != maintenanceorder.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := mouo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := mouo.mutation.UpdatedAt(); ok {
		_spec.SetField(maintenanceorder.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := mouo.mutation.Active(); ok {
		_spec.SetField(maintenanceorder.FieldActive, field.TypeBool, value)
	}
	if value, ok := mouo.mutation.Title(); ok {
		_spec.SetField(maintenanceorder.FieldTitle, field.TypeString, value)
	}
	if value, ok := mouo.mutation.Description(); ok {
		_spec.SetField(maintenanceorder.FieldDescription, field.TypeString, value)
	}
	if value, ok := mouo.mutation.Status(); ok {
		_spec.SetField(maintenanceorder.FieldStatus, field.TypeString, value)
	}
	if value, ok := mouo.mutation.Priority(); ok {
		_spec.SetField(maintenanceorder.FieldPriority, field.TypeString, value)
	}
	if value, ok := mouo.mutation.CancellationReason(); ok {
		_spec.SetField(maintenanceorder.FieldCancellationReason, field.TypeString, value)
	}
	if mouo.mutation.CancellationReasonCleared() {
		_spec.ClearField(maintenanceorder.FieldCancellationReason, field.TypeString)
	}
	if value, ok := mouo.mutation.RejectionReason(); ok {
		_spec.SetField(maintenanceorder.FieldRejectionReason, field.TypeString, value)
	}
	if mouo.mutation.RejectionReasonCleared() {
		_spec.ClearField(maintenanceorder.FieldRejectionReason, field.TypeString)
	}
	if mouo.mutation.BranchCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.BranchTable,
			Columns: []string{maintenanceorder.BranchColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(branch.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mouo.mutation.BranchIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.BranchTable,
			Columns: []string{maintenanceorder.BranchColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(branch.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if mouo.mutation.EquipmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.EquipmentTable,
			Columns: []string{maintenanceorder.EquipmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(equipment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mouo.mutation.EquipmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.EquipmentTable,
			Columns: []string{maintenanceorder.EquipmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(equipment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if mouo.mutation.RequesterCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.RequesterTable,
			Columns: []string{maintenanceorder.RequesterColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mouo.mutation.RequesterIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.RequesterTable,
			Columns: []string{maintenanceorder.RequesterColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if mouo.mutation.ApproverCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.ApproverTable,
			Columns: []string{maintenanceorder.ApproverColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mouo.mutation.ApproverIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.ApproverTable,
			Columns: []string{maintenanceorder.ApproverColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if mouo.mutation.TechnicianCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.TechnicianTable,
			Columns: []string{maintenanceorder.TechnicianColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mouo.mutation.TechnicianIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.TechnicianTable,
			Columns: []string{maintenanceorder.TechnicianColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if mouo.mutation.CostItemsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   maintenanceorder.CostItemsTable,
			Columns: []string{maintenanceorder.CostItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(costitem.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mouo.mutation.RemovedCostItemsIDs(); len(nodes) > 0 && !mouo.mutation.CostItemsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   maintenanceorder.CostItemsTable,
			Columns: []string{maintenanceorder.CostItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(costitem.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mouo.mutation.CostItemsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   maintenanceorder.CostItemsTable,
			Columns: []string{maintenanceorder.CostItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(costitem.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if mouo.mutation.InteractionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   maintenanceorder.InteractionsTable,
			Columns: []string{maintenanceorder.InteractionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(interaction.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mouo.mutation.RemovedInteractionsIDs(); len(nodes) > 0 && !mouo.mutation.InteractionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   maintenanceorder.InteractionsTable,
			Columns: []string{maintenanceorder.InteractionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(interaction.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mouo.mutation.InteractionsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   maintenanceorder.InteractionsTable,
			Columns: []string{maintenanceorder.InteractionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(interaction.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &MaintenanceOrder{config: mouo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, mouo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{maintenanceorder.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	mouo.mutation.done = true
	return _node, nil
}
