// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"
	"tradicao/internal/ent/branch"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Branch is the model entity for the Branch schema.
type Branch struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// Active holds the value of the "active" field.
	Active bool `json:"active,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// Code holds the value of the "code" field.
	Code string `json:"code,omitempty"`
	// Address holds the value of the "address" field.
	Address string `json:"address,omitempty"`
	// City holds the value of the "city" field.
	City string `json:"city,omitempty"`
	// State holds the value of the "state" field.
	State string `json:"state,omitempty"`
	// ZipCode holds the value of the "zip_code" field.
	ZipCode string `json:"zip_code,omitempty"`
	// Phone holds the value of the "phone" field.
	Phone string `json:"phone,omitempty"`
	// Email holds the value of the "email" field.
	Email string `json:"email,omitempty"`
	// Status holds the value of the "status" field.
	Status string `json:"status,omitempty"`
	// InaugurationDate holds the value of the "inauguration_date" field.
	InaugurationDate time.Time `json:"inauguration_date,omitempty"`
	// IsActive holds the value of the "is_active" field.
	IsActive bool `json:"is_active,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the BranchQuery when eager-loading is set.
	Edges                 BranchEdges `json:"edges"`
	user_managed_branches *int
	selectValues          sql.SelectValues
}

// BranchEdges holds the relations/edges for other nodes in the graph.
type BranchEdges struct {
	// Equipment holds the value of the equipment edge.
	Equipment []*Equipment `json:"equipment,omitempty"`
	// MaintenanceOrders holds the value of the maintenance_orders edge.
	MaintenanceOrders []*MaintenanceOrder `json:"maintenance_orders,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// EquipmentOrErr returns the Equipment value or an error if the edge
// was not loaded in eager-loading.
func (e BranchEdges) EquipmentOrErr() ([]*Equipment, error) {
	if e.loadedTypes[0] {
		return e.Equipment, nil
	}
	return nil, &NotLoadedError{edge: "equipment"}
}

// MaintenanceOrdersOrErr returns the MaintenanceOrders value or an error if the edge
// was not loaded in eager-loading.
func (e BranchEdges) MaintenanceOrdersOrErr() ([]*MaintenanceOrder, error) {
	if e.loadedTypes[1] {
		return e.MaintenanceOrders, nil
	}
	return nil, &NotLoadedError{edge: "maintenance_orders"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Branch) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case branch.FieldActive, branch.FieldIsActive:
			values[i] = new(sql.NullBool)
		case branch.FieldID:
			values[i] = new(sql.NullInt64)
		case branch.FieldName, branch.FieldCode, branch.FieldAddress, branch.FieldCity, branch.FieldState, branch.FieldZipCode, branch.FieldPhone, branch.FieldEmail, branch.FieldStatus:
			values[i] = new(sql.NullString)
		case branch.FieldCreatedAt, branch.FieldUpdatedAt, branch.FieldInaugurationDate:
			values[i] = new(sql.NullTime)
		case branch.ForeignKeys[0]: // user_managed_branches
			values[i] = new(sql.NullInt64)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Branch fields.
func (b *Branch) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case branch.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			b.ID = int(value.Int64)
		case branch.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				b.CreatedAt = value.Time
			}
		case branch.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				b.UpdatedAt = value.Time
			}
		case branch.FieldActive:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field active", values[i])
			} else if value.Valid {
				b.Active = value.Bool
			}
		case branch.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				b.Name = value.String
			}
		case branch.FieldCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field code", values[i])
			} else if value.Valid {
				b.Code = value.String
			}
		case branch.FieldAddress:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field address", values[i])
			} else if value.Valid {
				b.Address = value.String
			}
		case branch.FieldCity:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field city", values[i])
			} else if value.Valid {
				b.City = value.String
			}
		case branch.FieldState:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field state", values[i])
			} else if value.Valid {
				b.State = value.String
			}
		case branch.FieldZipCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field zip_code", values[i])
			} else if value.Valid {
				b.ZipCode = value.String
			}
		case branch.FieldPhone:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field phone", values[i])
			} else if value.Valid {
				b.Phone = value.String
			}
		case branch.FieldEmail:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field email", values[i])
			} else if value.Valid {
				b.Email = value.String
			}
		case branch.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				b.Status = value.String
			}
		case branch.FieldInaugurationDate:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field inauguration_date", values[i])
			} else if value.Valid {
				b.InaugurationDate = value.Time
			}
		case branch.FieldIsActive:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_active", values[i])
			} else if value.Valid {
				b.IsActive = value.Bool
			}
		case branch.ForeignKeys[0]:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for edge-field user_managed_branches", value)
			} else if value.Valid {
				b.user_managed_branches = new(int)
				*b.user_managed_branches = int(value.Int64)
			}
		default:
			b.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Branch.
// This includes values selected through modifiers, order, etc.
func (b *Branch) Value(name string) (ent.Value, error) {
	return b.selectValues.Get(name)
}

// QueryEquipment queries the "equipment" edge of the Branch entity.
func (b *Branch) QueryEquipment() *EquipmentQuery {
	return NewBranchClient(b.config).QueryEquipment(b)
}

// QueryMaintenanceOrders queries the "maintenance_orders" edge of the Branch entity.
func (b *Branch) QueryMaintenanceOrders() *MaintenanceOrderQuery {
	return NewBranchClient(b.config).QueryMaintenanceOrders(b)
}

// Update returns a builder for updating this Branch.
// Note that you need to call Branch.Unwrap() before calling this method if this Branch
// was returned from a transaction, and the transaction was committed or rolled back.
func (b *Branch) Update() *BranchUpdateOne {
	return NewBranchClient(b.config).UpdateOne(b)
}

// Unwrap unwraps the Branch entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (b *Branch) Unwrap() *Branch {
	_tx, ok := b.config.driver.(*txDriver)
	if !ok {
		panic("ent: Branch is not a transactional entity")
	}
	b.config.driver = _tx.drv
	return b
}

// String implements the fmt.Stringer.
func (b *Branch) String() string {
	var builder strings.Builder
	builder.WriteString("Branch(")
	builder.WriteString(fmt.Sprintf("id=%v, ", b.ID))
	builder.WriteString("created_at=")
	builder.WriteString(b.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(b.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("active=")
	builder.WriteString(fmt.Sprintf("%v", b.Active))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(b.Name)
	builder.WriteString(", ")
	builder.WriteString("code=")
	builder.WriteString(b.Code)
	builder.WriteString(", ")
	builder.WriteString("address=")
	builder.WriteString(b.Address)
	builder.WriteString(", ")
	builder.WriteString("city=")
	builder.WriteString(b.City)
	builder.WriteString(", ")
	builder.WriteString("state=")
	builder.WriteString(b.State)
	builder.WriteString(", ")
	builder.WriteString("zip_code=")
	builder.WriteString(b.ZipCode)
	builder.WriteString(", ")
	builder.WriteString("phone=")
	builder.WriteString(b.Phone)
	builder.WriteString(", ")
	builder.WriteString("email=")
	builder.WriteString(b.Email)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(b.Status)
	builder.WriteString(", ")
	builder.WriteString("inauguration_date=")
	builder.WriteString(b.InaugurationDate.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("is_active=")
	builder.WriteString(fmt.Sprintf("%v", b.IsActive))
	builder.WriteByte(')')
	return builder.String()
}

// Branches is a parsable slice of Branch.
type Branches []*Branch
