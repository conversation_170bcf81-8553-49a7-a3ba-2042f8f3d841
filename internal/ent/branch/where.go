// Code generated by ent, DO NOT EDIT.

package branch

import (
	"time"
	"tradicao/internal/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.Branch {
	return predicate.Branch(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.Branch {
	return predicate.Branch(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.Branch {
	return predicate.Branch(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.Branch {
	return predicate.Branch(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.Branch {
	return predicate.Branch(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.Branch {
	return predicate.Branch(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.Branch {
	return predicate.Branch(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldUpdatedAt, v))
}

// Active applies equality check predicate on the "active" field. It's identical to ActiveEQ.
func Active(v bool) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldActive, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldName, v))
}

// Code applies equality check predicate on the "code" field. It's identical to CodeEQ.
func Code(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldCode, v))
}

// Address applies equality check predicate on the "address" field. It's identical to AddressEQ.
func Address(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldAddress, v))
}

// City applies equality check predicate on the "city" field. It's identical to CityEQ.
func City(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldCity, v))
}

// State applies equality check predicate on the "state" field. It's identical to StateEQ.
func State(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldState, v))
}

// ZipCode applies equality check predicate on the "zip_code" field. It's identical to ZipCodeEQ.
func ZipCode(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldZipCode, v))
}

// Phone applies equality check predicate on the "phone" field. It's identical to PhoneEQ.
func Phone(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldPhone, v))
}

// Email applies equality check predicate on the "email" field. It's identical to EmailEQ.
func Email(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldEmail, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldStatus, v))
}

// InaugurationDate applies equality check predicate on the "inauguration_date" field. It's identical to InaugurationDateEQ.
func InaugurationDate(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldInaugurationDate, v))
}

// IsActive applies equality check predicate on the "is_active" field. It's identical to IsActiveEQ.
func IsActive(v bool) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldIsActive, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldLTE(FieldUpdatedAt, v))
}

// ActiveEQ applies the EQ predicate on the "active" field.
func ActiveEQ(v bool) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldActive, v))
}

// ActiveNEQ applies the NEQ predicate on the "active" field.
func ActiveNEQ(v bool) predicate.Branch {
	return predicate.Branch(sql.FieldNEQ(FieldActive, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Branch {
	return predicate.Branch(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Branch {
	return predicate.Branch(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Branch {
	return predicate.Branch(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Branch {
	return predicate.Branch(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Branch {
	return predicate.Branch(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Branch {
	return predicate.Branch(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Branch {
	return predicate.Branch(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Branch {
	return predicate.Branch(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Branch {
	return predicate.Branch(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Branch {
	return predicate.Branch(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Branch {
	return predicate.Branch(sql.FieldContainsFold(FieldName, v))
}

// CodeEQ applies the EQ predicate on the "code" field.
func CodeEQ(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldCode, v))
}

// CodeNEQ applies the NEQ predicate on the "code" field.
func CodeNEQ(v string) predicate.Branch {
	return predicate.Branch(sql.FieldNEQ(FieldCode, v))
}

// CodeIn applies the In predicate on the "code" field.
func CodeIn(vs ...string) predicate.Branch {
	return predicate.Branch(sql.FieldIn(FieldCode, vs...))
}

// CodeNotIn applies the NotIn predicate on the "code" field.
func CodeNotIn(vs ...string) predicate.Branch {
	return predicate.Branch(sql.FieldNotIn(FieldCode, vs...))
}

// CodeGT applies the GT predicate on the "code" field.
func CodeGT(v string) predicate.Branch {
	return predicate.Branch(sql.FieldGT(FieldCode, v))
}

// CodeGTE applies the GTE predicate on the "code" field.
func CodeGTE(v string) predicate.Branch {
	return predicate.Branch(sql.FieldGTE(FieldCode, v))
}

// CodeLT applies the LT predicate on the "code" field.
func CodeLT(v string) predicate.Branch {
	return predicate.Branch(sql.FieldLT(FieldCode, v))
}

// CodeLTE applies the LTE predicate on the "code" field.
func CodeLTE(v string) predicate.Branch {
	return predicate.Branch(sql.FieldLTE(FieldCode, v))
}

// CodeContains applies the Contains predicate on the "code" field.
func CodeContains(v string) predicate.Branch {
	return predicate.Branch(sql.FieldContains(FieldCode, v))
}

// CodeHasPrefix applies the HasPrefix predicate on the "code" field.
func CodeHasPrefix(v string) predicate.Branch {
	return predicate.Branch(sql.FieldHasPrefix(FieldCode, v))
}

// CodeHasSuffix applies the HasSuffix predicate on the "code" field.
func CodeHasSuffix(v string) predicate.Branch {
	return predicate.Branch(sql.FieldHasSuffix(FieldCode, v))
}

// CodeEqualFold applies the EqualFold predicate on the "code" field.
func CodeEqualFold(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEqualFold(FieldCode, v))
}

// CodeContainsFold applies the ContainsFold predicate on the "code" field.
func CodeContainsFold(v string) predicate.Branch {
	return predicate.Branch(sql.FieldContainsFold(FieldCode, v))
}

// AddressEQ applies the EQ predicate on the "address" field.
func AddressEQ(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldAddress, v))
}

// AddressNEQ applies the NEQ predicate on the "address" field.
func AddressNEQ(v string) predicate.Branch {
	return predicate.Branch(sql.FieldNEQ(FieldAddress, v))
}

// AddressIn applies the In predicate on the "address" field.
func AddressIn(vs ...string) predicate.Branch {
	return predicate.Branch(sql.FieldIn(FieldAddress, vs...))
}

// AddressNotIn applies the NotIn predicate on the "address" field.
func AddressNotIn(vs ...string) predicate.Branch {
	return predicate.Branch(sql.FieldNotIn(FieldAddress, vs...))
}

// AddressGT applies the GT predicate on the "address" field.
func AddressGT(v string) predicate.Branch {
	return predicate.Branch(sql.FieldGT(FieldAddress, v))
}

// AddressGTE applies the GTE predicate on the "address" field.
func AddressGTE(v string) predicate.Branch {
	return predicate.Branch(sql.FieldGTE(FieldAddress, v))
}

// AddressLT applies the LT predicate on the "address" field.
func AddressLT(v string) predicate.Branch {
	return predicate.Branch(sql.FieldLT(FieldAddress, v))
}

// AddressLTE applies the LTE predicate on the "address" field.
func AddressLTE(v string) predicate.Branch {
	return predicate.Branch(sql.FieldLTE(FieldAddress, v))
}

// AddressContains applies the Contains predicate on the "address" field.
func AddressContains(v string) predicate.Branch {
	return predicate.Branch(sql.FieldContains(FieldAddress, v))
}

// AddressHasPrefix applies the HasPrefix predicate on the "address" field.
func AddressHasPrefix(v string) predicate.Branch {
	return predicate.Branch(sql.FieldHasPrefix(FieldAddress, v))
}

// AddressHasSuffix applies the HasSuffix predicate on the "address" field.
func AddressHasSuffix(v string) predicate.Branch {
	return predicate.Branch(sql.FieldHasSuffix(FieldAddress, v))
}

// AddressEqualFold applies the EqualFold predicate on the "address" field.
func AddressEqualFold(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEqualFold(FieldAddress, v))
}

// AddressContainsFold applies the ContainsFold predicate on the "address" field.
func AddressContainsFold(v string) predicate.Branch {
	return predicate.Branch(sql.FieldContainsFold(FieldAddress, v))
}

// CityEQ applies the EQ predicate on the "city" field.
func CityEQ(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldCity, v))
}

// CityNEQ applies the NEQ predicate on the "city" field.
func CityNEQ(v string) predicate.Branch {
	return predicate.Branch(sql.FieldNEQ(FieldCity, v))
}

// CityIn applies the In predicate on the "city" field.
func CityIn(vs ...string) predicate.Branch {
	return predicate.Branch(sql.FieldIn(FieldCity, vs...))
}

// CityNotIn applies the NotIn predicate on the "city" field.
func CityNotIn(vs ...string) predicate.Branch {
	return predicate.Branch(sql.FieldNotIn(FieldCity, vs...))
}

// CityGT applies the GT predicate on the "city" field.
func CityGT(v string) predicate.Branch {
	return predicate.Branch(sql.FieldGT(FieldCity, v))
}

// CityGTE applies the GTE predicate on the "city" field.
func CityGTE(v string) predicate.Branch {
	return predicate.Branch(sql.FieldGTE(FieldCity, v))
}

// CityLT applies the LT predicate on the "city" field.
func CityLT(v string) predicate.Branch {
	return predicate.Branch(sql.FieldLT(FieldCity, v))
}

// CityLTE applies the LTE predicate on the "city" field.
func CityLTE(v string) predicate.Branch {
	return predicate.Branch(sql.FieldLTE(FieldCity, v))
}

// CityContains applies the Contains predicate on the "city" field.
func CityContains(v string) predicate.Branch {
	return predicate.Branch(sql.FieldContains(FieldCity, v))
}

// CityHasPrefix applies the HasPrefix predicate on the "city" field.
func CityHasPrefix(v string) predicate.Branch {
	return predicate.Branch(sql.FieldHasPrefix(FieldCity, v))
}

// CityHasSuffix applies the HasSuffix predicate on the "city" field.
func CityHasSuffix(v string) predicate.Branch {
	return predicate.Branch(sql.FieldHasSuffix(FieldCity, v))
}

// CityEqualFold applies the EqualFold predicate on the "city" field.
func CityEqualFold(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEqualFold(FieldCity, v))
}

// CityContainsFold applies the ContainsFold predicate on the "city" field.
func CityContainsFold(v string) predicate.Branch {
	return predicate.Branch(sql.FieldContainsFold(FieldCity, v))
}

// StateEQ applies the EQ predicate on the "state" field.
func StateEQ(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldState, v))
}

// StateNEQ applies the NEQ predicate on the "state" field.
func StateNEQ(v string) predicate.Branch {
	return predicate.Branch(sql.FieldNEQ(FieldState, v))
}

// StateIn applies the In predicate on the "state" field.
func StateIn(vs ...string) predicate.Branch {
	return predicate.Branch(sql.FieldIn(FieldState, vs...))
}

// StateNotIn applies the NotIn predicate on the "state" field.
func StateNotIn(vs ...string) predicate.Branch {
	return predicate.Branch(sql.FieldNotIn(FieldState, vs...))
}

// StateGT applies the GT predicate on the "state" field.
func StateGT(v string) predicate.Branch {
	return predicate.Branch(sql.FieldGT(FieldState, v))
}

// StateGTE applies the GTE predicate on the "state" field.
func StateGTE(v string) predicate.Branch {
	return predicate.Branch(sql.FieldGTE(FieldState, v))
}

// StateLT applies the LT predicate on the "state" field.
func StateLT(v string) predicate.Branch {
	return predicate.Branch(sql.FieldLT(FieldState, v))
}

// StateLTE applies the LTE predicate on the "state" field.
func StateLTE(v string) predicate.Branch {
	return predicate.Branch(sql.FieldLTE(FieldState, v))
}

// StateContains applies the Contains predicate on the "state" field.
func StateContains(v string) predicate.Branch {
	return predicate.Branch(sql.FieldContains(FieldState, v))
}

// StateHasPrefix applies the HasPrefix predicate on the "state" field.
func StateHasPrefix(v string) predicate.Branch {
	return predicate.Branch(sql.FieldHasPrefix(FieldState, v))
}

// StateHasSuffix applies the HasSuffix predicate on the "state" field.
func StateHasSuffix(v string) predicate.Branch {
	return predicate.Branch(sql.FieldHasSuffix(FieldState, v))
}

// StateEqualFold applies the EqualFold predicate on the "state" field.
func StateEqualFold(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEqualFold(FieldState, v))
}

// StateContainsFold applies the ContainsFold predicate on the "state" field.
func StateContainsFold(v string) predicate.Branch {
	return predicate.Branch(sql.FieldContainsFold(FieldState, v))
}

// ZipCodeEQ applies the EQ predicate on the "zip_code" field.
func ZipCodeEQ(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldZipCode, v))
}

// ZipCodeNEQ applies the NEQ predicate on the "zip_code" field.
func ZipCodeNEQ(v string) predicate.Branch {
	return predicate.Branch(sql.FieldNEQ(FieldZipCode, v))
}

// ZipCodeIn applies the In predicate on the "zip_code" field.
func ZipCodeIn(vs ...string) predicate.Branch {
	return predicate.Branch(sql.FieldIn(FieldZipCode, vs...))
}

// ZipCodeNotIn applies the NotIn predicate on the "zip_code" field.
func ZipCodeNotIn(vs ...string) predicate.Branch {
	return predicate.Branch(sql.FieldNotIn(FieldZipCode, vs...))
}

// ZipCodeGT applies the GT predicate on the "zip_code" field.
func ZipCodeGT(v string) predicate.Branch {
	return predicate.Branch(sql.FieldGT(FieldZipCode, v))
}

// ZipCodeGTE applies the GTE predicate on the "zip_code" field.
func ZipCodeGTE(v string) predicate.Branch {
	return predicate.Branch(sql.FieldGTE(FieldZipCode, v))
}

// ZipCodeLT applies the LT predicate on the "zip_code" field.
func ZipCodeLT(v string) predicate.Branch {
	return predicate.Branch(sql.FieldLT(FieldZipCode, v))
}

// ZipCodeLTE applies the LTE predicate on the "zip_code" field.
func ZipCodeLTE(v string) predicate.Branch {
	return predicate.Branch(sql.FieldLTE(FieldZipCode, v))
}

// ZipCodeContains applies the Contains predicate on the "zip_code" field.
func ZipCodeContains(v string) predicate.Branch {
	return predicate.Branch(sql.FieldContains(FieldZipCode, v))
}

// ZipCodeHasPrefix applies the HasPrefix predicate on the "zip_code" field.
func ZipCodeHasPrefix(v string) predicate.Branch {
	return predicate.Branch(sql.FieldHasPrefix(FieldZipCode, v))
}

// ZipCodeHasSuffix applies the HasSuffix predicate on the "zip_code" field.
func ZipCodeHasSuffix(v string) predicate.Branch {
	return predicate.Branch(sql.FieldHasSuffix(FieldZipCode, v))
}

// ZipCodeEqualFold applies the EqualFold predicate on the "zip_code" field.
func ZipCodeEqualFold(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEqualFold(FieldZipCode, v))
}

// ZipCodeContainsFold applies the ContainsFold predicate on the "zip_code" field.
func ZipCodeContainsFold(v string) predicate.Branch {
	return predicate.Branch(sql.FieldContainsFold(FieldZipCode, v))
}

// PhoneEQ applies the EQ predicate on the "phone" field.
func PhoneEQ(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldPhone, v))
}

// PhoneNEQ applies the NEQ predicate on the "phone" field.
func PhoneNEQ(v string) predicate.Branch {
	return predicate.Branch(sql.FieldNEQ(FieldPhone, v))
}

// PhoneIn applies the In predicate on the "phone" field.
func PhoneIn(vs ...string) predicate.Branch {
	return predicate.Branch(sql.FieldIn(FieldPhone, vs...))
}

// PhoneNotIn applies the NotIn predicate on the "phone" field.
func PhoneNotIn(vs ...string) predicate.Branch {
	return predicate.Branch(sql.FieldNotIn(FieldPhone, vs...))
}

// PhoneGT applies the GT predicate on the "phone" field.
func PhoneGT(v string) predicate.Branch {
	return predicate.Branch(sql.FieldGT(FieldPhone, v))
}

// PhoneGTE applies the GTE predicate on the "phone" field.
func PhoneGTE(v string) predicate.Branch {
	return predicate.Branch(sql.FieldGTE(FieldPhone, v))
}

// PhoneLT applies the LT predicate on the "phone" field.
func PhoneLT(v string) predicate.Branch {
	return predicate.Branch(sql.FieldLT(FieldPhone, v))
}

// PhoneLTE applies the LTE predicate on the "phone" field.
func PhoneLTE(v string) predicate.Branch {
	return predicate.Branch(sql.FieldLTE(FieldPhone, v))
}

// PhoneContains applies the Contains predicate on the "phone" field.
func PhoneContains(v string) predicate.Branch {
	return predicate.Branch(sql.FieldContains(FieldPhone, v))
}

// PhoneHasPrefix applies the HasPrefix predicate on the "phone" field.
func PhoneHasPrefix(v string) predicate.Branch {
	return predicate.Branch(sql.FieldHasPrefix(FieldPhone, v))
}

// PhoneHasSuffix applies the HasSuffix predicate on the "phone" field.
func PhoneHasSuffix(v string) predicate.Branch {
	return predicate.Branch(sql.FieldHasSuffix(FieldPhone, v))
}

// PhoneEqualFold applies the EqualFold predicate on the "phone" field.
func PhoneEqualFold(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEqualFold(FieldPhone, v))
}

// PhoneContainsFold applies the ContainsFold predicate on the "phone" field.
func PhoneContainsFold(v string) predicate.Branch {
	return predicate.Branch(sql.FieldContainsFold(FieldPhone, v))
}

// EmailEQ applies the EQ predicate on the "email" field.
func EmailEQ(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldEmail, v))
}

// EmailNEQ applies the NEQ predicate on the "email" field.
func EmailNEQ(v string) predicate.Branch {
	return predicate.Branch(sql.FieldNEQ(FieldEmail, v))
}

// EmailIn applies the In predicate on the "email" field.
func EmailIn(vs ...string) predicate.Branch {
	return predicate.Branch(sql.FieldIn(FieldEmail, vs...))
}

// EmailNotIn applies the NotIn predicate on the "email" field.
func EmailNotIn(vs ...string) predicate.Branch {
	return predicate.Branch(sql.FieldNotIn(FieldEmail, vs...))
}

// EmailGT applies the GT predicate on the "email" field.
func EmailGT(v string) predicate.Branch {
	return predicate.Branch(sql.FieldGT(FieldEmail, v))
}

// EmailGTE applies the GTE predicate on the "email" field.
func EmailGTE(v string) predicate.Branch {
	return predicate.Branch(sql.FieldGTE(FieldEmail, v))
}

// EmailLT applies the LT predicate on the "email" field.
func EmailLT(v string) predicate.Branch {
	return predicate.Branch(sql.FieldLT(FieldEmail, v))
}

// EmailLTE applies the LTE predicate on the "email" field.
func EmailLTE(v string) predicate.Branch {
	return predicate.Branch(sql.FieldLTE(FieldEmail, v))
}

// EmailContains applies the Contains predicate on the "email" field.
func EmailContains(v string) predicate.Branch {
	return predicate.Branch(sql.FieldContains(FieldEmail, v))
}

// EmailHasPrefix applies the HasPrefix predicate on the "email" field.
func EmailHasPrefix(v string) predicate.Branch {
	return predicate.Branch(sql.FieldHasPrefix(FieldEmail, v))
}

// EmailHasSuffix applies the HasSuffix predicate on the "email" field.
func EmailHasSuffix(v string) predicate.Branch {
	return predicate.Branch(sql.FieldHasSuffix(FieldEmail, v))
}

// EmailIsNil applies the IsNil predicate on the "email" field.
func EmailIsNil() predicate.Branch {
	return predicate.Branch(sql.FieldIsNull(FieldEmail))
}

// EmailNotNil applies the NotNil predicate on the "email" field.
func EmailNotNil() predicate.Branch {
	return predicate.Branch(sql.FieldNotNull(FieldEmail))
}

// EmailEqualFold applies the EqualFold predicate on the "email" field.
func EmailEqualFold(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEqualFold(FieldEmail, v))
}

// EmailContainsFold applies the ContainsFold predicate on the "email" field.
func EmailContainsFold(v string) predicate.Branch {
	return predicate.Branch(sql.FieldContainsFold(FieldEmail, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.Branch {
	return predicate.Branch(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.Branch {
	return predicate.Branch(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.Branch {
	return predicate.Branch(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.Branch {
	return predicate.Branch(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.Branch {
	return predicate.Branch(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.Branch {
	return predicate.Branch(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.Branch {
	return predicate.Branch(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.Branch {
	return predicate.Branch(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.Branch {
	return predicate.Branch(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.Branch {
	return predicate.Branch(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.Branch {
	return predicate.Branch(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.Branch {
	return predicate.Branch(sql.FieldContainsFold(FieldStatus, v))
}

// InaugurationDateEQ applies the EQ predicate on the "inauguration_date" field.
func InaugurationDateEQ(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldInaugurationDate, v))
}

// InaugurationDateNEQ applies the NEQ predicate on the "inauguration_date" field.
func InaugurationDateNEQ(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldNEQ(FieldInaugurationDate, v))
}

// InaugurationDateIn applies the In predicate on the "inauguration_date" field.
func InaugurationDateIn(vs ...time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldIn(FieldInaugurationDate, vs...))
}

// InaugurationDateNotIn applies the NotIn predicate on the "inauguration_date" field.
func InaugurationDateNotIn(vs ...time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldNotIn(FieldInaugurationDate, vs...))
}

// InaugurationDateGT applies the GT predicate on the "inauguration_date" field.
func InaugurationDateGT(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldGT(FieldInaugurationDate, v))
}

// InaugurationDateGTE applies the GTE predicate on the "inauguration_date" field.
func InaugurationDateGTE(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldGTE(FieldInaugurationDate, v))
}

// InaugurationDateLT applies the LT predicate on the "inauguration_date" field.
func InaugurationDateLT(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldLT(FieldInaugurationDate, v))
}

// InaugurationDateLTE applies the LTE predicate on the "inauguration_date" field.
func InaugurationDateLTE(v time.Time) predicate.Branch {
	return predicate.Branch(sql.FieldLTE(FieldInaugurationDate, v))
}

// IsActiveEQ applies the EQ predicate on the "is_active" field.
func IsActiveEQ(v bool) predicate.Branch {
	return predicate.Branch(sql.FieldEQ(FieldIsActive, v))
}

// IsActiveNEQ applies the NEQ predicate on the "is_active" field.
func IsActiveNEQ(v bool) predicate.Branch {
	return predicate.Branch(sql.FieldNEQ(FieldIsActive, v))
}

// HasEquipment applies the HasEdge predicate on the "equipment" edge.
func HasEquipment() predicate.Branch {
	return predicate.Branch(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, EquipmentTable, EquipmentColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasEquipmentWith applies the HasEdge predicate on the "equipment" edge with a given conditions (other predicates).
func HasEquipmentWith(preds ...predicate.Equipment) predicate.Branch {
	return predicate.Branch(func(s *sql.Selector) {
		step := newEquipmentStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasMaintenanceOrders applies the HasEdge predicate on the "maintenance_orders" edge.
func HasMaintenanceOrders() predicate.Branch {
	return predicate.Branch(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, MaintenanceOrdersTable, MaintenanceOrdersColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasMaintenanceOrdersWith applies the HasEdge predicate on the "maintenance_orders" edge with a given conditions (other predicates).
func HasMaintenanceOrdersWith(preds ...predicate.MaintenanceOrder) predicate.Branch {
	return predicate.Branch(func(s *sql.Selector) {
		step := newMaintenanceOrdersStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Branch) predicate.Branch {
	return predicate.Branch(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Branch) predicate.Branch {
	return predicate.Branch(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Branch) predicate.Branch {
	return predicate.Branch(sql.NotPredicates(p))
}
