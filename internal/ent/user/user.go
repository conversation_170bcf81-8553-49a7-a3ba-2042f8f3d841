// Code generated by ent, DO NOT EDIT.

package user

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the user type in the database.
	Label = "user"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldActive holds the string denoting the active field in the database.
	FieldActive = "active"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldEmail holds the string denoting the email field in the database.
	FieldEmail = "email"
	// FieldPassword holds the string denoting the password field in the database.
	FieldPassword = "password"
	// FieldRole holds the string denoting the role field in the database.
	FieldRole = "role"
	// EdgeManagedBranches holds the string denoting the managed_branches edge name in mutations.
	EdgeManagedBranches = "managed_branches"
	// EdgeRequestedOrders holds the string denoting the requested_orders edge name in mutations.
	EdgeRequestedOrders = "requested_orders"
	// EdgeAssignedOrders holds the string denoting the assigned_orders edge name in mutations.
	EdgeAssignedOrders = "assigned_orders"
	// EdgeApprovedOrders holds the string denoting the approved_orders edge name in mutations.
	EdgeApprovedOrders = "approved_orders"
	// EdgeCostItems holds the string denoting the cost_items edge name in mutations.
	EdgeCostItems = "cost_items"
	// EdgeInteractions holds the string denoting the interactions edge name in mutations.
	EdgeInteractions = "interactions"
	// Table holds the table name of the user in the database.
	Table = "users"
	// ManagedBranchesTable is the table that holds the managed_branches relation/edge.
	ManagedBranchesTable = "branches"
	// ManagedBranchesInverseTable is the table name for the Branch entity.
	// It exists in this package in order to avoid circular dependency with the "branch" package.
	ManagedBranchesInverseTable = "branches"
	// ManagedBranchesColumn is the table column denoting the managed_branches relation/edge.
	ManagedBranchesColumn = "user_managed_branches"
	// RequestedOrdersTable is the table that holds the requested_orders relation/edge.
	RequestedOrdersTable = "maintenance_orders"
	// RequestedOrdersInverseTable is the table name for the MaintenanceOrder entity.
	// It exists in this package in order to avoid circular dependency with the "maintenanceorder" package.
	RequestedOrdersInverseTable = "maintenance_orders"
	// RequestedOrdersColumn is the table column denoting the requested_orders relation/edge.
	RequestedOrdersColumn = "requester_id"
	// AssignedOrdersTable is the table that holds the assigned_orders relation/edge.
	AssignedOrdersTable = "maintenance_orders"
	// AssignedOrdersInverseTable is the table name for the MaintenanceOrder entity.
	// It exists in this package in order to avoid circular dependency with the "maintenanceorder" package.
	AssignedOrdersInverseTable = "maintenance_orders"
	// AssignedOrdersColumn is the table column denoting the assigned_orders relation/edge.
	AssignedOrdersColumn = "technician_id"
	// ApprovedOrdersTable is the table that holds the approved_orders relation/edge.
	ApprovedOrdersTable = "maintenance_orders"
	// ApprovedOrdersInverseTable is the table name for the MaintenanceOrder entity.
	// It exists in this package in order to avoid circular dependency with the "maintenanceorder" package.
	ApprovedOrdersInverseTable = "maintenance_orders"
	// ApprovedOrdersColumn is the table column denoting the approved_orders relation/edge.
	ApprovedOrdersColumn = "approver_id"
	// CostItemsTable is the table that holds the cost_items relation/edge.
	CostItemsTable = "cost_items"
	// CostItemsInverseTable is the table name for the CostItem entity.
	// It exists in this package in order to avoid circular dependency with the "costitem" package.
	CostItemsInverseTable = "cost_items"
	// CostItemsColumn is the table column denoting the cost_items relation/edge.
	CostItemsColumn = "user_id"
	// InteractionsTable is the table that holds the interactions relation/edge.
	InteractionsTable = "interactions"
	// InteractionsInverseTable is the table name for the Interaction entity.
	// It exists in this package in order to avoid circular dependency with the "interaction" package.
	InteractionsInverseTable = "interactions"
	// InteractionsColumn is the table column denoting the interactions relation/edge.
	InteractionsColumn = "user_interactions"
)

// Columns holds all SQL columns for user fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldActive,
	FieldName,
	FieldEmail,
	FieldPassword,
	FieldRole,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultActive holds the default value on creation for the "active" field.
	DefaultActive bool
	// NameValidator is a validator for the "name" field. It is called by the builders before save.
	NameValidator func(string) error
	// EmailValidator is a validator for the "email" field. It is called by the builders before save.
	EmailValidator func(string) error
	// PasswordValidator is a validator for the "password" field. It is called by the builders before save.
	PasswordValidator func(string) error
	// RoleValidator is a validator for the "role" field. It is called by the builders before save.
	RoleValidator func(string) error
)

// OrderOption defines the ordering options for the User queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByActive orders the results by the active field.
func ByActive(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldActive, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByEmail orders the results by the email field.
func ByEmail(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEmail, opts...).ToFunc()
}

// ByPassword orders the results by the password field.
func ByPassword(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPassword, opts...).ToFunc()
}

// ByRole orders the results by the role field.
func ByRole(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRole, opts...).ToFunc()
}

// ByManagedBranchesCount orders the results by managed_branches count.
func ByManagedBranchesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newManagedBranchesStep(), opts...)
	}
}

// ByManagedBranches orders the results by managed_branches terms.
func ByManagedBranches(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newManagedBranchesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByRequestedOrdersCount orders the results by requested_orders count.
func ByRequestedOrdersCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newRequestedOrdersStep(), opts...)
	}
}

// ByRequestedOrders orders the results by requested_orders terms.
func ByRequestedOrders(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newRequestedOrdersStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByAssignedOrdersCount orders the results by assigned_orders count.
func ByAssignedOrdersCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newAssignedOrdersStep(), opts...)
	}
}

// ByAssignedOrders orders the results by assigned_orders terms.
func ByAssignedOrders(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newAssignedOrdersStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByApprovedOrdersCount orders the results by approved_orders count.
func ByApprovedOrdersCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newApprovedOrdersStep(), opts...)
	}
}

// ByApprovedOrders orders the results by approved_orders terms.
func ByApprovedOrders(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newApprovedOrdersStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByCostItemsCount orders the results by cost_items count.
func ByCostItemsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newCostItemsStep(), opts...)
	}
}

// ByCostItems orders the results by cost_items terms.
func ByCostItems(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newCostItemsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByInteractionsCount orders the results by interactions count.
func ByInteractionsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newInteractionsStep(), opts...)
	}
}

// ByInteractions orders the results by interactions terms.
func ByInteractions(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newInteractionsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newManagedBranchesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(ManagedBranchesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, ManagedBranchesTable, ManagedBranchesColumn),
	)
}
func newRequestedOrdersStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(RequestedOrdersInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, RequestedOrdersTable, RequestedOrdersColumn),
	)
}
func newAssignedOrdersStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(AssignedOrdersInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, AssignedOrdersTable, AssignedOrdersColumn),
	)
}
func newApprovedOrdersStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(ApprovedOrdersInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, ApprovedOrdersTable, ApprovedOrdersColumn),
	)
}
func newCostItemsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(CostItemsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, CostItemsTable, CostItemsColumn),
	)
}
func newInteractionsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(InteractionsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, InteractionsTable, InteractionsColumn),
	)
}
