// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"
	"tradicao/internal/ent/interaction"
	"tradicao/internal/ent/maintenanceorder"
	"tradicao/internal/ent/predicate"
	"tradicao/internal/ent/user"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// InteractionQuery is the builder for querying Interaction entities.
type InteractionQuery struct {
	config
	ctx                  *QueryContext
	order                []interaction.OrderOption
	inters               []Interceptor
	predicates           []predicate.Interaction
	withMaintenanceOrder *MaintenanceOrderQuery
	withUser             *UserQuery
	withFKs              bool
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the InteractionQuery builder.
func (iq *InteractionQuery) Where(ps ...predicate.Interaction) *InteractionQuery {
	iq.predicates = append(iq.predicates, ps...)
	return iq
}

// Limit the number of records to be returned by this query.
func (iq *InteractionQuery) Limit(limit int) *InteractionQuery {
	iq.ctx.Limit = &limit
	return iq
}

// Offset to start from.
func (iq *InteractionQuery) Offset(offset int) *InteractionQuery {
	iq.ctx.Offset = &offset
	return iq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (iq *InteractionQuery) Unique(unique bool) *InteractionQuery {
	iq.ctx.Unique = &unique
	return iq
}

// Order specifies how the records should be ordered.
func (iq *InteractionQuery) Order(o ...interaction.OrderOption) *InteractionQuery {
	iq.order = append(iq.order, o...)
	return iq
}

// QueryMaintenanceOrder chains the current query on the "maintenance_order" edge.
func (iq *InteractionQuery) QueryMaintenanceOrder() *MaintenanceOrderQuery {
	query := (&MaintenanceOrderClient{config: iq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := iq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := iq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(interaction.Table, interaction.FieldID, selector),
			sqlgraph.To(maintenanceorder.Table, maintenanceorder.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, interaction.MaintenanceOrderTable, interaction.MaintenanceOrderColumn),
		)
		fromU = sqlgraph.SetNeighbors(iq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryUser chains the current query on the "user" edge.
func (iq *InteractionQuery) QueryUser() *UserQuery {
	query := (&UserClient{config: iq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := iq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := iq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(interaction.Table, interaction.FieldID, selector),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, interaction.UserTable, interaction.UserColumn),
		)
		fromU = sqlgraph.SetNeighbors(iq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first Interaction entity from the query.
// Returns a *NotFoundError when no Interaction was found.
func (iq *InteractionQuery) First(ctx context.Context) (*Interaction, error) {
	nodes, err := iq.Limit(1).All(setContextOp(ctx, iq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{interaction.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (iq *InteractionQuery) FirstX(ctx context.Context) *Interaction {
	node, err := iq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first Interaction ID from the query.
// Returns a *NotFoundError when no Interaction ID was found.
func (iq *InteractionQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = iq.Limit(1).IDs(setContextOp(ctx, iq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{interaction.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (iq *InteractionQuery) FirstIDX(ctx context.Context) int {
	id, err := iq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single Interaction entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one Interaction entity is found.
// Returns a *NotFoundError when no Interaction entities are found.
func (iq *InteractionQuery) Only(ctx context.Context) (*Interaction, error) {
	nodes, err := iq.Limit(2).All(setContextOp(ctx, iq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{interaction.Label}
	default:
		return nil, &NotSingularError{interaction.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (iq *InteractionQuery) OnlyX(ctx context.Context) *Interaction {
	node, err := iq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only Interaction ID in the query.
// Returns a *NotSingularError when more than one Interaction ID is found.
// Returns a *NotFoundError when no entities are found.
func (iq *InteractionQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = iq.Limit(2).IDs(setContextOp(ctx, iq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{interaction.Label}
	default:
		err = &NotSingularError{interaction.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (iq *InteractionQuery) OnlyIDX(ctx context.Context) int {
	id, err := iq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of Interactions.
func (iq *InteractionQuery) All(ctx context.Context) ([]*Interaction, error) {
	ctx = setContextOp(ctx, iq.ctx, ent.OpQueryAll)
	if err := iq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*Interaction, *InteractionQuery]()
	return withInterceptors[[]*Interaction](ctx, iq, qr, iq.inters)
}

// AllX is like All, but panics if an error occurs.
func (iq *InteractionQuery) AllX(ctx context.Context) []*Interaction {
	nodes, err := iq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of Interaction IDs.
func (iq *InteractionQuery) IDs(ctx context.Context) (ids []int, err error) {
	if iq.ctx.Unique == nil && iq.path != nil {
		iq.Unique(true)
	}
	ctx = setContextOp(ctx, iq.ctx, ent.OpQueryIDs)
	if err = iq.Select(interaction.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (iq *InteractionQuery) IDsX(ctx context.Context) []int {
	ids, err := iq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (iq *InteractionQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, iq.ctx, ent.OpQueryCount)
	if err := iq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, iq, querierCount[*InteractionQuery](), iq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (iq *InteractionQuery) CountX(ctx context.Context) int {
	count, err := iq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (iq *InteractionQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, iq.ctx, ent.OpQueryExist)
	switch _, err := iq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (iq *InteractionQuery) ExistX(ctx context.Context) bool {
	exist, err := iq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the InteractionQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (iq *InteractionQuery) Clone() *InteractionQuery {
	if iq == nil {
		return nil
	}
	return &InteractionQuery{
		config:               iq.config,
		ctx:                  iq.ctx.Clone(),
		order:                append([]interaction.OrderOption{}, iq.order...),
		inters:               append([]Interceptor{}, iq.inters...),
		predicates:           append([]predicate.Interaction{}, iq.predicates...),
		withMaintenanceOrder: iq.withMaintenanceOrder.Clone(),
		withUser:             iq.withUser.Clone(),
		// clone intermediate query.
		sql:  iq.sql.Clone(),
		path: iq.path,
	}
}

// WithMaintenanceOrder tells the query-builder to eager-load the nodes that are connected to
// the "maintenance_order" edge. The optional arguments are used to configure the query builder of the edge.
func (iq *InteractionQuery) WithMaintenanceOrder(opts ...func(*MaintenanceOrderQuery)) *InteractionQuery {
	query := (&MaintenanceOrderClient{config: iq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	iq.withMaintenanceOrder = query
	return iq
}

// WithUser tells the query-builder to eager-load the nodes that are connected to
// the "user" edge. The optional arguments are used to configure the query builder of the edge.
func (iq *InteractionQuery) WithUser(opts ...func(*UserQuery)) *InteractionQuery {
	query := (&UserClient{config: iq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	iq.withUser = query
	return iq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.Interaction.Query().
//		GroupBy(interaction.FieldCreateTime).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (iq *InteractionQuery) GroupBy(field string, fields ...string) *InteractionGroupBy {
	iq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &InteractionGroupBy{build: iq}
	grbuild.flds = &iq.ctx.Fields
	grbuild.label = interaction.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//	}
//
//	client.Interaction.Query().
//		Select(interaction.FieldCreateTime).
//		Scan(ctx, &v)
func (iq *InteractionQuery) Select(fields ...string) *InteractionSelect {
	iq.ctx.Fields = append(iq.ctx.Fields, fields...)
	sbuild := &InteractionSelect{InteractionQuery: iq}
	sbuild.label = interaction.Label
	sbuild.flds, sbuild.scan = &iq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a InteractionSelect configured with the given aggregations.
func (iq *InteractionQuery) Aggregate(fns ...AggregateFunc) *InteractionSelect {
	return iq.Select().Aggregate(fns...)
}

func (iq *InteractionQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range iq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, iq); err != nil {
				return err
			}
		}
	}
	for _, f := range iq.ctx.Fields {
		if !interaction.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if iq.path != nil {
		prev, err := iq.path(ctx)
		if err != nil {
			return err
		}
		iq.sql = prev
	}
	return nil
}

func (iq *InteractionQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*Interaction, error) {
	var (
		nodes       = []*Interaction{}
		withFKs     = iq.withFKs
		_spec       = iq.querySpec()
		loadedTypes = [2]bool{
			iq.withMaintenanceOrder != nil,
			iq.withUser != nil,
		}
	)
	if iq.withMaintenanceOrder != nil || iq.withUser != nil {
		withFKs = true
	}
	if withFKs {
		_spec.Node.Columns = append(_spec.Node.Columns, interaction.ForeignKeys...)
	}
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*Interaction).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &Interaction{config: iq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, iq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := iq.withMaintenanceOrder; query != nil {
		if err := iq.loadMaintenanceOrder(ctx, query, nodes, nil,
			func(n *Interaction, e *MaintenanceOrder) { n.Edges.MaintenanceOrder = e }); err != nil {
			return nil, err
		}
	}
	if query := iq.withUser; query != nil {
		if err := iq.loadUser(ctx, query, nodes, nil,
			func(n *Interaction, e *User) { n.Edges.User = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (iq *InteractionQuery) loadMaintenanceOrder(ctx context.Context, query *MaintenanceOrderQuery, nodes []*Interaction, init func(*Interaction), assign func(*Interaction, *MaintenanceOrder)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*Interaction)
	for i := range nodes {
		if nodes[i].maintenance_order_interactions == nil {
			continue
		}
		fk := *nodes[i].maintenance_order_interactions
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(maintenanceorder.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "maintenance_order_interactions" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (iq *InteractionQuery) loadUser(ctx context.Context, query *UserQuery, nodes []*Interaction, init func(*Interaction), assign func(*Interaction, *User)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*Interaction)
	for i := range nodes {
		if nodes[i].user_interactions == nil {
			continue
		}
		fk := *nodes[i].user_interactions
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(user.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "user_interactions" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (iq *InteractionQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := iq.querySpec()
	_spec.Node.Columns = iq.ctx.Fields
	if len(iq.ctx.Fields) > 0 {
		_spec.Unique = iq.ctx.Unique != nil && *iq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, iq.driver, _spec)
}

func (iq *InteractionQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(interaction.Table, interaction.Columns, sqlgraph.NewFieldSpec(interaction.FieldID, field.TypeInt))
	_spec.From = iq.sql
	if unique := iq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if iq.path != nil {
		_spec.Unique = true
	}
	if fields := iq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, interaction.FieldID)
		for i := range fields {
			if fields[i] != interaction.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := iq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := iq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := iq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := iq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (iq *InteractionQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(iq.driver.Dialect())
	t1 := builder.Table(interaction.Table)
	columns := iq.ctx.Fields
	if len(columns) == 0 {
		columns = interaction.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if iq.sql != nil {
		selector = iq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if iq.ctx.Unique != nil && *iq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range iq.predicates {
		p(selector)
	}
	for _, p := range iq.order {
		p(selector)
	}
	if offset := iq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := iq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// InteractionGroupBy is the group-by builder for Interaction entities.
type InteractionGroupBy struct {
	selector
	build *InteractionQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (igb *InteractionGroupBy) Aggregate(fns ...AggregateFunc) *InteractionGroupBy {
	igb.fns = append(igb.fns, fns...)
	return igb
}

// Scan applies the selector query and scans the result into the given value.
func (igb *InteractionGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, igb.build.ctx, ent.OpQueryGroupBy)
	if err := igb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*InteractionQuery, *InteractionGroupBy](ctx, igb.build, igb, igb.build.inters, v)
}

func (igb *InteractionGroupBy) sqlScan(ctx context.Context, root *InteractionQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(igb.fns))
	for _, fn := range igb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*igb.flds)+len(igb.fns))
		for _, f := range *igb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*igb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := igb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// InteractionSelect is the builder for selecting fields of Interaction entities.
type InteractionSelect struct {
	*InteractionQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (is *InteractionSelect) Aggregate(fns ...AggregateFunc) *InteractionSelect {
	is.fns = append(is.fns, fns...)
	return is
}

// Scan applies the selector query and scans the result into the given value.
func (is *InteractionSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, is.ctx, ent.OpQuerySelect)
	if err := is.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*InteractionQuery, *InteractionSelect](ctx, is.InteractionQuery, is, is.inters, v)
}

func (is *InteractionSelect) sqlScan(ctx context.Context, root *InteractionQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(is.fns))
	for _, fn := range is.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*is.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := is.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
