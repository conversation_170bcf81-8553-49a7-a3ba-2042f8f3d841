// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"
	"tradicao/internal/ent/branch"
	"tradicao/internal/ent/equipment"
	"tradicao/internal/ent/maintenanceorder"
	"tradicao/internal/ent/user"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// MaintenanceOrder is the model entity for the MaintenanceOrder schema.
type MaintenanceOrder struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// Active holds the value of the "active" field.
	Active bool `json:"active,omitempty"`
	// Title holds the value of the "title" field.
	Title string `json:"title,omitempty"`
	// Description holds the value of the "description" field.
	Description string `json:"description,omitempty"`
	// Status holds the value of the "status" field.
	Status string `json:"status,omitempty"`
	// Priority holds the value of the "priority" field.
	Priority string `json:"priority,omitempty"`
	// BranchID holds the value of the "branch_id" field.
	BranchID int `json:"branch_id,omitempty"`
	// EquipmentID holds the value of the "equipment_id" field.
	EquipmentID int `json:"equipment_id,omitempty"`
	// RequesterID holds the value of the "requester_id" field.
	RequesterID int `json:"requester_id,omitempty"`
	// ApproverID holds the value of the "approver_id" field.
	ApproverID int `json:"approver_id,omitempty"`
	// TechnicianID holds the value of the "technician_id" field.
	TechnicianID int `json:"technician_id,omitempty"`
	// CancellationReason holds the value of the "cancellation_reason" field.
	CancellationReason string `json:"cancellation_reason,omitempty"`
	// RejectionReason holds the value of the "rejection_reason" field.
	RejectionReason string `json:"rejection_reason,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the MaintenanceOrderQuery when eager-loading is set.
	Edges        MaintenanceOrderEdges `json:"edges"`
	selectValues sql.SelectValues
}

// MaintenanceOrderEdges holds the relations/edges for other nodes in the graph.
type MaintenanceOrderEdges struct {
	// Branch holds the value of the branch edge.
	Branch *Branch `json:"branch,omitempty"`
	// Equipment holds the value of the equipment edge.
	Equipment *Equipment `json:"equipment,omitempty"`
	// Requester holds the value of the requester edge.
	Requester *User `json:"requester,omitempty"`
	// Approver holds the value of the approver edge.
	Approver *User `json:"approver,omitempty"`
	// Technician holds the value of the technician edge.
	Technician *User `json:"technician,omitempty"`
	// CostItems holds the value of the cost_items edge.
	CostItems []*CostItem `json:"cost_items,omitempty"`
	// Interactions holds the value of the interactions edge.
	Interactions []*Interaction `json:"interactions,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [7]bool
}

// BranchOrErr returns the Branch value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e MaintenanceOrderEdges) BranchOrErr() (*Branch, error) {
	if e.Branch != nil {
		return e.Branch, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: branch.Label}
	}
	return nil, &NotLoadedError{edge: "branch"}
}

// EquipmentOrErr returns the Equipment value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e MaintenanceOrderEdges) EquipmentOrErr() (*Equipment, error) {
	if e.Equipment != nil {
		return e.Equipment, nil
	} else if e.loadedTypes[1] {
		return nil, &NotFoundError{label: equipment.Label}
	}
	return nil, &NotLoadedError{edge: "equipment"}
}

// RequesterOrErr returns the Requester value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e MaintenanceOrderEdges) RequesterOrErr() (*User, error) {
	if e.Requester != nil {
		return e.Requester, nil
	} else if e.loadedTypes[2] {
		return nil, &NotFoundError{label: user.Label}
	}
	return nil, &NotLoadedError{edge: "requester"}
}

// ApproverOrErr returns the Approver value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e MaintenanceOrderEdges) ApproverOrErr() (*User, error) {
	if e.Approver != nil {
		return e.Approver, nil
	} else if e.loadedTypes[3] {
		return nil, &NotFoundError{label: user.Label}
	}
	return nil, &NotLoadedError{edge: "approver"}
}

// TechnicianOrErr returns the Technician value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e MaintenanceOrderEdges) TechnicianOrErr() (*User, error) {
	if e.Technician != nil {
		return e.Technician, nil
	} else if e.loadedTypes[4] {
		return nil, &NotFoundError{label: user.Label}
	}
	return nil, &NotLoadedError{edge: "technician"}
}

// CostItemsOrErr returns the CostItems value or an error if the edge
// was not loaded in eager-loading.
func (e MaintenanceOrderEdges) CostItemsOrErr() ([]*CostItem, error) {
	if e.loadedTypes[5] {
		return e.CostItems, nil
	}
	return nil, &NotLoadedError{edge: "cost_items"}
}

// InteractionsOrErr returns the Interactions value or an error if the edge
// was not loaded in eager-loading.
func (e MaintenanceOrderEdges) InteractionsOrErr() ([]*Interaction, error) {
	if e.loadedTypes[6] {
		return e.Interactions, nil
	}
	return nil, &NotLoadedError{edge: "interactions"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*MaintenanceOrder) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case maintenanceorder.FieldActive:
			values[i] = new(sql.NullBool)
		case maintenanceorder.FieldID, maintenanceorder.FieldBranchID, maintenanceorder.FieldEquipmentID, maintenanceorder.FieldRequesterID, maintenanceorder.FieldApproverID, maintenanceorder.FieldTechnicianID:
			values[i] = new(sql.NullInt64)
		case maintenanceorder.FieldTitle, maintenanceorder.FieldDescription, maintenanceorder.FieldStatus, maintenanceorder.FieldPriority, maintenanceorder.FieldCancellationReason, maintenanceorder.FieldRejectionReason:
			values[i] = new(sql.NullString)
		case maintenanceorder.FieldCreatedAt, maintenanceorder.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the MaintenanceOrder fields.
func (mo *MaintenanceOrder) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case maintenanceorder.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			mo.ID = int(value.Int64)
		case maintenanceorder.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				mo.CreatedAt = value.Time
			}
		case maintenanceorder.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				mo.UpdatedAt = value.Time
			}
		case maintenanceorder.FieldActive:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field active", values[i])
			} else if value.Valid {
				mo.Active = value.Bool
			}
		case maintenanceorder.FieldTitle:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field title", values[i])
			} else if value.Valid {
				mo.Title = value.String
			}
		case maintenanceorder.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				mo.Description = value.String
			}
		case maintenanceorder.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				mo.Status = value.String
			}
		case maintenanceorder.FieldPriority:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field priority", values[i])
			} else if value.Valid {
				mo.Priority = value.String
			}
		case maintenanceorder.FieldBranchID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field branch_id", values[i])
			} else if value.Valid {
				mo.BranchID = int(value.Int64)
			}
		case maintenanceorder.FieldEquipmentID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field equipment_id", values[i])
			} else if value.Valid {
				mo.EquipmentID = int(value.Int64)
			}
		case maintenanceorder.FieldRequesterID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field requester_id", values[i])
			} else if value.Valid {
				mo.RequesterID = int(value.Int64)
			}
		case maintenanceorder.FieldApproverID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field approver_id", values[i])
			} else if value.Valid {
				mo.ApproverID = int(value.Int64)
			}
		case maintenanceorder.FieldTechnicianID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field technician_id", values[i])
			} else if value.Valid {
				mo.TechnicianID = int(value.Int64)
			}
		case maintenanceorder.FieldCancellationReason:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field cancellation_reason", values[i])
			} else if value.Valid {
				mo.CancellationReason = value.String
			}
		case maintenanceorder.FieldRejectionReason:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field rejection_reason", values[i])
			} else if value.Valid {
				mo.RejectionReason = value.String
			}
		default:
			mo.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the MaintenanceOrder.
// This includes values selected through modifiers, order, etc.
func (mo *MaintenanceOrder) Value(name string) (ent.Value, error) {
	return mo.selectValues.Get(name)
}

// QueryBranch queries the "branch" edge of the MaintenanceOrder entity.
func (mo *MaintenanceOrder) QueryBranch() *BranchQuery {
	return NewMaintenanceOrderClient(mo.config).QueryBranch(mo)
}

// QueryEquipment queries the "equipment" edge of the MaintenanceOrder entity.
func (mo *MaintenanceOrder) QueryEquipment() *EquipmentQuery {
	return NewMaintenanceOrderClient(mo.config).QueryEquipment(mo)
}

// QueryRequester queries the "requester" edge of the MaintenanceOrder entity.
func (mo *MaintenanceOrder) QueryRequester() *UserQuery {
	return NewMaintenanceOrderClient(mo.config).QueryRequester(mo)
}

// QueryApprover queries the "approver" edge of the MaintenanceOrder entity.
func (mo *MaintenanceOrder) QueryApprover() *UserQuery {
	return NewMaintenanceOrderClient(mo.config).QueryApprover(mo)
}

// QueryTechnician queries the "technician" edge of the MaintenanceOrder entity.
func (mo *MaintenanceOrder) QueryTechnician() *UserQuery {
	return NewMaintenanceOrderClient(mo.config).QueryTechnician(mo)
}

// QueryCostItems queries the "cost_items" edge of the MaintenanceOrder entity.
func (mo *MaintenanceOrder) QueryCostItems() *CostItemQuery {
	return NewMaintenanceOrderClient(mo.config).QueryCostItems(mo)
}

// QueryInteractions queries the "interactions" edge of the MaintenanceOrder entity.
func (mo *MaintenanceOrder) QueryInteractions() *InteractionQuery {
	return NewMaintenanceOrderClient(mo.config).QueryInteractions(mo)
}

// Update returns a builder for updating this MaintenanceOrder.
// Note that you need to call MaintenanceOrder.Unwrap() before calling this method if this MaintenanceOrder
// was returned from a transaction, and the transaction was committed or rolled back.
func (mo *MaintenanceOrder) Update() *MaintenanceOrderUpdateOne {
	return NewMaintenanceOrderClient(mo.config).UpdateOne(mo)
}

// Unwrap unwraps the MaintenanceOrder entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (mo *MaintenanceOrder) Unwrap() *MaintenanceOrder {
	_tx, ok := mo.config.driver.(*txDriver)
	if !ok {
		panic("ent: MaintenanceOrder is not a transactional entity")
	}
	mo.config.driver = _tx.drv
	return mo
}

// String implements the fmt.Stringer.
func (mo *MaintenanceOrder) String() string {
	var builder strings.Builder
	builder.WriteString("MaintenanceOrder(")
	builder.WriteString(fmt.Sprintf("id=%v, ", mo.ID))
	builder.WriteString("created_at=")
	builder.WriteString(mo.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(mo.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("active=")
	builder.WriteString(fmt.Sprintf("%v", mo.Active))
	builder.WriteString(", ")
	builder.WriteString("title=")
	builder.WriteString(mo.Title)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(mo.Description)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(mo.Status)
	builder.WriteString(", ")
	builder.WriteString("priority=")
	builder.WriteString(mo.Priority)
	builder.WriteString(", ")
	builder.WriteString("branch_id=")
	builder.WriteString(fmt.Sprintf("%v", mo.BranchID))
	builder.WriteString(", ")
	builder.WriteString("equipment_id=")
	builder.WriteString(fmt.Sprintf("%v", mo.EquipmentID))
	builder.WriteString(", ")
	builder.WriteString("requester_id=")
	builder.WriteString(fmt.Sprintf("%v", mo.RequesterID))
	builder.WriteString(", ")
	builder.WriteString("approver_id=")
	builder.WriteString(fmt.Sprintf("%v", mo.ApproverID))
	builder.WriteString(", ")
	builder.WriteString("technician_id=")
	builder.WriteString(fmt.Sprintf("%v", mo.TechnicianID))
	builder.WriteString(", ")
	builder.WriteString("cancellation_reason=")
	builder.WriteString(mo.CancellationReason)
	builder.WriteString(", ")
	builder.WriteString("rejection_reason=")
	builder.WriteString(mo.RejectionReason)
	builder.WriteByte(')')
	return builder.String()
}

// MaintenanceOrders is a parsable slice of MaintenanceOrder.
type MaintenanceOrders []*MaintenanceOrder
