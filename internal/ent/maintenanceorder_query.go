// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"database/sql/driver"
	"fmt"
	"math"
	"tradicao/internal/ent/branch"
	"tradicao/internal/ent/costitem"
	"tradicao/internal/ent/equipment"
	"tradicao/internal/ent/interaction"
	"tradicao/internal/ent/maintenanceorder"
	"tradicao/internal/ent/predicate"
	"tradicao/internal/ent/user"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MaintenanceOrderQuery is the builder for querying MaintenanceOrder entities.
type MaintenanceOrderQuery struct {
	config
	ctx              *QueryContext
	order            []maintenanceorder.OrderOption
	inters           []Interceptor
	predicates       []predicate.MaintenanceOrder
	withBranch       *BranchQuery
	withEquipment    *EquipmentQuery
	withRequester    *UserQuery
	withApprover     *UserQuery
	withTechnician   *UserQuery
	withCostItems    *CostItemQuery
	withInteractions *InteractionQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the MaintenanceOrderQuery builder.
func (moq *MaintenanceOrderQuery) Where(ps ...predicate.MaintenanceOrder) *MaintenanceOrderQuery {
	moq.predicates = append(moq.predicates, ps...)
	return moq
}

// Limit the number of records to be returned by this query.
func (moq *MaintenanceOrderQuery) Limit(limit int) *MaintenanceOrderQuery {
	moq.ctx.Limit = &limit
	return moq
}

// Offset to start from.
func (moq *MaintenanceOrderQuery) Offset(offset int) *MaintenanceOrderQuery {
	moq.ctx.Offset = &offset
	return moq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (moq *MaintenanceOrderQuery) Unique(unique bool) *MaintenanceOrderQuery {
	moq.ctx.Unique = &unique
	return moq
}

// Order specifies how the records should be ordered.
func (moq *MaintenanceOrderQuery) Order(o ...maintenanceorder.OrderOption) *MaintenanceOrderQuery {
	moq.order = append(moq.order, o...)
	return moq
}

// QueryBranch chains the current query on the "branch" edge.
func (moq *MaintenanceOrderQuery) QueryBranch() *BranchQuery {
	query := (&BranchClient{config: moq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := moq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := moq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(maintenanceorder.Table, maintenanceorder.FieldID, selector),
			sqlgraph.To(branch.Table, branch.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, maintenanceorder.BranchTable, maintenanceorder.BranchColumn),
		)
		fromU = sqlgraph.SetNeighbors(moq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryEquipment chains the current query on the "equipment" edge.
func (moq *MaintenanceOrderQuery) QueryEquipment() *EquipmentQuery {
	query := (&EquipmentClient{config: moq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := moq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := moq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(maintenanceorder.Table, maintenanceorder.FieldID, selector),
			sqlgraph.To(equipment.Table, equipment.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, maintenanceorder.EquipmentTable, maintenanceorder.EquipmentColumn),
		)
		fromU = sqlgraph.SetNeighbors(moq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryRequester chains the current query on the "requester" edge.
func (moq *MaintenanceOrderQuery) QueryRequester() *UserQuery {
	query := (&UserClient{config: moq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := moq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := moq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(maintenanceorder.Table, maintenanceorder.FieldID, selector),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, maintenanceorder.RequesterTable, maintenanceorder.RequesterColumn),
		)
		fromU = sqlgraph.SetNeighbors(moq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryApprover chains the current query on the "approver" edge.
func (moq *MaintenanceOrderQuery) QueryApprover() *UserQuery {
	query := (&UserClient{config: moq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := moq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := moq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(maintenanceorder.Table, maintenanceorder.FieldID, selector),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, maintenanceorder.ApproverTable, maintenanceorder.ApproverColumn),
		)
		fromU = sqlgraph.SetNeighbors(moq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryTechnician chains the current query on the "technician" edge.
func (moq *MaintenanceOrderQuery) QueryTechnician() *UserQuery {
	query := (&UserClient{config: moq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := moq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := moq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(maintenanceorder.Table, maintenanceorder.FieldID, selector),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, maintenanceorder.TechnicianTable, maintenanceorder.TechnicianColumn),
		)
		fromU = sqlgraph.SetNeighbors(moq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryCostItems chains the current query on the "cost_items" edge.
func (moq *MaintenanceOrderQuery) QueryCostItems() *CostItemQuery {
	query := (&CostItemClient{config: moq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := moq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := moq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(maintenanceorder.Table, maintenanceorder.FieldID, selector),
			sqlgraph.To(costitem.Table, costitem.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, maintenanceorder.CostItemsTable, maintenanceorder.CostItemsColumn),
		)
		fromU = sqlgraph.SetNeighbors(moq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryInteractions chains the current query on the "interactions" edge.
func (moq *MaintenanceOrderQuery) QueryInteractions() *InteractionQuery {
	query := (&InteractionClient{config: moq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := moq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := moq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(maintenanceorder.Table, maintenanceorder.FieldID, selector),
			sqlgraph.To(interaction.Table, interaction.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, maintenanceorder.InteractionsTable, maintenanceorder.InteractionsColumn),
		)
		fromU = sqlgraph.SetNeighbors(moq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first MaintenanceOrder entity from the query.
// Returns a *NotFoundError when no MaintenanceOrder was found.
func (moq *MaintenanceOrderQuery) First(ctx context.Context) (*MaintenanceOrder, error) {
	nodes, err := moq.Limit(1).All(setContextOp(ctx, moq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{maintenanceorder.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (moq *MaintenanceOrderQuery) FirstX(ctx context.Context) *MaintenanceOrder {
	node, err := moq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first MaintenanceOrder ID from the query.
// Returns a *NotFoundError when no MaintenanceOrder ID was found.
func (moq *MaintenanceOrderQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = moq.Limit(1).IDs(setContextOp(ctx, moq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{maintenanceorder.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (moq *MaintenanceOrderQuery) FirstIDX(ctx context.Context) int {
	id, err := moq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single MaintenanceOrder entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one MaintenanceOrder entity is found.
// Returns a *NotFoundError when no MaintenanceOrder entities are found.
func (moq *MaintenanceOrderQuery) Only(ctx context.Context) (*MaintenanceOrder, error) {
	nodes, err := moq.Limit(2).All(setContextOp(ctx, moq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{maintenanceorder.Label}
	default:
		return nil, &NotSingularError{maintenanceorder.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (moq *MaintenanceOrderQuery) OnlyX(ctx context.Context) *MaintenanceOrder {
	node, err := moq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only MaintenanceOrder ID in the query.
// Returns a *NotSingularError when more than one MaintenanceOrder ID is found.
// Returns a *NotFoundError when no entities are found.
func (moq *MaintenanceOrderQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = moq.Limit(2).IDs(setContextOp(ctx, moq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{maintenanceorder.Label}
	default:
		err = &NotSingularError{maintenanceorder.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (moq *MaintenanceOrderQuery) OnlyIDX(ctx context.Context) int {
	id, err := moq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of MaintenanceOrders.
func (moq *MaintenanceOrderQuery) All(ctx context.Context) ([]*MaintenanceOrder, error) {
	ctx = setContextOp(ctx, moq.ctx, ent.OpQueryAll)
	if err := moq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*MaintenanceOrder, *MaintenanceOrderQuery]()
	return withInterceptors[[]*MaintenanceOrder](ctx, moq, qr, moq.inters)
}

// AllX is like All, but panics if an error occurs.
func (moq *MaintenanceOrderQuery) AllX(ctx context.Context) []*MaintenanceOrder {
	nodes, err := moq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of MaintenanceOrder IDs.
func (moq *MaintenanceOrderQuery) IDs(ctx context.Context) (ids []int, err error) {
	if moq.ctx.Unique == nil && moq.path != nil {
		moq.Unique(true)
	}
	ctx = setContextOp(ctx, moq.ctx, ent.OpQueryIDs)
	if err = moq.Select(maintenanceorder.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (moq *MaintenanceOrderQuery) IDsX(ctx context.Context) []int {
	ids, err := moq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (moq *MaintenanceOrderQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, moq.ctx, ent.OpQueryCount)
	if err := moq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, moq, querierCount[*MaintenanceOrderQuery](), moq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (moq *MaintenanceOrderQuery) CountX(ctx context.Context) int {
	count, err := moq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (moq *MaintenanceOrderQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, moq.ctx, ent.OpQueryExist)
	switch _, err := moq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (moq *MaintenanceOrderQuery) ExistX(ctx context.Context) bool {
	exist, err := moq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the MaintenanceOrderQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (moq *MaintenanceOrderQuery) Clone() *MaintenanceOrderQuery {
	if moq == nil {
		return nil
	}
	return &MaintenanceOrderQuery{
		config:           moq.config,
		ctx:              moq.ctx.Clone(),
		order:            append([]maintenanceorder.OrderOption{}, moq.order...),
		inters:           append([]Interceptor{}, moq.inters...),
		predicates:       append([]predicate.MaintenanceOrder{}, moq.predicates...),
		withBranch:       moq.withBranch.Clone(),
		withEquipment:    moq.withEquipment.Clone(),
		withRequester:    moq.withRequester.Clone(),
		withApprover:     moq.withApprover.Clone(),
		withTechnician:   moq.withTechnician.Clone(),
		withCostItems:    moq.withCostItems.Clone(),
		withInteractions: moq.withInteractions.Clone(),
		// clone intermediate query.
		sql:  moq.sql.Clone(),
		path: moq.path,
	}
}

// WithBranch tells the query-builder to eager-load the nodes that are connected to
// the "branch" edge. The optional arguments are used to configure the query builder of the edge.
func (moq *MaintenanceOrderQuery) WithBranch(opts ...func(*BranchQuery)) *MaintenanceOrderQuery {
	query := (&BranchClient{config: moq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	moq.withBranch = query
	return moq
}

// WithEquipment tells the query-builder to eager-load the nodes that are connected to
// the "equipment" edge. The optional arguments are used to configure the query builder of the edge.
func (moq *MaintenanceOrderQuery) WithEquipment(opts ...func(*EquipmentQuery)) *MaintenanceOrderQuery {
	query := (&EquipmentClient{config: moq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	moq.withEquipment = query
	return moq
}

// WithRequester tells the query-builder to eager-load the nodes that are connected to
// the "requester" edge. The optional arguments are used to configure the query builder of the edge.
func (moq *MaintenanceOrderQuery) WithRequester(opts ...func(*UserQuery)) *MaintenanceOrderQuery {
	query := (&UserClient{config: moq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	moq.withRequester = query
	return moq
}

// WithApprover tells the query-builder to eager-load the nodes that are connected to
// the "approver" edge. The optional arguments are used to configure the query builder of the edge.
func (moq *MaintenanceOrderQuery) WithApprover(opts ...func(*UserQuery)) *MaintenanceOrderQuery {
	query := (&UserClient{config: moq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	moq.withApprover = query
	return moq
}

// WithTechnician tells the query-builder to eager-load the nodes that are connected to
// the "technician" edge. The optional arguments are used to configure the query builder of the edge.
func (moq *MaintenanceOrderQuery) WithTechnician(opts ...func(*UserQuery)) *MaintenanceOrderQuery {
	query := (&UserClient{config: moq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	moq.withTechnician = query
	return moq
}

// WithCostItems tells the query-builder to eager-load the nodes that are connected to
// the "cost_items" edge. The optional arguments are used to configure the query builder of the edge.
func (moq *MaintenanceOrderQuery) WithCostItems(opts ...func(*CostItemQuery)) *MaintenanceOrderQuery {
	query := (&CostItemClient{config: moq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	moq.withCostItems = query
	return moq
}

// WithInteractions tells the query-builder to eager-load the nodes that are connected to
// the "interactions" edge. The optional arguments are used to configure the query builder of the edge.
func (moq *MaintenanceOrderQuery) WithInteractions(opts ...func(*InteractionQuery)) *MaintenanceOrderQuery {
	query := (&InteractionClient{config: moq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	moq.withInteractions = query
	return moq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.MaintenanceOrder.Query().
//		GroupBy(maintenanceorder.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (moq *MaintenanceOrderQuery) GroupBy(field string, fields ...string) *MaintenanceOrderGroupBy {
	moq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &MaintenanceOrderGroupBy{build: moq}
	grbuild.flds = &moq.ctx.Fields
	grbuild.label = maintenanceorder.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.MaintenanceOrder.Query().
//		Select(maintenanceorder.FieldCreatedAt).
//		Scan(ctx, &v)
func (moq *MaintenanceOrderQuery) Select(fields ...string) *MaintenanceOrderSelect {
	moq.ctx.Fields = append(moq.ctx.Fields, fields...)
	sbuild := &MaintenanceOrderSelect{MaintenanceOrderQuery: moq}
	sbuild.label = maintenanceorder.Label
	sbuild.flds, sbuild.scan = &moq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a MaintenanceOrderSelect configured with the given aggregations.
func (moq *MaintenanceOrderQuery) Aggregate(fns ...AggregateFunc) *MaintenanceOrderSelect {
	return moq.Select().Aggregate(fns...)
}

func (moq *MaintenanceOrderQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range moq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, moq); err != nil {
				return err
			}
		}
	}
	for _, f := range moq.ctx.Fields {
		if !maintenanceorder.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if moq.path != nil {
		prev, err := moq.path(ctx)
		if err != nil {
			return err
		}
		moq.sql = prev
	}
	return nil
}

func (moq *MaintenanceOrderQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*MaintenanceOrder, error) {
	var (
		nodes       = []*MaintenanceOrder{}
		_spec       = moq.querySpec()
		loadedTypes = [7]bool{
			moq.withBranch != nil,
			moq.withEquipment != nil,
			moq.withRequester != nil,
			moq.withApprover != nil,
			moq.withTechnician != nil,
			moq.withCostItems != nil,
			moq.withInteractions != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*MaintenanceOrder).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &MaintenanceOrder{config: moq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, moq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := moq.withBranch; query != nil {
		if err := moq.loadBranch(ctx, query, nodes, nil,
			func(n *MaintenanceOrder, e *Branch) { n.Edges.Branch = e }); err != nil {
			return nil, err
		}
	}
	if query := moq.withEquipment; query != nil {
		if err := moq.loadEquipment(ctx, query, nodes, nil,
			func(n *MaintenanceOrder, e *Equipment) { n.Edges.Equipment = e }); err != nil {
			return nil, err
		}
	}
	if query := moq.withRequester; query != nil {
		if err := moq.loadRequester(ctx, query, nodes, nil,
			func(n *MaintenanceOrder, e *User) { n.Edges.Requester = e }); err != nil {
			return nil, err
		}
	}
	if query := moq.withApprover; query != nil {
		if err := moq.loadApprover(ctx, query, nodes, nil,
			func(n *MaintenanceOrder, e *User) { n.Edges.Approver = e }); err != nil {
			return nil, err
		}
	}
	if query := moq.withTechnician; query != nil {
		if err := moq.loadTechnician(ctx, query, nodes, nil,
			func(n *MaintenanceOrder, e *User) { n.Edges.Technician = e }); err != nil {
			return nil, err
		}
	}
	if query := moq.withCostItems; query != nil {
		if err := moq.loadCostItems(ctx, query, nodes,
			func(n *MaintenanceOrder) { n.Edges.CostItems = []*CostItem{} },
			func(n *MaintenanceOrder, e *CostItem) { n.Edges.CostItems = append(n.Edges.CostItems, e) }); err != nil {
			return nil, err
		}
	}
	if query := moq.withInteractions; query != nil {
		if err := moq.loadInteractions(ctx, query, nodes,
			func(n *MaintenanceOrder) { n.Edges.Interactions = []*Interaction{} },
			func(n *MaintenanceOrder, e *Interaction) { n.Edges.Interactions = append(n.Edges.Interactions, e) }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (moq *MaintenanceOrderQuery) loadBranch(ctx context.Context, query *BranchQuery, nodes []*MaintenanceOrder, init func(*MaintenanceOrder), assign func(*MaintenanceOrder, *Branch)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*MaintenanceOrder)
	for i := range nodes {
		fk := nodes[i].BranchID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(branch.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "branch_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (moq *MaintenanceOrderQuery) loadEquipment(ctx context.Context, query *EquipmentQuery, nodes []*MaintenanceOrder, init func(*MaintenanceOrder), assign func(*MaintenanceOrder, *Equipment)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*MaintenanceOrder)
	for i := range nodes {
		fk := nodes[i].EquipmentID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(equipment.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "equipment_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (moq *MaintenanceOrderQuery) loadRequester(ctx context.Context, query *UserQuery, nodes []*MaintenanceOrder, init func(*MaintenanceOrder), assign func(*MaintenanceOrder, *User)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*MaintenanceOrder)
	for i := range nodes {
		fk := nodes[i].RequesterID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(user.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "requester_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (moq *MaintenanceOrderQuery) loadApprover(ctx context.Context, query *UserQuery, nodes []*MaintenanceOrder, init func(*MaintenanceOrder), assign func(*MaintenanceOrder, *User)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*MaintenanceOrder)
	for i := range nodes {
		fk := nodes[i].ApproverID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(user.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "approver_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (moq *MaintenanceOrderQuery) loadTechnician(ctx context.Context, query *UserQuery, nodes []*MaintenanceOrder, init func(*MaintenanceOrder), assign func(*MaintenanceOrder, *User)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*MaintenanceOrder)
	for i := range nodes {
		fk := nodes[i].TechnicianID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(user.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "technician_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (moq *MaintenanceOrderQuery) loadCostItems(ctx context.Context, query *CostItemQuery, nodes []*MaintenanceOrder, init func(*MaintenanceOrder), assign func(*MaintenanceOrder, *CostItem)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*MaintenanceOrder)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(costitem.FieldMaintenanceOrderID)
	}
	query.Where(predicate.CostItem(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(maintenanceorder.CostItemsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.MaintenanceOrderID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "maintenance_order_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (moq *MaintenanceOrderQuery) loadInteractions(ctx context.Context, query *InteractionQuery, nodes []*MaintenanceOrder, init func(*MaintenanceOrder), assign func(*MaintenanceOrder, *Interaction)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*MaintenanceOrder)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	query.withFKs = true
	query.Where(predicate.Interaction(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(maintenanceorder.InteractionsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.maintenance_order_interactions
		if fk == nil {
			return fmt.Errorf(`foreign-key "maintenance_order_interactions" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "maintenance_order_interactions" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (moq *MaintenanceOrderQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := moq.querySpec()
	_spec.Node.Columns = moq.ctx.Fields
	if len(moq.ctx.Fields) > 0 {
		_spec.Unique = moq.ctx.Unique != nil && *moq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, moq.driver, _spec)
}

func (moq *MaintenanceOrderQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(maintenanceorder.Table, maintenanceorder.Columns, sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt))
	_spec.From = moq.sql
	if unique := moq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if moq.path != nil {
		_spec.Unique = true
	}
	if fields := moq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, maintenanceorder.FieldID)
		for i := range fields {
			if fields[i] != maintenanceorder.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if moq.withBranch != nil {
			_spec.Node.AddColumnOnce(maintenanceorder.FieldBranchID)
		}
		if moq.withEquipment != nil {
			_spec.Node.AddColumnOnce(maintenanceorder.FieldEquipmentID)
		}
		if moq.withRequester != nil {
			_spec.Node.AddColumnOnce(maintenanceorder.FieldRequesterID)
		}
		if moq.withApprover != nil {
			_spec.Node.AddColumnOnce(maintenanceorder.FieldApproverID)
		}
		if moq.withTechnician != nil {
			_spec.Node.AddColumnOnce(maintenanceorder.FieldTechnicianID)
		}
	}
	if ps := moq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := moq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := moq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := moq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (moq *MaintenanceOrderQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(moq.driver.Dialect())
	t1 := builder.Table(maintenanceorder.Table)
	columns := moq.ctx.Fields
	if len(columns) == 0 {
		columns = maintenanceorder.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if moq.sql != nil {
		selector = moq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if moq.ctx.Unique != nil && *moq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range moq.predicates {
		p(selector)
	}
	for _, p := range moq.order {
		p(selector)
	}
	if offset := moq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := moq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// MaintenanceOrderGroupBy is the group-by builder for MaintenanceOrder entities.
type MaintenanceOrderGroupBy struct {
	selector
	build *MaintenanceOrderQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (mogb *MaintenanceOrderGroupBy) Aggregate(fns ...AggregateFunc) *MaintenanceOrderGroupBy {
	mogb.fns = append(mogb.fns, fns...)
	return mogb
}

// Scan applies the selector query and scans the result into the given value.
func (mogb *MaintenanceOrderGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, mogb.build.ctx, ent.OpQueryGroupBy)
	if err := mogb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*MaintenanceOrderQuery, *MaintenanceOrderGroupBy](ctx, mogb.build, mogb, mogb.build.inters, v)
}

func (mogb *MaintenanceOrderGroupBy) sqlScan(ctx context.Context, root *MaintenanceOrderQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(mogb.fns))
	for _, fn := range mogb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*mogb.flds)+len(mogb.fns))
		for _, f := range *mogb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*mogb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := mogb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// MaintenanceOrderSelect is the builder for selecting fields of MaintenanceOrder entities.
type MaintenanceOrderSelect struct {
	*MaintenanceOrderQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (mos *MaintenanceOrderSelect) Aggregate(fns ...AggregateFunc) *MaintenanceOrderSelect {
	mos.fns = append(mos.fns, fns...)
	return mos
}

// Scan applies the selector query and scans the result into the given value.
func (mos *MaintenanceOrderSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, mos.ctx, ent.OpQuerySelect)
	if err := mos.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*MaintenanceOrderQuery, *MaintenanceOrderSelect](ctx, mos.MaintenanceOrderQuery, mos, mos.inters, v)
}

func (mos *MaintenanceOrderSelect) sqlScan(ctx context.Context, root *MaintenanceOrderQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(mos.fns))
	for _, fn := range mos.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*mos.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := mos.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
