// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"
	"tradicao/internal/ent/branch"
	"tradicao/internal/ent/equipment"
	"tradicao/internal/ent/maintenanceorder"
	"tradicao/internal/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// BranchUpdate is the builder for updating Branch entities.
type BranchUpdate struct {
	config
	hooks    []Hook
	mutation *BranchMutation
}

// Where appends a list predicates to the BranchUpdate builder.
func (bu *BranchUpdate) Where(ps ...predicate.Branch) *BranchUpdate {
	bu.mutation.Where(ps...)
	return bu
}

// SetUpdatedAt sets the "updated_at" field.
func (bu *BranchUpdate) SetUpdatedAt(t time.Time) *BranchUpdate {
	bu.mutation.SetUpdatedAt(t)
	return bu
}

// SetActive sets the "active" field.
func (bu *BranchUpdate) SetActive(b bool) *BranchUpdate {
	bu.mutation.SetActive(b)
	return bu
}

// SetNillableActive sets the "active" field if the given value is not nil.
func (bu *BranchUpdate) SetNillableActive(b *bool) *BranchUpdate {
	if b != nil {
		bu.SetActive(*b)
	}
	return bu
}

// SetName sets the "name" field.
func (bu *BranchUpdate) SetName(s string) *BranchUpdate {
	bu.mutation.SetName(s)
	return bu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (bu *BranchUpdate) SetNillableName(s *string) *BranchUpdate {
	if s != nil {
		bu.SetName(*s)
	}
	return bu
}

// SetCode sets the "code" field.
func (bu *BranchUpdate) SetCode(s string) *BranchUpdate {
	bu.mutation.SetCode(s)
	return bu
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (bu *BranchUpdate) SetNillableCode(s *string) *BranchUpdate {
	if s != nil {
		bu.SetCode(*s)
	}
	return bu
}

// SetAddress sets the "address" field.
func (bu *BranchUpdate) SetAddress(s string) *BranchUpdate {
	bu.mutation.SetAddress(s)
	return bu
}

// SetNillableAddress sets the "address" field if the given value is not nil.
func (bu *BranchUpdate) SetNillableAddress(s *string) *BranchUpdate {
	if s != nil {
		bu.SetAddress(*s)
	}
	return bu
}

// SetCity sets the "city" field.
func (bu *BranchUpdate) SetCity(s string) *BranchUpdate {
	bu.mutation.SetCity(s)
	return bu
}

// SetNillableCity sets the "city" field if the given value is not nil.
func (bu *BranchUpdate) SetNillableCity(s *string) *BranchUpdate {
	if s != nil {
		bu.SetCity(*s)
	}
	return bu
}

// SetState sets the "state" field.
func (bu *BranchUpdate) SetState(s string) *BranchUpdate {
	bu.mutation.SetState(s)
	return bu
}

// SetNillableState sets the "state" field if the given value is not nil.
func (bu *BranchUpdate) SetNillableState(s *string) *BranchUpdate {
	if s != nil {
		bu.SetState(*s)
	}
	return bu
}

// SetZipCode sets the "zip_code" field.
func (bu *BranchUpdate) SetZipCode(s string) *BranchUpdate {
	bu.mutation.SetZipCode(s)
	return bu
}

// SetNillableZipCode sets the "zip_code" field if the given value is not nil.
func (bu *BranchUpdate) SetNillableZipCode(s *string) *BranchUpdate {
	if s != nil {
		bu.SetZipCode(*s)
	}
	return bu
}

// SetPhone sets the "phone" field.
func (bu *BranchUpdate) SetPhone(s string) *BranchUpdate {
	bu.mutation.SetPhone(s)
	return bu
}

// SetNillablePhone sets the "phone" field if the given value is not nil.
func (bu *BranchUpdate) SetNillablePhone(s *string) *BranchUpdate {
	if s != nil {
		bu.SetPhone(*s)
	}
	return bu
}

// SetEmail sets the "email" field.
func (bu *BranchUpdate) SetEmail(s string) *BranchUpdate {
	bu.mutation.SetEmail(s)
	return bu
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (bu *BranchUpdate) SetNillableEmail(s *string) *BranchUpdate {
	if s != nil {
		bu.SetEmail(*s)
	}
	return bu
}

// ClearEmail clears the value of the "email" field.
func (bu *BranchUpdate) ClearEmail() *BranchUpdate {
	bu.mutation.ClearEmail()
	return bu
}

// SetStatus sets the "status" field.
func (bu *BranchUpdate) SetStatus(s string) *BranchUpdate {
	bu.mutation.SetStatus(s)
	return bu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (bu *BranchUpdate) SetNillableStatus(s *string) *BranchUpdate {
	if s != nil {
		bu.SetStatus(*s)
	}
	return bu
}

// SetInaugurationDate sets the "inauguration_date" field.
func (bu *BranchUpdate) SetInaugurationDate(t time.Time) *BranchUpdate {
	bu.mutation.SetInaugurationDate(t)
	return bu
}

// SetNillableInaugurationDate sets the "inauguration_date" field if the given value is not nil.
func (bu *BranchUpdate) SetNillableInaugurationDate(t *time.Time) *BranchUpdate {
	if t != nil {
		bu.SetInaugurationDate(*t)
	}
	return bu
}

// SetIsActive sets the "is_active" field.
func (bu *BranchUpdate) SetIsActive(b bool) *BranchUpdate {
	bu.mutation.SetIsActive(b)
	return bu
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (bu *BranchUpdate) SetNillableIsActive(b *bool) *BranchUpdate {
	if b != nil {
		bu.SetIsActive(*b)
	}
	return bu
}

// AddEquipmentIDs adds the "equipment" edge to the Equipment entity by IDs.
func (bu *BranchUpdate) AddEquipmentIDs(ids ...int) *BranchUpdate {
	bu.mutation.AddEquipmentIDs(ids...)
	return bu
}

// AddEquipment adds the "equipment" edges to the Equipment entity.
func (bu *BranchUpdate) AddEquipment(e ...*Equipment) *BranchUpdate {
	ids := make([]int, len(e))
	for i := range e {
		ids[i] = e[i].ID
	}
	return bu.AddEquipmentIDs(ids...)
}

// AddMaintenanceOrderIDs adds the "maintenance_orders" edge to the MaintenanceOrder entity by IDs.
func (bu *BranchUpdate) AddMaintenanceOrderIDs(ids ...int) *BranchUpdate {
	bu.mutation.AddMaintenanceOrderIDs(ids...)
	return bu
}

// AddMaintenanceOrders adds the "maintenance_orders" edges to the MaintenanceOrder entity.
func (bu *BranchUpdate) AddMaintenanceOrders(m ...*MaintenanceOrder) *BranchUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return bu.AddMaintenanceOrderIDs(ids...)
}

// Mutation returns the BranchMutation object of the builder.
func (bu *BranchUpdate) Mutation() *BranchMutation {
	return bu.mutation
}

// ClearEquipment clears all "equipment" edges to the Equipment entity.
func (bu *BranchUpdate) ClearEquipment() *BranchUpdate {
	bu.mutation.ClearEquipment()
	return bu
}

// RemoveEquipmentIDs removes the "equipment" edge to Equipment entities by IDs.
func (bu *BranchUpdate) RemoveEquipmentIDs(ids ...int) *BranchUpdate {
	bu.mutation.RemoveEquipmentIDs(ids...)
	return bu
}

// RemoveEquipment removes "equipment" edges to Equipment entities.
func (bu *BranchUpdate) RemoveEquipment(e ...*Equipment) *BranchUpdate {
	ids := make([]int, len(e))
	for i := range e {
		ids[i] = e[i].ID
	}
	return bu.RemoveEquipmentIDs(ids...)
}

// ClearMaintenanceOrders clears all "maintenance_orders" edges to the MaintenanceOrder entity.
func (bu *BranchUpdate) ClearMaintenanceOrders() *BranchUpdate {
	bu.mutation.ClearMaintenanceOrders()
	return bu
}

// RemoveMaintenanceOrderIDs removes the "maintenance_orders" edge to MaintenanceOrder entities by IDs.
func (bu *BranchUpdate) RemoveMaintenanceOrderIDs(ids ...int) *BranchUpdate {
	bu.mutation.RemoveMaintenanceOrderIDs(ids...)
	return bu
}

// RemoveMaintenanceOrders removes "maintenance_orders" edges to MaintenanceOrder entities.
func (bu *BranchUpdate) RemoveMaintenanceOrders(m ...*MaintenanceOrder) *BranchUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return bu.RemoveMaintenanceOrderIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (bu *BranchUpdate) Save(ctx context.Context) (int, error) {
	bu.defaults()
	return withHooks(ctx, bu.sqlSave, bu.mutation, bu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (bu *BranchUpdate) SaveX(ctx context.Context) int {
	affected, err := bu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (bu *BranchUpdate) Exec(ctx context.Context) error {
	_, err := bu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (bu *BranchUpdate) ExecX(ctx context.Context) {
	if err := bu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (bu *BranchUpdate) defaults() {
	if _, ok := bu.mutation.UpdatedAt(); !ok {
		v := branch.UpdateDefaultUpdatedAt()
		bu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (bu *BranchUpdate) check() error {
	if v, ok := bu.mutation.Name(); ok {
		if err := branch.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Branch.name": %w`, err)}
		}
	}
	if v, ok := bu.mutation.Code(); ok {
		if err := branch.CodeValidator(v); err != nil {
			return &ValidationError{Name: "code", err: fmt.Errorf(`ent: validator failed for field "Branch.code": %w`, err)}
		}
	}
	if v, ok := bu.mutation.Address(); ok {
		if err := branch.AddressValidator(v); err != nil {
			return &ValidationError{Name: "address", err: fmt.Errorf(`ent: validator failed for field "Branch.address": %w`, err)}
		}
	}
	if v, ok := bu.mutation.City(); ok {
		if err := branch.CityValidator(v); err != nil {
			return &ValidationError{Name: "city", err: fmt.Errorf(`ent: validator failed for field "Branch.city": %w`, err)}
		}
	}
	if v, ok := bu.mutation.State(); ok {
		if err := branch.StateValidator(v); err != nil {
			return &ValidationError{Name: "state", err: fmt.Errorf(`ent: validator failed for field "Branch.state": %w`, err)}
		}
	}
	if v, ok := bu.mutation.ZipCode(); ok {
		if err := branch.ZipCodeValidator(v); err != nil {
			return &ValidationError{Name: "zip_code", err: fmt.Errorf(`ent: validator failed for field "Branch.zip_code": %w`, err)}
		}
	}
	if v, ok := bu.mutation.Phone(); ok {
		if err := branch.PhoneValidator(v); err != nil {
			return &ValidationError{Name: "phone", err: fmt.Errorf(`ent: validator failed for field "Branch.phone": %w`, err)}
		}
	}
	return nil
}

func (bu *BranchUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := bu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(branch.Table, branch.Columns, sqlgraph.NewFieldSpec(branch.FieldID, field.TypeInt))
	if ps := bu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := bu.mutation.UpdatedAt(); ok {
		_spec.SetField(branch.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := bu.mutation.Active(); ok {
		_spec.SetField(branch.FieldActive, field.TypeBool, value)
	}
	if value, ok := bu.mutation.Name(); ok {
		_spec.SetField(branch.FieldName, field.TypeString, value)
	}
	if value, ok := bu.mutation.Code(); ok {
		_spec.SetField(branch.FieldCode, field.TypeString, value)
	}
	if value, ok := bu.mutation.Address(); ok {
		_spec.SetField(branch.FieldAddress, field.TypeString, value)
	}
	if value, ok := bu.mutation.City(); ok {
		_spec.SetField(branch.FieldCity, field.TypeString, value)
	}
	if value, ok := bu.mutation.State(); ok {
		_spec.SetField(branch.FieldState, field.TypeString, value)
	}
	if value, ok := bu.mutation.ZipCode(); ok {
		_spec.SetField(branch.FieldZipCode, field.TypeString, value)
	}
	if value, ok := bu.mutation.Phone(); ok {
		_spec.SetField(branch.FieldPhone, field.TypeString, value)
	}
	if value, ok := bu.mutation.Email(); ok {
		_spec.SetField(branch.FieldEmail, field.TypeString, value)
	}
	if bu.mutation.EmailCleared() {
		_spec.ClearField(branch.FieldEmail, field.TypeString)
	}
	if value, ok := bu.mutation.Status(); ok {
		_spec.SetField(branch.FieldStatus, field.TypeString, value)
	}
	if value, ok := bu.mutation.InaugurationDate(); ok {
		_spec.SetField(branch.FieldInaugurationDate, field.TypeTime, value)
	}
	if value, ok := bu.mutation.IsActive(); ok {
		_spec.SetField(branch.FieldIsActive, field.TypeBool, value)
	}
	if bu.mutation.EquipmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   branch.EquipmentTable,
			Columns: []string{branch.EquipmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(equipment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := bu.mutation.RemovedEquipmentIDs(); len(nodes) > 0 && !bu.mutation.EquipmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   branch.EquipmentTable,
			Columns: []string{branch.EquipmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(equipment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := bu.mutation.EquipmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   branch.EquipmentTable,
			Columns: []string{branch.EquipmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(equipment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if bu.mutation.MaintenanceOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   branch.MaintenanceOrdersTable,
			Columns: []string{branch.MaintenanceOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := bu.mutation.RemovedMaintenanceOrdersIDs(); len(nodes) > 0 && !bu.mutation.MaintenanceOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   branch.MaintenanceOrdersTable,
			Columns: []string{branch.MaintenanceOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := bu.mutation.MaintenanceOrdersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   branch.MaintenanceOrdersTable,
			Columns: []string{branch.MaintenanceOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, bu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{branch.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	bu.mutation.done = true
	return n, nil
}

// BranchUpdateOne is the builder for updating a single Branch entity.
type BranchUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *BranchMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (buo *BranchUpdateOne) SetUpdatedAt(t time.Time) *BranchUpdateOne {
	buo.mutation.SetUpdatedAt(t)
	return buo
}

// SetActive sets the "active" field.
func (buo *BranchUpdateOne) SetActive(b bool) *BranchUpdateOne {
	buo.mutation.SetActive(b)
	return buo
}

// SetNillableActive sets the "active" field if the given value is not nil.
func (buo *BranchUpdateOne) SetNillableActive(b *bool) *BranchUpdateOne {
	if b != nil {
		buo.SetActive(*b)
	}
	return buo
}

// SetName sets the "name" field.
func (buo *BranchUpdateOne) SetName(s string) *BranchUpdateOne {
	buo.mutation.SetName(s)
	return buo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (buo *BranchUpdateOne) SetNillableName(s *string) *BranchUpdateOne {
	if s != nil {
		buo.SetName(*s)
	}
	return buo
}

// SetCode sets the "code" field.
func (buo *BranchUpdateOne) SetCode(s string) *BranchUpdateOne {
	buo.mutation.SetCode(s)
	return buo
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (buo *BranchUpdateOne) SetNillableCode(s *string) *BranchUpdateOne {
	if s != nil {
		buo.SetCode(*s)
	}
	return buo
}

// SetAddress sets the "address" field.
func (buo *BranchUpdateOne) SetAddress(s string) *BranchUpdateOne {
	buo.mutation.SetAddress(s)
	return buo
}

// SetNillableAddress sets the "address" field if the given value is not nil.
func (buo *BranchUpdateOne) SetNillableAddress(s *string) *BranchUpdateOne {
	if s != nil {
		buo.SetAddress(*s)
	}
	return buo
}

// SetCity sets the "city" field.
func (buo *BranchUpdateOne) SetCity(s string) *BranchUpdateOne {
	buo.mutation.SetCity(s)
	return buo
}

// SetNillableCity sets the "city" field if the given value is not nil.
func (buo *BranchUpdateOne) SetNillableCity(s *string) *BranchUpdateOne {
	if s != nil {
		buo.SetCity(*s)
	}
	return buo
}

// SetState sets the "state" field.
func (buo *BranchUpdateOne) SetState(s string) *BranchUpdateOne {
	buo.mutation.SetState(s)
	return buo
}

// SetNillableState sets the "state" field if the given value is not nil.
func (buo *BranchUpdateOne) SetNillableState(s *string) *BranchUpdateOne {
	if s != nil {
		buo.SetState(*s)
	}
	return buo
}

// SetZipCode sets the "zip_code" field.
func (buo *BranchUpdateOne) SetZipCode(s string) *BranchUpdateOne {
	buo.mutation.SetZipCode(s)
	return buo
}

// SetNillableZipCode sets the "zip_code" field if the given value is not nil.
func (buo *BranchUpdateOne) SetNillableZipCode(s *string) *BranchUpdateOne {
	if s != nil {
		buo.SetZipCode(*s)
	}
	return buo
}

// SetPhone sets the "phone" field.
func (buo *BranchUpdateOne) SetPhone(s string) *BranchUpdateOne {
	buo.mutation.SetPhone(s)
	return buo
}

// SetNillablePhone sets the "phone" field if the given value is not nil.
func (buo *BranchUpdateOne) SetNillablePhone(s *string) *BranchUpdateOne {
	if s != nil {
		buo.SetPhone(*s)
	}
	return buo
}

// SetEmail sets the "email" field.
func (buo *BranchUpdateOne) SetEmail(s string) *BranchUpdateOne {
	buo.mutation.SetEmail(s)
	return buo
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (buo *BranchUpdateOne) SetNillableEmail(s *string) *BranchUpdateOne {
	if s != nil {
		buo.SetEmail(*s)
	}
	return buo
}

// ClearEmail clears the value of the "email" field.
func (buo *BranchUpdateOne) ClearEmail() *BranchUpdateOne {
	buo.mutation.ClearEmail()
	return buo
}

// SetStatus sets the "status" field.
func (buo *BranchUpdateOne) SetStatus(s string) *BranchUpdateOne {
	buo.mutation.SetStatus(s)
	return buo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (buo *BranchUpdateOne) SetNillableStatus(s *string) *BranchUpdateOne {
	if s != nil {
		buo.SetStatus(*s)
	}
	return buo
}

// SetInaugurationDate sets the "inauguration_date" field.
func (buo *BranchUpdateOne) SetInaugurationDate(t time.Time) *BranchUpdateOne {
	buo.mutation.SetInaugurationDate(t)
	return buo
}

// SetNillableInaugurationDate sets the "inauguration_date" field if the given value is not nil.
func (buo *BranchUpdateOne) SetNillableInaugurationDate(t *time.Time) *BranchUpdateOne {
	if t != nil {
		buo.SetInaugurationDate(*t)
	}
	return buo
}

// SetIsActive sets the "is_active" field.
func (buo *BranchUpdateOne) SetIsActive(b bool) *BranchUpdateOne {
	buo.mutation.SetIsActive(b)
	return buo
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (buo *BranchUpdateOne) SetNillableIsActive(b *bool) *BranchUpdateOne {
	if b != nil {
		buo.SetIsActive(*b)
	}
	return buo
}

// AddEquipmentIDs adds the "equipment" edge to the Equipment entity by IDs.
func (buo *BranchUpdateOne) AddEquipmentIDs(ids ...int) *BranchUpdateOne {
	buo.mutation.AddEquipmentIDs(ids...)
	return buo
}

// AddEquipment adds the "equipment" edges to the Equipment entity.
func (buo *BranchUpdateOne) AddEquipment(e ...*Equipment) *BranchUpdateOne {
	ids := make([]int, len(e))
	for i := range e {
		ids[i] = e[i].ID
	}
	return buo.AddEquipmentIDs(ids...)
}

// AddMaintenanceOrderIDs adds the "maintenance_orders" edge to the MaintenanceOrder entity by IDs.
func (buo *BranchUpdateOne) AddMaintenanceOrderIDs(ids ...int) *BranchUpdateOne {
	buo.mutation.AddMaintenanceOrderIDs(ids...)
	return buo
}

// AddMaintenanceOrders adds the "maintenance_orders" edges to the MaintenanceOrder entity.
func (buo *BranchUpdateOne) AddMaintenanceOrders(m ...*MaintenanceOrder) *BranchUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return buo.AddMaintenanceOrderIDs(ids...)
}

// Mutation returns the BranchMutation object of the builder.
func (buo *BranchUpdateOne) Mutation() *BranchMutation {
	return buo.mutation
}

// ClearEquipment clears all "equipment" edges to the Equipment entity.
func (buo *BranchUpdateOne) ClearEquipment() *BranchUpdateOne {
	buo.mutation.ClearEquipment()
	return buo
}

// RemoveEquipmentIDs removes the "equipment" edge to Equipment entities by IDs.
func (buo *BranchUpdateOne) RemoveEquipmentIDs(ids ...int) *BranchUpdateOne {
	buo.mutation.RemoveEquipmentIDs(ids...)
	return buo
}

// RemoveEquipment removes "equipment" edges to Equipment entities.
func (buo *BranchUpdateOne) RemoveEquipment(e ...*Equipment) *BranchUpdateOne {
	ids := make([]int, len(e))
	for i := range e {
		ids[i] = e[i].ID
	}
	return buo.RemoveEquipmentIDs(ids...)
}

// ClearMaintenanceOrders clears all "maintenance_orders" edges to the MaintenanceOrder entity.
func (buo *BranchUpdateOne) ClearMaintenanceOrders() *BranchUpdateOne {
	buo.mutation.ClearMaintenanceOrders()
	return buo
}

// RemoveMaintenanceOrderIDs removes the "maintenance_orders" edge to MaintenanceOrder entities by IDs.
func (buo *BranchUpdateOne) RemoveMaintenanceOrderIDs(ids ...int) *BranchUpdateOne {
	buo.mutation.RemoveMaintenanceOrderIDs(ids...)
	return buo
}

// RemoveMaintenanceOrders removes "maintenance_orders" edges to MaintenanceOrder entities.
func (buo *BranchUpdateOne) RemoveMaintenanceOrders(m ...*MaintenanceOrder) *BranchUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return buo.RemoveMaintenanceOrderIDs(ids...)
}

// Where appends a list predicates to the BranchUpdate builder.
func (buo *BranchUpdateOne) Where(ps ...predicate.Branch) *BranchUpdateOne {
	buo.mutation.Where(ps...)
	return buo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (buo *BranchUpdateOne) Select(field string, fields ...string) *BranchUpdateOne {
	buo.fields = append([]string{field}, fields...)
	return buo
}

// Save executes the query and returns the updated Branch entity.
func (buo *BranchUpdateOne) Save(ctx context.Context) (*Branch, error) {
	buo.defaults()
	return withHooks(ctx, buo.sqlSave, buo.mutation, buo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (buo *BranchUpdateOne) SaveX(ctx context.Context) *Branch {
	node, err := buo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (buo *BranchUpdateOne) Exec(ctx context.Context) error {
	_, err := buo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (buo *BranchUpdateOne) ExecX(ctx context.Context) {
	if err := buo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (buo *BranchUpdateOne) defaults() {
	if _, ok := buo.mutation.UpdatedAt(); !ok {
		v := branch.UpdateDefaultUpdatedAt()
		buo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (buo *BranchUpdateOne) check() error {
	if v, ok := buo.mutation.Name(); ok {
		if err := branch.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Branch.name": %w`, err)}
		}
	}
	if v, ok := buo.mutation.Code(); ok {
		if err := branch.CodeValidator(v); err != nil {
			return &ValidationError{Name: "code", err: fmt.Errorf(`ent: validator failed for field "Branch.code": %w`, err)}
		}
	}
	if v, ok := buo.mutation.Address(); ok {
		if err := branch.AddressValidator(v); err != nil {
			return &ValidationError{Name: "address", err: fmt.Errorf(`ent: validator failed for field "Branch.address": %w`, err)}
		}
	}
	if v, ok := buo.mutation.City(); ok {
		if err := branch.CityValidator(v); err != nil {
			return &ValidationError{Name: "city", err: fmt.Errorf(`ent: validator failed for field "Branch.city": %w`, err)}
		}
	}
	if v, ok := buo.mutation.State(); ok {
		if err := branch.StateValidator(v); err != nil {
			return &ValidationError{Name: "state", err: fmt.Errorf(`ent: validator failed for field "Branch.state": %w`, err)}
		}
	}
	if v, ok := buo.mutation.ZipCode(); ok {
		if err := branch.ZipCodeValidator(v); err != nil {
			return &ValidationError{Name: "zip_code", err: fmt.Errorf(`ent: validator failed for field "Branch.zip_code": %w`, err)}
		}
	}
	if v, ok := buo.mutation.Phone(); ok {
		if err := branch.PhoneValidator(v); err != nil {
			return &ValidationError{Name: "phone", err: fmt.Errorf(`ent: validator failed for field "Branch.phone": %w`, err)}
		}
	}
	return nil
}

func (buo *BranchUpdateOne) sqlSave(ctx context.Context) (_node *Branch, err error) {
	if err := buo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(branch.Table, branch.Columns, sqlgraph.NewFieldSpec(branch.FieldID, field.TypeInt))
	id, ok := buo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Branch.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := buo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, branch.FieldID)
		for _, f := range fields {
			if !branch.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != branch.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := buo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := buo.mutation.UpdatedAt(); ok {
		_spec.SetField(branch.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := buo.mutation.Active(); ok {
		_spec.SetField(branch.FieldActive, field.TypeBool, value)
	}
	if value, ok := buo.mutation.Name(); ok {
		_spec.SetField(branch.FieldName, field.TypeString, value)
	}
	if value, ok := buo.mutation.Code(); ok {
		_spec.SetField(branch.FieldCode, field.TypeString, value)
	}
	if value, ok := buo.mutation.Address(); ok {
		_spec.SetField(branch.FieldAddress, field.TypeString, value)
	}
	if value, ok := buo.mutation.City(); ok {
		_spec.SetField(branch.FieldCity, field.TypeString, value)
	}
	if value, ok := buo.mutation.State(); ok {
		_spec.SetField(branch.FieldState, field.TypeString, value)
	}
	if value, ok := buo.mutation.ZipCode(); ok {
		_spec.SetField(branch.FieldZipCode, field.TypeString, value)
	}
	if value, ok := buo.mutation.Phone(); ok {
		_spec.SetField(branch.FieldPhone, field.TypeString, value)
	}
	if value, ok := buo.mutation.Email(); ok {
		_spec.SetField(branch.FieldEmail, field.TypeString, value)
	}
	if buo.mutation.EmailCleared() {
		_spec.ClearField(branch.FieldEmail, field.TypeString)
	}
	if value, ok := buo.mutation.Status(); ok {
		_spec.SetField(branch.FieldStatus, field.TypeString, value)
	}
	if value, ok := buo.mutation.InaugurationDate(); ok {
		_spec.SetField(branch.FieldInaugurationDate, field.TypeTime, value)
	}
	if value, ok := buo.mutation.IsActive(); ok {
		_spec.SetField(branch.FieldIsActive, field.TypeBool, value)
	}
	if buo.mutation.EquipmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   branch.EquipmentTable,
			Columns: []string{branch.EquipmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(equipment.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := buo.mutation.RemovedEquipmentIDs(); len(nodes) > 0 && !buo.mutation.EquipmentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   branch.EquipmentTable,
			Columns: []string{branch.EquipmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(equipment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := buo.mutation.EquipmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   branch.EquipmentTable,
			Columns: []string{branch.EquipmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(equipment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if buo.mutation.MaintenanceOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   branch.MaintenanceOrdersTable,
			Columns: []string{branch.MaintenanceOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := buo.mutation.RemovedMaintenanceOrdersIDs(); len(nodes) > 0 && !buo.mutation.MaintenanceOrdersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   branch.MaintenanceOrdersTable,
			Columns: []string{branch.MaintenanceOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := buo.mutation.MaintenanceOrdersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   branch.MaintenanceOrdersTable,
			Columns: []string{branch.MaintenanceOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Branch{config: buo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, buo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{branch.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	buo.mutation.done = true
	return _node, nil
}
