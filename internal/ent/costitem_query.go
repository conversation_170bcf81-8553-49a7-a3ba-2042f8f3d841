// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"
	"tradicao/internal/ent/costitem"
	"tradicao/internal/ent/maintenanceorder"
	"tradicao/internal/ent/predicate"
	"tradicao/internal/ent/user"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CostItemQuery is the builder for querying CostItem entities.
type CostItemQuery struct {
	config
	ctx                  *QueryContext
	order                []costitem.OrderOption
	inters               []Interceptor
	predicates           []predicate.CostItem
	withMaintenanceOrder *MaintenanceOrderQuery
	withUser             *UserQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the CostItemQuery builder.
func (ciq *CostItemQuery) Where(ps ...predicate.CostItem) *CostItemQuery {
	ciq.predicates = append(ciq.predicates, ps...)
	return ciq
}

// Limit the number of records to be returned by this query.
func (ciq *CostItemQuery) Limit(limit int) *CostItemQuery {
	ciq.ctx.Limit = &limit
	return ciq
}

// Offset to start from.
func (ciq *CostItemQuery) Offset(offset int) *CostItemQuery {
	ciq.ctx.Offset = &offset
	return ciq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (ciq *CostItemQuery) Unique(unique bool) *CostItemQuery {
	ciq.ctx.Unique = &unique
	return ciq
}

// Order specifies how the records should be ordered.
func (ciq *CostItemQuery) Order(o ...costitem.OrderOption) *CostItemQuery {
	ciq.order = append(ciq.order, o...)
	return ciq
}

// QueryMaintenanceOrder chains the current query on the "maintenance_order" edge.
func (ciq *CostItemQuery) QueryMaintenanceOrder() *MaintenanceOrderQuery {
	query := (&MaintenanceOrderClient{config: ciq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := ciq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := ciq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(costitem.Table, costitem.FieldID, selector),
			sqlgraph.To(maintenanceorder.Table, maintenanceorder.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, costitem.MaintenanceOrderTable, costitem.MaintenanceOrderColumn),
		)
		fromU = sqlgraph.SetNeighbors(ciq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryUser chains the current query on the "user" edge.
func (ciq *CostItemQuery) QueryUser() *UserQuery {
	query := (&UserClient{config: ciq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := ciq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := ciq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(costitem.Table, costitem.FieldID, selector),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, costitem.UserTable, costitem.UserColumn),
		)
		fromU = sqlgraph.SetNeighbors(ciq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first CostItem entity from the query.
// Returns a *NotFoundError when no CostItem was found.
func (ciq *CostItemQuery) First(ctx context.Context) (*CostItem, error) {
	nodes, err := ciq.Limit(1).All(setContextOp(ctx, ciq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{costitem.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (ciq *CostItemQuery) FirstX(ctx context.Context) *CostItem {
	node, err := ciq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first CostItem ID from the query.
// Returns a *NotFoundError when no CostItem ID was found.
func (ciq *CostItemQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = ciq.Limit(1).IDs(setContextOp(ctx, ciq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{costitem.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (ciq *CostItemQuery) FirstIDX(ctx context.Context) int {
	id, err := ciq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single CostItem entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one CostItem entity is found.
// Returns a *NotFoundError when no CostItem entities are found.
func (ciq *CostItemQuery) Only(ctx context.Context) (*CostItem, error) {
	nodes, err := ciq.Limit(2).All(setContextOp(ctx, ciq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{costitem.Label}
	default:
		return nil, &NotSingularError{costitem.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (ciq *CostItemQuery) OnlyX(ctx context.Context) *CostItem {
	node, err := ciq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only CostItem ID in the query.
// Returns a *NotSingularError when more than one CostItem ID is found.
// Returns a *NotFoundError when no entities are found.
func (ciq *CostItemQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = ciq.Limit(2).IDs(setContextOp(ctx, ciq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{costitem.Label}
	default:
		err = &NotSingularError{costitem.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (ciq *CostItemQuery) OnlyIDX(ctx context.Context) int {
	id, err := ciq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of CostItems.
func (ciq *CostItemQuery) All(ctx context.Context) ([]*CostItem, error) {
	ctx = setContextOp(ctx, ciq.ctx, ent.OpQueryAll)
	if err := ciq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*CostItem, *CostItemQuery]()
	return withInterceptors[[]*CostItem](ctx, ciq, qr, ciq.inters)
}

// AllX is like All, but panics if an error occurs.
func (ciq *CostItemQuery) AllX(ctx context.Context) []*CostItem {
	nodes, err := ciq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of CostItem IDs.
func (ciq *CostItemQuery) IDs(ctx context.Context) (ids []int, err error) {
	if ciq.ctx.Unique == nil && ciq.path != nil {
		ciq.Unique(true)
	}
	ctx = setContextOp(ctx, ciq.ctx, ent.OpQueryIDs)
	if err = ciq.Select(costitem.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (ciq *CostItemQuery) IDsX(ctx context.Context) []int {
	ids, err := ciq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (ciq *CostItemQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, ciq.ctx, ent.OpQueryCount)
	if err := ciq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, ciq, querierCount[*CostItemQuery](), ciq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (ciq *CostItemQuery) CountX(ctx context.Context) int {
	count, err := ciq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (ciq *CostItemQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, ciq.ctx, ent.OpQueryExist)
	switch _, err := ciq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (ciq *CostItemQuery) ExistX(ctx context.Context) bool {
	exist, err := ciq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the CostItemQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (ciq *CostItemQuery) Clone() *CostItemQuery {
	if ciq == nil {
		return nil
	}
	return &CostItemQuery{
		config:               ciq.config,
		ctx:                  ciq.ctx.Clone(),
		order:                append([]costitem.OrderOption{}, ciq.order...),
		inters:               append([]Interceptor{}, ciq.inters...),
		predicates:           append([]predicate.CostItem{}, ciq.predicates...),
		withMaintenanceOrder: ciq.withMaintenanceOrder.Clone(),
		withUser:             ciq.withUser.Clone(),
		// clone intermediate query.
		sql:  ciq.sql.Clone(),
		path: ciq.path,
	}
}

// WithMaintenanceOrder tells the query-builder to eager-load the nodes that are connected to
// the "maintenance_order" edge. The optional arguments are used to configure the query builder of the edge.
func (ciq *CostItemQuery) WithMaintenanceOrder(opts ...func(*MaintenanceOrderQuery)) *CostItemQuery {
	query := (&MaintenanceOrderClient{config: ciq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	ciq.withMaintenanceOrder = query
	return ciq
}

// WithUser tells the query-builder to eager-load the nodes that are connected to
// the "user" edge. The optional arguments are used to configure the query builder of the edge.
func (ciq *CostItemQuery) WithUser(opts ...func(*UserQuery)) *CostItemQuery {
	query := (&UserClient{config: ciq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	ciq.withUser = query
	return ciq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.CostItem.Query().
//		GroupBy(costitem.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (ciq *CostItemQuery) GroupBy(field string, fields ...string) *CostItemGroupBy {
	ciq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &CostItemGroupBy{build: ciq}
	grbuild.flds = &ciq.ctx.Fields
	grbuild.label = costitem.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.CostItem.Query().
//		Select(costitem.FieldCreatedAt).
//		Scan(ctx, &v)
func (ciq *CostItemQuery) Select(fields ...string) *CostItemSelect {
	ciq.ctx.Fields = append(ciq.ctx.Fields, fields...)
	sbuild := &CostItemSelect{CostItemQuery: ciq}
	sbuild.label = costitem.Label
	sbuild.flds, sbuild.scan = &ciq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a CostItemSelect configured with the given aggregations.
func (ciq *CostItemQuery) Aggregate(fns ...AggregateFunc) *CostItemSelect {
	return ciq.Select().Aggregate(fns...)
}

func (ciq *CostItemQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range ciq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, ciq); err != nil {
				return err
			}
		}
	}
	for _, f := range ciq.ctx.Fields {
		if !costitem.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if ciq.path != nil {
		prev, err := ciq.path(ctx)
		if err != nil {
			return err
		}
		ciq.sql = prev
	}
	return nil
}

func (ciq *CostItemQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*CostItem, error) {
	var (
		nodes       = []*CostItem{}
		_spec       = ciq.querySpec()
		loadedTypes = [2]bool{
			ciq.withMaintenanceOrder != nil,
			ciq.withUser != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*CostItem).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &CostItem{config: ciq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, ciq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := ciq.withMaintenanceOrder; query != nil {
		if err := ciq.loadMaintenanceOrder(ctx, query, nodes, nil,
			func(n *CostItem, e *MaintenanceOrder) { n.Edges.MaintenanceOrder = e }); err != nil {
			return nil, err
		}
	}
	if query := ciq.withUser; query != nil {
		if err := ciq.loadUser(ctx, query, nodes, nil,
			func(n *CostItem, e *User) { n.Edges.User = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (ciq *CostItemQuery) loadMaintenanceOrder(ctx context.Context, query *MaintenanceOrderQuery, nodes []*CostItem, init func(*CostItem), assign func(*CostItem, *MaintenanceOrder)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*CostItem)
	for i := range nodes {
		fk := nodes[i].MaintenanceOrderID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(maintenanceorder.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "maintenance_order_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (ciq *CostItemQuery) loadUser(ctx context.Context, query *UserQuery, nodes []*CostItem, init func(*CostItem), assign func(*CostItem, *User)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*CostItem)
	for i := range nodes {
		fk := nodes[i].UserID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(user.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "user_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (ciq *CostItemQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := ciq.querySpec()
	_spec.Node.Columns = ciq.ctx.Fields
	if len(ciq.ctx.Fields) > 0 {
		_spec.Unique = ciq.ctx.Unique != nil && *ciq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, ciq.driver, _spec)
}

func (ciq *CostItemQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(costitem.Table, costitem.Columns, sqlgraph.NewFieldSpec(costitem.FieldID, field.TypeInt))
	_spec.From = ciq.sql
	if unique := ciq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if ciq.path != nil {
		_spec.Unique = true
	}
	if fields := ciq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, costitem.FieldID)
		for i := range fields {
			if fields[i] != costitem.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if ciq.withMaintenanceOrder != nil {
			_spec.Node.AddColumnOnce(costitem.FieldMaintenanceOrderID)
		}
		if ciq.withUser != nil {
			_spec.Node.AddColumnOnce(costitem.FieldUserID)
		}
	}
	if ps := ciq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := ciq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := ciq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := ciq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (ciq *CostItemQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(ciq.driver.Dialect())
	t1 := builder.Table(costitem.Table)
	columns := ciq.ctx.Fields
	if len(columns) == 0 {
		columns = costitem.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if ciq.sql != nil {
		selector = ciq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if ciq.ctx.Unique != nil && *ciq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range ciq.predicates {
		p(selector)
	}
	for _, p := range ciq.order {
		p(selector)
	}
	if offset := ciq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := ciq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// CostItemGroupBy is the group-by builder for CostItem entities.
type CostItemGroupBy struct {
	selector
	build *CostItemQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (cigb *CostItemGroupBy) Aggregate(fns ...AggregateFunc) *CostItemGroupBy {
	cigb.fns = append(cigb.fns, fns...)
	return cigb
}

// Scan applies the selector query and scans the result into the given value.
func (cigb *CostItemGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, cigb.build.ctx, ent.OpQueryGroupBy)
	if err := cigb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*CostItemQuery, *CostItemGroupBy](ctx, cigb.build, cigb, cigb.build.inters, v)
}

func (cigb *CostItemGroupBy) sqlScan(ctx context.Context, root *CostItemQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(cigb.fns))
	for _, fn := range cigb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*cigb.flds)+len(cigb.fns))
		for _, f := range *cigb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*cigb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := cigb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// CostItemSelect is the builder for selecting fields of CostItem entities.
type CostItemSelect struct {
	*CostItemQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (cis *CostItemSelect) Aggregate(fns ...AggregateFunc) *CostItemSelect {
	cis.fns = append(cis.fns, fns...)
	return cis
}

// Scan applies the selector query and scans the result into the given value.
func (cis *CostItemSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, cis.ctx, ent.OpQuerySelect)
	if err := cis.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*CostItemQuery, *CostItemSelect](ctx, cis.CostItemQuery, cis, cis.inters, v)
}

func (cis *CostItemSelect) sqlScan(ctx context.Context, root *CostItemQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(cis.fns))
	for _, fn := range cis.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*cis.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := cis.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
