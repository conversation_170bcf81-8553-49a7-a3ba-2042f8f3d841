// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"
	"tradicao/internal/ent/branch"
	"tradicao/internal/ent/equipment"
	"tradicao/internal/ent/maintenanceorder"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// EquipmentCreate is the builder for creating a Equipment entity.
type EquipmentCreate struct {
	config
	mutation *EquipmentMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (ec *EquipmentCreate) SetCreatedAt(t time.Time) *EquipmentCreate {
	ec.mutation.SetCreatedAt(t)
	return ec
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (ec *EquipmentCreate) SetNillableCreatedAt(t *time.Time) *EquipmentCreate {
	if t != nil {
		ec.SetCreatedAt(*t)
	}
	return ec
}

// SetUpdatedAt sets the "updated_at" field.
func (ec *EquipmentCreate) SetUpdatedAt(t time.Time) *EquipmentCreate {
	ec.mutation.SetUpdatedAt(t)
	return ec
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (ec *EquipmentCreate) SetNillableUpdatedAt(t *time.Time) *EquipmentCreate {
	if t != nil {
		ec.SetUpdatedAt(*t)
	}
	return ec
}

// SetActive sets the "active" field.
func (ec *EquipmentCreate) SetActive(b bool) *EquipmentCreate {
	ec.mutation.SetActive(b)
	return ec
}

// SetNillableActive sets the "active" field if the given value is not nil.
func (ec *EquipmentCreate) SetNillableActive(b *bool) *EquipmentCreate {
	if b != nil {
		ec.SetActive(*b)
	}
	return ec
}

// SetName sets the "name" field.
func (ec *EquipmentCreate) SetName(s string) *EquipmentCreate {
	ec.mutation.SetName(s)
	return ec
}

// SetType sets the "type" field.
func (ec *EquipmentCreate) SetType(s string) *EquipmentCreate {
	ec.mutation.SetType(s)
	return ec
}

// SetModel sets the "model" field.
func (ec *EquipmentCreate) SetModel(s string) *EquipmentCreate {
	ec.mutation.SetModel(s)
	return ec
}

// SetSerialNumber sets the "serial_number" field.
func (ec *EquipmentCreate) SetSerialNumber(s string) *EquipmentCreate {
	ec.mutation.SetSerialNumber(s)
	return ec
}

// SetInstallationDate sets the "installation_date" field.
func (ec *EquipmentCreate) SetInstallationDate(t time.Time) *EquipmentCreate {
	ec.mutation.SetInstallationDate(t)
	return ec
}

// SetNillableInstallationDate sets the "installation_date" field if the given value is not nil.
func (ec *EquipmentCreate) SetNillableInstallationDate(t *time.Time) *EquipmentCreate {
	if t != nil {
		ec.SetInstallationDate(*t)
	}
	return ec
}

// SetLastMaintenanceDate sets the "last_maintenance_date" field.
func (ec *EquipmentCreate) SetLastMaintenanceDate(t time.Time) *EquipmentCreate {
	ec.mutation.SetLastMaintenanceDate(t)
	return ec
}

// SetNillableLastMaintenanceDate sets the "last_maintenance_date" field if the given value is not nil.
func (ec *EquipmentCreate) SetNillableLastMaintenanceDate(t *time.Time) *EquipmentCreate {
	if t != nil {
		ec.SetLastMaintenanceDate(*t)
	}
	return ec
}

// SetBranchID sets the "branch_id" field.
func (ec *EquipmentCreate) SetBranchID(i int) *EquipmentCreate {
	ec.mutation.SetBranchID(i)
	return ec
}

// SetNillableBranchID sets the "branch_id" field if the given value is not nil.
func (ec *EquipmentCreate) SetNillableBranchID(i *int) *EquipmentCreate {
	if i != nil {
		ec.SetBranchID(*i)
	}
	return ec
}

// SetBranch sets the "branch" edge to the Branch entity.
func (ec *EquipmentCreate) SetBranch(b *Branch) *EquipmentCreate {
	return ec.SetBranchID(b.ID)
}

// AddMaintenanceOrderIDs adds the "maintenance_orders" edge to the MaintenanceOrder entity by IDs.
func (ec *EquipmentCreate) AddMaintenanceOrderIDs(ids ...int) *EquipmentCreate {
	ec.mutation.AddMaintenanceOrderIDs(ids...)
	return ec
}

// AddMaintenanceOrders adds the "maintenance_orders" edges to the MaintenanceOrder entity.
func (ec *EquipmentCreate) AddMaintenanceOrders(m ...*MaintenanceOrder) *EquipmentCreate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return ec.AddMaintenanceOrderIDs(ids...)
}

// Mutation returns the EquipmentMutation object of the builder.
func (ec *EquipmentCreate) Mutation() *EquipmentMutation {
	return ec.mutation
}

// Save creates the Equipment in the database.
func (ec *EquipmentCreate) Save(ctx context.Context) (*Equipment, error) {
	ec.defaults()
	return withHooks(ctx, ec.sqlSave, ec.mutation, ec.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (ec *EquipmentCreate) SaveX(ctx context.Context) *Equipment {
	v, err := ec.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ec *EquipmentCreate) Exec(ctx context.Context) error {
	_, err := ec.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ec *EquipmentCreate) ExecX(ctx context.Context) {
	if err := ec.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ec *EquipmentCreate) defaults() {
	if _, ok := ec.mutation.CreatedAt(); !ok {
		v := equipment.DefaultCreatedAt()
		ec.mutation.SetCreatedAt(v)
	}
	if _, ok := ec.mutation.UpdatedAt(); !ok {
		v := equipment.DefaultUpdatedAt()
		ec.mutation.SetUpdatedAt(v)
	}
	if _, ok := ec.mutation.Active(); !ok {
		v := equipment.DefaultActive
		ec.mutation.SetActive(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ec *EquipmentCreate) check() error {
	if _, ok := ec.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Equipment.created_at"`)}
	}
	if _, ok := ec.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Equipment.updated_at"`)}
	}
	if _, ok := ec.mutation.Active(); !ok {
		return &ValidationError{Name: "active", err: errors.New(`ent: missing required field "Equipment.active"`)}
	}
	if _, ok := ec.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Equipment.name"`)}
	}
	if v, ok := ec.mutation.Name(); ok {
		if err := equipment.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Equipment.name": %w`, err)}
		}
	}
	if _, ok := ec.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "Equipment.type"`)}
	}
	if v, ok := ec.mutation.GetType(); ok {
		if err := equipment.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Equipment.type": %w`, err)}
		}
	}
	if _, ok := ec.mutation.Model(); !ok {
		return &ValidationError{Name: "model", err: errors.New(`ent: missing required field "Equipment.model"`)}
	}
	if v, ok := ec.mutation.Model(); ok {
		if err := equipment.ModelValidator(v); err != nil {
			return &ValidationError{Name: "model", err: fmt.Errorf(`ent: validator failed for field "Equipment.model": %w`, err)}
		}
	}
	if _, ok := ec.mutation.SerialNumber(); !ok {
		return &ValidationError{Name: "serial_number", err: errors.New(`ent: missing required field "Equipment.serial_number"`)}
	}
	if v, ok := ec.mutation.SerialNumber(); ok {
		if err := equipment.SerialNumberValidator(v); err != nil {
			return &ValidationError{Name: "serial_number", err: fmt.Errorf(`ent: validator failed for field "Equipment.serial_number": %w`, err)}
		}
	}
	return nil
}

func (ec *EquipmentCreate) sqlSave(ctx context.Context) (*Equipment, error) {
	if err := ec.check(); err != nil {
		return nil, err
	}
	_node, _spec := ec.createSpec()
	if err := sqlgraph.CreateNode(ctx, ec.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	ec.mutation.id = &_node.ID
	ec.mutation.done = true
	return _node, nil
}

func (ec *EquipmentCreate) createSpec() (*Equipment, *sqlgraph.CreateSpec) {
	var (
		_node = &Equipment{config: ec.config}
		_spec = sqlgraph.NewCreateSpec(equipment.Table, sqlgraph.NewFieldSpec(equipment.FieldID, field.TypeInt))
	)
	if value, ok := ec.mutation.CreatedAt(); ok {
		_spec.SetField(equipment.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := ec.mutation.UpdatedAt(); ok {
		_spec.SetField(equipment.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := ec.mutation.Active(); ok {
		_spec.SetField(equipment.FieldActive, field.TypeBool, value)
		_node.Active = value
	}
	if value, ok := ec.mutation.Name(); ok {
		_spec.SetField(equipment.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := ec.mutation.GetType(); ok {
		_spec.SetField(equipment.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if value, ok := ec.mutation.Model(); ok {
		_spec.SetField(equipment.FieldModel, field.TypeString, value)
		_node.Model = value
	}
	if value, ok := ec.mutation.SerialNumber(); ok {
		_spec.SetField(equipment.FieldSerialNumber, field.TypeString, value)
		_node.SerialNumber = value
	}
	if value, ok := ec.mutation.InstallationDate(); ok {
		_spec.SetField(equipment.FieldInstallationDate, field.TypeTime, value)
		_node.InstallationDate = value
	}
	if value, ok := ec.mutation.LastMaintenanceDate(); ok {
		_spec.SetField(equipment.FieldLastMaintenanceDate, field.TypeTime, value)
		_node.LastMaintenanceDate = value
	}
	if nodes := ec.mutation.BranchIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   equipment.BranchTable,
			Columns: []string{equipment.BranchColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(branch.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.BranchID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := ec.mutation.MaintenanceOrdersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   equipment.MaintenanceOrdersTable,
			Columns: []string{equipment.MaintenanceOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// EquipmentCreateBulk is the builder for creating many Equipment entities in bulk.
type EquipmentCreateBulk struct {
	config
	err      error
	builders []*EquipmentCreate
}

// Save creates the Equipment entities in the database.
func (ecb *EquipmentCreateBulk) Save(ctx context.Context) ([]*Equipment, error) {
	if ecb.err != nil {
		return nil, ecb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ecb.builders))
	nodes := make([]*Equipment, len(ecb.builders))
	mutators := make([]Mutator, len(ecb.builders))
	for i := range ecb.builders {
		func(i int, root context.Context) {
			builder := ecb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*EquipmentMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ecb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ecb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ecb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ecb *EquipmentCreateBulk) SaveX(ctx context.Context) []*Equipment {
	v, err := ecb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ecb *EquipmentCreateBulk) Exec(ctx context.Context) error {
	_, err := ecb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ecb *EquipmentCreateBulk) ExecX(ctx context.Context) {
	if err := ecb.Exec(ctx); err != nil {
		panic(err)
	}
}
