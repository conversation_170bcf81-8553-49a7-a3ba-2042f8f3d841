// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"tradicao/internal/ent/interaction"
	"tradicao/internal/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// InteractionDelete is the builder for deleting a Interaction entity.
type InteractionDelete struct {
	config
	hooks    []Hook
	mutation *InteractionMutation
}

// Where appends a list predicates to the InteractionDelete builder.
func (id *InteractionDelete) Where(ps ...predicate.Interaction) *InteractionDelete {
	id.mutation.Where(ps...)
	return id
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (id *InteractionDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, id.sqlExec, id.mutation, id.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (id *InteractionDelete) ExecX(ctx context.Context) int {
	n, err := id.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (id *InteractionDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(interaction.Table, sqlgraph.NewFieldSpec(interaction.FieldID, field.TypeInt))
	if ps := id.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, id.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	id.mutation.done = true
	return affected, err
}

// InteractionDeleteOne is the builder for deleting a single Interaction entity.
type InteractionDeleteOne struct {
	id *InteractionDelete
}

// Where appends a list predicates to the InteractionDelete builder.
func (ido *InteractionDeleteOne) Where(ps ...predicate.Interaction) *InteractionDeleteOne {
	ido.id.mutation.Where(ps...)
	return ido
}

// Exec executes the deletion query.
func (ido *InteractionDeleteOne) Exec(ctx context.Context) error {
	n, err := ido.id.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{interaction.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (ido *InteractionDeleteOne) ExecX(ctx context.Context) {
	if err := ido.Exec(ctx); err != nil {
		panic(err)
	}
}
