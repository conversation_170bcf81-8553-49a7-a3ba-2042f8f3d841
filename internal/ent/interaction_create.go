// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"
	"tradicao/internal/ent/interaction"
	"tradicao/internal/ent/maintenanceorder"
	"tradicao/internal/ent/user"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// InteractionCreate is the builder for creating a Interaction entity.
type InteractionCreate struct {
	config
	mutation *InteractionMutation
	hooks    []Hook
}

// SetCreateTime sets the "create_time" field.
func (ic *InteractionCreate) SetCreateTime(t time.Time) *InteractionCreate {
	ic.mutation.SetCreateTime(t)
	return ic
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (ic *InteractionCreate) SetNillableCreateTime(t *time.Time) *InteractionCreate {
	if t != nil {
		ic.SetCreateTime(*t)
	}
	return ic
}

// SetUpdateTime sets the "update_time" field.
func (ic *InteractionCreate) SetUpdateTime(t time.Time) *InteractionCreate {
	ic.mutation.SetUpdateTime(t)
	return ic
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (ic *InteractionCreate) SetNillableUpdateTime(t *time.Time) *InteractionCreate {
	if t != nil {
		ic.SetUpdateTime(*t)
	}
	return ic
}

// SetMessage sets the "message" field.
func (ic *InteractionCreate) SetMessage(s string) *InteractionCreate {
	ic.mutation.SetMessage(s)
	return ic
}

// SetType sets the "type" field.
func (ic *InteractionCreate) SetType(s string) *InteractionCreate {
	ic.mutation.SetType(s)
	return ic
}

// SetNillableType sets the "type" field if the given value is not nil.
func (ic *InteractionCreate) SetNillableType(s *string) *InteractionCreate {
	if s != nil {
		ic.SetType(*s)
	}
	return ic
}

// SetMaintenanceOrderID sets the "maintenance_order" edge to the MaintenanceOrder entity by ID.
func (ic *InteractionCreate) SetMaintenanceOrderID(id int) *InteractionCreate {
	ic.mutation.SetMaintenanceOrderID(id)
	return ic
}

// SetMaintenanceOrder sets the "maintenance_order" edge to the MaintenanceOrder entity.
func (ic *InteractionCreate) SetMaintenanceOrder(m *MaintenanceOrder) *InteractionCreate {
	return ic.SetMaintenanceOrderID(m.ID)
}

// SetUserID sets the "user" edge to the User entity by ID.
func (ic *InteractionCreate) SetUserID(id int) *InteractionCreate {
	ic.mutation.SetUserID(id)
	return ic
}

// SetUser sets the "user" edge to the User entity.
func (ic *InteractionCreate) SetUser(u *User) *InteractionCreate {
	return ic.SetUserID(u.ID)
}

// Mutation returns the InteractionMutation object of the builder.
func (ic *InteractionCreate) Mutation() *InteractionMutation {
	return ic.mutation
}

// Save creates the Interaction in the database.
func (ic *InteractionCreate) Save(ctx context.Context) (*Interaction, error) {
	ic.defaults()
	return withHooks(ctx, ic.sqlSave, ic.mutation, ic.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (ic *InteractionCreate) SaveX(ctx context.Context) *Interaction {
	v, err := ic.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ic *InteractionCreate) Exec(ctx context.Context) error {
	_, err := ic.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ic *InteractionCreate) ExecX(ctx context.Context) {
	if err := ic.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ic *InteractionCreate) defaults() {
	if _, ok := ic.mutation.CreateTime(); !ok {
		v := interaction.DefaultCreateTime()
		ic.mutation.SetCreateTime(v)
	}
	if _, ok := ic.mutation.UpdateTime(); !ok {
		v := interaction.DefaultUpdateTime()
		ic.mutation.SetUpdateTime(v)
	}
	if _, ok := ic.mutation.GetType(); !ok {
		v := interaction.DefaultType
		ic.mutation.SetType(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ic *InteractionCreate) check() error {
	if _, ok := ic.mutation.CreateTime(); !ok {
		return &ValidationError{Name: "create_time", err: errors.New(`ent: missing required field "Interaction.create_time"`)}
	}
	if _, ok := ic.mutation.UpdateTime(); !ok {
		return &ValidationError{Name: "update_time", err: errors.New(`ent: missing required field "Interaction.update_time"`)}
	}
	if _, ok := ic.mutation.Message(); !ok {
		return &ValidationError{Name: "message", err: errors.New(`ent: missing required field "Interaction.message"`)}
	}
	if v, ok := ic.mutation.Message(); ok {
		if err := interaction.MessageValidator(v); err != nil {
			return &ValidationError{Name: "message", err: fmt.Errorf(`ent: validator failed for field "Interaction.message": %w`, err)}
		}
	}
	if _, ok := ic.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "Interaction.type"`)}
	}
	if v, ok := ic.mutation.GetType(); ok {
		if err := interaction.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Interaction.type": %w`, err)}
		}
	}
	if len(ic.mutation.MaintenanceOrderIDs()) == 0 {
		return &ValidationError{Name: "maintenance_order", err: errors.New(`ent: missing required edge "Interaction.maintenance_order"`)}
	}
	if len(ic.mutation.UserIDs()) == 0 {
		return &ValidationError{Name: "user", err: errors.New(`ent: missing required edge "Interaction.user"`)}
	}
	return nil
}

func (ic *InteractionCreate) sqlSave(ctx context.Context) (*Interaction, error) {
	if err := ic.check(); err != nil {
		return nil, err
	}
	_node, _spec := ic.createSpec()
	if err := sqlgraph.CreateNode(ctx, ic.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	ic.mutation.id = &_node.ID
	ic.mutation.done = true
	return _node, nil
}

func (ic *InteractionCreate) createSpec() (*Interaction, *sqlgraph.CreateSpec) {
	var (
		_node = &Interaction{config: ic.config}
		_spec = sqlgraph.NewCreateSpec(interaction.Table, sqlgraph.NewFieldSpec(interaction.FieldID, field.TypeInt))
	)
	if value, ok := ic.mutation.CreateTime(); ok {
		_spec.SetField(interaction.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := ic.mutation.UpdateTime(); ok {
		_spec.SetField(interaction.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := ic.mutation.Message(); ok {
		_spec.SetField(interaction.FieldMessage, field.TypeString, value)
		_node.Message = value
	}
	if value, ok := ic.mutation.GetType(); ok {
		_spec.SetField(interaction.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if nodes := ic.mutation.MaintenanceOrderIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   interaction.MaintenanceOrderTable,
			Columns: []string{interaction.MaintenanceOrderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.maintenance_order_interactions = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := ic.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   interaction.UserTable,
			Columns: []string{interaction.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.user_interactions = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// InteractionCreateBulk is the builder for creating many Interaction entities in bulk.
type InteractionCreateBulk struct {
	config
	err      error
	builders []*InteractionCreate
}

// Save creates the Interaction entities in the database.
func (icb *InteractionCreateBulk) Save(ctx context.Context) ([]*Interaction, error) {
	if icb.err != nil {
		return nil, icb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(icb.builders))
	nodes := make([]*Interaction, len(icb.builders))
	mutators := make([]Mutator, len(icb.builders))
	for i := range icb.builders {
		func(i int, root context.Context) {
			builder := icb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*InteractionMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, icb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, icb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, icb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (icb *InteractionCreateBulk) SaveX(ctx context.Context) []*Interaction {
	v, err := icb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (icb *InteractionCreateBulk) Exec(ctx context.Context) error {
	_, err := icb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (icb *InteractionCreateBulk) ExecX(ctx context.Context) {
	if err := icb.Exec(ctx); err != nil {
		panic(err)
	}
}
