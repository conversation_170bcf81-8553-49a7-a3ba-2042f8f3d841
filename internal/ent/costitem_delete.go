// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"tradicao/internal/ent/costitem"
	"tradicao/internal/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CostItemDelete is the builder for deleting a CostItem entity.
type CostItemDelete struct {
	config
	hooks    []Hook
	mutation *CostItemMutation
}

// Where appends a list predicates to the CostItemDelete builder.
func (cid *CostItemDelete) Where(ps ...predicate.CostItem) *CostItemDelete {
	cid.mutation.Where(ps...)
	return cid
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (cid *CostItemDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, cid.sqlExec, cid.mutation, cid.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (cid *CostItemDelete) ExecX(ctx context.Context) int {
	n, err := cid.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (cid *CostItemDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(costitem.Table, sqlgraph.NewFieldSpec(costitem.FieldID, field.TypeInt))
	if ps := cid.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, cid.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	cid.mutation.done = true
	return affected, err
}

// CostItemDeleteOne is the builder for deleting a single CostItem entity.
type CostItemDeleteOne struct {
	cid *CostItemDelete
}

// Where appends a list predicates to the CostItemDelete builder.
func (cido *CostItemDeleteOne) Where(ps ...predicate.CostItem) *CostItemDeleteOne {
	cido.cid.mutation.Where(ps...)
	return cido
}

// Exec executes the deletion query.
func (cido *CostItemDeleteOne) Exec(ctx context.Context) error {
	n, err := cido.cid.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{costitem.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (cido *CostItemDeleteOne) ExecX(ctx context.Context) {
	if err := cido.Exec(ctx); err != nil {
		panic(err)
	}
}
