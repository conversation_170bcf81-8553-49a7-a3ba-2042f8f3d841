// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"
	"tradicao/internal/ent/costitem"
	"tradicao/internal/ent/maintenanceorder"
	"tradicao/internal/ent/user"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// CostItem is the model entity for the CostItem schema.
type CostItem struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// Active holds the value of the "active" field.
	Active bool `json:"active,omitempty"`
	// MaintenanceOrderID holds the value of the "maintenance_order_id" field.
	MaintenanceOrderID int `json:"maintenance_order_id,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID int `json:"user_id,omitempty"`
	// Description holds the value of the "description" field.
	Description string `json:"description,omitempty"`
	// Quantity holds the value of the "quantity" field.
	Quantity float64 `json:"quantity,omitempty"`
	// UnitPrice holds the value of the "unit_price" field.
	UnitPrice float64 `json:"unit_price,omitempty"`
	// TotalPrice holds the value of the "total_price" field.
	TotalPrice float64 `json:"total_price,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the CostItemQuery when eager-loading is set.
	Edges        CostItemEdges `json:"edges"`
	selectValues sql.SelectValues
}

// CostItemEdges holds the relations/edges for other nodes in the graph.
type CostItemEdges struct {
	// MaintenanceOrder holds the value of the maintenance_order edge.
	MaintenanceOrder *MaintenanceOrder `json:"maintenance_order,omitempty"`
	// User holds the value of the user edge.
	User *User `json:"user,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// MaintenanceOrderOrErr returns the MaintenanceOrder value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e CostItemEdges) MaintenanceOrderOrErr() (*MaintenanceOrder, error) {
	if e.MaintenanceOrder != nil {
		return e.MaintenanceOrder, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: maintenanceorder.Label}
	}
	return nil, &NotLoadedError{edge: "maintenance_order"}
}

// UserOrErr returns the User value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e CostItemEdges) UserOrErr() (*User, error) {
	if e.User != nil {
		return e.User, nil
	} else if e.loadedTypes[1] {
		return nil, &NotFoundError{label: user.Label}
	}
	return nil, &NotLoadedError{edge: "user"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*CostItem) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case costitem.FieldActive:
			values[i] = new(sql.NullBool)
		case costitem.FieldQuantity, costitem.FieldUnitPrice, costitem.FieldTotalPrice:
			values[i] = new(sql.NullFloat64)
		case costitem.FieldID, costitem.FieldMaintenanceOrderID, costitem.FieldUserID:
			values[i] = new(sql.NullInt64)
		case costitem.FieldDescription:
			values[i] = new(sql.NullString)
		case costitem.FieldCreatedAt, costitem.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the CostItem fields.
func (ci *CostItem) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case costitem.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			ci.ID = int(value.Int64)
		case costitem.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				ci.CreatedAt = value.Time
			}
		case costitem.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				ci.UpdatedAt = value.Time
			}
		case costitem.FieldActive:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field active", values[i])
			} else if value.Valid {
				ci.Active = value.Bool
			}
		case costitem.FieldMaintenanceOrderID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field maintenance_order_id", values[i])
			} else if value.Valid {
				ci.MaintenanceOrderID = int(value.Int64)
			}
		case costitem.FieldUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				ci.UserID = int(value.Int64)
			}
		case costitem.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				ci.Description = value.String
			}
		case costitem.FieldQuantity:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field quantity", values[i])
			} else if value.Valid {
				ci.Quantity = value.Float64
			}
		case costitem.FieldUnitPrice:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field unit_price", values[i])
			} else if value.Valid {
				ci.UnitPrice = value.Float64
			}
		case costitem.FieldTotalPrice:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field total_price", values[i])
			} else if value.Valid {
				ci.TotalPrice = value.Float64
			}
		default:
			ci.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the CostItem.
// This includes values selected through modifiers, order, etc.
func (ci *CostItem) Value(name string) (ent.Value, error) {
	return ci.selectValues.Get(name)
}

// QueryMaintenanceOrder queries the "maintenance_order" edge of the CostItem entity.
func (ci *CostItem) QueryMaintenanceOrder() *MaintenanceOrderQuery {
	return NewCostItemClient(ci.config).QueryMaintenanceOrder(ci)
}

// QueryUser queries the "user" edge of the CostItem entity.
func (ci *CostItem) QueryUser() *UserQuery {
	return NewCostItemClient(ci.config).QueryUser(ci)
}

// Update returns a builder for updating this CostItem.
// Note that you need to call CostItem.Unwrap() before calling this method if this CostItem
// was returned from a transaction, and the transaction was committed or rolled back.
func (ci *CostItem) Update() *CostItemUpdateOne {
	return NewCostItemClient(ci.config).UpdateOne(ci)
}

// Unwrap unwraps the CostItem entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (ci *CostItem) Unwrap() *CostItem {
	_tx, ok := ci.config.driver.(*txDriver)
	if !ok {
		panic("ent: CostItem is not a transactional entity")
	}
	ci.config.driver = _tx.drv
	return ci
}

// String implements the fmt.Stringer.
func (ci *CostItem) String() string {
	var builder strings.Builder
	builder.WriteString("CostItem(")
	builder.WriteString(fmt.Sprintf("id=%v, ", ci.ID))
	builder.WriteString("created_at=")
	builder.WriteString(ci.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(ci.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("active=")
	builder.WriteString(fmt.Sprintf("%v", ci.Active))
	builder.WriteString(", ")
	builder.WriteString("maintenance_order_id=")
	builder.WriteString(fmt.Sprintf("%v", ci.MaintenanceOrderID))
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", ci.UserID))
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(ci.Description)
	builder.WriteString(", ")
	builder.WriteString("quantity=")
	builder.WriteString(fmt.Sprintf("%v", ci.Quantity))
	builder.WriteString(", ")
	builder.WriteString("unit_price=")
	builder.WriteString(fmt.Sprintf("%v", ci.UnitPrice))
	builder.WriteString(", ")
	builder.WriteString("total_price=")
	builder.WriteString(fmt.Sprintf("%v", ci.TotalPrice))
	builder.WriteByte(')')
	return builder.String()
}

// CostItems is a parsable slice of CostItem.
type CostItems []*CostItem
