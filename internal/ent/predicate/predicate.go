// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// Branch is the predicate function for branch builders.
type Branch func(*sql.Selector)

// CostItem is the predicate function for costitem builders.
type CostItem func(*sql.Selector)

// Equipment is the predicate function for equipment builders.
type Equipment func(*sql.Selector)

// Interaction is the predicate function for interaction builders.
type Interaction func(*sql.Selector)

// MaintenanceOrder is the predicate function for maintenanceorder builders.
type MaintenanceOrder func(*sql.Selector)

// User is the predicate function for user builders.
type User func(*sql.Selector)
