// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"tradicao/internal/ent/maintenanceorder"
	"tradicao/internal/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MaintenanceOrderDelete is the builder for deleting a MaintenanceOrder entity.
type MaintenanceOrderDelete struct {
	config
	hooks    []Hook
	mutation *MaintenanceOrderMutation
}

// Where appends a list predicates to the MaintenanceOrderDelete builder.
func (mod *MaintenanceOrderDelete) Where(ps ...predicate.MaintenanceOrder) *MaintenanceOrderDelete {
	mod.mutation.Where(ps...)
	return mod
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (mod *MaintenanceOrderDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, mod.sqlExec, mod.mutation, mod.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (mod *MaintenanceOrderDelete) ExecX(ctx context.Context) int {
	n, err := mod.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (mod *MaintenanceOrderDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(maintenanceorder.Table, sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt))
	if ps := mod.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, mod.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	mod.mutation.done = true
	return affected, err
}

// MaintenanceOrderDeleteOne is the builder for deleting a single MaintenanceOrder entity.
type MaintenanceOrderDeleteOne struct {
	mod *MaintenanceOrderDelete
}

// Where appends a list predicates to the MaintenanceOrderDelete builder.
func (modo *MaintenanceOrderDeleteOne) Where(ps ...predicate.MaintenanceOrder) *MaintenanceOrderDeleteOne {
	modo.mod.mutation.Where(ps...)
	return modo
}

// Exec executes the deletion query.
func (modo *MaintenanceOrderDeleteOne) Exec(ctx context.Context) error {
	n, err := modo.mod.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{maintenanceorder.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (modo *MaintenanceOrderDeleteOne) ExecX(ctx context.Context) {
	if err := modo.Exec(ctx); err != nil {
		panic(err)
	}
}
