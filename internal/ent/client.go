// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"tradicao/internal/ent/migrate"

	"tradicao/internal/ent/branch"
	"tradicao/internal/ent/costitem"
	"tradicao/internal/ent/equipment"
	"tradicao/internal/ent/interaction"
	"tradicao/internal/ent/maintenanceorder"
	"tradicao/internal/ent/user"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// Branch is the client for interacting with the Branch builders.
	Branch *BranchClient
	// CostItem is the client for interacting with the CostItem builders.
	CostItem *CostItemClient
	// Equipment is the client for interacting with the Equipment builders.
	Equipment *EquipmentClient
	// Interaction is the client for interacting with the Interaction builders.
	Interaction *InteractionClient
	// MaintenanceOrder is the client for interacting with the MaintenanceOrder builders.
	MaintenanceOrder *MaintenanceOrderClient
	// User is the client for interacting with the User builders.
	User *UserClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.Branch = NewBranchClient(c.config)
	c.CostItem = NewCostItemClient(c.config)
	c.Equipment = NewEquipmentClient(c.config)
	c.Interaction = NewInteractionClient(c.config)
	c.MaintenanceOrder = NewMaintenanceOrderClient(c.config)
	c.User = NewUserClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:              ctx,
		config:           cfg,
		Branch:           NewBranchClient(cfg),
		CostItem:         NewCostItemClient(cfg),
		Equipment:        NewEquipmentClient(cfg),
		Interaction:      NewInteractionClient(cfg),
		MaintenanceOrder: NewMaintenanceOrderClient(cfg),
		User:             NewUserClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:              ctx,
		config:           cfg,
		Branch:           NewBranchClient(cfg),
		CostItem:         NewCostItemClient(cfg),
		Equipment:        NewEquipmentClient(cfg),
		Interaction:      NewInteractionClient(cfg),
		MaintenanceOrder: NewMaintenanceOrderClient(cfg),
		User:             NewUserClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		Branch.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	for _, n := range []interface{ Use(...Hook) }{
		c.Branch, c.CostItem, c.Equipment, c.Interaction, c.MaintenanceOrder, c.User,
	} {
		n.Use(hooks...)
	}
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	for _, n := range []interface{ Intercept(...Interceptor) }{
		c.Branch, c.CostItem, c.Equipment, c.Interaction, c.MaintenanceOrder, c.User,
	} {
		n.Intercept(interceptors...)
	}
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *BranchMutation:
		return c.Branch.mutate(ctx, m)
	case *CostItemMutation:
		return c.CostItem.mutate(ctx, m)
	case *EquipmentMutation:
		return c.Equipment.mutate(ctx, m)
	case *InteractionMutation:
		return c.Interaction.mutate(ctx, m)
	case *MaintenanceOrderMutation:
		return c.MaintenanceOrder.mutate(ctx, m)
	case *UserMutation:
		return c.User.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// BranchClient is a client for the Branch schema.
type BranchClient struct {
	config
}

// NewBranchClient returns a client for the Branch from the given config.
func NewBranchClient(c config) *BranchClient {
	return &BranchClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `branch.Hooks(f(g(h())))`.
func (c *BranchClient) Use(hooks ...Hook) {
	c.hooks.Branch = append(c.hooks.Branch, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `branch.Intercept(f(g(h())))`.
func (c *BranchClient) Intercept(interceptors ...Interceptor) {
	c.inters.Branch = append(c.inters.Branch, interceptors...)
}

// Create returns a builder for creating a Branch entity.
func (c *BranchClient) Create() *BranchCreate {
	mutation := newBranchMutation(c.config, OpCreate)
	return &BranchCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Branch entities.
func (c *BranchClient) CreateBulk(builders ...*BranchCreate) *BranchCreateBulk {
	return &BranchCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *BranchClient) MapCreateBulk(slice any, setFunc func(*BranchCreate, int)) *BranchCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &BranchCreateBulk{err: fmt.Errorf("calling to BranchClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*BranchCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &BranchCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Branch.
func (c *BranchClient) Update() *BranchUpdate {
	mutation := newBranchMutation(c.config, OpUpdate)
	return &BranchUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *BranchClient) UpdateOne(b *Branch) *BranchUpdateOne {
	mutation := newBranchMutation(c.config, OpUpdateOne, withBranch(b))
	return &BranchUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *BranchClient) UpdateOneID(id int) *BranchUpdateOne {
	mutation := newBranchMutation(c.config, OpUpdateOne, withBranchID(id))
	return &BranchUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Branch.
func (c *BranchClient) Delete() *BranchDelete {
	mutation := newBranchMutation(c.config, OpDelete)
	return &BranchDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *BranchClient) DeleteOne(b *Branch) *BranchDeleteOne {
	return c.DeleteOneID(b.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *BranchClient) DeleteOneID(id int) *BranchDeleteOne {
	builder := c.Delete().Where(branch.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &BranchDeleteOne{builder}
}

// Query returns a query builder for Branch.
func (c *BranchClient) Query() *BranchQuery {
	return &BranchQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeBranch},
		inters: c.Interceptors(),
	}
}

// Get returns a Branch entity by its id.
func (c *BranchClient) Get(ctx context.Context, id int) (*Branch, error) {
	return c.Query().Where(branch.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *BranchClient) GetX(ctx context.Context, id int) *Branch {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryEquipment queries the equipment edge of a Branch.
func (c *BranchClient) QueryEquipment(b *Branch) *EquipmentQuery {
	query := (&EquipmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := b.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(branch.Table, branch.FieldID, id),
			sqlgraph.To(equipment.Table, equipment.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, branch.EquipmentTable, branch.EquipmentColumn),
		)
		fromV = sqlgraph.Neighbors(b.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryMaintenanceOrders queries the maintenance_orders edge of a Branch.
func (c *BranchClient) QueryMaintenanceOrders(b *Branch) *MaintenanceOrderQuery {
	query := (&MaintenanceOrderClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := b.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(branch.Table, branch.FieldID, id),
			sqlgraph.To(maintenanceorder.Table, maintenanceorder.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, branch.MaintenanceOrdersTable, branch.MaintenanceOrdersColumn),
		)
		fromV = sqlgraph.Neighbors(b.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *BranchClient) Hooks() []Hook {
	return c.hooks.Branch
}

// Interceptors returns the client interceptors.
func (c *BranchClient) Interceptors() []Interceptor {
	return c.inters.Branch
}

func (c *BranchClient) mutate(ctx context.Context, m *BranchMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&BranchCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&BranchUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&BranchUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&BranchDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Branch mutation op: %q", m.Op())
	}
}

// CostItemClient is a client for the CostItem schema.
type CostItemClient struct {
	config
}

// NewCostItemClient returns a client for the CostItem from the given config.
func NewCostItemClient(c config) *CostItemClient {
	return &CostItemClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `costitem.Hooks(f(g(h())))`.
func (c *CostItemClient) Use(hooks ...Hook) {
	c.hooks.CostItem = append(c.hooks.CostItem, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `costitem.Intercept(f(g(h())))`.
func (c *CostItemClient) Intercept(interceptors ...Interceptor) {
	c.inters.CostItem = append(c.inters.CostItem, interceptors...)
}

// Create returns a builder for creating a CostItem entity.
func (c *CostItemClient) Create() *CostItemCreate {
	mutation := newCostItemMutation(c.config, OpCreate)
	return &CostItemCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of CostItem entities.
func (c *CostItemClient) CreateBulk(builders ...*CostItemCreate) *CostItemCreateBulk {
	return &CostItemCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *CostItemClient) MapCreateBulk(slice any, setFunc func(*CostItemCreate, int)) *CostItemCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &CostItemCreateBulk{err: fmt.Errorf("calling to CostItemClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*CostItemCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &CostItemCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for CostItem.
func (c *CostItemClient) Update() *CostItemUpdate {
	mutation := newCostItemMutation(c.config, OpUpdate)
	return &CostItemUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *CostItemClient) UpdateOne(ci *CostItem) *CostItemUpdateOne {
	mutation := newCostItemMutation(c.config, OpUpdateOne, withCostItem(ci))
	return &CostItemUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *CostItemClient) UpdateOneID(id int) *CostItemUpdateOne {
	mutation := newCostItemMutation(c.config, OpUpdateOne, withCostItemID(id))
	return &CostItemUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for CostItem.
func (c *CostItemClient) Delete() *CostItemDelete {
	mutation := newCostItemMutation(c.config, OpDelete)
	return &CostItemDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *CostItemClient) DeleteOne(ci *CostItem) *CostItemDeleteOne {
	return c.DeleteOneID(ci.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *CostItemClient) DeleteOneID(id int) *CostItemDeleteOne {
	builder := c.Delete().Where(costitem.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &CostItemDeleteOne{builder}
}

// Query returns a query builder for CostItem.
func (c *CostItemClient) Query() *CostItemQuery {
	return &CostItemQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeCostItem},
		inters: c.Interceptors(),
	}
}

// Get returns a CostItem entity by its id.
func (c *CostItemClient) Get(ctx context.Context, id int) (*CostItem, error) {
	return c.Query().Where(costitem.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *CostItemClient) GetX(ctx context.Context, id int) *CostItem {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryMaintenanceOrder queries the maintenance_order edge of a CostItem.
func (c *CostItemClient) QueryMaintenanceOrder(ci *CostItem) *MaintenanceOrderQuery {
	query := (&MaintenanceOrderClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ci.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(costitem.Table, costitem.FieldID, id),
			sqlgraph.To(maintenanceorder.Table, maintenanceorder.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, costitem.MaintenanceOrderTable, costitem.MaintenanceOrderColumn),
		)
		fromV = sqlgraph.Neighbors(ci.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUser queries the user edge of a CostItem.
func (c *CostItemClient) QueryUser(ci *CostItem) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ci.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(costitem.Table, costitem.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, costitem.UserTable, costitem.UserColumn),
		)
		fromV = sqlgraph.Neighbors(ci.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *CostItemClient) Hooks() []Hook {
	return c.hooks.CostItem
}

// Interceptors returns the client interceptors.
func (c *CostItemClient) Interceptors() []Interceptor {
	return c.inters.CostItem
}

func (c *CostItemClient) mutate(ctx context.Context, m *CostItemMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&CostItemCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&CostItemUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&CostItemUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&CostItemDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown CostItem mutation op: %q", m.Op())
	}
}

// EquipmentClient is a client for the Equipment schema.
type EquipmentClient struct {
	config
}

// NewEquipmentClient returns a client for the Equipment from the given config.
func NewEquipmentClient(c config) *EquipmentClient {
	return &EquipmentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `equipment.Hooks(f(g(h())))`.
func (c *EquipmentClient) Use(hooks ...Hook) {
	c.hooks.Equipment = append(c.hooks.Equipment, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `equipment.Intercept(f(g(h())))`.
func (c *EquipmentClient) Intercept(interceptors ...Interceptor) {
	c.inters.Equipment = append(c.inters.Equipment, interceptors...)
}

// Create returns a builder for creating a Equipment entity.
func (c *EquipmentClient) Create() *EquipmentCreate {
	mutation := newEquipmentMutation(c.config, OpCreate)
	return &EquipmentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Equipment entities.
func (c *EquipmentClient) CreateBulk(builders ...*EquipmentCreate) *EquipmentCreateBulk {
	return &EquipmentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *EquipmentClient) MapCreateBulk(slice any, setFunc func(*EquipmentCreate, int)) *EquipmentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &EquipmentCreateBulk{err: fmt.Errorf("calling to EquipmentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*EquipmentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &EquipmentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Equipment.
func (c *EquipmentClient) Update() *EquipmentUpdate {
	mutation := newEquipmentMutation(c.config, OpUpdate)
	return &EquipmentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *EquipmentClient) UpdateOne(e *Equipment) *EquipmentUpdateOne {
	mutation := newEquipmentMutation(c.config, OpUpdateOne, withEquipment(e))
	return &EquipmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *EquipmentClient) UpdateOneID(id int) *EquipmentUpdateOne {
	mutation := newEquipmentMutation(c.config, OpUpdateOne, withEquipmentID(id))
	return &EquipmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Equipment.
func (c *EquipmentClient) Delete() *EquipmentDelete {
	mutation := newEquipmentMutation(c.config, OpDelete)
	return &EquipmentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *EquipmentClient) DeleteOne(e *Equipment) *EquipmentDeleteOne {
	return c.DeleteOneID(e.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *EquipmentClient) DeleteOneID(id int) *EquipmentDeleteOne {
	builder := c.Delete().Where(equipment.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &EquipmentDeleteOne{builder}
}

// Query returns a query builder for Equipment.
func (c *EquipmentClient) Query() *EquipmentQuery {
	return &EquipmentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeEquipment},
		inters: c.Interceptors(),
	}
}

// Get returns a Equipment entity by its id.
func (c *EquipmentClient) Get(ctx context.Context, id int) (*Equipment, error) {
	return c.Query().Where(equipment.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *EquipmentClient) GetX(ctx context.Context, id int) *Equipment {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryBranch queries the branch edge of a Equipment.
func (c *EquipmentClient) QueryBranch(e *Equipment) *BranchQuery {
	query := (&BranchClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := e.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(equipment.Table, equipment.FieldID, id),
			sqlgraph.To(branch.Table, branch.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, equipment.BranchTable, equipment.BranchColumn),
		)
		fromV = sqlgraph.Neighbors(e.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryMaintenanceOrders queries the maintenance_orders edge of a Equipment.
func (c *EquipmentClient) QueryMaintenanceOrders(e *Equipment) *MaintenanceOrderQuery {
	query := (&MaintenanceOrderClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := e.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(equipment.Table, equipment.FieldID, id),
			sqlgraph.To(maintenanceorder.Table, maintenanceorder.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, equipment.MaintenanceOrdersTable, equipment.MaintenanceOrdersColumn),
		)
		fromV = sqlgraph.Neighbors(e.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *EquipmentClient) Hooks() []Hook {
	return c.hooks.Equipment
}

// Interceptors returns the client interceptors.
func (c *EquipmentClient) Interceptors() []Interceptor {
	return c.inters.Equipment
}

func (c *EquipmentClient) mutate(ctx context.Context, m *EquipmentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&EquipmentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&EquipmentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&EquipmentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&EquipmentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Equipment mutation op: %q", m.Op())
	}
}

// InteractionClient is a client for the Interaction schema.
type InteractionClient struct {
	config
}

// NewInteractionClient returns a client for the Interaction from the given config.
func NewInteractionClient(c config) *InteractionClient {
	return &InteractionClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `interaction.Hooks(f(g(h())))`.
func (c *InteractionClient) Use(hooks ...Hook) {
	c.hooks.Interaction = append(c.hooks.Interaction, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `interaction.Intercept(f(g(h())))`.
func (c *InteractionClient) Intercept(interceptors ...Interceptor) {
	c.inters.Interaction = append(c.inters.Interaction, interceptors...)
}

// Create returns a builder for creating a Interaction entity.
func (c *InteractionClient) Create() *InteractionCreate {
	mutation := newInteractionMutation(c.config, OpCreate)
	return &InteractionCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Interaction entities.
func (c *InteractionClient) CreateBulk(builders ...*InteractionCreate) *InteractionCreateBulk {
	return &InteractionCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *InteractionClient) MapCreateBulk(slice any, setFunc func(*InteractionCreate, int)) *InteractionCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &InteractionCreateBulk{err: fmt.Errorf("calling to InteractionClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*InteractionCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &InteractionCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Interaction.
func (c *InteractionClient) Update() *InteractionUpdate {
	mutation := newInteractionMutation(c.config, OpUpdate)
	return &InteractionUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *InteractionClient) UpdateOne(i *Interaction) *InteractionUpdateOne {
	mutation := newInteractionMutation(c.config, OpUpdateOne, withInteraction(i))
	return &InteractionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *InteractionClient) UpdateOneID(id int) *InteractionUpdateOne {
	mutation := newInteractionMutation(c.config, OpUpdateOne, withInteractionID(id))
	return &InteractionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Interaction.
func (c *InteractionClient) Delete() *InteractionDelete {
	mutation := newInteractionMutation(c.config, OpDelete)
	return &InteractionDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *InteractionClient) DeleteOne(i *Interaction) *InteractionDeleteOne {
	return c.DeleteOneID(i.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *InteractionClient) DeleteOneID(id int) *InteractionDeleteOne {
	builder := c.Delete().Where(interaction.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &InteractionDeleteOne{builder}
}

// Query returns a query builder for Interaction.
func (c *InteractionClient) Query() *InteractionQuery {
	return &InteractionQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeInteraction},
		inters: c.Interceptors(),
	}
}

// Get returns a Interaction entity by its id.
func (c *InteractionClient) Get(ctx context.Context, id int) (*Interaction, error) {
	return c.Query().Where(interaction.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *InteractionClient) GetX(ctx context.Context, id int) *Interaction {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryMaintenanceOrder queries the maintenance_order edge of a Interaction.
func (c *InteractionClient) QueryMaintenanceOrder(i *Interaction) *MaintenanceOrderQuery {
	query := (&MaintenanceOrderClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := i.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(interaction.Table, interaction.FieldID, id),
			sqlgraph.To(maintenanceorder.Table, maintenanceorder.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, interaction.MaintenanceOrderTable, interaction.MaintenanceOrderColumn),
		)
		fromV = sqlgraph.Neighbors(i.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUser queries the user edge of a Interaction.
func (c *InteractionClient) QueryUser(i *Interaction) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := i.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(interaction.Table, interaction.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, interaction.UserTable, interaction.UserColumn),
		)
		fromV = sqlgraph.Neighbors(i.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *InteractionClient) Hooks() []Hook {
	return c.hooks.Interaction
}

// Interceptors returns the client interceptors.
func (c *InteractionClient) Interceptors() []Interceptor {
	return c.inters.Interaction
}

func (c *InteractionClient) mutate(ctx context.Context, m *InteractionMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&InteractionCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&InteractionUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&InteractionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&InteractionDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Interaction mutation op: %q", m.Op())
	}
}

// MaintenanceOrderClient is a client for the MaintenanceOrder schema.
type MaintenanceOrderClient struct {
	config
}

// NewMaintenanceOrderClient returns a client for the MaintenanceOrder from the given config.
func NewMaintenanceOrderClient(c config) *MaintenanceOrderClient {
	return &MaintenanceOrderClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `maintenanceorder.Hooks(f(g(h())))`.
func (c *MaintenanceOrderClient) Use(hooks ...Hook) {
	c.hooks.MaintenanceOrder = append(c.hooks.MaintenanceOrder, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `maintenanceorder.Intercept(f(g(h())))`.
func (c *MaintenanceOrderClient) Intercept(interceptors ...Interceptor) {
	c.inters.MaintenanceOrder = append(c.inters.MaintenanceOrder, interceptors...)
}

// Create returns a builder for creating a MaintenanceOrder entity.
func (c *MaintenanceOrderClient) Create() *MaintenanceOrderCreate {
	mutation := newMaintenanceOrderMutation(c.config, OpCreate)
	return &MaintenanceOrderCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of MaintenanceOrder entities.
func (c *MaintenanceOrderClient) CreateBulk(builders ...*MaintenanceOrderCreate) *MaintenanceOrderCreateBulk {
	return &MaintenanceOrderCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *MaintenanceOrderClient) MapCreateBulk(slice any, setFunc func(*MaintenanceOrderCreate, int)) *MaintenanceOrderCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &MaintenanceOrderCreateBulk{err: fmt.Errorf("calling to MaintenanceOrderClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*MaintenanceOrderCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &MaintenanceOrderCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for MaintenanceOrder.
func (c *MaintenanceOrderClient) Update() *MaintenanceOrderUpdate {
	mutation := newMaintenanceOrderMutation(c.config, OpUpdate)
	return &MaintenanceOrderUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *MaintenanceOrderClient) UpdateOne(mo *MaintenanceOrder) *MaintenanceOrderUpdateOne {
	mutation := newMaintenanceOrderMutation(c.config, OpUpdateOne, withMaintenanceOrder(mo))
	return &MaintenanceOrderUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *MaintenanceOrderClient) UpdateOneID(id int) *MaintenanceOrderUpdateOne {
	mutation := newMaintenanceOrderMutation(c.config, OpUpdateOne, withMaintenanceOrderID(id))
	return &MaintenanceOrderUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for MaintenanceOrder.
func (c *MaintenanceOrderClient) Delete() *MaintenanceOrderDelete {
	mutation := newMaintenanceOrderMutation(c.config, OpDelete)
	return &MaintenanceOrderDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *MaintenanceOrderClient) DeleteOne(mo *MaintenanceOrder) *MaintenanceOrderDeleteOne {
	return c.DeleteOneID(mo.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *MaintenanceOrderClient) DeleteOneID(id int) *MaintenanceOrderDeleteOne {
	builder := c.Delete().Where(maintenanceorder.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &MaintenanceOrderDeleteOne{builder}
}

// Query returns a query builder for MaintenanceOrder.
func (c *MaintenanceOrderClient) Query() *MaintenanceOrderQuery {
	return &MaintenanceOrderQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeMaintenanceOrder},
		inters: c.Interceptors(),
	}
}

// Get returns a MaintenanceOrder entity by its id.
func (c *MaintenanceOrderClient) Get(ctx context.Context, id int) (*MaintenanceOrder, error) {
	return c.Query().Where(maintenanceorder.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *MaintenanceOrderClient) GetX(ctx context.Context, id int) *MaintenanceOrder {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryBranch queries the branch edge of a MaintenanceOrder.
func (c *MaintenanceOrderClient) QueryBranch(mo *MaintenanceOrder) *BranchQuery {
	query := (&BranchClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := mo.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(maintenanceorder.Table, maintenanceorder.FieldID, id),
			sqlgraph.To(branch.Table, branch.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, maintenanceorder.BranchTable, maintenanceorder.BranchColumn),
		)
		fromV = sqlgraph.Neighbors(mo.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryEquipment queries the equipment edge of a MaintenanceOrder.
func (c *MaintenanceOrderClient) QueryEquipment(mo *MaintenanceOrder) *EquipmentQuery {
	query := (&EquipmentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := mo.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(maintenanceorder.Table, maintenanceorder.FieldID, id),
			sqlgraph.To(equipment.Table, equipment.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, maintenanceorder.EquipmentTable, maintenanceorder.EquipmentColumn),
		)
		fromV = sqlgraph.Neighbors(mo.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryRequester queries the requester edge of a MaintenanceOrder.
func (c *MaintenanceOrderClient) QueryRequester(mo *MaintenanceOrder) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := mo.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(maintenanceorder.Table, maintenanceorder.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, maintenanceorder.RequesterTable, maintenanceorder.RequesterColumn),
		)
		fromV = sqlgraph.Neighbors(mo.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryApprover queries the approver edge of a MaintenanceOrder.
func (c *MaintenanceOrderClient) QueryApprover(mo *MaintenanceOrder) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := mo.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(maintenanceorder.Table, maintenanceorder.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, maintenanceorder.ApproverTable, maintenanceorder.ApproverColumn),
		)
		fromV = sqlgraph.Neighbors(mo.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTechnician queries the technician edge of a MaintenanceOrder.
func (c *MaintenanceOrderClient) QueryTechnician(mo *MaintenanceOrder) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := mo.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(maintenanceorder.Table, maintenanceorder.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, maintenanceorder.TechnicianTable, maintenanceorder.TechnicianColumn),
		)
		fromV = sqlgraph.Neighbors(mo.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCostItems queries the cost_items edge of a MaintenanceOrder.
func (c *MaintenanceOrderClient) QueryCostItems(mo *MaintenanceOrder) *CostItemQuery {
	query := (&CostItemClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := mo.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(maintenanceorder.Table, maintenanceorder.FieldID, id),
			sqlgraph.To(costitem.Table, costitem.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, maintenanceorder.CostItemsTable, maintenanceorder.CostItemsColumn),
		)
		fromV = sqlgraph.Neighbors(mo.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryInteractions queries the interactions edge of a MaintenanceOrder.
func (c *MaintenanceOrderClient) QueryInteractions(mo *MaintenanceOrder) *InteractionQuery {
	query := (&InteractionClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := mo.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(maintenanceorder.Table, maintenanceorder.FieldID, id),
			sqlgraph.To(interaction.Table, interaction.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, maintenanceorder.InteractionsTable, maintenanceorder.InteractionsColumn),
		)
		fromV = sqlgraph.Neighbors(mo.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *MaintenanceOrderClient) Hooks() []Hook {
	return c.hooks.MaintenanceOrder
}

// Interceptors returns the client interceptors.
func (c *MaintenanceOrderClient) Interceptors() []Interceptor {
	return c.inters.MaintenanceOrder
}

func (c *MaintenanceOrderClient) mutate(ctx context.Context, m *MaintenanceOrderMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&MaintenanceOrderCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&MaintenanceOrderUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&MaintenanceOrderUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&MaintenanceOrderDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown MaintenanceOrder mutation op: %q", m.Op())
	}
}

// UserClient is a client for the User schema.
type UserClient struct {
	config
}

// NewUserClient returns a client for the User from the given config.
func NewUserClient(c config) *UserClient {
	return &UserClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `user.Hooks(f(g(h())))`.
func (c *UserClient) Use(hooks ...Hook) {
	c.hooks.User = append(c.hooks.User, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `user.Intercept(f(g(h())))`.
func (c *UserClient) Intercept(interceptors ...Interceptor) {
	c.inters.User = append(c.inters.User, interceptors...)
}

// Create returns a builder for creating a User entity.
func (c *UserClient) Create() *UserCreate {
	mutation := newUserMutation(c.config, OpCreate)
	return &UserCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of User entities.
func (c *UserClient) CreateBulk(builders ...*UserCreate) *UserCreateBulk {
	return &UserCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *UserClient) MapCreateBulk(slice any, setFunc func(*UserCreate, int)) *UserCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &UserCreateBulk{err: fmt.Errorf("calling to UserClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*UserCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &UserCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for User.
func (c *UserClient) Update() *UserUpdate {
	mutation := newUserMutation(c.config, OpUpdate)
	return &UserUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *UserClient) UpdateOne(u *User) *UserUpdateOne {
	mutation := newUserMutation(c.config, OpUpdateOne, withUser(u))
	return &UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *UserClient) UpdateOneID(id int) *UserUpdateOne {
	mutation := newUserMutation(c.config, OpUpdateOne, withUserID(id))
	return &UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for User.
func (c *UserClient) Delete() *UserDelete {
	mutation := newUserMutation(c.config, OpDelete)
	return &UserDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *UserClient) DeleteOne(u *User) *UserDeleteOne {
	return c.DeleteOneID(u.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *UserClient) DeleteOneID(id int) *UserDeleteOne {
	builder := c.Delete().Where(user.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &UserDeleteOne{builder}
}

// Query returns a query builder for User.
func (c *UserClient) Query() *UserQuery {
	return &UserQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeUser},
		inters: c.Interceptors(),
	}
}

// Get returns a User entity by its id.
func (c *UserClient) Get(ctx context.Context, id int) (*User, error) {
	return c.Query().Where(user.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *UserClient) GetX(ctx context.Context, id int) *User {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryManagedBranches queries the managed_branches edge of a User.
func (c *UserClient) QueryManagedBranches(u *User) *BranchQuery {
	query := (&BranchClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(branch.Table, branch.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.ManagedBranchesTable, user.ManagedBranchesColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryRequestedOrders queries the requested_orders edge of a User.
func (c *UserClient) QueryRequestedOrders(u *User) *MaintenanceOrderQuery {
	query := (&MaintenanceOrderClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(maintenanceorder.Table, maintenanceorder.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.RequestedOrdersTable, user.RequestedOrdersColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAssignedOrders queries the assigned_orders edge of a User.
func (c *UserClient) QueryAssignedOrders(u *User) *MaintenanceOrderQuery {
	query := (&MaintenanceOrderClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(maintenanceorder.Table, maintenanceorder.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.AssignedOrdersTable, user.AssignedOrdersColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryApprovedOrders queries the approved_orders edge of a User.
func (c *UserClient) QueryApprovedOrders(u *User) *MaintenanceOrderQuery {
	query := (&MaintenanceOrderClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(maintenanceorder.Table, maintenanceorder.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.ApprovedOrdersTable, user.ApprovedOrdersColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCostItems queries the cost_items edge of a User.
func (c *UserClient) QueryCostItems(u *User) *CostItemQuery {
	query := (&CostItemClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(costitem.Table, costitem.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.CostItemsTable, user.CostItemsColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryInteractions queries the interactions edge of a User.
func (c *UserClient) QueryInteractions(u *User) *InteractionQuery {
	query := (&InteractionClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(interaction.Table, interaction.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.InteractionsTable, user.InteractionsColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *UserClient) Hooks() []Hook {
	return c.hooks.User
}

// Interceptors returns the client interceptors.
func (c *UserClient) Interceptors() []Interceptor {
	return c.inters.User
}

func (c *UserClient) mutate(ctx context.Context, m *UserMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&UserCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&UserUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&UserDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown User mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		Branch, CostItem, Equipment, Interaction, MaintenanceOrder, User []ent.Hook
	}
	inters struct {
		Branch, CostItem, Equipment, Interaction, MaintenanceOrder,
		User []ent.Interceptor
	}
)
