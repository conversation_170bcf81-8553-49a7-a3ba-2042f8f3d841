// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"
	"tradicao/internal/ent/interaction"
	"tradicao/internal/ent/maintenanceorder"
	"tradicao/internal/ent/predicate"
	"tradicao/internal/ent/user"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// InteractionUpdate is the builder for updating Interaction entities.
type InteractionUpdate struct {
	config
	hooks    []Hook
	mutation *InteractionMutation
}

// Where appends a list predicates to the InteractionUpdate builder.
func (iu *InteractionUpdate) Where(ps ...predicate.Interaction) *InteractionUpdate {
	iu.mutation.Where(ps...)
	return iu
}

// SetUpdateTime sets the "update_time" field.
func (iu *InteractionUpdate) SetUpdateTime(t time.Time) *InteractionUpdate {
	iu.mutation.SetUpdateTime(t)
	return iu
}

// SetMessage sets the "message" field.
func (iu *InteractionUpdate) SetMessage(s string) *InteractionUpdate {
	iu.mutation.SetMessage(s)
	return iu
}

// SetNillableMessage sets the "message" field if the given value is not nil.
func (iu *InteractionUpdate) SetNillableMessage(s *string) *InteractionUpdate {
	if s != nil {
		iu.SetMessage(*s)
	}
	return iu
}

// SetType sets the "type" field.
func (iu *InteractionUpdate) SetType(s string) *InteractionUpdate {
	iu.mutation.SetType(s)
	return iu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (iu *InteractionUpdate) SetNillableType(s *string) *InteractionUpdate {
	if s != nil {
		iu.SetType(*s)
	}
	return iu
}

// SetMaintenanceOrderID sets the "maintenance_order" edge to the MaintenanceOrder entity by ID.
func (iu *InteractionUpdate) SetMaintenanceOrderID(id int) *InteractionUpdate {
	iu.mutation.SetMaintenanceOrderID(id)
	return iu
}

// SetMaintenanceOrder sets the "maintenance_order" edge to the MaintenanceOrder entity.
func (iu *InteractionUpdate) SetMaintenanceOrder(m *MaintenanceOrder) *InteractionUpdate {
	return iu.SetMaintenanceOrderID(m.ID)
}

// SetUserID sets the "user" edge to the User entity by ID.
func (iu *InteractionUpdate) SetUserID(id int) *InteractionUpdate {
	iu.mutation.SetUserID(id)
	return iu
}

// SetUser sets the "user" edge to the User entity.
func (iu *InteractionUpdate) SetUser(u *User) *InteractionUpdate {
	return iu.SetUserID(u.ID)
}

// Mutation returns the InteractionMutation object of the builder.
func (iu *InteractionUpdate) Mutation() *InteractionMutation {
	return iu.mutation
}

// ClearMaintenanceOrder clears the "maintenance_order" edge to the MaintenanceOrder entity.
func (iu *InteractionUpdate) ClearMaintenanceOrder() *InteractionUpdate {
	iu.mutation.ClearMaintenanceOrder()
	return iu
}

// ClearUser clears the "user" edge to the User entity.
func (iu *InteractionUpdate) ClearUser() *InteractionUpdate {
	iu.mutation.ClearUser()
	return iu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (iu *InteractionUpdate) Save(ctx context.Context) (int, error) {
	iu.defaults()
	return withHooks(ctx, iu.sqlSave, iu.mutation, iu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (iu *InteractionUpdate) SaveX(ctx context.Context) int {
	affected, err := iu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (iu *InteractionUpdate) Exec(ctx context.Context) error {
	_, err := iu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (iu *InteractionUpdate) ExecX(ctx context.Context) {
	if err := iu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (iu *InteractionUpdate) defaults() {
	if _, ok := iu.mutation.UpdateTime(); !ok {
		v := interaction.UpdateDefaultUpdateTime()
		iu.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (iu *InteractionUpdate) check() error {
	if v, ok := iu.mutation.Message(); ok {
		if err := interaction.MessageValidator(v); err != nil {
			return &ValidationError{Name: "message", err: fmt.Errorf(`ent: validator failed for field "Interaction.message": %w`, err)}
		}
	}
	if v, ok := iu.mutation.GetType(); ok {
		if err := interaction.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Interaction.type": %w`, err)}
		}
	}
	if iu.mutation.MaintenanceOrderCleared() && len(iu.mutation.MaintenanceOrderIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Interaction.maintenance_order"`)
	}
	if iu.mutation.UserCleared() && len(iu.mutation.UserIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Interaction.user"`)
	}
	return nil
}

func (iu *InteractionUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := iu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(interaction.Table, interaction.Columns, sqlgraph.NewFieldSpec(interaction.FieldID, field.TypeInt))
	if ps := iu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := iu.mutation.UpdateTime(); ok {
		_spec.SetField(interaction.FieldUpdateTime, field.TypeTime, value)
	}
	if value, ok := iu.mutation.Message(); ok {
		_spec.SetField(interaction.FieldMessage, field.TypeString, value)
	}
	if value, ok := iu.mutation.GetType(); ok {
		_spec.SetField(interaction.FieldType, field.TypeString, value)
	}
	if iu.mutation.MaintenanceOrderCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   interaction.MaintenanceOrderTable,
			Columns: []string{interaction.MaintenanceOrderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := iu.mutation.MaintenanceOrderIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   interaction.MaintenanceOrderTable,
			Columns: []string{interaction.MaintenanceOrderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if iu.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   interaction.UserTable,
			Columns: []string{interaction.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := iu.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   interaction.UserTable,
			Columns: []string{interaction.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, iu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{interaction.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	iu.mutation.done = true
	return n, nil
}

// InteractionUpdateOne is the builder for updating a single Interaction entity.
type InteractionUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *InteractionMutation
}

// SetUpdateTime sets the "update_time" field.
func (iuo *InteractionUpdateOne) SetUpdateTime(t time.Time) *InteractionUpdateOne {
	iuo.mutation.SetUpdateTime(t)
	return iuo
}

// SetMessage sets the "message" field.
func (iuo *InteractionUpdateOne) SetMessage(s string) *InteractionUpdateOne {
	iuo.mutation.SetMessage(s)
	return iuo
}

// SetNillableMessage sets the "message" field if the given value is not nil.
func (iuo *InteractionUpdateOne) SetNillableMessage(s *string) *InteractionUpdateOne {
	if s != nil {
		iuo.SetMessage(*s)
	}
	return iuo
}

// SetType sets the "type" field.
func (iuo *InteractionUpdateOne) SetType(s string) *InteractionUpdateOne {
	iuo.mutation.SetType(s)
	return iuo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (iuo *InteractionUpdateOne) SetNillableType(s *string) *InteractionUpdateOne {
	if s != nil {
		iuo.SetType(*s)
	}
	return iuo
}

// SetMaintenanceOrderID sets the "maintenance_order" edge to the MaintenanceOrder entity by ID.
func (iuo *InteractionUpdateOne) SetMaintenanceOrderID(id int) *InteractionUpdateOne {
	iuo.mutation.SetMaintenanceOrderID(id)
	return iuo
}

// SetMaintenanceOrder sets the "maintenance_order" edge to the MaintenanceOrder entity.
func (iuo *InteractionUpdateOne) SetMaintenanceOrder(m *MaintenanceOrder) *InteractionUpdateOne {
	return iuo.SetMaintenanceOrderID(m.ID)
}

// SetUserID sets the "user" edge to the User entity by ID.
func (iuo *InteractionUpdateOne) SetUserID(id int) *InteractionUpdateOne {
	iuo.mutation.SetUserID(id)
	return iuo
}

// SetUser sets the "user" edge to the User entity.
func (iuo *InteractionUpdateOne) SetUser(u *User) *InteractionUpdateOne {
	return iuo.SetUserID(u.ID)
}

// Mutation returns the InteractionMutation object of the builder.
func (iuo *InteractionUpdateOne) Mutation() *InteractionMutation {
	return iuo.mutation
}

// ClearMaintenanceOrder clears the "maintenance_order" edge to the MaintenanceOrder entity.
func (iuo *InteractionUpdateOne) ClearMaintenanceOrder() *InteractionUpdateOne {
	iuo.mutation.ClearMaintenanceOrder()
	return iuo
}

// ClearUser clears the "user" edge to the User entity.
func (iuo *InteractionUpdateOne) ClearUser() *InteractionUpdateOne {
	iuo.mutation.ClearUser()
	return iuo
}

// Where appends a list predicates to the InteractionUpdate builder.
func (iuo *InteractionUpdateOne) Where(ps ...predicate.Interaction) *InteractionUpdateOne {
	iuo.mutation.Where(ps...)
	return iuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (iuo *InteractionUpdateOne) Select(field string, fields ...string) *InteractionUpdateOne {
	iuo.fields = append([]string{field}, fields...)
	return iuo
}

// Save executes the query and returns the updated Interaction entity.
func (iuo *InteractionUpdateOne) Save(ctx context.Context) (*Interaction, error) {
	iuo.defaults()
	return withHooks(ctx, iuo.sqlSave, iuo.mutation, iuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (iuo *InteractionUpdateOne) SaveX(ctx context.Context) *Interaction {
	node, err := iuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (iuo *InteractionUpdateOne) Exec(ctx context.Context) error {
	_, err := iuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (iuo *InteractionUpdateOne) ExecX(ctx context.Context) {
	if err := iuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (iuo *InteractionUpdateOne) defaults() {
	if _, ok := iuo.mutation.UpdateTime(); !ok {
		v := interaction.UpdateDefaultUpdateTime()
		iuo.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (iuo *InteractionUpdateOne) check() error {
	if v, ok := iuo.mutation.Message(); ok {
		if err := interaction.MessageValidator(v); err != nil {
			return &ValidationError{Name: "message", err: fmt.Errorf(`ent: validator failed for field "Interaction.message": %w`, err)}
		}
	}
	if v, ok := iuo.mutation.GetType(); ok {
		if err := interaction.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Interaction.type": %w`, err)}
		}
	}
	if iuo.mutation.MaintenanceOrderCleared() && len(iuo.mutation.MaintenanceOrderIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Interaction.maintenance_order"`)
	}
	if iuo.mutation.UserCleared() && len(iuo.mutation.UserIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Interaction.user"`)
	}
	return nil
}

func (iuo *InteractionUpdateOne) sqlSave(ctx context.Context) (_node *Interaction, err error) {
	if err := iuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(interaction.Table, interaction.Columns, sqlgraph.NewFieldSpec(interaction.FieldID, field.TypeInt))
	id, ok := iuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Interaction.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := iuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, interaction.FieldID)
		for _, f := range fields {
			if !interaction.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != interaction.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := iuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := iuo.mutation.UpdateTime(); ok {
		_spec.SetField(interaction.FieldUpdateTime, field.TypeTime, value)
	}
	if value, ok := iuo.mutation.Message(); ok {
		_spec.SetField(interaction.FieldMessage, field.TypeString, value)
	}
	if value, ok := iuo.mutation.GetType(); ok {
		_spec.SetField(interaction.FieldType, field.TypeString, value)
	}
	if iuo.mutation.MaintenanceOrderCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   interaction.MaintenanceOrderTable,
			Columns: []string{interaction.MaintenanceOrderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := iuo.mutation.MaintenanceOrderIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   interaction.MaintenanceOrderTable,
			Columns: []string{interaction.MaintenanceOrderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if iuo.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   interaction.UserTable,
			Columns: []string{interaction.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := iuo.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   interaction.UserTable,
			Columns: []string{interaction.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Interaction{config: iuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, iuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{interaction.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	iuo.mutation.done = true
	return _node, nil
}
