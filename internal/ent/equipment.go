// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"
	"tradicao/internal/ent/branch"
	"tradicao/internal/ent/equipment"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Equipment is the model entity for the Equipment schema.
type Equipment struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// Active holds the value of the "active" field.
	Active bool `json:"active,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// Type holds the value of the "type" field.
	Type string `json:"type,omitempty"`
	// Model holds the value of the "model" field.
	Model string `json:"model,omitempty"`
	// SerialNumber holds the value of the "serial_number" field.
	SerialNumber string `json:"serial_number,omitempty"`
	// InstallationDate holds the value of the "installation_date" field.
	InstallationDate time.Time `json:"installation_date,omitempty"`
	// LastMaintenanceDate holds the value of the "last_maintenance_date" field.
	LastMaintenanceDate time.Time `json:"last_maintenance_date,omitempty"`
	// BranchID holds the value of the "branch_id" field.
	BranchID int `json:"branch_id,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the EquipmentQuery when eager-loading is set.
	Edges        EquipmentEdges `json:"edges"`
	selectValues sql.SelectValues
}

// EquipmentEdges holds the relations/edges for other nodes in the graph.
type EquipmentEdges struct {
	// Branch holds the value of the branch edge.
	Branch *Branch `json:"branch,omitempty"`
	// MaintenanceOrders holds the value of the maintenance_orders edge.
	MaintenanceOrders []*MaintenanceOrder `json:"maintenance_orders,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// BranchOrErr returns the Branch value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e EquipmentEdges) BranchOrErr() (*Branch, error) {
	if e.Branch != nil {
		return e.Branch, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: branch.Label}
	}
	return nil, &NotLoadedError{edge: "branch"}
}

// MaintenanceOrdersOrErr returns the MaintenanceOrders value or an error if the edge
// was not loaded in eager-loading.
func (e EquipmentEdges) MaintenanceOrdersOrErr() ([]*MaintenanceOrder, error) {
	if e.loadedTypes[1] {
		return e.MaintenanceOrders, nil
	}
	return nil, &NotLoadedError{edge: "maintenance_orders"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Equipment) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case equipment.FieldActive:
			values[i] = new(sql.NullBool)
		case equipment.FieldID, equipment.FieldBranchID:
			values[i] = new(sql.NullInt64)
		case equipment.FieldName, equipment.FieldType, equipment.FieldModel, equipment.FieldSerialNumber:
			values[i] = new(sql.NullString)
		case equipment.FieldCreatedAt, equipment.FieldUpdatedAt, equipment.FieldInstallationDate, equipment.FieldLastMaintenanceDate:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Equipment fields.
func (e *Equipment) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case equipment.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			e.ID = int(value.Int64)
		case equipment.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				e.CreatedAt = value.Time
			}
		case equipment.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				e.UpdatedAt = value.Time
			}
		case equipment.FieldActive:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field active", values[i])
			} else if value.Valid {
				e.Active = value.Bool
			}
		case equipment.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				e.Name = value.String
			}
		case equipment.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				e.Type = value.String
			}
		case equipment.FieldModel:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field model", values[i])
			} else if value.Valid {
				e.Model = value.String
			}
		case equipment.FieldSerialNumber:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field serial_number", values[i])
			} else if value.Valid {
				e.SerialNumber = value.String
			}
		case equipment.FieldInstallationDate:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field installation_date", values[i])
			} else if value.Valid {
				e.InstallationDate = value.Time
			}
		case equipment.FieldLastMaintenanceDate:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field last_maintenance_date", values[i])
			} else if value.Valid {
				e.LastMaintenanceDate = value.Time
			}
		case equipment.FieldBranchID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field branch_id", values[i])
			} else if value.Valid {
				e.BranchID = int(value.Int64)
			}
		default:
			e.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Equipment.
// This includes values selected through modifiers, order, etc.
func (e *Equipment) Value(name string) (ent.Value, error) {
	return e.selectValues.Get(name)
}

// QueryBranch queries the "branch" edge of the Equipment entity.
func (e *Equipment) QueryBranch() *BranchQuery {
	return NewEquipmentClient(e.config).QueryBranch(e)
}

// QueryMaintenanceOrders queries the "maintenance_orders" edge of the Equipment entity.
func (e *Equipment) QueryMaintenanceOrders() *MaintenanceOrderQuery {
	return NewEquipmentClient(e.config).QueryMaintenanceOrders(e)
}

// Update returns a builder for updating this Equipment.
// Note that you need to call Equipment.Unwrap() before calling this method if this Equipment
// was returned from a transaction, and the transaction was committed or rolled back.
func (e *Equipment) Update() *EquipmentUpdateOne {
	return NewEquipmentClient(e.config).UpdateOne(e)
}

// Unwrap unwraps the Equipment entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (e *Equipment) Unwrap() *Equipment {
	_tx, ok := e.config.driver.(*txDriver)
	if !ok {
		panic("ent: Equipment is not a transactional entity")
	}
	e.config.driver = _tx.drv
	return e
}

// String implements the fmt.Stringer.
func (e *Equipment) String() string {
	var builder strings.Builder
	builder.WriteString("Equipment(")
	builder.WriteString(fmt.Sprintf("id=%v, ", e.ID))
	builder.WriteString("created_at=")
	builder.WriteString(e.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(e.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("active=")
	builder.WriteString(fmt.Sprintf("%v", e.Active))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(e.Name)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(e.Type)
	builder.WriteString(", ")
	builder.WriteString("model=")
	builder.WriteString(e.Model)
	builder.WriteString(", ")
	builder.WriteString("serial_number=")
	builder.WriteString(e.SerialNumber)
	builder.WriteString(", ")
	builder.WriteString("installation_date=")
	builder.WriteString(e.InstallationDate.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("last_maintenance_date=")
	builder.WriteString(e.LastMaintenanceDate.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("branch_id=")
	builder.WriteString(fmt.Sprintf("%v", e.BranchID))
	builder.WriteByte(')')
	return builder.String()
}

// EquipmentSlice is a parsable slice of Equipment.
type EquipmentSlice []*Equipment
