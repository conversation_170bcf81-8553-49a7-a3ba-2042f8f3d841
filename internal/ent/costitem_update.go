// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"
	"tradicao/internal/ent/costitem"
	"tradicao/internal/ent/maintenanceorder"
	"tradicao/internal/ent/predicate"
	"tradicao/internal/ent/user"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CostItemUpdate is the builder for updating CostItem entities.
type CostItemUpdate struct {
	config
	hooks    []Hook
	mutation *CostItemMutation
}

// Where appends a list predicates to the CostItemUpdate builder.
func (ciu *CostItemUpdate) Where(ps ...predicate.CostItem) *CostItemUpdate {
	ciu.mutation.Where(ps...)
	return ciu
}

// SetUpdatedAt sets the "updated_at" field.
func (ciu *CostItemUpdate) SetUpdatedAt(t time.Time) *CostItemUpdate {
	ciu.mutation.SetUpdatedAt(t)
	return ciu
}

// SetActive sets the "active" field.
func (ciu *CostItemUpdate) SetActive(b bool) *CostItemUpdate {
	ciu.mutation.SetActive(b)
	return ciu
}

// SetNillableActive sets the "active" field if the given value is not nil.
func (ciu *CostItemUpdate) SetNillableActive(b *bool) *CostItemUpdate {
	if b != nil {
		ciu.SetActive(*b)
	}
	return ciu
}

// SetMaintenanceOrderID sets the "maintenance_order_id" field.
func (ciu *CostItemUpdate) SetMaintenanceOrderID(i int) *CostItemUpdate {
	ciu.mutation.SetMaintenanceOrderID(i)
	return ciu
}

// SetNillableMaintenanceOrderID sets the "maintenance_order_id" field if the given value is not nil.
func (ciu *CostItemUpdate) SetNillableMaintenanceOrderID(i *int) *CostItemUpdate {
	if i != nil {
		ciu.SetMaintenanceOrderID(*i)
	}
	return ciu
}

// ClearMaintenanceOrderID clears the value of the "maintenance_order_id" field.
func (ciu *CostItemUpdate) ClearMaintenanceOrderID() *CostItemUpdate {
	ciu.mutation.ClearMaintenanceOrderID()
	return ciu
}

// SetUserID sets the "user_id" field.
func (ciu *CostItemUpdate) SetUserID(i int) *CostItemUpdate {
	ciu.mutation.SetUserID(i)
	return ciu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (ciu *CostItemUpdate) SetNillableUserID(i *int) *CostItemUpdate {
	if i != nil {
		ciu.SetUserID(*i)
	}
	return ciu
}

// ClearUserID clears the value of the "user_id" field.
func (ciu *CostItemUpdate) ClearUserID() *CostItemUpdate {
	ciu.mutation.ClearUserID()
	return ciu
}

// SetDescription sets the "description" field.
func (ciu *CostItemUpdate) SetDescription(s string) *CostItemUpdate {
	ciu.mutation.SetDescription(s)
	return ciu
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (ciu *CostItemUpdate) SetNillableDescription(s *string) *CostItemUpdate {
	if s != nil {
		ciu.SetDescription(*s)
	}
	return ciu
}

// SetQuantity sets the "quantity" field.
func (ciu *CostItemUpdate) SetQuantity(f float64) *CostItemUpdate {
	ciu.mutation.ResetQuantity()
	ciu.mutation.SetQuantity(f)
	return ciu
}

// SetNillableQuantity sets the "quantity" field if the given value is not nil.
func (ciu *CostItemUpdate) SetNillableQuantity(f *float64) *CostItemUpdate {
	if f != nil {
		ciu.SetQuantity(*f)
	}
	return ciu
}

// AddQuantity adds f to the "quantity" field.
func (ciu *CostItemUpdate) AddQuantity(f float64) *CostItemUpdate {
	ciu.mutation.AddQuantity(f)
	return ciu
}

// SetUnitPrice sets the "unit_price" field.
func (ciu *CostItemUpdate) SetUnitPrice(f float64) *CostItemUpdate {
	ciu.mutation.ResetUnitPrice()
	ciu.mutation.SetUnitPrice(f)
	return ciu
}

// SetNillableUnitPrice sets the "unit_price" field if the given value is not nil.
func (ciu *CostItemUpdate) SetNillableUnitPrice(f *float64) *CostItemUpdate {
	if f != nil {
		ciu.SetUnitPrice(*f)
	}
	return ciu
}

// AddUnitPrice adds f to the "unit_price" field.
func (ciu *CostItemUpdate) AddUnitPrice(f float64) *CostItemUpdate {
	ciu.mutation.AddUnitPrice(f)
	return ciu
}

// SetTotalPrice sets the "total_price" field.
func (ciu *CostItemUpdate) SetTotalPrice(f float64) *CostItemUpdate {
	ciu.mutation.ResetTotalPrice()
	ciu.mutation.SetTotalPrice(f)
	return ciu
}

// SetNillableTotalPrice sets the "total_price" field if the given value is not nil.
func (ciu *CostItemUpdate) SetNillableTotalPrice(f *float64) *CostItemUpdate {
	if f != nil {
		ciu.SetTotalPrice(*f)
	}
	return ciu
}

// AddTotalPrice adds f to the "total_price" field.
func (ciu *CostItemUpdate) AddTotalPrice(f float64) *CostItemUpdate {
	ciu.mutation.AddTotalPrice(f)
	return ciu
}

// SetMaintenanceOrder sets the "maintenance_order" edge to the MaintenanceOrder entity.
func (ciu *CostItemUpdate) SetMaintenanceOrder(m *MaintenanceOrder) *CostItemUpdate {
	return ciu.SetMaintenanceOrderID(m.ID)
}

// SetUser sets the "user" edge to the User entity.
func (ciu *CostItemUpdate) SetUser(u *User) *CostItemUpdate {
	return ciu.SetUserID(u.ID)
}

// Mutation returns the CostItemMutation object of the builder.
func (ciu *CostItemUpdate) Mutation() *CostItemMutation {
	return ciu.mutation
}

// ClearMaintenanceOrder clears the "maintenance_order" edge to the MaintenanceOrder entity.
func (ciu *CostItemUpdate) ClearMaintenanceOrder() *CostItemUpdate {
	ciu.mutation.ClearMaintenanceOrder()
	return ciu
}

// ClearUser clears the "user" edge to the User entity.
func (ciu *CostItemUpdate) ClearUser() *CostItemUpdate {
	ciu.mutation.ClearUser()
	return ciu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (ciu *CostItemUpdate) Save(ctx context.Context) (int, error) {
	ciu.defaults()
	return withHooks(ctx, ciu.sqlSave, ciu.mutation, ciu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ciu *CostItemUpdate) SaveX(ctx context.Context) int {
	affected, err := ciu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (ciu *CostItemUpdate) Exec(ctx context.Context) error {
	_, err := ciu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ciu *CostItemUpdate) ExecX(ctx context.Context) {
	if err := ciu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ciu *CostItemUpdate) defaults() {
	if _, ok := ciu.mutation.UpdatedAt(); !ok {
		v := costitem.UpdateDefaultUpdatedAt()
		ciu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ciu *CostItemUpdate) check() error {
	if v, ok := ciu.mutation.Description(); ok {
		if err := costitem.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "CostItem.description": %w`, err)}
		}
	}
	return nil
}

func (ciu *CostItemUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := ciu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(costitem.Table, costitem.Columns, sqlgraph.NewFieldSpec(costitem.FieldID, field.TypeInt))
	if ps := ciu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ciu.mutation.UpdatedAt(); ok {
		_spec.SetField(costitem.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ciu.mutation.Active(); ok {
		_spec.SetField(costitem.FieldActive, field.TypeBool, value)
	}
	if value, ok := ciu.mutation.Description(); ok {
		_spec.SetField(costitem.FieldDescription, field.TypeString, value)
	}
	if value, ok := ciu.mutation.Quantity(); ok {
		_spec.SetField(costitem.FieldQuantity, field.TypeFloat64, value)
	}
	if value, ok := ciu.mutation.AddedQuantity(); ok {
		_spec.AddField(costitem.FieldQuantity, field.TypeFloat64, value)
	}
	if value, ok := ciu.mutation.UnitPrice(); ok {
		_spec.SetField(costitem.FieldUnitPrice, field.TypeFloat64, value)
	}
	if value, ok := ciu.mutation.AddedUnitPrice(); ok {
		_spec.AddField(costitem.FieldUnitPrice, field.TypeFloat64, value)
	}
	if value, ok := ciu.mutation.TotalPrice(); ok {
		_spec.SetField(costitem.FieldTotalPrice, field.TypeFloat64, value)
	}
	if value, ok := ciu.mutation.AddedTotalPrice(); ok {
		_spec.AddField(costitem.FieldTotalPrice, field.TypeFloat64, value)
	}
	if ciu.mutation.MaintenanceOrderCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   costitem.MaintenanceOrderTable,
			Columns: []string{costitem.MaintenanceOrderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ciu.mutation.MaintenanceOrderIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   costitem.MaintenanceOrderTable,
			Columns: []string{costitem.MaintenanceOrderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ciu.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   costitem.UserTable,
			Columns: []string{costitem.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ciu.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   costitem.UserTable,
			Columns: []string{costitem.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, ciu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{costitem.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	ciu.mutation.done = true
	return n, nil
}

// CostItemUpdateOne is the builder for updating a single CostItem entity.
type CostItemUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *CostItemMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (ciuo *CostItemUpdateOne) SetUpdatedAt(t time.Time) *CostItemUpdateOne {
	ciuo.mutation.SetUpdatedAt(t)
	return ciuo
}

// SetActive sets the "active" field.
func (ciuo *CostItemUpdateOne) SetActive(b bool) *CostItemUpdateOne {
	ciuo.mutation.SetActive(b)
	return ciuo
}

// SetNillableActive sets the "active" field if the given value is not nil.
func (ciuo *CostItemUpdateOne) SetNillableActive(b *bool) *CostItemUpdateOne {
	if b != nil {
		ciuo.SetActive(*b)
	}
	return ciuo
}

// SetMaintenanceOrderID sets the "maintenance_order_id" field.
func (ciuo *CostItemUpdateOne) SetMaintenanceOrderID(i int) *CostItemUpdateOne {
	ciuo.mutation.SetMaintenanceOrderID(i)
	return ciuo
}

// SetNillableMaintenanceOrderID sets the "maintenance_order_id" field if the given value is not nil.
func (ciuo *CostItemUpdateOne) SetNillableMaintenanceOrderID(i *int) *CostItemUpdateOne {
	if i != nil {
		ciuo.SetMaintenanceOrderID(*i)
	}
	return ciuo
}

// ClearMaintenanceOrderID clears the value of the "maintenance_order_id" field.
func (ciuo *CostItemUpdateOne) ClearMaintenanceOrderID() *CostItemUpdateOne {
	ciuo.mutation.ClearMaintenanceOrderID()
	return ciuo
}

// SetUserID sets the "user_id" field.
func (ciuo *CostItemUpdateOne) SetUserID(i int) *CostItemUpdateOne {
	ciuo.mutation.SetUserID(i)
	return ciuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (ciuo *CostItemUpdateOne) SetNillableUserID(i *int) *CostItemUpdateOne {
	if i != nil {
		ciuo.SetUserID(*i)
	}
	return ciuo
}

// ClearUserID clears the value of the "user_id" field.
func (ciuo *CostItemUpdateOne) ClearUserID() *CostItemUpdateOne {
	ciuo.mutation.ClearUserID()
	return ciuo
}

// SetDescription sets the "description" field.
func (ciuo *CostItemUpdateOne) SetDescription(s string) *CostItemUpdateOne {
	ciuo.mutation.SetDescription(s)
	return ciuo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (ciuo *CostItemUpdateOne) SetNillableDescription(s *string) *CostItemUpdateOne {
	if s != nil {
		ciuo.SetDescription(*s)
	}
	return ciuo
}

// SetQuantity sets the "quantity" field.
func (ciuo *CostItemUpdateOne) SetQuantity(f float64) *CostItemUpdateOne {
	ciuo.mutation.ResetQuantity()
	ciuo.mutation.SetQuantity(f)
	return ciuo
}

// SetNillableQuantity sets the "quantity" field if the given value is not nil.
func (ciuo *CostItemUpdateOne) SetNillableQuantity(f *float64) *CostItemUpdateOne {
	if f != nil {
		ciuo.SetQuantity(*f)
	}
	return ciuo
}

// AddQuantity adds f to the "quantity" field.
func (ciuo *CostItemUpdateOne) AddQuantity(f float64) *CostItemUpdateOne {
	ciuo.mutation.AddQuantity(f)
	return ciuo
}

// SetUnitPrice sets the "unit_price" field.
func (ciuo *CostItemUpdateOne) SetUnitPrice(f float64) *CostItemUpdateOne {
	ciuo.mutation.ResetUnitPrice()
	ciuo.mutation.SetUnitPrice(f)
	return ciuo
}

// SetNillableUnitPrice sets the "unit_price" field if the given value is not nil.
func (ciuo *CostItemUpdateOne) SetNillableUnitPrice(f *float64) *CostItemUpdateOne {
	if f != nil {
		ciuo.SetUnitPrice(*f)
	}
	return ciuo
}

// AddUnitPrice adds f to the "unit_price" field.
func (ciuo *CostItemUpdateOne) AddUnitPrice(f float64) *CostItemUpdateOne {
	ciuo.mutation.AddUnitPrice(f)
	return ciuo
}

// SetTotalPrice sets the "total_price" field.
func (ciuo *CostItemUpdateOne) SetTotalPrice(f float64) *CostItemUpdateOne {
	ciuo.mutation.ResetTotalPrice()
	ciuo.mutation.SetTotalPrice(f)
	return ciuo
}

// SetNillableTotalPrice sets the "total_price" field if the given value is not nil.
func (ciuo *CostItemUpdateOne) SetNillableTotalPrice(f *float64) *CostItemUpdateOne {
	if f != nil {
		ciuo.SetTotalPrice(*f)
	}
	return ciuo
}

// AddTotalPrice adds f to the "total_price" field.
func (ciuo *CostItemUpdateOne) AddTotalPrice(f float64) *CostItemUpdateOne {
	ciuo.mutation.AddTotalPrice(f)
	return ciuo
}

// SetMaintenanceOrder sets the "maintenance_order" edge to the MaintenanceOrder entity.
func (ciuo *CostItemUpdateOne) SetMaintenanceOrder(m *MaintenanceOrder) *CostItemUpdateOne {
	return ciuo.SetMaintenanceOrderID(m.ID)
}

// SetUser sets the "user" edge to the User entity.
func (ciuo *CostItemUpdateOne) SetUser(u *User) *CostItemUpdateOne {
	return ciuo.SetUserID(u.ID)
}

// Mutation returns the CostItemMutation object of the builder.
func (ciuo *CostItemUpdateOne) Mutation() *CostItemMutation {
	return ciuo.mutation
}

// ClearMaintenanceOrder clears the "maintenance_order" edge to the MaintenanceOrder entity.
func (ciuo *CostItemUpdateOne) ClearMaintenanceOrder() *CostItemUpdateOne {
	ciuo.mutation.ClearMaintenanceOrder()
	return ciuo
}

// ClearUser clears the "user" edge to the User entity.
func (ciuo *CostItemUpdateOne) ClearUser() *CostItemUpdateOne {
	ciuo.mutation.ClearUser()
	return ciuo
}

// Where appends a list predicates to the CostItemUpdate builder.
func (ciuo *CostItemUpdateOne) Where(ps ...predicate.CostItem) *CostItemUpdateOne {
	ciuo.mutation.Where(ps...)
	return ciuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (ciuo *CostItemUpdateOne) Select(field string, fields ...string) *CostItemUpdateOne {
	ciuo.fields = append([]string{field}, fields...)
	return ciuo
}

// Save executes the query and returns the updated CostItem entity.
func (ciuo *CostItemUpdateOne) Save(ctx context.Context) (*CostItem, error) {
	ciuo.defaults()
	return withHooks(ctx, ciuo.sqlSave, ciuo.mutation, ciuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ciuo *CostItemUpdateOne) SaveX(ctx context.Context) *CostItem {
	node, err := ciuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (ciuo *CostItemUpdateOne) Exec(ctx context.Context) error {
	_, err := ciuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ciuo *CostItemUpdateOne) ExecX(ctx context.Context) {
	if err := ciuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ciuo *CostItemUpdateOne) defaults() {
	if _, ok := ciuo.mutation.UpdatedAt(); !ok {
		v := costitem.UpdateDefaultUpdatedAt()
		ciuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ciuo *CostItemUpdateOne) check() error {
	if v, ok := ciuo.mutation.Description(); ok {
		if err := costitem.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "CostItem.description": %w`, err)}
		}
	}
	return nil
}

func (ciuo *CostItemUpdateOne) sqlSave(ctx context.Context) (_node *CostItem, err error) {
	if err := ciuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(costitem.Table, costitem.Columns, sqlgraph.NewFieldSpec(costitem.FieldID, field.TypeInt))
	id, ok := ciuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "CostItem.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := ciuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, costitem.FieldID)
		for _, f := range fields {
			if !costitem.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != costitem.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := ciuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ciuo.mutation.UpdatedAt(); ok {
		_spec.SetField(costitem.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ciuo.mutation.Active(); ok {
		_spec.SetField(costitem.FieldActive, field.TypeBool, value)
	}
	if value, ok := ciuo.mutation.Description(); ok {
		_spec.SetField(costitem.FieldDescription, field.TypeString, value)
	}
	if value, ok := ciuo.mutation.Quantity(); ok {
		_spec.SetField(costitem.FieldQuantity, field.TypeFloat64, value)
	}
	if value, ok := ciuo.mutation.AddedQuantity(); ok {
		_spec.AddField(costitem.FieldQuantity, field.TypeFloat64, value)
	}
	if value, ok := ciuo.mutation.UnitPrice(); ok {
		_spec.SetField(costitem.FieldUnitPrice, field.TypeFloat64, value)
	}
	if value, ok := ciuo.mutation.AddedUnitPrice(); ok {
		_spec.AddField(costitem.FieldUnitPrice, field.TypeFloat64, value)
	}
	if value, ok := ciuo.mutation.TotalPrice(); ok {
		_spec.SetField(costitem.FieldTotalPrice, field.TypeFloat64, value)
	}
	if value, ok := ciuo.mutation.AddedTotalPrice(); ok {
		_spec.AddField(costitem.FieldTotalPrice, field.TypeFloat64, value)
	}
	if ciuo.mutation.MaintenanceOrderCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   costitem.MaintenanceOrderTable,
			Columns: []string{costitem.MaintenanceOrderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ciuo.mutation.MaintenanceOrderIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   costitem.MaintenanceOrderTable,
			Columns: []string{costitem.MaintenanceOrderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ciuo.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   costitem.UserTable,
			Columns: []string{costitem.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ciuo.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   costitem.UserTable,
			Columns: []string{costitem.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &CostItem{config: ciuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, ciuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{costitem.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	ciuo.mutation.done = true
	return _node, nil
}
