// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"
	"tradicao/internal/ent/branch"
	"tradicao/internal/ent/costitem"
	"tradicao/internal/ent/equipment"
	"tradicao/internal/ent/interaction"
	"tradicao/internal/ent/maintenanceorder"
	"tradicao/internal/ent/user"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MaintenanceOrderCreate is the builder for creating a MaintenanceOrder entity.
type MaintenanceOrderCreate struct {
	config
	mutation *MaintenanceOrderMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (moc *MaintenanceOrderCreate) SetCreatedAt(t time.Time) *MaintenanceOrderCreate {
	moc.mutation.SetCreatedAt(t)
	return moc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (moc *MaintenanceOrderCreate) SetNillableCreatedAt(t *time.Time) *MaintenanceOrderCreate {
	if t != nil {
		moc.SetCreatedAt(*t)
	}
	return moc
}

// SetUpdatedAt sets the "updated_at" field.
func (moc *MaintenanceOrderCreate) SetUpdatedAt(t time.Time) *MaintenanceOrderCreate {
	moc.mutation.SetUpdatedAt(t)
	return moc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (moc *MaintenanceOrderCreate) SetNillableUpdatedAt(t *time.Time) *MaintenanceOrderCreate {
	if t != nil {
		moc.SetUpdatedAt(*t)
	}
	return moc
}

// SetActive sets the "active" field.
func (moc *MaintenanceOrderCreate) SetActive(b bool) *MaintenanceOrderCreate {
	moc.mutation.SetActive(b)
	return moc
}

// SetNillableActive sets the "active" field if the given value is not nil.
func (moc *MaintenanceOrderCreate) SetNillableActive(b *bool) *MaintenanceOrderCreate {
	if b != nil {
		moc.SetActive(*b)
	}
	return moc
}

// SetTitle sets the "title" field.
func (moc *MaintenanceOrderCreate) SetTitle(s string) *MaintenanceOrderCreate {
	moc.mutation.SetTitle(s)
	return moc
}

// SetDescription sets the "description" field.
func (moc *MaintenanceOrderCreate) SetDescription(s string) *MaintenanceOrderCreate {
	moc.mutation.SetDescription(s)
	return moc
}

// SetStatus sets the "status" field.
func (moc *MaintenanceOrderCreate) SetStatus(s string) *MaintenanceOrderCreate {
	moc.mutation.SetStatus(s)
	return moc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (moc *MaintenanceOrderCreate) SetNillableStatus(s *string) *MaintenanceOrderCreate {
	if s != nil {
		moc.SetStatus(*s)
	}
	return moc
}

// SetPriority sets the "priority" field.
func (moc *MaintenanceOrderCreate) SetPriority(s string) *MaintenanceOrderCreate {
	moc.mutation.SetPriority(s)
	return moc
}

// SetNillablePriority sets the "priority" field if the given value is not nil.
func (moc *MaintenanceOrderCreate) SetNillablePriority(s *string) *MaintenanceOrderCreate {
	if s != nil {
		moc.SetPriority(*s)
	}
	return moc
}

// SetBranchID sets the "branch_id" field.
func (moc *MaintenanceOrderCreate) SetBranchID(i int) *MaintenanceOrderCreate {
	moc.mutation.SetBranchID(i)
	return moc
}

// SetNillableBranchID sets the "branch_id" field if the given value is not nil.
func (moc *MaintenanceOrderCreate) SetNillableBranchID(i *int) *MaintenanceOrderCreate {
	if i != nil {
		moc.SetBranchID(*i)
	}
	return moc
}

// SetEquipmentID sets the "equipment_id" field.
func (moc *MaintenanceOrderCreate) SetEquipmentID(i int) *MaintenanceOrderCreate {
	moc.mutation.SetEquipmentID(i)
	return moc
}

// SetNillableEquipmentID sets the "equipment_id" field if the given value is not nil.
func (moc *MaintenanceOrderCreate) SetNillableEquipmentID(i *int) *MaintenanceOrderCreate {
	if i != nil {
		moc.SetEquipmentID(*i)
	}
	return moc
}

// SetRequesterID sets the "requester_id" field.
func (moc *MaintenanceOrderCreate) SetRequesterID(i int) *MaintenanceOrderCreate {
	moc.mutation.SetRequesterID(i)
	return moc
}

// SetNillableRequesterID sets the "requester_id" field if the given value is not nil.
func (moc *MaintenanceOrderCreate) SetNillableRequesterID(i *int) *MaintenanceOrderCreate {
	if i != nil {
		moc.SetRequesterID(*i)
	}
	return moc
}

// SetApproverID sets the "approver_id" field.
func (moc *MaintenanceOrderCreate) SetApproverID(i int) *MaintenanceOrderCreate {
	moc.mutation.SetApproverID(i)
	return moc
}

// SetNillableApproverID sets the "approver_id" field if the given value is not nil.
func (moc *MaintenanceOrderCreate) SetNillableApproverID(i *int) *MaintenanceOrderCreate {
	if i != nil {
		moc.SetApproverID(*i)
	}
	return moc
}

// SetTechnicianID sets the "technician_id" field.
func (moc *MaintenanceOrderCreate) SetTechnicianID(i int) *MaintenanceOrderCreate {
	moc.mutation.SetTechnicianID(i)
	return moc
}

// SetNillableTechnicianID sets the "technician_id" field if the given value is not nil.
func (moc *MaintenanceOrderCreate) SetNillableTechnicianID(i *int) *MaintenanceOrderCreate {
	if i != nil {
		moc.SetTechnicianID(*i)
	}
	return moc
}

// SetCancellationReason sets the "cancellation_reason" field.
func (moc *MaintenanceOrderCreate) SetCancellationReason(s string) *MaintenanceOrderCreate {
	moc.mutation.SetCancellationReason(s)
	return moc
}

// SetNillableCancellationReason sets the "cancellation_reason" field if the given value is not nil.
func (moc *MaintenanceOrderCreate) SetNillableCancellationReason(s *string) *MaintenanceOrderCreate {
	if s != nil {
		moc.SetCancellationReason(*s)
	}
	return moc
}

// SetRejectionReason sets the "rejection_reason" field.
func (moc *MaintenanceOrderCreate) SetRejectionReason(s string) *MaintenanceOrderCreate {
	moc.mutation.SetRejectionReason(s)
	return moc
}

// SetNillableRejectionReason sets the "rejection_reason" field if the given value is not nil.
func (moc *MaintenanceOrderCreate) SetNillableRejectionReason(s *string) *MaintenanceOrderCreate {
	if s != nil {
		moc.SetRejectionReason(*s)
	}
	return moc
}

// SetBranch sets the "branch" edge to the Branch entity.
func (moc *MaintenanceOrderCreate) SetBranch(b *Branch) *MaintenanceOrderCreate {
	return moc.SetBranchID(b.ID)
}

// SetEquipment sets the "equipment" edge to the Equipment entity.
func (moc *MaintenanceOrderCreate) SetEquipment(e *Equipment) *MaintenanceOrderCreate {
	return moc.SetEquipmentID(e.ID)
}

// SetRequester sets the "requester" edge to the User entity.
func (moc *MaintenanceOrderCreate) SetRequester(u *User) *MaintenanceOrderCreate {
	return moc.SetRequesterID(u.ID)
}

// SetApprover sets the "approver" edge to the User entity.
func (moc *MaintenanceOrderCreate) SetApprover(u *User) *MaintenanceOrderCreate {
	return moc.SetApproverID(u.ID)
}

// SetTechnician sets the "technician" edge to the User entity.
func (moc *MaintenanceOrderCreate) SetTechnician(u *User) *MaintenanceOrderCreate {
	return moc.SetTechnicianID(u.ID)
}

// AddCostItemIDs adds the "cost_items" edge to the CostItem entity by IDs.
func (moc *MaintenanceOrderCreate) AddCostItemIDs(ids ...int) *MaintenanceOrderCreate {
	moc.mutation.AddCostItemIDs(ids...)
	return moc
}

// AddCostItems adds the "cost_items" edges to the CostItem entity.
func (moc *MaintenanceOrderCreate) AddCostItems(c ...*CostItem) *MaintenanceOrderCreate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return moc.AddCostItemIDs(ids...)
}

// AddInteractionIDs adds the "interactions" edge to the Interaction entity by IDs.
func (moc *MaintenanceOrderCreate) AddInteractionIDs(ids ...int) *MaintenanceOrderCreate {
	moc.mutation.AddInteractionIDs(ids...)
	return moc
}

// AddInteractions adds the "interactions" edges to the Interaction entity.
func (moc *MaintenanceOrderCreate) AddInteractions(i ...*Interaction) *MaintenanceOrderCreate {
	ids := make([]int, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return moc.AddInteractionIDs(ids...)
}

// Mutation returns the MaintenanceOrderMutation object of the builder.
func (moc *MaintenanceOrderCreate) Mutation() *MaintenanceOrderMutation {
	return moc.mutation
}

// Save creates the MaintenanceOrder in the database.
func (moc *MaintenanceOrderCreate) Save(ctx context.Context) (*MaintenanceOrder, error) {
	moc.defaults()
	return withHooks(ctx, moc.sqlSave, moc.mutation, moc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (moc *MaintenanceOrderCreate) SaveX(ctx context.Context) *MaintenanceOrder {
	v, err := moc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (moc *MaintenanceOrderCreate) Exec(ctx context.Context) error {
	_, err := moc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (moc *MaintenanceOrderCreate) ExecX(ctx context.Context) {
	if err := moc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (moc *MaintenanceOrderCreate) defaults() {
	if _, ok := moc.mutation.CreatedAt(); !ok {
		v := maintenanceorder.DefaultCreatedAt()
		moc.mutation.SetCreatedAt(v)
	}
	if _, ok := moc.mutation.UpdatedAt(); !ok {
		v := maintenanceorder.DefaultUpdatedAt()
		moc.mutation.SetUpdatedAt(v)
	}
	if _, ok := moc.mutation.Active(); !ok {
		v := maintenanceorder.DefaultActive
		moc.mutation.SetActive(v)
	}
	if _, ok := moc.mutation.Status(); !ok {
		v := maintenanceorder.DefaultStatus
		moc.mutation.SetStatus(v)
	}
	if _, ok := moc.mutation.Priority(); !ok {
		v := maintenanceorder.DefaultPriority
		moc.mutation.SetPriority(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (moc *MaintenanceOrderCreate) check() error {
	if _, ok := moc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "MaintenanceOrder.created_at"`)}
	}
	if _, ok := moc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "MaintenanceOrder.updated_at"`)}
	}
	if _, ok := moc.mutation.Active(); !ok {
		return &ValidationError{Name: "active", err: errors.New(`ent: missing required field "MaintenanceOrder.active"`)}
	}
	if _, ok := moc.mutation.Title(); !ok {
		return &ValidationError{Name: "title", err: errors.New(`ent: missing required field "MaintenanceOrder.title"`)}
	}
	if v, ok := moc.mutation.Title(); ok {
		if err := maintenanceorder.TitleValidator(v); err != nil {
			return &ValidationError{Name: "title", err: fmt.Errorf(`ent: validator failed for field "MaintenanceOrder.title": %w`, err)}
		}
	}
	if _, ok := moc.mutation.Description(); !ok {
		return &ValidationError{Name: "description", err: errors.New(`ent: missing required field "MaintenanceOrder.description"`)}
	}
	if v, ok := moc.mutation.Description(); ok {
		if err := maintenanceorder.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "MaintenanceOrder.description": %w`, err)}
		}
	}
	if _, ok := moc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "MaintenanceOrder.status"`)}
	}
	if _, ok := moc.mutation.Priority(); !ok {
		return &ValidationError{Name: "priority", err: errors.New(`ent: missing required field "MaintenanceOrder.priority"`)}
	}
	return nil
}

func (moc *MaintenanceOrderCreate) sqlSave(ctx context.Context) (*MaintenanceOrder, error) {
	if err := moc.check(); err != nil {
		return nil, err
	}
	_node, _spec := moc.createSpec()
	if err := sqlgraph.CreateNode(ctx, moc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	moc.mutation.id = &_node.ID
	moc.mutation.done = true
	return _node, nil
}

func (moc *MaintenanceOrderCreate) createSpec() (*MaintenanceOrder, *sqlgraph.CreateSpec) {
	var (
		_node = &MaintenanceOrder{config: moc.config}
		_spec = sqlgraph.NewCreateSpec(maintenanceorder.Table, sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt))
	)
	if value, ok := moc.mutation.CreatedAt(); ok {
		_spec.SetField(maintenanceorder.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := moc.mutation.UpdatedAt(); ok {
		_spec.SetField(maintenanceorder.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := moc.mutation.Active(); ok {
		_spec.SetField(maintenanceorder.FieldActive, field.TypeBool, value)
		_node.Active = value
	}
	if value, ok := moc.mutation.Title(); ok {
		_spec.SetField(maintenanceorder.FieldTitle, field.TypeString, value)
		_node.Title = value
	}
	if value, ok := moc.mutation.Description(); ok {
		_spec.SetField(maintenanceorder.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := moc.mutation.Status(); ok {
		_spec.SetField(maintenanceorder.FieldStatus, field.TypeString, value)
		_node.Status = value
	}
	if value, ok := moc.mutation.Priority(); ok {
		_spec.SetField(maintenanceorder.FieldPriority, field.TypeString, value)
		_node.Priority = value
	}
	if value, ok := moc.mutation.CancellationReason(); ok {
		_spec.SetField(maintenanceorder.FieldCancellationReason, field.TypeString, value)
		_node.CancellationReason = value
	}
	if value, ok := moc.mutation.RejectionReason(); ok {
		_spec.SetField(maintenanceorder.FieldRejectionReason, field.TypeString, value)
		_node.RejectionReason = value
	}
	if nodes := moc.mutation.BranchIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.BranchTable,
			Columns: []string{maintenanceorder.BranchColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(branch.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.BranchID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := moc.mutation.EquipmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.EquipmentTable,
			Columns: []string{maintenanceorder.EquipmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(equipment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.EquipmentID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := moc.mutation.RequesterIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.RequesterTable,
			Columns: []string{maintenanceorder.RequesterColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.RequesterID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := moc.mutation.ApproverIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.ApproverTable,
			Columns: []string{maintenanceorder.ApproverColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.ApproverID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := moc.mutation.TechnicianIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   maintenanceorder.TechnicianTable,
			Columns: []string{maintenanceorder.TechnicianColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TechnicianID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := moc.mutation.CostItemsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   maintenanceorder.CostItemsTable,
			Columns: []string{maintenanceorder.CostItemsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(costitem.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := moc.mutation.InteractionsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   maintenanceorder.InteractionsTable,
			Columns: []string{maintenanceorder.InteractionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(interaction.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// MaintenanceOrderCreateBulk is the builder for creating many MaintenanceOrder entities in bulk.
type MaintenanceOrderCreateBulk struct {
	config
	err      error
	builders []*MaintenanceOrderCreate
}

// Save creates the MaintenanceOrder entities in the database.
func (mocb *MaintenanceOrderCreateBulk) Save(ctx context.Context) ([]*MaintenanceOrder, error) {
	if mocb.err != nil {
		return nil, mocb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(mocb.builders))
	nodes := make([]*MaintenanceOrder, len(mocb.builders))
	mutators := make([]Mutator, len(mocb.builders))
	for i := range mocb.builders {
		func(i int, root context.Context) {
			builder := mocb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*MaintenanceOrderMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, mocb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, mocb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, mocb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (mocb *MaintenanceOrderCreateBulk) SaveX(ctx context.Context) []*MaintenanceOrder {
	v, err := mocb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (mocb *MaintenanceOrderCreateBulk) Exec(ctx context.Context) error {
	_, err := mocb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (mocb *MaintenanceOrderCreateBulk) ExecX(ctx context.Context) {
	if err := mocb.Exec(ctx); err != nil {
		panic(err)
	}
}
