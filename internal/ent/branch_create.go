// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"
	"tradicao/internal/ent/branch"
	"tradicao/internal/ent/equipment"
	"tradicao/internal/ent/maintenanceorder"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// BranchCreate is the builder for creating a Branch entity.
type BranchCreate struct {
	config
	mutation *BranchMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (bc *BranchCreate) SetCreatedAt(t time.Time) *BranchCreate {
	bc.mutation.SetCreatedAt(t)
	return bc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (bc *BranchCreate) SetNillableCreatedAt(t *time.Time) *BranchCreate {
	if t != nil {
		bc.SetCreatedAt(*t)
	}
	return bc
}

// SetUpdatedAt sets the "updated_at" field.
func (bc *BranchCreate) SetUpdatedAt(t time.Time) *BranchCreate {
	bc.mutation.SetUpdatedAt(t)
	return bc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (bc *BranchCreate) SetNillableUpdatedAt(t *time.Time) *BranchCreate {
	if t != nil {
		bc.SetUpdatedAt(*t)
	}
	return bc
}

// SetActive sets the "active" field.
func (bc *BranchCreate) SetActive(b bool) *BranchCreate {
	bc.mutation.SetActive(b)
	return bc
}

// SetNillableActive sets the "active" field if the given value is not nil.
func (bc *BranchCreate) SetNillableActive(b *bool) *BranchCreate {
	if b != nil {
		bc.SetActive(*b)
	}
	return bc
}

// SetName sets the "name" field.
func (bc *BranchCreate) SetName(s string) *BranchCreate {
	bc.mutation.SetName(s)
	return bc
}

// SetCode sets the "code" field.
func (bc *BranchCreate) SetCode(s string) *BranchCreate {
	bc.mutation.SetCode(s)
	return bc
}

// SetAddress sets the "address" field.
func (bc *BranchCreate) SetAddress(s string) *BranchCreate {
	bc.mutation.SetAddress(s)
	return bc
}

// SetCity sets the "city" field.
func (bc *BranchCreate) SetCity(s string) *BranchCreate {
	bc.mutation.SetCity(s)
	return bc
}

// SetState sets the "state" field.
func (bc *BranchCreate) SetState(s string) *BranchCreate {
	bc.mutation.SetState(s)
	return bc
}

// SetZipCode sets the "zip_code" field.
func (bc *BranchCreate) SetZipCode(s string) *BranchCreate {
	bc.mutation.SetZipCode(s)
	return bc
}

// SetPhone sets the "phone" field.
func (bc *BranchCreate) SetPhone(s string) *BranchCreate {
	bc.mutation.SetPhone(s)
	return bc
}

// SetEmail sets the "email" field.
func (bc *BranchCreate) SetEmail(s string) *BranchCreate {
	bc.mutation.SetEmail(s)
	return bc
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (bc *BranchCreate) SetNillableEmail(s *string) *BranchCreate {
	if s != nil {
		bc.SetEmail(*s)
	}
	return bc
}

// SetStatus sets the "status" field.
func (bc *BranchCreate) SetStatus(s string) *BranchCreate {
	bc.mutation.SetStatus(s)
	return bc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (bc *BranchCreate) SetNillableStatus(s *string) *BranchCreate {
	if s != nil {
		bc.SetStatus(*s)
	}
	return bc
}

// SetInaugurationDate sets the "inauguration_date" field.
func (bc *BranchCreate) SetInaugurationDate(t time.Time) *BranchCreate {
	bc.mutation.SetInaugurationDate(t)
	return bc
}

// SetNillableInaugurationDate sets the "inauguration_date" field if the given value is not nil.
func (bc *BranchCreate) SetNillableInaugurationDate(t *time.Time) *BranchCreate {
	if t != nil {
		bc.SetInaugurationDate(*t)
	}
	return bc
}

// SetIsActive sets the "is_active" field.
func (bc *BranchCreate) SetIsActive(b bool) *BranchCreate {
	bc.mutation.SetIsActive(b)
	return bc
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (bc *BranchCreate) SetNillableIsActive(b *bool) *BranchCreate {
	if b != nil {
		bc.SetIsActive(*b)
	}
	return bc
}

// AddEquipmentIDs adds the "equipment" edge to the Equipment entity by IDs.
func (bc *BranchCreate) AddEquipmentIDs(ids ...int) *BranchCreate {
	bc.mutation.AddEquipmentIDs(ids...)
	return bc
}

// AddEquipment adds the "equipment" edges to the Equipment entity.
func (bc *BranchCreate) AddEquipment(e ...*Equipment) *BranchCreate {
	ids := make([]int, len(e))
	for i := range e {
		ids[i] = e[i].ID
	}
	return bc.AddEquipmentIDs(ids...)
}

// AddMaintenanceOrderIDs adds the "maintenance_orders" edge to the MaintenanceOrder entity by IDs.
func (bc *BranchCreate) AddMaintenanceOrderIDs(ids ...int) *BranchCreate {
	bc.mutation.AddMaintenanceOrderIDs(ids...)
	return bc
}

// AddMaintenanceOrders adds the "maintenance_orders" edges to the MaintenanceOrder entity.
func (bc *BranchCreate) AddMaintenanceOrders(m ...*MaintenanceOrder) *BranchCreate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return bc.AddMaintenanceOrderIDs(ids...)
}

// Mutation returns the BranchMutation object of the builder.
func (bc *BranchCreate) Mutation() *BranchMutation {
	return bc.mutation
}

// Save creates the Branch in the database.
func (bc *BranchCreate) Save(ctx context.Context) (*Branch, error) {
	bc.defaults()
	return withHooks(ctx, bc.sqlSave, bc.mutation, bc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (bc *BranchCreate) SaveX(ctx context.Context) *Branch {
	v, err := bc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (bc *BranchCreate) Exec(ctx context.Context) error {
	_, err := bc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (bc *BranchCreate) ExecX(ctx context.Context) {
	if err := bc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (bc *BranchCreate) defaults() {
	if _, ok := bc.mutation.CreatedAt(); !ok {
		v := branch.DefaultCreatedAt()
		bc.mutation.SetCreatedAt(v)
	}
	if _, ok := bc.mutation.UpdatedAt(); !ok {
		v := branch.DefaultUpdatedAt()
		bc.mutation.SetUpdatedAt(v)
	}
	if _, ok := bc.mutation.Active(); !ok {
		v := branch.DefaultActive
		bc.mutation.SetActive(v)
	}
	if _, ok := bc.mutation.Status(); !ok {
		v := branch.DefaultStatus
		bc.mutation.SetStatus(v)
	}
	if _, ok := bc.mutation.InaugurationDate(); !ok {
		v := branch.DefaultInaugurationDate()
		bc.mutation.SetInaugurationDate(v)
	}
	if _, ok := bc.mutation.IsActive(); !ok {
		v := branch.DefaultIsActive
		bc.mutation.SetIsActive(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (bc *BranchCreate) check() error {
	if _, ok := bc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Branch.created_at"`)}
	}
	if _, ok := bc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Branch.updated_at"`)}
	}
	if _, ok := bc.mutation.Active(); !ok {
		return &ValidationError{Name: "active", err: errors.New(`ent: missing required field "Branch.active"`)}
	}
	if _, ok := bc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Branch.name"`)}
	}
	if v, ok := bc.mutation.Name(); ok {
		if err := branch.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Branch.name": %w`, err)}
		}
	}
	if _, ok := bc.mutation.Code(); !ok {
		return &ValidationError{Name: "code", err: errors.New(`ent: missing required field "Branch.code"`)}
	}
	if v, ok := bc.mutation.Code(); ok {
		if err := branch.CodeValidator(v); err != nil {
			return &ValidationError{Name: "code", err: fmt.Errorf(`ent: validator failed for field "Branch.code": %w`, err)}
		}
	}
	if _, ok := bc.mutation.Address(); !ok {
		return &ValidationError{Name: "address", err: errors.New(`ent: missing required field "Branch.address"`)}
	}
	if v, ok := bc.mutation.Address(); ok {
		if err := branch.AddressValidator(v); err != nil {
			return &ValidationError{Name: "address", err: fmt.Errorf(`ent: validator failed for field "Branch.address": %w`, err)}
		}
	}
	if _, ok := bc.mutation.City(); !ok {
		return &ValidationError{Name: "city", err: errors.New(`ent: missing required field "Branch.city"`)}
	}
	if v, ok := bc.mutation.City(); ok {
		if err := branch.CityValidator(v); err != nil {
			return &ValidationError{Name: "city", err: fmt.Errorf(`ent: validator failed for field "Branch.city": %w`, err)}
		}
	}
	if _, ok := bc.mutation.State(); !ok {
		return &ValidationError{Name: "state", err: errors.New(`ent: missing required field "Branch.state"`)}
	}
	if v, ok := bc.mutation.State(); ok {
		if err := branch.StateValidator(v); err != nil {
			return &ValidationError{Name: "state", err: fmt.Errorf(`ent: validator failed for field "Branch.state": %w`, err)}
		}
	}
	if _, ok := bc.mutation.ZipCode(); !ok {
		return &ValidationError{Name: "zip_code", err: errors.New(`ent: missing required field "Branch.zip_code"`)}
	}
	if v, ok := bc.mutation.ZipCode(); ok {
		if err := branch.ZipCodeValidator(v); err != nil {
			return &ValidationError{Name: "zip_code", err: fmt.Errorf(`ent: validator failed for field "Branch.zip_code": %w`, err)}
		}
	}
	if _, ok := bc.mutation.Phone(); !ok {
		return &ValidationError{Name: "phone", err: errors.New(`ent: missing required field "Branch.phone"`)}
	}
	if v, ok := bc.mutation.Phone(); ok {
		if err := branch.PhoneValidator(v); err != nil {
			return &ValidationError{Name: "phone", err: fmt.Errorf(`ent: validator failed for field "Branch.phone": %w`, err)}
		}
	}
	if _, ok := bc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Branch.status"`)}
	}
	if _, ok := bc.mutation.InaugurationDate(); !ok {
		return &ValidationError{Name: "inauguration_date", err: errors.New(`ent: missing required field "Branch.inauguration_date"`)}
	}
	if _, ok := bc.mutation.IsActive(); !ok {
		return &ValidationError{Name: "is_active", err: errors.New(`ent: missing required field "Branch.is_active"`)}
	}
	return nil
}

func (bc *BranchCreate) sqlSave(ctx context.Context) (*Branch, error) {
	if err := bc.check(); err != nil {
		return nil, err
	}
	_node, _spec := bc.createSpec()
	if err := sqlgraph.CreateNode(ctx, bc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	bc.mutation.id = &_node.ID
	bc.mutation.done = true
	return _node, nil
}

func (bc *BranchCreate) createSpec() (*Branch, *sqlgraph.CreateSpec) {
	var (
		_node = &Branch{config: bc.config}
		_spec = sqlgraph.NewCreateSpec(branch.Table, sqlgraph.NewFieldSpec(branch.FieldID, field.TypeInt))
	)
	if value, ok := bc.mutation.CreatedAt(); ok {
		_spec.SetField(branch.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := bc.mutation.UpdatedAt(); ok {
		_spec.SetField(branch.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := bc.mutation.Active(); ok {
		_spec.SetField(branch.FieldActive, field.TypeBool, value)
		_node.Active = value
	}
	if value, ok := bc.mutation.Name(); ok {
		_spec.SetField(branch.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := bc.mutation.Code(); ok {
		_spec.SetField(branch.FieldCode, field.TypeString, value)
		_node.Code = value
	}
	if value, ok := bc.mutation.Address(); ok {
		_spec.SetField(branch.FieldAddress, field.TypeString, value)
		_node.Address = value
	}
	if value, ok := bc.mutation.City(); ok {
		_spec.SetField(branch.FieldCity, field.TypeString, value)
		_node.City = value
	}
	if value, ok := bc.mutation.State(); ok {
		_spec.SetField(branch.FieldState, field.TypeString, value)
		_node.State = value
	}
	if value, ok := bc.mutation.ZipCode(); ok {
		_spec.SetField(branch.FieldZipCode, field.TypeString, value)
		_node.ZipCode = value
	}
	if value, ok := bc.mutation.Phone(); ok {
		_spec.SetField(branch.FieldPhone, field.TypeString, value)
		_node.Phone = value
	}
	if value, ok := bc.mutation.Email(); ok {
		_spec.SetField(branch.FieldEmail, field.TypeString, value)
		_node.Email = value
	}
	if value, ok := bc.mutation.Status(); ok {
		_spec.SetField(branch.FieldStatus, field.TypeString, value)
		_node.Status = value
	}
	if value, ok := bc.mutation.InaugurationDate(); ok {
		_spec.SetField(branch.FieldInaugurationDate, field.TypeTime, value)
		_node.InaugurationDate = value
	}
	if value, ok := bc.mutation.IsActive(); ok {
		_spec.SetField(branch.FieldIsActive, field.TypeBool, value)
		_node.IsActive = value
	}
	if nodes := bc.mutation.EquipmentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   branch.EquipmentTable,
			Columns: []string{branch.EquipmentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(equipment.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := bc.mutation.MaintenanceOrdersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   branch.MaintenanceOrdersTable,
			Columns: []string{branch.MaintenanceOrdersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(maintenanceorder.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// BranchCreateBulk is the builder for creating many Branch entities in bulk.
type BranchCreateBulk struct {
	config
	err      error
	builders []*BranchCreate
}

// Save creates the Branch entities in the database.
func (bcb *BranchCreateBulk) Save(ctx context.Context) ([]*Branch, error) {
	if bcb.err != nil {
		return nil, bcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(bcb.builders))
	nodes := make([]*Branch, len(bcb.builders))
	mutators := make([]Mutator, len(bcb.builders))
	for i := range bcb.builders {
		func(i int, root context.Context) {
			builder := bcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*BranchMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, bcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, bcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, bcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (bcb *BranchCreateBulk) SaveX(ctx context.Context) []*Branch {
	v, err := bcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (bcb *BranchCreateBulk) Exec(ctx context.Context) error {
	_, err := bcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (bcb *BranchCreateBulk) ExecX(ctx context.Context) {
	if err := bcb.Exec(ctx); err != nil {
		panic(err)
	}
}
