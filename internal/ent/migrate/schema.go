// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// BranchesColumns holds the columns for the "branches" table.
	BranchesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "active", Type: field.TypeBool, Default: true},
		{Name: "name", Type: field.TypeString},
		{Name: "code", Type: field.TypeString},
		{Name: "address", Type: field.TypeString},
		{Name: "city", Type: field.TypeString},
		{Name: "state", Type: field.TypeString},
		{Name: "zip_code", Type: field.TypeString},
		{Name: "phone", Type: field.TypeString},
		{Name: "email", Type: field.TypeString, Nullable: true},
		{Name: "status", Type: field.TypeString, Default: "ativo"},
		{Name: "inauguration_date", Type: field.TypeTime},
		{Name: "is_active", Type: field.TypeBool, Default: true},
		{Name: "user_managed_branches", Type: field.TypeInt, Nullable: true},
	}
	// BranchesTable holds the schema information for the "branches" table.
	BranchesTable = &schema.Table{
		Name:       "branches",
		Columns:    BranchesColumns,
		PrimaryKey: []*schema.Column{BranchesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "branches_users_managed_branches",
				Columns:    []*schema.Column{BranchesColumns[15]},
				RefColumns: []*schema.Column{UsersColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// CostItemsColumns holds the columns for the "cost_items" table.
	CostItemsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "active", Type: field.TypeBool, Default: true},
		{Name: "description", Type: field.TypeString},
		{Name: "quantity", Type: field.TypeFloat64, Default: 1},
		{Name: "unit_price", Type: field.TypeFloat64, Default: 0},
		{Name: "total_price", Type: field.TypeFloat64, Default: 0},
		{Name: "maintenance_order_id", Type: field.TypeInt, Nullable: true},
		{Name: "user_id", Type: field.TypeInt, Nullable: true},
	}
	// CostItemsTable holds the schema information for the "cost_items" table.
	CostItemsTable = &schema.Table{
		Name:       "cost_items",
		Columns:    CostItemsColumns,
		PrimaryKey: []*schema.Column{CostItemsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "cost_items_maintenance_orders_cost_items",
				Columns:    []*schema.Column{CostItemsColumns[8]},
				RefColumns: []*schema.Column{MaintenanceOrdersColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "cost_items_users_cost_items",
				Columns:    []*schema.Column{CostItemsColumns[9]},
				RefColumns: []*schema.Column{UsersColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// EquipmentColumns holds the columns for the "equipment" table.
	EquipmentColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "active", Type: field.TypeBool, Default: true},
		{Name: "name", Type: field.TypeString},
		{Name: "type", Type: field.TypeString},
		{Name: "model", Type: field.TypeString},
		{Name: "serial_number", Type: field.TypeString, Unique: true},
		{Name: "installation_date", Type: field.TypeTime, Nullable: true},
		{Name: "last_maintenance_date", Type: field.TypeTime, Nullable: true},
		{Name: "branch_id", Type: field.TypeInt, Nullable: true},
	}
	// EquipmentTable holds the schema information for the "equipment" table.
	EquipmentTable = &schema.Table{
		Name:       "equipment",
		Columns:    EquipmentColumns,
		PrimaryKey: []*schema.Column{EquipmentColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "equipment_branches_equipment",
				Columns:    []*schema.Column{EquipmentColumns[10]},
				RefColumns: []*schema.Column{BranchesColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// InteractionsColumns holds the columns for the "interactions" table.
	InteractionsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "create_time", Type: field.TypeTime},
		{Name: "update_time", Type: field.TypeTime},
		{Name: "message", Type: field.TypeString, Size: 1000},
		{Name: "type", Type: field.TypeString, Default: "comment"},
		{Name: "maintenance_order_interactions", Type: field.TypeInt},
		{Name: "user_interactions", Type: field.TypeInt},
	}
	// InteractionsTable holds the schema information for the "interactions" table.
	InteractionsTable = &schema.Table{
		Name:       "interactions",
		Columns:    InteractionsColumns,
		PrimaryKey: []*schema.Column{InteractionsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "interactions_maintenance_orders_interactions",
				Columns:    []*schema.Column{InteractionsColumns[5]},
				RefColumns: []*schema.Column{MaintenanceOrdersColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "interactions_users_interactions",
				Columns:    []*schema.Column{InteractionsColumns[6]},
				RefColumns: []*schema.Column{UsersColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// MaintenanceOrdersColumns holds the columns for the "maintenance_orders" table.
	MaintenanceOrdersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "active", Type: field.TypeBool, Default: true},
		{Name: "title", Type: field.TypeString},
		{Name: "description", Type: field.TypeString},
		{Name: "status", Type: field.TypeString, Default: "pending"},
		{Name: "priority", Type: field.TypeString, Default: "medium"},
		{Name: "cancellation_reason", Type: field.TypeString, Nullable: true},
		{Name: "rejection_reason", Type: field.TypeString, Nullable: true},
		{Name: "branch_id", Type: field.TypeInt, Nullable: true},
		{Name: "equipment_id", Type: field.TypeInt, Nullable: true},
		{Name: "requester_id", Type: field.TypeInt, Nullable: true},
		{Name: "technician_id", Type: field.TypeInt, Nullable: true},
		{Name: "approver_id", Type: field.TypeInt, Nullable: true},
	}
	// MaintenanceOrdersTable holds the schema information for the "maintenance_orders" table.
	MaintenanceOrdersTable = &schema.Table{
		Name:       "maintenance_orders",
		Columns:    MaintenanceOrdersColumns,
		PrimaryKey: []*schema.Column{MaintenanceOrdersColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "maintenance_orders_branches_maintenance_orders",
				Columns:    []*schema.Column{MaintenanceOrdersColumns[10]},
				RefColumns: []*schema.Column{BranchesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "maintenance_orders_equipment_maintenance_orders",
				Columns:    []*schema.Column{MaintenanceOrdersColumns[11]},
				RefColumns: []*schema.Column{EquipmentColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "maintenance_orders_users_requested_orders",
				Columns:    []*schema.Column{MaintenanceOrdersColumns[12]},
				RefColumns: []*schema.Column{UsersColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "maintenance_orders_users_assigned_orders",
				Columns:    []*schema.Column{MaintenanceOrdersColumns[13]},
				RefColumns: []*schema.Column{UsersColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "maintenance_orders_users_approved_orders",
				Columns:    []*schema.Column{MaintenanceOrdersColumns[14]},
				RefColumns: []*schema.Column{UsersColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// UsersColumns holds the columns for the "users" table.
	UsersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "active", Type: field.TypeBool, Default: true},
		{Name: "name", Type: field.TypeString},
		{Name: "email", Type: field.TypeString, Unique: true},
		{Name: "password", Type: field.TypeString},
		{Name: "role", Type: field.TypeString},
	}
	// UsersTable holds the schema information for the "users" table.
	UsersTable = &schema.Table{
		Name:       "users",
		Columns:    UsersColumns,
		PrimaryKey: []*schema.Column{UsersColumns[0]},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		BranchesTable,
		CostItemsTable,
		EquipmentTable,
		InteractionsTable,
		MaintenanceOrdersTable,
		UsersTable,
	}
)

func init() {
	BranchesTable.ForeignKeys[0].RefTable = UsersTable
	CostItemsTable.ForeignKeys[0].RefTable = MaintenanceOrdersTable
	CostItemsTable.ForeignKeys[1].RefTable = UsersTable
	EquipmentTable.ForeignKeys[0].RefTable = BranchesTable
	InteractionsTable.ForeignKeys[0].RefTable = MaintenanceOrdersTable
	InteractionsTable.ForeignKeys[1].RefTable = UsersTable
	MaintenanceOrdersTable.ForeignKeys[0].RefTable = BranchesTable
	MaintenanceOrdersTable.ForeignKeys[1].RefTable = EquipmentTable
	MaintenanceOrdersTable.ForeignKeys[2].RefTable = UsersTable
	MaintenanceOrdersTable.ForeignKeys[3].RefTable = UsersTable
	MaintenanceOrdersTable.ForeignKeys[4].RefTable = UsersTable
}
