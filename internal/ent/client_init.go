package ent

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"sync"
	"time"

	"tradicao/internal/database"

	"entgo.io/ent/dialect"
	entsql "entgo.io/ent/dialect/sql"
	_ "github.com/lib/pq"
)

var (
	client      *Client
	once        sync.Once
	clientMutex sync.Mutex
	sqlDB       *sql.DB
)

// InitClient inicializa o cliente ENT com as configurações fornecidas
// Esta função é mantida para compatibilidade com código existente
func InitClient(driverName, dataSource string, debug bool) (*Client, error) {
	var err error
	once.Do(func() {
		clientMutex.Lock()
		defer clientMutex.Unlock()

		// Abrir conexão com o banco de dados
		driver, err := entsql.Open(driverName, dataSource)
		if err != nil {
			err = fmt.Errorf("falha ao abrir conexão com o banco de dados: %w", err)
			return
		}

		// Configurar o driver para debug se necessário
		var driverWithOptions dialect.Driver = driver
		if debug {
			driverWithOptions = dialect.Debug(driver)
		}

		// Criar cliente ENT
		client = NewClient(Driver(driverWithOptions))
		log.Println("Cliente ENT inicializado com sucesso")
	})

	if err != nil {
		return nil, err
	}

	return client, nil
}

// InitClientWithPool inicializa o cliente ENT usando o pool de conexões existente
func InitClientWithPool(debug bool) error {
	var err error

	// Obter o pool de conexões do pacote database
	sqlDB, err = database.GetConnectionPool()
	if err != nil {
		return fmt.Errorf("erro ao obter pool de conexões: %v", err)
	}

	// Configurar o pool de conexões para otimização
	sqlDB.SetMaxOpenConns(100)               // Máximo de 100 conexões abertas simultaneamente
	sqlDB.SetMaxIdleConns(20)                // Manter até 20 conexões inativas no pool
	sqlDB.SetConnMaxLifetime(time.Hour * 24) // Conexões podem ser reutilizadas por até 24 horas
	sqlDB.SetConnMaxIdleTime(time.Hour * 1)  // Conexões inativas por mais de 1 hora são fechadas

	// Criar driver para o ENT
	drv := entsql.OpenDB(dialect.Postgres, sqlDB)

	// Configurar o driver para debug se necessário
	var driverWithOptions dialect.Driver = drv
	if debug {
		driverWithOptions = dialect.Debug(drv)
		log.Println("Cliente ENT inicializado com modo debug ativado")
	}

	// Criar o cliente ENT
	client = NewClient(Driver(driverWithOptions))
	log.Println("Cliente ENT inicializado com pool de conexões")

	return nil
}

// GetClient retorna o cliente ENT inicializado
func GetClient() *Client {
	if client == nil {
		log.Fatal("Cliente ENT não inicializado. Chame InitClient primeiro.")
	}
	return client
}

// RunMigrations executa as migrações do ENT
func RunMigrations(ctx context.Context) error {
	if client == nil {
		return fmt.Errorf("cliente ENT não inicializado")
	}

	log.Println("Executando migrações do ENT...")
	err := client.Schema.Create(ctx)
	if err != nil {
		return fmt.Errorf("erro ao executar migrações: %v", err)
	}

	log.Println("Migrações do ENT executadas com sucesso")
	return nil
}

// RunMigrationsWithPool executa as migrações do ENT usando o pool de conexões existente
func RunMigrationsWithPool(ctx context.Context) error {
	return RunMigrations(ctx)
}

// Close fecha a conexão com o banco de dados
func Close() error {
	if client == nil {
		return nil
	}
	log.Println("Fechando cliente ENT")
	return client.Close()
}

// CloseClient fecha o cliente ENT
func CloseClient() error {
	return Close()
}
