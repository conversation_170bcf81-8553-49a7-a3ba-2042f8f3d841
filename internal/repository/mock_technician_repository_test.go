package repository

import (
	"testing"
	"time"
	"tradicao/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestMockTechnicianRepository(t *testing.T) {
	mockRepo := new(MockTechnicianRepository)

	t.Run("Test CreateSpecialty", func(t *testing.T) {
		specialty := &models.TechnicianSpecialty{
			Name:        "Eletricista",
			Description: "Especialista em manutenção elétrica",
		}

		mockRepo.On("CreateSpecialty", specialty).Return(nil)
		err := mockRepo.CreateSpecialty(specialty)
		assert.NoError(t, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Test GetSpecialty", func(t *testing.T) {
		specialty := &models.TechnicianSpecialty{
			ID:          1,
			Name:        "Mecânico",
			Description: "Especialista em manutenção mecânica",
		}

		mockRepo.On("GetSpecialty", uint(1)).Return(specialty, nil)
		found, err := mockRepo.GetSpecialty(1)
		assert.NoError(t, err)
		assert.Equal(t, specialty.Name, found.Name)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Test CreateBranchAssociation", func(t *testing.T) {
		association := &models.TechnicianBranch{
			TechnicianID: 1,
			BranchID:     1,
			SpecialtyID:  1,
		}

		mockRepo.On("CreateBranchAssociation", association).Return(nil)
		err := mockRepo.CreateBranchAssociation(association)
		assert.NoError(t, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Test ListBranchAssociations", func(t *testing.T) {
		associations := []models.TechnicianBranch{
			{
				TechnicianID: 1,
				BranchID:     1,
				SpecialtyID:  1,
			},
		}

		mockRepo.On("ListBranchAssociations", uint(1)).Return(associations, nil)
		result, err := mockRepo.ListBranchAssociations(1)
		assert.NoError(t, err)
		assert.Equal(t, len(associations), len(result))
		mockRepo.AssertExpectations(t)
	})

	t.Run("Test CreateMaintenanceHistory", func(t *testing.T) {
		history := &models.MaintenanceHistory{
			TechnicianID: 1,
			OrderID:      1,
			EquipmentID:  1,
			Description:  "Manutenção preventiva",
			Status:       "Concluído",
		}

		mockRepo.On("CreateMaintenanceHistory", history).Return(nil)
		err := mockRepo.CreateMaintenanceHistory(history)
		assert.NoError(t, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Test ListMaintenanceHistory", func(t *testing.T) {
		history := []models.MaintenanceHistory{
			{
				ID:           1,
				TechnicianID: 1,
				OrderID:      1,
				EquipmentID:  1,
				Description:  "Manutenção preventiva",
				Status:       "Concluído",
			},
		}

		startDate := time.Now().AddDate(0, -1, 0)
		endDate := time.Now()

		mockRepo.On("ListMaintenanceHistory", uint(1), startDate, endDate).Return(history, nil)
		result, err := mockRepo.ListMaintenanceHistory(1, startDate, endDate)
		assert.NoError(t, err)
		assert.Equal(t, len(history), len(result))
		mockRepo.AssertExpectations(t)
	})
}
