package repository

import (
	"tradicao/internal/models"
)

// IFilialRepository define a interface para operações com filiais
type IFilialRepository interface {
	GetFilialByID(id uint) (*models.Filial, error)
	GetAllFiliais() ([]models.Filial, error)
	GetFiliaisByUserID(userID int64) ([]models.Filial, error)
	CreateFilial(filial *models.Filial) error
	UpdateFilial(filial *models.Filial) error
	DeleteFilial(id uint) error
	GetFiliaisByRegion(region string) ([]models.Filial, error)
	GetActiveFiliais() ([]models.Filial, error)
	GetFilialMetrics() (*models.FilialMetrics, error)
}

// Função auxiliar para converter IStationRepository para IFilialRepository
func StationToFilialRepository(repo IStationRepository) IFilialRepository {
	return &stationToFilialAdapter{repo: repo}
}

// Adaptador para converter IStationRepository para IFilialRepository
type stationToFilialAdapter struct {
	repo IStationRepository
}

func (a *stationToFilialAdapter) GetFilialByID(id uint) (*models.Filial, error) {
	station, err := a.repo.GetStationByID(id)
	if err != nil {
		return nil, err
	}
	return models.FromStation(station), nil
}

func (a *stationToFilialAdapter) GetAllFiliais() ([]models.Filial, error) {
	stations, err := a.repo.GetAllStations()
	if err != nil {
		return nil, err
	}

	filiais := make([]models.Filial, len(stations))
	for i, station := range stations {
		filial := models.FromStation(&station)
		filiais[i] = *filial
	}

	return filiais, nil
}

func (a *stationToFilialAdapter) GetFiliaisByUserID(userID int64) ([]models.Filial, error) {
	stations, err := a.repo.GetStationsByUserID(userID)
	if err != nil {
		return nil, err
	}

	filiais := make([]models.Filial, len(stations))
	for i, station := range stations {
		filial := models.FromStation(&station)
		filiais[i] = *filial
	}

	return filiais, nil
}

func (a *stationToFilialAdapter) CreateFilial(filial *models.Filial) error {
	station := filial.ToStation()
	return a.repo.CreateStation(station)
}

func (a *stationToFilialAdapter) UpdateFilial(filial *models.Filial) error {
	station := filial.ToStation()
	return a.repo.UpdateStation(station)
}

func (a *stationToFilialAdapter) DeleteFilial(id uint) error {
	return a.repo.DeleteStation(id)
}

func (a *stationToFilialAdapter) GetFiliaisByRegion(region string) ([]models.Filial, error) {
	stations, err := a.repo.GetStationsByRegion(region)
	if err != nil {
		return nil, err
	}

	filiais := make([]models.Filial, len(stations))
	for i, station := range stations {
		filial := models.FromStation(&station)
		filiais[i] = *filial
	}

	return filiais, nil
}

func (a *stationToFilialAdapter) GetActiveFiliais() ([]models.Filial, error) {
	stations, err := a.repo.GetActiveStations()
	if err != nil {
		return nil, err
	}

	filiais := make([]models.Filial, len(stations))
	for i, station := range stations {
		filial := models.FromStation(&station)
		filiais[i] = *filial
	}

	return filiais, nil
}

func (a *stationToFilialAdapter) GetFilialMetrics() (*models.FilialMetrics, error) {
	stationMetrics, err := a.repo.GetStationMetrics()
	if err != nil {
		return nil, err
	}

	filialMetrics := &models.FilialMetrics{
		TotalFiliais:    stationMetrics.TotalStations,
		ActiveFiliais:   stationMetrics.ActiveStations,
		FiliaisByRegion: stationMetrics.StationsByRegion,
	}

	return filialMetrics, nil
}
