package repository

import (
	"tradicao/internal/models"
)

// MaintenanceRepository define a interface do repositório de manutenção
type MaintenanceRepository interface {
	// Operações básicas CRUD
	Create(order *models.MaintenanceOrder) error
	GetByID(id uint) (*models.MaintenanceOrder, error)
	Update(order *models.MaintenanceOrder) error
	Delete(id uint) error

	// Consultas
	GetAll(filters *models.MaintenanceOrderFilters) ([]models.MaintenanceOrder, int, error)
	GetAllDetailed() ([]models.MaintenanceOrderDetailed, error)
	FindByStatus(status string) ([]models.MaintenanceOrder, error)
	FindByBranchID(branchID uint) ([]models.MaintenanceOrder, error)
	FindByEquipmentID(equipmentID uint) ([]models.MaintenanceOrder, error)
	FindByServiceProviderID(providerID uint) ([]models.MaintenanceOrder, error)

	// Operações específicas
	UpdateStatus(id uint, status string, userID uint) error
	UpdateApprovalStatus(id uint, approved bool, approvedBy uint, notes string) error
	UpdatePaymentStatus(id uint, paid bool, paidBy uint, notes string) error
	AddNote(orderID uint, userID uint, content string) error
	AddMaterial(orderID uint, material *models.Material) error

	// Custos e Interações
	FindCostsByOrderID(orderID uint) ([]models.CostItem, error)
	CreateCost(cost *models.CostItem) (*models.CostItem, error)
	CreateInteraction(interaction *models.Interaction) (*models.Interaction, error)

	// Métricas e Análises
	GetMetrics() (*models.MaintenanceMetrics, error)
	GetExtendedMetrics() (*models.MaintenanceMetrics, error)

	// Métodos para busca de materiais e notas
	GetMaterialByID(materialID uint) (*models.Material, error)
	GetNoteByID(noteID uint) (*models.Note, error)
}
