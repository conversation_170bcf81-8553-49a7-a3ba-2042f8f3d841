package repository

import (
	"tradicao/internal/models"
)

// Este método foi movido para gorm_notification_repository.go

// GetAllNotificationsByUserID retorna todas as notificações de um usuário sem paginação
func (r *GormNotificationRepository) GetAllNotificationsByUserID(userID int64) ([]models.Notification, error) {
	var notifications []models.Notification
	err := r.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Find(&notifications).Error
	return notifications, err
}

// GetUnreadNotificationCount retorna o número de notificações não lidas de um usuário
func (r *GormNotificationRepository) GetUnreadNotificationCount(userID int64) (int, error) {
	return r.CountUnreadNotifications(userID)
}
