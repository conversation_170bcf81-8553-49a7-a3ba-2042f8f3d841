package repository

import (
	"context"
	"database/sql"
	"errors"
	"time"

	"tradicao/internal/models"
)

type tagRepositoryImpl struct {
	db *sql.DB
}

func NewTagRepository(db *sql.DB) TagRepository {
	return &tagRepositoryImpl{db: db}
}

func (r *tagRepositoryImpl) CreateCategory(ctx context.Context, category *models.TagCategory) error {
	query := `
		INSERT INTO tag_categories (name, description, created_at, updated_at)
		VALUES ($1, $2, $3, $4)
		RETURNING id`

	now := time.Now()
	err := r.db.QueryRowContext(ctx, query,
		category.Name,
		category.Description,
		now,
		now,
	).Scan(&category.ID)

	if err != nil {
		return err
	}

	category.CreatedAt = now
	category.UpdatedAt = now
	return nil
}

func (r *tagRepositoryImpl) GetCategory(ctx context.Context, id int64) (*models.TagCategory, error) {
	query := `
		SELECT id, name, description, created_at, updated_at
		FROM tag_categories
		WHERE id = $1`

	category := &models.TagCategory{}
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&category.ID,
		&category.Name,
		&category.Description,
		&category.CreatedAt,
		&category.UpdatedAt,
	)

	if err == sql.ErrNoRows {
		return nil, errors.New("categoria não encontrada")
	}
	if err != nil {
		return nil, err
	}

	return category, nil
}

func (r *tagRepositoryImpl) ListCategories(ctx context.Context) ([]*models.TagCategory, error) {
	query := `
		SELECT id, name, description, created_at, updated_at
		FROM tag_categories
		ORDER BY name`

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var categories []*models.TagCategory
	for rows.Next() {
		category := &models.TagCategory{}
		err := rows.Scan(
			&category.ID,
			&category.Name,
			&category.Description,
			&category.CreatedAt,
			&category.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		categories = append(categories, category)
	}

	return categories, nil
}

func (r *tagRepositoryImpl) CreateTag(ctx context.Context, tag *models.Tag) error {
	query := `
		INSERT INTO tags (name, description, category_id, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5)
		RETURNING id`

	now := time.Now()
	err := r.db.QueryRowContext(ctx, query,
		tag.Name,
		tag.Description,
		tag.CategoryID,
		now,
		now,
	).Scan(&tag.ID)

	if err != nil {
		return err
	}

	tag.CreatedAt = now
	tag.UpdatedAt = now
	return nil
}

func (r *tagRepositoryImpl) GetTag(ctx context.Context, id int64) (*models.Tag, error) {
	query := `
		SELECT id, name, description, category_id, created_at, updated_at
		FROM tags
		WHERE id = $1`

	tag := &models.Tag{}
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&tag.ID,
		&tag.Name,
		&tag.Description,
		&tag.CategoryID,
		&tag.CreatedAt,
		&tag.UpdatedAt,
	)

	if err == sql.ErrNoRows {
		return nil, errors.New("tag não encontrada")
	}
	if err != nil {
		return nil, err
	}

	return tag, nil
}

func (r *tagRepositoryImpl) ListTags(ctx context.Context, categoryID *int64) ([]*models.Tag, error) {
	var query string
	var args []interface{}

	if categoryID != nil {
		query = `
			SELECT id, name, description, category_id, created_at, updated_at
			FROM tags
			WHERE category_id = $1
			ORDER BY name`
		args = append(args, *categoryID)
	} else {
		query = `
			SELECT id, name, description, category_id, created_at, updated_at
			FROM tags
			ORDER BY name`
	}

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tags []*models.Tag
	for rows.Next() {
		tag := &models.Tag{}
		err := rows.Scan(
			&tag.ID,
			&tag.Name,
			&tag.Description,
			&tag.CategoryID,
			&tag.CreatedAt,
			&tag.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		tags = append(tags, tag)
	}

	return tags, nil
}

func (r *tagRepositoryImpl) AddTagToEquipment(ctx context.Context, equipmentID, tagID int64) error {
	query := `
		INSERT INTO equipment_tags (equipment_id, tag_id, created_at)
		VALUES ($1, $2, $3)`

	_, err := r.db.ExecContext(ctx, query, equipmentID, tagID, time.Now())
	return err
}

func (r *tagRepositoryImpl) RemoveTagFromEquipment(ctx context.Context, equipmentID, tagID int64) error {
	query := `
		DELETE FROM equipment_tags
		WHERE equipment_id = $1 AND tag_id = $2`

	_, err := r.db.ExecContext(ctx, query, equipmentID, tagID)
	return err
}

func (r *tagRepositoryImpl) GetEquipmentTags(ctx context.Context, equipmentID int64) ([]*models.Tag, error) {
	query := `
		SELECT t.id, t.name, t.description, t.category_id, t.created_at, t.updated_at
		FROM tags t
		JOIN equipment_tags et ON et.tag_id = t.id
		WHERE et.equipment_id = $1
		ORDER BY t.name`

	rows, err := r.db.QueryContext(ctx, query, equipmentID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tags []*models.Tag
	for rows.Next() {
		tag := &models.Tag{}
		err := rows.Scan(
			&tag.ID,
			&tag.Name,
			&tag.Description,
			&tag.CategoryID,
			&tag.CreatedAt,
			&tag.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		tags = append(tags, tag)
	}

	return tags, nil
}

func (r *tagRepositoryImpl) GetEquipmentsByTag(ctx context.Context, tagID int64) ([]*models.Equipment, error) {
	query := `
		SELECT e.id, e.name, e.status, e.created_at, e.updated_at
		FROM equipments e
		JOIN equipment_tags et ON et.equipment_id = e.id
		WHERE et.tag_id = $1
		ORDER BY e.name`

	rows, err := r.db.QueryContext(ctx, query, tagID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var equipments []*models.Equipment
	for rows.Next() {
		equipment := &models.Equipment{}
		err := rows.Scan(
			&equipment.ID,
			&equipment.Name,
			&equipment.Status,
			&equipment.CreatedAt,
			&equipment.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		equipments = append(equipments, equipment)
	}

	return equipments, nil
}

func (r *tagRepositoryImpl) DeleteCategory(ctx context.Context, id int64) error {
	query := `DELETE FROM tag_categories WHERE id = $1`
	_, err := r.db.ExecContext(ctx, query, id)
	return err
}

func (r *tagRepositoryImpl) UpdateCategory(ctx context.Context, category *models.TagCategory) error {
	query := `
		UPDATE tag_categories 
		SET name = $1, description = $2, updated_at = $3
		WHERE id = $4`

	now := time.Now()
	_, err := r.db.ExecContext(ctx, query,
		category.Name,
		category.Description,
		now,
		category.ID,
	)
	return err
}

func (r *tagRepositoryImpl) DeleteTag(ctx context.Context, id int64) error {
	query := `DELETE FROM tags WHERE id = $1`
	_, err := r.db.ExecContext(ctx, query, id)
	return err
}

func (r *tagRepositoryImpl) UpdateTag(ctx context.Context, tag *models.Tag) error {
	query := `
		UPDATE tags 
		SET name = $1, description = $2, category_id = $3, updated_at = $4
		WHERE id = $5`

	now := time.Now()
	_, err := r.db.ExecContext(ctx, query,
		tag.Name,
		tag.Description,
		tag.CategoryID,
		now,
		tag.ID,
	)
	return err
}
