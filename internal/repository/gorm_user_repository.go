package repository

import (
	"errors"
	"fmt"
	"time"

	"tradicao/internal/database"
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// GormUserRepository representa um repositório de usuários usando GORM
type GormUserRepository struct {
	db *gorm.DB
}

// NewGormUserRepository cria uma nova instância do repositório de usuários
func NewGormUserRepository() *GormUserRepository {
	db := database.GetGormDB()
	if db == nil {
		var err error
		db, err = database.InitGorm()
		if err != nil {
			return nil
		}
	}
	return &GormUserRepository{db: db}
}

// Create cria um novo usuário
func (r *GormUserRepository) Create(user models.User) (models.User, error) {
	if err := r.db.Create(&user).Error; err != nil {
		return models.User{}, err
	}
	return user, nil
}

// FindByID busca um usuário pelo ID (método legado)
func (r *GormUserRepository) FindByID(id uint) (models.User, error) {
	user, err := r.GetUserByID(int64(id))
	if err != nil {
		return models.User{}, err
	}
	return *user, nil
}

// FindByEmail busca um usuário pelo email
func (r *GormUserRepository) FindByEmail(email string) (models.User, error) {
	var user models.User
	if err := r.db.Where("email = ?", email).First(&user).Error; err != nil {
		return models.User{}, err
	}
	return user, nil
}

// FindByUsername busca um usuário pelo nome de usuário
func (r *GormUserRepository) FindByUsername(username string) (models.User, error) {
	var user models.User
	if err := r.db.Where("username = ?", username).First(&user).Error; err != nil {
		return models.User{}, err
	}
	return user, nil
}

// Update atualiza um usuário existente
func (r *GormUserRepository) Update(user models.User) (models.User, error) {
	if user.ID == 0 {
		return models.User{}, errors.New("ID do usuário não especificado")
	}

	if err := r.db.Save(&user).Error; err != nil {
		return models.User{}, err
	}
	return user, nil
}

// Delete remove um usuário (soft delete)
func (r *GormUserRepository) Delete(id uint) error {
	return r.db.Delete(&models.User{}, id).Error
}

// FindAll busca todos os usuários
func (r *GormUserRepository) FindAll() ([]models.User, error) {
	var users []models.User
	if err := r.db.Find(&users).Error; err != nil {
		return nil, err
	}
	return users, nil
}

// FindByRole busca usuários por função
func (r *GormUserRepository) FindByRole(role string) ([]models.User, error) {
	var users []models.User
	if err := r.db.Where("role = ?", role).Find(&users).Error; err != nil {
		return nil, err
	}
	return users, nil
}

// UpdateLastLogin atualiza a data do último login do usuário
func (r *GormUserRepository) UpdateLastLogin(id uint) error {
	return r.db.Model(&models.User{}).Where("id = ?", id).Update("last_login", time.Now()).Error
}

// UpdatePassword atualiza a senha do usuário
func (r *GormUserRepository) UpdatePassword(id uint, password string) error {
	fmt.Printf("[ALTERAR SENHA] Repositório: Atualizando senha para usuário ID: %d\n", id)

	// Usar um ponteiro para time.Time para ser consistente com o modelo
	now := time.Now()

	result := r.db.Model(&models.User{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"password":             password,
			"last_password_change": &now,
		})

	if result.Error != nil {
		fmt.Printf("[ALTERAR SENHA] Repositório: Erro ao atualizar senha: %v\n", result.Error)
		return result.Error
	}

	if result.RowsAffected == 0 {
		fmt.Printf("[ALTERAR SENHA] Repositório: Nenhuma linha afetada para ID: %d\n", id)
		return fmt.Errorf("nenhum usuário encontrado com ID: %d", id)
	}

	fmt.Printf("[ALTERAR SENHA] Repositório: Senha atualizada com sucesso para ID: %d\n", id)
	return nil
}

// IncrementFailedAttempts incrementa o contador de tentativas falhas de login
func (r *GormUserRepository) IncrementFailedAttempts(id uint) (int, error) {
	var user models.User
	if err := r.db.First(&user, id).Error; err != nil {
		return 0, err
	}

	user.FailedAttempts++
	if err := r.db.Save(&user).Error; err != nil {
		return 0, err
	}

	return user.FailedAttempts, nil
}

// ResetFailedAttempts reseta o contador de tentativas falhas de login
func (r *GormUserRepository) ResetFailedAttempts(id uint) error {
	return r.db.Model(&models.User{}).Where("id = ?", id).Update("failed_attempts", 0).Error
}

// BlockUser bloqueia ou desbloqueia um usuário
func (r *GormUserRepository) BlockUser(id uint, blocked bool) error {
	return r.db.Model(&models.User{}).Where("id = ?", id).Update("blocked", blocked).Error
}

// Search busca usuários por termo
func (r *GormUserRepository) Search(term string) ([]models.User, error) {
	var users []models.User
	if err := r.db.Where("name LIKE ? OR email LIKE ?",
		"%"+term+"%", "%"+term+"%").Find(&users).Error; err != nil {
		return nil, err
	}
	return users, nil
}

// Count retorna o número total de usuários
func (r *GormUserRepository) Count() (int64, error) {
	var count int64
	if err := r.db.Model(&models.User{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// GetDB retorna a instância do GORM DB para operações personalizadas
func (r *GormUserRepository) GetDB() *gorm.DB {
	return r.db
}

// CreateAuditLog cria um registro de auditoria
func (r *GormUserRepository) CreateAuditLog(log models.AuditLog) error {
	return r.db.Create(&log).Error
}

// GetSecurityPolicy obtém a política de segurança atual
func (r *GormUserRepository) GetSecurityPolicy() (models.SecurityPolicy, error) {
	var policy models.SecurityPolicy
	err := r.db.First(&policy).Error

	// Se não existir, cria a política padrão
	if errors.Is(err, gorm.ErrRecordNotFound) {
		policy = models.SecurityPolicy{
			PasswordMinLength:          8,
			PasswordRequireUppercase:   true,
			PasswordRequireNumber:      true,
			PasswordRequireSpecialChar: true,
			PasswordExpiryDays:         90,
			MaxLoginAttempts:           5,
			LockoutDurationMinutes:     30,
			Enable2FA:                  false,
			SessionTimeoutMinutes:      60,
		}
		r.db.Create(&policy)
		return policy, nil
	}

	return policy, err
}

// EnableTOTP ativa a autenticação de dois fatores para um usuário
func (r *GormUserRepository) EnableTOTP(userID uint, secret string) error {
	return r.db.Model(&models.User{}).Where("id = ?", userID).Updates(map[string]interface{}{
		"totp_secret":  secret,
		"totp_enabled": true,
	}).Error
}

// DisableTOTP desativa a autenticação de dois fatores para um usuário
func (r *GormUserRepository) DisableTOTP(userID uint) error {
	return r.db.Model(&models.User{}).Where("id = ?", userID).Updates(map[string]interface{}{
		"totp_secret":  "",
		"totp_enabled": false,
	}).Error
}

// SetTOTPSecret define o segredo TOTP para um usuário (sem ativar)
func (r *GormUserRepository) SetTOTPSecret(userID uint, secret string) error {
	return r.db.Model(&models.User{}).Where("id = ?", userID).Update("totp_secret", secret).Error
}

// GetTOTPSecret obtém o segredo TOTP de um usuário e seu status
func (r *GormUserRepository) GetTOTPSecret(userID uint) (string, bool, error) {
	var user models.User
	if err := r.db.Select("totp_secret", "totp_enabled").First(&user, userID).Error; err != nil {
		return "", false, err
	}
	return user.TOTPSecret, user.TOTPEnabled, nil
}

// UpdateLoginAttempts atualiza o contador de tentativas de login
func (r *GormUserRepository) UpdateLoginAttempts(userID uint, failed bool) error {
	var user models.User
	if err := r.db.First(&user, userID).Error; err != nil {
		return err
	}

	// Se o login foi bem-sucedido, reseta o contador
	if !failed {
		user.FailedAttempts = 0
		return r.db.Save(&user).Error
	}

	// Se falhou, incrementa o contador
	user.FailedAttempts++

	// Verifica se precisa bloquear o usuário
	policy, err := r.GetSecurityPolicy()
	if err == nil && user.FailedAttempts >= policy.MaxLoginAttempts {
		user.Blocked = true
	}

	return r.db.Save(&user).Error
}

// AddPasswordToHistory adiciona um hash de senha ao histórico de senhas do usuário
func (r *GormUserRepository) AddPasswordToHistory(userID uint, passwordHash string) error {
	// Implementação simplificada - em um sistema real, isso seria salvo em uma tabela de histórico
	return nil
}

// IsPasswordInHistory verifica se uma senha está no histórico do usuário
func (r *GormUserRepository) IsPasswordInHistory(userID uint, passwordHash string) (bool, error) {
	// Implementação simplificada - em um sistema real, isso verificaria uma tabela de histórico
	return false, nil
}

// DisableForcePasswordChange desativa a flag de obrigatoriedade de troca de senha
func (r *GormUserRepository) DisableForcePasswordChange(userID uint) error {
	return r.db.Model(&models.User{}).Where("id = ?", userID).Update("force_password_change", false).Error
}

// EnableForcePasswordChange ativa a flag de obrigatoriedade de troca de senha
func (r *GormUserRepository) EnableForcePasswordChange(userID uint) error {
	return r.db.Model(&models.User{}).Where("id = ?", userID).Update("force_password_change", true).Error
}

// GetUserByID obtém um usuário pelo ID
func (r *GormUserRepository) GetUserByID(id int64) (*models.User, error) {
	var user models.User
	err := r.db.Where("id = ?", id).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetUserByUID obtém um usuário pelo ID (método auxiliar para compatibilidade)
func (r *GormUserRepository) GetUserByUID(id uint) (*models.User, error) {
	return r.GetUserByID(int64(id))
}

// UpdateAvatar atualiza a URL do avatar de um usuário
func (r *GormUserRepository) UpdateAvatar(userID uint, avatarURL string) error {
	return r.db.Model(&models.User{}).Where("id = ?", userID).Update("avatar_url", avatarURL).Error
}
