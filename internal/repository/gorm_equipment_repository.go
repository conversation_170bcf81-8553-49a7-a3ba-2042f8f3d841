package repository

import (
	"errors"
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// GormEquipmentRepository implementa a interface IEquipmentRepository usando GORM
type GormEquipmentRepository struct {
	db *gorm.DB
}

// NewGormEquipmentRepository cria uma nova instância do repositório de equipamentos
func NewGormEquipmentRepository(db *gorm.DB) *GormEquipmentRepository {
	return &GormEquipmentRepository{
		db: db,
	}
}

// GetAllEquipment retorna todos os equipamentos
func (r *GormEquipmentRepository) GetAllEquipment() ([]models.Equipment, error) {
	var equipments []models.Equipment
	result := r.db.Find(&equipments)
	return equipments, result.Error
}

// GetEquipmentByID retorna um equipamento pelo ID
func (r *GormEquipmentRepository) GetEquipmentByID(id int) (*models.Equipment, error) {
	var equipment models.Equipment
	result := r.db.First(&equipment, id)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil // Retorna nil sem erro quando não encontra
		}
		return nil, result.Error
	}

	return &equipment, nil
}

// GetEquipmentsByType retorna equipamentos filtrados por tipo
func (r *GormEquipmentRepository) GetEquipmentsByType(equipmentType string) ([]models.Equipment, error) {
	var equipments []models.Equipment
	result := r.db.Where("type = ?", equipmentType).Find(&equipments)
	return equipments, result.Error
}

// CreateEquipment cria um novo equipamento
func (r *GormEquipmentRepository) CreateEquipment(equipment *models.Equipment) error {
	return r.db.Create(equipment).Error
}

// UpdateEquipment atualiza um equipamento existente
func (r *GormEquipmentRepository) UpdateEquipment(equipment *models.Equipment) error {
	return r.db.Save(equipment).Error
}

// DeleteEquipment remove um equipamento
func (r *GormEquipmentRepository) DeleteEquipment(id int) error {
	return r.db.Delete(&models.Equipment{}, id).Error
}

// EquipmentExists verifica se um equipamento existe
func (r *GormEquipmentRepository) EquipmentExists(id int) (bool, error) {
	var count int64
	result := r.db.Model(&models.Equipment{}).Where("id = ?", id).Count(&count)
	return count > 0, result.Error
}
