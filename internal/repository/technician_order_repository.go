package repository

import (
	"errors"
	"log"
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// TechnicianOrderRepositoryImpl implementa a interface TechnicianOrderRepository
type TechnicianOrderRepositoryImpl struct {
	db *gorm.DB
}

// NewTechnicianOrderRepository cria uma nova instância do repositório
func NewTechnicianOrderRepository(db *gorm.DB) models.TechnicianOrderRepository {
	return &TechnicianOrderRepositoryImpl{db: db}
}

// Create cria um novo relacionamento entre técnico e ordem
func (r *TechnicianOrderRepositoryImpl) Create(technicianID, orderID, createdBy uint, notes string) error {
	// Verificar se o técnico existe
	var technician models.User
	if err := r.db.First(&technician, technicianID).Error; err != nil {
		return errors.New("técnico não encontrado")
	}

	// Verificar se a ordem existe
	var order models.MaintenanceOrder
	if err := r.db.First(&order, orderID).Error; err != nil {
		return errors.New("ordem não encontrada")
	}

	// Verificar se o relacionamento já existe
	exists, err := r.Exists(technicianID, orderID)
	if err != nil {
		return err
	}
	if exists {
		return errors.New("relacionamento já existe")
	}

	// Criar o relacionamento
	techOrder := models.TechnicianOrder{
		TechnicianID: technicianID,
		OrderID:      orderID,
		CreatedBy:    &createdBy,
		Notes:        notes,
	}

	if err := r.db.Create(&techOrder).Error; err != nil {
		log.Printf("Erro ao criar relacionamento entre técnico %d e ordem %d: %v", technicianID, orderID, err)
		return err
	}

	log.Printf("Relacionamento criado com sucesso: Técnico %d -> Ordem %d", technicianID, orderID)
	return nil
}

// Delete remove um relacionamento entre técnico e ordem
func (r *TechnicianOrderRepositoryImpl) Delete(technicianID, orderID uint) error {
	result := r.db.Where("technician_id = ? AND order_id = ?", technicianID, orderID).Delete(&models.TechnicianOrder{})
	if result.Error != nil {
		log.Printf("Erro ao remover relacionamento entre técnico %d e ordem %d: %v", technicianID, orderID, result.Error)
		return result.Error
	}

	if result.RowsAffected == 0 {
		return errors.New("relacionamento não encontrado")
	}

	log.Printf("Relacionamento removido com sucesso: Técnico %d -> Ordem %d", technicianID, orderID)
	return nil
}

// GetByTechnician retorna todas as ordens associadas a um técnico
func (r *TechnicianOrderRepositoryImpl) GetByTechnician(technicianID uint) ([]models.TechnicianOrder, error) {
	var techOrders []models.TechnicianOrder
	if err := r.db.Where("technician_id = ?", technicianID).Find(&techOrders).Error; err != nil {
		log.Printf("Erro ao buscar ordens do técnico %d: %v", technicianID, err)
		return nil, err
	}

	return techOrders, nil
}

// GetByOrder retorna todos os técnicos associados a uma ordem
func (r *TechnicianOrderRepositoryImpl) GetByOrder(orderID uint) ([]models.TechnicianOrder, error) {
	var techOrders []models.TechnicianOrder
	if err := r.db.Where("order_id = ?", orderID).Find(&techOrders).Error; err != nil {
		log.Printf("Erro ao buscar técnicos da ordem %d: %v", orderID, err)
		return nil, err
	}

	return techOrders, nil
}

// Exists verifica se existe um relacionamento entre técnico e ordem
func (r *TechnicianOrderRepositoryImpl) Exists(technicianID, orderID uint) (bool, error) {
	var count int64
	if err := r.db.Model(&models.TechnicianOrder{}).
		Where("technician_id = ? AND order_id = ?", technicianID, orderID).
		Count(&count).Error; err != nil {
		log.Printf("Erro ao verificar existência de relacionamento: %v", err)
		return false, err
	}

	return count > 0, nil
}

// GetAll retorna todos os relacionamentos
func (r *TechnicianOrderRepositoryImpl) GetAll() ([]models.TechnicianOrder, error) {
	var techOrders []models.TechnicianOrder
	if err := r.db.Find(&techOrders).Error; err != nil {
		log.Printf("Erro ao buscar todos os relacionamentos: %v", err)
		return nil, err
	}

	return techOrders, nil
}
