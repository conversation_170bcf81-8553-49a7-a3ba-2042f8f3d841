package repository

import (
	"errors"
	"time"
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// BranchRepository é o repositório para operações relacionadas a filiais
type BranchRepository struct {
	DB *gorm.DB
}

// NewBranchRepository cria uma nova instância do repositório de filiais
func NewBranchRepository(db *gorm.DB) *BranchRepository {
	return &BranchRepository{DB: db}
}

// GetAllBranches retorna todas as filiais com paginação e filtros
func (r *BranchRepository) GetAllBranches(page, limit int, search string, isActive *bool) ([]models.Branch, int64, error) {
	var branches []models.Branch
	var total int64

	query := r.DB.Model(&models.Branch{})

	if search != "" {
		query = query.Where("name LIKE ? OR city LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if isActive != nil {
		query = query.Where("is_active = ?", *isActive)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * limit
	if err := query.Offset(offset).Limit(limit).Find(&branches).Error; err != nil {
		return nil, 0, err
	}

	return branches, total, nil
}

// GetBranchByID retorna uma filial pelo ID
func (r *BranchRepository) GetBranchByID(id uint) (*models.Branch, error) {
	var branch models.Branch
	if err := r.DB.First(&branch, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("filial não encontrada")
		}
		return nil, err
	}
	return &branch, nil
}

// GetBranchesByUserID retorna filiais associadas a um usuário
func (r *BranchRepository) GetBranchesByUserID(userID int64) ([]models.Branch, error) {
	var branches []models.Branch
	err := r.DB.Joins("JOIN user_branches ON branches.id = user_branches.branch_id").
		Where("user_branches.user_id = ?", userID).
		Find(&branches).Error
	return branches, err
}

// GetBranchesByTechnicianID retorna filiais onde um técnico está associado a ordens
func (r *BranchRepository) GetBranchesByTechnicianID(technicianID int64) ([]models.Branch, error) {
	var branches []models.Branch
	err := r.DB.Joins("JOIN maintenance_orders ON branches.id = maintenance_orders.branch_id").
		Where("maintenance_orders.technician_id = ?", technicianID).
		Group("branches.id").
		Find(&branches).Error
	return branches, err
}

// CreateBranch cria uma nova filial
func (r *BranchRepository) CreateBranch(branch *models.Branch) error {
	return r.DB.Create(branch).Error
}

// UpdateBranch atualiza uma filial existente
func (r *BranchRepository) UpdateBranch(branch *models.Branch) error {
	return r.DB.Save(branch).Error
}

// DeleteBranch remove uma filial
func (r *BranchRepository) DeleteBranch(id uint) error {
	return r.DB.Delete(&models.Branch{}, id).Error
}

// BranchExists verifica se uma filial existe
func (r *BranchRepository) BranchExists(id uint) (bool, error) {
	var count int64
	err := r.DB.Model(&models.Branch{}).Where("id = ?", id).Count(&count).Error
	return count > 0, err
}

// LinkProvider vincula um prestador a uma filial
func (r *BranchRepository) LinkProvider(branchID, providerID uint) error {
	link := models.BranchProviderLink{
		BranchID:          branchID,
		ServiceProviderID: providerID,
	}
	return r.DB.Create(&link).Error
}

// UnlinkProvider remove o vínculo entre um prestador e uma filial
func (r *BranchRepository) UnlinkProvider(branchID, providerID uint) error {
	return r.DB.Where("branch_id = ? AND service_provider_id = ?", branchID, providerID).
		Delete(&models.BranchProviderLink{}).Error
}

// GetLinkedProviders retorna os IDs dos prestadores vinculados a uma filial
func (r *BranchRepository) GetLinkedProviders(branchID uint) ([]uint, error) {
	var links []models.BranchProviderLink
	if err := r.DB.Where("branch_id = ?", branchID).Find(&links).Error; err != nil {
		return nil, err
	}

	providerIDs := make([]uint, len(links))
	for i, link := range links {
		providerIDs[i] = link.ServiceProviderID
	}
	return providerIDs, nil
}

// GenerateAuthToken gera um token de autenticação para uma filial
func (r *BranchRepository) GenerateAuthToken(branchID uint) (string, error) {
	token, err := models.GenerateToken(branchID)
	if err != nil {
		return "", err
	}

	if err := r.DB.Create(token).Error; err != nil {
		return "", err
	}

	return token.Token, nil
}

// ValidateAuthToken valida um token de autenticação de uma filial
func (r *BranchRepository) ValidateAuthToken(token string) (uint, error) {
	var authToken models.BranchAuthToken
	if err := r.DB.Where("token = ?", token).First(&authToken).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, errors.New("token inválido")
		}
		return 0, err
	}

	if authToken.IsUsed {
		return 0, errors.New("token já utilizado")
	}

	if authToken.ExpiresAt.Before(time.Now()) {
		return 0, errors.New("token expirado")
	}

	return authToken.BranchID, nil
}

// UseAuthToken utiliza um token de autenticação
func (r *BranchRepository) UseAuthToken(token string) error {
	result := r.DB.Model(&models.BranchAuthToken{}).
		Where("token = ?", token).
		Update("is_used", true)

	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return errors.New("token não encontrado")
	}

	return nil
}
