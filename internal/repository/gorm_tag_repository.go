package repository

import (
	"time"
	"tradicao/internal/models"

	"gorm.io/gorm"
)

type GormTagRepository struct {
	db *gorm.DB
}

func NewGormTagRepository(db *gorm.DB) *GormTagRepository {
	return &GormTagRepository{db: db}
}

// Implementação dos métodos da interface TagRepository

// Categoria
func (r *GormTagRepository) CreateCategory(category *models.TagCategory) error {
	category.CreatedAt = time.Now()
	category.UpdatedAt = time.Now()
	return r.db.Create(category).Error
}

func (r *GormTagRepository) GetCategory(id int64) (*models.TagCategory, error) {
	var category models.TagCategory
	err := r.db.First(&category, id).Error
	if err != nil {
		return nil, err
	}
	return &category, nil
}

func (r *GormTagRepository) ListCategories() ([]models.TagCategory, error) {
	var categories []models.TagCategory
	err := r.db.Find(&categories).Error
	return categories, err
}

func (r *GormTagRepository) UpdateCategory(category *models.TagCategory) error {
	category.UpdatedAt = time.Now()
	return r.db.Save(category).Error
}

func (r *GormTagRepository) DeleteCategory(id int64) error {
	return r.db.Delete(&models.TagCategory{}, id).Error
}

// Tag
func (r *GormTagRepository) CreateTag(tag *models.Tag) error {
	tag.CreatedAt = time.Now()
	tag.UpdatedAt = time.Now()
	return r.db.Create(tag).Error
}

func (r *GormTagRepository) GetTag(id int64) (*models.Tag, error) {
	var tag models.Tag
	err := r.db.Preload("Category").First(&tag, id).Error
	if err != nil {
		return nil, err
	}
	return &tag, nil
}

func (r *GormTagRepository) ListTags(categoryID *int64) ([]models.Tag, error) {
	var tags []models.Tag
	query := r.db.Preload("Category")
	if categoryID != nil {
		query = query.Where("category_id = ?", *categoryID)
	}
	err := query.Find(&tags).Error
	return tags, err
}

func (r *GormTagRepository) UpdateTag(tag *models.Tag) error {
	tag.UpdatedAt = time.Now()
	return r.db.Save(tag).Error
}

func (r *GormTagRepository) DeleteTag(id int64) error {
	return r.db.Delete(&models.Tag{}, id).Error
}

// Equipamento-Tag
func (r *GormTagRepository) AddTagToEquipment(equipmentID uint, tagID int64) error {
	equipmentTag := models.EquipmentTag{
		EquipmentID: int64(equipmentID),
		TagID:       tagID,
		CreatedAt:   time.Now(),
	}
	return r.db.Create(&equipmentTag).Error
}

func (r *GormTagRepository) RemoveTagFromEquipment(equipmentID uint, tagID int64) error {
	return r.db.Where("equipment_id = ? AND tag_id = ?", equipmentID, tagID).
		Delete(&models.EquipmentTag{}).Error
}

func (r *GormTagRepository) GetEquipmentTags(equipmentID uint) ([]models.Tag, error) {
	var tags []models.Tag
	err := r.db.Joins("JOIN equipment_tags ON equipment_tags.tag_id = tags.id").
		Where("equipment_tags.equipment_id = ?", equipmentID).
		Preload("Category").
		Find(&tags).Error
	return tags, err
}

func (r *GormTagRepository) GetEquipmentsByTag(tagID int64) ([]models.Equipment, error) {
	var equipments []models.Equipment
	err := r.db.Joins("JOIN equipment_tags ON equipment_tags.equipment_id = equipment.id").
		Where("equipment_tags.tag_id = ?", tagID).
		Find(&equipments).Error
	return equipments, err
}
