package repository

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// MaintenanceOrderGenericRepository implementa GenericRepository para MaintenanceOrder
// Esta implementação suporta PostgreSQL
type MaintenanceOrderGenericRepository struct {
	gormDB     *gorm.DB
	sqlDB      *sql.DB
	collection string
	converter  IDConverter
}

// NewMaintenanceOrderGenericRepository cria um novo repositório genérico para MaintenanceOrder
func NewMaintenanceOrderGenericRepository(
	gormDB *gorm.DB,
	sqlDB *sql.DB,
	collection string,
) GenericRepository[models.MaintenanceOrder] {
	return &MaintenanceOrderGenericRepository{
		gormDB:     gormDB,
		sqlDB:      sqlDB,
		collection: collection,
		converter:  DefaultIDConverter{},
	}
}

// GetByID retorna uma ordem pelo ID
func (r *MaintenanceOrderGenericRepository) GetByID(ctx context.Context, id models.ID) (*models.MaintenanceOrder, error) {
	// Verificar se temos GORM disponível
	if r.gormDB != nil {
		// Tentar obter o ID como NumericID
		numericID, err := r.converter.ToNumericID(id)
		if err != nil {
			return nil, err
		}

		// Buscar no PostgreSQL via GORM
		var order models.MaintenanceOrder
		result := r.gormDB.First(&order, numericID.Value)
		if result.Error == nil {
			return &order, nil
		}
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, ErrNotFound
		}
		return nil, result.Error
	}

	// Verificar se temos SQL disponível
	if r.sqlDB != nil {
		// Tentar obter o ID como NumericID
		numericID, err := r.converter.ToNumericID(id)
		if err != nil {
			return nil, err
		}

		// Buscar no PostgreSQL via SQL
		query := fmt.Sprintf("SELECT id, branch_id, equipment_id, assigned_provider_id, title, problem, status, priority, actual_cost, created_at, updated_at FROM %s WHERE id = $1", r.collection)
		row := r.sqlDB.QueryRowContext(ctx, query, numericID.Value)

		var order models.MaintenanceOrder
		err = row.Scan(
			&order.ID,
			&order.BranchID,
			&order.EquipmentID,
			&order.ServiceProviderID,  // Changed from TechnicianID
			&order.Number,             // Changed from Title
			&order.Problem,            // Changed from Description
			&order.Status,
			&order.Priority,
			&order.ActualCost,        // Changed from Cost
			&order.CreatedAt,
			&order.UpdatedAt,
		)
		if err == nil {
			return &order, nil
		}
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrNotFound
		}
		return nil, err
	}

	return nil, ErrNotFound
}

// Create cria uma nova ordem
func (r *MaintenanceOrderGenericRepository) Create(ctx context.Context, order *models.MaintenanceOrder) error {
	// Implementação para GORM (PostgreSQL)
	if r.gormDB != nil {
		result := r.gormDB.Create(order)
		if result.Error == nil {
			return nil
		}
		return result.Error
	}

	return ErrDatabaseError
}

// Update atualiza uma ordem existente
func (r *MaintenanceOrderGenericRepository) Update(ctx context.Context, id models.ID, order *models.MaintenanceOrder) error {
	// Implementação para GORM (PostgreSQL)
	if r.gormDB != nil {
		numericID, err := r.converter.ToNumericID(id)
		if err != nil {
			return err
		}

		result := r.gormDB.Model(&models.MaintenanceOrder{}).Where("id = ?", numericID.Value).Updates(order)
		if result.Error == nil {
			return nil
		}
		return result.Error
	}

	return ErrDatabaseError
}

// Delete remove uma ordem
func (r *MaintenanceOrderGenericRepository) Delete(ctx context.Context, id models.ID) error {
	// Implementação para GORM (PostgreSQL)
	if r.gormDB != nil {
		numericID, err := r.converter.ToNumericID(id)
		if err != nil {
			return err
		}

		result := r.gormDB.Delete(&models.MaintenanceOrder{}, numericID.Value)
		if result.Error == nil {
			return nil
		}
		return result.Error
	}

	return ErrDatabaseError
}

// List retorna uma lista de ordens com paginação
func (r *MaintenanceOrderGenericRepository) List(ctx context.Context, offset, limit int) ([]models.MaintenanceOrder, int, error) {
	var orders []models.MaintenanceOrder
	var total int64

	// Implementação para GORM (PostgreSQL)
	if r.gormDB != nil {
		result := r.gormDB.Model(&models.MaintenanceOrder{}).Count(&total)
		if result.Error != nil {
			return nil, 0, result.Error
		}

		result = r.gormDB.Offset(offset).Limit(limit).Find(&orders)
		if result.Error != nil {
			return nil, 0, result.Error
		}

		return orders, int(total), nil
	}

	return nil, 0, ErrDatabaseError
}
