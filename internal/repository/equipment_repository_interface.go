package repository

import (
	"context"
	"tradicao/internal/models"
)

// IEquipmentRepository define a interface para o repositório de equipamentos
type IEquipmentRepository interface {
	// List retorna todos os equipamentos
	List(ctx context.Context) ([]models.Equipment, error)

	// FindByID retorna um equipamento pelo ID
	FindByID(ctx context.Context, id uint) (*models.Equipment, error)

	// FindByType retorna equipamentos filtrados por tipo
	FindByType(ctx context.Context, equipmentType string) ([]models.Equipment, error)

	// Create cria um novo equipamento
	Create(ctx context.Context, equipment *models.Equipment) error

	// Update atualiza um equipamento existente
	Update(ctx context.Context, equipment *models.Equipment) error

	// Delete remove um equipamento
	Delete(ctx context.Context, id uint) error

	// Exists verifica se um equipamento existe
	Exists(ctx context.Context, id uint) (bool, error)

	// FindByBranch retorna equipamentos de uma filial específica
	FindByBranch(ctx context.Context, branchID uint) ([]models.Equipment, error)

	// FindNeedingMaintenance retorna equipamentos que precisam de manutenção
	FindNeedingMaintenance(ctx context.Context) ([]models.Equipment, error)
}
