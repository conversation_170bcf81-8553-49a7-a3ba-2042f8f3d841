package repository

import (
	"context"
	"fmt"
	"testing"
	"time"

	"tradicao/internal/models"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// setupTestDBMaintenance cria um banco de dados de teste em memória com SQLite para manutenção
func setupTestDBMaintenance(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open("file::memory:?cache=shared"), &gorm.Config{})
	require.NoError(t, err)

	// Cria a tabela maintenance_orders
	err = db.AutoMigrate(&models.MaintenanceOrder{})
	require.NoError(t, err)

	return db
}
func TestMaintenanceOrderRepository_Create(t *testing.T) {
	db := setupTestDBMaintenance(t)
	repo := NewMaintenanceOrderRepository(db)

	// Cria uma ordem de manutenção para o teste
	order := &models.MaintenanceOrder{
		BranchID:        1,
		EquipmentID:     1,
		Description:     "Teste de manutenção",
		Status:          models.StatusPending,
		Priority:        models.PriorityHigh,
		CreatedByUserID: 1,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// Testa a criação
	err := repo.Create(context.Background(), order)
	assert.NoError(t, err)
	assert.NotEqual(t, uint(0), order.ID, "ID deve ser preenchido após a criação")
}
func TestMaintenanceOrderRepository_GetByID(t *testing.T) {
	db := setupTestDBMaintenance(t)
	repo := NewMaintenanceOrderRepository(db)

	// Insere uma ordem para teste
	order := models.MaintenanceOrder{
		BranchID:        1,
		EquipmentID:     1,
		Description:     "Teste GetByID",
		Status:          models.StatusPending,
		Priority:        models.PriorityMedium,
		CreatedByUserID: 1,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
	db.Create(&order)

	// Testa a busca
	found, err := repo.GetByID(context.Background(), order.GetIDAsUint())
	assert.NoError(t, err)
	assert.Equal(t, order.ID, found.ID)
	assert.Equal(t, order.Description, found.Description)

	// Testa ID não existente
	_, err = repo.GetByID(context.Background(), 999)
	assert.Error(t, err)
}
func TestMaintenanceOrderRepository_GetAll(t *testing.T) {
	db := setupTestDBMaintenance(t)
	repo := NewMaintenanceOrderRepository(db)

	// Insere várias ordens para teste
	orders := []models.MaintenanceOrder{
		{
			BranchID:        1,
			EquipmentID:     1,
			Description:     "Ordem 1",
			Status:          models.StatusPending,
			Priority:        models.PriorityLow,
			CreatedByUserID: 1,
		},
		{
			BranchID:        1,
			EquipmentID:     2,
			Description:     "Ordem 2",
			Status:          models.StatusInProgress,
			Priority:        models.PriorityHigh,
			CreatedByUserID: 2,
		},
		{
			BranchID:        2,
			EquipmentID:     1,
			Description:     "Ordem 3",
			Status:          models.StatusCompleted,
			Priority:        models.PriorityMedium,
			CreatedByUserID: 1,
		},
	}

	for i := range orders {
		db.Create(&orders[i])
	}

	// Testa a busca de todos
	foundOrders, total, err := repo.GetAll(context.Background(), map[string]interface{}{}, 0, "", 0, 10)
	assert.NoError(t, err)
	assert.Equal(t, 3, len(foundOrders), "Deve encontrar 3 ordens")
	assert.Equal(t, int64(3), total)

	// Testa a paginação
	foundOrders, total, err = repo.GetAll(context.Background(), map[string]interface{}{}, 0, "", 0, 2)
	assert.NoError(t, err)
	assert.Equal(t, 2, len(foundOrders), "Deve encontrar 2 ordens com limit=2")
	assert.Equal(t, int64(3), total)

	// Testa o filtro
	filter := map[string]interface{}{"status": "pending"}
	foundOrders, total, err = repo.GetAll(context.Background(), filter, 0, "", 0, 10)
	assert.NoError(t, err)
	assert.Equal(t, 1, len(foundOrders), "Deve encontrar 1 ordem com status=pending")
	assert.Equal(t, int64(1), total)
}
func TestMaintenanceOrderRepository_Update(t *testing.T) {
	db := setupTestDBMaintenance(t)
	repo := NewMaintenanceOrderRepository(db)

	// Insere uma ordem para teste
	order := models.MaintenanceOrder{
		BranchID:        1,
		EquipmentID:     1,
		Description:     "Teste Update",
		Status:          models.StatusPending,
		Priority:        models.PriorityMedium,
		CreatedByUserID: 1,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
	db.Create(&order)

	// Atualiza a ordem
	order.Description = "Teste Update Modificado"
	order.Status = models.StatusInProgress

	err := repo.Update(context.Background(), &order)
	assert.NoError(t, err)

	// Verifica se foi atualizado
	var found models.MaintenanceOrder
	db.First(&found, order.ID)
	assert.Equal(t, "Teste Update Modificado", found.Description)
	assert.Equal(t, models.StatusInProgress, found.Status)
}
func TestMaintenanceOrderRepository_Delete(t *testing.T) {
	db := setupTestDBMaintenance(t)
	repo := NewMaintenanceOrderRepository(db)

	// Insere uma ordem para teste
	order := models.MaintenanceOrder{
		BranchID:        1,
		EquipmentID:     1,
		Description:     "Teste Delete",
		Status:          models.StatusPending,
		Priority:        models.PriorityLow,
		CreatedByUserID: 1,
	}
	db.Create(&order)

	// Verifica que a ordem existe
	var count int64
	db.Model(&models.MaintenanceOrder{}).Count(&count)
	assert.Equal(t, int64(1), count)

	// Testa a exclusão
	err := repo.Delete(context.Background(), order.GetIDAsUint())
	assert.NoError(t, err)

	// Verifica que foi excluído
	db.Model(&models.MaintenanceOrder{}).Count(&count)
	assert.Equal(t, int64(0), count)

	// Testa excluir ID não existente
	err = repo.Delete(context.Background(), 999)
	assert.Error(t, err)
}
func TestMaintenanceOrderRepository_GetMetrics(t *testing.T) {
	db := setupTestDBMaintenance(t)
	repo := NewMaintenanceOrderRepository(db)

	// Cria ordens com diferentes status para testar métricas
	statuses := []models.OrderStatus{
		models.StatusPending,
		models.StatusInProgress,
		models.StatusCompleted,
		models.StatusCanceled,
		models.StatusPending,
		models.StatusInProgress,
		models.StatusCompleted,
	}

	for i, status := range statuses {
		order := models.MaintenanceOrder{
			BranchID:        1,
			EquipmentID:     1,
			Description:     fmt.Sprintf("Ordem %d", i+1),
			Status:          status,
			Priority:        models.PriorityMedium,
			CreatedByUserID: 1,
			CreatedAt:       time.Now().AddDate(0, 0, -i), // Datas diferentes
		}
		db.Create(&order)
	}

	// Testa obtenção de métricas
	metrics, err := repo.GetMetrics(context.Background(), map[string]interface{}{}, 0, "")
	assert.NoError(t, err)

	// Verifica contagens por status
	assert.Equal(t, 2, metrics.PendingCount)
	assert.Equal(t, 2, metrics.InProgressCount)
	assert.Equal(t, 2, metrics.CompletedCount)
	assert.Equal(t, 1, metrics.CancelledCount)
	assert.Equal(t, 0, metrics.OnHoldCount)

	// Total de ordens deve ser 7
	assert.Equal(t, 7, metrics.TotalCount)
}
