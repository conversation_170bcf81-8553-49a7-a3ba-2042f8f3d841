package repository

import (
	"tradicao/internal/models"
)

// IStationRepository define a interface para operações com estações
// Deprecated: Use IFilialRepository em vez disso
type IStationRepository interface {
	// Obter estação por ID
	GetStationByID(id uint) (*models.Station, error)

	// Obter todas as estações
	GetAllStations() ([]models.Station, error)

	// Obter estações por usuário
	GetStationsByUserID(userID int64) ([]models.Station, error)

	// Criar estação
	CreateStation(station *models.Station) error

	// Atualizar estação
	UpdateStation(station *models.Station) error

	// Excluir estação
	DeleteStation(id uint) error

	// Obter estações por região
	GetStationsByRegion(region string) ([]models.Station, error)

	// Obter estações ativas
	GetActiveStations() ([]models.Station, error)

	// Obter métricas de estações
	GetStationMetrics() (*models.StationMetrics, error)
}
