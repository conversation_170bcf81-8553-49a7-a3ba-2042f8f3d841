package repository

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"tradicao/internal/models"
)

// SQLUserRepository implementa UserRepository usando database/sql
type SQLUserRepository struct {
	db *sql.DB
}

// NewUserRepository cria um novo repositório de usuários com SQL
func NewUserRepository(db *sql.DB) *SQLUserRepository {
	return &SQLUserRepository{db: db}
}

// GetUserByID busca um usuário pelo ID
func (r *SQLUserRepository) GetUserByID(id int64) (*models.User, error) {
	query := `
        SELECT id, name, email, type, active, branch_id, failed_attempts, blocked, 
               totp_secret, totp_enabled, created_at, updated_at 
        FROM users 
        WHERE id = $1`

	var user models.User
	var isActive sql.NullBool
	err := r.db.QueryRow(query, id).Scan(
		&user.ID, &user.Name, &user.Email, &user.Role, &isActive,
		&user.BranchID, &user.FailedAttempts, &user.Blocked, &user.TOTPSecret, &user.TOTPEnabled,
		&user.CreatedAt, &user.UpdatedAt,
	)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errors.New("usuário não encontrado")
		}
		return nil, err
	}

	user.Blocked = isActive.Valid && !isActive.Bool
	return &user, nil
}

// GetUserByUID busca um usuário pelo ID (método auxiliar para compatibilidade)
func (r *SQLUserRepository) GetUserByUID(id uint) (*models.User, error) {
	return r.GetUserByID(int64(id))
}

// FindByID busca um usuário pelo ID (método legado)
func (r *SQLUserRepository) FindByID(id uint) (models.User, error) {
	user, err := r.GetUserByID(int64(id))
	if err != nil {
		return models.User{}, err
	}
	return *user, nil
}

// FindByEmail busca um usuário pelo email
func (r *SQLUserRepository) FindByEmail(email string) (models.User, error) {
	query := `
                SELECT id, name, email, password, type, active, branch_id, failed_attempts, blocked, totp_secret, totp_enabled,
                       created_at, updated_at
                FROM users
                WHERE email = $1
        `

	var user models.User
	var isActive sql.NullBool
	err := r.db.QueryRow(query, email).Scan(
		&user.ID,
		&user.Name,
		&user.Email,
		&user.Password,
		&user.Role,
		&isActive,
		&user.BranchID,
		&user.FailedAttempts,
		&user.Blocked,
		&user.TOTPSecret,
		&user.TOTPEnabled,
		&user.CreatedAt,
		&user.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return models.User{}, nil
		}
		return models.User{}, err
	}

	user.Blocked = isActive.Valid && !isActive.Bool

	return user, nil
}

// Update atualiza um usuário
func (r *SQLUserRepository) Update(user models.User) (models.User, error) {
	query := `
                UPDATE users
                SET name = $1, email = $2, type = $3, active = $4, branch_id = $5,
                    failed_attempts = $6, blocked = $7, updated_at = $8
                WHERE id = $9
        `
	now := time.Now()
	isActive := !user.Blocked

	result, err := r.db.Exec(
		query,
		user.Name,
		user.Email,
		user.Role,
		isActive,
		user.BranchID,
		user.FailedAttempts,
		user.Blocked,
		now,
		user.ID,
	)

	if err != nil {
		if strings.Contains(err.Error(), "unique constraint") || strings.Contains(err.Error(), "duplicate key value") {
			return models.User{}, errors.New("email já pertence a outro usuário")
		}
		return models.User{}, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return models.User{}, err
	}

	if rowsAffected == 0 {
		return models.User{}, errors.New("usuário não encontrado ou nenhum dado alterado")
	}

	user.UpdatedAt = now
	return user, nil
}

// Delete remove um usuário
func (r *SQLUserRepository) Delete(id uint) error {
	query := `DELETE FROM users WHERE id = $1`

	result, err := r.db.Exec(query, id)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return errors.New("usuário não encontrado")
	}

	return nil
}

// FindAll lista todos os usuários
func (r *SQLUserRepository) FindAll() ([]models.User, error) {
	query := `
                SELECT id, name, email, password, type, active, branch_id, failed_attempts, blocked, totp_secret, totp_enabled,
                       created_at, updated_at
                FROM users
                ORDER BY id
        `

	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var users []models.User
	for rows.Next() {
		var user models.User
		var isActive sql.NullBool
		err := rows.Scan(
			&user.ID,
			&user.Name,
			&user.Email,
			&user.Password,
			&user.Role,
			&isActive,
			&user.BranchID,
			&user.FailedAttempts,
			&user.Blocked,
			&user.TOTPSecret,
			&user.TOTPEnabled,
			&user.CreatedAt,
			&user.UpdatedAt,
		)

		if err != nil {
			return nil, err
		}

		user.Blocked = isActive.Valid && !isActive.Bool

		users = append(users, user)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return users, nil
}

// CreateAuditLog cria um registro de auditoria
func (r *SQLUserRepository) CreateAuditLog(log models.AuditLog) error {
	query := `
                INSERT INTO audit_logs (user_id, action, resource, details, ip, user_agent, created_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
        `

	var userId *uint
	if log.UserID != nil {
		userId = log.UserID
	}

	_, err := r.db.Exec(
		query,
		userId,
		log.Action,
		log.Resource,
		log.Details,
		log.IP,
		log.UserAgent,
		time.Now(),
	)

	return err
}

// UpdateLoginAttempts atualiza o contador de tentativas de login (simulado, já que não temos as colunas no banco)
func (r *SQLUserRepository) UpdateLoginAttempts(userID uint, failed bool) error {
	query := `UPDATE users SET updated_at = $1 WHERE id = $2`
	_, err := r.db.Exec(query, time.Now(), userID)
	return err
}

// GetSecurityPolicy obtém a política de segurança
func (r *SQLUserRepository) GetSecurityPolicy() (models.SecurityPolicy, error) {
	return models.SecurityPolicy{
		MaxLoginAttempts:           5,
		PasswordExpiryDays:         90,
		PasswordRequireUppercase:   true,
		PasswordRequireNumber:      true,
		PasswordRequireSpecialChar: true,
		Enable2FA:                  false,
		SessionTimeoutMinutes:      30,
	}, nil
}

// EnableTOTP ativa a autenticação de dois fatores para um usuário
func (r *SQLUserRepository) EnableTOTP(userID uint, secret string) error {
	query := `
                UPDATE users
                SET totp_secret = $1, totp_enabled = $2, updated_at = $3
                WHERE id = $4
        `

	_, err := r.db.Exec(
		query,
		secret,
		true,
		time.Now(),
		userID,
	)

	return err
}

// DisableTOTP desativa a autenticação de dois fatores para um usuário
func (r *SQLUserRepository) DisableTOTP(userID uint) error {
	query := `
                UPDATE users
                SET totp_secret = '', totp_enabled = $1, updated_at = $2
                WHERE id = $3
        `

	_, err := r.db.Exec(
		query,
		false,
		time.Now(),
		userID,
	)

	return err
}

// SetTOTPSecret define o segredo TOTP para um usuário (sem ativar)
func (r *SQLUserRepository) SetTOTPSecret(userID uint, secret string) error {
	query := `
                UPDATE users
                SET totp_secret = $1, updated_at = $2
                WHERE id = $3
        `

	_, err := r.db.Exec(
		query,
		secret,
		time.Now(),
		userID,
	)

	return err
}

// GetTOTPSecret obtém o segredo TOTP de um usuário e seu status
func (r *SQLUserRepository) GetTOTPSecret(userID uint) (string, bool, error) {
	query := `
                SELECT totp_secret, totp_enabled
                FROM users
                WHERE id = $1
        `

	var secret string
	var enabled bool
	err := r.db.QueryRow(query, userID).Scan(&secret, &enabled)
	if err != nil {
		return "", false, err
	}

	return secret, enabled, nil
}

// UpdatePassword atualiza a senha de um usuário e registra a data de alteração
func (r *SQLUserRepository) UpdatePassword(userID uint, newPassword string) error {
	// Nota: Estamos usando last_password_change em vez de password_changed_at para ser consistente com a coluna adicionada
	query := `
                UPDATE users
                SET password = $1, last_password_change = $2, updated_at = $3
                WHERE id = $4
        `

	now := time.Now()
	_, err := r.db.Exec(
		query,
		newPassword,
		now,
		now,
		userID,
	)

	return err
}

// AddPasswordToHistory adiciona um hash de senha ao histórico de senhas do usuário
func (r *SQLUserRepository) AddPasswordToHistory(userID uint, passwordHash string) error {
	var currentHistory string
	historyQuery := `
                SELECT password_history
                FROM users
                WHERE id = $1
        `

	err := r.db.QueryRow(historyQuery, userID).Scan(&currentHistory)
	if err != nil && err != sql.ErrNoRows {
		return err
	}

	if err == sql.ErrNoRows {
		currentHistory = ""
	}

	var newHistory string
	if currentHistory == "" {
		newHistory = passwordHash
	} else {
		historySplit := strings.Split(currentHistory, ",")
		if len(historySplit) >= 5 {
			historySplit = historySplit[:5]
		}
		newHistory = passwordHash + "," + strings.Join(historySplit, ",")
	}

	updateQuery := `
                UPDATE users
                SET password_history = $1, updated_at = $2
                WHERE id = $3
        `

	_, err = r.db.Exec(
		updateQuery,
		newHistory,
		time.Now(),
		userID,
	)

	return err
}

// IsPasswordInHistory verifica se uma senha está no histórico do usuário
func (r *SQLUserRepository) IsPasswordInHistory(userID uint, passwordHash string) (bool, error) {
	var history string
	query := `
                SELECT password_history
                FROM users
                WHERE id = $1
        `

	err := r.db.QueryRow(query, userID).Scan(&history)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}

	if history == "" {
		return false, nil
	}

	historySplit := strings.Split(history, ",")
	for _, hash := range historySplit {
		if hash == passwordHash {
			return true, nil
		}
	}

	return false, nil
}

// DisableForcePasswordChange desativa a flag de obrigatoriedade de troca de senha
func (r *SQLUserRepository) DisableForcePasswordChange(userID uint) error {
	query := `
                UPDATE users
                SET force_password_change = $1, updated_at = $2
                WHERE id = $3
        `

	_, err := r.db.Exec(
		query,
		false,
		time.Now(),
		userID,
	)

	return err
}

// EnableForcePasswordChange ativa a flag de obrigatoriedade de troca de senha
func (r *SQLUserRepository) EnableForcePasswordChange(userID uint) error {
	query := `
                UPDATE users
                SET force_password_change = $1, updated_at = $2
                WHERE id = $3
        `

	_, err := r.db.Exec(
		query,
		true,
		time.Now(),
		userID,
	)

	return err
}
