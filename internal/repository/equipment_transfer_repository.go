package repository

import (
	"context"
	"log"
	"time"

	"tradicao/internal/models"

	"gorm.io/gorm"
)

// EquipmentTransferRepository gerencia o acesso aos dados de transferências de equipamentos
type EquipmentTransferRepository struct {
	db *gorm.DB
}

// NewEquipmentTransferRepository cria uma nova instância do repositório de transferências de equipamentos
func NewEquipmentTransferRepository(db *gorm.DB) *EquipmentTransferRepository {
	return &EquipmentTransferRepository{
		db: db,
	}
}

// Create cria uma nova transferência de equipamento
func (r *EquipmentTransferRepository) Create(ctx context.Context, transfer *models.EquipmentTransfer) error {
	if err := r.db.WithContext(ctx).Create(transfer).Error; err != nil {
		log.Printf("Erro ao criar transferência de equipamento: %v", err)
		return err
	}
	return nil
}

// FindByID busca uma transferência de equipamento pelo ID
func (r *EquipmentTransferRepository) FindByID(ctx context.Context, id uint) (*models.EquipmentTransfer, error) {
	var transfer models.EquipmentTransfer
	if err := r.db.WithContext(ctx).
		Preload("Equipment").
		Preload("SourceBranch").
		Preload("DestinationBranch").
		Preload("RequestedByUser").
		Preload("ApprovedByUser").
		First(&transfer, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		log.Printf("Erro ao buscar transferência de equipamento por ID %d: %v", id, err)
		return nil, err
	}
	return &transfer, nil
}

// FindByEquipmentID busca transferências de um equipamento específico
func (r *EquipmentTransferRepository) FindByEquipmentID(ctx context.Context, equipmentID uint) ([]models.EquipmentTransfer, error) {
	var transfers []models.EquipmentTransfer
	if err := r.db.WithContext(ctx).
		Preload("Equipment").
		Preload("SourceBranch").
		Preload("DestinationBranch").
		Preload("RequestedByUser").
		Preload("ApprovedByUser").
		Where("equipment_id = ?", equipmentID).
		Find(&transfers).Error; err != nil {
		log.Printf("Erro ao buscar transferências do equipamento %d: %v", equipmentID, err)
		return nil, err
	}
	return transfers, nil
}

// FindPendingBySourceBranch busca transferências pendentes de uma filial de origem
func (r *EquipmentTransferRepository) FindPendingBySourceBranch(ctx context.Context, branchID uint) ([]models.EquipmentTransfer, error) {
	var transfers []models.EquipmentTransfer
	if err := r.db.WithContext(ctx).
		Preload("Equipment").
		Preload("SourceBranch").
		Preload("DestinationBranch").
		Preload("RequestedByUser").
		Where("source_branch_id = ? AND status = ?", branchID, models.StatusTransferenciaPendente).
		Find(&transfers).Error; err != nil {
		log.Printf("Erro ao buscar transferências pendentes da filial %d: %v", branchID, err)
		return nil, err
	}
	return transfers, nil
}

// FindPendingByDestinationBranch busca transferências pendentes para uma filial de destino
func (r *EquipmentTransferRepository) FindPendingByDestinationBranch(ctx context.Context, branchID uint) ([]models.EquipmentTransfer, error) {
	var transfers []models.EquipmentTransfer
	if err := r.db.WithContext(ctx).
		Preload("Equipment").
		Preload("SourceBranch").
		Preload("DestinationBranch").
		Preload("RequestedByUser").
		Where("destination_branch_id = ? AND status = ?", branchID, models.StatusTransferenciaPendente).
		Find(&transfers).Error; err != nil {
		log.Printf("Erro ao buscar transferências pendentes para a filial %d: %v", branchID, err)
		return nil, err
	}
	return transfers, nil
}

// Update atualiza uma transferência de equipamento
func (r *EquipmentTransferRepository) Update(ctx context.Context, transfer *models.EquipmentTransfer) error {
	if err := r.db.WithContext(ctx).Save(transfer).Error; err != nil {
		log.Printf("Erro ao atualizar transferência de equipamento: %v", err)
		return err
	}
	return nil
}

// UpdateStatus atualiza o status de uma transferência de equipamento
func (r *EquipmentTransferRepository) UpdateStatus(ctx context.Context, id uint, status models.StatusTransferencia, userID *uint, notes string) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
		"notes":      notes,
	}

	if status == models.StatusTransferenciaAprovada && userID != nil {
		now := time.Now()
		updates["approved_by_user_id"] = userID
		updates["approved_at"] = now
	}

	if status == models.StatusTransferenciaConcluida {
		now := time.Now()
		updates["completed_at"] = now
	}

	if err := r.db.WithContext(ctx).Model(&models.EquipmentTransfer{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		log.Printf("Erro ao atualizar status da transferência %d: %v", id, err)
		return err
	}
	return nil
}

// CompleteTransfer completa uma transferência, atualizando o branch_id do equipamento
func (r *EquipmentTransferRepository) CompleteTransfer(ctx context.Context, transferID uint) error {
	// Iniciar uma transação
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// Buscar a transferência
	var transfer models.EquipmentTransfer
	if err := tx.Preload("Equipment").First(&transfer, transferID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Verificar se a transferência está aprovada
	if transfer.Status != models.StatusTransferenciaAprovada {
		tx.Rollback()
		return gorm.ErrInvalidData
	}

	// Atualizar o branch_id do equipamento
	if err := tx.Model(&models.Equipment{}).Where("id = ?", transfer.EquipmentID).
		Update("branch_id", transfer.DestinationBranchID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Atualizar o status da transferência para concluída
	now := time.Now()
	if err := tx.Model(&transfer).Updates(map[string]interface{}{
		"status":       models.StatusTransferenciaConcluida,
		"completed_at": now,
		"updated_at":   now,
	}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Commit da transação
	return tx.Commit().Error
}

// List lista todas as transferências de equipamentos
func (r *EquipmentTransferRepository) List(ctx context.Context) ([]models.EquipmentTransfer, error) {
	var transfers []models.EquipmentTransfer
	if err := r.db.WithContext(ctx).
		Preload("Equipment").
		Preload("SourceBranch").
		Preload("DestinationBranch").
		Preload("RequestedByUser").
		Preload("ApprovedByUser").
		Find(&transfers).Error; err != nil {
		log.Printf("Erro ao listar transferências de equipamentos: %v", err)
		return nil, err
	}
	return transfers, nil
}
