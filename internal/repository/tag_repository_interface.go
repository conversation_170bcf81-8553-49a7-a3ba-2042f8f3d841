package repository

import (
	"context"

	"tradicao/internal/models"
)

// TagRepository define a interface para operações com tags
type TagRepository interface {
	// Categoria
	CreateCategory(ctx context.Context, category *models.TagCategory) error
	GetCategory(ctx context.Context, id int64) (*models.TagCategory, error)
	ListCategories(ctx context.Context) ([]*models.TagCategory, error)
	UpdateCategory(ctx context.Context, category *models.TagCategory) error
	DeleteCategory(ctx context.Context, id int64) error

	// Tag
	CreateTag(ctx context.Context, tag *models.Tag) error
	GetTag(ctx context.Context, id int64) (*models.Tag, error)
	ListTags(ctx context.Context, categoryID *int64) ([]*models.Tag, error)
	UpdateTag(ctx context.Context, tag *models.Tag) error
	DeleteTag(ctx context.Context, id int64) error

	// Relacionamento Tag-Equipamento
	AddTagToEquipment(ctx context.Context, equipmentID, tagID int64) error
	RemoveTagFromEquipment(ctx context.Context, equipmentID, tagID int64) error
	GetEquipmentTags(ctx context.Context, equipmentID int64) ([]*models.Tag, error)
	GetEquipmentsByTag(ctx context.Context, tagID int64) ([]*models.Equipment, error)
}
