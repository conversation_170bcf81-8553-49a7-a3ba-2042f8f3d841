package repository

import (
	"database/sql"
	"fmt"
	"reflect"
	"strings"
)

// BaseRepository fornece operações CRUD genéricas para qualquer tipo
type BaseRepository struct {
	DB        *sql.DB
	TableName string
}

// NewBaseRepository cria um novo repositório base
func NewBaseRepository(db *sql.DB, tableName string) *BaseRepository {
	return &BaseRepository{
		DB:        db,
		TableName: tableName,
	}
}

// GetAll retorna todos os registros de uma tabela
func (r *BaseRepository) GetAll(dest interface{}) error {
	query := fmt.Sprintf("SELECT * FROM %s WHERE deleted_at IS NULL", r.TableName)
	rows, err := r.DB.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	// Implementação genérica para escanear resultados em uma slice de structs
	// Este é um esboço simplificado; uma implementação completa usaria reflexão
	return fmt.Errorf("método não implementado completamente")
}

// GetByID retorna um registro pelo ID
func (r *BaseRepository) GetByID(id int, dest interface{}) error {
	query := fmt.Sprintf("SELECT * FROM %s WHERE id = $1 AND deleted_at IS NULL", r.TableName)
	return r.DB.QueryRow(query, id).Scan(dest)
}

// Create insere um novo registro
func (r *BaseRepository) Create(model interface{}) (int, error) {
	// Extrair campos e valores usando reflexão
	val := reflect.ValueOf(model)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	var fields []string
	var placeholders []string
	var values []interface{}

	for i := 0; i < val.NumField(); i++ {
		field := val.Type().Field(i)
		tag := field.Tag.Get("json")
		if tag == "" || tag == "-" || tag == "id" ||
			strings.Contains(tag, "created_at") ||
			strings.Contains(tag, "updated_at") ||
			strings.Contains(tag, "deleted_at") {
			continue
		}

		fields = append(fields, tag)
		placeholders = append(placeholders, fmt.Sprintf("$%d", len(values)+1))
		values = append(values, val.Field(i).Interface())
	}

	query := fmt.Sprintf(
		"INSERT INTO %s (%s) VALUES (%s) RETURNING id",
		r.TableName,
		strings.Join(fields, ", "),
		strings.Join(placeholders, ", "),
	)

	var id int
	err := r.DB.QueryRow(query, values...).Scan(&id)
	return id, err
}

// Update atualiza um registro existente
func (r *BaseRepository) Update(id int, model interface{}) error {
	// Implementação similar a Create, mas para UPDATE
	val := reflect.ValueOf(model)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	var sets []string
	var values []interface{}

	for i := 0; i < val.NumField(); i++ {
		field := val.Type().Field(i)
		tag := field.Tag.Get("json")
		if tag == "" || tag == "-" || tag == "id" ||
			strings.Contains(tag, "created_at") ||
			strings.Contains(tag, "updated_at") ||
			strings.Contains(tag, "deleted_at") {
			continue
		}

		sets = append(sets, fmt.Sprintf("%s = $%d", tag, len(values)+1))
		values = append(values, val.Field(i).Interface())
	}

	values = append(values, id)

	query := fmt.Sprintf(
		"UPDATE %s SET %s, updated_at = CURRENT_TIMESTAMP WHERE id = $%d",
		r.TableName,
		strings.Join(sets, ", "),
		len(values),
	)

	_, err := r.DB.Exec(query, values...)
	return err
}

// Delete marca um registro como excluído (soft delete)
func (r *BaseRepository) Delete(id int) error {
	query := fmt.Sprintf("UPDATE %s SET deleted_at = CURRENT_TIMESTAMP WHERE id = $1", r.TableName)
	_, err := r.DB.Exec(query, id)
	return err
}

// HardDelete remove permanentemente um registro
func (r *BaseRepository) HardDelete(id int) error {
	query := fmt.Sprintf("DELETE FROM %s WHERE id = $1", r.TableName)
	_, err := r.DB.Exec(query, id)
	return err
}
