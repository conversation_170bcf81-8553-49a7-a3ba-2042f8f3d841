package repository

import (
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// GormStationRepository implementa StationRepository usando GORM
type GormStationRepository struct {
	db *gorm.DB
}

// NewGormStationRepository cria um novo repositório de estações com GORM
func NewGormStationRepository(db *gorm.DB) *GormStationRepository {
	return &GormStationRepository{db: db}
}

// GetStationByID busca uma estação pelo ID
func (r *GormStationRepository) GetStationByID(id uint) (*models.Station, error) {
	var station models.Station
	if err := r.db.First(&station, id).Error; err != nil {
		return nil, err
	}
	return &station, nil
}

// GetAllStations retorna todas as estações
func (r *GormStationRepository) GetAllStations() ([]models.Station, error) {
	var stations []models.Station
	if err := r.db.Find(&stations).Error; err != nil {
		return nil, err
	}
	return stations, nil
}

// GetStationsByUserID retorna as estações associadas a um usuário
func (r *GormStationRepository) GetStationsByUserID(userID int64) ([]models.Station, error) {
	var stations []models.Station
	err := r.db.Joins("JOIN user_stations ON stations.id = user_stations.station_id").
		Where("user_stations.user_id = ?", userID).
		Find(&stations).Error
	return stations, err
}

// CreateStation cria uma nova estação
func (r *GormStationRepository) CreateStation(station *models.Station) error {
	return r.db.Create(station).Error
}

// UpdateStation atualiza uma estação existente
func (r *GormStationRepository) UpdateStation(station *models.Station) error {
	return r.db.Save(station).Error
}

// DeleteStation remove uma estação
func (r *GormStationRepository) DeleteStation(id uint) error {
	return r.db.Delete(&models.Station{}, id).Error
}

// GetStationsByRegion retorna estações por região
func (r *GormStationRepository) GetStationsByRegion(region string) ([]models.Station, error) {
	var stations []models.Station
	err := r.db.Where("region = ?", region).Find(&stations).Error
	return stations, err
}

// GetActiveStations retorna todas as estações ativas
func (r *GormStationRepository) GetActiveStations() ([]models.Station, error) {
	var stations []models.Station
	err := r.db.Where("active = ?", true).Find(&stations).Error
	return stations, err
}

// GetStationMetrics retorna métricas das estações
func (r *GormStationRepository) GetStationMetrics() (*models.StationMetrics, error) {
	var metrics models.StationMetrics

	// Total de estações
	if err := r.db.Model(&models.Station{}).Count(&metrics.TotalStations).Error; err != nil {
		return nil, err
	}

	// Estações ativas
	if err := r.db.Model(&models.Station{}).Where("active = ?", true).Count(&metrics.ActiveStations).Error; err != nil {
		return nil, err
	}

	// Estações por região
	type RegionCount struct {
		Region string
		Count  int64
	}
	var regionCounts []RegionCount
	if err := r.db.Model(&models.Station{}).
		Select("region, count(*) as count").
		Group("region").
		Find(&regionCounts).Error; err != nil {
		return nil, err
	}

	metrics.StationsByRegion = make(map[string]int64)
	for _, rc := range regionCounts {
		metrics.StationsByRegion[rc.Region] = rc.Count
	}

	return &metrics, nil
}

// GetStationsByBranchID retorna estações associadas a uma filial
func (r *GormStationRepository) GetStationsByBranchID(branchID uint) ([]models.StationSummary, error) {
	var stations []models.StationSummary

	// Consulta estações associadas à filial
	result := r.db.Table("stations").
		Select("stations.id, stations.name, stations.code, stations.city, stations.state, stations.is_active").
		Joins("JOIN branch_station_links ON branch_station_links.station_id = stations.id").
		Where("branch_station_links.branch_id = ?", branchID).
		Find(&stations)

	// Se não encontrar resultados com a tabela de links, tenta obter por campo de filial direta (para compatibilidade)
	if len(stations) == 0 {
		result = r.db.Table("stations").
			Select("stations.id, stations.name, stations.code, stations.city, stations.state, stations.is_active").
			Where("stations.branch_id = ?", branchID).
			Find(&stations)
	}

	return stations, result.Error
}

// StationExists verifica se uma estação existe
func (r *GormStationRepository) StationExists(id uint) (bool, error) {
	var count int64
	result := r.db.Model(&models.Station{}).Where("id = ?", id).Count(&count)
	return count > 0, result.Error
}
