package repository

import (
	"context"
	"errors"
	"log"
	"time"

	"tradicao/internal/models"

	"gorm.io/gorm"
)

// EquipmentRepository gerencia o acesso aos dados de equipamentos
type EquipmentRepository struct {
	db *gorm.DB
}

// NewEquipmentRepository cria uma nova instância do repositório de equipamentos
func NewEquipmentRepository(db *gorm.DB) *EquipmentRepository {
	return &EquipmentRepository{
		db: db,
	}
}

// List retorna todos os equipamentos
func (r *EquipmentRepository) List(ctx context.Context) ([]models.Equipment, error) {
	var equipments []models.Equipment
	if err := r.db.WithContext(ctx).Find(&equipments).Error; err != nil {
		log.Printf("Erro ao listar equipamentos: %v", err)
		return nil, err
	}
	return equipments, nil
}

// FindByID retorna um equipamento pelo ID
func (r *EquipmentRepository) FindByID(ctx context.Context, id uint) (*models.Equipment, error) {
	var equipment models.Equipment
	if err := r.db.WithContext(ctx).First(&equipment, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		log.Printf("Erro ao buscar equipamento por ID %d: %v", id, err)
		return nil, err
	}
	return &equipment, nil
}

// FindByType retorna equipamentos filtrados por tipo
func (r *EquipmentRepository) FindByType(ctx context.Context, equipmentType string) ([]models.Equipment, error) {
	var equipments []models.Equipment
	if err := r.db.WithContext(ctx).Where("type = ?", equipmentType).Find(&equipments).Error; err != nil {
		log.Printf("Erro ao buscar equipamentos por tipo %s: %v", equipmentType, err)
		return nil, err
	}
	return equipments, nil
}

// Create cria um novo equipamento
func (r *EquipmentRepository) Create(ctx context.Context, equipment *models.Equipment) error {
	if err := r.db.WithContext(ctx).Create(equipment).Error; err != nil {
		log.Printf("Erro ao criar equipamento: %v", err)
		return err
	}
	return nil
}

// Update atualiza um equipamento existente
func (r *EquipmentRepository) Update(ctx context.Context, equipment *models.Equipment) error {
	if err := r.db.WithContext(ctx).Save(equipment).Error; err != nil {
		log.Printf("Erro ao atualizar equipamento %d: %v", equipment.ID, err)
		return err
	}
	return nil
}

// Delete remove um equipamento
func (r *EquipmentRepository) Delete(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Delete(&models.Equipment{}, id).Error; err != nil {
		log.Printf("Erro ao deletar equipamento %d: %v", id, err)
		return err
	}
	return nil
}

// Exists verifica se um equipamento existe
func (r *EquipmentRepository) Exists(ctx context.Context, id uint) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.Equipment{}).Where("id = ?", id).Count(&count).Error; err != nil {
		log.Printf("Erro ao verificar existência do equipamento %d: %v", id, err)
		return false, err
	}
	return count > 0, nil
}

// FindByBranch retorna equipamentos de uma filial específica
func (r *EquipmentRepository) FindByBranch(ctx context.Context, branchID uint) ([]models.Equipment, error) {
	var equipments []models.Equipment
	if err := r.db.WithContext(ctx).Where("branch_id = ?", branchID).Find(&equipments).Error; err != nil {
		log.Printf("Erro ao buscar equipamentos da filial %d: %v", branchID, err)
		return nil, err
	}
	return equipments, nil
}

// FindNeedingMaintenance retorna equipamentos que precisam de manutenção
func (r *EquipmentRepository) FindNeedingMaintenance(ctx context.Context) ([]models.Equipment, error) {
	var equipments []models.Equipment
	if err := r.db.WithContext(ctx).Where("next_preventive <= ?", time.Now()).Find(&equipments).Error; err != nil {
		log.Printf("Erro ao buscar equipamentos que precisam de manutenção: %v", err)
		return nil, err
	}
	return equipments, nil
}
