package repository

import (
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// GormFilialRepository implementa IFilialRepository usando GORM
type GormFilialRepository struct {
	db *gorm.DB
}

// NewGormFilialRepository cria um novo repositório de filiais com GORM
func NewGormFilialRepository(db *gorm.DB) *GormFilialRepository {
	return &GormFilialRepository{db: db}
}

// GetFilialByID busca uma filial pelo ID
func (r *GormFilialRepository) GetFilialByID(id uint) (*models.Filial, error) {
	var filial models.Filial
	if err := r.db.First(&filial, id).Error; err != nil {
		return nil, err
	}
	return &filial, nil
}

// GetAllFiliais retorna todas as filiais
func (r *GormFilialRepository) GetAllFiliais() ([]models.Filial, error) {
	var filiais []models.Filial
	if err := r.db.Find(&filiais).Error; err != nil {
		return nil, err
	}
	return filiais, nil
}

// GetFiliaisByUserID retorna as filiais associadas a um usuário
func (r *GormFilialRepository) GetFiliaisByUserID(userID int64) ([]models.Filial, error) {
	var filiais []models.Filial
	err := r.db.Joins("JOIN user_stations ON stations.id = user_stations.station_id").
		Where("user_stations.user_id = ?", userID).
		Find(&filiais).Error
	return filiais, err
}

// CreateFilial cria uma nova filial
func (r *GormFilialRepository) CreateFilial(filial *models.Filial) error {
	return r.db.Create(filial).Error
}

// UpdateFilial atualiza uma filial existente
func (r *GormFilialRepository) UpdateFilial(filial *models.Filial) error {
	return r.db.Save(filial).Error
}

// DeleteFilial remove uma filial
func (r *GormFilialRepository) DeleteFilial(id uint) error {
	return r.db.Delete(&models.Filial{}, id).Error
}

// GetFiliaisByRegion retorna filiais por região
func (r *GormFilialRepository) GetFiliaisByRegion(region string) ([]models.Filial, error) {
	var filiais []models.Filial
	err := r.db.Where("region = ?", region).Find(&filiais).Error
	return filiais, err
}

// GetActiveFiliais retorna todas as filiais ativas
func (r *GormFilialRepository) GetActiveFiliais() ([]models.Filial, error) {
	var filiais []models.Filial
	err := r.db.Where("is_active = ?", true).Find(&filiais).Error
	return filiais, err
}

// GetFilialMetrics retorna métricas das filiais
func (r *GormFilialRepository) GetFilialMetrics() (*models.FilialMetrics, error) {
	var metrics models.FilialMetrics

	// Total de filiais
	if err := r.db.Model(&models.Filial{}).Count(&metrics.TotalFiliais).Error; err != nil {
		return nil, err
	}

	// Filiais ativas
	if err := r.db.Model(&models.Filial{}).Where("is_active = ?", true).Count(&metrics.ActiveFiliais).Error; err != nil {
		return nil, err
	}

	// Filiais por região
	type RegionCount struct {
		Region string
		Count  int64
	}
	var regionCounts []RegionCount
	if err := r.db.Model(&models.Filial{}).
		Select("region, count(*) as count").
		Group("region").
		Find(&regionCounts).Error; err != nil {
		return nil, err
	}

	metrics.FiliaisByRegion = make(map[string]int64)
	for _, rc := range regionCounts {
		metrics.FiliaisByRegion[rc.Region] = rc.Count
	}

	return &metrics, nil
}

// GetFiliaisByBranchID retorna filiais associadas a uma branch (para compatibilidade)
func (r *GormFilialRepository) GetFiliaisByBranchID(branchID uint) ([]models.FilialSummary, error) {
	var filiais []models.FilialSummary

	// Consulta filiais associadas à branch
	result := r.db.Table("stations").
		Select("stations.id, stations.name, stations.code, stations.city, stations.state, stations.is_active").
		Joins("JOIN branch_station_links ON branch_station_links.station_id = stations.id").
		Where("branch_station_links.branch_id = ?", branchID).
		Find(&filiais)

	// Se não encontrar resultados com a tabela de links, tenta obter por campo de branch direta (para compatibilidade)
	if len(filiais) == 0 {
		result = r.db.Table("stations").
			Select("stations.id, stations.name, stations.code, stations.city, stations.state, stations.is_active").
			Where("stations.branch_id = ?", branchID).
			Find(&filiais)
	}

	return filiais, result.Error
}

// FilialExists verifica se uma filial existe
func (r *GormFilialRepository) FilialExists(id uint) (bool, error) {
	var count int64
	result := r.db.Model(&models.Filial{}).Where("id = ?", id).Count(&count)
	return count > 0, result.Error
}
