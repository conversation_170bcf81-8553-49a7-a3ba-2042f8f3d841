package repository

import (
	"tradicao/internal/models"
)

// BranchRepositoryAdapter adapta o BranchRepository para a interface IBranchRepository
type BranchRepositoryAdapter struct {
	repo *BranchRepository
}

// NewBranchRepositoryAdapter cria um novo adaptador para o BranchRepository
func NewBranchRepositoryAdapter(repo *BranchRepository) IBranchRepository {
	return &BranchRepositoryAdapter{repo: repo}
}

// GetAllBranches delega para o repositório subjacente
func (a *BranchRepositoryAdapter) GetAllBranches(page, limit int, search string, isActive *bool) ([]models.Branch, int64, error) {
	return a.repo.GetAllBranches(page, limit, search, isActive)
}

// GetBranchByID delega para o repositório subjacente
func (a *BranchRepositoryAdapter) GetBranchByID(id uint) (*models.Branch, error) {
	return a.repo.GetBranchByID(id)
}

// GetBranchesByUserID delega para o repositório subjacente
func (a *BranchRepositoryAdapter) GetBranchesByUserID(userID int64) ([]models.Branch, error) {
	return a.repo.GetBranchesByUserID(userID)
}

// GetBranchesByTechnicianID delega para o repositório subjacente
func (a *BranchRepositoryAdapter) GetBranchesByTechnicianID(technicianID int64) ([]models.Branch, error) {
	return a.repo.GetBranchesByTechnicianID(technicianID)
}

// CreateBranch delega para o repositório subjacente
func (a *BranchRepositoryAdapter) CreateBranch(branch *models.Branch) error {
	return a.repo.CreateBranch(branch)
}

// UpdateBranch delega para o repositório subjacente
func (a *BranchRepositoryAdapter) UpdateBranch(branch *models.Branch) error {
	return a.repo.UpdateBranch(branch)
}

// DeleteBranch delega para o repositório subjacente
func (a *BranchRepositoryAdapter) DeleteBranch(id uint) error {
	return a.repo.DeleteBranch(id)
}

// BranchExists delega para o repositório subjacente
func (a *BranchRepositoryAdapter) BranchExists(id uint) (bool, error) {
	return a.repo.BranchExists(id)
}

// LinkProvider delega para o repositório subjacente
func (a *BranchRepositoryAdapter) LinkProvider(branchID, providerID uint) error {
	return a.repo.LinkProvider(branchID, providerID)
}

// UnlinkProvider delega para o repositório subjacente
func (a *BranchRepositoryAdapter) UnlinkProvider(branchID, providerID uint) error {
	return a.repo.UnlinkProvider(branchID, providerID)
}

// GetLinkedProviders delega para o repositório subjacente
func (a *BranchRepositoryAdapter) GetLinkedProviders(branchID uint) ([]uint, error) {
	return a.repo.GetLinkedProviders(branchID)
}

// GenerateAuthToken delega para o repositório subjacente
func (a *BranchRepositoryAdapter) GenerateAuthToken(branchID uint) (string, error) {
	return a.repo.GenerateAuthToken(branchID)
}

// ValidateAuthToken delega para o repositório subjacente
func (a *BranchRepositoryAdapter) ValidateAuthToken(token string) (uint, error) {
	return a.repo.ValidateAuthToken(token)
}

// UseAuthToken delega para o repositório subjacente
func (a *BranchRepositoryAdapter) UseAuthToken(token string) error {
	return a.repo.UseAuthToken(token)
}

// GetBranchesByRegion implementa o método da interface IBranchRepository
func (a *BranchRepositoryAdapter) GetBranchesByRegion(region string) ([]models.Branch, error) {
	// Implementação temporária
	return []models.Branch{}, nil
}

// GetActiveBranches implementa o método da interface IBranchRepository
func (a *BranchRepositoryAdapter) GetActiveBranches() ([]models.Branch, error) {
	// Implementação temporária
	isActive := true
	branches, _, err := a.repo.GetAllBranches(1, 1000, "", &isActive)
	return branches, err
}

// GetBranchMetrics implementa o método da interface IBranchRepository
func (a *BranchRepositoryAdapter) GetBranchMetrics() (*models.BranchMetrics, error) {
	// Implementação temporária
	return &models.BranchMetrics{}, nil
}

// GetBranchSummaries implementa o método da interface IBranchRepository
func (a *BranchRepositoryAdapter) GetBranchSummaries() ([]models.BranchSummary, error) {
	// Implementação temporária
	return []models.BranchSummary{}, nil
}

// GetBranchSummaryByID implementa o método da interface IBranchRepository
func (a *BranchRepositoryAdapter) GetBranchSummaryByID(id uint) (*models.BranchSummary, error) {
	// Implementação temporária
	return &models.BranchSummary{}, nil
}
