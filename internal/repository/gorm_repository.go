package repository

import "gorm.io/gorm"

// GormRepository é um repositório base para implementações com GORM
type GormRepository struct {
	db *gorm.DB
}

// GetDB retorna a conexão com o banco de dados
func (r *GormRepository) GetDB() *gorm.DB {
	return r.db
}

// WithTx executa uma função dentro de uma transação
func (r *GormRepository) WithTx(fn func(tx *gorm.DB) error) error {
	tx := r.db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r) // re-throw panic after rollback
		} else if tx.Error != nil {
			tx.Rollback()
		}
	}()

	if err := fn(tx); err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}
