package repository

import (
	"time"
	"tradicao/internal/models"

	"github.com/stretchr/testify/mock"
)

// MockTechnicianRepository implementa TechnicianRepositoryInterface para testes
type MockTechnicianRepository struct {
	mock.Mock
}

func (m *MockTechnicianRepository) CreateSpecialty(specialty *models.TechnicianSpecialty) error {
	args := m.Called(specialty)
	return args.Error(0)
}

func (m *MockTechnicianRepository) ListSpecialties() ([]models.TechnicianSpecialty, error) {
	args := m.Called()
	return args.Get(0).([]models.TechnicianSpecialty), args.Error(1)
}

func (m *MockTechnicianRepository) UpdateSpecialty(specialty *models.TechnicianSpecialty) error {
	args := m.Called(specialty)
	return args.Error(0)
}

func (m *MockTechnicianRepository) DeleteSpecialty(id uint) error {
	args := m.Called(id)
	return args.Error(0)
}

func (m *MockTechnicianRepository) GetSpecialty(id uint) (*models.TechnicianSpecialty, error) {
	args := m.Called(id)
	return args.Get(0).(*models.TechnicianSpecialty), args.Error(1)
}

func (m *MockTechnicianRepository) CreateBranchAssociation(association *models.TechnicianBranch) error {
	args := m.Called(association)
	return args.Error(0)
}

func (m *MockTechnicianRepository) UpdateBranchAssociation(association *models.TechnicianBranch) error {
	args := m.Called(association)
	return args.Error(0)
}

func (m *MockTechnicianRepository) DeleteBranchAssociation(technicianID, branchID uint) error {
	args := m.Called(technicianID, branchID)
	return args.Error(0)
}

func (m *MockTechnicianRepository) GetBranchAssociation(technicianID, branchID uint) (*models.TechnicianBranch, error) {
	args := m.Called(technicianID, branchID)
	return args.Get(0).(*models.TechnicianBranch), args.Error(1)
}

func (m *MockTechnicianRepository) ListBranchAssociations(technicianID uint) ([]models.TechnicianBranch, error) {
	args := m.Called(technicianID)
	return args.Get(0).([]models.TechnicianBranch), args.Error(1)
}

func (m *MockTechnicianRepository) CreateMaintenanceHistory(history *models.MaintenanceHistory) error {
	args := m.Called(history)
	return args.Error(0)
}

func (m *MockTechnicianRepository) ListMaintenanceHistory(technicianID uint, startDate, endDate time.Time) ([]models.MaintenanceHistory, error) {
	args := m.Called(technicianID, startDate, endDate)
	return args.Get(0).([]models.MaintenanceHistory), args.Error(1)
}

func (m *MockTechnicianRepository) GetMaintenanceHistory(id uint) (*models.MaintenanceHistory, error) {
	args := m.Called(id)
	return args.Get(0).(*models.MaintenanceHistory), args.Error(1)
} 