package repository

import (
	"context"
	"time"
	"tradicao/internal/models"
)

// MaintenanceOrderRepositoryAdapter adapta o MaintenanceOrderRepository para a interface OrderRepository
type MaintenanceOrderRepositoryAdapter struct {
	repo *MaintenanceOrderRepository
}

// NewMaintenanceOrderRepositoryAdapter cria um novo adaptador para o repositório de ordens
func NewMaintenanceOrderRepositoryAdapter(repo *MaintenanceOrderRepository) OrderRepository {
	return &MaintenanceOrderRepositoryAdapter{
		repo: repo,
	}
}

// GetOrderByID implementa o método GetOrderByID da interface OrderRepository
func (a *MaintenanceOrderRepositoryAdapter) GetOrderByID(id uint) (*models.MaintenanceOrder, error) {
	return a.repo.GetByID(context.Background(), id)
}

// GetOrderCosts implementa o método GetOrderCosts da interface OrderRepository
func (a *MaintenanceOrderRepositoryAdapter) GetOrderCosts(orderID uint) ([]models.CostItem, error) {
	costs, err := a.repo.GetCosts(context.Background(), int64(orderID))
	if err != nil {
		return nil, err
	}
	// Converter MaintenanceOrderCosts para []CostItem
	var result []models.CostItem
	result = append(result, models.CostItem{
		MaintenanceOrderID: orderID,
		Description:        "Custo de Material",
		Quantity:           1,
		UnitPrice:          costs.MaterialCost,
	})
	result = append(result, models.CostItem{
		MaintenanceOrderID: orderID,
		Description:        "Custo de Mão de Obra",
		Quantity:           1,
		UnitPrice:          costs.LaborCost,
	})
	return result, nil
}

// GetOrderCountsByStatus implementa o método GetOrderCountsByStatus da interface OrderRepository
func (a *MaintenanceOrderRepositoryAdapter) GetOrderCountsByStatus() (map[models.OrderStatus]int, error) {
	return a.repo.GetMetricsByStatus(context.Background(), 0, "admin")
}

// GetOrderInteractions implementa o método GetOrderInteractions da interface OrderRepository
func (a *MaintenanceOrderRepositoryAdapter) GetOrderInteractions(orderID uint) ([]models.Interaction, error) {
	return a.repo.GetInteractions(context.Background(), int64(orderID))
}

// GetOrdersWithPagination implementa o método GetOrdersWithPagination da interface OrderRepository
func (a *MaintenanceOrderRepositoryAdapter) GetOrdersWithPagination(page, pageSize int, status models.OrderStatus, branchID uint, startDate, endDate time.Time) ([]models.MaintenanceOrder, int, error) {
	filters := make(map[string]interface{})
	if status != "" {
		filters["status"] = status
	}
	if branchID != 0 {
		filters["branch_id"] = branchID
	}

	orders, total, err := a.repo.GetAll(context.Background(), filters, 0, "admin", page, pageSize)
	if err != nil {
		return nil, 0, err
	}

	// Converter MaintenanceOrderDetailed para MaintenanceOrder
	var result []models.MaintenanceOrder
	for _, order := range orders {
		result = append(result, models.MaintenanceOrder{
			ID:               order.ID,
			BranchID:         order.StationID,
			BranchName:       order.StationName,
			CreatedByUserID:  order.CreatedBy,
			CreatedByName:    order.CreatedByName,
			Title:            order.Title,
			Description:      order.Description,
			Status:           models.OrderStatus(order.Status),
			Priority:         models.PriorityLevel(order.Priority),
			DueDate:          *order.StartDate,
			CompletionDate:   order.CompletedDate,
			CreatedAt:        order.CreatedAt,
			UpdatedAt:        order.UpdatedAt,
			AssignedToUserID: *order.AssignedTo,
		})
	}

	return result, int(total), nil
}
