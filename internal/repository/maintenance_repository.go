package repository

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"tradicao/internal/models"
)

// SQLMaintenanceRepository é uma implementação do repositório usando sql.DB
// NOTA: Este repositório parece conter código legado misturado com GORM.
// Algumas funções podem estar desatualizadas ou duplicadas.
type SQLMaintenanceRepository struct {
	db *sql.DB
}

// NewMaintenanceRepository cria uma nova instância do repositório de manutenção
func NewMaintenanceRepository(db *sql.DB) *SQLMaintenanceRepository {
	if db == nil {
		// Lógica para obter ou inicializar DB se necessário
		panic("DB não pode ser nulo") // Ou retorne um erro
	}
	return &SQLMaintenanceRepository{db: db}
}

// UpdateOrderStatus atualiza o status de uma ordem de manutenção
func (r *SQLMaintenanceRepository) UpdateOrderStatus(orderID int64, status models.OrderStatus, userID int64, notes string, completedDate *time.Time, actualTime *int) error {
	tx, err := r.db.Begin()
	if err != nil {
		return fmt.Errorf("erro ao iniciar transação: %v", err)
	}

	now := time.Now()
	query := `
                UPDATE maintenance_orders
                SET status = $1, updated_at = $2
        `

	args := []interface{}{status, now}
	paramCount := 2

	// Se o status for "em_andamento" e não tiver data de início, adiciona
	if status == models.StatusInProgress {
		paramCount++
		query += fmt.Sprintf(", start_date = COALESCE(start_date, $%d)", paramCount)
		args = append(args, now)
	}

	// Se o status for "concluida", adiciona a data de conclusão
	if status == models.StatusCompleted {
		paramCount++
		query += fmt.Sprintf(", completed_date = $%d", paramCount)
		if completedDate != nil {
			args = append(args, *completedDate)
		} else {
			args = append(args, now)
		}

		// Adiciona o tempo real, se fornecido
		if actualTime != nil {
			paramCount++
			query += fmt.Sprintf(", actual_time = $%d", paramCount)
			args = append(args, *actualTime)
		}
	}

	paramCount++
	query += fmt.Sprintf(" WHERE id = $%d", paramCount)
	args = append(args, orderID)

	_, err = tx.Exec(query, args...)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("erro ao atualizar status: %v", err)
	}

	// Adiciona uma nota sobre a atualização de status
	if notes != "" {
		noteQuery := `
                        INSERT INTO maintenance_notes (order_id, user_id, content, created_at)
                        VALUES ($1, $2, $3, $4)
                `

		statusNote := fmt.Sprintf("Status alterado para: %s. %s", status, notes)
		_, err = tx.Exec(noteQuery, orderID, userID, statusNote, now)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("erro ao adicionar nota sobre status: %v", err)
		}
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("erro ao confirmar transação: %v", err)
	}

	return nil
}

// UpdatePriority atualiza a prioridade de uma ordem de manutenção
func (r *SQLMaintenanceRepository) UpdatePriority(orderID int64, priority models.PriorityLevel, userID int64, notes string) error {
	tx, err := r.db.Begin()
	if err != nil {
		return fmt.Errorf("erro ao iniciar transação: %v", err)
	}

	now := time.Now()
	query := `
                UPDATE maintenance_orders
                SET priority = $1, updated_at = $2
                WHERE id = $3
        `

	_, err = tx.Exec(query, priority, now, orderID)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("erro ao atualizar prioridade: %v", err)
	}

	// Adiciona uma nota sobre a atualização de prioridade
	if notes != "" {
		noteQuery := `
                        INSERT INTO maintenance_notes (order_id, user_id, content, created_at)
                        VALUES ($1, $2, $3, $4)
                `

		priorityNote := fmt.Sprintf("Prioridade alterada para: %s. %s", priority, notes)
		_, err = tx.Exec(noteQuery, orderID, userID, priorityNote, now)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("erro ao adicionar nota sobre prioridade: %v", err)
		}
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("erro ao confirmar transação: %v", err)
	}

	return nil
}

// AddNoteToOrder adiciona uma nota a uma ordem de manutenção
func (r *SQLMaintenanceRepository) AddNoteToOrder(orderID int64, userID int64, content string) error {
	query := `
                INSERT INTO maintenance_notes (order_id, user_id, content, created_at)
                VALUES ($1, $2, $3, $4)
        `

	_, err := r.db.Exec(query, orderID, userID, content, time.Now())
	if err != nil {
		return fmt.Errorf("erro ao adicionar nota: %v", err)
	}

	return nil
}

// AddMaterialToOrder adiciona um material a uma ordem de manutenção
func (r *SQLMaintenanceRepository) AddMaterialToOrder(orderID int64, material models.Material) error {
	query := `
                INSERT INTO maintenance_materials (order_id, name, quantity, unit, cost, added_by, created_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
        `

	now := time.Now()

	// Cost já é float64, não é um ponteiro, então não precisamos desreferenciar
	cost := material.Cost

	_, err := r.db.Exec(
		query,
		orderID,
		material.Name,
		material.Quantity,
		material.Unit,
		cost,
		material.AddedByUserID,
		now,
	)

	if err != nil {
		return fmt.Errorf("erro ao adicionar material: %v", err)
	}

	return nil
}

// CreateValue (Interface IMaintenance) - Pode ser um wrapper para CreateOrder ou Create
func (r *SQLMaintenanceRepository) CreateValue(order models.MaintenanceOrder) (models.MaintenanceOrder, error) {
	// Implementação precisa ser definida. Exemplo:
	if err := r.CreateOrder(&order); err != nil { // Assume CreateOrder é a implementação desejada
		return models.MaintenanceOrder{}, err
	}
	return order, nil
}

// CreateOrder aceita um ponteiro
func (r *SQLMaintenanceRepository) CreateOrder(order *models.MaintenanceOrder) error {
	// Esta implementação provavelmente deveria usar GORM agora, ou ser removida
	// Se for mantida com SQL, precisa ser atualizada para a struct MaintenanceOrder correta
	return fmt.Errorf("CreateOrder não implementado ou desatualizado")
}

// Update (Interface IMaintenance) - Pode ser um wrapper para UpdateOrder
func (r *SQLMaintenanceRepository) Update(orderValue models.MaintenanceOrder) (models.MaintenanceOrder, error) {
	// Implementação precisa ser definida. Exemplo:
	if err := r.UpdateOrder(&orderValue); err != nil { // Assume UpdateOrder é a implementação desejada
		return models.MaintenanceOrder{}, err
	}
	return orderValue, nil
}

// UpdateOrder aceita um ponteiro
func (r *SQLMaintenanceRepository) UpdateOrder(order *models.MaintenanceOrder) error {
	// Esta implementação provavelmente deveria usar GORM agora, ou ser removida
	// Se for mantida com SQL, precisa ser atualizada para a struct MaintenanceOrder correta
	return fmt.Errorf("UpdateOrder não implementado ou desatualizado")
}

// UpdateStatus atualiza apenas o status (pode ser usado por GORM)
func (r *SQLMaintenanceRepository) UpdateStatus(id uint, status string) error {
	// Esta implementação provavelmente deveria usar GORM agora, ou ser removida
	return fmt.Errorf("UpdateStatus não implementado ou desatualizado")
}

// FindByID retorna a ordem básica (pode ser usado por GORM)
func (r *SQLMaintenanceRepository) FindByID(id uint) (models.MaintenanceOrder, error) {
	// Esta implementação provavelmente deveria usar GORM agora, ou ser removida
	return models.MaintenanceOrder{}, fmt.Errorf("FindByID não implementado ou desatualizado")
}

// FindAll retorna todas as ordens básicas (pode ser usado por GORM)
func (r *SQLMaintenanceRepository) FindAll() ([]models.MaintenanceOrder, error) {
	// Esta implementação provavelmente deveria usar GORM agora, ou ser removida
	return nil, fmt.Errorf("FindAll não implementado ou desatualizado")
}

// FindAllDetailed retorna todas as ordens detalhadas (pode ser usado por GORM)
func (r *SQLMaintenanceRepository) FindAllDetailed() ([]models.MaintenanceOrderDetailed, error) {
	// Esta implementação provavelmente deveria usar GORM agora, ou ser removida
	return nil, fmt.Errorf("FindAllDetailed não implementado ou desatualizado")
}

// FindByStation (pode ser usado por GORM)
func (r *SQLMaintenanceRepository) FindByStation(stationID int64) ([]models.MaintenanceOrder, error) {
	// Esta implementação provavelmente deveria usar GORM agora, ou ser removida
	return nil, fmt.Errorf("FindByStation não implementado ou desatualizado")
}

// FindByStatus (pode ser usado por GORM)
func (r *SQLMaintenanceRepository) FindByStatus(status string) ([]models.MaintenanceOrder, error) {
	// Esta implementação provavelmente deveria usar GORM agora, ou ser removida
	return nil, fmt.Errorf("FindByStatus não implementado ou desatualizado")
}

// FindByDateRange (pode ser usado por GORM)
func (r *SQLMaintenanceRepository) FindByDateRange(start, end time.Time) ([]models.MaintenanceOrder, error) {
	// Esta implementação provavelmente deveria usar GORM agora, ou ser removida
	return nil, fmt.Errorf("FindByDateRange não implementado ou desatualizado")
}

// CountByStatus (pode ser usado por GORM)
func (r *SQLMaintenanceRepository) CountByStatus(status string) (int64, error) {
	// Esta implementação provavelmente deveria usar GORM agora, ou ser removida
	return 0, fmt.Errorf("CountByStatus não implementado ou desatualizado")
}

// GetOverdueOrders (pode ser usado por GORM)
func (r *SQLMaintenanceRepository) GetOverdueOrders() ([]models.MaintenanceOrder, error) {
	// Esta implementação provavelmente deveria usar GORM agora, ou ser removida
	return nil, fmt.Errorf("GetOverdueOrders não implementado ou desatualizado")
}

// GetStats (pode ser usado por GORM)
func (r *SQLMaintenanceRepository) GetStats() (map[string]interface{}, error) {
	// Esta implementação provavelmente deveria usar GORM agora, ou ser removida
	return nil, fmt.Errorf("GetStats não implementado ou desatualizado")
}

// AddNote (pode ser usado por GORM)
func (r *SQLMaintenanceRepository) AddNote(note models.Note) (models.Note, error) {
	// Esta implementação provavelmente deveria usar GORM agora, ou ser removida
	return models.Note{}, fmt.Errorf("AddNote não implementado ou desatualizado")
}

// GetNotes (pode ser usado por GORM)
func (r *SQLMaintenanceRepository) GetNotes(orderID uint) ([]models.Note, error) {
	// Esta implementação provavelmente deveria usar GORM agora, ou ser removida
	return nil, fmt.Errorf("GetNotes não implementado ou desatualizado")
}

// AddMaterial (pode ser usado por GORM)
func (r *SQLMaintenanceRepository) AddMaterial(material models.Material) (models.Material, error) {
	// Esta implementação provavelmente deveria usar GORM agora, ou ser removida
	return models.Material{}, fmt.Errorf("AddMaterial não implementado ou desatualizado")
}

// GetMaterials (pode ser usado por GORM)
func (r *SQLMaintenanceRepository) GetMaterials(orderID uint) ([]models.Material, error) {
	// Esta implementação provavelmente deveria usar GORM agora, ou ser removida
	return nil, fmt.Errorf("GetMaterials não implementado ou desatualizado")
}

// Delete (pode ser usado por GORM)
func (r *SQLMaintenanceRepository) Delete(id uint) error {
	// Esta implementação provavelmente deveria usar GORM agora, ou ser removida
	return fmt.Errorf("Delete não implementado ou desatualizado")
}

// GetAll retorna ordens detalhadas com filtros, paginação e contagem total
func (r *SQLMaintenanceRepository) GetAll(filters map[string]interface{}, stationIDs []int64, page, limit int) ([]models.MaintenanceOrderDetailed, int, error) {
	// Base da consulta
	baseQuery := `
                SELECT
                        mo.id, mo.title, mo.type, mo.station_id, s.name AS station_name,
                        mo.status, mo.priority, mo.created_by, creator.name AS created_by_name,
                        mo.assigned_to, tech.name AS assigned_to_name, mo.equipment,
                        mo.start_date, mo.completed_date, mo.created_at, mo.updated_at
                FROM maintenance_orders mo
                JOIN stations s ON mo.station_id = s.id
                JOIN users creator ON mo.created_by = creator.id
                LEFT JOIN users tech ON mo.assigned_to = tech.id
                WHERE 1=1
        `

	// Consulta para contagem total
	countQuery := `
                SELECT COUNT(*)
                FROM maintenance_orders mo
                JOIN stations s ON mo.station_id = s.id
                WHERE 1=1
        `

	// Parâmetros
	var params []interface{}
	paramCount := 1 // Começando com $1

	// Adiciona filtros
	if status, ok := filters["status"].(models.OrderStatus); ok && status != "" {
		baseQuery += fmt.Sprintf(" AND mo.status = $%d", paramCount)
		countQuery += fmt.Sprintf(" AND mo.status = $%d", paramCount)
		params = append(params, status)
		paramCount++
	}

	if priority, ok := filters["priority"].(models.PriorityLevel); ok && priority != "" {
		baseQuery += fmt.Sprintf(" AND mo.priority = $%d", paramCount)
		countQuery += fmt.Sprintf(" AND mo.priority = $%d", paramCount)
		params = append(params, priority)
		paramCount++
	}

	if orderType, ok := filters["type"].(models.MaintenanceType); ok && orderType != "" {
		baseQuery += fmt.Sprintf(" AND mo.type = $%d", paramCount)
		countQuery += fmt.Sprintf(" AND mo.type = $%d", paramCount)
		params = append(params, orderType)
		paramCount++
	}

	if assignedTo, ok := filters["assigned_to"].(int64); ok && assignedTo > 0 {
		baseQuery += fmt.Sprintf(" AND mo.assigned_to = $%d", paramCount)
		countQuery += fmt.Sprintf(" AND mo.assigned_to = $%d", paramCount)
		params = append(params, assignedTo)
		paramCount++
	}

	if len(stationIDs) > 0 {
		placeholders := make([]string, len(stationIDs))
		for i := range stationIDs {
			placeholders[i] = fmt.Sprintf("$%d", paramCount)
			params = append(params, stationIDs[i])
			paramCount++
		}

		placeholdersStr := strings.Join(placeholders, ",")
		baseQuery += " AND mo.station_id IN (" + placeholdersStr + ")"
		countQuery += " AND mo.station_id IN (" + placeholdersStr + ")"
	}

	// Busca período
	if startDate, ok := filters["start_date"].(time.Time); ok {
		baseQuery += fmt.Sprintf(" AND mo.created_at >= $%d", paramCount)
		countQuery += fmt.Sprintf(" AND mo.created_at >= $%d", paramCount)
		params = append(params, startDate)
		paramCount++
	}

	if endDate, ok := filters["end_date"].(time.Time); ok {
		baseQuery += fmt.Sprintf(" AND mo.created_at <= $%d", paramCount)
		countQuery += fmt.Sprintf(" AND mo.created_at <= $%d", paramCount)
		params = append(params, endDate)
		paramCount++
	}

	// Busca por texto
	if search, ok := filters["search"].(string); ok && search != "" {
		searchTerm := "%" + search + "%"
		baseQuery += fmt.Sprintf(" AND (mo.title ILIKE $%d OR mo.description ILIKE $%d OR mo.equipment ILIKE $%d)",
			paramCount, paramCount+1, paramCount+2)
		countQuery += fmt.Sprintf(" AND (mo.title ILIKE $%d OR mo.description ILIKE $%d OR mo.equipment ILIKE $%d)",
			paramCount, paramCount+1, paramCount+2)
		params = append(params, searchTerm, searchTerm, searchTerm)
		paramCount += 3
	}

	// Contagem total
	var total int
	err := r.db.QueryRow(countQuery, params...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("erro ao contar registros: %v", err)
	}

	// Adiciona ordenação e paginação
	baseQuery += fmt.Sprintf(" ORDER BY mo.created_at DESC LIMIT $%d OFFSET $%d", paramCount, paramCount+1)
	offset := (page - 1) * limit
	params = append(params, limit, offset)

	// Executa a consulta principal
	rows, err := r.db.Query(baseQuery, params...)
	if err != nil {
		return nil, 0, fmt.Errorf("erro ao buscar ordens: %v", err)
	}
	defer rows.Close()

	orders := []models.MaintenanceOrderDetailed{}
	for rows.Next() {
		var order models.MaintenanceOrderDetailed
		var assignedTo sql.NullInt64
		var assignedToName sql.NullString
		var startDate sql.NullTime
		var completedDate sql.NullTime

		if err := rows.Scan(
			&order.ID,
			&order.Title,
			&order.Type,
			&order.StationID,
			&order.StationName,
			&order.Status,
			&order.Priority,
			&order.CreatedBy,
			&order.CreatedByName,
			&assignedTo,
			&assignedToName,
			&order.Equipment,
			&startDate,
			&completedDate,
			&order.CreatedAt,
			&order.UpdatedAt,
		); err != nil {
			return nil, 0, fmt.Errorf("erro ao ler ordem: %v", err)
		}

		if assignedTo.Valid {
			assignedToUint := uint(assignedTo.Int64)
			order.AssignedTo = &assignedToUint
			order.AssignedToName = assignedToName.String
		}

		if startDate.Valid {
			order.StartDate = &startDate.Time
		}

		if completedDate.Valid {
			order.CompletedDate = &completedDate.Time
		}

		orders = append(orders, order)
	}

	if err := rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("erro ao ler linhas: %v", err)
	}

	return orders, total, nil
}

// GetMetricsByStatus retorna a contagem de ordens por status, filtrando por estação
func (r *SQLMaintenanceRepository) GetMetricsByStatus(stationIDs []int64) (map[models.OrderStatus]int, error) {
	query := `
                SELECT status, COUNT(*) as count
                FROM maintenance_orders
                WHERE 1=1
        `

	var params []interface{}
	paramCount := 1

	// Adiciona filtro de postos se necessário
	if len(stationIDs) > 0 {
		placeholders := make([]string, len(stationIDs))
		for i := range stationIDs {
			placeholders[i] = fmt.Sprintf("$%d", paramCount)
			params = append(params, stationIDs[i])
			paramCount++
		}

		placeholdersStr := strings.Join(placeholders, ",")
		query += " AND station_id IN (" + placeholdersStr + ")"
	}

	query += " GROUP BY status"

	rows, err := r.db.Query(query, params...)
	if err != nil {
		return nil, fmt.Errorf("erro ao obter métricas por status: %v", err)
	}
	defer rows.Close()

	metrics := make(map[models.OrderStatus]int)
	for rows.Next() {
		var status models.OrderStatus
		var count int

		if err := rows.Scan(&status, &count); err != nil {
			return nil, fmt.Errorf("erro ao ler métricas: %v", err)
		}

		metrics[status] = count
	}

	// Garante que todos os status estejam presentes no retorno
	allStatuses := []models.OrderStatus{
		models.StatusPending,
		models.StatusInProgress,
		models.StatusCompleted,
		models.StatusCancelled,
	}

	for _, status := range allStatuses {
		if _, exists := metrics[status]; !exists {
			metrics[status] = 0
		}
	}

	return metrics, nil
}

// GetRecentOrders retorna as ordens mais recentes com detalhes, filtrando por estação
func (r *SQLMaintenanceRepository) GetRecentOrders(stationIDs []int64, limit int) ([]models.MaintenanceOrderDetailed, error) {
	query := `
                SELECT
                        mo.id, mo.title, mo.type, mo.station_id, s.name AS station_name,
                        mo.status, mo.priority, mo.created_by, creator.name AS created_by_name,
                        mo.assigned_to, tech.name AS assigned_to_name, mo.equipment,
                        mo.created_at, mo.updated_at
                FROM maintenance_orders mo
                JOIN stations s ON mo.station_id = s.id
                JOIN users creator ON mo.created_by = creator.id
                LEFT JOIN users tech ON mo.assigned_to = tech.id
                WHERE 1=1
        `

	var params []interface{}
	paramCount := 1

	// Adiciona filtro de postos se necessário
	if len(stationIDs) > 0 {
		placeholders := make([]string, len(stationIDs))
		for i := range stationIDs {
			placeholders[i] = fmt.Sprintf("$%d", paramCount)
			params = append(params, stationIDs[i])
			paramCount++
		}

		placeholdersStr := strings.Join(placeholders, ",")
		query += " AND mo.station_id IN (" + placeholdersStr + ")"
	}

	query += fmt.Sprintf(" ORDER BY mo.created_at DESC LIMIT $%d", paramCount)
	params = append(params, limit)

	rows, err := r.db.Query(query, params...)
	if err != nil {
		return nil, fmt.Errorf("erro ao buscar ordens recentes: %v", err)
	}
	defer rows.Close()

	orders := []models.MaintenanceOrderDetailed{}
	for rows.Next() {
		var order models.MaintenanceOrderDetailed
		var assignedTo sql.NullInt64
		var assignedToName sql.NullString

		if err := rows.Scan(
			&order.ID,
			&order.Title,
			&order.Type,
			&order.StationID,
			&order.StationName,
			&order.Status,
			&order.Priority,
			&order.CreatedBy,
			&order.CreatedByName,
			&assignedTo,
			&assignedToName,
			&order.Equipment,
			&order.CreatedAt,
			&order.UpdatedAt,
		); err != nil {
			return nil, fmt.Errorf("erro ao ler ordem: %v", err)
		}

		if assignedTo.Valid {
			assignedToUint := uint(assignedTo.Int64)
			order.AssignedTo = &assignedToUint
			order.AssignedToName = assignedToName.String
		}

		orders = append(orders, order)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("erro ao ler linhas: %v", err)
	}

	return orders, nil
}

// GetOrdersByPriority retorna a contagem de ordens por prioridade, filtrando por estação
func (r *SQLMaintenanceRepository) GetOrdersByPriority(stationIDs []int64) (map[models.PriorityLevel]int, error) {
	query := `
                SELECT priority, COUNT(*) as count
                FROM maintenance_orders
                WHERE status IN ($1, $2)
        `

	var params []interface{}
	params = append(params, models.StatusPending, models.StatusInProgress)
	paramCount := 3

	// Adiciona filtro de postos se necessário
	if len(stationIDs) > 0 {
		placeholders := make([]string, len(stationIDs))
		for i := range stationIDs {
			placeholders[i] = fmt.Sprintf("$%d", paramCount)
			params = append(params, stationIDs[i])
			paramCount++
		}

		placeholdersStr := strings.Join(placeholders, ",")
		query += " AND station_id IN (" + placeholdersStr + ")"
	}

	query += " GROUP BY priority"

	rows, err := r.db.Query(query, params...)
	if err != nil {
		return nil, fmt.Errorf("erro ao obter métricas por prioridade: %v", err)
	}
	defer rows.Close()

	metrics := make(map[models.PriorityLevel]int)
	for rows.Next() {
		var priority models.PriorityLevel
		var count int

		if err := rows.Scan(&priority, &count); err != nil {
			return nil, fmt.Errorf("erro ao ler métricas: %v", err)
		}

		metrics[priority] = count
	}

	// Garante que todas as prioridades estejam presentes no retorno
	allPriorities := []models.PriorityLevel{
		models.PriorityLow,
		models.PriorityMedium,
		models.PriorityHigh,
		models.PriorityCritical,
	}

	for _, priority := range allPriorities {
		if _, exists := metrics[priority]; !exists {
			metrics[priority] = 0
		}
	}

	return metrics, nil
}

// GetMaterialByID retorna um material pelo ID
func (r *SQLMaintenanceRepository) GetMaterialByID(materialID uint) (*models.Material, error) {
	var material models.Material
	query := `
        SELECT id, name, quantity, unit, cost, added_by, created_at 
        FROM maintenance_materials 
        WHERE id = $1
    `
	err := r.db.QueryRow(query, materialID).Scan(
		&material.ID,
		&material.Name,
		&material.Quantity,
		&material.Unit,
		&material.Cost,
		&material.AddedByUserID,
		&material.CreatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("erro ao buscar material: %v", err)
	}
	return &material, nil
}

// GetNoteByID retorna uma nota pelo ID
func (r *SQLMaintenanceRepository) GetNoteByID(noteID uint) (*models.Note, error) {
	var note models.Note
	query := `
        SELECT id, content, created_at 
        FROM maintenance_notes 
        WHERE id = $1
    `
	err := r.db.QueryRow(query, noteID).Scan(
		&note.ID,
		&note.Content,
		&note.CreatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("erro ao buscar nota: %v", err)
	}
	return &note, nil
}
