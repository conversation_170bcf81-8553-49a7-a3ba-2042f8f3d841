package repository

import (
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// NotificationRepository define a interface para operações com notificações
type NotificationRepository interface {
	Create(notification *models.Notification) error
	GetByUserID(userID uint) ([]models.Notification, error)
	MarkAsRead(notificationID uint) error
	GetUnreadCount(userID uint) (int64, error)
}

// NotificationRepositoryImpl é a implementação concreta do repositório de notificações
type NotificationRepositoryImpl struct {
	db *gorm.DB
}

// NewNotificationRepository cria um novo repositório de notificações
func NewNotificationRepository(db *gorm.DB) NotificationRepository {
	return &NotificationRepositoryImpl{
		db: db,
	}
}

// Create cria uma nova notificação no banco de dados
func (r *NotificationRepositoryImpl) Create(notification *models.Notification) error {
	return r.db.Create(notification).Error
}

// GetByUserID retorna as notificações de um usuário
func (r *NotificationRepositoryImpl) GetByUserID(userID uint) ([]models.Notification, error) {
	var notifications []models.Notification
	err := r.db.Where("user_id = ?", userID).Order("created_at desc").Find(&notifications).Error
	return notifications, err
}

// MarkAsRead marca uma notificação como lida
func (r *NotificationRepositoryImpl) MarkAsRead(notificationID uint) error {
	return r.db.Model(&models.Notification{}).Where("id = ?", notificationID).Update("read", true).Error
}

// GetUnreadCount retorna o número de notificações não lidas de um usuário
func (r *NotificationRepositoryImpl) GetUnreadCount(userID uint) (int64, error) {
	var count int64
	err := r.db.Model(&models.Notification{}).Where("user_id = ? AND read = ?", userID, false).Count(&count).Error
	return count, err
}
