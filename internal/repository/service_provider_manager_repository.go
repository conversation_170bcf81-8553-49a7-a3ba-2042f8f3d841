package repository

import (
	"tradicao/internal/models"
)

// ServiceProviderManagerRepository define a interface para operações com gestores de prestadoras
type ServiceProviderManagerRepository interface {
	FindAll() ([]models.ServiceProviderManager, error)
	FindByID(id uint) (*models.ServiceProviderManager, error)
	FindByUserID(userID uint) ([]models.ServiceProviderManager, error)
	FindByProviderID(providerID uint) ([]models.ServiceProviderManager, error)
	Create(manager *models.ServiceProviderManager) error
	Update(manager *models.ServiceProviderManager) error
	Delete(id uint) error
	IsProviderManager(providerID uint, userID uint) (bool, error)
}
