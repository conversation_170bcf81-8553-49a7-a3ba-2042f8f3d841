package repository

import (
	"database/sql"
	"errors"
	"fmt"
	"time"
	"tradicao/internal/models"
)

// SQLNotificationRepository implementação SQL da interface NotificationRepository
type SQLNotificationRepository struct {
	db *sql.DB
}

// NewSQLNotificationRepository cria um novo repositório de notificações SQL
func NewSQLNotificationRepository(db *sql.DB) *SQLNotificationRepository {
	return &SQLNotificationRepository{db: db}
}

// SaveSubscription salva uma assinatura de notificação
func (r *SQLNotificationRepository) SaveSubscription(subscription models.NotificationSubscription) error {
	// Verifica se já existe uma assinatura com esse endpoint
	var id int64
	err := r.db.QueryRow(
		"SELECT id FROM notification_subscriptions WHERE user_id = $1 AND subscription->>'endpoint' = $2",
		subscription.UserID, subscription.Subscription.Endpoint,
	).Scan(&id)

	if err == nil {
		// Atualizar a assinatura existente
		_, err = r.db.Exec(
			"UPDATE notification_subscriptions SET user_role = $1, subscription = $2, updated_at = $3 WHERE id = $4",
			subscription.UserRole, subscription.Subscription, time.Now(), id,
		)
		return err
	}

	if !errors.Is(err, sql.ErrNoRows) {
		return err
	}

	// Inserir nova assinatura
	_, err = r.db.Exec(
		"INSERT INTO notification_subscriptions (user_id, user_role, subscription, created_at, updated_at) VALUES ($1, $2, $3, $4, $5)",
		subscription.UserID, subscription.UserRole, subscription.Subscription, time.Now(), time.Now(),
	)
	return err
}

// DeleteSubscriptionByEndpoint remove uma assinatura pelo endpoint
func (r *SQLNotificationRepository) DeleteSubscriptionByEndpoint(endpoint string) error {
	_, err := r.db.Exec(
		"DELETE FROM notification_subscriptions WHERE subscription->>'endpoint' = $1",
		endpoint,
	)
	return err
}

// GetSubscriptionsByUserID obtém todas as assinaturas de um usuário
func (r *SQLNotificationRepository) GetSubscriptionsByUserID(userID int64) ([]models.NotificationSubscription, error) {
	rows, err := r.db.Query(
		"SELECT id, user_id, user_role, subscription, created_at, updated_at FROM notification_subscriptions WHERE user_id = $1",
		userID,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var subscriptions []models.NotificationSubscription
	for rows.Next() {
		var sub models.NotificationSubscription
		if err := rows.Scan(&sub.ID, &sub.UserID, &sub.UserRole, &sub.Subscription, &sub.CreatedAt, &sub.UpdatedAt); err != nil {
			return nil, err
		}
		subscriptions = append(subscriptions, sub)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return subscriptions, nil
}

// GetSubscriptionsByRole obtém todas as assinaturas de usuários com um papel específico
func (r *SQLNotificationRepository) GetSubscriptionsByRole(role string) ([]models.NotificationSubscription, error) {
	rows, err := r.db.Query(
		"SELECT id, user_id, user_role, subscription, created_at, updated_at FROM notification_subscriptions WHERE user_role = $1",
		role,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var subscriptions []models.NotificationSubscription
	for rows.Next() {
		var sub models.NotificationSubscription
		if err := rows.Scan(&sub.ID, &sub.UserID, &sub.UserRole, &sub.Subscription, &sub.CreatedAt, &sub.UpdatedAt); err != nil {
			return nil, err
		}
		subscriptions = append(subscriptions, sub)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return subscriptions, nil
}

// SaveNotification salva uma notificação no banco de dados
func (r *SQLNotificationRepository) SaveNotification(notification models.Notification) error {
	stmt, err := r.db.Prepare(
		"INSERT INTO notifications (user_id, title, body, url, order_id, action, actions, read, created_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING id",
	)
	if err != nil {
		return err
	}
	defer stmt.Close()

	err = stmt.QueryRow(
		notification.UserID,
		notification.Title,
		notification.Body,
		notification.URL,
		notification.OrderID,
		notification.Action,
		notification.Actions,
		notification.Read,
		notification.CreatedAt,
	).Scan(&notification.ID)

	return err
}

// GetNotificationsByUserID obtém notificações de um usuário com paginação
func (r *SQLNotificationRepository) GetNotificationsByUserID(userID int64, limit, offset int) ([]models.Notification, error) {
	rows, err := r.db.Query(
		"SELECT id, user_id, title, body, url, order_id, action, actions, read, created_at FROM notifications WHERE user_id = $1 ORDER BY created_at DESC LIMIT $2 OFFSET $3",
		userID, limit, offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var notifications []models.Notification
	for rows.Next() {
		var notification models.Notification
		if err := rows.Scan(
			&notification.ID,
			&notification.UserID,
			&notification.Title,
			&notification.Body,
			&notification.URL,
			&notification.OrderID,
			&notification.Action,
			&notification.Actions,
			&notification.Read,
			&notification.CreatedAt,
		); err != nil {
			return nil, err
		}
		notifications = append(notifications, notification)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return notifications, nil
}

// GetNotificationByID obtém uma notificação pelo ID
func (r *SQLNotificationRepository) GetNotificationByID(id int64) (*models.Notification, error) {
	var notification models.Notification
	err := r.db.QueryRow(
		"SELECT id, user_id, title, body, url, order_id, action, actions, read, created_at FROM notifications WHERE id = $1",
		id,
	).Scan(
		&notification.ID,
		&notification.UserID,
		&notification.Title,
		&notification.Body,
		&notification.URL,
		&notification.OrderID,
		&notification.Action,
		&notification.Actions,
		&notification.Read,
		&notification.CreatedAt,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("notificação não encontrada: %w", err)
		}
		return nil, err
	}

	return &notification, nil
}

// MarkNotificationAsRead marca uma notificação como lida
func (r *SQLNotificationRepository) MarkNotificationAsRead(id int64) error {
	_, err := r.db.Exec("UPDATE notifications SET read = true WHERE id = $1", id)
	return err
}

// DeleteNotification remove uma notificação
func (r *SQLNotificationRepository) DeleteNotification(id int64) error {
	_, err := r.db.Exec("DELETE FROM notifications WHERE id = $1", id)
	return err
}

// CountUnreadNotifications conta o número de notificações não lidas de um usuário
func (r *SQLNotificationRepository) CountUnreadNotifications(userID int64) (int, error) {
	var count int
	err := r.db.QueryRow("SELECT COUNT(*) FROM notifications WHERE user_id = $1 AND read = false", userID).Scan(&count)
	return count, err
}
