package repository

import (
	"context"

	"tradicao/internal/ent"
	"tradicao/internal/models"
)

type EntBranchRepository struct {
	client *ent.Client
}

func NewEntBranchRepository(client *ent.Client) *EntBranchRepository {
	return &EntBranchRepository{client: client}
}

func (r *EntBranchRepository) Create(ctx context.Context, branch *models.Branch) error {
	_, err := r.client.Branch.Create().
		SetName(branch.Name).
		SetAddress(branch.Address).
		SetCity(branch.City).
		SetState(branch.State).
		SetZipCode(branch.ZipCode).
		SetPhone(branch.Phone).
		SetActive(branch.IsActive).
		Save(ctx)

	return err
}

func (r *EntBranchRepository) FindByID(ctx context.Context, id int) (*models.Branch, error) {
	branch, err := r.client.Branch.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return &models.Branch{
		ID:          uint(branch.ID),
		Name:        branch.Name,
		Address:     branch.Address,
		City:        branch.City,
		State:       branch.State,
		ZipCode:     branch.ZipCode,
		Phone:       branch.Phone,
		Email:       "",
		ContactInfo: "",
		IsActive:    branch.Active,
		CreatedAt:   branch.CreatedAt,
		UpdatedAt:   branch.UpdatedAt,
	}, nil
}

func (r *EntBranchRepository) Update(ctx context.Context, branch *models.Branch) error {
	_, err := r.client.Branch.UpdateOneID(int(branch.ID)).
		SetName(branch.Name).
		SetAddress(branch.Address).
		SetCity(branch.City).
		SetState(branch.State).
		SetZipCode(branch.ZipCode).
		SetPhone(branch.Phone).
		SetActive(branch.IsActive).
		Save(ctx)

	return err
}

func (r *EntBranchRepository) Delete(ctx context.Context, id int) error {
	return r.client.Branch.DeleteOneID(id).Exec(ctx)
}

func (r *EntBranchRepository) List(ctx context.Context) ([]*models.Branch, error) {
	branches, err := r.client.Branch.Query().All(ctx)
	if err != nil {
		return nil, err
	}

	var result []*models.Branch
	for _, branch := range branches {
		result = append(result, &models.Branch{
			ID:          uint(branch.ID),
			Name:        branch.Name,
			Address:     branch.Address,
			City:        branch.City,
			State:       branch.State,
			ZipCode:     branch.ZipCode,
			Phone:       branch.Phone,
			Email:       "",
			ContactInfo: "",
			IsActive:    branch.Active,
			CreatedAt:   branch.CreatedAt,
			UpdatedAt:   branch.UpdatedAt,
		})
	}

	return result, nil
}
