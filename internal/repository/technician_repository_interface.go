package repository

import (
	"time"
	"tradicao/internal/models"
)

// TechnicianRepositoryInterface define a interface para operações relacionadas a técnicos
type TechnicianRepositoryInterface interface {
	// Especialidades
	CreateSpecialty(specialty *models.TechnicianSpecialty) error
	ListSpecialties() ([]models.TechnicianSpecialty, error)
	UpdateSpecialty(specialty *models.TechnicianSpecialty) error
	DeleteSpecialty(id uint) error
	GetSpecialty(id uint) (*models.TechnicianSpecialty, error)

	// Vinculação com filiais
	CreateBranchAssociation(association *models.TechnicianBranch) error
	UpdateBranchAssociation(association *models.TechnicianBranch) error
	DeleteBranchAssociation(technicianID, branchID uint) error
	GetBranchAssociation(technicianID, branchID uint) (*models.TechnicianBranch, error)
	ListBranchAssociations(technicianID uint) ([]models.TechnicianBranch, error)

	// Histórico de manutenções
	CreateMaintenanceHistory(history *models.MaintenanceHistory) error
	ListMaintenanceHistory(technicianID uint, startDate, endDate time.Time) ([]models.MaintenanceHistory, error)
	GetMaintenanceHistory(id uint) (*models.MaintenanceHistory, error)
}
