package repository

import (
	"context"
	"time"

	"tradicao/internal/ent"
	"tradicao/internal/ent/equipment"
	"tradicao/internal/models"
)

type EntEquipmentRepository struct {
	client *ent.Client
}

func NewEntEquipmentRepository(client *ent.Client) *EntEquipmentRepository {
	return &EntEquipmentRepository{client: client}
}

func (r *EntEquipmentRepository) Create(ctx context.Context, equipment *models.Equipment) error {
	var installationDate time.Time
	if equipment.InstallationDate != nil {
		installationDate = *equipment.InstallationDate
	} else {
		installationDate = time.Now()
	}

	var lastMaintenanceDate time.Time
	if equipment.LastMaintenance != nil {
		lastMaintenanceDate = *equipment.LastMaintenance
	} else {
		lastMaintenanceDate = time.Now()
	}

	_, err := r.client.Equipment.Create().
		SetName(equipment.Name).
		SetType(equipment.Type).
		SetModel(equipment.Model).
		SetSerialNumber(equipment.SerialNumber).
		SetInstallationDate(installationDate).
		SetLastMaintenanceDate(lastMaintenanceDate).
		SetActive(true).
		SetBranchID(int(equipment.BranchID)).
		Save(ctx)

	return err
}

func (r *EntEquipmentRepository) FindByID(ctx context.Context, id uint) (*models.Equipment, error) {
	e, err := r.client.Equipment.Get(ctx, int(id))
	if err != nil {
		return nil, err
	}

	installationDate := &e.InstallationDate
	lastMaintenance := &e.LastMaintenanceDate

	return &models.Equipment{
		ID:               uint(e.ID),
		Name:             e.Name,
		SerialNumber:     e.SerialNumber,
		Model:            e.Model,
		Type:             e.Type,
		InstallationDate: installationDate,
		LastMaintenance:  lastMaintenance,
		Status:           "ativo",
		BranchID:         uint(e.BranchID),
		CreatedAt:        e.CreatedAt,
		UpdatedAt:        e.UpdatedAt,
	}, nil
}

func (r *EntEquipmentRepository) Update(ctx context.Context, equipment *models.Equipment) error {
	var installationDate time.Time
	if equipment.InstallationDate != nil {
		installationDate = *equipment.InstallationDate
	}

	var lastMaintenanceDate time.Time
	if equipment.LastMaintenance != nil {
		lastMaintenanceDate = *equipment.LastMaintenance
	}

	_, err := r.client.Equipment.UpdateOneID(int(equipment.ID)).
		SetName(equipment.Name).
		SetType(equipment.Type).
		SetModel(equipment.Model).
		SetSerialNumber(equipment.SerialNumber).
		SetInstallationDate(installationDate).
		SetLastMaintenanceDate(lastMaintenanceDate).
		SetActive(true).
		SetBranchID(int(equipment.BranchID)).
		Save(ctx)

	return err
}

func (r *EntEquipmentRepository) Delete(ctx context.Context, id uint) error {
	return r.client.Equipment.DeleteOneID(int(id)).Exec(ctx)
}

func (r *EntEquipmentRepository) List(ctx context.Context) ([]*models.Equipment, error) {
	equipments, err := r.client.Equipment.Query().All(ctx)
	if err != nil {
		return nil, err
	}

	var result []*models.Equipment
	for _, e := range equipments {
		installationDate := &e.InstallationDate
		lastMaintenance := &e.LastMaintenanceDate

		result = append(result, &models.Equipment{
			ID:               uint(e.ID),
			Name:             e.Name,
			SerialNumber:     e.SerialNumber,
			Model:            e.Model,
			Type:             e.Type,
			InstallationDate: installationDate,
			LastMaintenance:  lastMaintenance,
			Status:           "ativo",
			BranchID:         uint(e.BranchID),
			CreatedAt:        e.CreatedAt,
			UpdatedAt:        e.UpdatedAt,
		})
	}

	return result, nil
}

func (r *EntEquipmentRepository) FindByBranch(ctx context.Context, branchID uint) ([]*models.Equipment, error) {
	equipments, err := r.client.Equipment.Query().
		Where(equipment.BranchID(int(branchID))).
		All(ctx)
	if err != nil {
		return nil, err
	}

	var result []*models.Equipment
	for _, e := range equipments {
		installationDate := &e.InstallationDate
		lastMaintenance := &e.LastMaintenanceDate

		result = append(result, &models.Equipment{
			ID:               uint(e.ID),
			Name:             e.Name,
			SerialNumber:     e.SerialNumber,
			Model:            e.Model,
			Type:             e.Type,
			InstallationDate: installationDate,
			LastMaintenance:  lastMaintenance,
			Status:           "ativo",
			BranchID:         uint(e.BranchID),
			CreatedAt:        e.CreatedAt,
			UpdatedAt:        e.UpdatedAt,
		})
	}

	return result, nil
}

func (r *EntEquipmentRepository) FindNeedingMaintenance(ctx context.Context, threshold time.Duration) ([]*models.Equipment, error) {
	return r.List(ctx)
}
