package repository

import (
	"tradicao/internal/models"
)

// UserRepository define a interface para operações com usuários
type UserRepository interface {
	Create(user models.User) (models.User, error)
	FindByID(id uint) (models.User, error)
	FindByEmail(email string) (models.User, error)
	Update(user models.User) (models.User, error)
	Delete(id uint) error
	FindAll() ([]models.User, error)
	CreateAuditLog(log models.AuditLog) error
	UpdateLoginAttempts(userID uint, failed bool) error
	GetSecurityPolicy() (models.SecurityPolicy, error)
	GetUserByID(id int64) (*models.User, error)
	GetUsersByBranchID(branchID int64) ([]models.User, error)

	// Métodos para TOTP / 2FA
	EnableTOTP(userID uint, secret string) error
	DisableTOTP(userID uint) error
	SetTOTPSecret(userID uint, secret string) error
	GetTOTPSecret(userID uint) (string, bool, error)

	// Métodos para política de senha
	UpdatePassword(userID uint, newPassword string) error
	AddPasswordToHistory(userID uint, passwordHash string) error
	IsPasswordInHistory(userID uint, passwordHash string) (bool, error)
	DisableForcePasswordChange(userID uint) error
	EnableForcePasswordChange(userID uint) error

	// Métodos para perfil de usuário
	UpdateAvatar(userID uint, avatarURL string) error
}
