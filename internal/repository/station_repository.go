package repository

import (
	"database/sql"
	"errors"
	"fmt"

	"tradicao/internal/models"
)

// StationRepository gerencia operações de banco de dados relacionadas a postos
type StationRepository struct {
	db *sql.DB
}

// NewStationRepository cria um novo repositório de postos
func NewStationRepository(db *sql.DB) *StationRepository {
	return &StationRepository{db: db}
}

// GetAll retorna todos os postos
func (r *StationRepository) GetAll() ([]models.Station, error) {
	query := `
                SELECT id, name, code, type, address, city, state, postal_code,
                       latitude, longitude, manager_id, phone_number, email,
                       opening_hours, is_active, created_at, updated_at
                FROM stations
                ORDER BY name
        `

	rows, err := r.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("erro ao buscar postos: %v", err)
	}
	defer rows.Close()

	var stations []models.Station
	for rows.Next() {
		var station models.Station
		var latitude, longitude sql.NullFloat64
		var managerID sql.NullInt64
		var email, openingHours sql.NullString

		if err := rows.Scan(
			&station.ID,
			&station.Name,
			&station.Code,
			&station.Type,
			&station.Address,
			&station.City,
			&station.State,
			&station.PostalCode,
			&latitude,
			&longitude,
			&managerID,
			&station.PhoneNumber,
			&email,
			&openingHours,
			&station.IsActive,
			&station.CreatedAt,
			&station.UpdatedAt,
		); err != nil {
			return nil, fmt.Errorf("erro ao ler dados do posto: %v", err)
		}

		if latitude.Valid {
			station.Latitude = &latitude.Float64
		}

		if longitude.Valid {
			station.Longitude = &longitude.Float64
		}

		if managerID.Valid {
			managerIDUint := uint(managerID.Int64)
			station.ManagerID = &managerIDUint
		}

		if email.Valid {
			station.Email = email.String
		}

		if openingHours.Valid {
			station.OpeningHours = openingHours.String
		}

		stations = append(stations, station)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("erro ao ler linhas: %v", err)
	}

	return stations, nil
}

// GetByID retorna um posto pelo ID
func (r *StationRepository) GetByID(id int64) (*models.Station, error) {
	query := `
                SELECT s.id, s.name, s.code, s.type, s.address, s.city, s.state, s.postal_code,
                       s.latitude, s.longitude, s.manager_id, u.name as manager_name, s.phone_number,
                       s.email, s.opening_hours, s.is_active, s.created_at, s.updated_at
                FROM stations s
                LEFT JOIN users u ON s.manager_id = u.id
                WHERE s.id = ?
        `

	var station models.Station
	var latitude, longitude sql.NullFloat64
	var managerID sql.NullInt64
	var managerName, email, openingHours sql.NullString

	err := r.db.QueryRow(query, id).Scan(
		&station.ID,
		&station.Name,
		&station.Code,
		&station.Type,
		&station.Address,
		&station.City,
		&station.State,
		&station.PostalCode,
		&latitude,
		&longitude,
		&managerID,
		&managerName,
		&station.PhoneNumber,
		&email,
		&openingHours,
		&station.IsActive,
		&station.CreatedAt,
		&station.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New("posto não encontrado")
		}
		return nil, fmt.Errorf("erro ao buscar posto: %v", err)
	}

	if latitude.Valid {
		station.Latitude = &latitude.Float64
	}

	if longitude.Valid {
		station.Longitude = &longitude.Float64
	}

	if managerID.Valid {
		managerIDUint := uint(managerID.Int64)
		station.ManagerID = &managerIDUint
	}

	if managerName.Valid {
		station.ManagerName = managerName.String
	}

	if email.Valid {
		station.Email = email.String
	}

	if openingHours.Valid {
		station.OpeningHours = openingHours.String
	}

	return &station, nil
}

// GetAllSummary retorna todos os postos em formato resumido
func (r *StationRepository) GetAllSummary() ([]models.StationSummary, error) {
	query := `
                SELECT id, name, code, city, state, is_active
                FROM stations
                ORDER BY name
        `

	rows, err := r.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("erro ao buscar resumo dos postos: %v", err)
	}
	defer rows.Close()

	var stations []models.StationSummary
	for rows.Next() {
		var station models.StationSummary
		if err := rows.Scan(
			&station.ID,
			&station.Name,
			&station.Code,
			&station.City,
			&station.State,
			&station.IsActive,
		); err != nil {
			return nil, fmt.Errorf("erro ao ler dados do posto: %v", err)
		}

		stations = append(stations, station)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("erro ao ler linhas: %v", err)
	}

	return stations, nil
}

// GetStationsByUserRole retorna os postos associados a um usuário com base em sua função
func (r *StationRepository) GetStationsByUserRole(userID int64, role models.UserRole) ([]models.StationSummary, error) {
	var query string
	var params []interface{}

	switch role {
	case models.RoleAdmin, models.RoleGerente:
		// Administradores e gerentes veem todos os postos
		query = `
                        SELECT id, name, code, city, state, is_active
                        FROM stations
                        ORDER BY name
                `
	case models.RoleTechnician:
		// Técnicos veem todos os postos (podem ser alocados a qualquer posto)
		query = `
                        SELECT id, name, code, city, state, is_active
                        FROM stations
                        ORDER BY name
                `
	case models.RoleFilial:
		// Funcionários de posto veem apenas o posto ao qual estão associados
		query = `
                        SELECT s.id, s.name, s.code, s.city, s.state, s.is_active
                        FROM stations s
                        JOIN users u ON s.id = u.station_id
                        WHERE u.id = ?
                        ORDER BY s.name
                `
		params = append(params, userID)
	default:
		return nil, errors.New("função de usuário inválida")
	}

	var rows *sql.Rows
	var err error

	if len(params) > 0 {
		rows, err = r.db.Query(query, params...)
	} else {
		rows, err = r.db.Query(query)
	}

	if err != nil {
		return nil, fmt.Errorf("erro ao buscar postos por função: %v", err)
	}
	defer rows.Close()

	var stations []models.StationSummary
	for rows.Next() {
		var station models.StationSummary
		if err := rows.Scan(
			&station.ID,
			&station.Name,
			&station.Code,
			&station.City,
			&station.State,
			&station.IsActive,
		); err != nil {
			return nil, fmt.Errorf("erro ao ler dados do posto: %v", err)
		}

		stations = append(stations, station)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("erro ao ler linhas: %v", err)
	}

	return stations, nil
}
