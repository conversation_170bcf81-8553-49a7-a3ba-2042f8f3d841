package repository

import (
	"context"
	"errors"
	"tradicao/internal/models"
)

// Erros comuns
var (
	ErrNotFound      = errors.New("registro não encontrado")
	ErrInvalidID     = errors.New("ID inválido")
	ErrDatabaseError = errors.New("erro de banco de dados")
)

// GenericRepository define uma interface genérica para operações de repositório
// que usa a interface ID abstrata
type GenericRepository[T any] interface {
	// GetByID retorna um registro pelo ID
	GetByID(ctx context.Context, id models.ID) (*T, error)

	// Create cria um novo registro
	Create(ctx context.Context, entity *T) error

	// Update atualiza um registro existente
	Update(ctx context.Context, id models.ID, entity *T) error

	// Delete remove um registro
	Delete(ctx context.Context, id models.ID) error

	// List retorna uma lista de registros com paginação
	List(ctx context.Context, offset, limit int) ([]T, int, error)
}

// IDConverter define uma interface para conversão entre tipos de ID
type IDConverter interface {
	// ToNumericID converte um ID para NumericID
	ToNumericID(id models.ID) (models.NumericID, error)

	// FromString converte uma string para ID
	FromString(idStr string) (models.ID, error)
}

// DefaultIDConverter implementa a interface IDConverter
type DefaultIDConverter struct{}

// ToNumericID converte um ID para NumericID
func (c DefaultIDConverter) ToNumericID(id models.ID) (models.NumericID, error) {
	return models.ConvertToNumericID(id)
}

// FromString converte uma string para ID
func (c DefaultIDConverter) FromString(idStr string) (models.ID, error) {
	return models.ParseID(idStr)
}
