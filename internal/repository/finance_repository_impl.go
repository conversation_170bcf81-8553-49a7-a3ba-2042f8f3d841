package repository

import (
	"context"
	"database/sql"
	"errors"
	"time"

	"tradicao/internal/models"
)

// SQLFinanceRepository implementa a interface FinanceRepository usando SQL
type SQLFinanceRepository struct {
	db *sql.DB
}

// NewFinanceRepository cria uma nova instância do repositório financeiro
func NewFinanceRepository(db *sql.DB) FinanceRepository {
	return &SQLFinanceRepository{db: db}
}

// ListPayments retorna a lista de pagamentos com base nos filtros
func (r *SQLFinanceRepository) ListPayments(ctx context.Context, filters *models.PaymentFilters) ([]*models.Payment, error) {
	query := `SELECT id, description, amount, due_date, status, created_at, updated_at 
			  FROM payments WHERE 1=1`
	args := []interface{}{}

	if filters != nil {
		if filters.Status != "" {
			query += " AND status = ?"
			args = append(args, filters.Status)
		}
		if !filters.StartDate.IsZero() {
			query += " AND due_date >= ?"
			args = append(args, filters.StartDate)
		}
		if !filters.EndDate.IsZero() {
			query += " AND due_date <= ?"
			args = append(args, filters.EndDate)
		}
	}

	query += " ORDER BY due_date DESC"

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var payments []*models.Payment
	for rows.Next() {
		payment := &models.Payment{}
		err := rows.Scan(
			&payment.ID,
			&payment.Description,
			&payment.Amount,
			&payment.DueDate,
			&payment.Status,
			&payment.CreatedAt,
			&payment.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		payments = append(payments, payment)
	}

	return payments, nil
}

// GetPayment retorna um pagamento pelo ID
func (r *SQLFinanceRepository) GetPayment(ctx context.Context, id int64) (*models.Payment, error) {
	query := `SELECT id, description, amount, due_date, status, created_at, updated_at 
			  FROM payments WHERE id = ?`

	payment := &models.Payment{}
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&payment.ID,
		&payment.Description,
		&payment.Amount,
		&payment.DueDate,
		&payment.Status,
		&payment.CreatedAt,
		&payment.UpdatedAt,
	)
	if err == sql.ErrNoRows {
		return nil, errors.New("payment not found")
	}
	if err != nil {
		return nil, err
	}

	return payment, nil
}

// CreatePayment cria um novo pagamento
func (r *SQLFinanceRepository) CreatePayment(ctx context.Context, payment *models.Payment) error {
	query := `INSERT INTO payments (description, amount, due_date, status, created_at, updated_at) 
			  VALUES (?, ?, ?, ?, ?, ?)`

	now := time.Now()
	payment.CreatedAt = now
	payment.UpdatedAt = now

	result, err := r.db.ExecContext(ctx, query,
		payment.Description,
		payment.Amount,
		payment.DueDate,
		payment.Status,
		payment.CreatedAt,
		payment.UpdatedAt,
	)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}
	payment.ID = id

	return nil
}

// UpdatePayment atualiza um pagamento existente
func (r *SQLFinanceRepository) UpdatePayment(ctx context.Context, payment *models.Payment) error {
	query := `UPDATE payments 
			  SET description = ?, amount = ?, due_date = ?, status = ?, updated_at = ? 
			  WHERE id = ?`

	payment.UpdatedAt = time.Now()

	_, err := r.db.ExecContext(ctx, query,
		payment.Description,
		payment.Amount,
		payment.DueDate,
		payment.Status,
		payment.UpdatedAt,
		payment.ID,
	)
	return err
}

// DeletePayment remove um pagamento pelo ID
func (r *SQLFinanceRepository) DeletePayment(ctx context.Context, id int64) error {
	query := `DELETE FROM payments WHERE id = ?`
	_, err := r.db.ExecContext(ctx, query, id)
	return err
}

// ListInvoices retorna a lista de notas fiscais com base nos filtros
func (r *SQLFinanceRepository) ListInvoices(ctx context.Context, filters *models.InvoiceFilters) ([]*models.Invoice, error) {
	query := `SELECT id, number, amount, issue_date, due_date, status, created_at, updated_at 
			  FROM invoices WHERE 1=1`
	args := []interface{}{}

	if filters != nil {
		if filters.Status != "" {
			query += " AND status = ?"
			args = append(args, filters.Status)
		}
		if !filters.StartDate.IsZero() {
			query += " AND issue_date >= ?"
			args = append(args, filters.StartDate)
		}
		if !filters.EndDate.IsZero() {
			query += " AND issue_date <= ?"
			args = append(args, filters.EndDate)
		}
	}

	query += " ORDER BY issue_date DESC"

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var invoices []*models.Invoice
	for rows.Next() {
		invoice := &models.Invoice{}
		err := rows.Scan(
			&invoice.ID,
			&invoice.Number,
			&invoice.Amount,
			&invoice.IssueDate,
			&invoice.DueDate,
			&invoice.Status,
			&invoice.CreatedAt,
			&invoice.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		invoices = append(invoices, invoice)
	}

	return invoices, nil
}

// GetInvoice retorna uma nota fiscal pelo ID
func (r *SQLFinanceRepository) GetInvoice(ctx context.Context, id int64) (*models.Invoice, error) {
	query := `SELECT id, number, amount, issue_date, due_date, status, created_at, updated_at 
			  FROM invoices WHERE id = ?`

	invoice := &models.Invoice{}
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&invoice.ID,
		&invoice.Number,
		&invoice.Amount,
		&invoice.IssueDate,
		&invoice.DueDate,
		&invoice.Status,
		&invoice.CreatedAt,
		&invoice.UpdatedAt,
	)
	if err == sql.ErrNoRows {
		return nil, errors.New("invoice not found")
	}
	if err != nil {
		return nil, err
	}

	return invoice, nil
}

// CreateInvoice cria uma nova nota fiscal
func (r *SQLFinanceRepository) CreateInvoice(ctx context.Context, invoice *models.Invoice) error {
	query := `INSERT INTO invoices (number, amount, issue_date, due_date, status, created_at, updated_at) 
			  VALUES (?, ?, ?, ?, ?, ?, ?)`

	now := time.Now()
	invoice.CreatedAt = now
	invoice.UpdatedAt = now

	result, err := r.db.ExecContext(ctx, query,
		invoice.Number,
		invoice.Amount,
		invoice.IssueDate,
		invoice.DueDate,
		invoice.Status,
		invoice.CreatedAt,
		invoice.UpdatedAt,
	)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}
	invoice.ID = id

	return nil
}

// UpdateInvoice atualiza uma nota fiscal existente
func (r *SQLFinanceRepository) UpdateInvoice(ctx context.Context, invoice *models.Invoice) error {
	query := `UPDATE invoices 
			  SET number = ?, amount = ?, issue_date = ?, due_date = ?, status = ?, updated_at = ? 
			  WHERE id = ?`

	invoice.UpdatedAt = time.Now()

	_, err := r.db.ExecContext(ctx, query,
		invoice.Number,
		invoice.Amount,
		invoice.IssueDate,
		invoice.DueDate,
		invoice.Status,
		invoice.UpdatedAt,
		invoice.ID,
	)
	return err
}

// DeleteInvoice remove uma nota fiscal pelo ID
func (r *SQLFinanceRepository) DeleteInvoice(ctx context.Context, id int64) error {
	query := `DELETE FROM invoices WHERE id = ?`
	_, err := r.db.ExecContext(ctx, query, id)
	return err
}

// ListBudgets retorna a lista de orçamentos com base nos filtros
func (r *SQLFinanceRepository) ListBudgets(ctx context.Context, filters *models.BudgetFilters) ([]*models.Budget, error) {
	query := `SELECT id, category, amount, period_start, period_end, created_at, updated_at 
			  FROM budgets WHERE 1=1`
	args := []interface{}{}

	if filters != nil {
		if !filters.StartDate.IsZero() {
			query += " AND period_start >= ?"
			args = append(args, filters.StartDate)
		}
		if !filters.EndDate.IsZero() {
			query += " AND period_end <= ?"
			args = append(args, filters.EndDate)
		}
	}

	query += " ORDER BY period_start DESC"

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var budgets []*models.Budget
	for rows.Next() {
		budget := &models.Budget{}
		err := rows.Scan(
			&budget.ID,
			&budget.Category,
			&budget.Amount,
			&budget.PeriodStart,
			&budget.PeriodEnd,
			&budget.CreatedAt,
			&budget.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		budgets = append(budgets, budget)
	}

	return budgets, nil
}

// GetBudget retorna um orçamento pelo ID
func (r *SQLFinanceRepository) GetBudget(ctx context.Context, id int64) (*models.Budget, error) {
	query := `SELECT id, category, amount, period_start, period_end, created_at, updated_at 
			  FROM budgets WHERE id = ?`

	budget := &models.Budget{}
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&budget.ID,
		&budget.Category,
		&budget.Amount,
		&budget.PeriodStart,
		&budget.PeriodEnd,
		&budget.CreatedAt,
		&budget.UpdatedAt,
	)
	if err == sql.ErrNoRows {
		return nil, errors.New("budget not found")
	}
	if err != nil {
		return nil, err
	}

	return budget, nil
}

// CreateBudget cria um novo orçamento
func (r *SQLFinanceRepository) CreateBudget(ctx context.Context, budget *models.Budget) error {
	query := `INSERT INTO budgets (category, amount, period_start, period_end, created_at, updated_at) 
			  VALUES (?, ?, ?, ?, ?, ?)`

	now := time.Now()
	budget.CreatedAt = now
	budget.UpdatedAt = now

	result, err := r.db.ExecContext(ctx, query,
		budget.Category,
		budget.Amount,
		budget.PeriodStart,
		budget.PeriodEnd,
		budget.CreatedAt,
		budget.UpdatedAt,
	)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}
	budget.ID = id

	return nil
}

// UpdateBudget atualiza um orçamento existente
func (r *SQLFinanceRepository) UpdateBudget(ctx context.Context, budget *models.Budget) error {
	query := `UPDATE budgets 
			  SET category = ?, amount = ?, period_start = ?, period_end = ?, updated_at = ? 
			  WHERE id = ?`

	budget.UpdatedAt = time.Now()

	_, err := r.db.ExecContext(ctx, query,
		budget.Category,
		budget.Amount,
		budget.PeriodStart,
		budget.PeriodEnd,
		budget.UpdatedAt,
		budget.ID,
	)
	return err
}

// DeleteBudget remove um orçamento pelo ID
func (r *SQLFinanceRepository) DeleteBudget(ctx context.Context, id int64) error {
	query := `DELETE FROM budgets WHERE id = ?`
	_, err := r.db.ExecContext(ctx, query, id)
	return err
}

// ListReports retorna a lista de relatórios com base nos filtros
func (r *SQLFinanceRepository) ListReports(ctx context.Context, filters *models.ReportFilters) ([]*models.Report, error) {
	query := `SELECT id, type, data, created_at, updated_at 
			  FROM reports WHERE 1=1`
	args := []interface{}{}

	if filters != nil {
		if filters.Type != "" {
			query += " AND type = ?"
			args = append(args, filters.Type)
		}
		if !filters.StartDate.IsZero() {
			query += " AND created_at >= ?"
			args = append(args, filters.StartDate)
		}
		if !filters.EndDate.IsZero() {
			query += " AND created_at <= ?"
			args = append(args, filters.EndDate)
		}
	}

	query += " ORDER BY created_at DESC"

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var reports []*models.Report
	for rows.Next() {
		report := &models.Report{}
		err := rows.Scan(
			&report.ID,
			&report.Type,
			&report.Data,
			&report.CreatedAt,
			&report.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		reports = append(reports, report)
	}

	return reports, nil
}

// GetReport retorna um relatório pelo ID
func (r *SQLFinanceRepository) GetReport(ctx context.Context, id int64) (*models.Report, error) {
	query := `SELECT id, type, data, created_at, updated_at 
			  FROM reports WHERE id = ?`

	report := &models.Report{}
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&report.ID,
		&report.Type,
		&report.Data,
		&report.CreatedAt,
		&report.UpdatedAt,
	)
	if err == sql.ErrNoRows {
		return nil, errors.New("report not found")
	}
	if err != nil {
		return nil, err
	}

	return report, nil
}

// CreateReport cria um novo relatório
func (r *SQLFinanceRepository) CreateReport(ctx context.Context, report *models.Report) error {
	query := `INSERT INTO reports (type, data, created_at, updated_at) 
			  VALUES (?, ?, ?, ?)`

	now := time.Now()
	report.CreatedAt = now
	report.UpdatedAt = now

	result, err := r.db.ExecContext(ctx, query,
		report.Type,
		report.Data,
		report.CreatedAt,
		report.UpdatedAt,
	)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}
	report.ID = id

	return nil
}

// DeleteReport remove um relatório pelo ID
func (r *SQLFinanceRepository) DeleteReport(ctx context.Context, id int64) error {
	query := `DELETE FROM reports WHERE id = ?`
	_, err := r.db.ExecContext(ctx, query, id)
	return err
}

// ListQuotes retorna a lista de cotações
func (r *SQLFinanceRepository) ListQuotes(ctx context.Context) ([]*models.Quote, error) {
	query := `SELECT symbol, price, variation, last_update 
			  FROM quotes`

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var quotes []*models.Quote
	for rows.Next() {
		quote := &models.Quote{}
		err := rows.Scan(
			&quote.Symbol,
			&quote.Price,
			&quote.Variation,
			&quote.LastUpdate,
		)
		if err != nil {
			return nil, err
		}
		quotes = append(quotes, quote)
	}

	return quotes, nil
}

// GetQuote retorna uma cotação pelo símbolo
func (r *SQLFinanceRepository) GetQuote(ctx context.Context, symbol string) (*models.Quote, error) {
	query := `SELECT symbol, price, change, change_percent, updated_at 
			  FROM quotes WHERE symbol = ?`

	quote := &models.Quote{}
	err := r.db.QueryRowContext(ctx, query, symbol).Scan(
		&quote.Symbol,
		&quote.Price,
		&quote.Change,
		&quote.ChangePercent,
		&quote.UpdatedAt,
	)
	if err == sql.ErrNoRows {
		return nil, errors.New("quote not found")
	}
	if err != nil {
		return nil, err
	}

	return quote, nil
}

// RefreshQuotes atualiza as cotações
func (r *SQLFinanceRepository) RefreshQuotes(ctx context.Context) error {
	// Implementação da atualização das cotações
	// Esta função deve ser implementada de acordo com a fonte de dados das cotações
	return nil
}

// ListNews retorna a lista de notícias financeiras
func (r *SQLFinanceRepository) ListNews(ctx context.Context) ([]*models.News, error) {
	query := `SELECT id, title, content, source, url, published_at, created_at, updated_at 
			  FROM news ORDER BY published_at DESC`

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var news []*models.News
	for rows.Next() {
		item := &models.News{}
		err := rows.Scan(
			&item.ID,
			&item.Title,
			&item.Content,
			&item.Source,
			&item.URL,
			&item.PublishedAt,
			&item.CreatedAt,
			&item.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		news = append(news, item)
	}

	return news, nil
}

// GetNews retorna uma notícia pelo ID
func (r *SQLFinanceRepository) GetNews(ctx context.Context, id int64) (*models.News, error) {
	query := `SELECT id, title, content, source, url, published_at, created_at, updated_at 
			  FROM news WHERE id = ?`

	item := &models.News{}
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&item.ID,
		&item.Title,
		&item.Content,
		&item.Source,
		&item.URL,
		&item.PublishedAt,
		&item.CreatedAt,
		&item.UpdatedAt,
	)
	if err == sql.ErrNoRows {
		return nil, errors.New("news not found")
	}
	if err != nil {
		return nil, err
	}

	return item, nil
}

// GetSettings retorna as configurações financeiras
func (r *SQLFinanceRepository) GetSettings(ctx context.Context) (*models.FinanceSettings, error) {
	query := `SELECT id, auto_refresh, refresh_interval, color_scheme, created_at, updated_at 
			  FROM finance_settings WHERE id = 1`

	settings := &models.FinanceSettings{}
	err := r.db.QueryRowContext(ctx, query).Scan(
		&settings.ID,
		&settings.AutoRefresh,
		&settings.RefreshInterval,
		&settings.ColorScheme,
		&settings.CreatedAt,
		&settings.UpdatedAt,
	)
	if err == sql.ErrNoRows {
		return nil, errors.New("settings not found")
	}
	if err != nil {
		return nil, err
	}

	return settings, nil
}

// UpdateSettings atualiza as configurações financeiras
func (r *SQLFinanceRepository) UpdateSettings(ctx context.Context, settings *models.FinanceSettings) error {
	query := `INSERT INTO finance_settings (id, auto_refresh, refresh_interval, color_scheme, created_at, updated_at) 
			  VALUES (?, ?, ?, ?, ?, ?) 
			  ON DUPLICATE KEY UPDATE 
			  auto_refresh = VALUES(auto_refresh), 
			  refresh_interval = VALUES(refresh_interval), 
			  color_scheme = VALUES(color_scheme), 
			  updated_at = VALUES(updated_at)`

	now := time.Now()
	if settings.CreatedAt.IsZero() {
		settings.CreatedAt = now
	}
	settings.UpdatedAt = now

	_, err := r.db.ExecContext(ctx, query,
		settings.ID,
		settings.AutoRefresh,
		settings.RefreshInterval,
		settings.ColorScheme,
		settings.CreatedAt,
		settings.UpdatedAt,
	)
	return err
}

// CreateNews cria uma nova notícia financeira
func (r *SQLFinanceRepository) CreateNews(ctx context.Context, news *models.News) error {
	query := `INSERT INTO news (title, content, source, url, created_at) 
			  VALUES (?, ?, ?, ?, ?)`

	now := time.Now()
	news.CreatedAt = now

	result, err := r.db.ExecContext(ctx, query,
		news.Title,
		news.Content,
		news.Source,
		news.URL,
		news.CreatedAt,
	)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}
	news.ID = id

	return nil
}

// UpdateNews atualiza uma notícia financeira existente
func (r *SQLFinanceRepository) UpdateNews(ctx context.Context, news *models.News) error {
	query := `UPDATE news 
			  SET title = ?, content = ?, source = ?, url = ? 
			  WHERE id = ?`

	_, err := r.db.ExecContext(ctx, query,
		news.Title,
		news.Content,
		news.Source,
		news.URL,
		news.ID,
	)
	return err
}

// DeleteNews remove uma notícia financeira pelo ID
func (r *SQLFinanceRepository) DeleteNews(ctx context.Context, id int64) error {
	query := `DELETE FROM news WHERE id = ?`
	_, err := r.db.ExecContext(ctx, query, id)
	return err
}

// UpdateQuote atualiza uma cotação existente
func (r *SQLFinanceRepository) UpdateQuote(ctx context.Context, quote *models.Quote) error {
	query := `UPDATE quotes 
			  SET price = ?, change = ?, updated_at = ? 
			  WHERE symbol = ?`

	now := time.Now()
	quote.UpdatedAt = now

	_, err := r.db.ExecContext(ctx, query,
		quote.Price,
		quote.Change,
		quote.UpdatedAt,
		quote.Symbol,
	)
	return err
}
