package repository

import (
	"context"
	"tradicao/internal/models"
)

// FinanceRepository define a interface para operações financeiras
type FinanceRepository interface {
	// Métodos de Pagamento
	ListPayments(ctx context.Context, filters *models.PaymentFilters) ([]*models.Payment, error)
	GetPayment(ctx context.Context, id int64) (*models.Payment, error)
	CreatePayment(ctx context.Context, payment *models.Payment) error
	UpdatePayment(ctx context.Context, payment *models.Payment) error
	DeletePayment(ctx context.Context, id int64) error

	// Métodos de Nota Fiscal
	ListInvoices(ctx context.Context, filters *models.InvoiceFilters) ([]*models.Invoice, error)
	GetInvoice(ctx context.Context, id int64) (*models.Invoice, error)
	CreateInvoice(ctx context.Context, invoice *models.Invoice) error
	UpdateInvoice(ctx context.Context, invoice *models.Invoice) error
	DeleteInvoice(ctx context.Context, id int64) error

	// Métodos de Orçamento
	ListBudgets(ctx context.Context, filters *models.BudgetFilters) ([]*models.Budget, error)
	GetBudget(ctx context.Context, id int64) (*models.Budget, error)
	CreateBudget(ctx context.Context, budget *models.Budget) error
	UpdateBudget(ctx context.Context, budget *models.Budget) error
	DeleteBudget(ctx context.Context, id int64) error

	// Métodos de Relatório
	ListReports(ctx context.Context, filters *models.ReportFilters) ([]*models.Report, error)
	GetReport(ctx context.Context, id int64) (*models.Report, error)
	CreateReport(ctx context.Context, report *models.Report) error
	DeleteReport(ctx context.Context, id int64) error

	// Métodos de Cotação
	ListQuotes(ctx context.Context) ([]*models.Quote, error)
	GetQuote(ctx context.Context, symbol string) (*models.Quote, error)
	RefreshQuotes(ctx context.Context) error
	UpdateQuote(ctx context.Context, quote *models.Quote) error

	// Métodos de Notícias
	ListNews(ctx context.Context) ([]*models.News, error)
	GetNews(ctx context.Context, id int64) (*models.News, error)
	CreateNews(ctx context.Context, news *models.News) error
	UpdateNews(ctx context.Context, news *models.News) error
	DeleteNews(ctx context.Context, id int64) error

	// Métodos de Configurações
	GetSettings(ctx context.Context) (*models.FinanceSettings, error)
	UpdateSettings(ctx context.Context, settings *models.FinanceSettings) error
}
