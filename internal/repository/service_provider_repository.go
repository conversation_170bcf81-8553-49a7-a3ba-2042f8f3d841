package repository

import (
	"tradicao/internal/models"
)

// ServiceProviderRepository define a interface para operações com prestadores
type ServiceProviderRepository interface {
	// Métodos existentes
	Create(provider models.ServiceProvider) (models.ServiceProvider, error)
	FindByID(id uint) (models.ServiceProvider, error)
	FindAll() ([]models.ServiceProvider, error)
	FindActive() ([]models.ServiceProvider, error)
	Update(provider models.ServiceProvider) (models.ServiceProvider, error)
	Delete(id uint) error
	Search(term string) ([]models.ServiceProvider, error)
	FindBySpecialty(specialty string) ([]models.ServiceProvider, error)
	FindByBranch(branchID uint) ([]models.ServiceProvider, error)
	UpdateRating(id uint, rating float64) error
	Count() (int64, error)
	GetDB() interface{}
	
	// Novos métodos para gerenciar técnicos
	GetTechnicians(providerID uint) ([]models.User, error)
	AddTechnician(providerID uint, userID uint) error
	RemoveTechnician(userID uint) error
	
	// Novos métodos para gerenciar logomarca
	<PERSON>date<PERSON>(providerID uint, logoURL string) error
}
