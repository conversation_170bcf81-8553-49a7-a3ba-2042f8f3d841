package repository

import (
	"context"

	"tradicao/internal/ent"
	"tradicao/internal/ent/user"
	"tradicao/internal/models"
)

type EntUserRepository struct {
	client *ent.Client
}

func NewEntUserRepository(client *ent.Client) *EntUserRepository {
	return &EntUserRepository{client: client}
}

func (r *EntUserRepository) Create(user *models.User) error {
	_, err := r.client.User.
		Create().
		SetName(user.Name).
		SetEmail(user.Email).
		SetPassword(user.Password).
		SetRole(string(user.Role)).
		Save(context.Background())
	return err
}

func (r *EntUserRepository) FindByEmail(email string) (*models.User, error) {
	u, err := r.client.User.
		Query().
		Where(user.EmailEQ(email)).
		Only(context.Background())
	if err != nil {
		return nil, err
	}
	return &models.User{
		ID:       uint(u.ID),
		Name:     u.Name,
		Email:    u.Email,
		Password: u.Password,
		Role:     models.UserRole(u.Role),
	}, nil
}

func (r *EntUserRepository) FindByID(id int) (*models.User, error) {
	u, err := r.client.User.
		Query().
		Where(user.IDEQ(id)).
		Only(context.Background())
	if err != nil {
		return nil, err
	}
	return &models.User{
		ID:       uint(u.ID),
		Name:     u.Name,
		Email:    u.Email,
		Password: u.Password,
		Role:     models.UserRole(u.Role),
	}, nil
}

func (r *EntUserRepository) Update(user *models.User) error {
	_, err := r.client.User.
		UpdateOneID(int(user.ID)).
		SetName(user.Name).
		SetEmail(user.Email).
		SetPassword(user.Password).
		SetRole(string(user.Role)).
		Save(context.Background())
	return err
}

func (r *EntUserRepository) Delete(id int) error {
	return r.client.User.
		DeleteOneID(id).
		Exec(context.Background())
}

func (r *EntUserRepository) List() ([]*models.User, error) {
	users, err := r.client.User.
		Query().
		All(context.Background())
	if err != nil {
		return nil, err
	}

	var result []*models.User
	for _, u := range users {
		result = append(result, &models.User{
			ID:       uint(u.ID),
			Name:     u.Name,
			Email:    u.Email,
			Password: u.Password,
			Role:     models.UserRole(u.Role),
		})
	}
	return result, nil
}
