package repository

import (
	"tradicao/internal/models"
)

// IBranchRepository define a interface para o repositório de filiais
type IBranchRepository interface {
	// Métodos principais
	GetAllBranches(page, limit int, search string, isActive *bool) ([]models.Branch, int64, error)
	GetBranchByID(id uint) (*models.Branch, error)
	GetBranchesByUserID(userID int64) ([]models.Branch, error)
	GetBranchesByTechnicianID(technicianID int64) ([]models.Branch, error)
	CreateBranch(branch *models.Branch) error
	UpdateBranch(branch *models.Branch) error
	DeleteBranch(id uint) error
	BranchExists(id uint) (bool, error)

	// Métodos para vinculação com prestadores
	LinkProvider(branchID, providerID uint) error
	UnlinkProvider(branchID, providerID uint) error
	GetLinkedProviders(branchID uint) ([]uint, error)

	// Métodos para autenticação
	GenerateAuthToken(branchID uint) (string, error)
	ValidateAuthToken(token string) (uint, error)
	UseAuthToken(token string) error

	// Métodos adicionais para compatibilidade com IFilialRepository
	GetBranchesByRegion(region string) ([]models.Branch, error)
	GetActiveBranches() ([]models.Branch, error)
	GetBranchMetrics() (*models.BranchMetrics, error)

	// Métodos para compatibilidade com código legado
	GetBranchSummaries() ([]models.BranchSummary, error)
	GetBranchSummaryByID(id uint) (*models.BranchSummary, error)
}
