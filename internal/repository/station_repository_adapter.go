package repository

import (
	"tradicao/internal/models"
)

// StationRepositoryAdapter adapta o GormStationRepository para a interface IStationRepository
type StationRepositoryAdapter struct {
	repo *GormStationRepository
}

// NewStationRepositoryAdapter cria um novo adaptador
func NewStationRepositoryAdapter(repo *GormStationRepository) IStationRepository {
	return &StationRepositoryAdapter{repo: repo}
}

// GetStationByID implementa a interface StationRepository
func (a *StationRepositoryAdapter) GetStationByID(id uint) (*models.Station, error) {
	return a.repo.GetStationByID(id)
}

// GetAllStations implementa a interface StationRepository
func (a *StationRepositoryAdapter) GetAllStations() ([]models.Station, error) {
	return a.repo.GetAllStations()
}

// GetStationsByUserID implementa a interface StationRepository
func (a *StationRepositoryAdapter) GetStationsByUserID(userID int64) ([]models.Station, error) {
	return a.repo.GetStationsByUserID(userID)
}

// CreateStation implementa a interface StationRepository
func (a *StationRepositoryAdapter) CreateStation(station *models.Station) error {
	return a.repo.CreateStation(station)
}

// UpdateStation implementa a interface StationRepository
func (a *StationRepositoryAdapter) UpdateStation(station *models.Station) error {
	return a.repo.UpdateStation(station)
}

// DeleteStation implementa a interface StationRepository
func (a *StationRepositoryAdapter) DeleteStation(id uint) error {
	return a.repo.DeleteStation(id)
}

// GetStationsByRegion implementa a interface StationRepository
func (a *StationRepositoryAdapter) GetStationsByRegion(region string) ([]models.Station, error) {
	return a.repo.GetStationsByRegion(region)
}

// GetActiveStations implementa a interface StationRepository
func (a *StationRepositoryAdapter) GetActiveStations() ([]models.Station, error) {
	return a.repo.GetActiveStations()
}

// GetStationMetrics implementa a interface StationRepository
func (a *StationRepositoryAdapter) GetStationMetrics() (*models.StationMetrics, error) {
	return a.repo.GetStationMetrics()
}
