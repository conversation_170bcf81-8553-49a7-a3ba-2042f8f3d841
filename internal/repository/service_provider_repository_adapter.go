package repository

import (
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// ServiceProviderRepositoryAdapter adapta o GormServiceProviderRepository para a interface ServiceProviderRepository
type ServiceProviderRepositoryAdapter struct {
	repo *GormServiceProviderRepository
	db   *gorm.DB
}

// NewServiceProviderRepository cria uma nova instância do repositório adaptado
func NewServiceProviderRepository(db *gorm.DB) ServiceProviderRepository {
	gormRepo := &GormServiceProviderRepository{db: db}
	return &ServiceProviderRepositoryAdapter{
		repo: gormRepo,
		db:   db,
	}
}

// Create delega para o repositório GORM
func (a *ServiceProviderRepositoryAdapter) Create(provider models.ServiceProvider) (models.ServiceProvider, error) {
	return a.repo.Create(provider)
}

// FindByID delega para o repositório GORM
func (a *ServiceProviderRepositoryAdapter) FindByID(id uint) (models.ServiceProvider, error) {
	return a.repo.FindByID(id)
}

// FindAll delega para o repositório GORM
func (a *ServiceProviderRepositoryAdapter) FindAll() ([]models.ServiceProvider, error) {
	return a.repo.FindAll()
}

// FindActive delega para o repositório GORM
func (a *ServiceProviderRepositoryAdapter) FindActive() ([]models.ServiceProvider, error) {
	return a.repo.FindActive()
}

// Update delega para o repositório GORM
func (a *ServiceProviderRepositoryAdapter) Update(provider models.ServiceProvider) (models.ServiceProvider, error) {
	return a.repo.Update(provider)
}

// Delete delega para o repositório GORM
func (a *ServiceProviderRepositoryAdapter) Delete(id uint) error {
	return a.repo.Delete(id)
}

// Search delega para o repositório GORM
func (a *ServiceProviderRepositoryAdapter) Search(term string) ([]models.ServiceProvider, error) {
	return a.repo.Search(term)
}

// FindBySpecialty delega para o repositório GORM
func (a *ServiceProviderRepositoryAdapter) FindBySpecialty(specialty string) ([]models.ServiceProvider, error) {
	return a.repo.FindBySpecialty(specialty)
}

// FindByBranch delega para o repositório GORM
func (a *ServiceProviderRepositoryAdapter) FindByBranch(branchID uint) ([]models.ServiceProvider, error) {
	return a.repo.FindByBranch(branchID)
}

// UpdateRating delega para o repositório GORM
func (a *ServiceProviderRepositoryAdapter) UpdateRating(id uint, rating float64) error {
	return a.repo.UpdateRating(id, rating)
}

// Count delega para o repositório GORM
func (a *ServiceProviderRepositoryAdapter) Count() (int64, error) {
	return a.repo.Count()
}

// GetDB delega para o repositório GORM
func (a *ServiceProviderRepositoryAdapter) GetDB() interface{} {
	return a.repo.GetDB()
}

// GetTechnicians delega para o repositório GORM
func (a *ServiceProviderRepositoryAdapter) GetTechnicians(providerID uint) ([]models.User, error) {
	return a.repo.GetTechnicians(providerID)
}

// AddTechnician delega para o repositório GORM
func (a *ServiceProviderRepositoryAdapter) AddTechnician(providerID uint, userID uint) error {
	return a.repo.AddTechnician(providerID, userID)
}

// RemoveTechnician delega para o repositório GORM
func (a *ServiceProviderRepositoryAdapter) RemoveTechnician(userID uint) error {
	return a.repo.RemoveTechnician(userID)
}

// UpdateLogo delega para o repositório GORM
func (a *ServiceProviderRepositoryAdapter) UpdateLogo(providerID uint, logoURL string) error {
	return a.repo.UpdateLogo(providerID, logoURL)
}
