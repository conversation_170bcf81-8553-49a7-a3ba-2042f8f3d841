package repository

import (
	"tradicao/internal/models"
)

// IMaintenance define a interface para repositórios de manutenção
type IMaintenance interface {
	// Métodos básicos de CRUD
	Create(order *models.MaintenanceOrder) error
	GetByID(id uint) (*models.MaintenanceOrderDetailed, error)
	Update(order *models.MaintenanceOrder) error
	Delete(id uint) error

	// Métodos específicos
	GetAll() ([]models.MaintenanceOrder, error)
	GetAllDetailed() ([]models.MaintenanceOrderDetailed, error)
	GetByStatus(status string) ([]models.MaintenanceOrder, error)
	GetByStationID(stationID uint) ([]models.MaintenanceOrder, error)

	// Atualizações e operações específicas
	UpdateStatusSimple(id uint, status string) error
	UpdateStatusDetailed(id uint, status string, details string) error
	AddNote(orderID uint, userID uint, content string) error
	AddMaterial(orderID uint, material models.Material) error

	// Métricas e relatórios
	GetOrderCount() (int, error)
	GetOrderCountByStatus(status string) (int, error)
	GetOrderMetrics() (*models.MaintenanceOrderMetricsV2, error)
	GetExtendedMetrics() (*models.MaintenanceOrderMetricsV2, error) // Usando V2 como temporário
}
