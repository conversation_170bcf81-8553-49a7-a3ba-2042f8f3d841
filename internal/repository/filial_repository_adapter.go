package repository

import (
	"tradicao/internal/models"
)

// FilialRepositoryAdapter adapta o GormFilialRepository para a interface IFilialRepository
type FilialRepositoryAdapter struct {
	repo *GormFilialRepository
}

// NewFilialRepositoryAdapter cria um novo adaptador
func NewFilialRepositoryAdapter(repo *GormFilialRepository) IFilialRepository {
	return &FilialRepositoryAdapter{repo: repo}
}

// GetFilialByID implementa a interface IFilialRepository
func (a *FilialRepositoryAdapter) GetFilialByID(id uint) (*models.Filial, error) {
	return a.repo.GetFilialByID(id)
}

// GetAllFiliais implementa a interface IFilialRepository
func (a *FilialRepositoryAdapter) GetAllFiliais() ([]models.Filial, error) {
	return a.repo.GetAllFiliais()
}

// GetFiliaisByUserID implementa a interface IFilialRepository
func (a *FilialRepositoryAdapter) GetFiliaisByUserID(userID int64) ([]models.Filial, error) {
	return a.repo.GetFiliaisByUserID(userID)
}

// CreateFilial implementa a interface IFilialRepository
func (a *FilialRepositoryAdapter) CreateFilial(filial *models.Filial) error {
	return a.repo.CreateFilial(filial)
}

// UpdateFilial implementa a interface IFilialRepository
func (a *FilialRepositoryAdapter) UpdateFilial(filial *models.Filial) error {
	return a.repo.UpdateFilial(filial)
}

// DeleteFilial implementa a interface IFilialRepository
func (a *FilialRepositoryAdapter) DeleteFilial(id uint) error {
	return a.repo.DeleteFilial(id)
}

// GetFiliaisByRegion implementa a interface IFilialRepository
func (a *FilialRepositoryAdapter) GetFiliaisByRegion(region string) ([]models.Filial, error) {
	return a.repo.GetFiliaisByRegion(region)
}

// GetActiveFiliais implementa a interface IFilialRepository
func (a *FilialRepositoryAdapter) GetActiveFiliais() ([]models.Filial, error) {
	return a.repo.GetActiveFiliais()
}

// GetFilialMetrics implementa a interface IFilialRepository
func (a *FilialRepositoryAdapter) GetFilialMetrics() (*models.FilialMetrics, error) {
	return a.repo.GetFilialMetrics()
}

// Adaptador para compatibilidade com IStationRepository
type filialToStationAdapter struct {
	repo IFilialRepository
}

// NewFilialToStationAdapter cria um adaptador para converter IFilialRepository para IStationRepository
func NewFilialToStationAdapter(repo IFilialRepository) IStationRepository {
	return &filialToStationAdapter{repo: repo}
}

// GetStationByID implementa a interface IStationRepository
func (a *filialToStationAdapter) GetStationByID(id uint) (*models.Station, error) {
	filial, err := a.repo.GetFilialByID(id)
	if err != nil {
		return nil, err
	}
	return filial.ToStation(), nil
}

// GetAllStations implementa a interface IStationRepository
func (a *filialToStationAdapter) GetAllStations() ([]models.Station, error) {
	filiais, err := a.repo.GetAllFiliais()
	if err != nil {
		return nil, err
	}

	stations := make([]models.Station, len(filiais))
	for i, filial := range filiais {
		station := filial.ToStation()
		stations[i] = *station
	}

	return stations, nil
}

// GetStationsByUserID implementa a interface IStationRepository
func (a *filialToStationAdapter) GetStationsByUserID(userID int64) ([]models.Station, error) {
	filiais, err := a.repo.GetFiliaisByUserID(userID)
	if err != nil {
		return nil, err
	}

	stations := make([]models.Station, len(filiais))
	for i, filial := range filiais {
		station := filial.ToStation()
		stations[i] = *station
	}

	return stations, nil
}

// CreateStation implementa a interface IStationRepository
func (a *filialToStationAdapter) CreateStation(station *models.Station) error {
	filial := models.FromStation(station)
	return a.repo.CreateFilial(filial)
}

// UpdateStation implementa a interface IStationRepository
func (a *filialToStationAdapter) UpdateStation(station *models.Station) error {
	filial := models.FromStation(station)
	return a.repo.UpdateFilial(filial)
}

// DeleteStation implementa a interface IStationRepository
func (a *filialToStationAdapter) DeleteStation(id uint) error {
	return a.repo.DeleteFilial(id)
}

// GetStationsByRegion implementa a interface IStationRepository
func (a *filialToStationAdapter) GetStationsByRegion(region string) ([]models.Station, error) {
	filiais, err := a.repo.GetFiliaisByRegion(region)
	if err != nil {
		return nil, err
	}

	stations := make([]models.Station, len(filiais))
	for i, filial := range filiais {
		station := filial.ToStation()
		stations[i] = *station
	}

	return stations, nil
}

// GetActiveStations implementa a interface IStationRepository
func (a *filialToStationAdapter) GetActiveStations() ([]models.Station, error) {
	filiais, err := a.repo.GetActiveFiliais()
	if err != nil {
		return nil, err
	}

	stations := make([]models.Station, len(filiais))
	for i, filial := range filiais {
		station := filial.ToStation()
		stations[i] = *station
	}

	return stations, nil
}

// GetStationMetrics implementa a interface IStationRepository
func (a *filialToStationAdapter) GetStationMetrics() (*models.StationMetrics, error) {
	filialMetrics, err := a.repo.GetFilialMetrics()
	if err != nil {
		return nil, err
	}

	stationMetrics := &models.StationMetrics{
		TotalStations:    filialMetrics.TotalFiliais,
		ActiveStations:   filialMetrics.ActiveFiliais,
		StationsByRegion: filialMetrics.FiliaisByRegion,
	}

	return stationMetrics, nil
}
