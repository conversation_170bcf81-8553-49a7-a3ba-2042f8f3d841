package repository

import (
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// GormNotificationRepository implementa NotificationRepository usando GORM
type GormNotificationRepository struct {
	db *gorm.DB
}

// NewGormNotificationRepository cria um novo repositório de notificações com GORM
func NewGormNotificationRepository(db *gorm.DB) *GormNotificationRepository {
	return &GormNotificationRepository{db: db}
}

// SaveSubscription salva uma assinatura de notificação
func (r *GormNotificationRepository) SaveSubscription(subscription models.NotificationSubscription) error {
	return r.db.Create(&subscription).Error
}

// DeleteSubscriptionByEndpoint remove uma assinatura pelo endpoint
func (r *GormNotificationRepository) DeleteSubscriptionByEndpoint(endpoint string) error {
	return r.db.Where("endpoint = ?", endpoint).Delete(&models.NotificationSubscription{}).Error
}

// GetSubscriptionsByUserID retorna as assinaturas de um usuário
func (r *GormNotificationRepository) GetSubscriptionsByUserID(userID int64) ([]models.NotificationSubscription, error) {
	var subscriptions []models.NotificationSubscription
	err := r.db.Where("user_id = ?", userID).Find(&subscriptions).Error
	return subscriptions, err
}

// GetSubscriptionsByRole retorna as assinaturas por função
func (r *GormNotificationRepository) GetSubscriptionsByRole(role string) ([]models.NotificationSubscription, error) {
	var subscriptions []models.NotificationSubscription
	err := r.db.Where("role = ?", role).Find(&subscriptions).Error
	return subscriptions, err
}

// SaveNotification salva uma notificação
func (r *GormNotificationRepository) SaveNotification(notification models.Notification) error {
	return r.db.Create(&notification).Error
}

// GetNotificationsByUserID retorna as notificações de um usuário
func (r *GormNotificationRepository) GetNotificationsByUserID(userID int64, limit, offset int) ([]models.Notification, error) {
	var notifications []models.Notification
	err := r.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&notifications).Error
	return notifications, err
}

// GetNotificationByID retorna uma notificação pelo ID
func (r *GormNotificationRepository) GetNotificationByID(id int64) (*models.Notification, error) {
	var notification models.Notification
	if err := r.db.First(&notification, id).Error; err != nil {
		return nil, err
	}
	return &notification, nil
}

// MarkNotificationAsRead marca uma notificação como lida
func (r *GormNotificationRepository) MarkNotificationAsRead(id int64) error {
	return r.db.Model(&models.Notification{}).Where("id = ?", id).Update("read", true).Error
}

// DeleteNotification remove uma notificação
func (r *GormNotificationRepository) DeleteNotification(id int64) error {
	return r.db.Delete(&models.Notification{}, id).Error
}

// CountUnreadNotifications conta notificações não lidas
func (r *GormNotificationRepository) CountUnreadNotifications(userID int64) (int, error) {
	var count int64
	err := r.db.Model(&models.Notification{}).
		Where("user_id = ? AND read = ?", userID, false).
		Count(&count).Error
	return int(count), err
}

// MarkAllNotificationsAsRead marca todas as notificações de um usuário como lidas
func (r *GormNotificationRepository) MarkAllNotificationsAsRead(userID int64) error {
	result := r.db.Model(&models.Notification{}).
		Where("user_id = ? AND read = ?", userID, false).
		Update("read", true)
	return result.Error
}
