# Tools Configuration


## Environment

GOBIN: undefined
toolsGopath: 
gopath: /root/go
GOROOT: /usr/lib/go-1.21
PATH: /root/.cursor-server/bin/7801a556824585b7f2721900066bc87c4a09b740/bin/remote-cli:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/usr/lib/wsl/lib:/mnt/c/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin:/mnt/c/WINDOWS/system32:/mnt/c/WINDOWS:/mnt/c/WINDOWS/System32/Wbem:/mnt/c/WINDOWS/System32/WindowsPowerShell/v1.0/:/mnt/c/WINDOWS/System32/OpenSSH/:/mnt/c/Program Files/PowerShell/7/:/mnt/c/Program Files/Git/cmd:/mnt/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/mnt/c/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin:/mnt/c/Program Files/nodejs/:/mnt/c/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin:/mnt/c/WINDOWS/system32:/mnt/c/WINDOWS:/mnt/c/WINDOWS/System32/Wbem:/mnt/c/WINDOWS/System32/WindowsPowerShell/v1.0/:/mnt/c/WINDOWS/System32/OpenSSH/:/mnt/c/Program Files/PowerShell/7/:/mnt/c/Program Files/Git/cmd:/mnt/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/mnt/c/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin:/mnt/c/Program Files/nodejs/:/mnt/c/Program Files/nodejs/:/snap/bin:/usr/local/go/bin

## Tools

	go:	/usr/bin/go: go version go1.21.8 linux/amd64

	gopls:	not installed
	gotests:	not installed
	gomodifytags:	not installed
	impl:	not installed
	goplay:	not installed
	dlv:	not installed
	staticcheck:	not installed

## Go env

Workspace Folder (projeto): /home/<USER>

	GO111MODULE=''
	GOARCH='amd64'
	GOBIN=''
	GOCACHE='/root/.cache/go-build'
	GOENV='/root/.config/go/env'
	GOEXE=''
	GOEXPERIMENT=''
	GOFLAGS=''
	GOHOSTARCH='amd64'
	GOHOSTOS='linux'
	GOINSECURE=''
	GOMODCACHE='/root/go/pkg/mod'
	GONOPROXY=''
	GONOSUMDB=''
	GOOS='linux'
	GOPATH='/root/go'
	GOPRIVATE=''
	GOPROXY='https://proxy.golang.org,direct'
	GOROOT='/usr/lib/go-1.21'
	GOSUMDB='sum.golang.org'
	GOTMPDIR=''
	GOTOOLDIR='/usr/lib/go-1.21/pkg/tool/linux_amd64'
	GOVCS=''
	GOVERSION='go1.21.8'
	GCCGO='gccgo'
	GOAMD64='v1'
	AR='ar'
	CC='gcc'
	CXX='g++'
	CGO_ENABLED='1'
	GOMOD='/home/<USER>/go.mod'
	GOWORK=''
	CGO_CFLAGS='-g -O2'
	CGO_CPPFLAGS=''
	CGO_CXXFLAGS='-g -O2'
	CGO_FFLAGS='-g -O2'
	CGO_LDFLAGS='-g -O2'
	PKG_CONFIG='pkg-config'
	GOGCCFLAGS='-fPIC -m64 -pthread -fmessage-length=0 -fdebug-prefix-map=/tmp/go-build2309813143=/tmp/go-build -gno-record-gcc-switches'
	