package repository

import (
	"fmt"
	"time"
	"tradicao/internal/models"
)

// OrderRepositoryAdapter adapta o GormOrderRepository para a interface MaintenanceOrderRepositoryInterface
type OrderRepositoryAdapter struct {
	repo *GormOrderRepository
}

// NewOrderRepositoryAdapter cria um novo adaptador para o repositório de ordens
func NewOrderRepositoryAdapter(repo *GormOrderRepository) *OrderRepositoryAdapter {
	return &OrderRepositoryAdapter{repo: repo}
}

// GetByID obtém uma ordem pelo ID
func (a *OrderRepositoryAdapter) GetByID(id uint) (*models.MaintenanceOrder, error) {
	return a.repo.GetByID(id)
}

// GetAll obtém todas as ordens
func (a *OrderRepositoryAdapter) GetAll(offset, limit int, filter string) ([]models.MaintenanceOrder, error) {
	return a.repo.GetAll(offset, limit, filter)
}

// Create cria uma nova ordem
func (a *OrderRepositoryAdapter) Create(order *models.MaintenanceOrder) error {
	return a.repo.Create(order)
}

// Update atualiza uma ordem existente
func (a *OrderRepositoryAdapter) Update(order *models.MaintenanceOrder) error {
	return a.repo.Update(order)
}

// Delete remove uma ordem
func (a *OrderRepositoryAdapter) Delete(id uint) error {
	return a.repo.Delete(id)
}

// UpdateOrder atualiza uma ordem com os dados fornecidos
func (a *OrderRepositoryAdapter) UpdateOrder(orderID uint, updateData map[string]interface{}) (*models.MaintenanceOrder, error) {
	// Implementação temporária
	order, err := a.GetByID(orderID)
	if err != nil {
		return nil, err
	}

	// Atualizar campos com base nos dados fornecidos
	if status, ok := updateData["status"].(models.OrderStatus); ok {
		order.Status = status
	}

	if err := a.Update(order); err != nil {
		return nil, err
	}

	return order, nil
}

// FindCostsByOrderID retorna os custos associados a uma ordem
func (a *OrderRepositoryAdapter) FindCostsByOrderID(orderID uint) ([]models.CostItem, error) {
	// Implementação temporária
	return []models.CostItem{}, nil
}

// CreateCost cria um novo item de custo
func (a *OrderRepositoryAdapter) CreateCost(cost *models.CostItem) (*models.CostItem, error) {
	// Implementação temporária
	cost.ID = 1 // Temporário
	cost.CreatedAt = time.Now()
	cost.UpdatedAt = time.Now()
	return cost, nil
}

// CreateInteraction cria uma nova interação
func (a *OrderRepositoryAdapter) CreateInteraction(interaction *models.Interaction) (*models.Interaction, error) {
	// Implementação temporária
	interaction.ID = 1 // Temporário
	interaction.CreatedAt = time.Now()
	interaction.UpdatedAt = time.Now()
	return interaction, nil
}

// FindByNumber busca uma ordem pelo número
func (a *OrderRepositoryAdapter) FindByNumber(number string) (*models.MaintenanceOrder, error) {
	// Implementação temporária
	return nil, fmt.Errorf("não implementado")
}

// FindByStatus busca ordens por status
func (a *OrderRepositoryAdapter) FindByStatus(status string) ([]models.MaintenanceOrder, error) {
	// Implementação temporária
	return []models.MaintenanceOrder{}, nil
}

// FindByBranchID busca ordens por filial
func (a *OrderRepositoryAdapter) FindByBranchID(branchID uint) ([]models.MaintenanceOrder, error) {
	// Implementação temporária
	return []models.MaintenanceOrder{}, nil
}

// FindByEquipmentID busca ordens por equipamento
func (a *OrderRepositoryAdapter) FindByEquipmentID(equipmentID uint) ([]models.MaintenanceOrder, error) {
	// Implementação temporária
	return []models.MaintenanceOrder{}, nil
}

// FindByServiceProviderID busca ordens por prestador de serviço
func (a *OrderRepositoryAdapter) FindByServiceProviderID(providerID uint) ([]models.MaintenanceOrder, error) {
	// Implementação temporária
	return []models.MaintenanceOrder{}, nil
}

// FindAllDetailed retorna todas as ordens com detalhes
func (a *OrderRepositoryAdapter) FindAllDetailed() ([]models.MaintenanceOrderDetailed, error) {
	// Implementação temporária
	return []models.MaintenanceOrderDetailed{}, nil
}

// GetMetrics retorna métricas das ordens
func (a *OrderRepositoryAdapter) GetMetrics() (*models.MaintenanceOrderMetricsV2, error) {
	// Implementação temporária
	return &models.MaintenanceOrderMetricsV2{}, nil
}
