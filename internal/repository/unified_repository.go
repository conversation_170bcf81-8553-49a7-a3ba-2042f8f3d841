package repository

import (
	"tradicao/internal/adapters"
	"tradicao/internal/models"
)

// UnifiedFilialRepository é um repositório unificado que implementa todas as interfaces
// relacionadas a filiais (IFilialRepository, IStationRepository, IBranchRepository)
type UnifiedFilialRepository struct {
	filialRepo  IFilialRepository
	stationRepo IStationRepository
	branchRepo  IBranchRepository
}

// NewUnifiedFilialRepository cria um novo repositório unificado
func NewUnifiedFilialRepository(
	filialRepo IFilialRepository,
	stationRepo IStationRepository,
	branchRepo IBranchRepository,
) *UnifiedFilialRepository {
	return &UnifiedFilialRepository{
		filialRepo:  filialRepo,
		stationRepo: stationRepo,
		branchRepo:  branchRepo,
	}
}

// Implementação de IFilialRepository

func (r *UnifiedFilialRepository) GetFilialByID(id uint) (*models.Filial, error) {
	// Tentar primeiro o repositório de filial
	if r.filialRepo != nil {
		filial, err := r.filialRepo.GetFilialByID(id)
		if err == nil && filial != nil {
			return filial, nil
		}
	}

	// Tentar o repositório de estação
	if r.stationRepo != nil {
		station, err := r.stationRepo.GetStationByID(id)
		if err == nil && station != nil {
			return adapters.ConvertStationToFilial(station), nil
		}
	}

	// Tentar o repositório de branch
	if r.branchRepo != nil {
		branch, err := r.branchRepo.GetBranchByID(id)
		if err == nil && branch != nil {
			return adapters.ConvertBranchToFilial(branch), nil
		}
	}

	return nil, nil
}

func (r *UnifiedFilialRepository) GetAllFiliais() ([]models.Filial, error) {
	var allFiliais []models.Filial

	// Obter filiais do repositório de filial
	if r.filialRepo != nil {
		filiais, err := r.filialRepo.GetAllFiliais()
		if err == nil {
			allFiliais = append(allFiliais, filiais...)
		}
	}

	// Obter estações e converter para filiais
	if r.stationRepo != nil {
		stations, err := r.stationRepo.GetAllStations()
		if err == nil {
			for _, station := range stations {
				filial := adapters.ConvertStationToFilial(&station)
				// Verificar se já existe na lista
				exists := false
				for _, f := range allFiliais {
					if f.ID == filial.ID {
						exists = true
						break
					}
				}
				if !exists {
					allFiliais = append(allFiliais, *filial)
				}
			}
		}
	}

	// Obter branches e converter para filiais
	if r.branchRepo != nil {
		branches, _, err := r.branchRepo.GetAllBranches(1, 1000, "", nil)
		if err == nil {
			for _, branch := range branches {
				filial := adapters.ConvertBranchToFilial(&branch)
				// Verificar se já existe na lista
				exists := false
				for _, f := range allFiliais {
					if f.ID == filial.ID {
						exists = true
						break
					}
				}
				if !exists {
					allFiliais = append(allFiliais, *filial)
				}
			}
		}
	}

	return allFiliais, nil
}

func (r *UnifiedFilialRepository) GetFiliaisByUserID(userID int64) ([]models.Filial, error) {
	// Implementar conforme necessário
	return nil, nil
}

func (r *UnifiedFilialRepository) CreateFilial(filial *models.Filial) error {
	// Criar no repositório de filial
	if r.filialRepo != nil {
		if err := r.filialRepo.CreateFilial(filial); err != nil {
			return err
		}
	}

	// Criar no repositório de estação
	if r.stationRepo != nil {
		station := adapters.ConvertFilialToStation(filial)
		if err := r.stationRepo.CreateStation(station); err != nil {
			return err
		}
	}

	// Criar no repositório de branch
	if r.branchRepo != nil {
		branch := adapters.ConvertFilialToBranch(filial)
		if err := r.branchRepo.CreateBranch(branch); err != nil {
			return err
		}
	}

	return nil
}

func (r *UnifiedFilialRepository) UpdateFilial(filial *models.Filial) error {
	// Atualizar no repositório de filial
	if r.filialRepo != nil {
		if err := r.filialRepo.UpdateFilial(filial); err != nil {
			return err
		}
	}

	// Atualizar no repositório de estação
	if r.stationRepo != nil {
		station := adapters.ConvertFilialToStation(filial)
		if err := r.stationRepo.UpdateStation(station); err != nil {
			return err
		}
	}

	// Atualizar no repositório de branch
	if r.branchRepo != nil {
		branch := adapters.ConvertFilialToBranch(filial)
		if err := r.branchRepo.UpdateBranch(branch); err != nil {
			return err
		}
	}

	return nil
}

func (r *UnifiedFilialRepository) DeleteFilial(id uint) error {
	// Excluir do repositório de filial
	if r.filialRepo != nil {
		if err := r.filialRepo.DeleteFilial(id); err != nil {
			return err
		}
	}

	// Excluir do repositório de estação
	if r.stationRepo != nil {
		if err := r.stationRepo.DeleteStation(id); err != nil {
			return err
		}
	}

	// Excluir do repositório de branch
	if r.branchRepo != nil {
		if err := r.branchRepo.DeleteBranch(id); err != nil {
			return err
		}
	}

	return nil
}

func (r *UnifiedFilialRepository) GetFiliaisByRegion(region string) ([]models.Filial, error) {
	// Implementar conforme necessário
	return nil, nil
}

func (r *UnifiedFilialRepository) GetActiveFiliais() ([]models.Filial, error) {
	// Implementar conforme necessário
	return nil, nil
}

func (r *UnifiedFilialRepository) GetFilialMetrics() (*models.FilialMetrics, error) {
	// Implementar conforme necessário
	return nil, nil
}

// Implementação de IStationRepository

func (r *UnifiedFilialRepository) GetStationByID(id uint) (*models.Station, error) {
	filial, err := r.GetFilialByID(id)
	if err != nil {
		return nil, err
	}
	if filial == nil {
		return nil, nil
	}
	return adapters.ConvertFilialToStation(filial), nil
}

func (r *UnifiedFilialRepository) GetAllStations() ([]models.Station, error) {
	filiais, err := r.GetAllFiliais()
	if err != nil {
		return nil, err
	}

	stations := make([]models.Station, len(filiais))
	for i, filial := range filiais {
		station := adapters.ConvertFilialToStation(&filial)
		stations[i] = *station
	}

	return stations, nil
}

func (r *UnifiedFilialRepository) GetStationsByUserID(userID int64) ([]models.Station, error) {
	// Implementar conforme necessário
	return nil, nil
}

func (r *UnifiedFilialRepository) CreateStation(station *models.Station) error {
	filial := adapters.ConvertStationToFilial(station)
	return r.CreateFilial(filial)
}

func (r *UnifiedFilialRepository) UpdateStation(station *models.Station) error {
	filial := adapters.ConvertStationToFilial(station)
	return r.UpdateFilial(filial)
}

func (r *UnifiedFilialRepository) DeleteStation(id uint) error {
	return r.DeleteFilial(id)
}

func (r *UnifiedFilialRepository) GetStationsByRegion(region string) ([]models.Station, error) {
	// Implementar conforme necessário
	return nil, nil
}

func (r *UnifiedFilialRepository) GetActiveStations() ([]models.Station, error) {
	// Implementar conforme necessário
	return nil, nil
}

func (r *UnifiedFilialRepository) GetStationMetrics() (*models.StationMetrics, error) {
	// Implementar conforme necessário
	return nil, nil
}

// Implementação de IBranchRepository

func (r *UnifiedFilialRepository) GetBranchByID(id uint) (*models.Branch, error) {
	filial, err := r.GetFilialByID(id)
	if err != nil {
		return nil, err
	}
	if filial == nil {
		return nil, nil
	}
	return adapters.ConvertFilialToBranch(filial), nil
}

func (r *UnifiedFilialRepository) GetAllBranches(page, limit int, search string, isActive *bool) ([]models.Branch, int64, error) {
	filiais, err := r.GetAllFiliais()
	if err != nil {
		return nil, 0, err
	}

	// Aplicar filtros
	var filteredFiliais []models.Filial
	for _, filial := range filiais {
		if search != "" {
			// Implementar filtro de busca
		}
		if isActive != nil && filial.IsActive != *isActive {
			continue
		}
		filteredFiliais = append(filteredFiliais, filial)
	}

	// Aplicar paginação
	start := (page - 1) * limit
	end := start + limit
	if start >= len(filteredFiliais) {
		return []models.Branch{}, int64(len(filteredFiliais)), nil
	}
	if end > len(filteredFiliais) {
		end = len(filteredFiliais)
	}

	paginatedFiliais := filteredFiliais[start:end]

	// Converter para Branch
	branches := make([]models.Branch, len(paginatedFiliais))
	for i, filial := range paginatedFiliais {
		branch := adapters.ConvertFilialToBranch(&filial)
		branches[i] = *branch
	}

	return branches, int64(len(filteredFiliais)), nil
}

func (r *UnifiedFilialRepository) GetBranchesByUserID(userID int64) ([]models.Branch, error) {
	// Implementar conforme necessário
	return nil, nil
}

func (r *UnifiedFilialRepository) GetBranchesByTechnicianID(technicianID int64) ([]models.Branch, error) {
	// Implementar conforme necessário
	return nil, nil
}

func (r *UnifiedFilialRepository) CreateBranch(branch *models.Branch) error {
	filial := adapters.ConvertBranchToFilial(branch)
	return r.CreateFilial(filial)
}

func (r *UnifiedFilialRepository) UpdateBranch(branch *models.Branch) error {
	filial := adapters.ConvertBranchToFilial(branch)
	return r.UpdateFilial(filial)
}

func (r *UnifiedFilialRepository) DeleteBranch(id uint) error {
	return r.DeleteFilial(id)
}

func (r *UnifiedFilialRepository) BranchExists(id uint) (bool, error) {
	filial, err := r.GetFilialByID(id)
	if err != nil {
		return false, err
	}
	return filial != nil, nil
}

func (r *UnifiedFilialRepository) LinkProvider(branchID, providerID uint) error {
	// Implementar conforme necessário
	return nil
}

func (r *UnifiedFilialRepository) UnlinkProvider(branchID, providerID uint) error {
	// Implementar conforme necessário
	return nil
}

func (r *UnifiedFilialRepository) GetLinkedProviders(branchID uint) ([]uint, error) {
	// Implementar conforme necessário
	return nil, nil
}

func (r *UnifiedFilialRepository) GenerateAuthToken(branchID uint) (string, error) {
	// Implementar conforme necessário
	return "", nil
}

func (r *UnifiedFilialRepository) ValidateAuthToken(token string) (uint, error) {
	// Implementar conforme necessário
	return 0, nil
}

func (r *UnifiedFilialRepository) UseAuthToken(token string) error {
	// Implementar conforme necessário
	return nil
}
