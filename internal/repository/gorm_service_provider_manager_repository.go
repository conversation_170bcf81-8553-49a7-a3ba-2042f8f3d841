package repository

import (
	"tradicao/internal/database"
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// GormServiceProviderManagerRepository implementa ServiceProviderManagerRepository usando GORM
type GormServiceProviderManagerRepository struct {
	db *gorm.DB
}

// NewGormServiceProviderManagerRepository cria um novo GormServiceProviderManagerRepository
func NewGormServiceProviderManagerRepository() *GormServiceProviderManagerRepository {
	db := database.GetGormDB()
	if db == nil {
		var err error
		db, err = database.InitGorm()
		if err != nil {
			return nil
		}
	}
	return &GormServiceProviderManagerRepository{db: db}
}

// FindAll retorna todos os gestores de prestadoras
func (r *GormServiceProviderManagerRepository) FindAll() ([]models.ServiceProviderManager, error) {
	var managers []models.ServiceProviderManager
	err := r.db.Preload("User").Preload("ServiceProvider").Find(&managers).Error
	return managers, err
}

// FindByID retorna um gestor de prestadora pelo ID
func (r *GormServiceProviderManagerRepository) FindByID(id uint) (*models.ServiceProviderManager, error) {
	var manager models.ServiceProviderManager
	err := r.db.Preload("User").Preload("ServiceProvider").First(&manager, id).Error
	return &manager, err
}

// FindByUserID retorna gestores de prestadoras pelo ID do usuário
func (r *GormServiceProviderManagerRepository) FindByUserID(userID uint) ([]models.ServiceProviderManager, error) {
	var managers []models.ServiceProviderManager
	err := r.db.Where("user_id = ?", userID).Preload("ServiceProvider").Find(&managers).Error
	return managers, err
}

// FindByProviderID retorna gestores de prestadoras pelo ID da prestadora
func (r *GormServiceProviderManagerRepository) FindByProviderID(providerID uint) ([]models.ServiceProviderManager, error) {
	var managers []models.ServiceProviderManager
	err := r.db.Where("service_provider_id = ?", providerID).Preload("User").Find(&managers).Error
	return managers, err
}

// Create cria um novo gestor de prestadora
func (r *GormServiceProviderManagerRepository) Create(manager *models.ServiceProviderManager) error {
	return r.db.Create(manager).Error
}

// Update atualiza um gestor de prestadora
func (r *GormServiceProviderManagerRepository) Update(manager *models.ServiceProviderManager) error {
	return r.db.Save(manager).Error
}

// Delete remove um gestor de prestadora
func (r *GormServiceProviderManagerRepository) Delete(id uint) error {
	return r.db.Delete(&models.ServiceProviderManager{}, id).Error
}

// IsProviderManager verifica se um usuário é gestor de uma prestadora
func (r *GormServiceProviderManagerRepository) IsProviderManager(providerID uint, userID uint) (bool, error) {
	var count int64
	err := r.db.Model(&models.ServiceProviderManager{}).
		Where("service_provider_id = ? AND user_id = ?", providerID, userID).
		Count(&count).Error
	return count > 0, err
}
