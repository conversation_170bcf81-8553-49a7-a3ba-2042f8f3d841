package repository

import (
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// GormOrderRepository implementa OrderRepository usando GORM
type GormOrderRepository struct {
	db *gorm.DB
}

// NewGormOrderRepository cria um novo repositório de ordens com GORM
func NewGormOrderRepository(db *gorm.DB) *GormOrderRepository {
	return &GormOrderRepository{db: db}
}

// GetOrderByID busca uma ordem de serviço pelo ID (método original)
func (r *GormOrderRepository) GetOrderByID(id int64) (*models.MaintenanceOrder, error) {
	var order models.MaintenanceOrder
	if err := r.db.First(&order, id).Error; err != nil {
		return nil, err
	}
	return &order, nil
}

// GetByID busca uma ordem pelo ID (método para o adaptador)
func (r *GormOrderRepository) GetByID(id uint) (*models.MaintenanceOrder, error) {
	var order models.MaintenanceOrder
	if err := r.db.First(&order, id).Error; err != nil {
		return nil, err
	}
	return &order, nil
}

// GetAll busca todas as ordens com paginação e filtros
func (r *GormOrderRepository) GetAll(offset, limit int, filter string) ([]models.MaintenanceOrder, error) {
	var orders []models.MaintenanceOrder
	query := r.db

	// Aplicar filtro se fornecido
	if filter != "" {
		query = query.Where("status = ?", filter)
	}

	// Aplicar paginação
	query = query.Offset(offset).Limit(limit)

	// Executar a consulta
	if err := query.Find(&orders).Error; err != nil {
		return nil, err
	}

	return orders, nil
}

// Create cria uma nova ordem
func (r *GormOrderRepository) Create(order *models.MaintenanceOrder) error {
	return r.db.Create(order).Error
}

// Update atualiza uma ordem existente
func (r *GormOrderRepository) Update(order *models.MaintenanceOrder) error {
	return r.db.Save(order).Error
}

// Delete remove uma ordem
func (r *GormOrderRepository) Delete(id uint) error {
	return r.db.Delete(&models.MaintenanceOrder{}, id).Error
}
