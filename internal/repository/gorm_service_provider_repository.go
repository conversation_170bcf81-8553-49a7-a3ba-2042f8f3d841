package repository

import (
	"tradicao/internal/database"
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// GormServiceProviderRepository representa um repositório de prestadores de serviço usando GORM
type GormServiceProviderRepository struct {
	db *gorm.DB
}

// NewGormServiceProviderRepository cria uma nova instância do repositório de prestadores
func NewGormServiceProviderRepository() *GormServiceProviderRepository {
	db := database.GetGormDB()
	if db == nil {
		var err error
		db, err = database.InitGorm()
		if err != nil {
			return nil
		}
	}
	return &GormServiceProviderRepository{db: db}
}

// Create cria um novo prestador de serviço
func (r *GormServiceProviderRepository) Create(provider models.ServiceProvider) (models.ServiceProvider, error) {
	if err := r.db.Create(&provider).Error; err != nil {
		return models.ServiceProvider{}, err
	}
	return provider, nil
}

// FindByID busca um prestador pelo ID
func (r *GormServiceProviderRepository) FindByID(id uint) (models.ServiceProvider, error) {
	var provider models.ServiceProvider
	if err := r.db.First(&provider, id).Error; err != nil {
		return models.ServiceProvider{}, err
	}
	return provider, nil
}

// FindAll busca todos os prestadores
func (r *GormServiceProviderRepository) FindAll() ([]models.ServiceProvider, error) {
	var providers []models.ServiceProvider
	if err := r.db.Find(&providers).Error; err != nil {
		return nil, err
	}
	return providers, nil
}

// FindActive busca todos os prestadores ativos
func (r *GormServiceProviderRepository) FindActive() ([]models.ServiceProvider, error) {
	var providers []models.ServiceProvider
	if err := r.db.Where("status = ?", "ativo").Find(&providers).Error; err != nil {
		return nil, err
	}
	return providers, nil
}

// Update atualiza um prestador existente
func (r *GormServiceProviderRepository) Update(provider models.ServiceProvider) (models.ServiceProvider, error) {
	if err := r.db.Save(&provider).Error; err != nil {
		return models.ServiceProvider{}, err
	}
	return provider, nil
}

// Delete remove um prestador
func (r *GormServiceProviderRepository) Delete(id uint) error {
	return r.db.Delete(&models.ServiceProvider{}, id).Error
}

// Search busca prestadores por termo
func (r *GormServiceProviderRepository) Search(term string) ([]models.ServiceProvider, error) {
	var providers []models.ServiceProvider
	if err := r.db.Where("name LIKE ? OR cnpj LIKE ? OR specialties LIKE ? OR area_of_expertise LIKE ?",
		"%"+term+"%", "%"+term+"%", "%"+term+"%", "%"+term+"%").Find(&providers).Error; err != nil {
		return nil, err
	}
	return providers, nil
}

// FindBySpecialty busca prestadores por especialidade
func (r *GormServiceProviderRepository) FindBySpecialty(specialty string) ([]models.ServiceProvider, error) {
	var providers []models.ServiceProvider
	if err := r.db.Where("specialties LIKE ? OR area_of_expertise LIKE ?",
		"%"+specialty+"%", "%"+specialty+"%").Find(&providers).Error; err != nil {
		return nil, err
	}
	return providers, nil
}

// FindByBranch busca prestadores vinculados a uma filial
func (r *GormServiceProviderRepository) FindByBranch(branchID uint) ([]models.ServiceProvider, error) {
	var links []models.BranchProviderLink
	if err := r.db.Where("branch_id = ?", branchID).Find(&links).Error; err != nil {
		return nil, err
	}

	// Se não houver prestadores vinculados, retorna array vazio
	if len(links) == 0 {
		return []models.ServiceProvider{}, nil
	}

	// Extrair IDs dos prestadores
	providerIDs := make([]uint, len(links))
	for i, link := range links {
		providerIDs[i] = link.ServiceProviderID
	}

	// Buscar os prestadores pelo ID
	var providers []models.ServiceProvider
	if err := r.db.Where("id IN ?", providerIDs).Find(&providers).Error; err != nil {
		return nil, err
	}

	return providers, nil
}

// UpdateRating atualiza a classificação média de um prestador
func (r *GormServiceProviderRepository) UpdateRating(id uint, rating float64) error {
	return r.db.Model(&models.ServiceProvider{}).Where("id = ?", id).
		Update("average_rating", rating).Error
}

// Count retorna o número total de prestadores
func (r *GormServiceProviderRepository) Count() (int64, error) {
	var count int64
	if err := r.db.Model(&models.ServiceProvider{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// GetDB retorna a instância do GORM DB para operações personalizadas
func (r *GormServiceProviderRepository) GetDB() interface{} {
	return r.db
}

// GetTechnicians retorna todos os técnicos de um prestador
func (r *GormServiceProviderRepository) GetTechnicians(providerID uint) ([]models.User, error) {
	// Buscar IDs dos técnicos vinculados ao prestador
	var technicianIDs []uint
	if err := r.db.Table("provider_technicians").
		Where("provider_id = ?", providerID).
		Pluck("technician_id", &technicianIDs).Error; err != nil {
		return nil, err
	}

	// Se não houver técnicos vinculados, retornar lista vazia
	if len(technicianIDs) == 0 {
		return []models.User{}, nil
	}

	// Buscar os detalhes dos técnicos
	var technicians []models.User
	if err := r.db.Where("id IN ?", technicianIDs).Find(&technicians).Error; err != nil {
		return nil, err
	}

	return technicians, nil
}

// AddTechnician adiciona um técnico a um prestador
func (r *GormServiceProviderRepository) AddTechnician(providerID uint, userID uint) error {
	return r.db.Model(&models.User{}).Where("id = ?", userID).Update("service_provider_id", providerID).Error
}

// RemoveTechnician remove um técnico de um prestador
func (r *GormServiceProviderRepository) RemoveTechnician(userID uint) error {
	return r.db.Model(&models.User{}).Where("id = ?", userID).Update("service_provider_id", nil).Error
}

// UpdateLogo atualiza a URL da logo de um prestador
func (r *GormServiceProviderRepository) UpdateLogo(providerID uint, logoURL string) error {
	return r.db.Model(&models.ServiceProvider{}).
		Where("id = ?", providerID).
		Update("logo_url", logoURL).Error
}
