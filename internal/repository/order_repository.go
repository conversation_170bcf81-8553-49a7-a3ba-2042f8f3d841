package repository

import (
	"database/sql"
	"fmt"
	"log"
	"time"
	"tradicao/internal/models"

	"github.com/patrickmn/go-cache"
)

// OrderRepository define a interface para operações com ordens de serviço
type OrderRepository interface {
	GetOrderByID(id uint) (*models.MaintenanceOrder, error)
	GetOrdersWithPagination(page, pageSize int, status models.OrderStatus, branchID uint, startDate, endDate time.Time) ([]models.MaintenanceOrder, int, error)
	GetOrderCountsByStatus() (map[models.OrderStatus]int, error)
	GetOrderInteractions(orderID uint) ([]models.Interaction, error)
	GetOrderCosts(orderID uint) ([]models.CostItem, error)
}

// SQLOrderRepository implementa OrderRepository usando SQL
type SQLOrderRepository struct {
	db    *sql.DB
	cache *cache.Cache
}

// NewSQLOrderRepository cria uma nova instância de SQLOrderRepository
func NewSQLOrderRepository(db *sql.DB) *SQLOrderRepository {
	// Criar índices otimizados
	createIndexes(db)
	return &SQLOrderRepository{
		db:    db,
		cache: cache.New(5*time.Minute, 10*time.Minute),
	}
}

// createIndexes cria os índices necessários para otimização
func createIndexes(db *sql.DB) {
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_orders_status ON maintenance_orders(status)",
		"CREATE INDEX IF NOT EXISTS idx_orders_branch ON maintenance_orders(branch_id)",
		"CREATE INDEX IF NOT EXISTS idx_orders_dates ON maintenance_orders(scheduled_start_date, scheduled_end_date)",
		"CREATE INDEX IF NOT EXISTS idx_orders_created ON maintenance_orders(created_at)",
		"CREATE INDEX IF NOT EXISTS idx_interactions_order ON order_interactions(order_id)",
		"CREATE INDEX IF NOT EXISTS idx_costs_order ON order_costs(order_id)",
	}

	for _, index := range indexes {
		_, err := db.Exec(index)
		if err != nil {
			log.Printf("Erro ao criar índice: %v", err)
		}
	}
}

// GetOrderByID busca uma ordem pelo ID
func (r *SQLOrderRepository) GetOrderByID(id uint) (*models.MaintenanceOrder, error) {
	query := `
		SELECT 
			o.id, o.title, o.problem, o.branch_id, b.name as branch_name,
			o.number, o.status, o.priority, o.service_provider_id,
			o.created_by_user_id, u.name as created_by_name,
			o.created_at, o.open_date, o.due_date, o.updated_at,
			o.estimated_cost, o.actual_cost, o.completion_date,
			o.approval_status, o.approval_date, o.payment_status,
			o.payment_date, o.rating, o.partial_functioning,
			o.extra_equipment, o.same_day, o.part_replacement
		FROM maintenance_orders o
		LEFT JOIN branches b ON o.branch_id = b.id
		LEFT JOIN users u ON o.created_by_user_id = u.id
		WHERE o.id = $1`

	order := &models.MaintenanceOrder{}
	err := r.db.QueryRow(query, id).Scan(
		&order.ID, &order.Title, &order.Problem, &order.BranchID, &order.BranchName,
		&order.Number, &order.Status, &order.Priority, &order.ServiceProviderID,
		&order.CreatedByUserID, &order.CreatedByName,
		&order.CreatedAt, &order.OpenDate, &order.DueDate, &order.UpdatedAt,
		&order.EstimatedCost, &order.ActualCost, &order.CompletionDate,
		&order.ApprovalStatus, &order.ApprovalDate, &order.PaymentStatus,
		&order.PaymentDate, &order.Rating, &order.PartialFunctioning,
		&order.ExtraEquipment, &order.SameDay, &order.PartReplacement,
	)
	if err != nil {
		return nil, err
	}

	return order, nil
}

// GetOrdersWithPagination busca ordens com paginação e filtros
func (r *SQLOrderRepository) GetOrdersWithPagination(page, pageSize int, status models.OrderStatus, branchID uint, startDate, endDate time.Time) ([]models.MaintenanceOrder, int, error) {
	cacheKey := fmt.Sprintf("orders:%d:%d:%s:%d:%s:%s", page, pageSize, status, branchID, startDate.Format(time.RFC3339), endDate.Format(time.RFC3339))

	// Verificar cache
	if cached, found := r.cache.Get(cacheKey); found {
		return cached.([]models.MaintenanceOrder), 0, nil
	}

	offset := (page - 1) * pageSize
	query := `
		SELECT 
			mo.id, mo.title, mo.description, mo.status, mo.priority, 
			mo.created_at, mo.updated_at, mo.equipment_id, mo.branch_id,
			mo.created_by_user_id, mo.assigned_to_user_id,
			b.name as branch_name,
			u.name as created_by_name,
			e.name as equipment_name
		FROM maintenance_orders mo
		LEFT JOIN branches b ON mo.branch_id = b.id
		LEFT JOIN users u ON mo.created_by_user_id = u.id
		LEFT JOIN equipment e ON mo.equipment_id = e.id
		WHERE 1=1
	`

	var args []interface{}
	if status != "" {
		query += " AND mo.status = ?"
		args = append(args, status)
	}
	if branchID > 0 {
		query += " AND mo.branch_id = ?"
		args = append(args, branchID)
	}
	if !startDate.IsZero() {
		query += " AND mo.created_at >= ?"
		args = append(args, startDate)
	}
	if !endDate.IsZero() {
		query += " AND mo.created_at <= ?"
		args = append(args, endDate)
	}

	query += " ORDER BY mo.created_at DESC LIMIT ? OFFSET ?"
	args = append(args, pageSize, offset)

	rows, err := r.db.Query(query, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var orders []models.MaintenanceOrder
	for rows.Next() {
		var order models.MaintenanceOrder
		err := rows.Scan(
			&order.ID, &order.Title, &order.Description, &order.Status, &order.Priority,
			&order.CreatedAt, &order.UpdatedAt, &order.EquipmentID, &order.BranchID,
			&order.CreatedByUserID, &order.AssignedToUserID,
			&order.BranchName, &order.CreatedByName, &order.EquipmentName,
		)
		if err != nil {
			return nil, 0, err
		}
		orders = append(orders, order)
	}

	// Armazenar no cache
	r.cache.Set(cacheKey, orders, cache.DefaultExpiration)

	return orders, len(orders), nil
}

// GetOrderCountsByStatus retorna a contagem de ordens por status
func (r *SQLOrderRepository) GetOrderCountsByStatus() (map[models.OrderStatus]int, error) {
	query := `
		SELECT status, COUNT(*) as count
		FROM maintenance_orders
		GROUP BY status`

	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	counts := make(map[models.OrderStatus]int)
	for rows.Next() {
		var status models.OrderStatus
		var count int
		if err := rows.Scan(&status, &count); err != nil {
			return nil, err
		}
		counts[status] = count
	}
	return counts, nil
}

// GetOrderInteractions busca as interações de uma ordem
func (r *SQLOrderRepository) GetOrderInteractions(orderID uint) ([]models.Interaction, error) {
	query := `
		SELECT 
			i.id, i.maintenance_order_id, i.user_id, u.name as user_name,
			i.message, i.timestamp, i.created_at, i.updated_at
		FROM interactions i
		LEFT JOIN users u ON i.user_id = u.id
		WHERE i.maintenance_order_id = $1
		ORDER BY i.created_at DESC`

	rows, err := r.db.Query(query, orderID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var interactions []models.Interaction
	for rows.Next() {
		var interaction models.Interaction
		err := rows.Scan(
			&interaction.ID, &interaction.MaintenanceOrderID, &interaction.UserID, &interaction.User.Name,
			&interaction.Message, &interaction.Timestamp, &interaction.CreatedAt, &interaction.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		interactions = append(interactions, interaction)
	}
	return interactions, nil
}

// GetOrderCosts busca os custos de uma ordem
func (r *SQLOrderRepository) GetOrderCosts(orderID uint) ([]models.CostItem, error) {
	query := `
		SELECT 
			c.id, c.maintenance_order_id, c.added_by_user_id, u.name as added_by_name,
			c.description, c.quantity, c.unit_price, c.total_item_cost,
			c.created_at, c.updated_at
		FROM cost_items c
		LEFT JOIN users u ON c.added_by_user_id = u.id
		WHERE c.maintenance_order_id = $1
		ORDER BY c.created_at DESC`

	rows, err := r.db.Query(query, orderID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var costs []models.CostItem
	for rows.Next() {
		var cost models.CostItem
		err := rows.Scan(
			&cost.ID, &cost.MaintenanceOrderID, &cost.AddedByUserID, &cost.AddedByUser.Name,
			&cost.Description, &cost.Quantity, &cost.UnitPrice, &cost.TotalItemCost,
			&cost.CreatedAt, &cost.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		costs = append(costs, cost)
	}
	return costs, nil
}
