package repository

import (
	"testing"
	"time"
	"tradicao/internal/models"

	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupTechnicianTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(t, err)

	// Migrar tabelas
	err = db.AutoMigrate(
		&models.TechnicianSpecialty{},
		&models.TechnicianBranch{},
		&models.MaintenanceHistory{},
	)
	assert.NoError(t, err)

	return db
}

func TestTechnicianRepository(t *testing.T) {
	db := setupTechnicianTestDB(t)
	repo := NewTechnicianRepository(db)

	t.Run("Test CreateSpecialty", func(t *testing.T) {
		specialty := &models.TechnicianSpecialty{
			Name:        "Eletricista",
			Description: "Especialista em manutenção elétrica",
		}

		err := repo.CreateSpecialty(specialty)
		assert.NoError(t, err)
		assert.NotZero(t, specialty.ID)
	})

	t.Run("Test GetSpecialty", func(t *testing.T) {
		specialty := &models.TechnicianSpecialty{
			Name:        "Mecânico",
			Description: "Especialista em manutenção mecânica",
		}

		err := repo.CreateSpecialty(specialty)
		assert.NoError(t, err)

		found, err := repo.GetSpecialty(specialty.ID)
		assert.NoError(t, err)
		assert.Equal(t, specialty.Name, found.Name)
	})

	t.Run("Test CreateBranchAssociation", func(t *testing.T) {
		association := &models.TechnicianBranch{
			TechnicianID: 1,
			BranchID:     1,
			SpecialtyID:  1,
		}

		err := repo.CreateBranchAssociation(association)
		assert.NoError(t, err)
	})

	t.Run("Test ListBranchAssociations", func(t *testing.T) {
		associations, err := repo.ListBranchAssociations(1)
		assert.NoError(t, err)
		assert.NotNil(t, associations)
	})

	t.Run("Test CreateMaintenanceHistory", func(t *testing.T) {
		history := &models.MaintenanceHistory{
			TechnicianID: 1,
			OrderID:      1,
			EquipmentID:  1,
			Description:  "Manutenção preventiva",
			Status:       "Concluído",
		}

		err := repo.CreateMaintenanceHistory(history)
		assert.NoError(t, err)
		assert.NotZero(t, history.ID)
	})

	t.Run("Test ListMaintenanceHistory", func(t *testing.T) {
		startDate := time.Now().AddDate(0, -1, 0)
		endDate := time.Now()

		history, err := repo.ListMaintenanceHistory(1, startDate, endDate)
		assert.NoError(t, err)
		assert.NotNil(t, history)
	})
}
