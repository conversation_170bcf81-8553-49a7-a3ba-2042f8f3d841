package repository

import (
	"log"
	"tradicao/internal/models"
)

// GetUsersByBranchID retorna todos os usuários de uma filial específica
func (r *SQLUserRepository) GetUsersByBranchID(branchID int64) ([]models.User, error) {
	query := `
		SELECT id, name, email, type, active, branch_id, failed_attempts, blocked, totp_secret, totp_enabled, created_at, updated_at 
		FROM users 
		WHERE branch_id = $1
	`
	
	rows, err := r.db.Query(query, branchID)
	if err != nil {
		log.Printf("Erro ao buscar usuários da filial %d: %v", branchID, err)
		return nil, err
	}
	defer rows.Close()
	
	var users []models.User
	for rows.Next() {
		var user models.User
		var isActive bool
		err := rows.Scan(
			&user.ID,
			&user.Name,
			&user.Email,
			&user.Role,
			&isActive,
			&user.BranchID,
			&user.FailedAttempts,
			&user.Blocked,
			&user.TOTPSecret,
			&user.TOTPEnabled,
			&user.CreatedAt,
			&user.UpdatedAt,
		)
		if err != nil {
			log.Printf("Erro ao escanear usuário: %v", err)
			continue
		}
		
		// Limpar senha por segurança
		user.Password = ""
		
		users = append(users, user)
	}
	
	if err := rows.Err(); err != nil {
		log.Printf("Erro ao iterar sobre usuários: %v", err)
		return nil, err
	}
	
	return users, nil
}

// GetUsersByBranchID retorna todos os usuários de uma filial específica
func (r *GormUserRepository) GetUsersByBranchID(branchID int64) ([]models.User, error) {
	var users []models.User
	
	if err := r.db.Where("branch_id = ?", branchID).Find(&users).Error; err != nil {
		log.Printf("Erro ao buscar usuários da filial %d: %v", branchID, err)
		return nil, err
	}
	
	// Limpar senhas por segurança
	for i := range users {
		users[i].Password = ""
	}
	
	return users, nil
}
