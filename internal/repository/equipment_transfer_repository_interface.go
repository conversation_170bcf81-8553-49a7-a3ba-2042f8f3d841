package repository

import (
	"context"

	"tradicao/internal/models"
)

// IEquipmentTransferRepository define a interface para o repositório de transferências de equipamentos
type IEquipmentTransferRepository interface {
	// Create cria uma nova transferência de equipamento
	Create(ctx context.Context, transfer *models.EquipmentTransfer) error

	// FindByID busca uma transferência de equipamento pelo ID
	FindByID(ctx context.Context, id uint) (*models.EquipmentTransfer, error)

	// FindByEquipmentID busca transferências de um equipamento específico
	FindByEquipmentID(ctx context.Context, equipmentID uint) ([]models.EquipmentTransfer, error)

	// FindPendingBySourceBranch busca transferências pendentes de uma filial de origem
	FindPendingBySourceBranch(ctx context.Context, branchID uint) ([]models.EquipmentTransfer, error)

	// FindPendingByDestinationBranch busca transferências pendentes para uma filial de destino
	FindPendingByDestinationBranch(ctx context.Context, branchID uint) ([]models.EquipmentTransfer, error)

	// Update atualiza uma transferência de equipamento
	Update(ctx context.Context, transfer *models.EquipmentTransfer) error

	// UpdateStatus atualiza o status de uma transferência de equipamento
	UpdateStatus(ctx context.Context, id uint, status models.StatusTransferencia, userID *uint, notes string) error

	// CompleteTransfer completa uma transferência, atualizando o branch_id do equipamento
	CompleteTransfer(ctx context.Context, transferID uint) error

	// List lista todas as transferências de equipamentos
	List(ctx context.Context) ([]models.EquipmentTransfer, error)
}
