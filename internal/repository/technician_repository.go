package repository

import (
	"time"
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// TechnicianRepository define a interface para operações relacionadas a técnicos
type TechnicianRepository interface {
	// Especialidades
	CreateSpecialty(specialty *models.TechnicianSpecialty) error
	ListSpecialties() ([]models.TechnicianSpecialty, error)
	UpdateSpecialty(specialty *models.TechnicianSpecialty) error
	DeleteSpecialty(id uint) error
	GetSpecialty(id uint) (*models.TechnicianSpecialty, error)

	// Vinculação com filiais
	CreateBranchAssociation(association *models.TechnicianBranch) error
	UpdateBranchAssociation(association *models.TechnicianBranch) error
	DeleteBranchAssociation(technicianID, branchID uint) error
	GetBranchAssociation(technicianID, branchID uint) (*models.TechnicianBranch, error)
	ListBranchAssociations(technicianID uint) ([]models.TechnicianBranch, error)
	GetBranchTechnicians(branchID uint) ([]models.Technician, error)

	// Histórico de manutenções
	CreateMaintenanceHistory(history *models.MaintenanceHistory) error
	ListMaintenanceHistory(technicianID uint, startDate, endDate time.Time) ([]models.MaintenanceHistory, error)
	GetMaintenanceHistory(id uint) (*models.MaintenanceHistory, error)
}

// TechnicianRepositoryImpl implementa TechnicianRepository
type TechnicianRepositoryImpl struct {
	db *gorm.DB
}

// NewTechnicianRepository cria uma nova instância do repositório
func NewTechnicianRepository(db *gorm.DB) TechnicianRepository {
	return &TechnicianRepositoryImpl{db: db}
}

// Implementação dos métodos da interface

func (r *TechnicianRepositoryImpl) CreateSpecialty(specialty *models.TechnicianSpecialty) error {
	return r.db.Create(specialty).Error
}

func (r *TechnicianRepositoryImpl) ListSpecialties() ([]models.TechnicianSpecialty, error) {
	var specialties []models.TechnicianSpecialty
	err := r.db.Find(&specialties).Error
	return specialties, err
}

func (r *TechnicianRepositoryImpl) UpdateSpecialty(specialty *models.TechnicianSpecialty) error {
	return r.db.Save(specialty).Error
}

func (r *TechnicianRepositoryImpl) DeleteSpecialty(id uint) error {
	return r.db.Delete(&models.TechnicianSpecialty{}, id).Error
}

func (r *TechnicianRepositoryImpl) GetSpecialty(id uint) (*models.TechnicianSpecialty, error) {
	var specialty models.TechnicianSpecialty
	err := r.db.First(&specialty, id).Error
	return &specialty, err
}

func (r *TechnicianRepositoryImpl) CreateBranchAssociation(association *models.TechnicianBranch) error {
	return r.db.Create(association).Error
}

func (r *TechnicianRepositoryImpl) UpdateBranchAssociation(association *models.TechnicianBranch) error {
	return r.db.Save(association).Error
}

func (r *TechnicianRepositoryImpl) DeleteBranchAssociation(technicianID, branchID uint) error {
	return r.db.Where("technician_id = ? AND branch_id = ?", technicianID, branchID).
		Delete(&models.TechnicianBranch{}).Error
}

func (r *TechnicianRepositoryImpl) GetBranchAssociation(technicianID, branchID uint) (*models.TechnicianBranch, error) {
	var association models.TechnicianBranch
	err := r.db.Where("technician_id = ? AND branch_id = ?", technicianID, branchID).
		Preload("Technician").
		Preload("Branch").
		Preload("Specialty").
		First(&association).Error
	return &association, err
}

func (r *TechnicianRepositoryImpl) ListBranchAssociations(technicianID uint) ([]models.TechnicianBranch, error) {
	var associations []models.TechnicianBranch
	err := r.db.Where("technician_id = ?", technicianID).
		Preload("Branch").
		Preload("Specialty").
		Find(&associations).Error
	return associations, err
}

func (r *TechnicianRepositoryImpl) CreateMaintenanceHistory(history *models.MaintenanceHistory) error {
	return r.db.Create(history).Error
}

func (r *TechnicianRepositoryImpl) ListMaintenanceHistory(technicianID uint, startDate, endDate time.Time) ([]models.MaintenanceHistory, error) {
	var history []models.MaintenanceHistory
	err := r.db.Where("technician_id = ? AND created_at BETWEEN ? AND ?",
		technicianID, startDate, endDate).
		Preload("Order").
		Preload("Equipment").
		Preload("Technician").
		Find(&history).Error
	return history, err
}

func (r *TechnicianRepositoryImpl) GetMaintenanceHistory(id uint) (*models.MaintenanceHistory, error) {
	var history models.MaintenanceHistory
	err := r.db.Preload("Order").
		Preload("Equipment").
		Preload("Technician").
		First(&history, id).Error
	return &history, err
}

// GetBranchTechnicians retorna os técnicos vinculados a uma filial
func (r *TechnicianRepositoryImpl) GetBranchTechnicians(branchID uint) ([]models.Technician, error) {
	var technicians []models.Technician

	// Consulta para obter os IDs dos técnicos vinculados à filial
	var technicianIDs []uint
	err := r.db.Model(&models.TechnicianBranch{}).
		Where("branch_id = ?", branchID).
		Pluck("technician_id", &technicianIDs).Error
	if err != nil {
		return nil, err
	}

	// Se não houver técnicos vinculados, retornar lista vazia
	if len(technicianIDs) == 0 {
		return []models.Technician{}, nil
	}

	// Buscar os detalhes dos técnicos
	for _, techID := range technicianIDs {
		var user models.User
		if err := r.db.First(&user, techID).Error; err != nil {
			continue // Pular se não encontrar o usuário
		}

		// Verificar se o usuário é um técnico
		if !user.IsTechnician() {
			continue // Pular se não for um técnico
		}

		// Criar objeto Technician a partir do User
		technician := models.Technician{
			ID:        user.ID,
			UserID:    user.ID,
			Name:      user.Name,
			Email:     user.Email,
			CreatedAt: user.CreatedAt,
			UpdatedAt: user.UpdatedAt,
		}

		technicians = append(technicians, technician)
	}

	return technicians, nil
}
