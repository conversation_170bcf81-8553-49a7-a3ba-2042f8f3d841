package db

import (
	"log"
	"os"
	"sync"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

var (
	db     *gorm.DB
	dbOnce sync.Once
)

// GetDB retorna a conexão com o banco de dados
func GetDB() *gorm.DB {
	dbOnce.Do(func() {
		// Obter string de conexão do ambiente
		dsn := os.Getenv("DATABASE_URL")
		if dsn == "" {
			log.Fatal("Variável de ambiente DATABASE_URL não definida")
		}

		// Configurar logger do GORM
		newLogger := logger.New(
			log.New(os.Stdout, "\r\n", log.LstdFlags),
			logger.Config{
				LogLevel:                  logger.Info,
				IgnoreRecordNotFoundError: true,
				Colorful:                  true,
			},
		)

		// Configurar GORM
		config := &gorm.Config{
			Logger: newLogger,
			NamingStrategy: schema.NamingStrategy{
				SingularTable: true,
			},
			PrepareStmt: true,
		}

		// Conectar ao banco
		var err error
		db, err = gorm.Open(postgres.Open(dsn), config)
		if err != nil {
			log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
		}

		log.Println("Conexão com o banco de dados estabelecida")

		// Obter conexão SQL subjacente para configurações adicionais
		sqlDB, err := db.DB()
		if err != nil {
			log.Fatalf("Erro ao obter conexão SQL: %v", err)
		}

		// Configurar pool de conexões
		sqlDB.SetMaxIdleConns(10)
		sqlDB.SetMaxOpenConns(100)
	})

	return db
}
