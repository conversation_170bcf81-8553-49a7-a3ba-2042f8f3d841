-- <PERSON><PERSON><PERSON> da tabela de especialidades
CREATE TABLE IF NOT EXISTS technician_specialties (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    equipment_types TEXT[] NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON> da tabela de vinculação técnico-filial
CREATE TABLE IF NOT EXISTS technician_branches (
    technician_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    branch_id INTEGER NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
    specialty_id INTEGER NOT NULL REFERENCES technician_specialties(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (technician_id, branch_id, specialty_id)
);

-- <PERSON><PERSON><PERSON> da tabela de histórico de manutenções
CREATE TABLE IF NOT EXISTS maintenance_history (
    id SERIAL PRIMARY KEY,
    technician_id INTEGER NOT NULL REFERENCES users(id),
    order_id INTEGER NOT NULL REFERENCES maintenance_orders(id),
    equipment_id INTEGER NOT NULL REFERENCES equipments(id),
    description TEXT NOT NULL,
    parts_used TEXT[],
    costs DECIMAL(10,2),
    status VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Índices para melhor performance
CREATE INDEX idx_technician_branches_technician_id ON technician_branches(technician_id);
CREATE INDEX idx_technician_branches_branch_id ON technician_branches(branch_id);
CREATE INDEX idx_maintenance_history_technician_id ON maintenance_history(technician_id);
CREATE INDEX idx_maintenance_history_order_id ON maintenance_history(order_id);
CREATE INDEX idx_maintenance_history_equipment_id ON maintenance_history(equipment_id); 