-- Criar tabela de categorias
CREATE TABLE tag_categories (
    id BIGSERIAL PRIMARY KEY,
    name VARCHA<PERSON>(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON> tabel<PERSON> de tags
CREATE TABLE tags (
    id BIGSERIAL PRIMARY KEY,
    category_id BIGINT REFERENCES tag_categories(id),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    color VARCHAR(7),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Criar tabela de relação equipamento-tag
CREATE TABLE equipment_tags (
    equipment_id BIGINT REFERENCES equipment(id) ON DELETE CASCADE,
    tag_id BIGINT REFERENCES tags(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (equipment_id, tag_id)
);

-- Criar índices
CREATE INDEX idx_tags_category ON tags(category_id);
CREATE INDEX idx_equipment_tags_equipment ON equipment_tags(equipment_id);
CREATE INDEX idx_equipment_tags_tag ON equipment_tags(tag_id);

-- Inserir categorias padrão
INSERT INTO tag_categories (name, description) VALUES
    ('tipo_equipamento', 'Categoria para tipos específicos de equipamento'),
    ('caracteristica', 'Características específicas do equipamento'),
    ('prioridade', 'Níveis de prioridade para manutenção'),
    ('localizacao', 'Localização do equipamento'),
    ('responsavel', 'Responsável pela manutenção'); 