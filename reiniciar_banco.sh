#!/bin/bash

# Script para reiniciar apenas o banco de dados PostgreSQL
# Uso: ./reiniciar_banco.sh

echo "Reiniciando banco de dados PostgreSQL..."
sudo service postgresql restart

# Verificar se o PostgreSQL está em execução
if sudo service postgresql status | grep -q "active"; then
    echo "PostgreSQL reiniciado com sucesso!"
else
    echo "Falha ao reiniciar o PostgreSQL. Verifique o serviço."
    exit 1
fi

echo "Banco de dados PostgreSQL está pronto para uso."
