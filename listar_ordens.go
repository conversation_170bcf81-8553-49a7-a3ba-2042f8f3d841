package main

import (
	"fmt"
	"log"

	"tradicao/internal/database"
	"tradicao/internal/models"
)

func main() {
	// Inicializar conexão com o banco de dados
	db := database.GetGormDB()
	if db == nil {
		log.Fatal("Falha ao conectar ao banco de dados")
	}

	// Buscar todas as ordens de manutenção
	var ordens []models.MaintenanceOrder
	if err := db.Find(&ordens).Error; err != nil {
		log.Fatalf("Erro ao buscar ordens: %v", err)
	}

	fmt.Printf("Total de ordens encontradas: %d\n\n", len(ordens))
	fmt.Println("Lista de todas as ordens:")
	fmt.Println("ID | Branch | Equipment | Technician | Provider | Number | Status")
	fmt.Println("------------------------------------------------------------------")

	for _, ordem := range ordens {
		fmt.Printf("%d | %d | %d | %d | %d | %s | %s\n",
			ordem.ID,
			ordem.BranchID,
			ordem.EquipmentID,
			ordem.TechnicianID,
			ordem.ServiceProviderID,
			ordem.Number,
			ordem.Status)
	}

	// Buscar ordens associadas ao técnico específico
	var tecnicoID uint = 94 // <NAME_EMAIL>
	var ordensDoTecnico []models.MaintenanceOrder

	if err := db.Where("technician_id = ? OR service_provider_id IN (SELECT service_provider_id FROM users WHERE id = ?)",
		tecnicoID, tecnicoID).Find(&ordensDoTecnico).Error; err != nil {
		log.Fatalf("Erro ao buscar ordens do técnico: %v", err)
	}

	fmt.Printf("\n\nOrdens associadas ao técnico ID %d (<EMAIL>):\n", tecnicoID)
	fmt.Println("ID | Branch | Equipment | Technician | Provider | Number | Status")
	fmt.Println("------------------------------------------------------------------")

	for _, ordem := range ordensDoTecnico {
		fmt.Printf("%d | %d | %d | %d | %d | %s | %s\n",
			ordem.ID,
			ordem.BranchID,
			ordem.EquipmentID,
			ordem.TechnicianID,
			ordem.ServiceProviderID,
			ordem.Number,
			ordem.Status)
	}
}
