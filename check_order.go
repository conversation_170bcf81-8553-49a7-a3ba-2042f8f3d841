package main

import (
	"fmt"
	"log"
	"os"
	"strconv"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// MaintenanceOrder representa uma ordem de manutenção
type MaintenanceOrder struct {
	ID                uint    `gorm:"primaryKey" json:"id"`
	BranchID          uint    `gorm:"column:branch_id" json:"branch_id"`
	EquipmentID       uint    `gorm:"column:equipment_id" json:"equipment_id"`
	ServiceProviderID *uint   `gorm:"column:service_provider_id" json:"service_provider_id,omitempty"`
	TechnicianID      *uint   `json:"technician_id" gorm:"column:technician_id"`
	Title             string  `gorm:"column:title" json:"title"`
	Number            string  `gorm:"column:number" json:"number"`
	Status            string  `gorm:"column:status" json:"status"`
	Priority          string  `gorm:"column:priority" json:"priority"`
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Uso: go run check_order.go <id_da_ordem>")
		os.Exit(1)
	}

	orderID, err := strconv.ParseUint(os.Args[1], 10, 32)
	if err != nil {
		fmt.Printf("ID inválido: %v\n", err)
		os.Exit(1)
	}

	// Configurar conexão com o banco de dados
	dsn := "host=localhost user=postgres password=postgres dbname=tradicao port=5432 sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Buscar ordem
	var order MaintenanceOrder
	if err := db.First(&order, orderID).Error; err != nil {
		fmt.Printf("Erro ao buscar ordem %d: %v\n", orderID, err)
		os.Exit(1)
	}

	// Exibir detalhes da ordem
	fmt.Printf("Detalhes da Ordem #%d:\n", order.ID)
	fmt.Printf("  Título: %s\n", order.Title)
	fmt.Printf("  Número: %s\n", order.Number)
	fmt.Printf("  Filial ID: %d\n", order.BranchID)
	fmt.Printf("  Equipamento ID: %d\n", order.EquipmentID)
	
	if order.TechnicianID != nil {
		fmt.Printf("  Técnico ID: %d\n", *order.TechnicianID)
	} else {
		fmt.Printf("  Técnico ID: <não atribuído>\n")
	}
	
	if order.ServiceProviderID != nil {
		fmt.Printf("  Prestador ID: %d\n", *order.ServiceProviderID)
	} else {
		fmt.Printf("  Prestador ID: <não atribuído>\n")
	}
	
	fmt.Printf("  Status: %s\n", order.Status)
	fmt.Printf("  Prioridade: %s\n", order.Priority)
}
