# Configurações do Banco de Dados
# IMPORTANTE: Substitua pelos valores reais do seu banco de dados
DB_USER=seu_usuario_do_banco
DB_PASS=sua_senha_do_banco
DB_HOST=seu_host_do_banco
DB_PORT=sua_porta_do_banco
DB_NAME=nome_do_banco

# String de conexão completa (opcional, se não fornecida será construída automaticamente)
DATABASE_URL=postgresql://usuario:senha@host:porta/banco?sslmode=disable

# Configurações da Aplicação
APP_PORT=8080
APP_ENV=development
APP_SECRET=your-secret-key

# Configurações de Log
LOG_LEVEL=debug
LOG_FILE=logs/app.log

# Configurações de Email
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-smtp-password
SMTP_FROM=<EMAIL>

# Configurações de Segurança
JWT_SECRET=your-jwt-secret
JWT_EXPIRY=24h
PASSWORD_SALT=your-password-salt
